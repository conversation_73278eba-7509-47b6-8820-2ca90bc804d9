#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وحدة الترقيم التلقائي الجديدة
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_new_numbering_module():
    """اختبار وحدة الترقيم التلقائي الجديدة"""
    print("🔢 اختبار وحدة الترقيم التلقائي الجديدة...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.auto_numbering_module import AutoNumberingWidget
        from database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager("accounting_system.db")
        print("   ✅ تم إنشاء مدير قاعدة البيانات")
        
        # إنشاء وحدة الترقيم التلقائي
        numbering_widget = AutoNumberingWidget(db_manager)
        print("   ✅ تم إنشاء وحدة الترقيم التلقائي الجديدة")
        
        # اختبار تحميل البيانات
        numbering_widget.load_data()
        print("   ✅ تم تحميل البيانات بنجاح")
        
        # فحص عدد الصفوف في الجدول
        row_count = numbering_widget.sequences_table.rowCount()
        print(f"   📊 عدد التسلسلات في الجدول: {row_count}")
        
        if row_count > 0:
            print("   ✅ البيانات تظهر في الجدول")
            
            # عرض بعض البيانات
            for row in range(min(3, row_count)):
                doc_type_item = numbering_widget.sequences_table.item(row, 0)
                prefix_item = numbering_widget.sequences_table.item(row, 1)
                preview_item = numbering_widget.sequences_table.item(row, 5)
                
                if doc_type_item and prefix_item and preview_item:
                    print(f"      {row+1}. {doc_type_item.text()}: {preview_item.text()}")
                else:
                    print(f"      {row+1}. بيانات غير مكتملة")
        else:
            print("   ❌ لا توجد بيانات في الجدول")
            return False
        
        # اختبار إنتاج رقم تلقائي
        test_number = AutoNumberingWidget.get_next_number(db_manager, "فاتورة مبيعات")
        print(f"   ✅ تم إنتاج رقم تلقائي: {test_number}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الوحدة الجديدة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_structure():
    """اختبار هيكل قاعدة البيانات"""
    print("\n🗄️ اختبار هيكل قاعدة البيانات...")
    
    try:
        from database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # فحص وجود الجدول
        check_query = """
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='numbering_sequences'
        """
        result = db_manager.execute_query(check_query)
        
        if result:
            print("   ✅ جدول numbering_sequences موجود")
            
            # فحص هيكل الجدول
            structure_query = "PRAGMA table_info(numbering_sequences)"
            columns = db_manager.execute_query(structure_query)
            
            print("   📋 أعمدة الجدول:")
            for col in columns:
                print(f"      • {col[1]} ({col[2]})")
            
            # فحص البيانات
            count_query = "SELECT COUNT(*) FROM numbering_sequences"
            count_result = db_manager.execute_query(count_query)
            count = count_result[0][0] if count_result else 0
            
            print(f"   📊 عدد التسلسلات في قاعدة البيانات: {count}")
            
            return True
        else:
            print("   ❌ جدول numbering_sequences غير موجود")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def test_number_generation():
    """اختبار إنتاج الأرقام"""
    print("\n🔢 اختبار إنتاج الأرقام...")
    
    try:
        from modules.auto_numbering_module import AutoNumberingWidget
        from database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إنتاج أرقام لأنواع مختلفة من المستندات
        document_types = [
            "فاتورة مبيعات",
            "فاتورة مشتريات", 
            "سند قبض",
            "سند صرف",
            "قيد يومية"
        ]
        
        for doc_type in document_types:
            try:
                number = AutoNumberingWidget.get_next_number(db_manager, doc_type)
                print(f"   ✅ {doc_type}: {number}")
            except Exception as e:
                print(f"   ❌ خطأ في {doc_type}: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إنتاج الأرقام: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار وحدة الترقيم التلقائي الجديدة")
    print("=" * 70)
    
    tests = [
        ("هيكل قاعدة البيانات", test_database_structure),
        ("وحدة الترقيم التلقائي الجديدة", test_new_numbering_module),
        ("إنتاج الأرقام", test_number_generation),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم بناء وحدة الترقيم التلقائي الجديدة بنجاح!")
        print("✅ جميع الوظائف تعمل بدون مشاكل")
        
        print("\n🔧 الميزات الجديدة:")
        print("   ✅ كود مبسط وواضح")
        print("   ✅ معالجة أخطاء محسنة")
        print("   ✅ واجهة مستخدم بسيطة")
        print("   ✅ تحديث فوري للبيانات")
        print("   ✅ معاينة الأرقام")
        print("   ✅ إنتاج أرقام تلقائي")
        
        print("\n🚀 للاستخدام:")
        print("   1. شغل النظام: python src/main_system_restructured.py")
        print("   2. انقر على 'إعدادات النظام'")
        print("   3. اختر 'الترقيم التلقائي للمستندات'")
        print("   4. أضف أو عدل التسلسلات - ستظهر فوراً!")
        
    else:
        print("\n⚠️ بعض جوانب الوحدة الجديدة تحتاج إلى مراجعة")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
