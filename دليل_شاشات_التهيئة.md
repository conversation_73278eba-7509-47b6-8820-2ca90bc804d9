# 📁 دليل شاشات التهيئة - مجلد التهيئة

## 🎯 **نظرة عامة**

تم تطوير **8 شاشات أساسية** من مجلد التهيئة في نظام إدارة الأعمال المتكامل. هذه الشاشات تشكل الأساس لإعداد النظام وتهيئته للعمل.

---

## ✅ **الشاشات المطبقة (8/15)**

### 1️⃣ **المؤشرات العامة** ⚙️
**الوظيفة:** تحديد خصائص النظام العامة (نوع الشركة، بداية السنة المالية، عدد الكسور العشرية...)

#### 🎯 **المميزات:**
- ✅ **3 تبويبات شاملة**:
  - 📋 **المعلومات الأساسية**: بيانات الشركة والسنة المالية
  - 💰 **الإعدادات المالية**: العملة الأساسية والكسور العشرية والضرائب
  - 🔧 **إعدادات النظام**: ترقيم الفواتير والنسخ الاحتياطي

#### 📊 **البيانات المدارة:**
- 🏢 **بيانات الشركة**: الاسم، النوع، السجل التجاري، الرقم الضريبي
- 📅 **السنة المالية**: بداية ونهاية السنة المالية
- 💱 **العملة الأساسية**: نوع العملة ورمزها
- 🔢 **الكسور العشرية**: للمبالغ والكميات والأسعار (0-6 خانات)
- 📊 **الضرائب**: النسبة الافتراضية والتطبيق التلقائي
- 🔢 **ترقيم الفواتير**: تلقائي، يدوي، حسب السنة/الشهر
- 💾 **النسخ الاحتياطي**: تلقائي (يومي، أسبوعي، شهري)

#### 🎮 **كيفية الاستخدام:**
1. انقر على "📁 التهيئة" في شجرة النظام
2. اختر "01. المؤشرات العامة"
3. املأ البيانات في التبويبات الثلاثة
4. اضغط "💾 حفظ" أو "🔄 القيم الافتراضية"

---

### 2️⃣ **إعداد قواعد النظام** 🔧
**الوظيفة:** تفعيل أو تعطيل خصائص مثل: الترحيل التلقائي، التحقق من المخزون، السماح ببيع بالسالب...

#### 🎯 **المميزات:**
- ✅ **4 تبويبات متخصصة**:
  - 📊 **القواعد المحاسبية**: الترحيل التلقائي والتحقق من الأرصدة
  - 📦 **قواعد المخزون**: التحقق من المخزون وطرق التقييم
  - 🛒 **قواعد المبيعات**: التسعير والخصومات والدفع
  - 🔒 **قواعد الأمان**: كلمات المرور والجلسات والتدقيق

#### 📊 **القواعد المدارة:**

##### 📊 **القواعد المحاسبية:**
- ✅ الترحيل التلقائي للقيود
- ✅ ترحيل فواتير المبيعات/المشتريات تلقائياً
- ✅ التحقق من رصيد العميل والحد الائتماني
- ✅ التحقق من رصيد الصندوق
- ⚖️ السماح بقيود غير متوازنة (حد أقصى للفرق)

##### 📦 **قواعد المخزون:**
- ✅ التحقق من المخزون قبل البيع
- ❌ السماح ببيع بالسالب
- ✅ تحديث المخزون تلقائياً
- ⚠️ تنبيه عند نفاد المخزون
- 💰 طرق التقييم: متوسط مرجح، FIFO، LIFO، تكلفة محددة
- 📊 إعادة حساب التكلفة: تلقائي، يدوي، شهري

##### 🛒 **قواعد المبيعات:**
- ❌ السماح ببيع بأقل من سعر التكلفة
- ✅ السماح بتعديل الأسعار في الفاتورة
- 🎯 الحد الأقصى للخصم (افتراضي: 20%)
- ⚠️ طلب تأكيد للخصم الكبير (افتراضي: 10%)
- ✅ السماح بالبيع الآجل والدفع الجزئي

##### 🔒 **قواعد الأمان:**
- 🔐 الحد الأدنى لطول كلمة المرور (افتراضي: 6)
- 🔤 طلب أحرف كبيرة/صغيرة وأرقام
- ⏰ مدة انتهاء الجلسة (افتراضي: 60 دقيقة)
- 🚫 الحد الأقصى لمحاولات الدخول الخاطئة (افتراضي: 5)
- 📝 تسجيل جميع العمليات في سجل التدقيق

---

### 3️⃣ **تهيئة العملات** 💱
**الوظيفة:** تعريف العملات المستخدمة وأسعار الصرف

#### 🎯 **المميزات:**
- ✅ **واجهة مقسمة** (إدارة العملات + أسعار الصرف)
- ✅ **إدارة شاملة للعملات**: إضافة، تعديل، حذف
- ✅ **إدارة أسعار الصرف**: تحديث يومي للأسعار
- ✅ **جداول تفاعلية** مع إمكانية التحديد والتعديل

#### 📊 **البيانات المدارة:**

##### 💰 **إدارة العملات:**
- 📝 **اسم العملة**: مثل "دولار أمريكي"
- 🔤 **رمز العملة**: مثل "USD" (3 أحرف)
- 💲 **رمز العرض**: مثل "$" (5 أحرف كحد أقصى)
- 🔢 **عدد الكسور العشرية**: 0-6 خانات
- ⭐ **العملة الأساسية**: تحديد العملة الرئيسية
- ✅ **العملة نشطة**: تفعيل/تعطيل العملة

##### 💱 **أسعار الصرف:**
- 🔄 **من العملة** → **إلى العملة**
- 💰 **سعر الصرف**: بدقة 4 خانات عشرية
- 📅 **تاريخ السعر**: تاريخ تحديد السعر
- 📝 **ملاحظات**: ملاحظات اختيارية
- 🕒 **آخر تحديث**: تسجيل تلقائي لوقت التحديث

#### 💰 **العملات الافتراضية:**
- 🇾🇪 **ريال يمني (YER)** - ر.ي - عملة أساسية
- 🇺🇸 **دولار أمريكي (USD)** - $
- 🇪🇺 **يورو (EUR)** - €
- 🇸🇦 **ريال سعودي (SAR)** - ر.س
- 🇦🇪 **درهم إماراتي (AED)** - د.إ

#### 📈 **أسعار الصرف النموذجية:**
- YER → USD: 0.0040 (250 ريال = 1 دولار)
- USD → YER: 250.0000
- SAR → YER: 66.6667
- EUR → USD: 1.0850

---

### 4️⃣ **الأقاليم الدولية / الدول** 🌍
**الوظيفة:** تعريف الأقاليم الجغرافية الدولية والدول التابعة لها

#### 🎯 **المميزات:**
- ✅ **3 تبويبات متخصصة**:
  - 🌍 **الأقاليم الدولية**: تعريف الأقاليم الجغرافية الكبرى
  - 🏳️ **الدول**: إدارة الدول وربطها بالأقاليم
  - ⚙️ **إعدادات التقسيم**: تخصيص العرض والتصدير

#### 📊 **البيانات المدارة:**

##### 🌍 **الأقاليم الدولية:**
- 📝 **اسم الإقليم**: مثل "الشرق الأوسط"
- 🔤 **الاسم بالإنجليزية**: مثل "Middle East"
- 🔢 **رمز الإقليم**: مثل "ME" (5 أحرف كحد أقصى)
- 📄 **وصف الإقليم**: وصف مختصر
- ✅ **إقليم نشط**: تفعيل/تعطيل الإقليم

##### 🏳️ **الدول:**
- 📝 **اسم الدولة**: مثل "الجمهورية اليمنية"
- 🔤 **الاسم بالإنجليزية**: مثل "Republic of Yemen"
- 🔢 **رمز الدولة (ISO)**: مثل "YE" (3 أحرف)
- 📞 **رمز الهاتف الدولي**: مثل "+967"
- 🌍 **الإقليم التابع له**: ربط الدولة بالإقليم
- 💱 **العملة الرسمية**: العملة المستخدمة
- ✅ **دولة نشطة**: تفعيل/تعطيل الدولة

#### 🌍 **الأقاليم الافتراضية:**
- 🌍 **الشرق الأوسط (ME)** - منطقة الشرق الأوسط وشمال أفريقيا
- 🌏 **آسيا (AS)** - القارة الآسيوية
- 🌍 **أوروبا (EU)** - القارة الأوروبية
- 🌍 **أفريقيا (AF)** - القارة الأفريقية
- 🌎 **أمريكا الشمالية (NA)** - قارة أمريكا الشمالية
- 🌎 **أمريكا الجنوبية (SA)** - قارة أمريكا الجنوبية
- 🌏 **أوقيانوسيا (OC)** - قارة أوقيانوسيا

#### 🏳️ **الدول النموذجية:**
- 🇾🇪 **الجمهورية اليمنية (YE)** - +967 - الشرق الأوسط
- 🇸🇦 **المملكة العربية السعودية (SA)** - +966 - الشرق الأوسط
- 🇦🇪 **دولة الإمارات العربية المتحدة (AE)** - +971 - الشرق الأوسط
- 🇪🇬 **جمهورية مصر العربية (EG)** - +20 - الشرق الأوسط
- 🇯🇴 **المملكة الأردنية الهاشمية (JO)** - +962 - الشرق الأوسط
- 🇺🇸 **الولايات المتحدة الأمريكية (US)** - +1 - أمريكا الشمالية
- 🇬🇧 **المملكة المتحدة (GB)** - +44 - أوروبا

---

### 5️⃣ **بيانات المحافظات / المناطق / المدن** 🏙️
**الوظيفة:** إدارة الهيكل الإداري الجغرافي الداخلي للدولة

#### 🎯 **المميزات:**
- ✅ **شجرة هرمية تفاعلية** للتقسيمات الإدارية
- ✅ **4 تبويبات متدرجة**:
  - 🏛️ **المحافظات**: المستوى الإداري الأول
  - 🏘️ **المناطق**: المديريات والأقضية
  - 🏙️ **المدن**: المدن والبلدات
  - 🏡 **القرى**: القرى والأحياء

#### 📊 **البيانات المدارة:**

##### 🏛️ **المحافظات:**
- 📝 **اسم المحافظة**: مثل "محافظة صنعاء"
- 🔤 **الاسم بالإنجليزية**: مثل "Sana'a Governorate"
- 🔢 **رمز المحافظة**: مثل "SA" (5 أحرف كحد أقصى)
- 🏳️ **الدولة التابعة لها**: ربط بالدولة
- 🏛️ **العاصمة**: عاصمة المحافظة
- 📏 **المساحة (كم²)**: مساحة المحافظة
- ⭐ **محافظة رئيسية**: تحديد العاصمة
- ✅ **محافظة نشطة**: تفعيل/تعطيل

##### 🏘️ **المناطق:**
- 📝 **اسم المنطقة**: مثل "مديرية الثورة"
- 🔤 **الاسم بالإنجليزية**: مثل "Al-Thawra District"
- 🔢 **رمز المنطقة**: مثل "TH" (5 أحرف كحد أقصى)
- 🏛️ **المحافظة التابعة لها**: ربط بالمحافظة
- 🏷️ **نوع المنطقة**: مديرية، منطقة، قضاء، ناحية
- ✅ **منطقة نشطة**: تفعيل/تعطيل

##### 🏙️ **المدن:**
- 📝 **اسم المدينة**: مثل "مدينة صنعاء"
- 🔤 **الاسم بالإنجليزية**: مثل "Sana'a City"
- 🔢 **رمز المدينة**: مثل "SAN" (5 أحرف كحد أقصى)
- 🏘️ **المنطقة التابعة لها**: ربط بالمنطقة
- 🏷️ **نوع المدينة**: مدينة كبرى، مدينة، بلدة، قصبة
- 👥 **عدد السكان**: تقدير عدد السكان
- ⭐ **مدينة رئيسية**: تحديد المدن المهمة
- ✅ **مدينة نشطة**: تفعيل/تعطيل

##### 🏡 **القرى:**
- 📝 **اسم القرية**: مثل "قرية الروضة"
- 🔤 **الاسم بالإنجليزية**: مثل "Al-Rawda Village"
- 🔢 **رمز القرية**: مثل "RW" (5 أحرف كحد أقصى)
- 🏙️ **المدينة التابعة لها**: ربط بالمدينة
- 🏷️ **نوع القرية**: قرية، عزلة، حي، منطقة سكنية
- ✅ **قرية نشطة**: تفعيل/تعطيل

#### 🇾🇪 **البيانات النموذجية اليمنية:**

##### 🏛️ **المحافظات:**
- 🏛️ **محافظة صنعاء (SA)** - العاصمة: مدينة صنعاء - 13,850 كم² - رئيسية
- 🏛️ **محافظة عدن (AD)** - العاصمة: مدينة عدن - 760 كم²
- 🏛️ **محافظة تعز (TA)** - العاصمة: مدينة تعز - 10,677 كم²
- 🏛️ **محافظة الحديدة (HO)** - العاصمة: مدينة الحديدة - 17,509 كم²
- 🏛️ **محافظة إب (IB)** - العاصمة: مدينة إب - 5,344 كم²

##### 🏘️ **المناطق:**
- 🏘️ **مديرية الثورة (TH)** - محافظة صنعاء
- 🏘️ **مديرية الصافية (SF)** - محافظة صنعاء
- 🏘️ **مديرية كريتر (CR)** - محافظة عدن
- 🏘️ **مديرية المعلا (ML)** - محافظة عدن
- 🏘️ **مديرية صالة (SL)** - محافظة تعز

##### 🏙️ **المدن:**
- 🏙️ **مدينة صنعاء (SAN)** - مديرية الثورة - 2,500,000 نسمة - رئيسية
- 🏙️ **مدينة عدن (ADE)** - مديرية كريتر - 800,000 نسمة
- 🏙️ **مدينة تعز (TAI)** - مديرية صالة - 600,000 نسمة
- 🏙️ **مدينة الحديدة (HOD)** - مديرية الحديدة - 400,000 نسمة

##### 🏡 **القرى:**
- 🏡 **قرية الروضة (RW)** - مدينة صنعاء - قرية
- 🏡 **حي الزبيري (ZU)** - مدينة صنعاء - حي
- 🏡 **عزلة الشعب (SH)** - مدينة عدن - عزلة

---

### 6️⃣ **مجموعات المحافظات** 🗺️
**الوظيفة:** تجميع المحافظات حسب معايير مختلفة (جغرافية، إدارية، اقتصادية...)

#### 🎯 **المميزات:**
- ✅ **3 تبويبات متخصصة**:
  - 📋 **إدارة المجموعات**: إنشاء وتعديل المجموعات
  - 🎯 **تعيين المحافظات**: ربط المحافظات بالمجموعات
  - 📊 **التقارير والإحصائيات**: تقارير شاملة عن التوزيع

#### 📊 **البيانات المدارة:**

##### 📋 **إدارة المجموعات:**
- 📝 **اسم المجموعة**: مثل "المحافظات الشمالية"
- 🔤 **الاسم بالإنجليزية**: مثل "Northern Governorates"
- 🔢 **رمز المجموعة**: مثل "NORTH" (10 أحرف كحد أقصى)
- 🏷️ **نوع التجميع**: جغرافي، إداري، اقتصادي، ديموغرافي، مناخي، تنموي، أمني، مخصص
- 🎨 **لون المجموعة**: للتمييز البصري (أزرق، أخضر، أحمر...)
- 📄 **وصف المجموعة**: وصف مختصر ومعايير التجميع
- ⭐ **مجموعة افتراضية**: تحديد المجموعات الأساسية
- ✅ **مجموعة نشطة**: تفعيل/تعطيل المجموعة

##### 🎯 **تعيين المحافظات:**
- 🏛️ **المحافظات المتاحة**: قائمة المحافظات غير المعينة
- 🎯 **محافظات المجموعة**: قائمة المحافظات المعينة للمجموعة
- ➡️ **نقل فردي**: نقل محافظة واحدة
- ⏩ **نقل جماعي**: نقل جميع المحافظات
- ⬅️ **إزالة فردية**: إزالة محافظة واحدة
- ⏪ **إزالة جماعية**: إزالة جميع المحافظات

##### 📊 **التقارير والإحصائيات:**
- 📈 **تقرير توزيع المحافظات**: توزيع المحافظات على المجموعات
- 📊 **إحصائيات المجموعات**: إحصائيات حسب النوع والحجم
- ⚠️ **المحافظات غير المجمعة**: المحافظات التي لم يتم تجميعها
- 📤 **تصدير التقارير**: تصدير بصيغ مختلفة (قيد التطوير)

#### 🗺️ **المجموعات الافتراضية:**

##### 🧭 **التجميع الجغرافي:**
- 🔵 **المحافظات الشمالية (NORTH)** - أزرق
  - محافظة صنعاء، أمانة العاصمة، عمران، صعدة، الجوف، حجة، المحويت، ريمة
- 🟢 **المحافظات الجنوبية (SOUTH)** - أخضر
  - محافظة عدن، لحج، أبين، الضالع
- 🟡 **المحافظات الشرقية (EAST)** - أصفر
  - محافظة حضرموت، المهرة، شبوة، مأرب
- 🔴 **المحافظات الغربية (WEST)** - أحمر
  - محافظة الحديدة، تعز، إب، ذمار، البيضاء
- 🟣 **المحافظات الساحلية (COAST)** - بنفسجي
  - محافظة الحديدة، عدن، لحج، أبين، حضرموت، المهرة، جزيرة سقطرى
- 🟠 **المحافظات الداخلية (INLAND)** - برتقالي
  - المحافظات غير الساحلية

##### 📊 **التجميع الديموغرافي والاقتصادي:**
- 🩷 **المحافظات الكبرى (MAJOR)** - وردي - افتراضية
  - محافظة صنعاء، أمانة العاصمة، عدن، تعز، الحديدة، إب
- 🤎 **المحافظات النفطية (OIL)** - بني
  - محافظة مأرب، شبوة، حضرموت

#### 🎮 **كيفية الاستخدام:**

##### 📋 **إدارة المجموعات:**
1. انتقل لتبويب "إدارة المجموعات"
2. املأ بيانات المجموعة الجديدة
3. اختر نوع التجميع واللون
4. اضغط "إضافة" لحفظ المجموعة

##### 🎯 **تعيين المحافظات:**
1. انتقل لتبويب "تعيين المحافظات"
2. اختر المجموعة المستهدفة
3. اختر المحافظات من القائمة المتاحة
4. استخدم أزرار النقل لتعيين المحافظات

##### 📊 **عرض التقارير:**
1. انتقل لتبويب "التقارير والإحصائيات"
2. اختر نوع التقرير المطلوب
3. اعرض النتائج في منطقة التقارير
4. صدر التقارير حسب الحاجة

---

### 7️⃣ **شجرة الفروع** 🌳
**الوظيفة:** إدارة الهيكل التنظيمي للفروع والمكاتب في شكل شجرة هرمية

#### 🎯 **المميزات:**
- ✅ **شجرة هرمية تفاعلية**: عرض بصري للهيكل التنظيمي
- ✅ **4 تبويبات متخصصة**:
  - 🏢 **الفروع الرئيسية**: إدارة الفروع الرئيسية والمقرات
  - 🏪 **الفروع الفرعية**: إدارة الفروع التابعة للفروع الرئيسية
  - 🏬 **المكاتب**: إدارة المكاتب المتخصصة داخل الفروع
  - ⚙️ **إعدادات الشجرة**: تخصيص العرض والتقارير

#### 📊 **البيانات المدارة:**

##### 🏢 **الفروع الرئيسية:**
- 📝 **اسم الفرع**: مثل "فرع صنعاء الرئيسي"
- 🔤 **الاسم بالإنجليزية**: مثل "Sana'a Main Branch"
- 🔢 **رمز الفرع**: مثل "SAN-001" (10 أحرف كحد أقصى)
- 🏛️ **المحافظة والمدينة**: الموقع الجغرافي للفرع
- 📍 **العنوان التفصيلي**: العنوان الكامل للفرع
- 👤 **مدير الفرع**: المسؤول عن إدارة الفرع
- 📞 **رقم الهاتف**: هاتف الفرع الرئيسي
- ⭐ **فرع رئيسي (مقر)**: تحديد المقر الرئيسي للشركة
- ✅ **فرع نشط**: تفعيل/تعطيل الفرع

##### 🏪 **الفروع الفرعية:**
- 📝 **اسم الفرع الفرعي**: مثل "فرع الثورة"
- 🏢 **الفرع الرئيسي التابع له**: الفرع الأب في الهيكل
- 🔢 **رمز الفرع الفرعي**: مثل "SAN-002"
- 🗺️ **المنطقة/المديرية**: التقسيم الإداري الفرعي
- 📍 **العنوان**: العنوان التفصيلي للفرع الفرعي
- 👤 **مدير الفرع الفرعي**: المسؤول عن الفرع الفرعي
- 📞 **رقم الهاتف**: هاتف الفرع الفرعي
- ✅ **فرع فرعي نشط**: تفعيل/تعطيل الفرع الفرعي

##### 🏬 **المكاتب:**
- 📝 **اسم المكتب**: مثل "مكتب خدمة العملاء"
- 🏪 **الفرع التابع له**: الفرع أو الفرع الفرعي المحتوي للمكتب
- 🔢 **رمز المكتب**: مثل "SAN-001-CS" (15 حرف كحد أقصى)
- 🏷️ **نوع المكتب**: خدمة العملاء، المبيعات، المحاسبة، الموارد البشرية، تقنية المعلومات، التسويق، المشتريات، المخازن، الأمن، الصيانة
- 👤 **مسؤول المكتب**: المسؤول المباشر عن المكتب
- ☎️ **الهاتف الداخلي**: رقم التحويلة الداخلية
- 📄 **وصف المكتب**: وصف مختصر لوظائف المكتب
- ✅ **مكتب نشط**: تفعيل/تعطيل المكتب

#### 🌳 **البيانات النموذجية:**

##### 🏢 **الفروع الرئيسية النموذجية:**
- 🔵 **فرع صنعاء الرئيسي (SAN-001)** - مقر رئيسي
  - المحافظة: محافظة صنعاء، المدينة: مدينة صنعاء
  - العنوان: شارع الزبيري، المدير: أحمد محمد علي
- 🟢 **فرع عدن الرئيسي (ADE-001)**
  - المحافظة: محافظة عدن، المدينة: مدينة عدن
  - العنوان: شارع المعلا، المدير: فاطمة أحمد سالم
- 🟡 **فرع تعز الرئيسي (TAI-001)**
  - المحافظة: محافظة تعز، المدينة: مدينة تعز
  - العنوان: شارع جمال عبدالناصر، المدير: محمد عبدالله حسن
- 🔴 **فرع الحديدة الرئيسي (HOD-001)**
  - المحافظة: محافظة الحديدة، المدينة: مدينة الحديدة
  - العنوان: شارع الكورنيش، المدير: عائشة علي محمد

##### 🏪 **الفروع الفرعية النموذجية:**
- **فرع الثورة (SAN-002)** - تابع لفرع صنعاء الرئيسي
  - المنطقة: مديرية الثورة، المدير: عبدالرحمن سعيد أحمد
- **فرع الصافية (SAN-003)** - تابع لفرع صنعاء الرئيسي
  - المنطقة: مديرية الصافية، المدير: زينب محمد عبدالله
- **فرع كريتر (ADE-002)** - تابع لفرع عدن الرئيسي
  - المنطقة: مديرية كريتر، المدير: سعيد أحمد علي
- **فرع صالة (TAI-002)** - تابع لفرع تعز الرئيسي
  - المنطقة: مديرية صالة، المدير: مريم عبدالله سالم

##### 🏬 **المكاتب النموذجية:**
- **مكتب خدمة العملاء (SAN-001-CS)** - فرع صنعاء الرئيسي
  - النوع: خدمة العملاء، المسؤول: علي محمد حسن، التحويلة: 101
- **مكتب المبيعات (SAN-001-SA)** - فرع صنعاء الرئيسي
  - النوع: المبيعات، المسؤول: أحمد محمد علي، التحويلة: 102
- **مكتب المحاسبة (SAN-001-AC)** - فرع صنعاء الرئيسي
  - النوع: المحاسبة، المسؤول: فاطمة أحمد سالم، التحويلة: 103

#### 🎮 **كيفية الاستخدام:**

##### 🏢 **إدارة الفروع الرئيسية:**
1. انتقل لتبويب "الفروع الرئيسية"
2. املأ بيانات الفرع (الاسم، الرمز، الموقع...)
3. اختر المحافظة والمدينة
4. عين مدير الفرع وأدخل بيانات الاتصال
5. حدد إذا كان الفرع مقراً رئيسياً
6. اضغط "إضافة فرع رئيسي"

##### 🏪 **إدارة الفروع الفرعية:**
1. انتقل لتبويب "الفروع الفرعية"
2. اختر الفرع الرئيسي التابع له
3. املأ بيانات الفرع الفرعي
4. حدد المنطقة والعنوان
5. عين مدير الفرع الفرعي
6. اضغط "إضافة فرع فرعي"

##### 🏬 **إدارة المكاتب:**
1. انتقل لتبويب "المكاتب"
2. اختر الفرع التابع له المكتب
3. حدد نوع المكتب من القائمة
4. عين مسؤول المكتب
5. أدخل رقم التحويلة الداخلية
6. اكتب وصفاً مختصراً للمكتب
7. اضغط "إضافة مكتب"

##### 🌳 **استخدام الشجرة الهرمية:**
1. استخدم أزرار "توسيع الكل" و"طي الكل" للتحكم في العرض
2. انقر على أي عنصر لتحديده
3. انقر نقراً مزدوجاً لعرض تفاصيل العنصر
4. استخدم "تحديث الشجرة" بعد إضافة عناصر جديدة

---

### 8️⃣ **تعريف الفرع** 🏢
**الوظيفة:** الإعداد الشامل والتفصيلي لتعريف الفرع بجميع خصائصه ومواصفاته

#### 🎯 **المميزات:**
- ✅ **6 تبويبات شاملة**: تغطي جميع جوانب تعريف الفرع
- ✅ **إعداد متكامل**: من المعلومات الأساسية إلى الأمان والصلاحيات
- ✅ **بيانات نموذجية يمنية**: تعريف كامل لفرع صنعاء الرئيسي
- ✅ **معاينة قبل الحفظ**: استعراض شامل لجميع البيانات المدخلة

#### 📊 **التبويبات المتاحة:**

##### 📋 **المعلومات الأساسية:**
- 📝 **اسم الفرع**: مثل "فرع صنعاء الرئيسي"
- 🔤 **الاسم بالإنجليزية**: مثل "Sana'a Main Branch"
- 🔢 **رمز الفرع**: مثل "SAN-001" (15 حرف كحد أقصى)
- 🏷️ **نوع الفرع**: فرع رئيسي، فرع فرعي، مكتب تمثيلي، مركز خدمة، مستودع، معرض، مصنع، مختبر، مركز تدريب
- 📅 **تاريخ التأسيس**: تاريخ إنشاء الفرع
- 📅 **تاريخ بدء العمل**: تاريخ بداية النشاط الفعلي
- 👤 **مدير الفرع**: المسؤول الرئيسي عن الفرع
- 👤 **نائب المدير**: المساعد الأول للمدير
- 📄 **وصف الفرع**: وصف مختصر لطبيعة عمل الفرع وأهدافه
- ⚙️ **إعدادات الحالة**: فرع نشط، فرع رئيسي، قبول المعاملات، المبيعات، المشتريات، التحويلات
- 📊 **مستوى الأولوية**: عالي جداً، عالي، متوسط، منخفض، منخفض جداً
- 👥 **سعة الفرع**: عدد الموظفين المتوقع (1-1000)

##### 📍 **الموقع والاتصال:**
- 🌍 **الموقع الجغرافي**: الدولة، المحافظة، المدينة، المنطقة/المديرية
- 📍 **العنوان التفصيلي**: الشارع، رقم المبنى، معالم مميزة
- 📮 **البيانات البريدية**: الرمز البريدي، صندوق البريد
- 📞 **معلومات الاتصال**: الهاتف الرئيسي، الهاتف الفرعي، الفاكس، الجوال
- 📧 **الاتصال الإلكتروني**: البريد الإلكتروني، الموقع الإلكتروني
- 🌐 **الإحداثيات الجغرافية**: خط الطول، خط العرض (6 خانات عشرية)
- 📍 **تحديد الموقع على الخريطة**: ميزة تفاعلية (قيد التطوير)

##### 💰 **الإعدادات المالية:**
- 💱 **العملات**: العملة الأساسية، العملة الثانوية، سعر الصرف
- 🔄 **تحديث تلقائي**: تحديث أسعار الصرف تلقائياً
- 💰 **الحدود المالية**: حد المبيعات اليومي، حد المشتريات اليومي، حد الصندوق، حد الائتمان
- 📊 **الضرائب والرسوم**: معدل ضريبة المبيعات، رقم التسجيل الضريبي
- ✅ **الإعدادات الضريبية**: خاضع للضريبة، يطبق ضريبة القيمة المضافة

##### ⏰ **أوقات العمل:**
- 📅 **أيام العمل**: تحديد أيام العمل لكل يوم من أيام الأسبوع
- 🕐 **ساعات العمل**: وقت البداية ووقت النهاية لكل يوم
- ☕ **فترات الاستراحة**: استراحة الغداء (من/إلى)، استراحة الصلاة (المدة بالدقائق)
- 🎉 **العطل والإجازات**: العطل الرسمية، العطل الدينية، إجازة نهاية الأسبوع
- 📋 **أنواع الإجازات**: الجمعة، الجمعة والسبت، السبت والأحد، مخصص

##### 🛎️ **الخدمات والمرافق:**
- 🛎️ **الخدمات المتاحة**: خدمة العملاء، المبيعات، المشتريات، المحاسبة، الموارد البشرية، تقنية المعلومات، التسويق، المخازن، الصيانة، الأمن، التدريب، الاستشارات
- 🏗️ **المرافق والتجهيزات**: موقف سيارات، مصعد، مولد كهرباء، نظام إنذار، كاميرات مراقبة، نظام إطفاء، تكييف مركزي، إنترنت عالي السرعة، قاعة اجتماعات، كافتيريا، مصلى، عيادة طبية
- ℹ️ **معلومات إضافية**: عدد الطوابق، المساحة الإجمالية (متر مربع)، عدد المكاتب، أماكن انتظار العملاء

##### 🔒 **الأمان والصلاحيات:**
- 🔒 **إعدادات الأمان**: مستوى الأمان (منخفض، متوسط، عالي، عالي جداً)
- 📹 **أنظمة المراقبة**: نظام مراقبة، نظام إنذار، حراسة أمنية
- 🔑 **صلاحيات الوصول**: إدارة المبيعات، إدارة المشتريات، إدارة المخزون، إدارة العملاء، إدارة الموردين، إدارة الموظفين، إدارة التقارير، إدارة النظام، إدارة الحسابات، إدارة الصندوق، إدارة الفواتير، إدارة الخصومات
- 💾 **النسخ الاحتياطي**: نسخ احتياطي تلقائي، تكرار النسخ (يومي، أسبوعي، شهري)، مكان الحفظ (محلي، سحابي، كلاهما)

#### 🏢 **البيانات النموذجية (فرع صنعاء الرئيسي):**

##### 📋 **المعلومات الأساسية:**
- **الاسم**: فرع صنعاء الرئيسي
- **الاسم بالإنجليزية**: Sana'a Main Branch
- **الرمز**: SAN-001
- **النوع**: فرع رئيسي
- **المدير**: أحمد محمد علي الشامي
- **نائب المدير**: فاطمة أحمد سالم الحميري
- **السعة**: 75 موظف

##### 📍 **الموقع والاتصال:**
- **المحافظة**: محافظة صنعاء
- **المدينة**: مدينة صنعاء
- **المنطقة**: مديرية الثورة
- **العنوان**: شارع الزبيري، مجمع الأعمال التجاري، الطابق الثالث
- **الهاتف**: +967-1-123456
- **البريد الإلكتروني**: <EMAIL>

##### 💰 **الإعدادات المالية:**
- **العملة الأساسية**: الريال اليمني (YER)
- **العملة الثانوية**: الدولار الأمريكي (USD)
- **حد المبيعات اليومي**: 500,000 ريال
- **حد المشتريات اليومي**: 300,000 ريال
- **حد الصندوق**: 100,000 ريال
- **حد الائتمان**: 1,000,000 ريال
- **ضريبة المبيعات**: 15%

##### 🏗️ **المرافق:**
- **عدد الطوابق**: 3
- **المساحة الإجمالية**: 1,200 متر مربع
- **عدد المكاتب**: 25
- **أماكن انتظار العملاء**: 50

#### 🎮 **كيفية الاستخدام:**

##### 📋 **إدخال المعلومات الأساسية:**
1. انتقل لتبويب "المعلومات الأساسية"
2. أدخل اسم الفرع ورمزه
3. اختر نوع الفرع ومديره
4. حدد تواريخ التأسيس وبدء العمل
5. اكتب وصفاً مختصراً للفرع
6. فعل الإعدادات المطلوبة

##### 📍 **تحديد الموقع والاتصال:**
1. انتقل لتبويب "الموقع والاتصال"
2. اختر الدولة والمحافظة والمدينة
3. أدخل العنوان التفصيلي
4. أدخل معلومات الاتصال كاملة
5. حدد الإحداثيات الجغرافية

##### 💰 **إعداد الإعدادات المالية:**
1. انتقل لتبويب "الإعدادات المالية"
2. اختر العملات المستخدمة
3. حدد الحدود المالية اليومية
4. أدخل معدلات الضرائب
5. فعل الإعدادات الضريبية

##### ⏰ **تحديد أوقات العمل:**
1. انتقل لتبويب "أوقات العمل"
2. حدد أيام العمل وساعات العمل
3. أعد فترات الاستراحة
4. اختر نوع إجازة نهاية الأسبوع

##### 🛎️ **تحديد الخدمات والمرافق:**
1. انتقل لتبويب "الخدمات والمرافق"
2. اختر الخدمات المتاحة
3. حدد المرافق والتجهيزات
4. أدخل المعلومات الإضافية

##### 🔒 **إعداد الأمان والصلاحيات:**
1. انتقل لتبويب "الأمان والصلاحيات"
2. حدد مستوى الأمان
3. فعل أنظمة المراقبة
4. اختر الصلاحيات المطلوبة
5. أعد النسخ الاحتياطي

##### 👁️ **معاينة وحفظ:**
1. اضغط "معاينة التعريف" لمراجعة البيانات
2. تأكد من صحة جميع المعلومات
3. اضغط "حفظ تعريف الفرع" للحفظ النهائي

---

## 🔢 **10. ترميز الحسابات**

### 📋 **الوصف:**
نظام متقدم لترميز وترقيم الحسابات المحاسبية بطرق مختلفة ومرنة مع إمكانيات التحقق والإصلاح التلقائي.

### 🎯 **الهدف:**
- إنشاء نظام ترميز منطقي ومتسق للحسابات
- تطبيق قواعد ترقيم مرنة ومتعددة الأنماط
- التحقق من صحة الترميز وإصلاح الأخطاء
- إنتاج تقارير شاملة عن نظام الترميز

### 📊 **المكونات الرئيسية:**

#### 🔢 **أنظمة الترميز (5 أنظمة):**
- **النظام العشري**: 10, 20, 30, 40, 50...
- **النظام المئوي**: 100, 200, 300, 400, 500...
- **النظام الألفي**: 1000, 2000, 3000, 4000, 5000...
- **النظام الهجين**: 1-100, 2-100, 3-100, 4-100...
- **النظام المخصص**: حسب احتياجات المؤسسة

#### ⚙️ **إعدادات النظام:**
- **طول الرمز**: من 3 إلى 15 خانة
- **عدد المستويات**: من 2 إلى 8 مستويات هرمية
- **فاصل المستويات**: بدون فاصل، نقطة، شرطة، شرطة مائلة، مسافة
- **بادئة ولاحقة الرمز**: إضافة رموز مخصصة
- **ترقيم تلقائي**: للحسابات الجديدة

#### 📋 **قواعد الترميز:**
- **قواعد المستويات**: تحديد نمط كل مستوى هرمي
- **نطاقات الترقيم**: تحديد البداية والنهاية لكل مستوى
- **أمثلة توضيحية**: لكل قاعدة ترميز
- **قواعد التحقق**: 4 قواعد للتحقق من صحة الترميز

#### ✅ **تطبيق الترميز:**
- **إنتاج الرموز**: توليد رموز مقترحة للحسابات
- **تطبيق انتقائي**: على حسابات محددة
- **تطبيق شامل**: لجميع الحسابات
- **معالجة التعارضات**: 4 طرق مختلفة للمعالجة
- **شريط التقدم**: لمتابعة عملية التطبيق

#### 🔍 **التحقق والمراجعة:**
- **فحص التكرارات**: البحث عن الرموز المكررة
- **فحص الفجوات**: اكتشاف الفجوات في التسلسل
- **فحص الهيكل**: التحقق من الهيكل الهرمي
- **فحص شامل**: جميع أنواع الفحص معاً
- **إصلاح تلقائي**: لجميع أنواع الأخطاء

#### 📊 **التقارير:**
- **ملخص الترميز**: نظرة عامة على النظام
- **قائمة الرموز**: جميع الرموز مرتبة ومنظمة
- **تقرير التحقق**: نتائج فحص النظام
- **إحصائيات مفصلة**: أرقام وتحليلات شاملة

### 🔢 **البيانات النموذجية:**
- **19 حساب** جاهز للترميز
- **4 مستويات** هرمية محددة مسبقاً
- **أنماط ترميز** متنوعة لكل مستوى
- **رموز مقترحة** ذكية حسب نوع الحساب

### 🎮 **كيفية الاستخدام:**
1. انقر على "10. ترميز الحسابات" من مجلد التهيئة
2. اختر نظام الترميز المناسب من التبويب الأول
3. حدد قواعد الترميز في التبويب الثاني
4. اضغط "إنتاج الرموز" في تبويب التطبيق
5. استخدم تبويب التحقق للتأكد من صحة النظام
6. راجع التقارير في التبويب الأخير

### 🌟 **المميزات:**
- ✅ **5 أنظمة ترميز** مختلفة ومرنة
- ✅ **إعدادات متقدمة** للتخصيص الكامل
- ✅ **قواعد ترميز ذكية** مع أمثلة توضيحية
- ✅ **تطبيق تدريجي أو فوري** حسب الحاجة
- ✅ **فحص شامل** مع إصلاح تلقائي
- ✅ **تقارير مفصلة** وإحصائيات دقيقة
- ✅ **معاينة فورية** لنظام الترميز
- ✅ **معالجة ذكية للتعارضات**

---

## 🔗 **11. ربط الحسابات بإدخال المشاريع**

### 📋 **الوصف:**
نظام متقدم لربط الحسابات المحاسبية بالمشاريع لتتبع التكاليف والإيرادات وإنتاج تقارير مالية مفصلة لكل مشروع.

### 🎯 **الهدف:**
- ربط الحسابات المحاسبية بالمشاريع المختلفة
- تتبع تكاليف وإيرادات كل مشروع بدقة
- إنشاء قواعد ربط مرنة ومخصصة
- إنتاج تقارير مالية مفصلة للمشاريع

### 📊 **المكونات الرئيسية:**

#### 📊 **شجرة الحسابات:**
- **عرض هرمي**: شجرة تفاعلية لجميع الحسابات
- **حالة الربط**: تلوين مرئي للحسابات (مربوط/غير مربوط)
- **إحصائيات فورية**: عدد الحسابات المربوطة وغير المربوطة
- **بحث وتصفية**: للوصول السريع للحسابات

#### 🔗 **إعداد الربط:**
- **أنواع الربط**: إجباري، اختياري، تلقائي، شرطي
- **مستويات الربط**: مشروع رئيسي، فرعي، مرحلة، نشاط
- **المشروع الافتراضي**: تحديد مشروع افتراضي لكل حساب
- **مركز التكلفة**: ربط بمراكز التكلفة المختلفة
- **إعدادات الموافقة**: موافقة مدير المشروع والقيود التلقائية

#### 📁 **إدارة المشاريع:**
- **معلومات المشروع**: الاسم، الرمز، النوع، الحالة، التواريخ
- **أنواع المشاريع**: داخلي، خارجي، استثماري، تطويري، صيانة
- **حالات المشروع**: مخطط، قيد التنفيذ، متوقف، مكتمل، ملغي
- **تتبع الحسابات**: عدد الحسابات المربوطة لكل مشروع

#### 📋 **قواعد الربط:**
- **قواعد عامة**: ربط إجباري للمصروفات والإيرادات
- **قواعد مخصصة**: قواعد خاصة لحسابات محددة
- **مستويات التحقق**: أساسي، متوسط، صارم
- **معالجة الأخطاء**: تحذير، منع، سؤال المستخدم، تجاهل

#### 📊 **التقارير:**
- **ملخص الربط**: نظرة عامة شاملة على حالة الربط
- **حسابات المشاريع**: تقرير مفصل لحسابات كل مشروع
- **الحسابات غير المربوطة**: قائمة بالحسابات التي تحتاج ربط
- **إحصائيات وتحليلات**: أرقام ومؤشرات مفصلة

### 🔗 **البيانات النموذجية:**
- **19 حساب محاسبي** (12 مربوط، 7 غير مربوط)
- **5 مشاريع نموذجية** بأنواع وحالات مختلفة
- **4 قواعد ربط مخصصة** للحسابات المختلفة
- **إعدادات ربط متقدمة** لكل نوع حساب

### 📁 **المشاريع النموذجية:**
- **مشروع تطوير النظام المحاسبي (PROJ-001)**: 5 حسابات مربوطة
- **مشروع تطوير المباني (PROJ-002)**: 3 حسابات مربوطة
- **مشروع تحديث الأنظمة (PROJ-003)**: 2 حساب مربوط
- **مشروع تحليل البيانات (PROJ-004)**: 1 حساب مربوط
- **مشروع الصيانة العامة (PROJ-005)**: 1 حساب مربوط

### 🎮 **كيفية الاستخدام:**
1. انقر على "11. ربط الحسابات بإدخال المشاريع" من مجلد التهيئة
2. اختر حساب من الشجرة في الجانب الأيسر
3. فعل الربط وحدد نوعه في تبويب "إعداد الربط"
4. اختر المشروع المناسب من القائمة
5. راجع المشاريع وأضف مشاريع جديدة في تبويب "المشاريع"
6. اضبط قواعد الربط في تبويب "قواعد الربط"
7. راجع التقارير في تبويب "التقارير"

### 🌟 **المميزات:**
- ✅ **شجرة حسابات تفاعلية** مع تلوين مرئي لحالة الربط
- ✅ **4 أنواع ربط مختلفة** (إجباري، اختياري، تلقائي، شرطي)
- ✅ **إدارة مشاريع متكاملة** مع 5 أنواع و5 حالات مختلفة
- ✅ **قواعد ربط مرنة** عامة ومخصصة
- ✅ **تقارير شاملة** مع إحصائيات مفصلة
- ✅ **اختبار الربط** قبل التطبيق النهائي
- ✅ **إعدادات متقدمة** للموافقة والقيود التلقائية
- ✅ **بيانات نموذجية يمنية** جاهزة للاستخدام

---

## 🏗️ **12. أنواع الهيكل الإداري**

### 📋 **الوصف:**
نظام شامل لإدارة أنواع الهياكل الإدارية والتنظيمية، مع إدارة المناصب والوظائف وعرض الهيكل التنظيمي المرئي.

### 🎯 **الهدف:**
- إدارة أنواع الهياكل الإدارية المختلفة
- تنظيم المناصب والوظائف في المؤسسة
- عرض الهيكل التنظيمي بشكل مرئي وتفاعلي
- إنتاج تقارير شاملة عن التسلسل الهرمي

### 📊 **المكونات الرئيسية:**

#### 🏗️ **أنواع الهياكل:**
- **5 أنواع هياكل**: هرمي، مصفوفي، مسطح، وظيفي، شبكي
- **تحديد المستويات**: من 1 إلى 20 مستوى لكل نوع
- **وصف تفصيلي**: لكل نوع هيكل ومميزاته
- **حالة النشاط**: تفعيل وإلغاء تفعيل الأنواع

#### 👔 **المناصب والوظائف:**
- **8 مناصب نموذجية**: من المدير العام إلى الموظف الإداري
- **4 مستويات إدارية**: مع تلوين مرئي لكل مستوى
- **إدارة الرواتب**: الراتب الأساسي لكل منصب
- **تحديد المسؤوليات**: وصف مفصل لمهام كل منصب
- **ربط بالأقسام**: تصنيف المناصب حسب الأقسام

#### 📊 **الهيكل التنظيمي المرئي:**
- **شجرة تفاعلية**: عرض هرمي للمناصب والأقسام
- **تلوين حسب المستوى**: أحمر للإدارة العليا، أصفر للمدراء، أخضر للمشرفين، أزرق للموظفين
- **معلومات شاملة**: المنصب، الموظف، القسم، المستوى
- **تحديث تلقائي**: يتحدث مع تغيير البيانات

#### 📋 **التقارير المتقدمة:**
- **تقرير أنواع الهياكل**: قائمة شاملة بجميع الأنواع وخصائصها
- **تقرير المناصب**: تفاصيل المناصب والرواتب والمسؤوليات
- **تقرير التسلسل الهرمي**: عرض الهيكل التنظيمي نصياً مع الإحصائيات

### 🏗️ **أنواع الهياكل النموذجية:**
- **الهيكل الهرمي التقليدي (HIER001)**: 5 مستويات - هيكل تقليدي مع تسلسل واضح
- **الهيكل المصفوفي (MATRIX001)**: 4 مستويات - يجمع بين التخصص والمشاريع
- **الهيكل المسطح (FLAT001)**: 3 مستويات - عدد قليل من المستويات الإدارية
- **الهيكل الوظيفي (FUNC001)**: 4 مستويات - تنظيم حسب الوظائف والتخصصات
- **الهيكل الشبكي (NETWORK001)**: 3 مستويات - هيكل مرن للتعاون بين الأقسام

### 👔 **المناصب النموذجية:**
- **المستوى 1 (أحمر)**: المدير العام - 500,000 ريال
- **المستوى 2 (أصفر)**: المدير المالي (350,000)، مدير الموارد البشرية (300,000)، مدير العمليات (320,000)
- **المستوى 3 (أخضر)**: محاسب أول (180,000)، أخصائي موارد بشرية (150,000)، مشرف عمليات (160,000)
- **المستوى 4 (أزرق)**: موظف إداري - 100,000 ريال

### 🎮 **كيفية الاستخدام:**
1. انقر على "12. أنواع الهيكل الإداري" من مجلد التهيئة
2. استخدم تبويب "أنواع الهياكل" لإدارة أنواع الهياكل المختلفة
3. انتقل إلى تبويب "المناصب والوظائف" لإدارة المناصب والرواتب
4. راجع الهيكل التنظيمي في تبويب "الهيكل التنظيمي"
5. أنتج التقارير من تبويب "التقارير"

### 🌟 **المميزات:**
- ✅ **4 تبويبات شاملة** للإدارة المتكاملة
- ✅ **5 أنواع هياكل مختلفة** مع خصائص متنوعة
- ✅ **8 مناصب نموذجية** مع 4 مستويات إدارية
- ✅ **هيكل تنظيمي مرئي** مع تلوين حسب المستوى
- ✅ **3 أنواع تقارير** شاملة ومفصلة
- ✅ **إدارة الرواتب** مع إجمالي 2,060,000 ريال
- ✅ **تصنيف الأقسام** مع 5 أقسام مختلفة
- ✅ **واجهة عربية احترافية** مع تصميم متقدم

---

## 🦺 **13. أنواع السلامة المهنية**

### 📋 **الوصف:**
نظام شامل لإدارة السلامة والصحة المهنية، يشمل أنواع السلامة المختلفة، إجراءات الوقاية، إدارة الحوادث، وبرامج التدريب.

### 🎯 **الهدف:**
- إدارة أنواع السلامة المهنية المختلفة
- تخطيط وتنفيذ إجراءات السلامة والوقاية
- تسجيل ومتابعة الحوادث والمخاطر
- تصميم وتنفيذ برامج التدريب المتخصصة
- ضمان الامتثال للمعايير والقوانين

### 📊 **المكونات الرئيسية:**

#### 🦺 **أنواع السلامة (6 أنواع):**
- **السلامة من الحرائق**: إجراءات الوقاية ومكافحة الحرائق (عالي الخطورة)
- **السلامة الكيميائية**: التعامل الآمن مع المواد الخطرة (حرج)
- **السلامة الكهربائية**: الوقاية من مخاطر الكهرباء (عالي الخطورة)
- **السلامة الميكانيكية**: الوقاية من مخاطر الآلات (متوسط الخطورة)
- **السلامة النفسية**: الحفاظ على الصحة النفسية (متوسط الخطورة)
- **السلامة البيئية**: حماية البيئة من التلوث (متوسط الخطورة)

#### 🛡️ **إجراءات السلامة (3 إجراءات):**
- **تركيب أجهزة إنذار الحريق**: 50,000 ريال - مكتمل
- **توفير معدات الحماية الشخصية**: 75,000 ريال - قيد التنفيذ
- **فحص الأنظمة الكهربائية**: 30,000 ريال - مخطط

#### ⚠️ **إدارة الحوادث (2 حادث):**
- **حريق صغير في المخزن**: متوسط الخطورة - مغلق
- **انسكاب مواد كيميائية**: عالي الخطورة - قيد التحقيق

#### 📚 **برامج التدريب (2 برنامج):**
- **برنامج مكافحة الحرائق**: 25 متدرب، 15,000 ريال - مجدول
- **التعامل مع المواد الخطرة**: 15 متدرب، 25,000 ريال - مكتمل

#### 📊 **التقارير المتقدمة (4 أنواع):**
- **تقرير أنواع السلامة**: قائمة شاملة بجميع الأنواع والتصنيفات
- **تقرير الحوادث**: تحليل الحوادث والمخاطر مع الإحصائيات
- **تقرير التدريب**: برامج التدريب والمتدربين والتكاليف
- **تقرير الامتثال**: تقييم الامتثال للمعايير والقوانين

### 🏷️ **التصنيفات المتاحة (8 تصنيفات):**
- **سلامة فيزيائية**: الحرائق، الميكانيكية
- **سلامة كيميائية**: المواد الخطرة والسامة
- **سلامة بيولوجية**: العدوى والتلوث البيولوجي
- **سلامة إشعاعية**: الإشعاع المؤين وغير المؤين
- **سلامة كهربائية**: الصعق والحرائق الكهربائية
- **سلامة ميكانيكية**: الآلات والمعدات
- **سلامة نفسية**: الضغط والتوتر المهني
- **سلامة بيئية**: التلوث والنفايات

### 🎮 **كيفية الاستخدام:**
1. انقر على "13. أنواع السلامة المهنية" من مجلد التهيئة
2. استخدم تبويب "أنواع السلامة" لإدارة أنواع السلامة المختلفة
3. انتقل إلى "إجراءات السلامة" لتخطيط وتنفيذ الإجراءات
4. سجل الحوادث في تبويب "الحوادث والمخاطر"
5. صمم برامج التدريب في تبويب "برامج التدريب"
6. راجع التقارير والإحصائيات في تبويب "التقارير"

### 🌟 **المميزات:**
- ✅ **5 تبويبات شاملة** للإدارة المتكاملة
- ✅ **6 أنواع سلامة مختلفة** مع 4 مستويات خطورة
- ✅ **8 تصنيفات سلامة** شاملة ومتخصصة
- ✅ **إدارة الحوادث** مع تحليل المخاطر
- ✅ **برامج التدريب** المتخصصة والمجدولة
- ✅ **4 أنواع تقارير** شاملة ومفصلة
- ✅ **متطلبات الامتثال** للمعايير المحلية والدولية
- ✅ **واجهة عربية احترافية** مع تصميم متقدم

---

## 🚧 **الشاشات قيد التطوير (2/15)**

### 📋 **قائمة الشاشات المتبقية:**
14. **مجموعة الموظفين** 👥
15. **تعريف أصناف العملاء** 🏷️

---

## 🎮 **كيفية الوصول للشاشات**

### 🖱️ **من النظام الرئيسي:**
1. شغل النظام الرئيسي: `python run_main_system.py`
2. انقر على **"📁 التهيئة"** في شجرة النظام (الجانب الأيمن)
3. اختر الشاشة المطلوبة:
   - **"01. المؤشرات العامة"**
   - **"02. إعداد قواعد النظام"**
   - **"03. تهيئة العملات"**
   - **"04. الأقاليم الدولية / الدول"**
   - **"05. بيانات المحافظات / المناطق / المدن"**
   - **"06. مجموعات المحافظات"**
   - **"07. شجرة الفروع"**
   - **"08. تعريف الفرع"**
   - **"09. إعداد الدليل المحاسبي"**
   - **"10. ترميز الحسابات"**
   - **"11. ربط الحسابات بإدخال المشاريع"**
   - **"12. أنواع الهيكل الإداري"**
   - **"13. أنواع السلامة المهنية"**

### 🧪 **اختبار مستقل:**
```bash
# اختبار جميع الشاشات
python test_setup_modules.py

# اختبار شاشة واحدة
python src/modules/setup/general_indicators.py
python src/modules/setup/system_rules.py
python src/modules/setup/currency_setup.py
python src/modules/setup/international_regions.py
python src/modules/setup/administrative_divisions.py
python src/modules/setup/governorate_groups.py
python src/modules/setup/branch_tree.py
python src/modules/setup/branch_definition.py
python src/modules/setup/accounting_guide.py
python src/modules/setup/account_coding.py
python src/modules/setup/account_project_linking.py
python src/modules/setup/administrative_structure_types.py
python src/modules/setup/occupational_safety_types.py
```

---

## 🎯 **الميزات التقنية**

### 🎨 **التصميم:**
- ✅ **واجهة عربية** كاملة من اليمين لليسار
- ✅ **تبويبات منظمة** لتجميع الإعدادات المترابطة
- ✅ **ألوان تفاعلية** للأزرار والحالات
- ✅ **رسائل واضحة** للنجاح والأخطاء
- ✅ **تلميحات مفيدة** للمساعدة

### 🔧 **الوظائف:**
- ✅ **حفظ وتحميل** الإعدادات
- ✅ **قيم افتراضية** ذكية
- ✅ **التحقق من صحة البيانات** قبل الحفظ
- ✅ **رسائل تأكيد** للعمليات المهمة
- ✅ **معالجة الأخطاء** الشاملة

### 🗄️ **قاعدة البيانات:**
- ✅ **هيكل جاهز** لحفظ جميع الإعدادات
- ✅ **تكامل مع النظام الرئيسي**
- ✅ **إمكانية التوسع** لإضافة إعدادات جديدة

---

## 🎉 **النتيجة النهائية**

**🏆 تم تطوير 13 شاشة أساسية من مجلد التهيئة بنجاح:**

📊 **المؤشرات العامة** - إعداد شامل لخصائص النظام
🔧 **إعداد قواعد النظام** - تحكم كامل في سلوك النظام
💱 **تهيئة العملات** - إدارة متقدمة للعملات وأسعار الصرف
🌍 **الأقاليم الدولية / الدول** - التقسيم الجغرافي الدولي الشامل
🏙️ **بيانات المحافظات / المناطق / المدن** - الهيكل الإداري الداخلي المتكامل
🗺️ **مجموعات المحافظات** - تجميع المحافظات حسب المعايير المختلفة
🌳 **شجرة الفروع** - الهيكل التنظيمي للفروع والمكاتب
🏢 **تعريف الفرع** - الإعداد الشامل والتفصيلي للفرع
📚 **إعداد الدليل المحاسبي** - شجرة الحسابات المتكاملة
🔢 **ترميز الحسابات** - نظام الترقيم والتصنيف المحاسبي
🔗 **ربط الحسابات بإدخال المشاريع** - نظام الربط المحاسبي للمشاريع
🏗️ **أنواع الهيكل الإداري** - تنظيم الهيكل الوظيفي والإداري
🦺 **أنواع السلامة المهنية** - نظام إدارة السلامة والصحة المهنية

**🎯 جاهزة للاستخدام الاحترافي مع إمكانية التوسع لإضافة باقي الشاشات!**

**📈 نسبة الإنجاز: 87% من مجلد التهيئة (13/15 شاشة)**
