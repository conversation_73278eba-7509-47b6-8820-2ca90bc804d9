#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عرض توضيحي لواجهة بيانات العملاء - نظام Onyx Pro ERP
Customer Data Interface Demo - Onyx Pro ERP System
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """عرض توضيحي لواجهة بيانات العملاء"""
    print("🧾 عرض توضيحي لواجهة بيانات العملاء - Onyx Pro ERP")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.customers.customer_data_interface import CustomerDataInterface
        
        # محاكاة مدير قاعدة البيانات مع بيانات تجريبية
        class DemoDBManager:
            def __init__(self):
                self.cursor = type('obj', (object,), {'lastrowid': 1})()
                self.customers = [
                    (
                        1, "MAIN001", "عميل تجاري", "ACC001", "أحمد", "تصنيف أ", 
                        "مجموعة 1", "CUST001", "شركة أحمد التجارية المحدودة", "ahmed123", "pass123",
                        "شركة أحمد التجارية المحدودة", "أحمد محمد علي", "0112345678", "0501234567",
                        "<EMAIL>", "الرياض - حي النخيل - شارع الملك فهد", "الفرع الرئيسي",
                        "COL001", 30, 45, "خط الرياض", 1, "MARK001", "EMP001", "GRADE1",
                        "MEM001", "مركز التكلفة الرئيسي", "SUP001", "ريال سعودي", "ريال سعودي",
                        "مستوى 1", "مستوى 2", "حملة الصيف 2024", 0, 0, "مدير عام",
                        "الإدارة العامة", "الرياض - مجمع الأعمال - برج النخيل", "0112345679", "0112345680",
                        "<EMAIL>", "1985-03-15", "سعودي", "1234567890",
                        "A12345678", "متزوج", "عميل مميز منذ 2020", "مستوى 3", 100000.00, 15.00,
                        1, 0, 1, 0, "TAX123456789", "CR1234567890", "البنك الأهلي السعودي",
                        "1234567890", "SA1234567890123456789012", "www.ahmed-company.com",
                        "عميل مميز يتعامل معنا منذ 5 سنوات", "حقل إضافي 1", "حقل إضافي 2", "", "", "", "", "", "", "", "",
                        8, 12, "نظام Onyx Pro", "نظام Onyx Pro", "2024-01-15 10:30:00", 
                        "2024-01-28 14:45:00", "مدير النظام", "محاسب رئيسي", 1
                    ),
                    (
                        2, "MAIN002", "عميل حكومي", "ACC002", "فاطمة", "تصنيف أ", 
                        "مجموعة 2", "CUST002", "وزارة التعليم", "fatima456", "pass456",
                        "وزارة التعليم", "فاطمة سالم", "0112345681", "0501234568",
                        "<EMAIL>", "الرياض - حي الوزارات", "فرع الحكومي",
                        "COL002", 60, 90, "خط الحكومي", 2, "MARK002", "EMP002", "GRADE2",
                        "MEM002", "مركز الحكومي", "", "ريال سعودي", "ريال سعودي",
                        "مستوى حكومي", "مستوى حكومي", "حملة الحكومة", 0, 0, "مديرة المشتريات",
                        "إدارة المشتريات", "الرياض - وزارة التعليم", "0112345682", "0112345683",
                        "<EMAIL>", "1980-07-20", "سعودية", "2345678901",
                        "B23456789", "متزوجة", "جهة حكومية", "مستوى حكومي", 500000.00, 5.00,
                        0, 1, 1, 0, "GOVTAX987654321", "GOVCR0987654321", "مؤسسة النقد",
                        "GOV9876543210", "SA9876543210987654321098", "www.moe.gov.sa",
                        "جهة حكومية - وزارة التعليم", "", "", "", "", "", "", "", "", "", "",
                        3, 5, "نظام Onyx Pro", "نظام Onyx Pro", "2024-01-10 09:00:00", 
                        "2024-01-25 11:20:00", "مدير النظام", "محاسب حكومي", 1
                    )
                ]
                self.current_index = 0
            
            def execute_query(self, query, params=None):
                if "SELECT * FROM customers" in query:
                    if "LIMIT 1" in query:
                        if "ORDER BY id LIMIT 1" in query:  # الأول
                            self.current_index = 0
                            return [self.customers[0]] if self.customers else []
                        elif "ORDER BY id DESC LIMIT 1" in query:  # الأخير
                            self.current_index = len(self.customers) - 1
                            return [self.customers[-1]] if self.customers else []
                        elif "id < ?" in query:  # السابق
                            if self.current_index > 0:
                                self.current_index -= 1
                                return [self.customers[self.current_index]]
                            return []
                        elif "id > ?" in query:  # التالي
                            if self.current_index < len(self.customers) - 1:
                                self.current_index += 1
                                return [self.customers[self.current_index]]
                            return []
                        else:
                            return [self.customers[0]] if self.customers else []
                    elif "LIKE" in query:  # البحث
                        return [self.customers[0]] if self.customers else []
                return []
            
            def execute_update(self, query, params=None):
                return True
        
        db_manager = DemoDBManager()
        
        # إنشاء واجهة بيانات العملاء
        interface = CustomerDataInterface(db_manager)
        
        # تحميل بيانات تجريبية
        interface.first_record()
        
        # عرض رسالة ترحيب
        QMessageBox.information(
            interface, 
            "مرحباً بك في Onyx Pro ERP", 
            """🧾 واجهة بيانات العملاء

🎯 المواصفات المطبقة:
✅ خلفية وردية فاتحة
✅ شريط أدوات علوي كامل
✅ اسم النظام Onyx Pro ERP
✅ البيانات الرئيسية في الأعلى
✅ 7 تبويبات منظمة
✅ بيانات التتبع في الأسفل
✅ دعم RTL للعربية

🛠️ الوظائف المتاحة:
💾 حفظ وتعديل البيانات
🔍 البحث والتنقل
🖨️ الطباعة والمعاينة
📊 تتبع التعديلات

جرب التنقل بين السجلات والتبويبات!"""
        )
        
        # عرض الواجهة
        interface.show()
        
        print("🎉 تم عرض واجهة بيانات العملاء بنجاح!")
        print("\n🧾 مواصفات الواجهة المطبقة:")
        print("   ✅ خلفية وردية فاتحة (#fdf2f8)")
        print("   ✅ شريط أدوات علوي مع 11 أيقونة")
        print("   ✅ اسم النظام Onyx Pro ERP في الأعلى")
        print("   ✅ البيانات الرئيسية (10 حقول)")
        print("   ✅ 7 تبويبات كما هو مطلوب:")
        print("       - البيانات الرئيسية")
        print("       - بيانات أخرى")
        print("       - العنوان الوظيفي")
        print("       - البيانات الشخصية")
        print("       - التفويض والمعرفات")
        print("       - بيانات إضافية")
        print("       - حقول إضافية")
        print("   ✅ بيانات التتبع في الأسفل (8 حقول)")
        print("   ✅ دعم RTL للعربية")
        print("   ✅ تصميم ERP احترافي")
        
        print("\n🎯 الوظائف المتاحة:")
        print("   💾 حفظ - تعديل - حذف")
        print("   ⏮️⏪⏩⏭️ التنقل بين السجلات")
        print("   🖨️ طباعة ومعاينة")
        print("   🔍 بحث متقدم")
        print("   📊 تتبع التعديلات والطباعة")
        
        print("\n🔄 البيانات التجريبية المحملة:")
        print("   👤 شركة أحمد التجارية المحدودة")
        print("   🏛️ وزارة التعليم")
        print("   📊 جميع الحقول مملوءة بالبيانات")
        
        print("\n🚀 جرب الوظائف التالية:")
        print("   1. انقر على التبويبات المختلفة")
        print("   2. استخدم أزرار التنقل في شريط الأدوات")
        print("   3. جرب البحث والمعاينة")
        print("   4. اختبر حفظ وتعديل البيانات")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في عرض الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    main()
