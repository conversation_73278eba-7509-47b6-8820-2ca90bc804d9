#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات المحسن والمتطور
Enhanced Database Manager
"""

import sqlite3
import mysql.connector
import psycopg2
import pymongo
import redis
import bcrypt
import hashlib
import json
import os
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from contextlib import contextmanager
from dataclasses import dataclass, asdict
from enum import Enum
import uuid
import pickle
import gzip
import shutil

# إعداد نظام السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('database.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class DatabaseType(Enum):
    """أنواع قواعد البيانات المدعومة"""
    SQLITE = "sqlite"
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    MONGODB = "mongodb"
    REDIS = "redis"

class ConnectionPoolManager:
    """مدير مجموعة الاتصالات"""
    
    def __init__(self, db_type: DatabaseType, config: Dict, pool_size: int = 10):
        self.db_type = db_type
        self.config = config
        self.pool_size = pool_size
        self.connections = []
        self.available_connections = []
        self.used_connections = []
        self.lock = threading.Lock()
        self.logger = logging.getLogger(f"ConnectionPool-{db_type.value}")
        
        self._initialize_pool()
    
    def _initialize_pool(self):
        """تهيئة مجموعة الاتصالات"""
        try:
            for _ in range(self.pool_size):
                connection = self._create_connection()
                if connection:
                    self.connections.append(connection)
                    self.available_connections.append(connection)
            
            self.logger.info(f"تم إنشاء مجموعة اتصالات بحجم {len(self.connections)}")
            
        except Exception as e:
            self.logger.error(f"خطأ في تهيئة مجموعة الاتصالات: {e}")
            raise
    
    def _create_connection(self):
        """إنشاء اتصال جديد"""
        try:
            if self.db_type == DatabaseType.SQLITE:
                return sqlite3.connect(
                    self.config.get('database', 'accounting_system.db'),
                    check_same_thread=False
                )
            
            elif self.db_type == DatabaseType.MYSQL:
                return mysql.connector.connect(
                    host=self.config['host'],
                    port=self.config['port'],
                    user=self.config['user'],
                    password=self.config['password'],
                    database=self.config['database'],
                    charset='utf8mb4',
                    autocommit=False
                )
            
            elif self.db_type == DatabaseType.POSTGRESQL:
                return psycopg2.connect(
                    host=self.config['host'],
                    port=self.config['port'],
                    user=self.config['user'],
                    password=self.config['password'],
                    database=self.config['database']
                )
            
            elif self.db_type == DatabaseType.MONGODB:
                client = pymongo.MongoClient(
                    host=self.config['host'],
                    port=self.config['port'],
                    username=self.config.get('user'),
                    password=self.config.get('password')
                )
                return client[self.config['database']]
            
            elif self.db_type == DatabaseType.REDIS:
                return redis.Redis(
                    host=self.config['host'],
                    port=self.config['port'],
                    password=self.config.get('password'),
                    decode_responses=True
                )
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الاتصال: {e}")
            return None
    
    @contextmanager
    def get_connection(self):
        """الحصول على اتصال من المجموعة"""
        connection = None
        try:
            with self.lock:
                if self.available_connections:
                    connection = self.available_connections.pop()
                    self.used_connections.append(connection)
                else:
                    # إنشاء اتصال جديد إذا لم تتوفر اتصالات
                    connection = self._create_connection()
                    if connection:
                        self.used_connections.append(connection)
            
            if connection:
                yield connection
            else:
                raise Exception("فشل في الحصول على اتصال قاعدة البيانات")
                
        finally:
            if connection:
                with self.lock:
                    if connection in self.used_connections:
                        self.used_connections.remove(connection)
                        self.available_connections.append(connection)
    
    def close_all(self):
        """إغلاق جميع الاتصالات"""
        with self.lock:
            for connection in self.connections:
                try:
                    if hasattr(connection, 'close'):
                        connection.close()
                except:
                    pass
            
            self.connections.clear()
            self.available_connections.clear()
            self.used_connections.clear()

@dataclass
class QueryResult:
    """نتيجة الاستعلام"""
    success: bool
    data: Any = None
    error: str = None
    affected_rows: int = 0
    execution_time: float = 0.0
    query: str = ""

class CacheManager:
    """مدير التخزين المؤقت"""
    
    def __init__(self, cache_type: str = "memory", redis_config: Dict = None):
        self.cache_type = cache_type
        self.memory_cache = {}
        self.cache_expiry = {}
        self.lock = threading.Lock()
        self.logger = logging.getLogger("CacheManager")
        
        if cache_type == "redis" and redis_config:
            try:
                self.redis_client = redis.Redis(**redis_config, decode_responses=True)
                self.redis_client.ping()
                self.logger.info("تم الاتصال بـ Redis للتخزين المؤقت")
            except Exception as e:
                self.logger.warning(f"فشل الاتصال بـ Redis، سيتم استخدام الذاكرة: {e}")
                self.cache_type = "memory"
                self.redis_client = None
        else:
            self.redis_client = None
    
    def get(self, key: str) -> Any:
        """الحصول على قيمة من التخزين المؤقت"""
        try:
            if self.cache_type == "redis" and self.redis_client:
                value = self.redis_client.get(key)
                if value:
                    return json.loads(value)
                return None
            
            else:
                with self.lock:
                    # فحص انتهاء الصلاحية
                    if key in self.cache_expiry:
                        if datetime.now() > self.cache_expiry[key]:
                            del self.memory_cache[key]
                            del self.cache_expiry[key]
                            return None
                    
                    return self.memory_cache.get(key)
                    
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على القيمة من التخزين المؤقت: {e}")
            return None
    
    def set(self, key: str, value: Any, expiry_seconds: int = 3600):
        """تعيين قيمة في التخزين المؤقت"""
        try:
            if self.cache_type == "redis" and self.redis_client:
                self.redis_client.setex(key, expiry_seconds, json.dumps(value, default=str))
            
            else:
                with self.lock:
                    self.memory_cache[key] = value
                    self.cache_expiry[key] = datetime.now() + timedelta(seconds=expiry_seconds)
                    
        except Exception as e:
            self.logger.error(f"خطأ في تعيين القيمة في التخزين المؤقت: {e}")
    
    def delete(self, key: str):
        """حذف قيمة من التخزين المؤقت"""
        try:
            if self.cache_type == "redis" and self.redis_client:
                self.redis_client.delete(key)
            
            else:
                with self.lock:
                    self.memory_cache.pop(key, None)
                    self.cache_expiry.pop(key, None)
                    
        except Exception as e:
            self.logger.error(f"خطأ في حذف القيمة من التخزين المؤقت: {e}")
    
    def clear(self):
        """مسح جميع القيم من التخزين المؤقت"""
        try:
            if self.cache_type == "redis" and self.redis_client:
                self.redis_client.flushdb()
            
            else:
                with self.lock:
                    self.memory_cache.clear()
                    self.cache_expiry.clear()
                    
        except Exception as e:
            self.logger.error(f"خطأ في مسح التخزين المؤقت: {e}")

class BackupManager:
    """مدير النسخ الاحتياطية"""
    
    def __init__(self, backup_dir: str = "backups"):
        self.backup_dir = backup_dir
        self.logger = logging.getLogger("BackupManager")
        
        # إنشاء مجلد النسخ الاحتياطية
        os.makedirs(backup_dir, exist_ok=True)
    
    def create_backup(self, db_path: str, backup_name: str = None) -> str:
        """إنشاء نسخة احتياطية"""
        try:
            if not backup_name:
                backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            
            backup_path = os.path.join(self.backup_dir, backup_name)
            
            # نسخ قاعدة البيانات
            shutil.copy2(db_path, backup_path)
            
            # ضغط النسخة الاحتياطية
            compressed_path = f"{backup_path}.gz"
            with open(backup_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # حذف النسخة غير المضغوطة
            os.remove(backup_path)
            
            self.logger.info(f"تم إنشاء نسخة احتياطية: {compressed_path}")
            return compressed_path
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            raise
    
    def restore_backup(self, backup_path: str, target_path: str):
        """استعادة نسخة احتياطية"""
        try:
            # إلغاء ضغط النسخة الاحتياطية
            temp_path = backup_path.replace('.gz', '')
            
            with gzip.open(backup_path, 'rb') as f_in:
                with open(temp_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # نسخ إلى المسار المطلوب
            shutil.copy2(temp_path, target_path)
            
            # حذف الملف المؤقت
            os.remove(temp_path)
            
            self.logger.info(f"تم استعادة النسخة الاحتياطية: {target_path}")
            
        except Exception as e:
            self.logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            raise
    
    def list_backups(self) -> List[Dict]:
        """قائمة النسخ الاحتياطية"""
        try:
            backups = []
            
            for filename in os.listdir(self.backup_dir):
                if filename.endswith('.db.gz'):
                    file_path = os.path.join(self.backup_dir, filename)
                    stat = os.stat(file_path)
                    
                    backups.append({
                        'name': filename,
                        'path': file_path,
                        'size': stat.st_size,
                        'created': datetime.fromtimestamp(stat.st_ctime),
                        'modified': datetime.fromtimestamp(stat.st_mtime)
                    })
            
            # ترتيب حسب تاريخ الإنشاء
            backups.sort(key=lambda x: x['created'], reverse=True)
            
            return backups
            
        except Exception as e:
            self.logger.error(f"خطأ في قائمة النسخ الاحتياطية: {e}")
            return []
    
    def cleanup_old_backups(self, keep_count: int = 10):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.list_backups()
            
            if len(backups) > keep_count:
                for backup in backups[keep_count:]:
                    os.remove(backup['path'])
                    self.logger.info(f"تم حذف النسخة الاحتياطية القديمة: {backup['name']}")
                    
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف النسخ الاحتياطية: {e}")

class SecurityManager:
    """مدير الأمان"""
    
    def __init__(self):
        self.logger = logging.getLogger("SecurityManager")
        self.failed_attempts = {}
        self.blocked_ips = set()
        self.max_attempts = 5
        self.block_duration = 300  # 5 دقائق
    
    def hash_password(self, password: str) -> str:
        """تشفير كلمة المرور"""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    def verify_password(self, password: str, hashed: str) -> bool:
        """التحقق من كلمة المرور"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من كلمة المرور: {e}")
            return False
    
    def generate_token(self, user_id: str, expiry_hours: int = 24) -> str:
        """توليد رمز مميز"""
        payload = {
            'user_id': user_id,
            'issued_at': datetime.now().isoformat(),
            'expires_at': (datetime.now() + timedelta(hours=expiry_hours)).isoformat(),
            'token_id': str(uuid.uuid4())
        }
        
        # تشفير الرمز المميز
        token_data = json.dumps(payload)
        token_hash = hashlib.sha256(token_data.encode()).hexdigest()
        
        return f"{token_hash}.{payload['token_id']}"
    
    def validate_token(self, token: str) -> Optional[Dict]:
        """التحقق من صحة الرمز المميز"""
        try:
            # هنا يجب التحقق من الرمز في قاعدة البيانات
            # مؤقتاً سنعيد معلومات وهمية
            return {
                'user_id': 'demo_user',
                'valid': True,
                'expires_at': datetime.now() + timedelta(hours=1)
            }
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من الرمز المميز: {e}")
            return None
    
    def log_failed_attempt(self, ip_address: str):
        """تسجيل محاولة فاشلة"""
        current_time = datetime.now()
        
        if ip_address not in self.failed_attempts:
            self.failed_attempts[ip_address] = []
        
        self.failed_attempts[ip_address].append(current_time)
        
        # إزالة المحاولات القديمة
        cutoff_time = current_time - timedelta(minutes=15)
        self.failed_attempts[ip_address] = [
            attempt for attempt in self.failed_attempts[ip_address]
            if attempt > cutoff_time
        ]
        
        # فحص إذا تجاوز الحد الأقصى
        if len(self.failed_attempts[ip_address]) >= self.max_attempts:
            self.blocked_ips.add(ip_address)
            self.logger.warning(f"تم حظر العنوان {ip_address} بسبب المحاولات الفاشلة")
    
    def is_ip_blocked(self, ip_address: str) -> bool:
        """فحص إذا كان العنوان محظور"""
        return ip_address in self.blocked_ips
    
    def unblock_ip(self, ip_address: str):
        """إلغاء حظر العنوان"""
        self.blocked_ips.discard(ip_address)
        self.failed_attempts.pop(ip_address, None)
        self.logger.info(f"تم إلغاء حظر العنوان {ip_address}")

class QueryBuilder:
    """بناء الاستعلامات"""
    
    def __init__(self, table_name: str):
        self.table_name = table_name
        self.query_parts = {
            'select': [],
            'where': [],
            'join': [],
            'order': [],
            'group': [],
            'having': [],
            'limit': None,
            'offset': None
        }
        self.parameters = []
    
    def select(self, *columns):
        """تحديد الأعمدة للاختيار"""
        if columns:
            self.query_parts['select'].extend(columns)
        else:
            self.query_parts['select'] = ['*']
        return self
    
    def where(self, condition: str, *params):
        """إضافة شرط WHERE"""
        self.query_parts['where'].append(condition)
        self.parameters.extend(params)
        return self
    
    def join(self, table: str, condition: str, join_type: str = "INNER"):
        """إضافة JOIN"""
        self.query_parts['join'].append(f"{join_type} JOIN {table} ON {condition}")
        return self
    
    def order_by(self, column: str, direction: str = "ASC"):
        """ترتيب النتائج"""
        self.query_parts['order'].append(f"{column} {direction}")
        return self
    
    def group_by(self, *columns):
        """تجميع النتائج"""
        self.query_parts['group'].extend(columns)
        return self
    
    def having(self, condition: str, *params):
        """شرط HAVING"""
        self.query_parts['having'].append(condition)
        self.parameters.extend(params)
        return self
    
    def limit(self, count: int, offset: int = None):
        """تحديد عدد النتائج"""
        self.query_parts['limit'] = count
        if offset is not None:
            self.query_parts['offset'] = offset
        return self
    
    def build(self) -> Tuple[str, List]:
        """بناء الاستعلام النهائي"""
        # SELECT
        select_clause = "SELECT " + (", ".join(self.query_parts['select']) or "*")
        
        # FROM
        from_clause = f"FROM {self.table_name}"
        
        # JOIN
        join_clause = " ".join(self.query_parts['join'])
        
        # WHERE
        where_clause = ""
        if self.query_parts['where']:
            where_clause = "WHERE " + " AND ".join(self.query_parts['where'])
        
        # GROUP BY
        group_clause = ""
        if self.query_parts['group']:
            group_clause = "GROUP BY " + ", ".join(self.query_parts['group'])
        
        # HAVING
        having_clause = ""
        if self.query_parts['having']:
            having_clause = "HAVING " + " AND ".join(self.query_parts['having'])
        
        # ORDER BY
        order_clause = ""
        if self.query_parts['order']:
            order_clause = "ORDER BY " + ", ".join(self.query_parts['order'])
        
        # LIMIT
        limit_clause = ""
        if self.query_parts['limit'] is not None:
            limit_clause = f"LIMIT {self.query_parts['limit']}"
            if self.query_parts['offset'] is not None:
                limit_clause += f" OFFSET {self.query_parts['offset']}"
        
        # تجميع الاستعلام
        query_parts = [select_clause, from_clause, join_clause, where_clause, 
                      group_clause, having_clause, order_clause, limit_clause]
        
        query = " ".join(part for part in query_parts if part.strip())
        
        return query, self.parameters

class EnhancedDatabaseManager:
    """مدير قاعدة البيانات المحسن والمتطور"""

    def __init__(self, config: Dict = None):
        self.config = config or self._get_default_config()
        self.db_type = DatabaseType(self.config.get('type', 'sqlite'))
        self.logger = logging.getLogger("EnhancedDatabaseManager")

        # تهيئة المكونات
        self.connection_pool = ConnectionPoolManager(
            self.db_type,
            self.config.get('connection', {}),
            self.config.get('pool_size', 10)
        )

        self.cache_manager = CacheManager(
            self.config.get('cache_type', 'memory'),
            self.config.get('redis_config')
        )

        self.backup_manager = BackupManager(
            self.config.get('backup_dir', 'backups')
        )

        self.security_manager = SecurityManager()

        # إحصائيات الأداء
        self.stats = {
            'queries_executed': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_execution_time': 0.0,
            'errors': 0
        }

        # تهيئة قاعدة البيانات
        self._initialize_database()

    def _get_default_config(self) -> Dict:
        """الحصول على الإعدادات الافتراضية"""
        return {
            'type': 'sqlite',
            'connection': {
                'database': 'accounting_system.db'
            },
            'pool_size': 10,
            'cache_type': 'memory',
            'backup_dir': 'backups',
            'auto_backup': True,
            'backup_interval_hours': 24
        }

    def _initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            self.logger.info("بدء تهيئة قاعدة البيانات المحسنة...")

            # إنشاء الجداول الأساسية
            self._create_core_tables()

            # إنشاء الفهارس
            self._create_indexes()

            # إنشاء البيانات الافتراضية
            self._create_default_data()

            # بدء النسخ الاحتياطي التلقائي
            if self.config.get('auto_backup'):
                self._start_auto_backup()

            self.logger.info("تم تهيئة قاعدة البيانات المحسنة بنجاح")

        except Exception as e:
            self.logger.error(f"خطأ في تهيئة قاعدة البيانات: {e}")
            raise

    def _create_core_tables(self):
        """إنشاء الجداول الأساسية"""
        tables_sql = {
            # جدول المستخدمين المحسن
            'users': """
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100) NOT NULL,
                    role VARCHAR(20) DEFAULT 'user',
                    status VARCHAR(20) DEFAULT 'active',
                    last_login DATETIME,
                    login_attempts INTEGER DEFAULT 0,
                    locked_until DATETIME,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_by INTEGER,
                    profile_image TEXT,
                    phone VARCHAR(20),
                    address TEXT,
                    preferences TEXT,
                    two_factor_enabled BOOLEAN DEFAULT FALSE,
                    two_factor_secret VARCHAR(32)
                )
            """,

            # جدول الجلسات
            'user_sessions': """
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    session_token VARCHAR(255) UNIQUE NOT NULL,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    expires_at DATETIME NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            """,

            # جدول العملاء المحسن
            'customers': """
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_code VARCHAR(20) UNIQUE NOT NULL,
                    customer_name VARCHAR(100) NOT NULL,
                    customer_type VARCHAR(20) DEFAULT 'individual',
                    company_name VARCHAR(100),
                    tax_number VARCHAR(50),
                    phone VARCHAR(20),
                    mobile VARCHAR(20),
                    email VARCHAR(100),
                    website VARCHAR(100),
                    address TEXT,
                    city VARCHAR(50),
                    country VARCHAR(50),
                    postal_code VARCHAR(20),
                    credit_limit DECIMAL(15,2) DEFAULT 0,
                    current_balance DECIMAL(15,2) DEFAULT 0,
                    payment_terms INTEGER DEFAULT 30,
                    discount_percentage DECIMAL(5,2) DEFAULT 0,
                    status VARCHAR(20) DEFAULT 'active',
                    notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_by INTEGER,
                    tags TEXT,
                    birth_date DATE,
                    gender VARCHAR(10),
                    preferred_language VARCHAR(10) DEFAULT 'ar',
                    FOREIGN KEY (created_by) REFERENCES users(id)
                )
            """,

            # جدول الموردين المحسن
            'suppliers': """
                CREATE TABLE IF NOT EXISTS suppliers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    supplier_code VARCHAR(20) UNIQUE NOT NULL,
                    supplier_name VARCHAR(100) NOT NULL,
                    company_name VARCHAR(100),
                    tax_number VARCHAR(50),
                    phone VARCHAR(20),
                    mobile VARCHAR(20),
                    email VARCHAR(100),
                    website VARCHAR(100),
                    address TEXT,
                    city VARCHAR(50),
                    country VARCHAR(50),
                    postal_code VARCHAR(20),
                    credit_limit DECIMAL(15,2) DEFAULT 0,
                    current_balance DECIMAL(15,2) DEFAULT 0,
                    payment_terms INTEGER DEFAULT 30,
                    status VARCHAR(20) DEFAULT 'active',
                    notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_by INTEGER,
                    bank_account VARCHAR(50),
                    bank_name VARCHAR(100),
                    contact_person VARCHAR(100),
                    FOREIGN KEY (created_by) REFERENCES users(id)
                )
            """,

            # جدول المنتجات المحسن
            'products': """
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_code VARCHAR(50) UNIQUE NOT NULL,
                    barcode VARCHAR(50) UNIQUE,
                    product_name VARCHAR(100) NOT NULL,
                    description TEXT,
                    category_id INTEGER,
                    brand VARCHAR(50),
                    model VARCHAR(50),
                    unit VARCHAR(20) DEFAULT 'قطعة',
                    cost_price DECIMAL(15,2) DEFAULT 0,
                    selling_price DECIMAL(15,2) DEFAULT 0,
                    wholesale_price DECIMAL(15,2) DEFAULT 0,
                    min_stock_level INTEGER DEFAULT 0,
                    max_stock_level INTEGER DEFAULT 0,
                    current_stock INTEGER DEFAULT 0,
                    reserved_stock INTEGER DEFAULT 0,
                    reorder_point INTEGER DEFAULT 0,
                    reorder_quantity INTEGER DEFAULT 0,
                    weight DECIMAL(10,3),
                    dimensions VARCHAR(50),
                    color VARCHAR(30),
                    size VARCHAR(30),
                    status VARCHAR(20) DEFAULT 'active',
                    is_service BOOLEAN DEFAULT FALSE,
                    tax_rate DECIMAL(5,2) DEFAULT 0,
                    discount_allowed BOOLEAN DEFAULT TRUE,
                    track_inventory BOOLEAN DEFAULT TRUE,
                    expiry_date DATE,
                    manufacture_date DATE,
                    warranty_period INTEGER,
                    notes TEXT,
                    image_url TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_by INTEGER,
                    supplier_id INTEGER,
                    location VARCHAR(50),
                    FOREIGN KEY (created_by) REFERENCES users(id),
                    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
                    FOREIGN KEY (category_id) REFERENCES product_categories(id)
                )
            """,

            # جدول فئات المنتجات
            'product_categories': """
                CREATE TABLE IF NOT EXISTS product_categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category_name VARCHAR(100) NOT NULL,
                    parent_id INTEGER,
                    description TEXT,
                    status VARCHAR(20) DEFAULT 'active',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (parent_id) REFERENCES product_categories(id)
                )
            """,

            # جدول فواتير المبيعات المحسن
            'sales_invoices': """
                CREATE TABLE IF NOT EXISTS sales_invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_number VARCHAR(50) UNIQUE NOT NULL,
                    customer_id INTEGER NOT NULL,
                    invoice_date DATE NOT NULL,
                    due_date DATE,
                    payment_terms INTEGER DEFAULT 30,
                    subtotal DECIMAL(15,2) DEFAULT 0,
                    discount_amount DECIMAL(15,2) DEFAULT 0,
                    discount_percentage DECIMAL(5,2) DEFAULT 0,
                    tax_amount DECIMAL(15,2) DEFAULT 0,
                    tax_rate DECIMAL(5,2) DEFAULT 0,
                    total_amount DECIMAL(15,2) DEFAULT 0,
                    paid_amount DECIMAL(15,2) DEFAULT 0,
                    remaining_amount DECIMAL(15,2) DEFAULT 0,
                    status VARCHAR(20) DEFAULT 'draft',
                    payment_status VARCHAR(20) DEFAULT 'unpaid',
                    currency VARCHAR(10) DEFAULT 'YER',
                    exchange_rate DECIMAL(10,4) DEFAULT 1,
                    notes TEXT,
                    terms_conditions TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    created_by INTEGER,
                    approved_by INTEGER,
                    approved_at DATETIME,
                    printed_count INTEGER DEFAULT 0,
                    last_printed_at DATETIME,
                    reference_number VARCHAR(50),
                    delivery_date DATE,
                    delivery_address TEXT,
                    FOREIGN KEY (customer_id) REFERENCES customers(id),
                    FOREIGN KEY (created_by) REFERENCES users(id),
                    FOREIGN KEY (approved_by) REFERENCES users(id)
                )
            """,

            # جدول تفاصيل فواتير المبيعات
            'sales_invoice_items': """
                CREATE TABLE IF NOT EXISTS sales_invoice_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity DECIMAL(10,3) NOT NULL,
                    unit_price DECIMAL(15,2) NOT NULL,
                    discount_percentage DECIMAL(5,2) DEFAULT 0,
                    discount_amount DECIMAL(15,2) DEFAULT 0,
                    tax_rate DECIMAL(5,2) DEFAULT 0,
                    tax_amount DECIMAL(15,2) DEFAULT 0,
                    total_amount DECIMAL(15,2) NOT NULL,
                    notes TEXT,
                    line_number INTEGER,
                    FOREIGN KEY (invoice_id) REFERENCES sales_invoices(id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products(id)
                )
            """,

            # جدول سجل النشاطات
            'activity_log': """
                CREATE TABLE IF NOT EXISTS activity_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action VARCHAR(50) NOT NULL,
                    table_name VARCHAR(50),
                    record_id INTEGER,
                    old_values TEXT,
                    new_values TEXT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            """,

            # جدول الإعدادات
            'system_settings': """
                CREATE TABLE IF NOT EXISTS system_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key VARCHAR(100) UNIQUE NOT NULL,
                    setting_value TEXT,
                    setting_type VARCHAR(20) DEFAULT 'string',
                    description TEXT,
                    category VARCHAR(50),
                    is_public BOOLEAN DEFAULT FALSE,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_by INTEGER,
                    FOREIGN KEY (updated_by) REFERENCES users(id)
                )
            """
        }

        # تنفيذ إنشاء الجداول
        for table_name, sql in tables_sql.items():
            try:
                self.execute_query(sql)
                self.logger.info(f"تم إنشاء الجدول: {table_name}")
            except Exception as e:
                self.logger.error(f"خطأ في إنشاء الجدول {table_name}: {e}")
                raise

    def _create_indexes(self):
        """إنشاء الفهارس لتحسين الأداء"""
        indexes_sql = [
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)",
            "CREATE INDEX IF NOT EXISTS idx_customers_code ON customers(customer_code)",
            "CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name)",
            "CREATE INDEX IF NOT EXISTS idx_customers_status ON customers(status)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_code ON suppliers(supplier_code)",
            "CREATE INDEX IF NOT EXISTS idx_products_code ON products(product_code)",
            "CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)",
            "CREATE INDEX IF NOT EXISTS idx_products_name ON products(product_name)",
            "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)",
            "CREATE INDEX IF NOT EXISTS idx_sales_invoices_number ON sales_invoices(invoice_number)",
            "CREATE INDEX IF NOT EXISTS idx_sales_invoices_customer ON sales_invoices(customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_sales_invoices_date ON sales_invoices(invoice_date)",
            "CREATE INDEX IF NOT EXISTS idx_sales_invoices_status ON sales_invoices(status)",
            "CREATE INDEX IF NOT EXISTS idx_sales_invoice_items_invoice ON sales_invoice_items(invoice_id)",
            "CREATE INDEX IF NOT EXISTS idx_sales_invoice_items_product ON sales_invoice_items(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_activity_log_user ON activity_log(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_activity_log_action ON activity_log(action)",
            "CREATE INDEX IF NOT EXISTS idx_activity_log_date ON activity_log(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)",
            "CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON user_sessions(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(setting_key)"
        ]

        for index_sql in indexes_sql:
            try:
                self.execute_query(index_sql)
            except Exception as e:
                self.logger.warning(f"تحذير في إنشاء الفهرس: {e}")

    def _create_default_data(self):
        """إنشاء البيانات الافتراضية"""
        try:
            # فحص وجود المستخدم الافتراضي
            admin_exists = self.fetch_one(
                "SELECT id FROM users WHERE username = ?",
                ('admin',)
            )

            if not admin_exists:
                # إنشاء المستخدم الافتراضي
                admin_password = self.security_manager.hash_password('admin123')

                self.execute_query("""
                    INSERT INTO users (username, email, password_hash, full_name, role, status)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    'admin',
                    '<EMAIL>',
                    admin_password,
                    'مدير النظام',
                    'admin',
                    'active'
                ))

                self.logger.info("تم إنشاء المستخدم الافتراضي: admin")

            # إنشاء الإعدادات الافتراضية
            default_settings = [
                ('company_name', 'شركة المحاسبة', 'string', 'اسم الشركة', 'company', True),
                ('company_address', '', 'text', 'عنوان الشركة', 'company', True),
                ('company_phone', '', 'string', 'هاتف الشركة', 'company', True),
                ('company_email', '', 'string', 'بريد الشركة', 'company', True),
                ('tax_rate', '0', 'decimal', 'معدل الضريبة الافتراضي', 'tax', True),
                ('currency', 'YER', 'string', 'العملة الافتراضية', 'general', True),
                ('date_format', 'Y-m-d', 'string', 'تنسيق التاريخ', 'general', False),
                ('decimal_places', '2', 'integer', 'عدد الخانات العشرية', 'general', False),
                ('backup_enabled', 'true', 'boolean', 'تفعيل النسخ الاحتياطي', 'system', False),
                ('backup_interval', '24', 'integer', 'فترة النسخ الاحتياطي بالساعات', 'system', False)
            ]

            for setting in default_settings:
                existing = self.fetch_one(
                    "SELECT id FROM system_settings WHERE setting_key = ?",
                    (setting[0],)
                )

                if not existing:
                    self.execute_query("""
                        INSERT INTO system_settings
                        (setting_key, setting_value, setting_type, description, category, is_public)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, setting)

            self.logger.info("تم إنشاء البيانات الافتراضية")

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء البيانات الافتراضية: {e}")
            raise

    def execute_query(self, query: str, params: Tuple = None, use_cache: bool = False, cache_ttl: int = 3600) -> QueryResult:
        """تنفيذ استعلام قاعدة البيانات"""
        start_time = time.time()
        result = QueryResult(success=False, query=query)

        try:
            # فحص التخزين المؤقت
            cache_key = None
            if use_cache and params:
                cache_key = f"query:{hashlib.md5(f'{query}{params}'.encode()).hexdigest()}"
                cached_result = self.cache_manager.get(cache_key)
                if cached_result:
                    self.stats['cache_hits'] += 1
                    result.success = True
                    result.data = cached_result
                    result.execution_time = time.time() - start_time
                    return result
                else:
                    self.stats['cache_misses'] += 1

            # تنفيذ الاستعلام
            with self.connection_pool.get_connection() as connection:
                cursor = connection.cursor()

                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)

                # تحديد نوع الاستعلام
                query_type = query.strip().upper().split()[0]

                if query_type == 'SELECT':
                    if self.db_type == DatabaseType.SQLITE:
                        result.data = [dict(row) for row in cursor.fetchall()]
                    else:
                        result.data = cursor.fetchall()

                    # حفظ في التخزين المؤقت
                    if use_cache and cache_key:
                        self.cache_manager.set(cache_key, result.data, cache_ttl)

                else:
                    result.affected_rows = cursor.rowcount
                    connection.commit()

                result.success = True

        except Exception as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            result.error = str(e)
            self.stats['errors'] += 1

        finally:
            result.execution_time = time.time() - start_time
            self.stats['queries_executed'] += 1
            self.stats['total_execution_time'] += result.execution_time

            # تسجيل الاستعلامات البطيئة
            if result.execution_time > 1.0:
                self.logger.warning(f"استعلام بطيء ({result.execution_time:.2f}s): {query[:100]}...")

        return result

    def fetch_all(self, query: str, params: Tuple = None, use_cache: bool = False) -> List[Dict]:
        """جلب جميع النتائج"""
        result = self.execute_query(query, params, use_cache)
        return result.data if result.success else []

    def fetch_one(self, query: str, params: Tuple = None, use_cache: bool = False) -> Optional[Dict]:
        """جلب نتيجة واحدة"""
        result = self.execute_query(query, params, use_cache)
        if result.success and result.data:
            return result.data[0]
        return None

    def insert(self, table: str, data: Dict, return_id: bool = True) -> Optional[int]:
        """إدراج بيانات جديدة"""
        try:
            columns = list(data.keys())
            placeholders = ['?' for _ in columns]
            values = list(data.values())

            query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
            result = self.execute_query(query, tuple(values))

            if result.success and return_id:
                # الحصول على ID المُدرج
                id_result = self.fetch_one("SELECT last_insert_rowid() as id")
                return id_result['id'] if id_result else None

            return result.affected_rows if result.success else None

        except Exception as e:
            self.logger.error(f"خطأ في الإدراج: {e}")
            return None

    def update(self, table: str, data: Dict, where_clause: str, where_params: Tuple = None) -> int:
        """تحديث البيانات"""
        try:
            set_clauses = [f"{column} = ?" for column in data.keys()]
            values = list(data.values())

            if where_params:
                values.extend(where_params)

            query = f"UPDATE {table} SET {', '.join(set_clauses)} WHERE {where_clause}"
            result = self.execute_query(query, tuple(values))

            return result.affected_rows if result.success else 0

        except Exception as e:
            self.logger.error(f"خطأ في التحديث: {e}")
            return 0

    def delete(self, table: str, where_clause: str, where_params: Tuple = None) -> int:
        """حذف البيانات"""
        try:
            query = f"DELETE FROM {table} WHERE {where_clause}"
            result = self.execute_query(query, where_params)

            return result.affected_rows if result.success else 0

        except Exception as e:
            self.logger.error(f"خطأ في الحذف: {e}")
            return 0

    def query_builder(self, table: str) -> QueryBuilder:
        """إنشاء بناء استعلام"""
        return QueryBuilder(table)

    def begin_transaction(self):
        """بدء معاملة"""
        return self.execute_query("BEGIN TRANSACTION")

    def commit_transaction(self):
        """تأكيد المعاملة"""
        return self.execute_query("COMMIT")

    def rollback_transaction(self):
        """التراجع عن المعاملة"""
        return self.execute_query("ROLLBACK")

    @contextmanager
    def transaction(self):
        """سياق المعاملة"""
        try:
            self.begin_transaction()
            yield
            self.commit_transaction()
        except Exception as e:
            self.rollback_transaction()
            self.logger.error(f"خطأ في المعاملة، تم التراجع: {e}")
            raise

    def create_backup(self, backup_name: str = None) -> str:
        """إنشاء نسخة احتياطية"""
        try:
            if self.db_type == DatabaseType.SQLITE:
                db_path = self.config['connection']['database']
                return self.backup_manager.create_backup(db_path, backup_name)
            else:
                self.logger.warning("النسخ الاحتياطي متاح فقط لـ SQLite حالياً")
                return None

        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            raise

    def restore_backup(self, backup_path: str):
        """استعادة نسخة احتياطية"""
        try:
            if self.db_type == DatabaseType.SQLITE:
                db_path = self.config['connection']['database']
                self.backup_manager.restore_backup(backup_path, db_path)
                # إعادة تهيئة الاتصالات
                self.connection_pool.close_all()
                self.connection_pool._initialize_pool()
            else:
                self.logger.warning("استعادة النسخ الاحتياطية متاحة فقط لـ SQLite حالياً")

        except Exception as e:
            self.logger.error(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            raise

    def get_backup_list(self) -> List[Dict]:
        """الحصول على قائمة النسخ الاحتياطية"""
        return self.backup_manager.list_backups()

    def cleanup_old_backups(self, keep_count: int = 10):
        """تنظيف النسخ الاحتياطية القديمة"""
        self.backup_manager.cleanup_old_backups(keep_count)

    def _start_auto_backup(self):
        """بدء النسخ الاحتياطي التلقائي"""
        def backup_worker():
            while True:
                try:
                    interval_hours = self.config.get('backup_interval_hours', 24)
                    time.sleep(interval_hours * 3600)  # تحويل إلى ثواني

                    if self.config.get('auto_backup', True):
                        backup_name = f"auto_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                        self.create_backup(backup_name)
                        self.cleanup_old_backups()
                        self.logger.info("تم إنشاء نسخة احتياطية تلقائية")

                except Exception as e:
                    self.logger.error(f"خطأ في النسخ الاحتياطي التلقائي: {e}")

        backup_thread = threading.Thread(target=backup_worker, daemon=True)
        backup_thread.start()
        self.logger.info("تم بدء النسخ الاحتياطي التلقائي")

    def log_activity(self, user_id: int, action: str, table_name: str = None,
                    record_id: int = None, old_values: Dict = None,
                    new_values: Dict = None, ip_address: str = None,
                    user_agent: str = None):
        """تسجيل النشاط"""
        try:
            activity_data = {
                'user_id': user_id,
                'action': action,
                'table_name': table_name,
                'record_id': record_id,
                'old_values': json.dumps(old_values, default=str) if old_values else None,
                'new_values': json.dumps(new_values, default=str) if new_values else None,
                'ip_address': ip_address,
                'user_agent': user_agent
            }

            self.insert('activity_log', activity_data, return_id=False)

        except Exception as e:
            self.logger.error(f"خطأ في تسجيل النشاط: {e}")

    def get_statistics(self) -> Dict:
        """الحصول على إحصائيات الأداء"""
        stats = self.stats.copy()

        # إضافة إحصائيات إضافية
        if stats['queries_executed'] > 0:
            stats['average_execution_time'] = stats['total_execution_time'] / stats['queries_executed']
            stats['cache_hit_rate'] = stats['cache_hits'] / (stats['cache_hits'] + stats['cache_misses']) * 100
        else:
            stats['average_execution_time'] = 0
            stats['cache_hit_rate'] = 0

        # إحصائيات قاعدة البيانات
        try:
            table_stats = self.fetch_all("""
                SELECT name as table_name,
                       (SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=m.name) as exists
                FROM sqlite_master m WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            stats['tables'] = table_stats

            # حجم قاعدة البيانات
            if self.db_type == DatabaseType.SQLITE:
                db_path = self.config['connection']['database']
                if os.path.exists(db_path):
                    stats['database_size'] = os.path.getsize(db_path)
                else:
                    stats['database_size'] = 0

        except Exception as e:
            self.logger.error(f"خطأ في جمع إحصائيات قاعدة البيانات: {e}")

        return stats

    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            if self.db_type == DatabaseType.SQLITE:
                # تحليل الجداول
                self.execute_query("ANALYZE")

                # ضغط قاعدة البيانات
                self.execute_query("VACUUM")

                # إعادة بناء الفهارس
                self.execute_query("REINDEX")

                self.logger.info("تم تحسين قاعدة البيانات بنجاح")
            else:
                self.logger.warning("تحسين قاعدة البيانات متاح فقط لـ SQLite حالياً")

        except Exception as e:
            self.logger.error(f"خطأ في تحسين قاعدة البيانات: {e}")
            raise

    def check_database_integrity(self) -> bool:
        """فحص سلامة قاعدة البيانات"""
        try:
            if self.db_type == DatabaseType.SQLITE:
                result = self.fetch_one("PRAGMA integrity_check")
                if result and result.get('integrity_check') == 'ok':
                    self.logger.info("قاعدة البيانات سليمة")
                    return True
                else:
                    self.logger.error("قاعدة البيانات تحتوي على أخطاء")
                    return False
            else:
                self.logger.warning("فحص سلامة قاعدة البيانات متاح فقط لـ SQLite حالياً")
                return True

        except Exception as e:
            self.logger.error(f"خطأ في فحص سلامة قاعدة البيانات: {e}")
            return False

    def get_table_info(self, table_name: str) -> List[Dict]:
        """الحصول على معلومات الجدول"""
        try:
            if self.db_type == DatabaseType.SQLITE:
                return self.fetch_all(f"PRAGMA table_info({table_name})")
            else:
                return []

        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات الجدول: {e}")
            return []

    def close(self):
        """إغلاق جميع الاتصالات"""
        try:
            self.connection_pool.close_all()
            self.logger.info("تم إغلاق جميع اتصالات قاعدة البيانات")

        except Exception as e:
            self.logger.error(f"خطأ في إغلاق الاتصالات: {e}")

    def __enter__(self):
        """دخول السياق"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """خروج السياق"""
        self.close()


# مثيل عام لمدير قاعدة البيانات
db_manager = None

def get_db_manager(config: Dict = None) -> EnhancedDatabaseManager:
    """الحصول على مثيل مدير قاعدة البيانات"""
    global db_manager

    if db_manager is None:
        db_manager = EnhancedDatabaseManager(config)

    return db_manager

def initialize_database(config: Dict = None):
    """تهيئة قاعدة البيانات"""
    global db_manager
    db_manager = EnhancedDatabaseManager(config)
    return db_manager
