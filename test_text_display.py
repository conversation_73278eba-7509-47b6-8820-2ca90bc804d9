#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عرض النص العربي
Arabic Text Display Test
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, Q<PERSON><PERSON>t, QVBoxLayout, 
                             QLabel, QPushButton, QTextEdit, QHBoxLayout)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class TextDisplayTest(QMainWindow):
    """نافذة اختبار عرض النص"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار عرض النص العربي")
        self.setGeometry(100, 100, 800, 600)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # اختبار 1: نص عادي
        label1 = QLabel("🔍 اختبار النص العربي العادي")
        label1.setFont(QFont("Arial", 14))
        layout.addWidget(label1)
        
        # اختبار 2: نص مع خلفية ملونة
        label2 = QLabel("🎨 نص مع خلفية ملونة")
        label2.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: white;
                background-color: #3498db;
                padding: 10px;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        layout.addWidget(label2)
        
        # اختبار 3: نص مع خلفية مختلفة
        label3 = QLabel("✅ نص مع خلفية خضراء")
        label3.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: white;
                background-color: #27ae60;
                padding: 10px;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        layout.addWidget(label3)
        
        # اختبار 4: نص أسود على خلفية فاتحة
        label4 = QLabel("📝 نص أسود على خلفية فاتحة")
        label4.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: black;
                background-color: #ecf0f1;
                padding: 10px;
                border-radius: 5px;
                margin: 5px;
                border: 2px solid #bdc3c7;
            }
        """)
        layout.addWidget(label4)
        
        # اختبار 5: أزرار
        buttons_layout = QHBoxLayout()
        
        btn1 = QPushButton("🔄 زر عادي")
        btn1.clicked.connect(lambda: self.show_message("تم النقر على الزر العادي"))
        buttons_layout.addWidget(btn1)
        
        btn2 = QPushButton("🎯 زر ملون")
        btn2.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        btn2.clicked.connect(lambda: self.show_message("تم النقر على الزر الملون"))
        buttons_layout.addWidget(btn2)
        
        layout.addLayout(buttons_layout)
        
        # اختبار 6: منطقة نص
        text_label = QLabel("📄 منطقة النص:")
        layout.addWidget(text_label)
        
        self.text_edit = QTextEdit()
        self.text_edit.setPlainText("""
هذا نص تجريبي للتأكد من عرض النص العربي بشكل صحيح.

الميزات المختبرة:
✅ النص العربي
✅ الأيقونات التعبيرية
✅ الألوان والخلفيات
✅ الخطوط والأحجام

إذا كنت تقرأ هذا النص بوضوح، فإن عرض النص يعمل بشكل صحيح.
        """)
        self.text_edit.setMaximumHeight(200)
        layout.addWidget(self.text_edit)
        
        # اختبار 7: معلومات النظام
        info_label = QLabel("ℹ️ معلومات النظام:")
        layout.addWidget(info_label)
        
        system_info = f"""
نظام التشغيل: {sys.platform}
إصدار Python: {sys.version.split()[0]}
ترميز النظام: {sys.getdefaultencoding()}
        """
        
        info_text = QTextEdit()
        info_text.setPlainText(system_info.strip())
        info_text.setMaximumHeight(100)
        info_text.setReadOnly(True)
        layout.addWidget(info_text)
        
        # اختبار 8: أزرار الاختبار
        test_buttons_layout = QHBoxLayout()
        
        test_font_btn = QPushButton("🔤 اختبار الخط")
        test_font_btn.clicked.connect(self.test_font)
        test_buttons_layout.addWidget(test_font_btn)
        
        test_color_btn = QPushButton("🎨 اختبار الألوان")
        test_color_btn.clicked.connect(self.test_colors)
        test_buttons_layout.addWidget(test_color_btn)
        
        test_encoding_btn = QPushButton("🔤 اختبار الترميز")
        test_encoding_btn.clicked.connect(self.test_encoding)
        test_buttons_layout.addWidget(test_encoding_btn)
        
        layout.addLayout(test_buttons_layout)
        
        # رسالة الحالة
        self.status_label = QLabel("✅ جاهز للاختبار")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                font-weight: bold;
                padding: 5px;
                border: 1px solid #27ae60;
                border-radius: 3px;
                background-color: #d5f4e6;
            }
        """)
        layout.addWidget(self.status_label)
        
        central_widget.setLayout(layout)
    
    def show_message(self, message):
        """عرض رسالة"""
        self.status_label.setText(f"📢 {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #3498db;
                font-weight: bold;
                padding: 5px;
                border: 1px solid #3498db;
                border-radius: 3px;
                background-color: #ebf3fd;
            }
        """)
    
    def test_font(self):
        """اختبار الخط"""
        fonts = ["Arial", "Tahoma", "Segoe UI", "Times New Roman"]
        current_font = self.text_edit.font().family()
        
        # تغيير الخط
        for font in fonts:
            if font != current_font:
                new_font = QFont(font, 12)
                self.text_edit.setFont(new_font)
                self.show_message(f"تم تغيير الخط إلى: {font}")
                break
    
    def test_colors(self):
        """اختبار الألوان"""
        colors = [
            ("#000000", "#ffffff", "أسود على أبيض"),
            ("#ffffff", "#000000", "أبيض على أسود"),
            ("#2c3e50", "#ecf0f1", "داكن على فاتح"),
            ("#e74c3c", "#ffffff", "أحمر مع أبيض")
        ]
        
        import random
        color, bg_color, description = random.choice(colors)
        
        self.text_edit.setStyleSheet(f"""
            QTextEdit {{
                color: {color};
                background-color: {bg_color};
                font-size: 14px;
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
            }}
        """)
        
        self.show_message(f"تم تطبيق ألوان: {description}")
    
    def test_encoding(self):
        """اختبار الترميز"""
        test_texts = [
            "النص العربي: مرحباً بكم في النظام المحاسبي",
            "English Text: Welcome to the Accounting System",
            "الأرقام: ١٢٣٤٥٦٧٨٩٠ و **********",
            "الرموز: !@#$%^&*()_+-=[]{}|;':\",./<>?"
        ]
        
        import random
        test_text = random.choice(test_texts)
        
        self.text_edit.clear()
        self.text_edit.setPlainText(test_text)
        
        self.show_message("تم اختبار الترميز - تحقق من النص أعلاه")

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # تعيين اتجاه النص للعربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين خط افتراضي يدعم العربية
    font = QFont("Tahoma", 10)
    app.setFont(font)
    
    # إنشاء النافذة
    window = TextDisplayTest()
    window.show()
    
    print("🚀 تم تشغيل اختبار عرض النص")
    print("📝 تحقق من النافذة للتأكد من عرض النص بشكل صحيح")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
