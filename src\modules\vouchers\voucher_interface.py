#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة السندات - نظام Onyx Pro ERP
Voucher Interface - Onyx Pro ERP System
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QLineEdit, QComboBox, QPushButton, QTableWidget,
                             QTableWidgetItem, QGroupBox, QFrame, QDateEdit, QTextEdit,
                             QHeaderView, QMessageBox, QToolBar, QAction, QSpinBox,
                             QTabWidget, QWidget, QFormLayout, QShortcut)
from PyQt5.QtCore import Qt, QDate, QDateTime, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette, QKeySequence

class VoucherInterface(QDialog):
    """واجهة السندات الموحدة"""
    
    voucher_saved = pyqtSignal(dict)
    
    def __init__(self, db_manager, voucher_type="receipt", parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.voucher_type = voucher_type  # "receipt" أو "payment"
        self.current_voucher_id = None
        self.voucher_items = []
        
        self.setup_ui()
        self.setup_database()
        self.load_combo_data()
        self.setup_shortcuts()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تحديد العنوان حسب نوع السند
        title = "سند قبض" if self.voucher_type == "receipt" else "سند صرف"
        self.setWindowTitle(f"{title} - Onyx Pro ERP")
        self.setGeometry(50, 50, 1400, 900)  # شاشة أكبر
        self.showMaximized()  # عرض بملء الشاشة
        
        # تطبيق تنسيق يطابق الصورة
        self.setStyleSheet("""
            QDialog {
                background-color: #f0e6f7;
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 11px;
            }
            QGroupBox {
                font-weight: normal;
                border: 1px solid #8e44ad;
                border-radius: 3px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: #faf8fc;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 5px 0 5px;
                color: #8e44ad;
                font-size: 11px;
                font-weight: bold;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 11px;
                color: #2c3e50;
                background-color: transparent;
                padding: 2px;
                border: none;
            }
            QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 11px;
                color: #2c3e50;
                padding: 3px;
                border: 1px solid #bdc3c7;
                border-radius: 2px;
                background-color: white;
                min-height: 18px;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus, QSpinBox:focus {
                border: 1px solid #3498db;
                background-color: #ebf3fd;
            }
            QLineEdit:read-only {
                background-color: #f5f5f5;
                color: #666;
                border: 1px solid #ddd;
            }
            QTableWidget {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 11px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #bdc3c7;
                gridline-color: #ddd;
            }
            QHeaderView::section {
                background-color: #e8e8e8;
                color: #2c3e50;
                padding: 4px 8px;
                border: 1px solid #bdc3c7;
                font-weight: bold;
                font-size: 11px;
            }
        """)
        
        layout = QVBoxLayout()
        
        # شريط الأدوات العلوي
        self.create_toolbar()
        layout.addWidget(self.toolbar)
        
        # رأس السند
        self.create_header()
        layout.addWidget(self.header_frame)
        
        # بيانات السند الأساسية
        self.create_voucher_info()
        layout.addWidget(self.voucher_info_frame)
        
        # جدول تفاصيل السند
        self.create_voucher_details()
        layout.addWidget(self.details_frame)
        
        # إجماليات السند
        self.create_totals_section()
        layout.addWidget(self.totals_frame)

        # بيانات المستخدم ومُدخل السند
        self.create_user_info_section()
        layout.addWidget(self.user_info_frame)

        # شريط الحالة السفلي
        self.create_status_bar()
        layout.addWidget(self.status_frame)
        
        self.setLayout(layout)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar = QToolBar()
        self.toolbar.setFixedHeight(50)  # ارتفاع أكبر
        self.toolbar.setStyleSheet("""
            QToolBar {
                background-color: #e8e8e8;
                border: 1px solid #bdc3c7;
                spacing: 3px;
                padding: 5px;
            }
            QToolBar QToolButton {
                background-color: #f8f9fa;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px 12px;
                margin: 2px;
                color: #2c3e50;
                font-size: 12px;
                font-weight: bold;
                min-width: 60px;
                min-height: 32px;
            }
            QToolBar QToolButton:hover {
                background-color: #e3f2fd;
                border: 2px solid #2196f3;
                color: #1976d2;
            }
            QToolBar QToolButton:pressed {
                background-color: #bbdefb;
                border: 2px solid #1565c0;
            }
        """)
        
        # أزرار شريط الأدوات مع نصوص واضحة
        actions = [
            ("💾 حفظ", "حفظ السند (Ctrl+S)", self.save_voucher),
            ("📄 جديد", "سند جديد (Ctrl+N)", self.new_voucher),
            ("✏️ تعديل", "تعديل السند (F2)", self.edit_voucher),
            ("🗑️ حذف", "حذف السند (Delete)", self.delete_voucher),
            ("🖨️ طباعة", "طباعة السند (Ctrl+P)", self.print_voucher),
            ("👁️ معاينة", "معاينة الطباعة", self.preview_voucher),
            ("📂 فتح", "فتح سند موجود (Ctrl+O)", self.open_voucher),
            ("🔍 بحث", "البحث في السندات (Ctrl+F)", self.search_voucher),
            ("⏮️ الأول", "السند الأول", self.first_voucher),
            ("⏪ السابق", "السند السابق", self.previous_voucher),
            ("⏩ التالي", "السند التالي", self.next_voucher),
            ("⏭️ الأخير", "السند الأخير", self.last_voucher),
            ("📊 تقرير", "تقارير السندات", self.show_reports),
            ("⚙️ إعدادات", "إعدادات النظام", self.show_settings),
            ("❓ مساعدة", "المساعدة (F1)", self.show_help),
            ("❌ إغلاق", "إغلاق النافذة", self.close)
        ]

        for text, tooltip, slot in actions:
            action = QAction(text, self)
            action.setToolTip(tooltip)
            action.triggered.connect(slot)
            self.toolbar.addAction(action)

            # إضافة فواصل بعد مجموعات معينة
            if text in ["🗑️ حذف", "📂 فتح", "⏭️ الأخير"]:
                self.toolbar.addSeparator()
    
    def create_header(self):
        """إنشاء رأس السند"""
        self.header_frame = QFrame()
        self.header_frame.setStyleSheet("""
            QFrame {
                background-color: #e91e63;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        
        header_layout = QHBoxLayout()
        
        # شعار النظام
        logo_label = QLabel("🏢 Onyx Pro ERP")
        logo_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
            }
        """)
        header_layout.addWidget(logo_label)
        
        header_layout.addStretch()
        
        # عنوان السند
        title = "سند قبض" if self.voucher_type == "receipt" else "سند صرف"
        title_label = QLabel(f"📄 {title}")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                padding: 15px;
            }
        """)
        header_layout.addWidget(title_label)
        
        self.header_frame.setLayout(header_layout)
    
    def create_voucher_info(self):
        """إنشاء قسم بيانات السند مثل الصورة"""
        # إنشاء تخطيط شبكي رئيسي
        main_layout = QGridLayout()
        main_layout.setVerticalSpacing(5)
        main_layout.setHorizontalSpacing(10)

        # الصف الأول - الجانب الأيسر
        main_layout.addWidget(QLabel("رقم السند:"), 0, 0)
        self.voucher_number_edit = QLineEdit()
        self.voucher_number_edit.setMaximumWidth(80)
        main_layout.addWidget(self.voucher_number_edit, 0, 1)

        # فاصل
        main_layout.addWidget(QLabel(""), 0, 2)

        # الجانب الأيمن - نوع السند
        main_layout.addWidget(QLabel("نوع السند:"), 0, 3)
        self.voucher_type_combo = QComboBox()
        self.voucher_type_combo.addItems(["عام", "مبيعات", "مشتريات", "مرتجعات"])
        self.voucher_type_combo.setMaximumWidth(100)
        main_layout.addWidget(self.voucher_type_combo, 0, 4)

        # نوع العملية
        main_layout.addWidget(QLabel("نوع العملية:"), 0, 5)
        self.operation_type_combo = QComboBox()
        self.operation_type_combo.addItems(["نقداً", "بنكي"])
        self.operation_type_combo.setMaximumWidth(80)
        main_layout.addWidget(self.operation_type_combo, 0, 6)

        # العملة
        main_layout.addWidget(QLabel("العملة:"), 0, 7)
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["R.Y", "USD", "SAR", "EUR"])
        self.currency_combo.setMaximumWidth(60)
        main_layout.addWidget(self.currency_combo, 0, 8)

        # عدد المرفقات
        main_layout.addWidget(QLabel("عدد المرفقات:"), 0, 9)
        self.attachments_count_edit = QLineEdit("0")
        self.attachments_count_edit.setMaximumWidth(60)
        main_layout.addWidget(self.attachments_count_edit, 0, 10)

        # الصف الثاني
        # التاريخ
        main_layout.addWidget(QLabel("التاريخ:"), 1, 0)
        self.voucher_date_edit = QDateEdit()
        self.voucher_date_edit.setDate(QDate.currentDate())
        self.voucher_date_edit.setCalendarPopup(True)
        self.voucher_date_edit.setDisplayFormat("dd/MM/yyyy")
        self.voucher_date_edit.setMaximumWidth(100)
        main_layout.addWidget(self.voucher_date_edit, 1, 1)

        # فاصل
        main_layout.addWidget(QLabel(""), 1, 2)

        # مركز رئيسي/فرعي
        main_layout.addWidget(QLabel("مركز رئيسي/فرعي:"), 1, 3)
        self.center_combo = QComboBox()
        self.center_combo.addItems(["المركز الرئيسي", "الفرع الأول", "الفرع الثاني"])
        self.center_combo.setMaximumWidth(120)
        main_layout.addWidget(self.center_combo, 1, 4, 1, 2)

        # رقم الفرع
        main_layout.addWidget(QLabel("رقم الفرع:"), 1, 6)
        self.branch_number_edit = QLineEdit()
        self.branch_number_edit.setMaximumWidth(60)
        main_layout.addWidget(self.branch_number_edit, 1, 7)

        # رقم الصندوق
        main_layout.addWidget(QLabel("رقم الصندوق:"), 1, 8)
        self.cashbox_code_edit = QLineEdit()
        self.cashbox_code_edit.setMaximumWidth(100)
        self.cashbox_code_edit.setPlaceholderText("صندوق المحل الرئيسي")
        main_layout.addWidget(self.cashbox_code_edit, 1, 9)

        # زر البحث
        self.search_cashbox_btn = QPushButton("🔍")
        self.search_cashbox_btn.setMaximumWidth(25)
        self.search_cashbox_btn.setToolTip("البحث عن الصندوق/البنك (F9)")
        self.search_cashbox_btn.clicked.connect(self.search_cashbox)
        main_layout.addWidget(self.search_cashbox_btn, 1, 10)

        # الصف الثالث
        # R.Y المبلغ المحلي
        main_layout.addWidget(QLabel("R.Y المبلغ المحلي:"), 2, 0)
        self.local_amount_edit = QLineEdit("0.00")
        self.local_amount_edit.setReadOnly(True)
        self.local_amount_edit.setStyleSheet("background-color: #f0f0f0; font-weight: bold;")
        self.local_amount_edit.setMaximumWidth(100)
        main_layout.addWidget(self.local_amount_edit, 2, 1)

        # فاصل
        main_layout.addWidget(QLabel(""), 2, 2)

        # اسم المستلم
        main_layout.addWidget(QLabel("اسم المستلم:"), 2, 3)
        self.recipient_name_edit = QLineEdit()
        self.recipient_name_edit.setMaximumWidth(150)
        main_layout.addWidget(self.recipient_name_edit, 2, 4, 1, 2)

        # رقم المرجع
        main_layout.addWidget(QLabel("رقم المرجع:"), 2, 6)
        self.reference_number_edit = QLineEdit()
        self.reference_number_edit.setMaximumWidth(80)
        main_layout.addWidget(self.reference_number_edit, 2, 7)

        # اسم المرجع
        main_layout.addWidget(QLabel("اسم المرجع:"), 2, 8)
        self.reference_name_edit = QLineEdit()
        self.reference_name_edit.setMaximumWidth(120)
        main_layout.addWidget(self.reference_name_edit, 2, 9, 1, 2)

        # الصف الرابع
        # رقم الموظف
        main_layout.addWidget(QLabel("رقم الموظف:"), 3, 6)
        self.employee_number_edit = QLineEdit()
        self.employee_number_edit.setMaximumWidth(80)
        main_layout.addWidget(self.employee_number_edit, 3, 7)

        # الرقم
        main_layout.addWidget(QLabel("الرقم:"), 3, 8)
        self.number_edit = QLineEdit()
        self.number_edit.setMaximumWidth(80)
        main_layout.addWidget(self.number_edit, 3, 9)

        # الصف الخامس - بيان الصندوق/البنك
        main_layout.addWidget(QLabel("بيان الصندوق/البنك:"), 4, 3)
        self.cashbox_description_edit = QLineEdit()
        self.cashbox_description_edit.setPlaceholderText("شرح تفصيلي لسبب السند أو تفاصيل الدفع/التحصيل...")
        main_layout.addWidget(self.cashbox_description_edit, 4, 4, 1, 6)

        # أزرار إضافية
        self.calculator_btn = QPushButton("🧮")
        self.calculator_btn.setMaximumWidth(30)
        self.calculator_btn.setToolTip("فتح الآلة الحاسبة")
        main_layout.addWidget(self.calculator_btn, 4, 10)

        # إنشاء إطار رئيسي
        self.voucher_info_frame = QFrame()
        self.voucher_info_frame.setLayout(main_layout)
        self.voucher_info_frame.setMaximumHeight(160)  # ارتفاع أكبر للصفوف الإضافية

        # إخفاء الحقول الأقل أهمية أو دمجها
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["ريال سعودي", "دولار أمريكي", "يورو"])
        self.currency_combo.setCurrentText("ريال سعودي")
        self.currency_combo.hide()  # إخفاء مؤقت

        self.exchange_rate_edit = QLineEdit("1.00")
        self.exchange_rate_edit.hide()  # إخفاء مؤقت

        self.reference_edit = QLineEdit()
        self.reference_edit.hide()  # إخفاء مؤقت

        self.status_combo = QComboBox()
        self.status_combo.addItems(["مسودة", "معتمد", "مرحل", "ملغي"])
        self.status_combo.hide()  # إخفاء مؤقت

    def create_voucher_details(self):
        """إنشاء جدول تفاصيل السند"""
        self.details_frame = QGroupBox("📊 تفاصيل السند")
        layout = QVBoxLayout()

        # أزرار إدارة الصفوف
        buttons_layout = QHBoxLayout()

        self.add_row_btn = QPushButton("➕ إضافة صف")
        self.add_row_btn.clicked.connect(self.add_detail_row)
        self.add_row_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        buttons_layout.addWidget(self.add_row_btn)

        self.delete_row_btn = QPushButton("➖ حذف صف")
        self.delete_row_btn.clicked.connect(self.delete_detail_row)
        self.delete_row_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        buttons_layout.addWidget(self.delete_row_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        # جدول التفاصيل
        self.details_table = QTableWidget()
        self.details_table.setColumnCount(11)

        headers = [
            "رقم الحساب", "الحساب التحليلي", "الاسم", "البيان",
            "العملة", "سعر التحويل", "نسبة", "المبلغ",
            "المبلغ الأجنبي", "رقم المركز", "رقم المندوب/المرجع"
        ]
        self.details_table.setHorizontalHeaderLabels(headers)

        # تعيين عرض الأعمدة للجدول الجديد
        header = self.details_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)   # رقم الحساب
        header.setSectionResizeMode(1, QHeaderView.Fixed)   # الحساب التحليلي
        header.setSectionResizeMode(2, QHeaderView.Fixed)   # الاسم
        header.setSectionResizeMode(3, QHeaderView.Stretch) # البيان
        header.setSectionResizeMode(4, QHeaderView.Fixed)   # العملة
        header.setSectionResizeMode(5, QHeaderView.Fixed)   # سعر التحويل
        header.setSectionResizeMode(6, QHeaderView.Fixed)   # نسبة
        header.setSectionResizeMode(7, QHeaderView.Fixed)   # المبلغ
        header.setSectionResizeMode(8, QHeaderView.Fixed)   # المبلغ الأجنبي
        header.setSectionResizeMode(9, QHeaderView.Fixed)   # رقم المركز
        header.setSectionResizeMode(10, QHeaderView.Fixed)  # رقم المندوب/المرجع

        # تحديد عرض الأعمدة
        self.details_table.setColumnWidth(0, 120)  # رقم الحساب
        self.details_table.setColumnWidth(1, 120)  # الحساب التحليلي
        self.details_table.setColumnWidth(2, 200)  # الاسم
        self.details_table.setColumnWidth(4, 80)   # العملة
        self.details_table.setColumnWidth(5, 100)  # سعر التحويل
        self.details_table.setColumnWidth(6, 80)   # نسبة
        self.details_table.setColumnWidth(7, 120)  # المبلغ
        self.details_table.setColumnWidth(8, 120)  # المبلغ الأجنبي
        self.details_table.setColumnWidth(9, 100)  # رقم المركز
        self.details_table.setColumnWidth(10, 120) # رقم المندوب/المرجع

        # تحسين تنسيق الجدول
        self.details_table.verticalHeader().setDefaultSectionSize(35)  # ارتفاع الصفوف
        self.details_table.setAlternatingRowColors(True)  # ألوان متناوبة للصفوف

        # تحسين خط الجدول
        table_font = QFont("Tahoma", 11)
        table_font.setBold(False)
        self.details_table.setFont(table_font)

        # ربط الأحداث لضمان ظهور النص (سيتم ربطها لاحقاً)
        # self.details_table.cellChanged.connect(self.calculate_totals)
        # self.details_table.itemChanged.connect(self.ensure_text_visibility)

        # تطبيق تنسيق بسيط وواضح للجدول
        self.details_table.setStyleSheet("""
            QTableWidget {
                font-family: "Tahoma", "Arial", sans-serif;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                font-size: 12px;
                gridline-color: #ddd;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 12px 8px;
                border: 1px solid #34495e;
                font-weight: bold;
                font-size: 13px;
                text-align: center;
            }
        """)

        # إضافة صف افتراضي
        self.add_detail_row()

        layout.addWidget(self.details_table)
        self.details_frame.setLayout(layout)

    def create_totals_section(self):
        """إنشاء قسم الإجماليات - مصغر"""
        self.totals_frame = QGroupBox("💰 إجمالي السند")
        self.totals_frame.setMaximumHeight(80)  # تحديد ارتفاع أقصى
        layout = QHBoxLayout()  # تغيير إلى تخطيط أفقي
        layout.setSpacing(15)  # تقليل المسافات

        # إجمالي السند
        voucher_type_text = "إجمالي المقبوض:" if self.voucher_type == "receipt" else "إجمالي المصروف:"
        layout.addWidget(QLabel(voucher_type_text))
        self.total_debit_label = QLabel("0.00")
        self.total_debit_label.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8;
                border: 2px solid #27ae60;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                text-align: center;
                min-width: 120px;
                max-height: 35px;
            }
        """)
        layout.addWidget(self.total_debit_label)

        # فاصل
        layout.addWidget(QLabel("|"))

        # عدد البنود
        layout.addWidget(QLabel("عدد البنود:"))
        self.items_count_label = QLabel("0")
        self.items_count_label.setStyleSheet("""
            QLabel {
                background-color: #e3f2fd;
                border: 2px solid #2196f3;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
                font-weight: bold;
                color: #1976d2;
                text-align: center;
                min-width: 60px;
                max-height: 35px;
            }
        """)
        layout.addWidget(self.items_count_label)

        # فاصل
        layout.addWidget(QLabel("|"))

        # حالة السند
        layout.addWidget(QLabel("حالة السند:"))
        self.balance_status_label = QLabel("سند صحيح")
        self.balance_status_label.setStyleSheet("""
            QLabel {
                background-color: #d4edda;
                border: 2px solid #28a745;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
                font-weight: bold;
                color: #155724;
                text-align: center;
                min-width: 100px;
                max-height: 35px;
            }
        """)
        layout.addWidget(self.balance_status_label)

        # إضافة مساحة مرنة في النهاية
        layout.addStretch()

        # إخفاء الليبلات غير المستخدمة (للتوافق مع الكود القديم)
        self.total_credit_label = self.total_debit_label  # نفس القيمة
        self.difference_label = QLabel("0.00")  # دائماً صفر

        self.totals_frame.setLayout(layout)

    def create_user_info_section(self):
        """إنشاء قسم بيانات المستخدم ومُدخل السند"""
        self.user_info_frame = QGroupBox("👤 بيانات المستخدم ومُدخل السند")
        self.user_info_frame.setMaximumHeight(100)

        layout = QGridLayout()
        layout.setVerticalSpacing(5)
        layout.setHorizontalSpacing(15)

        # الصف الأول
        layout.addWidget(QLabel("مدخل السجل:"), 0, 0)
        self.record_creator_label = QLabel("مدير النظام")
        self.record_creator_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        layout.addWidget(self.record_creator_label, 0, 1)

        layout.addWidget(QLabel("معدل السجل:"), 0, 2)
        self.record_modifier_label = QLabel("-")
        layout.addWidget(self.record_modifier_label, 0, 3)

        layout.addWidget(QLabel("تاريخ الإدخال:"), 0, 4)
        current_datetime = QDateTime.currentDateTime().toString("dd/MM/yyyy hh:mm:ss")
        self.creation_date_label = QLabel(current_datetime)
        layout.addWidget(self.creation_date_label, 0, 5)

        # الصف الثاني
        layout.addWidget(QLabel("تاريخ آخر تعديل:"), 1, 0)
        self.modification_date_label = QLabel("-")
        layout.addWidget(self.modification_date_label, 1, 1)

        layout.addWidget(QLabel("الجهاز المُدخل:"), 1, 2)
        self.input_device_label = QLabel("GLST002")
        layout.addWidget(self.input_device_label, 1, 3)

        layout.addWidget(QLabel("الجهاز المعدل:"), 1, 4)
        self.modifier_device_label = QLabel("-")
        layout.addWidget(self.modifier_device_label, 1, 5)

        # الصف الثالث
        layout.addWidget(QLabel("مرات الطباعة:"), 2, 0)
        self.print_count_label = QLabel("0")
        layout.addWidget(self.print_count_label, 2, 1)

        layout.addWidget(QLabel("مرات التعديل:"), 2, 2)
        self.modification_count_label = QLabel("0")
        layout.addWidget(self.modification_count_label, 2, 3)

        # معلومات النظام
        system_info_layout = QHBoxLayout()

        # الإصدار
        version_label = QLabel("V8.1.18-05-2025")
        version_label.setStyleSheet("color: #7f8c8d; font-size: 10px;")
        system_info_layout.addWidget(version_label)

        system_info_layout.addStretch()

        # المساعدة
        help_label = QLabel("للمساعدة اضغط على F9")
        help_label.setStyleSheet("color: #3498db; font-size: 10px; font-style: italic;")
        system_info_layout.addWidget(help_label)

        layout.addLayout(system_info_layout, 2, 4, 1, 2)

        self.user_info_frame.setLayout(layout)

    def create_status_bar(self):
        """إنشاء شريط الحالة البسيط"""
        self.status_frame = QFrame()
        self.status_frame.setMaximumHeight(25)
        self.status_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 3px;
                margin: 2px;
            }
        """)

        layout = QHBoxLayout()
        layout.setContentsMargins(10, 3, 10, 3)

        # حالة السند
        self.voucher_status_label = QLabel("📄 سند جديد")
        self.voucher_status_label.setStyleSheet("color: white; font-size: 11px;")
        layout.addWidget(self.voucher_status_label)

        layout.addStretch()

        # معلومات سريعة
        info_label = QLabel("جاهز للإدخال")
        info_label.setStyleSheet("color: #bdc3c7; font-size: 10px;")
        layout.addWidget(info_label)

        self.status_frame.setLayout(layout)

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            # إنشاء جدول السندات
            create_vouchers_table = """
            CREATE TABLE IF NOT EXISTS vouchers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                voucher_number TEXT UNIQUE NOT NULL,
                voucher_type TEXT NOT NULL,
                voucher_date DATE NOT NULL,
                beneficiary TEXT,
                beneficiary_id TEXT,
                cashbox_code TEXT,
                cashbox_name TEXT,
                currency TEXT DEFAULT 'ريال سعودي',
                exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
                reference TEXT,
                description TEXT,
                status TEXT DEFAULT 'مسودة',
                total_debit DECIMAL(15,2) DEFAULT 0.00,
                total_credit DECIMAL(15,2) DEFAULT 0.00,
                created_by TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_by TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
            """

            # إنشاء جدول تفاصيل السندات
            create_voucher_details_table = """
            CREATE TABLE IF NOT EXISTS voucher_details (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                voucher_id INTEGER NOT NULL,
                row_number INTEGER NOT NULL,
                account_code TEXT NOT NULL,
                account_name TEXT,
                description TEXT,
                amount DECIMAL(15,2) DEFAULT 0.00,
                cost_center TEXT,
                notes TEXT,
                FOREIGN KEY (voucher_id) REFERENCES vouchers (id)
            )
            """

            self.db_manager.execute_update(create_vouchers_table)
            self.db_manager.execute_update(create_voucher_details_table)

        except Exception as e:
            QMessageBox.critical(self, "خطأ في قاعدة البيانات", f"فشل في إعداد قاعدة البيانات:\n{str(e)}")

    def load_combo_data(self):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # أنواع السندات
            if self.voucher_type == "receipt":
                voucher_types = ["سند قبض نقدي", "سند قبض بنكي", "سند قبض شيك"]
            else:
                voucher_types = ["سند صرف نقدي", "سند صرف بنكي", "سند صرف شيك"]

            self.voucher_type_combo.addItems(voucher_types)

            # العملات
            currencies = ["ريال سعودي", "دولار أمريكي", "يورو", "جنيه إسترليني"]
            self.currency_combo.addItems(currencies)

            # توليد رقم السند التلقائي
            self.generate_voucher_number()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")

    def generate_voucher_number(self):
        """توليد رقم السند التلقائي"""
        try:
            prefix = "RC" if self.voucher_type == "receipt" else "PY"
            year = QDate.currentDate().year()

            # البحث عن آخر رقم سند
            query = """
            SELECT MAX(CAST(SUBSTR(voucher_number, -6) AS INTEGER))
            FROM vouchers
            WHERE voucher_type = ? AND voucher_number LIKE ?
            """
            pattern = f"{prefix}{year}%"
            result = self.db_manager.execute_query(query, (self.voucher_type, pattern))

            last_number = result[0][0] if result and result[0][0] else 0
            new_number = last_number + 1

            voucher_number = f"{prefix}{year}{new_number:06d}"
            self.voucher_number_edit.setText(voucher_number)

        except Exception as e:
            # في حالة الخطأ، استخدم رقم افتراضي
            prefix = "RC" if self.voucher_type == "receipt" else "PY"
            year = QDate.currentDate().year()
            self.voucher_number_edit.setText(f"{prefix}{year}000001")

    def add_detail_row(self):
        """إضافة صف جديد لتفاصيل السند"""
        row_count = self.details_table.rowCount()
        self.details_table.insertRow(row_count)

        # رقم الحساب
        account_code_item = QTableWidgetItem("")
        self.details_table.setItem(row_count, 0, account_code_item)

        # الحساب التحليلي
        analytical_account_item = QTableWidgetItem("")
        self.details_table.setItem(row_count, 1, analytical_account_item)

        # الاسم
        account_name_item = QTableWidgetItem("")
        self.details_table.setItem(row_count, 2, account_name_item)

        # البيان
        description_item = QTableWidgetItem("")
        self.details_table.setItem(row_count, 3, description_item)

        # العملة
        currency_item = QTableWidgetItem("R.Y")
        self.details_table.setItem(row_count, 4, currency_item)

        # سعر التحويل
        exchange_rate_item = QTableWidgetItem("1.0000")
        exchange_rate_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.details_table.setItem(row_count, 5, exchange_rate_item)

        # نسبة
        percentage_item = QTableWidgetItem("0.00")
        percentage_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.details_table.setItem(row_count, 6, percentage_item)

        # المبلغ
        amount_item = QTableWidgetItem("0.00")
        amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.details_table.setItem(row_count, 7, amount_item)

        # المبلغ الأجنبي
        foreign_amount_item = QTableWidgetItem("0.00")
        foreign_amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.details_table.setItem(row_count, 8, foreign_amount_item)

        # رقم المركز
        center_number_item = QTableWidgetItem("")
        self.details_table.setItem(row_count, 9, center_number_item)

        # رقم المندوب/المرجع
        representative_item = QTableWidgetItem("")
        self.details_table.setItem(row_count, 10, representative_item)

        # ربط إشارة تغيير القيم لحساب الإجماليات (إذا لم تكن مربوطة بالفعل)
        try:
            self.details_table.itemChanged.disconnect()
        except:
            pass
        self.details_table.itemChanged.connect(self.calculate_totals)

        # ربط إشارة النقر على الخلايا
        try:
            self.details_table.cellClicked.disconnect()
        except:
            pass
        self.details_table.cellClicked.connect(self.on_cell_clicked)

    def delete_detail_row(self):
        """حذف الصف المحدد"""
        current_row = self.details_table.currentRow()
        if current_row >= 0:
            self.details_table.removeRow(current_row)
            self.update_row_numbers()
            self.calculate_totals()

    def update_row_numbers(self):
        """تحديث أرقام الصفوف - لا حاجة لها في الجدول الجديد"""
        pass  # الجدول الجديد لا يحتوي على عمود رقم الصف

    def calculate_totals(self):
        """حساب الإجماليات"""
        total_amount = 0.0
        items_count = 0

        for row in range(self.details_table.rowCount()):
            # حساب المبلغ (العمود 7 في الجدول الجديد)
            amount_item = self.details_table.item(row, 7)
            account_item = self.details_table.item(row, 0)

            if amount_item and amount_item.text() and account_item and account_item.text():
                try:
                    amount_text = amount_item.text().replace(',', '')  # إزالة الفواصل
                    amount = float(amount_text)
                    if amount > 0:  # فقط البنود التي لها مبلغ
                        total_amount += amount
                        items_count += 1
                except ValueError:
                    pass

        # تحديث الليبلات إذا كانت موجودة
        if hasattr(self, 'total_debit_label'):
            self.total_debit_label.setText(f"{total_amount:,.2f}")
        if hasattr(self, 'items_count_label'):
            self.items_count_label.setText(str(items_count))

        # تحديث المبلغ المحلي في رأس السند
        if hasattr(self, 'local_amount_edit'):
            self.local_amount_edit.setText(f"{total_amount:,.2f}")

        # تحديث حالة السند
        if total_amount > 0 and items_count > 0:
            self.balance_status_label.setText("سند صحيح")
            self.balance_status_label.setStyleSheet("""
                QLabel {
                    background-color: #d4edda;
                    border: 2px solid #28a745;
                    border-radius: 5px;
                    padding: 15px;
                    font-size: 16px;
                    font-weight: bold;
                    color: #155724;
                    text-align: center;
                    min-width: 150px;
                }
            """)
        else:
            self.balance_status_label.setText("سند فارغ")
            self.balance_status_label.setStyleSheet("""
                QLabel {
                    background-color: #fff3cd;
                    border: 2px solid #ffc107;
                    border-radius: 5px;
                    padding: 15px;
                    font-size: 16px;
                    font-weight: bold;
                    color: #856404;
                    text-align: center;
                    min-width: 150px;
                }
            """)

    # وظائف شريط الأدوات
    def save_voucher(self):
        """حفظ السند"""
        try:
            # التحقق من صحة البيانات
            if not self.voucher_number_edit.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم السند")
                return

            if not self.beneficiary_edit.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المستفيد")
                return

            # التحقق من توازن السند
            difference = abs(float(self.total_debit_label.text()) - float(self.total_credit_label.text()))
            if difference >= 0.01:
                reply = QMessageBox.question(
                    self, "تحذير",
                    "السند غير متوازن. هل تريد المتابعة؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply == QMessageBox.No:
                    return

            # حفظ السند
            voucher_data = self.collect_voucher_data()

            if self.current_voucher_id:
                self.update_voucher(voucher_data)
            else:
                self.insert_voucher(voucher_data)

            QMessageBox.information(self, "نجح", "تم حفظ السند بنجاح")
            self.voucher_saved.emit(voucher_data)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ السند:\n{str(e)}")

    def new_voucher(self):
        """سند جديد"""
        self.clear_form()
        self.generate_voucher_number()
        self.voucher_status_label.setText("📄 سند جديد")

    def edit_voucher(self):
        """تعديل السند"""
        if not self.current_voucher_id:
            QMessageBox.warning(self, "تحذير", "لا يوجد سند محدد للتعديل")
            return

        self.voucher_status_label.setText("✏️ وضع التعديل")

    def delete_voucher(self):
        """حذف السند"""
        if not self.current_voucher_id:
            QMessageBox.warning(self, "تحذير", "لا يوجد سند محدد للحذف")
            return

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف السند رقم '{self.voucher_number_edit.text()}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # حذف تفاصيل السند
                self.db_manager.execute_update(
                    "DELETE FROM voucher_details WHERE voucher_id = ?",
                    (self.current_voucher_id,)
                )

                # حذف السند
                self.db_manager.execute_update(
                    "UPDATE vouchers SET is_active = 0 WHERE id = ?",
                    (self.current_voucher_id,)
                )

                QMessageBox.information(self, "تم الحذف", "تم حذف السند بنجاح")
                self.new_voucher()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف السند:\n{str(e)}")

    def first_voucher(self):
        """الانتقال للسند الأول"""
        try:
            query = "SELECT * FROM vouchers WHERE voucher_type = ? AND is_active = 1 ORDER BY id LIMIT 1"
            result = self.db_manager.execute_query(query, (self.voucher_type,))

            if result:
                self.load_voucher_data(result[0])
            else:
                QMessageBox.information(self, "معلومات", "لا توجد سندات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل السند:\n{str(e)}")

    def previous_voucher(self):
        """الانتقال للسند السابق"""
        if not self.current_voucher_id:
            return

        try:
            query = """
            SELECT * FROM vouchers
            WHERE voucher_type = ? AND is_active = 1 AND id < ?
            ORDER BY id DESC LIMIT 1
            """
            result = self.db_manager.execute_query(query, (self.voucher_type, self.current_voucher_id))

            if result:
                self.load_voucher_data(result[0])
            else:
                QMessageBox.information(self, "معلومات", "هذا هو السند الأول")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل السند:\n{str(e)}")

    def next_voucher(self):
        """الانتقال للسند التالي"""
        if not self.current_voucher_id:
            return

        try:
            query = """
            SELECT * FROM vouchers
            WHERE voucher_type = ? AND is_active = 1 AND id > ?
            ORDER BY id LIMIT 1
            """
            result = self.db_manager.execute_query(query, (self.voucher_type, self.current_voucher_id))

            if result:
                self.load_voucher_data(result[0])
            else:
                QMessageBox.information(self, "معلومات", "هذا هو السند الأخير")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل السند:\n{str(e)}")

    def last_voucher(self):
        """الانتقال للسند الأخير"""
        try:
            query = "SELECT * FROM vouchers WHERE voucher_type = ? AND is_active = 1 ORDER BY id DESC LIMIT 1"
            result = self.db_manager.execute_query(query, (self.voucher_type,))

            if result:
                self.load_voucher_data(result[0])
            else:
                QMessageBox.information(self, "معلومات", "لا توجد سندات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل السند:\n{str(e)}")

    def print_voucher(self):
        """طباعة السند"""
        if not self.current_voucher_id:
            QMessageBox.warning(self, "تحذير", "لا يوجد سند محدد للطباعة")
            return

        QMessageBox.information(self, "طباعة", "سيتم إرسال السند للطباعة")

    def search_voucher(self):
        """البحث عن سند"""
        from PyQt5.QtWidgets import QInputDialog

        search_text, ok = QInputDialog.getText(
            self, "البحث عن سند",
            "أدخل رقم السند أو اسم المستفيد:"
        )

        if ok and search_text.strip():
            try:
                query = """
                SELECT * FROM vouchers
                WHERE voucher_type = ? AND is_active = 1 AND (
                    voucher_number LIKE ? OR
                    beneficiary LIKE ?
                )
                LIMIT 1
                """
                search_pattern = f"%{search_text.strip()}%"
                result = self.db_manager.execute_query(
                    query, (self.voucher_type, search_pattern, search_pattern)
                )

                if result:
                    self.load_voucher_data(result[0])
                    QMessageBox.information(self, "نتيجة البحث", "تم العثور على السند")
                else:
                    QMessageBox.information(self, "نتيجة البحث", "لم يتم العثور على السند")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في البحث:\n{str(e)}")

    def preview_voucher(self):
        """معاينة السند"""
        if not self.current_voucher_id:
            QMessageBox.warning(self, "تحذير", "لا يوجد سند محدد للمعاينة")
            return

        # إنشاء نافذة معاينة
        preview_text = self.generate_voucher_preview()

        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton

        preview_dialog = QDialog(self)
        preview_dialog.setWindowTitle("معاينة السند")
        preview_dialog.setGeometry(200, 200, 600, 500)

        layout = QVBoxLayout()

        text_edit = QTextEdit()
        text_edit.setPlainText(preview_text)
        text_edit.setReadOnly(True)
        layout.addWidget(text_edit)

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(preview_dialog.accept)
        layout.addWidget(close_btn)

        preview_dialog.setLayout(layout)
        preview_dialog.exec_()

    # وظائف مساعدة
    def collect_voucher_data(self):
        """جمع بيانات السند"""
        return {
            'voucher_number': self.voucher_number_edit.text(),
            'voucher_type': self.voucher_type,
            'voucher_date': self.voucher_date_edit.date().toString('yyyy-MM-dd'),
            'beneficiary': self.beneficiary_edit.text(),
            'beneficiary_id': self.beneficiary_id_edit.text(),
            'cashbox_code': self.cashbox_code_edit.text(),
            'cashbox_name': self.cashbox_name_edit.text(),
            'currency': self.currency_combo.currentText(),
            'exchange_rate': float(self.exchange_rate_edit.text() or 1.0),
            'reference': self.reference_edit.text(),
            'description': self.description_edit.text(),  # تغيير من toPlainText إلى text
            'status': self.status_combo.currentText(),
            'total_debit': float(self.total_debit_label.text().replace(',', '') or 0),
            'total_credit': float(self.total_debit_label.text().replace(',', '') or 0)
        }

    def collect_voucher_details(self):
        """جمع تفاصيل السند"""
        details = []
        for row in range(self.details_table.rowCount()):
            detail = {
                'row_number': row + 1,
                'account_code': self.details_table.item(row, 1).text() if self.details_table.item(row, 1) else "",
                'account_name': self.details_table.item(row, 2).text() if self.details_table.item(row, 2) else "",
                'description': self.details_table.item(row, 3).text() if self.details_table.item(row, 3) else "",
                'amount': float(self.details_table.item(row, 4).text().replace(',', '') or 0),
                'cost_center': self.details_table.item(row, 5).text() if self.details_table.item(row, 5) else "",
                'notes': self.details_table.item(row, 6).text() if self.details_table.item(row, 6) else ""
            }
            details.append(detail)
        return details

    def insert_voucher(self, voucher_data):
        """إدراج سند جديد"""
        try:
            # إدراج السند الرئيسي
            query = """
            INSERT INTO vouchers (
                voucher_number, voucher_type, voucher_date, beneficiary, beneficiary_id,
                cashbox_code, cashbox_name, currency, exchange_rate, reference,
                description, status, total_debit, total_credit, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            values = (
                voucher_data['voucher_number'], voucher_data['voucher_type'],
                voucher_data['voucher_date'], voucher_data['beneficiary'],
                voucher_data['beneficiary_id'], voucher_data['cashbox_code'],
                voucher_data['cashbox_name'], voucher_data['currency'],
                voucher_data['exchange_rate'], voucher_data['reference'],
                voucher_data['description'], voucher_data['status'],
                voucher_data['total_debit'], voucher_data['total_credit'],
                "المستخدم الحالي"
            )

            self.db_manager.execute_update(query, values)

            # الحصول على ID السند الجديد
            self.current_voucher_id = self.db_manager.cursor.lastrowid

            # إدراج تفاصيل السند
            self.insert_voucher_details()

            self.voucher_status_label.setText(f"📄 سند محفوظ - رقم {voucher_data['voucher_number']}")

        except Exception as e:
            raise Exception(f"فشل في إدراج السند: {str(e)}")

    def insert_voucher_details(self):
        """إدراج تفاصيل السند"""
        details = self.collect_voucher_details()

        for detail in details:
            # تجاهل الصفوف الفارغة
            if not detail['account_code'] and detail['amount'] == 0:
                continue

            query = """
            INSERT INTO voucher_details (
                voucher_id, row_number, account_code, account_name, description,
                amount, cost_center, notes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """

            values = (
                self.current_voucher_id, detail['row_number'], detail['account_code'],
                detail['account_name'], detail['description'], detail['amount'],
                detail['cost_center'], detail['notes']
            )

            self.db_manager.execute_update(query, values)

    def load_voucher_data(self, voucher_record):
        """تحميل بيانات السند"""
        if not voucher_record:
            return

        self.current_voucher_id = voucher_record[0]

        # تحميل البيانات الأساسية
        self.voucher_number_edit.setText(str(voucher_record[1]))
        self.voucher_date_edit.setDate(QDate.fromString(voucher_record[3], 'yyyy-MM-dd'))
        self.beneficiary_edit.setText(str(voucher_record[4] or ""))
        self.beneficiary_id_edit.setText(str(voucher_record[5] or ""))
        self.cashbox_code_edit.setText(str(voucher_record[6] or ""))
        self.cashbox_name_edit.setText(str(voucher_record[7] or ""))
        self.currency_combo.setCurrentText(str(voucher_record[8] or "ريال سعودي"))
        self.exchange_rate_edit.setText(str(voucher_record[9] or 1.0))
        self.reference_edit.setText(str(voucher_record[10] or ""))
        self.description_edit.setText(str(voucher_record[11] or ""))  # تغيير من setPlainText إلى setText
        self.status_combo.setCurrentText(str(voucher_record[12] or "مسودة"))

        # تحميل تفاصيل السند
        self.load_voucher_details()

        # تحديث شريط الحالة
        self.voucher_status_label.setText(f"📄 سند رقم {voucher_record[1]}")

    def load_voucher_details(self):
        """تحميل تفاصيل السند"""
        if not self.current_voucher_id:
            return

        try:
            query = """
            SELECT * FROM voucher_details
            WHERE voucher_id = ?
            ORDER BY row_number
            """
            details = self.db_manager.execute_query(query, (self.current_voucher_id,))

            # مسح الجدول
            self.details_table.setRowCount(0)

            # تحميل التفاصيل
            for detail in details:
                row = self.details_table.rowCount()
                self.details_table.insertRow(row)

                self.details_table.setItem(row, 0, QTableWidgetItem(str(detail[2])))  # row_number
                self.details_table.setItem(row, 1, QTableWidgetItem(str(detail[3])))  # account_code
                self.details_table.setItem(row, 2, QTableWidgetItem(str(detail[4] or "")))  # account_name
                self.details_table.setItem(row, 3, QTableWidgetItem(str(detail[5] or "")))  # description

                # المبلغ مع محاذاة يمين
                amount_item = QTableWidgetItem(str(detail[6]))  # amount
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                self.details_table.setItem(row, 4, amount_item)

                self.details_table.setItem(row, 5, QTableWidgetItem(str(detail[7] or "")))  # cost_center
                self.details_table.setItem(row, 6, QTableWidgetItem(str(detail[8] or "")))  # notes

            # حساب الإجماليات
            self.calculate_totals()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل تفاصيل السند:\n{str(e)}")

    def clear_form(self):
        """مسح النموذج"""
        self.current_voucher_id = None

        # مسح البيانات الأساسية
        self.voucher_number_edit.clear()
        self.voucher_date_edit.setDate(QDate.currentDate())
        self.beneficiary_edit.clear()
        self.beneficiary_id_edit.clear()
        self.cashbox_code_edit.clear()
        self.cashbox_name_edit.clear()
        self.currency_combo.setCurrentIndex(0)
        self.exchange_rate_edit.setText("1.00")
        self.reference_edit.clear()
        self.description_edit.clear()  # QLineEdit.clear() بدلاً من QTextEdit
        self.status_combo.setCurrentIndex(0)

        # مسح الجدول
        self.details_table.setRowCount(0)
        self.add_detail_row()

        # إعادة تعيين الإجماليات
        self.total_debit_label.setText("0.00")
        self.total_credit_label.setText("0.00")
        self.difference_label.setText("0.00")
        self.balance_status_label.setText("غير متوازن")

    def generate_voucher_preview(self):
        """إنتاج معاينة السند"""
        title = "سند قبض" if self.voucher_type == "receipt" else "سند صرف"

        preview = f"""
=== {title} - Onyx Pro ERP ===

رقم السند: {self.voucher_number_edit.text()}
التاريخ: {self.voucher_date_edit.date().toString('yyyy/MM/dd')}
المستفيد: {self.beneficiary_edit.text()}
الصندوق/البنك: {self.cashbox_code_edit.text()} - {self.cashbox_name_edit.text()}
العملة: {self.currency_combo.currentText()}
الحالة: {self.status_combo.currentText()}

البيان: {self.description_edit.text()}

=== تفاصيل السند ===
"""

        for row in range(self.details_table.rowCount()):
            account_code = self.details_table.item(row, 1).text() if self.details_table.item(row, 1) else ""
            account_name = self.details_table.item(row, 2).text() if self.details_table.item(row, 2) else ""
            description = self.details_table.item(row, 3).text() if self.details_table.item(row, 3) else ""
            amount = self.details_table.item(row, 4).text() if self.details_table.item(row, 4) else "0.00"

            if account_code or float(amount.replace(',', '') or 0) > 0:
                preview += f"{row+1}. {account_code} - {account_name}\n"
                preview += f"   البيان: {description}\n"
                preview += f"   المبلغ: {amount}\n\n"

        preview += f"""
=== إجمالي السند ===
إجمالي المبلغ: {self.total_debit_label.text()}
عدد البنود: {self.items_count_label.text()}
حالة السند: {self.balance_status_label.text()}
        """

        return preview

    def show_reports(self):
        """عرض التقارير"""
        QMessageBox.information(self, "تقارير السندات",
                              "📊 تقارير السندات:\n\n"
                              "• تقرير السندات اليومية\n"
                              "• تقرير السندات الشهرية\n"
                              "• تقرير الحسابات\n"
                              "• تقرير الأرصدة")

    def show_settings(self):
        """عرض الإعدادات"""
        QMessageBox.information(self, "إعدادات النظام",
                              "⚙️ إعدادات النظام:\n\n"
                              "• إعدادات الطباعة\n"
                              "• إعدادات العملة\n"
                              "• إعدادات المستخدمين\n"
                              "• إعدادات النسخ الاحتياطي")

    def open_voucher(self):
        """فتح سند موجود"""
        QMessageBox.information(self, "فتح سند",
                              "📂 فتح سند موجود:\n\n"
                              "أدخل رقم السند المطلوب فتحه")

    def show_help(self):
        """عرض المساعدة"""
        help_text = """
        🔧 مساعدة نظام السندات - Onyx Pro ERP

        ⌨️ اختصارات لوحة المفاتيح:
        • F1: المساعدة
        • F2: تعديل السند
        • F3: البحث
        • F9: البحث في الحسابات
        • Ctrl+S: حفظ
        • Ctrl+N: سند جديد
        • Ctrl+P: طباعة
        • ESC: إلغاء

        📋 استخدام السندات:
        1. اختر نوع السند (قبض/صرف)
        2. أدخل بيانات الرأس
        3. أضف تفاصيل الحسابات
        4. تأكد من توازن المدين والدائن
        5. احفظ السند

        💡 نصائح:
        • استخدم F9 للبحث السريع في الحسابات
        • تأكد من صحة أرقام الحسابات
        • راجع الإجماليات قبل الحفظ
        """

        QMessageBox.information(self, "مساعدة النظام", help_text)

    def search_cashbox(self):
        """البحث عن الصندوق/البنك"""
        from PyQt5.QtWidgets import QInputDialog

        search_text, ok = QInputDialog.getText(
            self, "البحث عن صندوق/بنك",
            "أدخل رقم أو اسم الصندوق/البنك:"
        )

        if ok and search_text.strip():
            # محاكاة البحث
            cashboxes = [
                ("111001", "صندوق المحل الرئيسي"),
                ("112001", "البنك الأهلي اليمني"),
                ("112002", "بنك التضامن الإسلامي"),
                ("111002", "صندوق الفرع الأول")
            ]

            found = None
            for code, name in cashboxes:
                if search_text.lower() in code.lower() or search_text.lower() in name.lower():
                    found = (code, name)
                    break

            if found:
                self.cashbox_code_edit.setText(found[0])
                if hasattr(self, 'cashbox_name_edit'):
                    self.cashbox_name_edit.setText(found[1])
                QMessageBox.information(self, "نتيجة البحث", f"تم العثور على:\n{found[0]} - {found[1]}")
            else:
                QMessageBox.information(self, "نتيجة البحث", "لم يتم العثور على صندوق/بنك مطابق").strip()

    def setup_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # F9 للبحث عن الحسابات
        f9_shortcut = QShortcut(QKeySequence(Qt.Key_F9), self)
        f9_shortcut.activated.connect(self.open_account_search)

        # Ctrl+S للحفظ
        save_shortcut = QShortcut(QKeySequence("Ctrl+S"), self)
        save_shortcut.activated.connect(self.save_voucher)

        # Ctrl+N لسند جديد
        new_shortcut = QShortcut(QKeySequence("Ctrl+N"), self)
        new_shortcut.activated.connect(self.new_voucher)

        # F1 للمساعدة
        help_shortcut = QShortcut(QKeySequence(Qt.Key_F1), self)
        help_shortcut.activated.connect(self.show_help)

    def on_cell_clicked(self, row, column):
        """عند النقر على خلية في الجدول"""
        # إذا كان النقر في عمود رقم الحساب (العمود 1)
        if column == 1:
            self.current_account_row = row
            self.current_account_column = column

    def open_account_search(self):
        """فتح نافذة البحث عن الحسابات"""
        try:
            # التحقق من أن المؤشر في عمود رقم الحساب
            current_row = self.details_table.currentRow()
            current_column = self.details_table.currentColumn()

            if current_column != 1:  # ليس في عمود رقم الحساب
                # الانتقال لعمود رقم الحساب في الصف الحالي
                if current_row >= 0:
                    self.details_table.setCurrentCell(current_row, 1)
                else:
                    # إضافة صف جديد إذا لم يكن هناك صف محدد
                    self.add_detail_row()
                    self.details_table.setCurrentCell(self.details_table.rowCount() - 1, 1)

                current_row = self.details_table.currentRow()
                current_column = 1

            # حفظ الموقع الحالي
            self.current_account_row = current_row
            self.current_account_column = current_column

            # فتح نافذة البحث
            from .account_search_dialog import AccountSearchDialog
            search_dialog = AccountSearchDialog(self.db_manager, self)
            search_dialog.account_selected.connect(self.on_account_selected)
            search_dialog.exec_()

        except ImportError:
            # في حالة عدم وجود نافذة البحث، عرض رسالة
            QMessageBox.information(
                self,
                "البحث عن الحسابات",
                "نافذة البحث عن الحسابات قيد التطوير.\n\nاستخدم F9 للبحث عن الحسابات."
            )
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة البحث:\n{str(e)}")

    def on_account_selected(self, account_data):
        """عند اختيار حساب من نافذة البحث"""
        try:
            if hasattr(self, 'current_account_row') and self.current_account_row >= 0:
                row = self.current_account_row

                # تعبئة رقم الحساب
                account_code_item = QTableWidgetItem(account_data['code'])
                self.details_table.setItem(row, 1, account_code_item)

                # تعبئة اسم الحساب
                account_name_item = QTableWidgetItem(account_data['name'])
                self.details_table.setItem(row, 2, account_name_item)

                # تعبئة البيان إذا كان فارغاً
                description_item = self.details_table.item(row, 3)
                if not description_item or not description_item.text().strip():
                    description_text = f"حساب {account_data['name']}"
                    description_item = QTableWidgetItem(description_text)
                    self.details_table.setItem(row, 3, description_item)

                # الانتقال للعمود التالي (المبلغ)
                self.details_table.setCurrentCell(row, 4)  # عمود المبلغ

                # تحديث شريط الحالة
                self.voucher_status_label.setText(f"تم اختيار الحساب: {account_data['code']} - {account_data['name']}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تعبئة بيانات الحساب:\n{str(e)}")

    def search_cashbox(self):
        """البحث عن الصندوق/البنك"""
        try:
            # فتح نافذة البحث مخصصة للصناديق والبنوك
            from .account_search_dialog import AccountSearchDialog
            search_dialog = AccountSearchDialog(self.db_manager, self)

            # تخصيص البحث للصناديق والبنوك فقط
            search_dialog.setWindowTitle("البحث عن الصناديق والبنوك")
            search_dialog.search_type_combo.clear()
            search_dialog.search_type_combo.addItems([
                "الكل",
                "رقم الحساب",
                "اسم الحساب"
            ])

            # تصفية النتائج للصناديق والبنوك فقط
            search_dialog.filter_cashbox_accounts()

            def on_cashbox_selected(account_data):
                # تعبئة بيانات الصندوق
                self.cashbox_code_edit.setText(account_data['code'])
                self.cashbox_name_edit.setText(account_data['name'])

                # تحديث شريط الحالة
                self.voucher_status_label.setText(f"تم اختيار الصندوق: {account_data['code']} - {account_data['name']}")

            search_dialog.account_selected.connect(on_cashbox_selected)
            search_dialog.exec_()

        except ImportError:
            # في حالة عدم وجود نافذة البحث، عرض قائمة بسيطة
            self.show_simple_cashbox_list()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح البحث عن الصندوق:\n{str(e)}")

    def show_simple_cashbox_list(self):
        """عرض قائمة بسيطة للصناديق والبنوك"""
        from PyQt5.QtWidgets import QInputDialog

        # قائمة الصناديق والبنوك الافتراضية
        cashboxes = [
            "1010001 - صندوق المركز الرئيسي",
            "1010002 - صندوق الفرع الأول",
            "1010003 - صندوق الفرع الثاني",
            "1020001 - البنك الأهلي التجاري",
            "1020002 - مصرف الراجحي",
            "1020003 - البنك السعودي للاستثمار",
            "1020004 - بنك الرياض",
            "1020005 - البنك السعودي الفرنسي"
        ]

        selected_cashbox, ok = QInputDialog.getItem(
            self,
            "اختيار الصندوق/البنك",
            "اختر الصندوق أو البنك:",
            cashboxes,
            0,
            False
        )

        if ok and selected_cashbox:
            # استخراج الرقم والاسم
            parts = selected_cashbox.split(" - ")
            if len(parts) >= 2:
                code = parts[0]
                name = parts[1]

                self.cashbox_code_edit.setText(code)
                self.cashbox_name_edit.setText(name)

                # تحديث شريط الحالة
                self.voucher_status_label.setText(f"تم اختيار الصندوق: {code} - {name}")

    def ensure_text_visibility(self, item):
        """ضمان ظهور النص في عناصر الجدول"""
        if item:
            # إزالة أي تنسيق قد يخفي النص
            from PyQt5.QtGui import QColor, QFont
            item.setForeground(QColor("black"))
            item.setBackground(QColor("white"))

            # التأكد من أن النص مرئي
            if item.column() == 7:  # عمود المبلغ في الجدول الجديد
                item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                # تطبيق خط واضح للمبلغ
                font = QFont("Tahoma", 11)
                item.setFont(font)
            elif item.column() in [5, 6, 8]:  # أعمدة الأرقام الأخرى
                item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)

    def show_help(self):
        """عرض نافذة المساعدة"""
        help_text = """
🔍 اختصارات لوحة المفاتيح في نظام السندات:

⌨️ الاختصارات الأساسية:
• F9 - البحث عن الحسابات (في عمود رقم الحساب)
• Ctrl+S - حفظ السند
• Ctrl+N - سند جديد
• F1 - عرض هذه المساعدة
• Enter - اختيار الحساب (في نافذة البحث)
• Esc - إلغاء (في نافذة البحث)

🧾 استخدام البحث عن الحسابات:
1. انقر في عمود "رقم الحساب" في الجدول
2. اضغط F9 لفتح نافذة البحث
3. اكتب جزء من رقم الحساب أو اسمه
4. اختر نوع البحث من القائمة المنسدلة
5. انقر نقراً مزدوجاً على الحساب أو اضغط Enter

💡 نصائح:
• يمكن البحث في رقم الحساب أو اسمه أو نوعه
• البحث التلقائي يحدث أثناء الكتابة
• استخدم الأسهم للتنقل في النتائج
• يتم تعبئة رقم واسم الحساب تلقائياً عند الاختيار
        """

        QMessageBox.information(self, "مساعدة نظام السندات", help_text.strip())

    def keyPressEvent(self, event):
        """معالجة ضغطات المفاتيح"""
        if event.key() == Qt.Key_F9:
            # التحقق من التركيز
            if self.details_table.hasFocus():
                # البحث عن الحسابات في الجدول
                self.open_account_search()
            elif self.cashbox_code_edit.hasFocus():
                # البحث عن الصندوق/البنك
                self.search_cashbox()
            else:
                # الانتقال للجدول وفتح البحث
                self.details_table.setFocus()
                if self.details_table.rowCount() == 0:
                    self.add_detail_row()
                self.details_table.setCurrentCell(0, 1)  # عمود رقم الحساب
                self.open_account_search()
        else:
            super().keyPressEvent(event)


# دالة اختبار الواجهة
def main():
    """دالة اختبار واجهة السندات"""
    import sys
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        def __init__(self):
            self.cursor = type('obj', (object,), {'lastrowid': 1})()

        def execute_query(self, query, params=None):
            return []

        def execute_update(self, query, params=None):
            return True

    db_manager = MockDBManager()

    # إنشاء واجهة سند قبض
    receipt_dialog = VoucherInterface(db_manager, "receipt")
    receipt_dialog.show()

    print("🎉 تم تشغيل نظام السندات مع البحث بـ F9!")
    print("📋 الميزات المتاحة:")
    print("   ✅ سند القبض مع تخصيص للعملاء")
    print("   ✅ سند الصرف مع تخصيص للموردين")
    print("   ✅ واجهة موحدة احترافية")
    print("   ✅ شريط أدوات كامل")
    print("   🔍 البحث عن الحسابات بـ F9")
    print("   ⌨️ اختصارات لوحة المفاتيح")
    print("")
    print("💡 نصائح الاستخدام:")
    print("   • اضغط F9 في عمود رقم الحساب للبحث")
    print("   • اضغط F1 لعرض المساعدة")
    print("   • اضغط Ctrl+S للحفظ")
    print("   • اضغط Ctrl+N لسند جديد")

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
