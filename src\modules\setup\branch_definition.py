#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة تعريف الفرع
Branch Definition Setup Screen
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QMessageBox, 
                             QFrame, QSplitter, QTextEdit, QTabWidget, QWidget,
                             QDateEdit, QTimeEdit, QListWidget, QListWidgetItem,
                             QProgressBar, QSlider)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime
from PyQt5.QtGui import QFont, QColor, QIcon, QPixmap

class BranchDefinitionDialog(QDialog):
    """شاشة تعريف الفرع"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تعريف الفرع - إعداد تفاصيل الفرع الشامل")
        self.setGeometry(50, 50, 1500, 950)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان الشاشة
        title_label = QLabel("🏢 تعريف الفرع - الإعداد الشامل للفرع")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 22px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات الإعداد
        tabs = QTabWidget()
        
        # تبويب المعلومات الأساسية
        basic_info_tab = self.create_basic_info_tab()
        tabs.addTab(basic_info_tab, "📋 المعلومات الأساسية")
        
        # تبويب الموقع والاتصال
        location_tab = self.create_location_tab()
        tabs.addTab(location_tab, "📍 الموقع والاتصال")
        
        # تبويب الإعدادات المالية
        financial_tab = self.create_financial_tab()
        tabs.addTab(financial_tab, "💰 الإعدادات المالية")
        
        # تبويب أوقات العمل
        working_hours_tab = self.create_working_hours_tab()
        tabs.addTab(working_hours_tab, "⏰ أوقات العمل")
        
        # تبويب الخدمات والمرافق
        services_tab = self.create_services_tab()
        tabs.addTab(services_tab, "🛎️ الخدمات والمرافق")
        
        # تبويب الأمان والصلاحيات
        security_tab = self.create_security_tab()
        tabs.addTab(security_tab, "🔒 الأمان والصلاحيات")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة البيانات الأساسية
        basic_group = QGroupBox("📝 البيانات الأساسية للفرع")
        basic_layout = QGridLayout()
        
        # اسم الفرع
        basic_layout.addWidget(QLabel("اسم الفرع:"), 0, 0)
        self.branch_name = QLineEdit()
        self.branch_name.setPlaceholderText("مثال: فرع صنعاء الرئيسي")
        basic_layout.addWidget(self.branch_name, 0, 1)
        
        # اسم الفرع بالإنجليزية
        basic_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 0, 2)
        self.branch_name_en = QLineEdit()
        self.branch_name_en.setPlaceholderText("Sana'a Main Branch")
        basic_layout.addWidget(self.branch_name_en, 0, 3)
        
        # رمز الفرع
        basic_layout.addWidget(QLabel("رمز الفرع:"), 1, 0)
        self.branch_code = QLineEdit()
        self.branch_code.setPlaceholderText("SAN-001")
        self.branch_code.setMaxLength(15)
        basic_layout.addWidget(self.branch_code, 1, 1)
        
        # نوع الفرع
        basic_layout.addWidget(QLabel("نوع الفرع:"), 1, 2)
        self.branch_type = QComboBox()
        self.branch_type.addItems([
            "فرع رئيسي", "فرع فرعي", "مكتب تمثيلي", "مركز خدمة", 
            "مستودع", "معرض", "مصنع", "مختبر", "مركز تدريب"
        ])
        basic_layout.addWidget(self.branch_type, 1, 3)
        
        # تاريخ التأسيس
        basic_layout.addWidget(QLabel("تاريخ التأسيس:"), 2, 0)
        self.establishment_date = QDateEdit()
        self.establishment_date.setDate(QDate.currentDate())
        self.establishment_date.setCalendarPopup(True)
        basic_layout.addWidget(self.establishment_date, 2, 1)
        
        # تاريخ بدء العمل
        basic_layout.addWidget(QLabel("تاريخ بدء العمل:"), 2, 2)
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate())
        self.start_date.setCalendarPopup(True)
        basic_layout.addWidget(self.start_date, 2, 3)
        
        # مدير الفرع
        basic_layout.addWidget(QLabel("مدير الفرع:"), 3, 0)
        self.branch_manager = QComboBox()
        self.branch_manager.setEditable(True)
        basic_layout.addWidget(self.branch_manager, 3, 1)
        
        # نائب المدير
        basic_layout.addWidget(QLabel("نائب المدير:"), 3, 2)
        self.deputy_manager = QComboBox()
        self.deputy_manager.setEditable(True)
        basic_layout.addWidget(self.deputy_manager, 3, 3)
        
        # الوصف
        basic_layout.addWidget(QLabel("وصف الفرع:"), 4, 0)
        self.branch_description = QTextEdit()
        self.branch_description.setMaximumHeight(80)
        self.branch_description.setPlaceholderText("وصف مختصر لطبيعة عمل الفرع وأهدافه...")
        basic_layout.addWidget(self.branch_description, 4, 1, 1, 3)
        
        basic_group.setLayout(basic_layout)
        
        # مجموعة الحالة والإعدادات
        status_group = QGroupBox("⚙️ حالة الفرع والإعدادات")
        status_layout = QGridLayout()
        
        # فرع نشط
        self.branch_active = QCheckBox("فرع نشط ومفعل")
        self.branch_active.setChecked(True)
        status_layout.addWidget(self.branch_active, 0, 0)
        
        # فرع رئيسي
        self.is_main_branch = QCheckBox("فرع رئيسي (مقر)")
        status_layout.addWidget(self.is_main_branch, 0, 1)
        
        # يقبل المعاملات
        self.accepts_transactions = QCheckBox("يقبل المعاملات المالية")
        self.accepts_transactions.setChecked(True)
        status_layout.addWidget(self.accepts_transactions, 0, 2)
        
        # يقبل المبيعات
        self.accepts_sales = QCheckBox("يقبل عمليات البيع")
        self.accepts_sales.setChecked(True)
        status_layout.addWidget(self.accepts_sales, 1, 0)
        
        # يقبل المشتريات
        self.accepts_purchases = QCheckBox("يقبل عمليات الشراء")
        self.accepts_purchases.setChecked(True)
        status_layout.addWidget(self.accepts_purchases, 1, 1)
        
        # يقبل التحويلات
        self.accepts_transfers = QCheckBox("يقبل التحويلات بين الفروع")
        self.accepts_transfers.setChecked(True)
        status_layout.addWidget(self.accepts_transfers, 1, 2)
        
        # مستوى الأولوية
        status_layout.addWidget(QLabel("مستوى الأولوية:"), 2, 0)
        self.priority_level = QComboBox()
        self.priority_level.addItems(["عالي جداً", "عالي", "متوسط", "منخفض", "منخفض جداً"])
        self.priority_level.setCurrentText("متوسط")
        status_layout.addWidget(self.priority_level, 2, 1)
        
        # سعة الفرع
        status_layout.addWidget(QLabel("سعة الفرع (موظف):"), 2, 2)
        self.branch_capacity = QSpinBox()
        self.branch_capacity.setRange(1, 1000)
        self.branch_capacity.setValue(50)
        status_layout.addWidget(self.branch_capacity, 2, 3)
        
        status_group.setLayout(status_layout)
        
        layout.addWidget(basic_group)
        layout.addWidget(status_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab
        
    def create_location_tab(self):
        """إنشاء تبويب الموقع والاتصال"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة الموقع الجغرافي
        location_group = QGroupBox("🗺️ الموقع الجغرافي")
        location_layout = QGridLayout()
        
        # الدولة
        location_layout.addWidget(QLabel("الدولة:"), 0, 0)
        self.country = QComboBox()
        location_layout.addWidget(self.country, 0, 1)
        
        # المحافظة
        location_layout.addWidget(QLabel("المحافظة:"), 0, 2)
        self.governorate = QComboBox()
        location_layout.addWidget(self.governorate, 0, 3)
        
        # المدينة
        location_layout.addWidget(QLabel("المدينة:"), 1, 0)
        self.city = QComboBox()
        location_layout.addWidget(self.city, 1, 1)
        
        # المنطقة/المديرية
        location_layout.addWidget(QLabel("المنطقة/المديرية:"), 1, 2)
        self.district = QComboBox()
        location_layout.addWidget(self.district, 1, 3)
        
        # العنوان التفصيلي
        location_layout.addWidget(QLabel("العنوان التفصيلي:"), 2, 0)
        self.detailed_address = QLineEdit()
        self.detailed_address.setPlaceholderText("الشارع، رقم المبنى، معالم مميزة...")
        location_layout.addWidget(self.detailed_address, 2, 1, 1, 3)
        
        # الرمز البريدي
        location_layout.addWidget(QLabel("الرمز البريدي:"), 3, 0)
        self.postal_code = QLineEdit()
        self.postal_code.setPlaceholderText("12345")
        location_layout.addWidget(self.postal_code, 3, 1)
        
        # صندوق البريد
        location_layout.addWidget(QLabel("صندوق البريد:"), 3, 2)
        self.po_box = QLineEdit()
        self.po_box.setPlaceholderText("P.O. Box 123")
        location_layout.addWidget(self.po_box, 3, 3)
        
        location_group.setLayout(location_layout)
        
        # مجموعة معلومات الاتصال
        contact_group = QGroupBox("📞 معلومات الاتصال")
        contact_layout = QGridLayout()
        
        # الهاتف الرئيسي
        contact_layout.addWidget(QLabel("الهاتف الرئيسي:"), 0, 0)
        self.main_phone = QLineEdit()
        self.main_phone.setPlaceholderText("+967-1-123456")
        contact_layout.addWidget(self.main_phone, 0, 1)
        
        # الهاتف الفرعي
        contact_layout.addWidget(QLabel("الهاتف الفرعي:"), 0, 2)
        self.secondary_phone = QLineEdit()
        self.secondary_phone.setPlaceholderText("+967-1-654321")
        contact_layout.addWidget(self.secondary_phone, 0, 3)
        
        # الفاكس
        contact_layout.addWidget(QLabel("الفاكس:"), 1, 0)
        self.fax = QLineEdit()
        self.fax.setPlaceholderText("+967-1-789012")
        contact_layout.addWidget(self.fax, 1, 1)
        
        # الجوال
        contact_layout.addWidget(QLabel("الجوال:"), 1, 2)
        self.mobile = QLineEdit()
        self.mobile.setPlaceholderText("+967-77-1234567")
        contact_layout.addWidget(self.mobile, 1, 3)
        
        # البريد الإلكتروني
        contact_layout.addWidget(QLabel("البريد الإلكتروني:"), 2, 0)
        self.email = QLineEdit()
        self.email.setPlaceholderText("<EMAIL>")
        contact_layout.addWidget(self.email, 2, 1)
        
        # الموقع الإلكتروني
        contact_layout.addWidget(QLabel("الموقع الإلكتروني:"), 2, 2)
        self.website = QLineEdit()
        self.website.setPlaceholderText("www.company.com/branch")
        contact_layout.addWidget(self.website, 2, 3)
        
        contact_group.setLayout(contact_layout)
        
        # مجموعة الإحداثيات
        coordinates_group = QGroupBox("🌐 الإحداثيات الجغرافية")
        coordinates_layout = QGridLayout()
        
        # خط الطول
        coordinates_layout.addWidget(QLabel("خط الطول:"), 0, 0)
        self.longitude = QDoubleSpinBox()
        self.longitude.setRange(-180.0, 180.0)
        self.longitude.setDecimals(6)
        self.longitude.setValue(44.206667)  # صنعاء
        coordinates_layout.addWidget(self.longitude, 0, 1)
        
        # خط العرض
        coordinates_layout.addWidget(QLabel("خط العرض:"), 0, 2)
        self.latitude = QDoubleSpinBox()
        self.latitude.setRange(-90.0, 90.0)
        self.latitude.setDecimals(6)
        self.latitude.setValue(15.369445)  # صنعاء
        coordinates_layout.addWidget(self.latitude, 0, 3)
        
        # زر تحديد الموقع
        locate_btn = QPushButton("📍 تحديد الموقع على الخريطة")
        locate_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        locate_btn.clicked.connect(self.locate_on_map)
        coordinates_layout.addWidget(locate_btn, 1, 0, 1, 4)
        
        coordinates_group.setLayout(coordinates_layout)
        
        layout.addWidget(location_group)
        layout.addWidget(contact_group)
        layout.addWidget(coordinates_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab

    def create_financial_tab(self):
        """إنشاء تبويب الإعدادات المالية"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة العملات والأسعار
        currency_group = QGroupBox("💱 العملات وأسعار الصرف")
        currency_layout = QGridLayout()

        # العملة الأساسية
        currency_layout.addWidget(QLabel("العملة الأساسية:"), 0, 0)
        self.base_currency = QComboBox()
        currency_layout.addWidget(self.base_currency, 0, 1)

        # العملة الثانوية
        currency_layout.addWidget(QLabel("العملة الثانوية:"), 0, 2)
        self.secondary_currency = QComboBox()
        currency_layout.addWidget(self.secondary_currency, 0, 3)

        # سعر الصرف
        currency_layout.addWidget(QLabel("سعر الصرف:"), 1, 0)
        self.exchange_rate = QDoubleSpinBox()
        self.exchange_rate.setRange(0.01, 10000.0)
        self.exchange_rate.setDecimals(4)
        self.exchange_rate.setValue(1.0)
        currency_layout.addWidget(self.exchange_rate, 1, 1)

        # تحديث تلقائي للأسعار
        self.auto_update_rates = QCheckBox("تحديث تلقائي لأسعار الصرف")
        currency_layout.addWidget(self.auto_update_rates, 1, 2, 1, 2)

        currency_group.setLayout(currency_layout)

        # مجموعة الحدود المالية
        limits_group = QGroupBox("💰 الحدود والضوابط المالية")
        limits_layout = QGridLayout()

        # حد المبيعات اليومي
        limits_layout.addWidget(QLabel("حد المبيعات اليومي:"), 0, 0)
        self.daily_sales_limit = QDoubleSpinBox()
        self.daily_sales_limit.setRange(0, 10000000)
        self.daily_sales_limit.setValue(100000)
        self.daily_sales_limit.setSuffix(" ريال")
        limits_layout.addWidget(self.daily_sales_limit, 0, 1)

        # حد المشتريات اليومي
        limits_layout.addWidget(QLabel("حد المشتريات اليومي:"), 0, 2)
        self.daily_purchase_limit = QDoubleSpinBox()
        self.daily_purchase_limit.setRange(0, 10000000)
        self.daily_purchase_limit.setValue(50000)
        self.daily_purchase_limit.setSuffix(" ريال")
        limits_layout.addWidget(self.daily_purchase_limit, 0, 3)

        # حد الصندوق
        limits_layout.addWidget(QLabel("حد الصندوق:"), 1, 0)
        self.cash_limit = QDoubleSpinBox()
        self.cash_limit.setRange(0, 1000000)
        self.cash_limit.setValue(20000)
        self.cash_limit.setSuffix(" ريال")
        limits_layout.addWidget(self.cash_limit, 1, 1)

        # حد الائتمان
        limits_layout.addWidget(QLabel("حد الائتمان:"), 1, 2)
        self.credit_limit = QDoubleSpinBox()
        self.credit_limit.setRange(0, 5000000)
        self.credit_limit.setValue(200000)
        self.credit_limit.setSuffix(" ريال")
        limits_layout.addWidget(self.credit_limit, 1, 3)

        limits_group.setLayout(limits_layout)

        # مجموعة الضرائب والرسوم
        tax_group = QGroupBox("📊 الضرائب والرسوم")
        tax_layout = QGridLayout()

        # معدل ضريبة المبيعات
        tax_layout.addWidget(QLabel("ضريبة المبيعات (%):"), 0, 0)
        self.sales_tax_rate = QDoubleSpinBox()
        self.sales_tax_rate.setRange(0, 50)
        self.sales_tax_rate.setDecimals(2)
        self.sales_tax_rate.setValue(15.0)
        self.sales_tax_rate.setSuffix("%")
        tax_layout.addWidget(self.sales_tax_rate, 0, 1)

        # رقم التسجيل الضريبي
        tax_layout.addWidget(QLabel("رقم التسجيل الضريبي:"), 0, 2)
        self.tax_registration = QLineEdit()
        self.tax_registration.setPlaceholderText("*********")
        tax_layout.addWidget(self.tax_registration, 0, 3)

        # خاضع للضريبة
        self.subject_to_tax = QCheckBox("خاضع للضريبة")
        self.subject_to_tax.setChecked(True)
        tax_layout.addWidget(self.subject_to_tax, 1, 0, 1, 2)

        # يطبق ضريبة القيمة المضافة
        self.applies_vat = QCheckBox("يطبق ضريبة القيمة المضافة")
        tax_layout.addWidget(self.applies_vat, 1, 2, 1, 2)

        tax_group.setLayout(tax_layout)

        layout.addWidget(currency_group)
        layout.addWidget(limits_group)
        layout.addWidget(tax_group)
        layout.addStretch()

        tab.setLayout(layout)
        return tab

    def create_working_hours_tab(self):
        """إنشاء تبويب أوقات العمل"""
        from .branch_definition_functions import create_working_hours_tab_function
        return create_working_hours_tab_function(self)

    def create_services_tab(self):
        """إنشاء تبويب الخدمات والمرافق"""
        from .branch_definition_functions import create_services_tab_function
        return create_services_tab_function(self)

    def create_security_tab(self):
        """إنشاء تبويب الأمان والصلاحيات"""
        from .branch_definition_functions import create_security_tab_function
        return create_security_tab_function(self)

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        from .branch_definition_functions import create_control_buttons_function
        return create_control_buttons_function(self)

    def load_data(self):
        """تحميل البيانات المحفوظة"""
        from .branch_definition_data import load_data_function
        load_data_function(self)

    def load_locations(self):
        """تحميل قائمة المواقع الجغرافية"""
        from .branch_definition_data import load_locations_function
        load_locations_function(self)

    def load_currencies(self):
        """تحميل قائمة العملات"""
        from .branch_definition_data import load_currencies_function
        load_currencies_function(self)

    def load_managers(self):
        """تحميل قائمة المديرين والموظفين"""
        from .branch_definition_data import load_managers_function
        load_managers_function(self)

    def load_sample_branch_data(self):
        """تحميل البيانات النموذجية للفرع"""
        from .branch_definition_data import load_sample_branch_data_function
        load_sample_branch_data_function(self)

    def locate_on_map(self):
        """تحديد الموقع على الخريطة"""
        from .branch_definition_data import locate_on_map_function
        locate_on_map_function(self)

    def preview_branch_definition(self):
        """معاينة تعريف الفرع"""
        from .branch_definition_data import preview_branch_definition_function
        preview_branch_definition_function(self)

    def show_help(self):
        """عرض المساعدة"""
        from .branch_definition_data import show_help_function
        show_help_function(self)

    def save_branch_definition(self):
        """حفظ تعريف الفرع"""
        from .branch_definition_data import save_branch_definition_function
        save_branch_definition_function(self)


def main():
    """اختبار الشاشة"""
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass

    dialog = BranchDefinitionDialog(MockDBManager())
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
