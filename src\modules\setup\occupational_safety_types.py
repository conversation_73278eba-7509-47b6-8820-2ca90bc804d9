#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أنواع السلامة المهنية
Occupational Safety Types
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QMessageBox, 
                             QFrame, QSplitter, QTextEdit, QTabWidget, QWidget,
                             QTreeWidget, QTreeWidgetItem, QListWidget, QListWidgetItem,
                             QProgressBar, QSlider, QDateEdit, QRadioButton, QButtonGroup,
                             QApplication)
from PyQt5.QtCore import Qt, pyqtSignal, <PERSON>D<PERSON>, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon, QPixmap

class OccupationalSafetyTypesDialog(QDialog):
    """حوار أنواع السلامة المهنية"""
    
    def __init__(self, db_manager=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.safety_types_data = []
        self.safety_measures_data = []
        self.incidents_data = []
        self.training_programs_data = []
        self.setup_ui()
        self.load_sample_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("أنواع السلامة المهنية - نظام إدارة السلامة والصحة المهنية")
        self.setGeometry(100, 100, 1300, 850)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("🦺 أنواع السلامة المهنية - نظام إدارة السلامة والصحة المهنية")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات النظام
        self.tabs = QTabWidget()
        
        # تبويب أنواع السلامة
        safety_types_tab = self.create_safety_types_tab()
        self.tabs.addTab(safety_types_tab, "🦺 أنواع السلامة")
        
        # تبويب إجراءات السلامة
        safety_measures_tab = self.create_safety_measures_tab()
        self.tabs.addTab(safety_measures_tab, "🛡️ إجراءات السلامة")
        
        # تبويب الحوادث والمخاطر
        incidents_tab = self.create_incidents_tab()
        self.tabs.addTab(incidents_tab, "⚠️ الحوادث والمخاطر")
        
        # تبويب برامج التدريب
        training_tab = self.create_training_tab()
        self.tabs.addTab(training_tab, "📚 برامج التدريب")
        
        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        self.tabs.addTab(reports_tab, "📊 التقارير")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_safety_types_tab(self):
        """إنشاء تبويب أنواع السلامة"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إدارة أنواع السلامة
        types_group = QGroupBox("🦺 إدارة أنواع السلامة المهنية")
        types_layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        add_type_btn = QPushButton("➕ إضافة نوع سلامة")
        add_type_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_type_btn.clicked.connect(self.add_safety_type)
        
        edit_type_btn = QPushButton("✏️ تعديل نوع")
        edit_type_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_type_btn.clicked.connect(self.edit_safety_type)
        
        delete_type_btn = QPushButton("🗑️ حذف نوع")
        delete_type_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_type_btn.clicked.connect(self.delete_safety_type)
        
        toolbar_layout.addWidget(add_type_btn)
        toolbar_layout.addWidget(edit_type_btn)
        toolbar_layout.addWidget(delete_type_btn)
        toolbar_layout.addStretch()
        
        # جدول أنواع السلامة
        self.safety_types_table = QTableWidget()
        self.safety_types_table.setColumnCount(7)
        self.safety_types_table.setHorizontalHeaderLabels([
            "رمز النوع", "اسم النوع", "التصنيف", "مستوى الخطورة", "الوصف", "متطلبات الامتثال", "الحالة"
        ])
        
        # تنسيق الجدول
        self.safety_types_table.horizontalHeader().setStretchLastSection(True)
        self.safety_types_table.setAlternatingRowColors(True)
        self.safety_types_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.safety_types_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 12px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)
        
        types_layout.addLayout(toolbar_layout)
        types_layout.addWidget(self.safety_types_table)
        types_group.setLayout(types_layout)
        
        # مجموعة تفاصيل النوع المحدد
        details_group = QGroupBox("📋 تفاصيل النوع المحدد")
        details_layout = QGridLayout()
        
        # رمز النوع
        details_layout.addWidget(QLabel("رمز النوع:"), 0, 0)
        self.type_code_input = QLineEdit()
        self.type_code_input.setPlaceholderText("مثال: SAFE001")
        details_layout.addWidget(self.type_code_input, 0, 1)
        
        # اسم النوع
        details_layout.addWidget(QLabel("اسم النوع:"), 0, 2)
        self.type_name_input = QLineEdit()
        self.type_name_input.setPlaceholderText("مثال: السلامة من الحرائق")
        details_layout.addWidget(self.type_name_input, 0, 3)
        
        # التصنيف
        details_layout.addWidget(QLabel("التصنيف:"), 1, 0)
        self.classification_input = QComboBox()
        self.classification_input.addItems([
            "سلامة فيزيائية", "سلامة كيميائية", "سلامة بيولوجية", 
            "سلامة إشعاعية", "سلامة كهربائية", "سلامة ميكانيكية",
            "سلامة نفسية", "سلامة بيئية"
        ])
        details_layout.addWidget(self.classification_input, 1, 1)
        
        # مستوى الخطورة
        details_layout.addWidget(QLabel("مستوى الخطورة:"), 1, 2)
        self.risk_level_input = QComboBox()
        self.risk_level_input.addItems(["منخفض", "متوسط", "عالي", "حرج"])
        details_layout.addWidget(self.risk_level_input, 1, 3)
        
        # الوصف
        details_layout.addWidget(QLabel("الوصف:"), 2, 0)
        self.type_description_input = QTextEdit()
        self.type_description_input.setMaximumHeight(80)
        self.type_description_input.setPlaceholderText("وصف تفصيلي لنوع السلامة المهنية...")
        details_layout.addWidget(self.type_description_input, 2, 1, 1, 3)
        
        details_group.setLayout(details_layout)
        
        layout.addWidget(types_group)
        layout.addWidget(details_group)
        
        tab.setLayout(layout)
        return tab
        
    def create_safety_measures_tab(self):
        """إنشاء تبويب إجراءات السلامة"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إدارة إجراءات السلامة
        measures_group = QGroupBox("🛡️ إدارة إجراءات السلامة")
        measures_layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        add_measure_btn = QPushButton("➕ إضافة إجراء")
        add_measure_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_measure_btn.clicked.connect(self.add_safety_measure)
        
        edit_measure_btn = QPushButton("✏️ تعديل إجراء")
        edit_measure_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_measure_btn.clicked.connect(self.edit_safety_measure)
        
        delete_measure_btn = QPushButton("🗑️ حذف إجراء")
        delete_measure_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_measure_btn.clicked.connect(self.delete_safety_measure)
        
        toolbar_layout.addWidget(add_measure_btn)
        toolbar_layout.addWidget(edit_measure_btn)
        toolbar_layout.addWidget(delete_measure_btn)
        toolbar_layout.addStretch()
        
        # جدول إجراءات السلامة
        self.safety_measures_table = QTableWidget()
        self.safety_measures_table.setColumnCount(8)
        self.safety_measures_table.setHorizontalHeaderLabels([
            "رمز الإجراء", "اسم الإجراء", "نوع السلامة", "الأولوية", 
            "التكلفة", "مدة التنفيذ", "المسؤول", "الحالة"
        ])
        
        # تنسيق الجدول
        self.safety_measures_table.horizontalHeader().setStretchLastSection(True)
        self.safety_measures_table.setAlternatingRowColors(True)
        self.safety_measures_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.safety_measures_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 12px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)
        
        measures_layout.addLayout(toolbar_layout)
        measures_layout.addWidget(self.safety_measures_table)
        measures_group.setLayout(measures_layout)
        
        layout.addWidget(measures_group)
        
        tab.setLayout(layout)
        return tab

    def create_incidents_tab(self):
        """إنشاء تبويب الحوادث والمخاطر"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة إدارة الحوادث
        incidents_group = QGroupBox("⚠️ إدارة الحوادث والمخاطر")
        incidents_layout = QVBoxLayout()

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        add_incident_btn = QPushButton("➕ تسجيل حادث")
        add_incident_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        add_incident_btn.clicked.connect(self.add_incident)

        edit_incident_btn = QPushButton("✏️ تعديل حادث")
        edit_incident_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_incident_btn.clicked.connect(self.edit_incident)

        analyze_risks_btn = QPushButton("📊 تحليل المخاطر")
        analyze_risks_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        analyze_risks_btn.clicked.connect(self.analyze_risks)

        toolbar_layout.addWidget(add_incident_btn)
        toolbar_layout.addWidget(edit_incident_btn)
        toolbar_layout.addWidget(analyze_risks_btn)
        toolbar_layout.addStretch()

        # جدول الحوادث
        self.incidents_table = QTableWidget()
        self.incidents_table.setColumnCount(8)
        self.incidents_table.setHorizontalHeaderLabels([
            "رقم الحادث", "تاريخ الحادث", "نوع الحادث", "الموقع",
            "مستوى الخطورة", "الإصابات", "الأضرار", "الحالة"
        ])

        # تنسيق الجدول
        self.incidents_table.horizontalHeader().setStretchLastSection(True)
        self.incidents_table.setAlternatingRowColors(True)
        self.incidents_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.incidents_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 12px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)

        incidents_layout.addLayout(toolbar_layout)
        incidents_layout.addWidget(self.incidents_table)
        incidents_group.setLayout(incidents_layout)

        layout.addWidget(incidents_group)

        tab.setLayout(layout)
        return tab

    def create_training_tab(self):
        """إنشاء تبويب برامج التدريب"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة إدارة برامج التدريب
        training_group = QGroupBox("📚 إدارة برامج التدريب")
        training_layout = QVBoxLayout()

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        add_program_btn = QPushButton("➕ إضافة برنامج")
        add_program_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        add_program_btn.clicked.connect(self.add_training_program)

        edit_program_btn = QPushButton("✏️ تعديل برنامج")
        edit_program_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_program_btn.clicked.connect(self.edit_training_program)

        schedule_training_btn = QPushButton("📅 جدولة التدريب")
        schedule_training_btn.setStyleSheet("""
            QPushButton {
                background-color: #16a085;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138d75;
            }
        """)
        schedule_training_btn.clicked.connect(self.schedule_training)

        toolbar_layout.addWidget(add_program_btn)
        toolbar_layout.addWidget(edit_program_btn)
        toolbar_layout.addWidget(schedule_training_btn)
        toolbar_layout.addStretch()

        # جدول برامج التدريب
        self.training_programs_table = QTableWidget()
        self.training_programs_table.setColumnCount(8)
        self.training_programs_table.setHorizontalHeaderLabels([
            "رمز البرنامج", "اسم البرنامج", "نوع السلامة", "المدة",
            "عدد المتدربين", "التكلفة", "المدرب", "الحالة"
        ])

        # تنسيق الجدول
        self.training_programs_table.horizontalHeader().setStretchLastSection(True)
        self.training_programs_table.setAlternatingRowColors(True)
        self.training_programs_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.training_programs_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 12px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)

        training_layout.addLayout(toolbar_layout)
        training_layout.addWidget(self.training_programs_table)
        training_group.setLayout(training_layout)

        layout.addWidget(training_group)

        tab.setLayout(layout)
        return tab

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        from .occupational_safety_functions import create_reports_tab_function
        return create_reports_tab_function(self)

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        from .occupational_safety_functions import create_control_buttons_function
        return create_control_buttons_function(self)

    # ربط الوظائف من الملفات المنفصلة
    def load_sample_data(self):
        """تحميل البيانات النموذجية"""
        from .occupational_safety_functions import load_sample_data_function
        load_sample_data_function(self)

    def update_safety_types_table(self):
        """تحديث جدول أنواع السلامة"""
        from .occupational_safety_functions import update_safety_types_table_function
        update_safety_types_table_function(self)

    def update_safety_measures_table(self):
        """تحديث جدول إجراءات السلامة"""
        from .occupational_safety_functions import update_safety_measures_table_function
        update_safety_measures_table_function(self)

    def update_incidents_table(self):
        """تحديث جدول الحوادث"""
        from .occupational_safety_functions import update_incidents_table_function
        update_incidents_table_function(self)

    def update_training_programs_table(self):
        """تحديث جدول برامج التدريب"""
        from .occupational_safety_functions import update_training_programs_table_function
        update_training_programs_table_function(self)

    # وظائف أنواع السلامة
    def add_safety_type(self):
        """إضافة نوع سلامة"""
        from .occupational_safety_operations import add_safety_type_function
        add_safety_type_function(self)

    def edit_safety_type(self):
        """تعديل نوع سلامة"""
        from .occupational_safety_operations import edit_safety_type_function
        edit_safety_type_function(self)

    def delete_safety_type(self):
        """حذف نوع سلامة"""
        from .occupational_safety_operations import delete_safety_type_function
        delete_safety_type_function(self)

    # وظائف إجراءات السلامة
    def add_safety_measure(self):
        """إضافة إجراء سلامة"""
        from .occupational_safety_operations import add_safety_measure_function
        add_safety_measure_function(self)

    def edit_safety_measure(self):
        """تعديل إجراء سلامة"""
        from .occupational_safety_operations import edit_safety_measure_function
        edit_safety_measure_function(self)

    def delete_safety_measure(self):
        """حذف إجراء سلامة"""
        from .occupational_safety_operations import delete_safety_measure_function
        delete_safety_measure_function(self)

    # وظائف الحوادث
    def add_incident(self):
        """تسجيل حادث"""
        from .occupational_safety_operations import add_incident_function
        add_incident_function(self)

    def edit_incident(self):
        """تعديل حادث"""
        from .occupational_safety_operations import edit_incident_function
        edit_incident_function(self)

    def analyze_risks(self):
        """تحليل المخاطر"""
        from .occupational_safety_operations import analyze_risks_function
        analyze_risks_function(self)

    # وظائف برامج التدريب
    def add_training_program(self):
        """إضافة برنامج تدريب"""
        from .occupational_safety_operations import add_training_program_function
        add_training_program_function(self)

    def edit_training_program(self):
        """تعديل برنامج تدريب"""
        from .occupational_safety_operations import edit_training_program_function
        edit_training_program_function(self)

    def schedule_training(self):
        """جدولة التدريب"""
        from .occupational_safety_operations import schedule_training_function
        schedule_training_function(self)

    # وظائف التقارير
    def generate_safety_types_report(self):
        """تقرير أنواع السلامة"""
        from .occupational_safety_operations import generate_safety_types_report_function
        generate_safety_types_report_function(self)

    def generate_incidents_report(self):
        """تقرير الحوادث"""
        from .occupational_safety_operations import generate_incidents_report_function
        generate_incidents_report_function(self)

    def generate_training_report(self):
        """تقرير التدريب"""
        from .occupational_safety_operations import generate_training_report_function
        generate_training_report_function(self)

    def generate_compliance_report(self):
        """تقرير الامتثال"""
        from .occupational_safety_operations import generate_compliance_report_function
        generate_compliance_report_function(self)

    def export_report(self):
        """تصدير التقرير"""
        from .occupational_safety_operations import export_report_function
        export_report_function(self)

    def print_report(self):
        """طباعة التقرير"""
        from .occupational_safety_operations import print_report_function
        print_report_function(self)

    def clear_report(self):
        """مسح التقرير"""
        from .occupational_safety_operations import clear_report_function
        clear_report_function(self)

    def save_changes(self):
        """حفظ التغييرات"""
        from .occupational_safety_operations import save_changes_function
        save_changes_function(self)

    def show_help(self):
        """عرض المساعدة"""
        from .occupational_safety_operations import show_help_function
        show_help_function(self)


def main():
    """اختبار الشاشة"""
    import sys

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass

    dialog = OccupationalSafetyTypesDialog(MockDBManager())
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
