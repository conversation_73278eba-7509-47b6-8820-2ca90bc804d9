#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف إضافية لشاشة شجرة الفروع
Additional Functions for Branch Tree
"""

from PyQt5.QtWidgets import QMessageBox, QTableWidgetItem, QTreeWidgetItem
from PyQt5.QtCore import Qt
from datetime import datetime

def create_control_buttons_function(self):
    """إنشاء أزرار التحكم"""
    from PyQt5.QtWidgets import QHBoxLayout, QPushButton
    
    layout = QHBoxLayout()
    
    # زر الحفظ
    save_btn = QPushButton("💾 حفظ جميع التغييرات")
    save_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    save_btn.clicked.connect(self.save_all_changes)
    
    # زر الإلغاء
    cancel_btn = QPushButton("❌ إلغاء")
    cancel_btn.setStyleSheet("""
        QPushButton {
            background-color: #e74c3c;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #c0392b;
        }
    """)
    cancel_btn.clicked.connect(self.reject)
    
    # زر المساعدة
    help_btn = QPushButton("❓ المساعدة")
    help_btn.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    help_btn.clicked.connect(self.show_help)
    
    layout.addWidget(help_btn)
    layout.addStretch()
    layout.addWidget(save_btn)
    layout.addWidget(cancel_btn)
    
    return layout

def load_data_function(self):
    """تحميل البيانات المحفوظة"""
    try:
        # تحميل المحافظات والمدن
        self.load_locations()
        
        # تحميل المديرين والموظفين
        self.load_managers()
        
        # تحميل البيانات النموذجية
        self.load_sample_data()
        
        # بناء الشجرة الهرمية
        self.build_branch_tree()
        
        # تحديث الإحصائيات
        self.update_tree_statistics()
        
    except Exception as e:
        print(f"خطأ في تحميل البيانات: {e}")

def load_locations_function(self):
    """تحميل قائمة المحافظات والمدن"""
    # المحافظات
    governorates = [
        "محافظة صنعاء", "محافظة عدن", "محافظة تعز", "محافظة الحديدة",
        "محافظة إب", "محافظة ذمار", "محافظة حضرموت", "محافظة المهرة",
        "محافظة شبوة", "محافظة أبين", "محافظة لحج", "محافظة الضالع"
    ]
    
    self.main_branch_governorate.clear()
    for gov in governorates:
        self.main_branch_governorate.addItem(gov)
    
    # المدن (مبسطة)
    cities = [
        "مدينة صنعاء", "مدينة عدن", "مدينة تعز", "مدينة الحديدة",
        "مدينة إب", "مدينة ذمار", "مدينة المكلا", "مدينة الغيضة"
    ]
    
    self.main_branch_city.clear()
    for city in cities:
        self.main_branch_city.addItem(city)
    
    # المناطق
    districts = [
        "مديرية الثورة", "مديرية الصافية", "مديرية كريتر", "مديرية المعلا",
        "مديرية صالة", "مديرية الحوك", "مديرية الميناء", "مديرية التحرير"
    ]
    
    self.sub_branch_district.clear()
    for district in districts:
        self.sub_branch_district.addItem(district)

def load_managers_function(self):
    """تحميل قائمة المديرين والموظفين"""
    managers = [
        "أحمد محمد علي", "فاطمة أحمد سالم", "محمد عبدالله حسن",
        "عائشة علي محمد", "عبدالرحمن سعيد أحمد", "زينب محمد عبدالله",
        "سعيد أحمد علي", "مريم عبدالله سالم", "علي محمد حسن"
    ]
    
    # تحديث جميع قوائم المديرين
    for combo in [self.main_branch_manager, self.sub_branch_manager, self.office_supervisor]:
        combo.clear()
        for manager in managers:
            combo.addItem(manager)

def load_sample_data_function(self):
    """تحميل البيانات النموذجية"""
    # الفروع الرئيسية النموذجية
    main_branches = [
        ("فرع صنعاء الرئيسي", "Sana'a Main Branch", "SAN-001", "محافظة صنعاء", "مدينة صنعاء", 
         "شارع الزبيري", "أحمد محمد علي", "+967-1-123456", True, True),
        ("فرع عدن الرئيسي", "Aden Main Branch", "ADE-001", "محافظة عدن", "مدينة عدن",
         "شارع المعلا", "فاطمة أحمد سالم", "+967-2-234567", True, False),
        ("فرع تعز الرئيسي", "Taiz Main Branch", "TAI-001", "محافظة تعز", "مدينة تعز",
         "شارع جمال عبدالناصر", "محمد عبدالله حسن", "+967-4-345678", True, False),
        ("فرع الحديدة الرئيسي", "Hodeidah Main Branch", "HOD-001", "محافظة الحديدة", "مدينة الحديدة",
         "شارع الكورنيش", "عائشة علي محمد", "+967-3-456789", True, False)
    ]
    
    self.main_branches_table.setRowCount(len(main_branches))
    for row, (name, name_en, code, gov, city, address, manager, phone, active, is_main) in enumerate(main_branches):
        self.main_branches_table.setItem(row, 0, QTableWidgetItem(name))
        self.main_branches_table.setItem(row, 1, QTableWidgetItem(code))
        self.main_branches_table.setItem(row, 2, QTableWidgetItem(gov))
        self.main_branches_table.setItem(row, 3, QTableWidgetItem(city))
        self.main_branches_table.setItem(row, 4, QTableWidgetItem(manager))
        self.main_branches_table.setItem(row, 5, QTableWidgetItem(phone))
        self.main_branches_table.setItem(row, 6, QTableWidgetItem("0"))  # عدد الفروع الفرعية
        
        status = "نشط"
        if is_main:
            status += " (مقر رئيسي)"
        elif not active:
            status = "غير نشط"
        self.main_branches_table.setItem(row, 7, QTableWidgetItem(status))
    
    # تحديث قائمة الفروع الرئيسية
    self.update_parent_branches()
    
    # الفروع الفرعية النموذجية
    sub_branches = [
        ("فرع الثورة", "SAN-002", "فرع صنعاء الرئيسي", "مديرية الثورة", 
         "شارع الثورة", "عبدالرحمن سعيد أحمد", "+967-1-111222", True),
        ("فرع الصافية", "SAN-003", "فرع صنعاء الرئيسي", "مديرية الصافية",
         "شارع الصافية", "زينب محمد عبدالله", "+967-1-333444", True),
        ("فرع كريتر", "ADE-002", "فرع عدن الرئيسي", "مديرية كريتر",
         "شارع كريتر", "سعيد أحمد علي", "+967-2-555666", True),
        ("فرع صالة", "TAI-002", "فرع تعز الرئيسي", "مديرية صالة",
         "شارع صالة", "مريم عبدالله سالم", "+967-4-777888", True)
    ]
    
    self.sub_branches_table.setRowCount(len(sub_branches))
    for row, (name, code, parent, district, address, manager, phone, active) in enumerate(sub_branches):
        self.sub_branches_table.setItem(row, 0, QTableWidgetItem(name))
        self.sub_branches_table.setItem(row, 1, QTableWidgetItem(code))
        self.sub_branches_table.setItem(row, 2, QTableWidgetItem(parent))
        self.sub_branches_table.setItem(row, 3, QTableWidgetItem(district))
        self.sub_branches_table.setItem(row, 4, QTableWidgetItem(manager))
        self.sub_branches_table.setItem(row, 5, QTableWidgetItem(phone))
        
        status = "نشط" if active else "غير نشط"
        self.sub_branches_table.setItem(row, 6, QTableWidgetItem(status))
    
    # تحديث قائمة الفروع للمكاتب
    self.update_branch_offices()
    
    # المكاتب النموذجية
    offices = [
        ("مكتب خدمة العملاء", "SAN-001-CS", "خدمة العملاء", "فرع صنعاء الرئيسي",
         "علي محمد حسن", "101", "استقبال العملاء وحل المشاكل", True),
        ("مكتب المبيعات", "SAN-001-SA", "المبيعات", "فرع صنعاء الرئيسي",
         "أحمد محمد علي", "102", "إدارة عمليات البيع", True),
        ("مكتب المحاسبة", "SAN-001-AC", "المحاسبة", "فرع صنعاء الرئيسي",
         "فاطمة أحمد سالم", "103", "إدارة الحسابات والمالية", True),
        ("مكتب خدمة العملاء", "ADE-001-CS", "خدمة العملاء", "فرع عدن الرئيسي",
         "محمد عبدالله حسن", "201", "استقبال العملاء وحل المشاكل", True)
    ]
    
    self.offices_table.setRowCount(len(offices))
    for row, (name, code, type_name, branch, supervisor, extension, desc, active) in enumerate(offices):
        self.offices_table.setItem(row, 0, QTableWidgetItem(name))
        self.offices_table.setItem(row, 1, QTableWidgetItem(code))
        self.offices_table.setItem(row, 2, QTableWidgetItem(type_name))
        self.offices_table.setItem(row, 3, QTableWidgetItem(branch))
        self.offices_table.setItem(row, 4, QTableWidgetItem(supervisor))
        self.offices_table.setItem(row, 5, QTableWidgetItem(extension))
        
        status = "نشط" if active else "غير نشط"
        self.offices_table.setItem(row, 6, QTableWidgetItem(status))
    
    # تحديث عدد الفروع الفرعية
    self.update_sub_branch_counts()

def update_parent_branches_function(self):
    """تحديث قائمة الفروع الرئيسية"""
    self.sub_branch_parent.clear()
    for row in range(self.main_branches_table.rowCount()):
        branch_name = self.main_branches_table.item(row, 0).text()
        self.sub_branch_parent.addItem(branch_name)

def update_branch_offices_function(self):
    """تحديث قائمة الفروع للمكاتب"""
    self.office_branch.clear()
    
    # إضافة الفروع الرئيسية
    for row in range(self.main_branches_table.rowCount()):
        branch_name = self.main_branches_table.item(row, 0).text()
        self.office_branch.addItem(branch_name)
    
    # إضافة الفروع الفرعية
    for row in range(self.sub_branches_table.rowCount()):
        branch_name = self.sub_branches_table.item(row, 0).text()
        self.office_branch.addItem(branch_name)

def update_sub_branch_counts_function(self):
    """تحديث عدد الفروع الفرعية لكل فرع رئيسي"""
    for main_row in range(self.main_branches_table.rowCount()):
        main_branch_name = self.main_branches_table.item(main_row, 0).text()
        count = 0
        
        for sub_row in range(self.sub_branches_table.rowCount()):
            parent_branch = self.sub_branches_table.item(sub_row, 2).text()
            if parent_branch == main_branch_name:
                count += 1
        
        self.main_branches_table.setItem(main_row, 6, QTableWidgetItem(str(count)))

def build_branch_tree_function(self):
    """بناء الشجرة الهرمية للفروع"""
    self.branch_tree.clear()
    
    # إنشاء عقد الفروع الرئيسية
    for main_row in range(self.main_branches_table.rowCount()):
        main_name = self.main_branches_table.item(main_row, 0).text()
        main_code = self.main_branches_table.item(main_row, 1).text()
        main_manager = self.main_branches_table.item(main_row, 4).text()
        main_status = self.main_branches_table.item(main_row, 7).text()
        
        main_item = QTreeWidgetItem([main_name, "فرع رئيسي", main_code, main_manager, main_status])
        main_item.setExpanded(True)
        
        # إضافة الفروع الفرعية
        for sub_row in range(self.sub_branches_table.rowCount()):
            parent_branch = self.sub_branches_table.item(sub_row, 2).text()
            if parent_branch == main_name:
                sub_name = self.sub_branches_table.item(sub_row, 0).text()
                sub_code = self.sub_branches_table.item(sub_row, 1).text()
                sub_manager = self.sub_branches_table.item(sub_row, 4).text()
                sub_status = self.sub_branches_table.item(sub_row, 6).text()
                
                sub_item = QTreeWidgetItem([sub_name, "فرع فرعي", sub_code, sub_manager, sub_status])
                sub_item.setExpanded(True)
                
                # إضافة المكاتب التابعة للفرع الفرعي
                for office_row in range(self.offices_table.rowCount()):
                    office_branch = self.offices_table.item(office_row, 3).text()
                    if office_branch == sub_name:
                        office_name = self.offices_table.item(office_row, 0).text()
                        office_code = self.offices_table.item(office_row, 1).text()
                        office_supervisor = self.offices_table.item(office_row, 4).text()
                        office_status = self.offices_table.item(office_row, 6).text()
                        
                        office_item = QTreeWidgetItem([office_name, "مكتب", office_code, office_supervisor, office_status])
                        sub_item.addChild(office_item)
                
                main_item.addChild(sub_item)
        
        # إضافة المكاتب التابعة مباشرة للفرع الرئيسي
        for office_row in range(self.offices_table.rowCount()):
            office_branch = self.offices_table.item(office_row, 3).text()
            if office_branch == main_name:
                office_name = self.offices_table.item(office_row, 0).text()
                office_code = self.offices_table.item(office_row, 1).text()
                office_supervisor = self.offices_table.item(office_row, 4).text()
                office_status = self.offices_table.item(office_row, 6).text()
                
                office_item = QTreeWidgetItem([office_name, "مكتب", office_code, office_supervisor, office_status])
                main_item.addChild(office_item)
        
        self.branch_tree.addTopLevelItem(main_item)

def update_tree_statistics_function(self):
    """تحديث إحصائيات الشجرة"""
    main_branches = self.main_branches_table.rowCount()
    sub_branches = self.sub_branches_table.rowCount()
    offices = self.offices_table.rowCount()
    
    total_branches = main_branches + sub_branches
    
    stats_text = f"📊 إحصائيات الشجرة: {total_branches} فرع ({main_branches} رئيسي، {sub_branches} فرعي)، {offices} مكتب"
    self.tree_info.setText(stats_text)

def refresh_tree_function(self):
    """تحديث الشجرة الهرمية"""
    self.build_branch_tree()
    self.update_tree_statistics()
    QMessageBox.information(self, "تم", "تم تحديث شجرة الفروع بنجاح")

def on_tree_item_clicked_function(self, item, column):
    """معالجة النقر على عنصر في الشجرة"""
    item_name = item.text(0)
    item_type = item.text(1)
    print(f"تم النقر على: {item_name} ({item_type})")

def on_tree_item_double_clicked_function(self, item, column):
    """معالجة النقر المزدوج على عنصر في الشجرة"""
    item_name = item.text(0)
    item_type = item.text(1)
    item_code = item.text(2)
    item_manager = item.text(3)
    item_status = item.text(4)
    
    QMessageBox.information(self, "تفاصيل العنصر", 
                           f"📍 {item_type}: {item_name}\n"
                           f"🔤 الرمز: {item_code}\n"
                           f"👤 المسؤول: {item_manager}\n"
                           f"📊 الحالة: {item_status}")

def print_tree_function(self):
    """طباعة الشجرة"""
    QMessageBox.information(self, "قيد التطوير", 
                           "🖨️ ميزة طباعة الشجرة قيد التطوير\n"
                           "سيتم إضافتها في الإصدارات القادمة")

def generate_org_chart_function(self):
    """إنتاج الهيكل التنظيمي"""
    QMessageBox.information(self, "قيد التطوير", 
                           "📋 ميزة إنتاج الهيكل التنظيمي قيد التطوير\n"
                           "سيتم إضافتها في الإصدارات القادمة")

def generate_branches_report_function(self):
    """إنتاج تقرير الفروع"""
    QMessageBox.information(self, "قيد التطوير", 
                           "📈 ميزة تقرير الفروع قيد التطوير\n"
                           "سيتم إضافتها في الإصدارات القادمة")

def generate_contacts_report_function(self):
    """إنتاج دليل الاتصالات"""
    QMessageBox.information(self, "قيد التطوير", 
                           "📞 ميزة دليل الاتصالات قيد التطوير\n"
                           "سيتم إضافتها في الإصدارات القادمة")

def show_help_function(self):
    """عرض المساعدة"""
    help_text = """
🌳 مساعدة شجرة الفروع

📋 الوظائف المتاحة:

🏢 الفروع الرئيسية:
• إنشاء فروع رئيسية جديدة
• تحديد المحافظة والمدينة
• تعيين مدير الفرع
• تحديد الفرع الرئيسي (المقر)

🏪 الفروع الفرعية:
• إنشاء فروع فرعية تابعة للفروع الرئيسية
• تحديد المنطقة والعنوان
• تعيين مدير الفرع الفرعي

🏬 المكاتب:
• إنشاء مكاتب متخصصة
• تحديد نوع المكتب (خدمة عملاء، مبيعات...)
• تعيين مسؤول المكتب
• تحديد الهاتف الداخلي

🌳 الشجرة الهرمية:
• عرض الهيكل التنظيمي بصرياً
• توسيع وطي الفروع
• النقر المزدوج لعرض التفاصيل

💡 نصائح:
• استخدم رموز واضحة للفروع والمكاتب
• تأكد من تعيين المديرين والمسؤولين
• راجع الشجرة بانتظام للتأكد من صحة الهيكل
• استخدم الألوان للتمييز بين أنواع الفروع
"""
    
    QMessageBox.information(self, "المساعدة", help_text)

def save_all_changes_function(self):
    """حفظ جميع التغييرات"""
    try:
        # جمع جميع البيانات
        data = {
            'main_branches': [],
            'sub_branches': [],
            'offices': []
        }
        
        # جمع بيانات الفروع الرئيسية
        for row in range(self.main_branches_table.rowCount()):
            branch = {
                'name': self.main_branches_table.item(row, 0).text(),
                'code': self.main_branches_table.item(row, 1).text(),
                'governorate': self.main_branches_table.item(row, 2).text(),
                'city': self.main_branches_table.item(row, 3).text(),
                'manager': self.main_branches_table.item(row, 4).text(),
                'phone': self.main_branches_table.item(row, 5).text(),
                'sub_branches_count': int(self.main_branches_table.item(row, 6).text()),
                'status': self.main_branches_table.item(row, 7).text()
            }
            data['main_branches'].append(branch)
        
        # جمع بيانات الفروع الفرعية
        for row in range(self.sub_branches_table.rowCount()):
            branch = {
                'name': self.sub_branches_table.item(row, 0).text(),
                'code': self.sub_branches_table.item(row, 1).text(),
                'parent': self.sub_branches_table.item(row, 2).text(),
                'district': self.sub_branches_table.item(row, 3).text(),
                'manager': self.sub_branches_table.item(row, 4).text(),
                'phone': self.sub_branches_table.item(row, 5).text(),
                'status': self.sub_branches_table.item(row, 6).text()
            }
            data['sub_branches'].append(branch)
        
        # جمع بيانات المكاتب
        for row in range(self.offices_table.rowCount()):
            office = {
                'name': self.offices_table.item(row, 0).text(),
                'code': self.offices_table.item(row, 1).text(),
                'type': self.offices_table.item(row, 2).text(),
                'branch': self.offices_table.item(row, 3).text(),
                'supervisor': self.offices_table.item(row, 4).text(),
                'extension': self.offices_table.item(row, 5).text(),
                'status': self.offices_table.item(row, 6).text()
            }
            data['offices'].append(office)
        
        # حفظ في قاعدة البيانات
        # هنا يتم حفظ البيانات في قاعدة البيانات
        
        total_entities = len(data['main_branches']) + len(data['sub_branches']) + len(data['offices'])
        
        summary = f"""✅ تم حفظ شجرة الفروع بنجاح!

🏢 الفروع الرئيسية: {len(data['main_branches'])}
🏪 الفروع الفرعية: {len(data['sub_branches'])}
🏬 المكاتب: {len(data['offices'])}

📊 إجمالي الكيانات: {total_entities}"""
        
        QMessageBox.information(self, "نجح الحفظ", summary)
        self.accept()
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في حفظ البيانات:\n{str(e)}")
