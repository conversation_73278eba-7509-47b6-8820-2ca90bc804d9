#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حفظ الفواتير
Test Invoice Save Functionality
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_invoice_save():
    """اختبار حفظ الفاتورة"""
    print("🧪 اختبار حفظ الفاتورة في قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        from datetime import datetime
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # بيانات فاتورة تجريبية
        invoice_data = {
            'invoice_number': f'INV-TEST-{datetime.now().strftime("%Y%m%d%H%M%S")}',
            'customer_id': 1,
            'invoice_date': '2025-01-29',
            'due_date': '2025-02-28',
            'subtotal': 1000.00,
            'discount_amount': 100.00,
            'tax_amount': 135.00,
            'total_amount': 1035.00,
            'status': 'draft',
            'payment_method': 'نقداً',
            'notes': 'فاتورة تجريبية للاختبار'
        }
        
        # إدراج الفاتورة
        invoice_query = """
            INSERT INTO sales_invoices (
                invoice_number, customer_id, invoice_date, due_date,
                subtotal, discount_amount, tax_amount, total_amount,
                paid_amount, remaining_amount, status, payment_method, notes, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        invoice_params = (
            invoice_data['invoice_number'],
            invoice_data['customer_id'],
            invoice_data['invoice_date'],
            invoice_data['due_date'],
            invoice_data['subtotal'],
            invoice_data['discount_amount'],
            invoice_data['tax_amount'],
            invoice_data['total_amount'],
            0,  # paid_amount
            invoice_data['total_amount'],  # remaining_amount
            invoice_data['status'],
            invoice_data['payment_method'],
            invoice_data['notes'],
            1  # created_by
        )
        
        result = db_manager.execute_insert(invoice_query, invoice_params)
        
        if result:
            invoice_id = db_manager.cursor.lastrowid
            print(f"✅ تم حفظ الفاتورة بنجاح - ID: {invoice_id}")
            
            # إدراج تفاصيل الفاتورة
            item_query = """
                INSERT INTO sales_invoice_items (
                    invoice_id, product_id, quantity, unit_price,
                    discount_amount, tax_rate, tax_amount, line_total
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            item_params = (
                invoice_id,
                1,  # product_id
                10,  # quantity
                100,  # unit_price
                100,  # discount_amount
                15,  # tax_rate
                135,  # tax_amount
                1035  # line_total
            )
            
            item_result = db_manager.execute_insert(item_query, item_params)
            
            if item_result:
                print("✅ تم حفظ تفاصيل الفاتورة بنجاح")
                
                # التحقق من الحفظ
                check_query = "SELECT * FROM sales_invoices WHERE id = ?"
                check_result = db_manager.execute_query(check_query, (invoice_id,))
                
                if check_result:
                    saved_invoice = check_result[0]
                    print(f"✅ تم التحقق من الفاتورة المحفوظة:")
                    print(f"   📄 رقم الفاتورة: {saved_invoice['invoice_number']}")
                    print(f"   💰 المبلغ الإجمالي: {saved_invoice['total_amount']:.2f} ريال")
                    print(f"   📅 تاريخ الفاتورة: {saved_invoice['invoice_date']}")
                    print(f"   📋 الحالة: {saved_invoice['status']}")
                    
                    return True
                else:
                    print("❌ فشل في التحقق من الفاتورة المحفوظة")
                    return False
            else:
                print("❌ فشل في حفظ تفاصيل الفاتورة")
                return False
        else:
            print("❌ فشل في حفظ الفاتورة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار حفظ الفاتورة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_invoice_retrieval():
    """اختبار استرجاع الفواتير"""
    print("\n🧪 اختبار استرجاع الفواتير من قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager()
        
        # استرجاع جميع الفواتير
        query = """
            SELECT si.*, COUNT(sii.id) as items_count
            FROM sales_invoices si
            LEFT JOIN sales_invoice_items sii ON si.id = sii.invoice_id
            GROUP BY si.id
            ORDER BY si.created_at DESC
            LIMIT 5
        """
        
        invoices = db_manager.execute_query(query)
        
        if invoices:
            print(f"✅ تم العثور على {len(invoices)} فاتورة:")
            for invoice in invoices:
                print(f"   📄 {invoice['invoice_number']} - {invoice['total_amount']:.2f} ريال - {invoice['items_count']} صنف")
            return True
        else:
            print("⚠️ لا توجد فواتير في قاعدة البيانات")
            return True  # ليس خطأ، فقط لا توجد بيانات
            
    except Exception as e:
        print(f"❌ خطأ في استرجاع الفواتير: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار وظائف حفظ واسترجاع الفواتير")
    print("=" * 60)
    
    tests = [
        ("حفظ الفاتورة", test_invoice_save),
        ("استرجاع الفواتير", test_invoice_retrieval),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed_tests}/{total_tests} اختبار نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع اختبارات حفظ الفواتير نجحت!")
        print("\n✨ الوظائف المكتملة:")
        print("   💾 حفظ الفواتير في قاعدة البيانات")
        print("   📋 حفظ تفاصيل الفواتير")
        print("   🔍 استرجاع الفواتير المحفوظة")
        print("   ✅ التحقق من صحة البيانات المحفوظة")
    else:
        print("⚠️ بعض الاختبارات فشلت")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
