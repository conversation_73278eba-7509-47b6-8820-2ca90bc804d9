#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار واجهة بيانات العملاء مع التحكم في القراءة فقط
Customer Data Interface with Read-Only Control Test
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """اختبار واجهة بيانات العملاء مع التحكم في القراءة فقط"""
    print("🧾 اختبار واجهة بيانات العملاء - التحكم في القراءة فقط")
    print("=" * 70)
    
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.customers.customer_data_interface import CustomerDataInterface
        
        # محاكاة مدير قاعدة البيانات مع بيانات تجريبية
        class TestDBManager:
            def __init__(self):
                self.cursor = type('obj', (object,), {'lastrowid': 1})()
                self.customers = [
                    (
                        1, "MAIN001", "عميل تجاري", "ACC001", "أحمد", "تصنيف أ", 
                        "مجموعة 1", "CUST001", "شركة أحمد التجارية", "ahmed123", "pass123",
                        "شركة أحمد التجارية المحدودة", "أحمد محمد علي", "0112345678", "0501234567",
                        "<EMAIL>", "الرياض - حي النخيل", "الفرع الرئيسي",
                        "COL001", 30, 45, "خط الرياض", 1, "MARK001", "EMP001", "GRADE1",
                        "MEM001", "مركز التكلفة", "SUP001", "ريال سعودي", "ريال سعودي",
                        "مستوى 1", "مستوى 2", "حملة الصيف", 0, 0, "مدير عام",
                        "الإدارة", "الرياض - مكتب", "0112345679", "0112345680",
                        "<EMAIL>", "1985-03-15", "سعودي", "1234567890",
                        "A12345678", "متزوج", "عميل مميز", "مستوى 3", 100000.00, 15.00,
                        1, 0, 1, 0, "TAX123", "CR123", "البنك الأهلي",
                        "1234567890", "SA1234567890", "www.company.com",
                        "ملاحظات", "", "", "", "", "", "", "", "", "", "",
                        5, 3, "النظام", "النظام", "2024-01-15 10:00:00", 
                        "2024-01-28 14:30:00", "مدير النظام", "محاسب", 1
                    )
                ]
                self.current_index = 0
            
            def execute_query(self, query, params=None):
                if "SELECT * FROM customers" in query and self.customers:
                    return [self.customers[0]]
                return []
            
            def execute_update(self, query, params=None):
                return True
        
        db_manager = TestDBManager()
        
        # إنشاء واجهة بيانات العملاء
        interface = CustomerDataInterface(db_manager)
        
        # تحميل بيانات تجريبية
        interface.first_record()
        
        print("✅ تم إنشاء واجهة بيانات العملاء بنجاح")
        print("✅ تم تحميل البيانات التجريبية")
        
        # اختبار حالة القراءة فقط الافتراضية
        print("\n🔒 اختبار حالة القراءة فقط الافتراضية:")
        
        # فحص حالة الحقول
        readonly_fields = 0
        total_fields = 0
        
        from PyQt5.QtWidgets import QLineEdit, QTextEdit
        
        for widget in interface.findChildren(QLineEdit):
            total_fields += 1
            if widget.isReadOnly():
                readonly_fields += 1
        
        for widget in interface.findChildren(QTextEdit):
            total_fields += 1
            if widget.isReadOnly():
                readonly_fields += 1
        
        print(f"   📊 الحقول للقراءة فقط: {readonly_fields}/{total_fields}")
        
        if readonly_fields == total_fields:
            print("   ✅ جميع الحقول في وضع القراءة فقط (صحيح)")
        else:
            print("   ⚠️ بعض الحقول قابلة للكتابة (غير متوقع)")
        
        # عرض رسالة توضيحية
        QMessageBox.information(
            interface, 
            "🧾 واجهة بيانات العملاء - التحكم في القراءة فقط", 
            """🎯 الميزات الجديدة:

🔒 التحكم في القراءة فقط:
✅ الحقول للقراءة فقط افتراضياً
✅ تفعيل الكتابة عند الإضافة/التعديل
✅ التحكم حسب التبويب النشط

🛠️ أزرار التحكم الجديدة:
➕ جديد - لإضافة عميل جديد
✏️ تعديل - لتعديل العميل الحالي
❌ إلغاء - لإلغاء التغييرات
💾 حفظ - لحفظ البيانات

🎯 طريقة الاستخدام:
1. انقر "جديد" لإضافة عميل
2. انقر "تعديل" لتعديل العميل الحالي
3. غير التبويب لتعديل حقول مختلفة
4. انقر "حفظ" أو "إلغاء"

جرب الأزرار والتبويبات!"""
        )
        
        # عرض الواجهة
        interface.show()
        
        print("\n🎉 تم عرض واجهة بيانات العملاء بنجاح!")
        
        print("\n🔒 ميزات التحكم في القراءة فقط:")
        print("   ✅ الحقول للقراءة فقط افتراضياً")
        print("   ✅ زر 'جديد' لإضافة عميل جديد")
        print("   ✅ زر 'تعديل' لتعديل العميل الحالي")
        print("   ✅ زر 'إلغاء' لإلغاء التغييرات")
        print("   ✅ التحكم حسب التبويب النشط")
        print("   ✅ منع التنقل أثناء التعديل")
        print("   ✅ تغيير لون الحقول حسب الحالة")
        
        print("\n🎯 سيناريوهات الاستخدام:")
        print("   1️⃣ عرض البيانات: جميع الحقول للقراءة فقط")
        print("   2️⃣ إضافة جديد: تفعيل الحقول حسب التبويب")
        print("   3️⃣ تعديل موجود: تفعيل الحقول حسب التبويب")
        print("   4️⃣ تغيير التبويب: تفعيل حقول التبويب الجديد فقط")
        
        print("\n🛠️ الأزرار المتاحة:")
        print("   ➕ جديد - إضافة عميل جديد")
        print("   ✏️ تعديل - تعديل العميل الحالي")
        print("   💾 حفظ - حفظ التغييرات")
        print("   ❌ إلغاء - إلغاء التغييرات")
        print("   ⏮️⏪⏩⏭️ التنقل (معطل أثناء التعديل)")
        print("   🔍 بحث - البحث عن عميل")
        print("   👁️ معاينة - معاينة البيانات")
        print("   🖨️ طباعة - طباعة البيانات")
        
        print("\n🎨 التحسينات البصرية:")
        print("   🔒 خلفية رمادية للحقول المقفلة")
        print("   ✏️ خلفية بيضاء للحقول القابلة للتعديل")
        print("   🎯 تمييز التبويب النشط")
        print("   📱 رسائل توضيحية للمستخدم")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    main()
