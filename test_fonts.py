#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الخطوط المتاحة
Available Fonts Test
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel
from PyQt5.QtGui import QFontDatabase, QFont
from PyQt5.QtCore import Qt

def test_available_fonts():
    """اختبار الخطوط المتاحة"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # الحصول على قائمة الخطوط
    font_db = QFontDatabase()
    font_families = font_db.families()
    
    print("🔤 الخطوط المتاحة في النظام:")
    print("=" * 50)
    
    # البحث عن خطوط تدعم العربية
    arabic_fonts = []
    for family in font_families:
        if any(keyword in family.lower() for keyword in ['arabic', 'tahoma', 'arial', 'segoe']):
            arabic_fonts.append(family)
            print(f"✅ {family}")
    
    if not arabic_fonts:
        print("⚠️ لم يتم العثور على خطوط عربية محددة")
        print("📝 الخطوط المتاحة:")
        for family in font_families[:20]:  # أول 20 خط
            print(f"   - {family}")
    
    print(f"\n📊 إجمالي الخطوط: {len(font_families)}")
    print(f"📊 الخطوط العربية: {len(arabic_fonts)}")
    
    # اختبار خط افتراضي
    default_font = app.font()
    print(f"\n🔤 الخط الافتراضي: {default_font.family()}")
    print(f"📏 حجم الخط الافتراضي: {default_font.pointSize()}")
    
    app.quit()

if __name__ == "__main__":
    test_available_fonts()
