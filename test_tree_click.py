"""
اختبار النقر على عناصر الشجرة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import re

def test_text_cleaning():
    """اختبار تنظيف النصوص"""
    test_texts = [
        "🔧 المتغيرات العامة",
        "📅 إعداد فترات النظام", 
        "+ 🏢 تهيئة النظام",
        "- 📑 التهيئة العامة"
    ]
    
    for text in test_texts:
        # إزالة علامات + أو - من النص للمقارنة وإزالة الرموز التعبيرية
        clean_text = text.replace("+ ", "").replace("- ", "")
        # إزالة الرموز التعبيرية
        clean_text = re.sub(r'[^\w\s\u0600-\u06FF]', '', clean_text).strip()
        
        print(f"النص الأصلي: '{text}' -> النص المنظف: '{clean_text}'")
        
        # اختبار المطابقة
        if "المتغيرات العامة" in clean_text:
            print("  ✅ سيتم استدعاء show_general_variables()")
        elif "إعداد فترات النظام" in clean_text:
            print("  ✅ سيتم استدعاء show_system_periods()")
        elif "تهيئة النظام" in clean_text:
            print("  ✅ سيتم استدعاء show_system_setup_module()")
        elif "التهيئة العامة" in clean_text:
            print("  ✅ سيتم استدعاء show_system_setup_module()")
        else:
            print("  ❌ لن يتم استدعاء أي وظيفة محددة")
        
        print()

if __name__ == "__main__":
    test_text_cleaning()
