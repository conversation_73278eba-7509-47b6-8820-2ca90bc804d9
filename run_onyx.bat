@echo off
chcp 65001 >nul
echo ========================================
echo    🏢 Onyx ERP System
echo    نظام تخطيط موارد المؤسسات
echo ========================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo    Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo    Python found
echo.

REM تثبيت المتطلبات إذا لم تكن مثبتة
echo 🔍 جاري التحقق من المتطلبات...
echo    Checking requirements...
python -c "import PyQt5, sqlite3, bcrypt" >nul 2>&1
if errorlevel 1 (
    echo 📦 جاري تثبيت المتطلبات...
    echo    Installing requirements...
    pip install PyQt5 bcrypt python-dateutil
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        echo    Failed to install requirements
        pause
        exit /b 1
    )
)

echo ✅ المتطلبات جاهزة
echo    Requirements ready
echo.

REM تشغيل النظام
echo 🚀 جاري تشغيل نظام Onyx ERP...
echo    Starting Onyx ERP System...
echo.
echo 📋 بيانات تسجيل الدخول الافتراضية:
echo    Default login credentials:
echo    👤 المستخدم / User: admin
echo    🔑 كلمة المرور / Password: admin123
echo.
echo 🏢 إعدادات النظام الافتراضية:
echo    Default system settings:
echo    📅 السنة المالية / Fiscal Year: 2025
echo    🌐 اللغة / Language: عربي
echo    🏪 الشركة / Company: صقالات ابو سيف للنقل
echo    🏢 الفرع / Branch: 1 - الفرع الرئيسي
echo.

python run_onyx.py

if errorlevel 1 (
    echo.
    echo ❌ خطأ في تشغيل النظام
    echo    Error running system
    echo.
    pause
)

echo.
echo 👋 شكراً لاستخدام نظام Onyx ERP
echo    Thank you for using Onyx ERP System
pause