#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الترقيم التلقائي
Auto Numbering System Test
"""

import sys
import os
from datetime import datetime

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_auto_numbering_module():
    """اختبار وحدة الترقيم التلقائي"""
    print("🔢 اختبار وحدة الترقيم التلقائي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.auto_numbering_module import AutoNumberingWidget, NumberingSequenceDialog
        from database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إنشاء الوحدة الرئيسية
        numbering_widget = AutoNumberingWidget(db_manager)
        print("   ✅ تم إنشاء وحدة الترقيم التلقائي بنجاح")
        
        # اختبار إنشاء حوار التسلسل
        sequence_dialog = NumberingSequenceDialog(db_manager)
        print("   ✅ تم إنشاء حوار تسلسل الترقيم بنجاح")
        
        # اختبار الوظائف الأساسية
        if hasattr(numbering_widget, 'sequences_table'):
            print("   ✅ جدول التسلسلات موجود")
        
        if hasattr(numbering_widget, 'add_sequence'):
            print("   ✅ دالة إضافة التسلسل موجودة")
        
        if hasattr(numbering_widget, 'generate_preview_number'):
            print("   ✅ دالة معاينة الرقم موجودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار وحدة الترقيم التلقائي: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_table():
    """اختبار جدول قاعدة البيانات"""
    print("\n🗄️ اختبار جدول تسلسلات الترقيم...")
    
    try:
        import sqlite3
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect("accounting_system.db")
        cursor = conn.cursor()
        
        # فحص وجود الجدول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='numbering_sequences'")
        if cursor.fetchone():
            print("   ✅ جدول numbering_sequences موجود")
        else:
            print("   ❌ جدول numbering_sequences غير موجود")
            return False
        
        # فحص هيكل الجدول
        cursor.execute("PRAGMA table_info(numbering_sequences)")
        columns = cursor.fetchall()
        
        required_columns = [
            'document_type', 'prefix', 'current_number', 'number_length',
            'include_date', 'date_format', 'separator', 'reset_yearly', 'reset_monthly'
        ]
        
        existing_columns = [col[1] for col in columns]
        
        for col_name in required_columns:
            if col_name in existing_columns:
                print(f"   ✅ عمود {col_name} موجود")
            else:
                print(f"   ❌ عمود {col_name} غير موجود")
        
        # فحص البيانات الافتراضية
        cursor.execute("SELECT COUNT(*) FROM numbering_sequences")
        count = cursor.fetchone()[0]
        print(f"   📊 عدد التسلسلات المعرفة: {count}")
        
        if count > 0:
            cursor.execute("SELECT document_type, prefix FROM numbering_sequences LIMIT 5")
            sequences = cursor.fetchall()
            print("   📋 أمثلة على التسلسلات:")
            for doc_type, prefix in sequences:
                print(f"      • {doc_type}: {prefix}")
        
        conn.close()
        return count > 0
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_number_generation():
    """اختبار إنتاج الأرقام"""
    print("\n🎯 اختبار إنتاج الأرقام...")
    
    try:
        from database.sqlite_manager import SQLiteManager
        from modules.auto_numbering_module import AutoNumberingWidget
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إنتاج أرقام مختلفة
        document_types = [
            "فاتورة مبيعات",
            "فاتورة مشتريات", 
            "سند قبض",
            "سند صرف",
            "قيد يومية"
        ]
        
        generated_numbers = []
        
        for doc_type in document_types:
            try:
                number = AutoNumberingWidget.get_next_number(db_manager, doc_type)
                generated_numbers.append((doc_type, number))
                print(f"   ✅ {doc_type}: {number}")
            except Exception as e:
                print(f"   ❌ فشل في إنتاج رقم لـ {doc_type}: {e}")
        
        return len(generated_numbers) >= 3
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إنتاج الأرقام: {e}")
        return False

def test_sales_invoice_integration():
    """اختبار تكامل فواتير المبيعات"""
    print("\n🧾 اختبار تكامل فواتير المبيعات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.sales_invoices_new import SalesInvoiceDialog
        from database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # إنشاء حوار فاتورة المبيعات
        invoice_dialog = SalesInvoiceDialog(db_manager)
        print("   ✅ تم إنشاء حوار فاتورة المبيعات")
        
        # اختبار وجود الوظائف الجديدة
        if hasattr(invoice_dialog, 'generate_auto_invoice_number'):
            print("   ✅ دالة الترقيم التلقائي موجودة")
        
        if hasattr(invoice_dialog, 'enable_number_edit'):
            print("   ✅ دالة تعديل الرقم موجودة")
        
        if hasattr(invoice_dialog, 'set_current_date'):
            print("   ✅ دالة تعيين التاريخ الحالي موجودة")
        
        # اختبار وجود الأزرار
        if hasattr(invoice_dialog, 'auto_number_btn'):
            print("   ✅ زر الترقيم التلقائي موجود")
        
        if hasattr(invoice_dialog, 'edit_number_btn'):
            print("   ✅ زر تعديل الرقم موجود")
        
        if hasattr(invoice_dialog, 'current_date_btn'):
            print("   ✅ زر التاريخ الحالي موجود")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار تكامل فواتير المبيعات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_system_integration():
    """اختبار تكامل النظام الرئيسي"""
    print("\n🔗 اختبار تكامل النظام الرئيسي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        # اختبار النظام الرئيسي
        from main_system import MainSystemWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainSystemWindow()
        print("   ✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار وجود دالة الترقيم التلقائي
        if hasattr(main_window, 'open_auto_numbering'):
            print("   ✅ دالة open_auto_numbering موجودة")
        else:
            print("   ❌ دالة open_auto_numbering غير موجودة")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التكامل: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sequence_operations():
    """اختبار عمليات التسلسل"""
    print("\n⚙️ اختبار عمليات التسلسل...")
    
    try:
        from database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إضافة تسلسل جديد
        try:
            test_sequence_query = """
                INSERT OR REPLACE INTO numbering_sequences 
                (document_type, prefix, current_number, number_length, include_date, 
                 date_format, separator, reset_yearly, reset_monthly, is_active, 
                 created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            test_params = (
                "اختبار المستند",
                "TEST",
                1,
                6,
                True,
                "YYYY",
                "-",
                True,
                False,
                True,
                datetime.now().isoformat(),
                datetime.now().isoformat()
            )
            
            db_manager.execute_update(test_sequence_query, test_params)
            print("   ✅ تم إضافة تسلسل اختباري")
            
        except Exception as e:
            print(f"   ⚠️ تعذر إضافة تسلسل اختباري: {e}")
        
        # اختبار قراءة التسلسلات
        try:
            sequences_query = """
                SELECT document_type, prefix, current_number 
                FROM numbering_sequences 
                WHERE is_active = 1
                ORDER BY document_type
            """
            
            sequences = db_manager.execute_query(sequences_query)
            print(f"   📊 عدد التسلسلات النشطة: {len(sequences)}")
            
            for doc_type, prefix, current_num in sequences[:3]:
                print(f"      • {doc_type}: {prefix}-{current_num}")
            
        except Exception as e:
            print(f"   ❌ خطأ في قراءة التسلسلات: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار عمليات التسلسل: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار نظام الترقيم التلقائي")
    print("=" * 70)
    
    tests = [
        ("جدول قاعدة البيانات", test_database_table),
        ("وحدة الترقيم التلقائي", test_auto_numbering_module),
        ("إنتاج الأرقام", test_number_generation),
        ("عمليات التسلسل", test_sequence_operations),
        ("تكامل فواتير المبيعات", test_sales_invoice_integration),
        ("تكامل النظام الرئيسي", test_main_system_integration),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم تفعيل نظام الترقيم التلقائي بنجاح!")
        print("✅ جميع الوظائف تعمل بدون أخطاء")
        
        print("\n🔧 الوظائف المتاحة:")
        print("   🔢 ترقيم تلقائي للفواتير والسندات - مفعل ✅")
        print("   ✏️ تعديل أرقام المستندات - مفعل ✅")
        print("   📅 تعديل تواريخ المستندات - مفعل ✅")
        print("   ⚙️ إدارة تسلسلات الترقيم - مفعلة ✅")
        print("   🎯 معاينة الأرقام - مفعلة ✅")
        print("   🔄 إعادة تعيين سنوي/شهري - مفعلة ✅")
        
        print("\n🚀 كيفية الاستخدام:")
        print("   1. شغل النظام: python src/main_system.py")
        print("   2. انقر على 'إدارة النظام' في الشجرة")
        print("   3. اختر 'الترقيم التلقائي للمستندات'")
        print("   4. أو استخدم الأزرار في فواتير المبيعات:")
        print("      - 🔢 للترقيم التلقائي")
        print("      - ✏️ لتعديل الرقم")
        print("      - 📅 للتاريخ الحالي")
        
    else:
        print("\n⚠️ بعض وظائف الترقيم التلقائي تحتاج إلى إصلاح")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
