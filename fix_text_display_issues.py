#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل عرض النص
Fix Text Display Issues
"""

import os
import re

def fix_text_color_issues():
    """إصلاح مشاكل ألوان النص"""
    print("🔧 إصلاح مشاكل ألوان النص...")
    
    # قائمة الملفات للفحص
    files_to_check = [
        "src/modules/closures_audits_module.py",
        "src/modules/login_interface_control_module.py",
        "src/modules/activity_tracking_module.py",
        "src/modules/data_synchronization_module.py"
    ]
    
    fixes_applied = 0
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"   🔍 فحص ملف: {os.path.basename(file_path)}")
            
            try:
                # قراءة الملف
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # إصلاح 1: التأكد من وجود لون النص في العناوين
                content = re.sub(
                    r'(QLabel\s*\{[^}]*background-color:\s*[^;]+;[^}]*)\}',
                    lambda m: m.group(1) + '\n                color: white;\n            }' if 'color:' not in m.group(1) else m.group(0),
                    content
                )
                
                # إصلاح 2: التأكد من وجود لون النص في الأزرار
                content = re.sub(
                    r'(QPushButton\s*\{[^}]*background-color:\s*[^;]+;[^}]*)\}',
                    lambda m: m.group(1) + '\n                color: white;\n            }' if 'color:' not in m.group(1) else m.group(0),
                    content
                )
                
                # إصلاح 3: إضافة خط افتراضي
                if 'font-family:' not in content and 'QLabel' in content:
                    content = content.replace(
                        'font-size:',
                        'font-family: "Tahoma", "Arial", sans-serif;\n                font-size:'
                    )
                
                # حفظ الملف إذا تم تعديله
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"   ✅ تم إصلاح: {os.path.basename(file_path)}")
                    fixes_applied += 1
                else:
                    print(f"   ℹ️ لا يحتاج إصلاح: {os.path.basename(file_path)}")
                    
            except Exception as e:
                print(f"   ❌ خطأ في معالجة {file_path}: {e}")
        else:
            print(f"   ⚠️ ملف غير موجود: {file_path}")
    
    print(f"\n📊 تم إصلاح {fixes_applied} ملف")
    return fixes_applied > 0

def create_text_style_helper():
    """إنشاء ملف مساعد لأنماط النص"""
    print("\n🎨 إنشاء ملف مساعد لأنماط النص...")
    
    style_helper_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مساعد أنماط النص
Text Styles Helper
"""

def get_title_style(bg_color="#3498db"):
    """الحصول على نمط العنوان"""
    return f"""
        QLabel {{
            font-family: "Tahoma", "Arial", sans-serif;
            font-size: 18px;
            font-weight: bold;
            color: white;
            background-color: {bg_color};
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }}
    """

def get_button_style(bg_color="#27ae60", hover_color="#229954"):
    """الحصول على نمط الزر"""
    return f"""
        QPushButton {{
            font-family: "Tahoma", "Arial", sans-serif;
            background-color: {bg_color};
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            font-size: 14px;
            border: none;
        }}
        QPushButton:hover {{
            background-color: {hover_color};
        }}
        QPushButton:pressed {{
            background-color: {hover_color};
            padding: 11px 19px 9px 21px;
        }}
    """

def get_info_label_style():
    """الحصول على نمط تسمية المعلومات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", sans-serif;
            color: #2c3e50;
            font-size: 14px;
            font-weight: bold;
            padding: 8px 15px;
            border-radius: 5px;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
        }
    """

def get_status_style(status_type="success"):
    """الحصول على نمط الحالة"""
    colors = {
        "success": {"bg": "#d5f4e6", "border": "#27ae60", "color": "#27ae60"},
        "warning": {"bg": "#fff3cd", "border": "#f39c12", "color": "#f39c12"},
        "error": {"bg": "#ffebee", "border": "#e74c3c", "color": "#e74c3c"},
        "info": {"bg": "#ebf3fd", "border": "#3498db", "color": "#3498db"}
    }
    
    style_colors = colors.get(status_type, colors["info"])
    
    return f"""
        QLabel {{
            font-family: "Tahoma", "Arial", sans-serif;
            color: {style_colors["color"]};
            font-weight: bold;
            padding: 8px 15px;
            border: 2px solid {style_colors["border"]};
            border-radius: 5px;
            background-color: {style_colors["bg"]};
            font-size: 14px;
        }}
    """

def get_table_style():
    """الحصول على نمط الجدول"""
    return """
        QTableWidget {
            font-family: "Tahoma", "Arial", sans-serif;
            background-color: white;
            alternate-background-color: #f8f9fa;
            selection-background-color: #3498db;
            selection-color: white;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            font-size: 12px;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        QHeaderView::section {
            background-color: #34495e;
            color: white;
            padding: 10px;
            border: none;
            font-weight: bold;
            font-size: 13px;
        }
    """

def get_text_edit_style():
    """الحصول على نمط منطقة النص"""
    return """
        QTextEdit {
            font-family: "Courier New", "Consolas", monospace;
            background-color: white;
            color: #2c3e50;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            padding: 10px;
            font-size: 12px;
            line-height: 1.4;
        }
        
        QTextEdit:focus {
            border-color: #3498db;
        }
    """

def apply_global_font_fix():
    """تطبيق إصلاح الخط العام"""
    return """
        * {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
        }
        
        QWidget {
            color: #2c3e50;
            background-color: #ffffff;
        }
        
        QDialog {
            background-color: #f8f9fa;
        }
    """
'''
    
    try:
        with open('src/utils/text_styles.py', 'w', encoding='utf-8') as f:
            f.write(style_helper_content)
        print("   ✅ تم إنشاء ملف مساعد الأنماط: src/utils/text_styles.py")
        return True
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء ملف الأنماط: {e}")
        return False

def create_font_test():
    """إنشاء اختبار الخطوط"""
    print("\n🔤 إنشاء اختبار الخطوط...")
    
    font_test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الخطوط المتاحة
Available Fonts Test
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel
from PyQt5.QtGui import QFontDatabase, QFont
from PyQt5.QtCore import Qt

def test_available_fonts():
    """اختبار الخطوط المتاحة"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # الحصول على قائمة الخطوط
    font_db = QFontDatabase()
    font_families = font_db.families()
    
    print("🔤 الخطوط المتاحة في النظام:")
    print("=" * 50)
    
    # البحث عن خطوط تدعم العربية
    arabic_fonts = []
    for family in font_families:
        if any(keyword in family.lower() for keyword in ['arabic', 'tahoma', 'arial', 'segoe']):
            arabic_fonts.append(family)
            print(f"✅ {family}")
    
    if not arabic_fonts:
        print("⚠️ لم يتم العثور على خطوط عربية محددة")
        print("📝 الخطوط المتاحة:")
        for family in font_families[:20]:  # أول 20 خط
            print(f"   - {family}")
    
    print(f"\\n📊 إجمالي الخطوط: {len(font_families)}")
    print(f"📊 الخطوط العربية: {len(arabic_fonts)}")
    
    # اختبار خط افتراضي
    default_font = app.font()
    print(f"\\n🔤 الخط الافتراضي: {default_font.family()}")
    print(f"📏 حجم الخط الافتراضي: {default_font.pointSize()}")
    
    app.quit()

if __name__ == "__main__":
    test_available_fonts()
'''
    
    try:
        with open('test_fonts.py', 'w', encoding='utf-8') as f:
            f.write(font_test_content)
        print("   ✅ تم إنشاء اختبار الخطوط: test_fonts.py")
        return True
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء اختبار الخطوط: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🛠️ إصلاح مشاكل عرض النص")
    print("=" * 50)
    
    # إنشاء مجلد utils إذا لم يكن موجوداً
    os.makedirs('src/utils', exist_ok=True)
    
    # تطبيق الإصلاحات
    fixes = [
        ("إصلاح ألوان النص", fix_text_color_issues),
        ("إنشاء مساعد الأنماط", create_text_style_helper),
        ("إنشاء اختبار الخطوط", create_font_test),
    ]
    
    successful_fixes = 0
    
    for fix_name, fix_function in fixes:
        print(f"\\n🔧 {fix_name}:")
        print("-" * 30)
        
        try:
            if fix_function():
                print(f"✅ نجح: {fix_name}")
                successful_fixes += 1
            else:
                print(f"⚠️ لم يحتج إصلاح: {fix_name}")
                successful_fixes += 1
        except Exception as e:
            print(f"❌ فشل: {fix_name} - {e}")
    
    print("\\n" + "=" * 50)
    print("📊 ملخص الإصلاحات:")
    print(f"   ✅ الإصلاحات الناجحة: {successful_fixes}/{len(fixes)}")
    
    if successful_fixes == len(fixes):
        print("\\n🎉 تم إصلاح جميع مشاكل عرض النص!")
        print("\\n📝 الخطوات التالية:")
        print("   1. شغل: python test_fonts.py")
        print("   2. شغل: python test_text_display.py")
        print("   3. شغل النظام الرئيسي للتحقق من الإصلاحات")
        
        print("\\n💡 نصائح لحل مشاكل النص:")
        print("   🔤 تأكد من تثبيت خطوط عربية (Tahoma, Arial)")
        print("   🎨 تحقق من إعدادات العرض في النظام")
        print("   🔧 استخدم مساعد الأنماط الجديد في الوحدات")
        
    else:
        print("\\n⚠️ بعض الإصلاحات تحتاج مراجعة")
    
    return successful_fixes == len(fixes)

if __name__ == "__main__":
    main()
