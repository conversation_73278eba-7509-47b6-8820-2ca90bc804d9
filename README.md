# نظام Onyx ERP - نظام تخطيط موارد المؤسسات

![Onyx ERP](https://img.shields.io/badge/Onyx%20ERP-v1.0.0-blue)
![Python](https://img.shields.io/badge/Python-3.8+-green)
![PyQt5](https://img.shields.io/badge/PyQt5-5.15+-orange)
![SQLite](https://img.shields.io/badge/SQLite-3.0+-lightgrey)

نظام Onyx ERP هو نظام تخطيط موارد المؤسسات مطور بلغة Python باستخدام PyQt5، مصمم خصيصاً للشركات العربية مع دعم كامل للغة العربية والمحاسبة المالية.

## المميزات الرئيسية

### 1. واجهة تسجيل الدخول
- تسجيل دخول آمن بكلمة مرور مشفرة
- دعم اللغتين العربية والإنجليزية
- إمكانية استعادة كلمة المرور
- واجهة مستخدم حديثة وسهلة الاستخدام

### 2. اللوحة الرئيسية (Dashboard)
- نظرة شاملة على حالة الحسابات والمالية
- مؤشرات أداء رئيسية (KPIs):
  - إجمالي الإيرادات
  - إجمالي المصروفات
  - الربح الصافي
  - المبالغ المستحقة من العملاء
  - المبالغ المستحقة للموردين
  - الضرائب المستحقة
- اختصارات سريعة للعمليات الأساسية

### 3. الوحدات الرئيسية
- **إدارة الحسابات**: دليل الحسابات والقيود اليومية
- **إدارة الفواتير**: فواتير البيع والشراء
- **إدارة المخزون**: المنتجات والخدمات
- **العملاء والموردين**: إدارة قاعدة بيانات العملاء
- **التقارير المالية**: تقارير شاملة ومخصصة
- **إدارة الرواتب**: كشوف الرواتب والموظفين
- **التحليلات**: إحصائيات ومخططات بيانية
- **الإعدادات**: تخصيص النظام

### 4. المميزات التقنية
- واجهة مستخدم متجاوبة ومتعددة اللغات
- قاعدة بيانات MySQL آمنة ومحسنة
- نظام أمان متقدم للمستخدمين
- تشفير كلمات المرور
- نسخ احتياطي تلقائي
- تقارير قابلة للتصدير (PDF, Excel)

## متطلبات النظام

### البرمجيات المطلوبة
- Python 3.7 أو أحدث
- MySQL Server 5.7 أو أحدث
- نظام التشغيل: Windows 10/11, Linux, macOS

### المكتبات المطلوبة
```
PyQt5==5.15.10
mysql-connector-python==8.2.0
matplotlib==3.8.2
pandas==2.1.4
reportlab==4.0.7
Pillow==10.1.0
bcrypt==4.1.2
python-dateutil==2.8.2
openpyxl==3.1.2
```

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd HGR
```

### 2. تثبيت المتطلبات
```bash
python setup.py
```

أو يدوياً:
```bash
pip install -r requirements.txt
```

### 3. إعداد قاعدة البيانات
1. تأكد من تشغيل MySQL Server
2. قم بتعديل إعدادات قاعدة البيانات في `config.json`
3. سيتم إنشاء قاعدة البيانات والجداول تلقائياً عند أول تشغيل

### 4. تشغيل النظام
```bash
python main.py
```

## بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

⚠️ **تحذير**: يرجى تغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول

## هيكل المشروع

```
HGR/
├── main.py                 # الملف الرئيسي
├── setup.py               # إعداد النظام
├── requirements.txt       # المتطلبات
├── config.json           # ملف الإعدادات
├── README.md             # دليل الاستخدام
├── src/                  # مجلد الكود المصدري
│   ├── __init__.py
│   ├── database/         # إدارة قاعدة البيانات
│   │   ├── __init__.py
│   │   └── database_manager.py
│   ├── ui/              # واجهات المستخدم
│   │   ├── __init__.py
│   │   ├── login_window.py
│   │   └── main_window.py
│   └── utils/           # الأدوات المساعدة
│       ├── __init__.py
│       ├── config.py
│       └── translations.py
```

## الاستخدام

### 1. تسجيل الدخول
1. شغل النظام باستخدام `python main.py`
2. أدخل بيانات تسجيل الدخول
3. اختر اللغة المفضلة
4. اضغط "دخول"

### 2. استخدام اللوحة الرئيسية
- استعرض مؤشرات الأداء الرئيسية
- استخدم الاختصارات السريعة للعمليات الشائعة
- تنقل بين الوحدات من الشريط الجانبي

### 3. إدارة الحسابات
- إنشاء وتعديل دليل الحسابات
- إدخال القيود اليومية
- مراجعة الأرصدة

### 4. إدارة الفواتير
- إنشاء فواتير البيع والشراء
- تتبع حالة الدفع
- طباعة وتصدير الفواتير

## الإعدادات

يمكن تخصيص النظام من خلال ملف `config.json`:

```json
{
    "database": {
        "host": "localhost",
        "port": 3306,
        "user": "root",
        "password": "",
        "database": "accounting_system"
    },
    "ui": {
        "language": "ar",
        "theme": "light",
        "font_size": 10
    },
    "company": {
        "name": "شركة المحاسبة",
        "name_en": "Accounting Company",
        "address": "",
        "phone": "",
        "email": "",
        "tax_number": ""
    },
    "accounting": {
        "currency": "SAR",
        "currency_symbol": "ر.س",
        "decimal_places": 2,
        "tax_rate": 15.0,
        "fiscal_year_start": "01-01"
    }
}
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ في الاتصال بقاعدة البيانات**
   - تأكد من تشغيل MySQL Server
   - تحقق من صحة بيانات الاتصال في config.json
   - تأكد من وجود صلاحيات إنشاء قواعد البيانات

2. **خطأ في تثبيت المتطلبات**
   - تأكد من تثبيت Python 3.7+
   - استخدم pip الأحدث: `python -m pip install --upgrade pip`
   - في حالة Windows، قد تحتاج Visual C++ Build Tools

3. **مشاكل في الواجهة الرسومية**
   - تأكد من تثبيت PyQt5 بشكل صحيح
   - في Linux، قد تحتاج: `sudo apt-get install python3-pyqt5`

## التطوير والمساهمة

### إضافة وحدات جديدة
1. أنشئ ملف جديد في `src/ui/`
2. اتبع نفس نمط الوحدات الموجودة
3. أضف الوحدة إلى القائمة الجانبية في `main_window.py`

### إضافة ترجمات
1. أضف المفاتيح الجديدة في `src/utils/translations.py`
2. أضف الترجمات للغتين العربية والإنجليزية

## الأمان

- كلمات المرور مشفرة باستخدام bcrypt
- جلسات المستخدمين محمية
- التحقق من الصلاحيات على مستوى قاعدة البيانات
- سجلات تدقيق للعمليات الحساسة

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- أنشئ issue في المستودع
- تأكد من تضمين تفاصيل الخطأ ونظام التشغيل

## الترخيص

هذا المشروع مرخص تحت [اسم الترخيص] - راجع ملف LICENSE للتفاصيل.

## الإصدارات

### الإصدار 1.0.0
- واجهة تسجيل الدخول
- اللوحة الرئيسية
- هيكل قاعدة البيانات الأساسي
- نظام الترجمة
- الإعدادات الأساسية

### الإصدارات القادمة
- وحدات إدارة الحسابات والفواتير
- التقارير المالية
- نظام النسخ الاحتياطي
- واجهة الويب
- تطبيق الهاتف المحمول