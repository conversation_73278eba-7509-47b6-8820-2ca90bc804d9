#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""اختبار سريع للنص الواضح"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    from modules.customers.customer_data_interface import CustomerDataInterface
    
    class MockDB:
        def __init__(self):
            self.cursor = type('obj', (object,), {'lastrowid': 1})()
        def execute_query(self, query, params=None):
            return []
        def execute_update(self, query, params=None):
            return True
    
    interface = CustomerDataInterface(MockDB())
    interface.show()
    
    print("🎉 تم عرض الواجهة مع النص الواضح!")
    print("تحقق من وضوح جميع النصوص في الليبلات")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
