#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوحدة إدارة العملاء
Comprehensive test for customers module
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_customers_module():
    """اختبار وحدة إدارة العملاء"""
    print("🧪 اختبار وحدة إدارة العملاء...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.customers_module import CustomersWidget, CustomerDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار إنشاء وحدة إدارة العملاء
        customers_widget = CustomersWidget(db_manager)
        print("✅ تم إنشاء وحدة إدارة العملاء")
        
        # اختبار تحميل العملاء
        customers_widget.load_customers()
        print("✅ تم تحميل العملاء من قاعدة البيانات")
        
        # اختبار عدد العملاء المحملين
        customer_count = customers_widget.customers_table.rowCount()
        print(f"📊 عدد العملاء المحملين: {customer_count}")
        
        # اختبار البحث والفلترة
        customers_widget.search_edit.setText("شركة")
        customers_widget.filter_customers()
        print("✅ تم اختبار وظيفة البحث")
        
        # اختبار فلترة النوع
        customers_widget.type_filter.setCurrentText("شركات")
        customers_widget.filter_customers()
        print("✅ تم اختبار فلترة النوع")
        
        # اختبار مسح الفلاتر
        customers_widget.search_edit.clear()
        customers_widget.type_filter.setCurrentText("الكل")
        customers_widget.status_filter.setCurrentText("الكل")
        customers_widget.filter_customers()
        print("✅ تم اختبار مسح الفلاتر")
        
        # تنظيف
        customers_widget.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وحدة إدارة العملاء: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_customer_dialog():
    """اختبار حوار إدارة العملاء"""
    print("\n🧪 اختبار حوار إدارة العملاء...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.customers_module import CustomerDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار حوار إضافة عميل جديد
        dialog = CustomerDialog(db_manager)
        print("✅ تم إنشاء حوار إضافة عميل")
        
        # اختبار ملء البيانات
        dialog.customer_name_edit.setText("عميل تجريبي")
        dialog.customer_type_combo.setCurrentIndex(0)  # فرد
        dialog.phone_edit.setText("011-1234567")
        dialog.mobile_edit.setText("0501234567")
        dialog.email_edit.setText("<EMAIL>")
        dialog.city_edit.setText("الرياض")
        dialog.credit_limit_spin.setValue(10000.0)
        print("✅ تم ملء البيانات الأساسية")
        
        # اختبار التحقق من صحة البيانات
        if dialog.validate_data():
            print("✅ البيانات صحيحة")
        else:
            print("❌ البيانات غير صحيحة")
        
        # اختبار إنشاء كود العميل
        customer_code = dialog.generate_customer_code()
        print(f"✅ تم إنشاء كود العميل: {customer_code}")
        
        # اختبار تغيير نوع العميل
        dialog.customer_type_combo.setCurrentIndex(1)  # شركة
        dialog.on_customer_type_changed()
        print("✅ تم اختبار تغيير نوع العميل")
        
        # تنظيف
        dialog.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حوار إدارة العملاء: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_customers_database_operations():
    """اختبار عمليات قاعدة البيانات للعملاء"""
    print("\n🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار استعلام العملاء
        query = """
            SELECT id, customer_code, customer_name, customer_type, phone, 
                   email, city, credit_limit, is_active
            FROM customers 
            ORDER BY customer_name
        """
        
        customers = db_manager.execute_query(query)
        print(f"✅ تم استعلام {len(customers)} عميل من قاعدة البيانات")
        
        # اختبار استعلام العملاء النشطين
        active_query = """
            SELECT COUNT(*) as count FROM customers WHERE is_active = 1
        """
        active_result = db_manager.execute_query(active_query)
        active_count = active_result[0]['count'] if active_result else 0
        print(f"✅ العملاء النشطين: {active_count}")
        
        # اختبار استعلام أنواع العملاء
        types_query = """
            SELECT customer_type, COUNT(*) as count FROM customers 
            GROUP BY customer_type
            ORDER BY count DESC
        """
        types_result = db_manager.execute_query(types_query)
        print(f"📊 توزيع أنواع العملاء:")
        for type_data in types_result:
            type_name = {'individual': 'أفراد', 'company': 'شركات'}.get(
                type_data['customer_type'], type_data['customer_type']
            )
            print(f"   {type_name}: {type_data['count']}")
        
        # اختبار حساب إجمالي حدود الائتمان
        credit_query = """
            SELECT SUM(credit_limit) as total_credit FROM customers WHERE is_active = 1
        """
        credit_result = db_manager.execute_query(credit_query)
        total_credit = credit_result[0]['total_credit'] if credit_result else 0
        print(f"💰 إجمالي حدود الائتمان: {total_credit:,.2f} ريال")
        
        # اختبار البحث في العملاء
        search_query = """
            SELECT COUNT(*) as count FROM customers 
            WHERE customer_name LIKE ? OR customer_code LIKE ? OR phone LIKE ?
        """
        search_result = db_manager.execute_query(search_query, ('%شركة%', '%CUST%', '%011%'))
        search_count = search_result[0]['count'] if search_result else 0
        print(f"🔍 نتائج البحث: {search_count}")
        
        # اختبار العملاء حسب المدن
        cities_query = """
            SELECT city, COUNT(*) as count FROM customers 
            WHERE city IS NOT NULL AND city != ''
            GROUP BY city
            ORDER BY count DESC
            LIMIT 5
        """
        cities_result = db_manager.execute_query(cities_query)
        print(f"🏙️ أهم المدن:")
        for city_data in cities_result:
            print(f"   {city_data['city']}: {city_data['count']}")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_structure():
    """اختبار هيكل قاعدة البيانات للعملاء"""
    print("\n🧪 اختبار هيكل قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار وجود جدول customers
        table_query = "SELECT name FROM sqlite_master WHERE type='table' AND name='customers'"
        table_result = db_manager.execute_query(table_query)
        
        if table_result:
            print("✅ جدول customers موجود")
        else:
            print("❌ جدول customers مفقود")
            return False
        
        # اختبار أعمدة الجدول
        columns_query = "PRAGMA table_info(customers)"
        columns = db_manager.execute_query(columns_query)
        column_names = [col['name'] for col in columns]
        
        required_columns = [
            'id', 'customer_code', 'customer_name', 'customer_type', 'contact_person',
            'phone', 'mobile', 'email', 'address', 'city', 'country', 'tax_number',
            'credit_limit', 'payment_terms', 'is_active', 'notes', 'created_at', 'updated_at'
        ]
        
        print("\n📋 أعمدة جدول customers:")
        for col in required_columns:
            if col in column_names:
                print(f"   ✅ {col}")
            else:
                print(f"   ❌ {col} مفقود")
        
        # عرض جميع الأعمدة الموجودة
        print(f"\n📊 إجمالي الأعمدة الموجودة: {len(column_names)}")
        print(f"الأعمدة: {', '.join(column_names)}")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار هيكل قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار شامل لوحدة إدارة العملاء")
    print("   Comprehensive test for customers module")
    print("=" * 60)
    
    # اختبار هيكل قاعدة البيانات
    structure_test = test_database_structure()
    
    # اختبار وحدة إدارة العملاء
    module_test = test_customers_module()
    
    # اختبار حوار إدارة العملاء
    dialog_test = test_customer_dialog()
    
    # اختبار عمليات قاعدة البيانات
    database_test = test_customers_database_operations()
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار وحدة إدارة العملاء:")
    print(f"   🗄️ هيكل قاعدة البيانات: {'✅ نجح' if structure_test else '❌ فشل'}")
    print(f"   🏗️ اختبار الوحدة: {'✅ نجح' if module_test else '❌ فشل'}")
    print(f"   💬 اختبار الحوار: {'✅ نجح' if dialog_test else '❌ فشل'}")
    print(f"   🗄️ اختبار قاعدة البيانات: {'✅ نجح' if database_test else '❌ فشل'}")
    
    if structure_test and module_test and dialog_test and database_test:
        print("\n🎉 جميع اختبارات وحدة إدارة العملاء نجحت!")
        print("✅ وحدة إدارة العملاء جاهزة للاستخدام")
        
        print("\n📋 الوظائف المتاحة:")
        print("   👥 إدارة العملاء (إضافة، تعديل، حذف)")
        print("   🔍 البحث والفلترة المتقدمة")
        print("   📊 عرض إحصائيات العملاء")
        print("   💰 إدارة حدود الائتمان")
        print("   🏢 دعم الأفراد والشركات")
        print("   📱 معلومات الاتصال الكاملة")
        print("   🌍 إدارة العناوين والمدن")
        print("   📄 الرقم الضريبي للشركات")
        print("   ⚙️ إعدادات شروط الدفع")
    else:
        print("\n⚠️ بعض اختبارات وحدة إدارة العملاء فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)
    
    return 0 if (structure_test and module_test and dialog_test and database_test) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
