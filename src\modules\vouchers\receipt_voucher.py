#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سند القبض - نظام Onyx Pro ERP
Receipt Voucher - Onyx Pro ERP System
"""

from .voucher_interface import VoucherInterface

class ReceiptVoucherDialog(VoucherInterface):
    """حوار سند القبض"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(db_manager, "receipt", parent)
        self.customize_for_receipt()
    
    def customize_for_receipt(self):
        """تخصيص الواجهة لسند القبض"""
        # تحديث العنوان
        self.setWindowTitle("سند قبض - Onyx Pro ERP")
        
        # تخصيص أنواع السندات
        self.voucher_type_combo.clear()
        self.voucher_type_combo.addItems([
            "سند قبض نقدي",
            "سند قبض بنكي", 
            "سند قبض شيك",
            "سند قبض تحويل بنكي"
        ])
        
        # إضافة صف افتراضي للنقدية
        if self.details_table.rowCount() == 1:
            # إضافة حساب النقدية كصف افتراضي
            self.details_table.setItem(0, 1, self.details_table.item(0, 1) or self.create_table_item("1010001"))
            self.details_table.setItem(0, 2, self.details_table.item(0, 2) or self.create_table_item("النقدية"))
            self.details_table.setItem(0, 3, self.details_table.item(0, 3) or self.create_table_item("مبلغ مقبوض"))
    
    def create_table_item(self, text):
        """إنشاء عنصر جدول"""
        from PyQt5.QtWidgets import QTableWidgetItem
        return QTableWidgetItem(str(text))
    
    def generate_voucher_number(self):
        """توليد رقم سند القبض"""
        try:
            from PyQt5.QtCore import QDate
            year = QDate.currentDate().year()
            
            # البحث عن آخر رقم سند قبض
            query = """
            SELECT MAX(CAST(SUBSTR(voucher_number, -6) AS INTEGER)) 
            FROM vouchers 
            WHERE voucher_type = 'receipt' AND voucher_number LIKE ?
            """
            pattern = f"RC{year}%"
            result = self.db_manager.execute_query(query, (pattern,))
            
            last_number = result[0][0] if result and result[0][0] else 0
            new_number = last_number + 1
            
            voucher_number = f"RC{year}{new_number:06d}"
            self.voucher_number_edit.setText(voucher_number)
            
        except Exception:
            # في حالة الخطأ، استخدم رقم افتراضي
            from PyQt5.QtCore import QDate
            year = QDate.currentDate().year()
            self.voucher_number_edit.setText(f"RC{year}000001")
