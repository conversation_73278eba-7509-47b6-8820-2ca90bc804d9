#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر لشاشة إعداد الدليل المحاسبي
Direct Test for Accounting Guide Setup Screen
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_accounting_guide():
    """اختبار شاشة إعداد الدليل المحاسبي"""
    try:
        print("🚀 تشغيل شاشة إعداد الدليل المحاسبي...")
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from modules.setup.accounting_guide import AccountingGuideDialog
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("✅ تم إنشاء التطبيق بنجاح")
        
        dialog = AccountingGuideDialog(MockDBManager())
        print("✅ تم إنشاء شاشة الدليل المحاسبي بنجاح")
        
        dialog.show()
        print("📚 شاشة إعداد الدليل المحاسبي جاهزة!")
        
        print("\n📋 الميزات المتاحة:")
        print("   ✓ شجرة حسابات هرمية تفاعلية")
        print("   ✓ 5 تبويبات شاملة للإدارة")
        print("   ✓ الحسابات الرئيسية والفرعية")
        print("   ✓ أنواع الحسابات وقوالب جاهزة")
        print("   ✓ إعدادات الدليل والأمان")
        print("   ✓ تقارير مالية متقدمة")
        print("   ✓ دليل محاسبي نموذجي يمني")
        print("   ✓ ميزان مراجعة وأرصدة حسابات")
        print("   ✓ 8 حسابات رئيسية و12 حساب فرعي")
        print("   ✓ نظام ترقيم متقدم ومرن")
        
        print("\n🎯 التبويبات المتاحة:")
        print("   📊 الحسابات الرئيسية - إدارة الحسابات الأساسية")
        print("   📋 الحسابات الفرعية - إدارة الحسابات التفصيلية")
        print("   🏷️ أنواع الحسابات - تصنيف وقوالب الحسابات")
        print("   ⚙️ إعدادات الدليل - إعدادات العرض والأمان")
        print("   📊 التقارير والتحليل - تقارير مالية شاملة")
        
        print("\n🌟 المميزات الخاصة:")
        print("   🌳 شجرة تفاعلية مع إمكانية التوسيع والطي")
        print("   📊 إحصائيات فورية للحسابات")
        print("   📈 تقارير مالية متقدمة (دليل الحسابات، ميزان المراجعة)")
        print("   🔍 بحث وتصفية في الحسابات")
        print("   💾 حفظ وتصدير البيانات")
        print("   🎨 واجهة عربية احترافية")
        
        print("\n🎮 كيفية الاستخدام:")
        print("   1. استخدم الشجرة على اليسار لتصفح الحسابات")
        print("   2. انقر على التبويبات لإدارة أنواع الحسابات المختلفة")
        print("   3. استخدم تبويب التقارير لإنتاج التقارير المالية")
        print("   4. اضبط الإعدادات في تبويب الإعدادات")
        
        print("\n🎉 الشاشة جاهزة للاستخدام!")
        
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من وجود ملفات الوحدة في المسار الصحيح")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الشاشة: {e}")
        return False

if __name__ == "__main__":
    test_accounting_guide()
