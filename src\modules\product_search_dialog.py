#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة البحث عن المنتجات - F5
Product Search Dialog - F5
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont


class ProductSearchDialog(QDialog):
    """نافذة البحث عن المنتجات مع F5"""
    
    product_selected = pyqtSignal(dict)
    
    def __init__(self, products, parent=None):
        super().__init__(parent)
        self.products = products
        self.filtered_products = products.copy()
        self.setup_ui()
        self.setup_connections()
        self.load_products()
    
    def setup_ui(self):
        """إعداد واجهة نافذة البحث"""
        self.setWindowTitle("🔍 البحث عن المنتجات - اضغط F5")
        self.setModal(True)
        self.resize(900, 700)
        
        # تنسيق النافذة
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLineEdit {
                padding: 12px;
                border: 2px solid #007bff;
                border-radius: 8px;
                font-size: 16px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #0056b3;
                background-color: #e3f2fd;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                font-size: 14px;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
                font-weight: bold;
            }
            QHeaderView::section {
                background-color: #343a40;
                color: white;
                padding: 15px;
                border: none;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setSpacing(20)
        layout.setContentsMargins(25, 25, 25, 25)
        
        # عنوان النافذة
        title_label = QLabel("🔍 البحث عن المنتجات والخدمات")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 22px;
                font-weight: bold;
                color: #343a40;
                padding: 15px;
                background-color: #e9ecef;
                border-radius: 8px;
                border: 3px solid #007bff;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # تعليمات الاستخدام
        instructions_label = QLabel("💡 اكتب اسم المنتج أو رقمه واضغط Enter للبحث | اضغط مرتين على المنتج لاختياره")
        instructions_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 14px;
                font-style: italic;
                padding: 10px;
                background-color: #fff3cd;
                border-radius: 6px;
                border: 1px solid #ffeaa7;
            }
        """)
        layout.addWidget(instructions_label)
        
        # حقل البحث
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("🔍 البحث:"))
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("اكتب اسم المنتج أو رقمه...")
        search_layout.addWidget(self.search_edit)
        
        self.search_btn = QPushButton("🔍 بحث")
        self.search_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        search_layout.addWidget(self.search_btn)
        
        layout.addLayout(search_layout)
        
        # جدول المنتجات
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(4)
        self.products_table.setHorizontalHeaderLabels([
            "رقم المنتج", "اسم المنتج", "السعر", "الوحدة"
        ])
        
        # تعديل عرض الأعمدة
        header = self.products_table.horizontalHeader()
        header.resizeSection(0, 120)
        header.resizeSection(1, 400)
        header.resizeSection(2, 120)
        header.resizeSection(3, 120)
        
        self.products_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.products_table.setAlternatingRowColors(True)
        layout.addWidget(self.products_table)
        
        # معلومات البحث
        self.info_label = QLabel("📊 عدد المنتجات: 0")
        self.info_label.setStyleSheet("""
            QLabel {
                color: #495057;
                font-weight: bold;
                padding: 8px;
                background-color: #e9ecef;
                border-radius: 6px;
                font-size: 14px;
            }
        """)
        layout.addWidget(self.info_label)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.select_btn = QPushButton("✅ اختيار المنتج")
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.select_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.search_edit.textChanged.connect(self.filter_products)
        self.search_edit.returnPressed.connect(self.search_products)
        self.search_btn.clicked.connect(self.search_products)
        self.products_table.doubleClicked.connect(self.select_current_product)
        self.products_table.keyPressEvent = self.table_key_press
        self.select_btn.clicked.connect(self.select_current_product)
        self.cancel_btn.clicked.connect(self.reject)
    
    def load_products(self):
        """تحميل المنتجات في الجدول"""
        self.products_table.setRowCount(len(self.filtered_products))
        
        for row, product in enumerate(self.filtered_products):
            # رقم المنتج
            id_item = QTableWidgetItem(str(product['id']))
            id_item.setTextAlignment(Qt.AlignCenter)
            self.products_table.setItem(row, 0, id_item)
            
            # اسم المنتج
            name_item = QTableWidgetItem(product['product_name'])
            self.products_table.setItem(row, 1, name_item)
            
            # السعر
            price = product.get('unit_price', 0.0)
            price_item = QTableWidgetItem(f"{price:.2f} ريال")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.products_table.setItem(row, 2, price_item)
            
            # الوحدة
            unit_item = QTableWidgetItem("قطعة")
            unit_item.setTextAlignment(Qt.AlignCenter)
            self.products_table.setItem(row, 3, unit_item)
        
        # تحديث معلومات البحث
        self.info_label.setText(f"📊 عدد المنتجات: {len(self.filtered_products)} منتج")
    
    def filter_products(self, text):
        """فلترة المنتجات حسب النص المدخل"""
        if not text.strip():
            self.filtered_products = self.products.copy()
        else:
            text = text.lower()
            self.filtered_products = [
                product for product in self.products
                if text in product['product_name'].lower() or text in str(product['id'])
            ]
        
        self.load_products()
    
    def search_products(self):
        """البحث عن المنتجات"""
        search_text = self.search_edit.text().strip()
        if search_text:
            self.filter_products(search_text)
            if self.filtered_products:
                # تحديد أول منتج
                self.products_table.selectRow(0)
                QMessageBox.information(self, "نتائج البحث", 
                                      f"🔍 تم العثور على {len(self.filtered_products)} منتج\n"
                                      "اضغط مرتين على المنتج لاختياره")
            else:
                QMessageBox.warning(self, "لا توجد نتائج", 
                                  f"❌ لم يتم العثور على منتجات تحتوي على: {search_text}")
    
    def table_key_press(self, event):
        """معالجة الضغط على المفاتيح في الجدول"""
        if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            self.select_current_product()
        else:
            QTableWidget.keyPressEvent(self.products_table, event)
    
    def select_current_product(self):
        """اختيار المنتج الحالي"""
        current_row = self.products_table.currentRow()
        if current_row >= 0 and current_row < len(self.filtered_products):
            selected_product = self.filtered_products[current_row]
            self.product_selected.emit(selected_product)
            self.accept()
        else:
            QMessageBox.warning(self, "تحذير", "⚠️ يرجى اختيار منتج من القائمة!")
