#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
معلومات النظام والإحصائيات
System Information and Statistics
"""

import sys
import os
import sqlite3
from datetime import datetime

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 التحقق من المتطلبات:")
    print("-" * 30)
    
    # Python
    print(f"✅ Python: {sys.version}")
    
    # المكتبات المطلوبة
    required_modules = [
        ('PyQt5', 'PyQt5'),
        ('sqlite3', 'sqlite3'),
        ('bcrypt', 'bcrypt'),
        ('dateutil', 'python-dateutil')
    ]
    
    for module_name, package_name in required_modules:
        try:
            __import__(module_name)
            print(f"✅ {package_name}: مثبت")
        except ImportError:
            print(f"❌ {package_name}: غير مثبت")
    
    print()

def check_database():
    """التحقق من قاعدة البيانات"""
    print("🗄️ معلومات قاعدة البيانات:")
    print("-" * 30)
    
    db_path = "accounting_system.db"
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        print("💡 قم بتشغيل النظام لإنشاء قاعدة البيانات")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # حجم قاعدة البيانات
        size = os.path.getsize(db_path)
        print(f"📊 حجم قاعدة البيانات: {size:,} بايت ({size/1024:.1f} KB)")
        
        # الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"📋 عدد الجداول: {len(tables)}")
        
        # إحصائيات الجداول
        for (table_name,) in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   • {table_name}: {count} سجل")
        
        conn.close()
        print("✅ قاعدة البيانات جاهزة")
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
    
    print()

def show_sample_data():
    """عرض عينة من البيانات"""
    print("📋 عينة من البيانات:")
    print("-" * 30)
    
    db_path = "accounting_system.db"
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # المستخدمين
        print("👥 المستخدمين:")
        cursor.execute("SELECT username, full_name, role FROM users LIMIT 5")
        users = cursor.fetchall()
        for user in users:
            print(f"   • {user['username']} - {user['full_name']} ({user['role']})")
        
        print()
        
        # دليل الحسابات (أول 10)
        print("📊 دليل الحسابات (أول 10):")
        cursor.execute("SELECT account_code, account_name_ar, account_type FROM chart_of_accounts ORDER BY account_code LIMIT 10")
        accounts = cursor.fetchall()
        for account in accounts:
            print(f"   • {account['account_code']} - {account['account_name_ar']} ({account['account_type']})")
        
        print()
        
        # الإعدادات
        print("⚙️ الإعدادات:")
        cursor.execute("SELECT setting_key, setting_value FROM settings")
        settings = cursor.fetchall()
        for setting in settings:
            print(f"   • {setting['setting_key']}: {setting['setting_value']}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في عرض البيانات: {e}")
    
    print()

def show_file_structure():
    """عرض هيكل الملفات"""
    print("📁 هيكل المشروع:")
    print("-" * 30)
    
    def print_tree(path, prefix="", max_depth=3, current_depth=0):
        if current_depth >= max_depth:
            return
        
        try:
            items = sorted(os.listdir(path))
            for i, item in enumerate(items):
                if item.startswith('.'):
                    continue
                
                item_path = os.path.join(path, item)
                is_last = i == len(items) - 1
                
                current_prefix = "└── " if is_last else "├── "
                print(f"{prefix}{current_prefix}{item}")
                
                if os.path.isdir(item_path) and current_depth < max_depth - 1:
                    next_prefix = prefix + ("    " if is_last else "│   ")
                    print_tree(item_path, next_prefix, max_depth, current_depth + 1)
        except PermissionError:
            pass
    
    print_tree(".")
    print()

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🏢 نظام المحاسبة الشامل - معلومات النظام")
    print("📅 " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 50)
    print()
    
    check_requirements()
    check_database()
    show_sample_data()
    show_file_structure()
    
    print("=" * 50)
    print("💡 لتشغيل النظام:")
    print("   python simple_run.py    (مبسط)")
    print("   python run_full.py      (كامل)")
    print("   run.bat                 (Windows)")
    print("=" * 50)

if __name__ == "__main__":
    main()