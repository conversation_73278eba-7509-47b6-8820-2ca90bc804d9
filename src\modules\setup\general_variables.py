"""
نافذة إدارة المتغيرات العامة للنظام
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QTextEdit, QComboBox, QSpinBox,
                             QPushButton, QGroupBox, QTabWidget, QWidget,
                             QCheckBox, QDateEdit, QTimeEdit, QMessageBox,
                             QFileDialog, QFrame, QScrollArea)
from PyQt5.QtCore import Qt, QDate, QTime
from PyQt5.QtGui import QFont, QPixmap, QIcon
import os
import json


class GeneralVariablesDialog(QDialog):
    """نافذة إدارة المتغيرات العامة"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.init_ui()
        self.load_settings()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("🔧 المتغيرات العامة للنظام")
        self.setModal(True)
        self.resize(800, 600)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # إنشاء التبويبات
        self.tabs = QTabWidget()
        
        # تبويب معلومات الشركة
        self.company_tab = self.create_company_tab()
        self.tabs.addTab(self.company_tab, "🏢 معلومات الشركة")
        
        # تبويب إعدادات النظام
        self.system_tab = self.create_system_tab()
        self.tabs.addTab(self.system_tab, "⚙️ إعدادات النظام")
        
        # تبويب إعدادات العرض
        self.display_tab = self.create_display_tab()
        self.tabs.addTab(self.display_tab, "🖥️ إعدادات العرض")
        
        # تبويب إعدادات الأمان
        self.security_tab = self.create_security_tab()
        self.tabs.addTab(self.security_tab, "🔒 إعدادات الأمان")
        
        main_layout.addWidget(self.tabs)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("💾 حفظ")
        self.save_btn.clicked.connect(self.save_settings)
        
        self.reset_btn = QPushButton("🔄 استعادة الافتراضي")
        self.reset_btn.clicked.connect(self.reset_to_defaults)
        
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.reset_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)
        
        main_layout.addLayout(buttons_layout)
        self.setLayout(main_layout)
        
        # تطبيق الأنماط
        self.apply_styles()
    
    def create_company_tab(self):
        """إنشاء تبويب معلومات الشركة"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة المعلومات الأساسية
        basic_group = QGroupBox("📋 المعلومات الأساسية")
        basic_layout = QGridLayout()
        
        # اسم الشركة
        basic_layout.addWidget(QLabel("اسم الشركة:"), 0, 0)
        self.company_name = QLineEdit()
        self.company_name.setPlaceholderText("أدخل اسم الشركة")
        basic_layout.addWidget(self.company_name, 0, 1)
        
        # اسم الشركة بالإنجليزية
        basic_layout.addWidget(QLabel("اسم الشركة (إنجليزي):"), 1, 0)
        self.company_name_en = QLineEdit()
        self.company_name_en.setPlaceholderText("Company Name in English")
        basic_layout.addWidget(self.company_name_en, 1, 1)
        
        # نوع النشاط
        basic_layout.addWidget(QLabel("نوع النشاط:"), 2, 0)
        self.business_type = QComboBox()
        self.business_type.addItems([
            "تجاري", "صناعي", "خدمي", "استثماري", "مقاولات", 
            "استيراد وتصدير", "تقنية معلومات", "أخرى"
        ])
        basic_layout.addWidget(self.business_type, 2, 1)
        
        # الرقم الضريبي
        basic_layout.addWidget(QLabel("الرقم الضريبي:"), 3, 0)
        self.tax_number = QLineEdit()
        self.tax_number.setPlaceholderText("أدخل الرقم الضريبي")
        basic_layout.addWidget(self.tax_number, 3, 1)
        
        # السجل التجاري
        basic_layout.addWidget(QLabel("السجل التجاري:"), 4, 0)
        self.commercial_register = QLineEdit()
        self.commercial_register.setPlaceholderText("أدخل رقم السجل التجاري")
        basic_layout.addWidget(self.commercial_register, 4, 1)
        
        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)
        
        # مجموعة معلومات الاتصال
        contact_group = QGroupBox("📞 معلومات الاتصال")
        contact_layout = QGridLayout()
        
        # العنوان
        contact_layout.addWidget(QLabel("العنوان:"), 0, 0)
        self.address = QTextEdit()
        self.address.setMaximumHeight(80)
        self.address.setPlaceholderText("أدخل عنوان الشركة")
        contact_layout.addWidget(self.address, 0, 1)
        
        # الهاتف
        contact_layout.addWidget(QLabel("الهاتف:"), 1, 0)
        self.phone = QLineEdit()
        self.phone.setPlaceholderText("أدخل رقم الهاتف")
        contact_layout.addWidget(self.phone, 1, 1)
        
        # الفاكس
        contact_layout.addWidget(QLabel("الفاكس:"), 2, 0)
        self.fax = QLineEdit()
        self.fax.setPlaceholderText("أدخل رقم الفاكس")
        contact_layout.addWidget(self.fax, 2, 1)
        
        # البريد الإلكتروني
        contact_layout.addWidget(QLabel("البريد الإلكتروني:"), 3, 0)
        self.email = QLineEdit()
        self.email.setPlaceholderText("<EMAIL>")
        contact_layout.addWidget(self.email, 3, 1)
        
        # الموقع الإلكتروني
        contact_layout.addWidget(QLabel("الموقع الإلكتروني:"), 4, 0)
        self.website = QLineEdit()
        self.website.setPlaceholderText("www.company.com")
        contact_layout.addWidget(self.website, 4, 1)
        
        contact_group.setLayout(contact_layout)
        layout.addWidget(contact_group)
        
        # مجموعة الشعار
        logo_group = QGroupBox("🖼️ شعار الشركة")
        logo_layout = QHBoxLayout()
        
        self.logo_label = QLabel()
        self.logo_label.setFixedSize(150, 100)
        self.logo_label.setStyleSheet("border: 2px dashed #ccc; background: #f9f9f9;")
        self.logo_label.setAlignment(Qt.AlignCenter)
        self.logo_label.setText("لا يوجد شعار")
        
        logo_buttons_layout = QVBoxLayout()
        self.browse_logo_btn = QPushButton("📁 تصفح")
        self.browse_logo_btn.clicked.connect(self.browse_logo)
        self.remove_logo_btn = QPushButton("🗑️ إزالة")
        self.remove_logo_btn.clicked.connect(self.remove_logo)
        
        logo_buttons_layout.addWidget(self.browse_logo_btn)
        logo_buttons_layout.addWidget(self.remove_logo_btn)
        logo_buttons_layout.addStretch()
        
        logo_layout.addWidget(self.logo_label)
        logo_layout.addLayout(logo_buttons_layout)
        logo_layout.addStretch()
        
        logo_group.setLayout(logo_layout)
        layout.addWidget(logo_group)
        
        layout.addStretch()
        tab.setLayout(layout)
        return tab
    
    def create_system_tab(self):
        """إنشاء تبويب إعدادات النظام"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة الإعدادات العامة
        general_group = QGroupBox("⚙️ الإعدادات العامة")
        general_layout = QGridLayout()
        
        # العملة الافتراضية
        general_layout.addWidget(QLabel("العملة الافتراضية:"), 0, 0)
        self.default_currency = QComboBox()
        self.default_currency.addItems(["ريال يمني", "دولار أمريكي", "يورو", "ريال سعودي"])
        general_layout.addWidget(self.default_currency, 0, 1)
        
        # عدد الخانات العشرية
        general_layout.addWidget(QLabel("عدد الخانات العشرية:"), 1, 0)
        self.decimal_places = QSpinBox()
        self.decimal_places.setRange(0, 6)
        self.decimal_places.setValue(2)
        general_layout.addWidget(self.decimal_places, 1, 1)
        
        # تاريخ بداية السنة المالية
        general_layout.addWidget(QLabel("بداية السنة المالية:"), 2, 0)
        self.fiscal_year_start = QDateEdit()
        self.fiscal_year_start.setDate(QDate.currentDate())
        self.fiscal_year_start.setCalendarPopup(True)
        general_layout.addWidget(self.fiscal_year_start, 2, 1)
        
        # نوع التقويم
        general_layout.addWidget(QLabel("نوع التقويم:"), 3, 0)
        self.calendar_type = QComboBox()
        self.calendar_type.addItems(["ميلادي", "هجري", "مختلط"])
        general_layout.addWidget(self.calendar_type, 3, 1)
        
        general_group.setLayout(general_layout)
        layout.addWidget(general_group)
        
        # مجموعة إعدادات النسخ الاحتياطي
        backup_group = QGroupBox("💾 إعدادات النسخ الاحتياطي")
        backup_layout = QGridLayout()
        
        # تفعيل النسخ الاحتياطي التلقائي
        self.auto_backup = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        backup_layout.addWidget(self.auto_backup, 0, 0, 1, 2)
        
        # مجلد النسخ الاحتياطي
        backup_layout.addWidget(QLabel("مجلد النسخ الاحتياطي:"), 1, 0)
        backup_folder_layout = QHBoxLayout()
        self.backup_folder = QLineEdit()
        self.backup_folder.setPlaceholderText("اختر مجلد النسخ الاحتياطي")
        self.browse_backup_btn = QPushButton("📁")
        self.browse_backup_btn.clicked.connect(self.browse_backup_folder)
        backup_folder_layout.addWidget(self.backup_folder)
        backup_folder_layout.addWidget(self.browse_backup_btn)
        backup_layout.addLayout(backup_folder_layout, 1, 1)
        
        # تكرار النسخ الاحتياطي
        backup_layout.addWidget(QLabel("تكرار النسخ:"), 2, 0)
        self.backup_frequency = QComboBox()
        self.backup_frequency.addItems(["يومي", "أسبوعي", "شهري"])
        backup_layout.addWidget(self.backup_frequency, 2, 1)
        
        backup_group.setLayout(backup_layout)
        layout.addWidget(backup_group)
        
        layout.addStretch()
        tab.setLayout(layout)
        return tab
    
    def create_display_tab(self):
        """إنشاء تبويب إعدادات العرض"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إعدادات الواجهة
        interface_group = QGroupBox("🖥️ إعدادات الواجهة")
        interface_layout = QGridLayout()
        
        # اللغة
        interface_layout.addWidget(QLabel("اللغة:"), 0, 0)
        self.language = QComboBox()
        self.language.addItems(["العربية", "English"])
        interface_layout.addWidget(self.language, 0, 1)
        
        # السمة
        interface_layout.addWidget(QLabel("السمة:"), 1, 0)
        self.theme = QComboBox()
        self.theme.addItems(["فاتح", "داكن", "تلقائي"])
        interface_layout.addWidget(self.theme, 1, 1)
        
        # حجم الخط
        interface_layout.addWidget(QLabel("حجم الخط:"), 2, 0)
        self.font_size = QSpinBox()
        self.font_size.setRange(8, 24)
        self.font_size.setValue(10)
        interface_layout.addWidget(self.font_size, 2, 1)
        
        # عرض شريط الأدوات
        self.show_toolbar = QCheckBox("عرض شريط الأدوات")
        self.show_toolbar.setChecked(True)
        interface_layout.addWidget(self.show_toolbar, 3, 0, 1, 2)
        
        # عرض شريط الحالة
        self.show_statusbar = QCheckBox("عرض شريط الحالة")
        self.show_statusbar.setChecked(True)
        interface_layout.addWidget(self.show_statusbar, 4, 0, 1, 2)
        
        interface_group.setLayout(interface_layout)
        layout.addWidget(interface_group)
        
        layout.addStretch()
        tab.setLayout(layout)
        return tab
    
    def create_security_tab(self):
        """إنشاء تبويب إعدادات الأمان"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إعدادات كلمة المرور
        password_group = QGroupBox("🔒 إعدادات كلمة المرور")
        password_layout = QGridLayout()
        
        # الحد الأدنى لطول كلمة المرور
        password_layout.addWidget(QLabel("الحد الأدنى لطول كلمة المرور:"), 0, 0)
        self.min_password_length = QSpinBox()
        self.min_password_length.setRange(4, 20)
        self.min_password_length.setValue(8)
        password_layout.addWidget(self.min_password_length, 0, 1)
        
        # مدة انتهاء كلمة المرور (بالأيام)
        password_layout.addWidget(QLabel("مدة انتهاء كلمة المرور (أيام):"), 1, 0)
        self.password_expiry_days = QSpinBox()
        self.password_expiry_days.setRange(0, 365)
        self.password_expiry_days.setValue(90)
        self.password_expiry_days.setSpecialValueText("بدون انتهاء")
        password_layout.addWidget(self.password_expiry_days, 1, 1)
        
        # عدد محاولات تسجيل الدخول
        password_layout.addWidget(QLabel("عدد محاولات تسجيل الدخول:"), 2, 0)
        self.max_login_attempts = QSpinBox()
        self.max_login_attempts.setRange(1, 10)
        self.max_login_attempts.setValue(3)
        password_layout.addWidget(self.max_login_attempts, 2, 1)
        
        # مدة قفل الحساب (بالدقائق)
        password_layout.addWidget(QLabel("مدة قفل الحساب (دقائق):"), 3, 0)
        self.account_lockout_duration = QSpinBox()
        self.account_lockout_duration.setRange(1, 1440)
        self.account_lockout_duration.setValue(30)
        password_layout.addWidget(self.account_lockout_duration, 3, 1)
        
        password_group.setLayout(password_layout)
        layout.addWidget(password_group)
        
        # مجموعة إعدادات الجلسة
        session_group = QGroupBox("⏰ إعدادات الجلسة")
        session_layout = QGridLayout()
        
        # مدة انتهاء الجلسة (بالدقائق)
        session_layout.addWidget(QLabel("مدة انتهاء الجلسة (دقائق):"), 0, 0)
        self.session_timeout = QSpinBox()
        self.session_timeout.setRange(5, 480)
        self.session_timeout.setValue(60)
        session_layout.addWidget(self.session_timeout, 0, 1)
        
        # تحذير قبل انتهاء الجلسة
        self.session_warning = QCheckBox("تحذير قبل انتهاء الجلسة")
        self.session_warning.setChecked(True)
        session_layout.addWidget(self.session_warning, 1, 0, 1, 2)
        
        session_group.setLayout(session_layout)
        layout.addWidget(session_group)
        
        layout.addStretch()
        tab.setLayout(layout)
        return tab
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDateEdit {
                border: 1px solid #ced4da;
                border-radius: 4px;
                padding: 6px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDateEdit:focus {
                border-color: #007bff;
                outline: none;
            }
        """)
    
    def browse_logo(self):
        """تصفح شعار الشركة"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر شعار الشركة", "", 
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif)"
        )
        if file_path:
            pixmap = QPixmap(file_path)
            scaled_pixmap = pixmap.scaled(150, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.logo_label.setPixmap(scaled_pixmap)
            self.logo_path = file_path
    
    def remove_logo(self):
        """إزالة الشعار"""
        self.logo_label.clear()
        self.logo_label.setText("لا يوجد شعار")
        self.logo_path = None
    
    def browse_backup_folder(self):
        """تصفح مجلد النسخ الاحتياطي"""
        folder_path = QFileDialog.getExistingDirectory(
            self, "اختر مجلد النسخ الاحتياطي"
        )
        if folder_path:
            self.backup_folder.setText(folder_path)
    
    def load_settings(self):
        """تحميل الإعدادات المحفوظة"""
        # هنا يمكن تحميل الإعدادات من قاعدة البيانات
        # مؤقتاً سنضع قيم افتراضية
        self.company_name.setText("شركة أونيكس للبرمجيات")
        self.company_name_en.setText("Onyx Software Company")
        self.business_type.setCurrentText("تقنية معلومات")
        self.address.setText("صنعاء - اليمن")
        self.phone.setText("+967-1-123456")
        self.email.setText("<EMAIL>")
        self.website.setText("www.onyx-software.com")
    
    def save_settings(self):
        """حفظ الإعدادات"""
        # جمع البيانات
        settings = {
            'company_name': self.company_name.text(),
            'company_name_en': self.company_name_en.text(),
            'business_type': self.business_type.currentText(),
            'tax_number': self.tax_number.text(),
            'commercial_register': self.commercial_register.text(),
            'address': self.address.toPlainText(),
            'phone': self.phone.text(),
            'fax': self.fax.text(),
            'email': self.email.text(),
            'website': self.website.text(),
            'default_currency': self.default_currency.currentText(),
            'decimal_places': self.decimal_places.value(),
            'fiscal_year_start': self.fiscal_year_start.date().toString(),
            'calendar_type': self.calendar_type.currentText(),
            'auto_backup': self.auto_backup.isChecked(),
            'backup_folder': self.backup_folder.text(),
            'backup_frequency': self.backup_frequency.currentText(),
            'language': self.language.currentText(),
            'theme': self.theme.currentText(),
            'font_size': self.font_size.value(),
            'show_toolbar': self.show_toolbar.isChecked(),
            'show_statusbar': self.show_statusbar.isChecked(),
            'min_password_length': self.min_password_length.value(),
            'password_expiry_days': self.password_expiry_days.value(),
            'max_login_attempts': self.max_login_attempts.value(),
            'account_lockout_duration': self.account_lockout_duration.value(),
            'session_timeout': self.session_timeout.value(),
            'session_warning': self.session_warning.isChecked()
        }
        
        # هنا يمكن حفظ الإعدادات في قاعدة البيانات
        # مؤقتاً سنعرض رسالة نجاح
        QMessageBox.information(self, "تم الحفظ", "تم حفظ الإعدادات بنجاح!")
        self.accept()
    
    def reset_to_defaults(self):
        """استعادة الإعدادات الافتراضية"""
        reply = QMessageBox.question(
            self, "تأكيد الاستعادة",
            "هل أنت متأكد من استعادة الإعدادات الافتراضية؟\n"
            "سيتم فقدان جميع التغييرات الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إعادة تعيين القيم الافتراضية
            self.company_name.clear()
            self.company_name_en.clear()
            self.business_type.setCurrentIndex(0)
            self.tax_number.clear()
            self.commercial_register.clear()
            self.address.clear()
            self.phone.clear()
            self.fax.clear()
            self.email.clear()
            self.website.clear()
            self.default_currency.setCurrentIndex(0)
            self.decimal_places.setValue(2)
            self.fiscal_year_start.setDate(QDate.currentDate())
            self.calendar_type.setCurrentIndex(0)
            self.auto_backup.setChecked(False)
            self.backup_folder.clear()
            self.backup_frequency.setCurrentIndex(0)
            self.language.setCurrentIndex(0)
            self.theme.setCurrentIndex(0)
            self.font_size.setValue(10)
            self.show_toolbar.setChecked(True)
            self.show_statusbar.setChecked(True)
            self.min_password_length.setValue(8)
            self.password_expiry_days.setValue(90)
            self.max_login_attempts.setValue(3)
            self.account_lockout_duration.setValue(30)
            self.session_timeout.setValue(60)
            self.session_warning.setChecked(True)
            self.remove_logo()
