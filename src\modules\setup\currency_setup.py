#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة تهيئة العملات
Currency Setup Screen
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QMessageBox, 
                             QFrame, QSplitter, QDateEdit)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

class CurrencySetupDialog(QDialog):
    """شاشة تهيئة العملات"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تهيئة العملات - تعريف العملات وأسعار الصرف")
        self.setGeometry(150, 50, 1000, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان الشاشة
        title_label = QLabel("💱 تهيئة العملات وأسعار الصرف")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تقسيم الشاشة
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - إدارة العملات
        left_panel = self.create_currency_management_panel()
        splitter.addWidget(left_panel)
        
        # الجانب الأيمن - أسعار الصرف
        right_panel = self.create_exchange_rates_panel()
        splitter.addWidget(right_panel)
        
        # تحديد نسب التقسيم
        splitter.setSizes([400, 600])
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(splitter)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_currency_management_panel(self):
        """إنشاء لوحة إدارة العملات"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # عنوان القسم
        section_title = QLabel("💰 إدارة العملات")
        section_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                padding: 10px;
                background-color: #d5dbdb;
                border-radius: 5px;
                margin-bottom: 15px;
            }
        """)
        section_title.setAlignment(Qt.AlignCenter)
        
        # نموذج إضافة عملة جديدة
        currency_form = QGroupBox("➕ إضافة عملة جديدة")
        form_layout = QGridLayout()
        
        # اسم العملة
        form_layout.addWidget(QLabel("اسم العملة:"), 0, 0)
        self.currency_name = QLineEdit()
        self.currency_name.setPlaceholderText("مثال: دولار أمريكي")
        form_layout.addWidget(self.currency_name, 0, 1)
        
        # رمز العملة
        form_layout.addWidget(QLabel("رمز العملة:"), 1, 0)
        self.currency_code = QLineEdit()
        self.currency_code.setPlaceholderText("مثال: USD")
        self.currency_code.setMaxLength(3)
        form_layout.addWidget(self.currency_code, 1, 1)
        
        # رمز العرض
        form_layout.addWidget(QLabel("رمز العرض:"), 2, 0)
        self.currency_symbol = QLineEdit()
        self.currency_symbol.setPlaceholderText("مثال: $")
        self.currency_symbol.setMaxLength(5)
        form_layout.addWidget(self.currency_symbol, 2, 1)
        
        # عدد الكسور العشرية
        form_layout.addWidget(QLabel("عدد الكسور العشرية:"), 3, 0)
        self.decimal_places = QSpinBox()
        self.decimal_places.setRange(0, 6)
        self.decimal_places.setValue(2)
        form_layout.addWidget(self.decimal_places, 3, 1)
        
        # العملة الأساسية
        self.is_base_currency = QCheckBox("عملة أساسية")
        form_layout.addWidget(self.is_base_currency, 4, 0, 1, 2)
        
        # العملة نشطة
        self.is_active = QCheckBox("عملة نشطة")
        self.is_active.setChecked(True)
        form_layout.addWidget(self.is_active, 5, 0, 1, 2)
        
        currency_form.setLayout(form_layout)
        
        # أزرار إدارة العملة
        currency_buttons = QHBoxLayout()
        
        add_currency_btn = QPushButton("➕ إضافة")
        add_currency_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_currency_btn.clicked.connect(self.add_currency)
        
        edit_currency_btn = QPushButton("✏️ تعديل")
        edit_currency_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_currency_btn.clicked.connect(self.edit_currency)
        
        delete_currency_btn = QPushButton("🗑️ حذف")
        delete_currency_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_currency_btn.clicked.connect(self.delete_currency)
        
        currency_buttons.addWidget(add_currency_btn)
        currency_buttons.addWidget(edit_currency_btn)
        currency_buttons.addWidget(delete_currency_btn)
        currency_buttons.addStretch()
        
        # جدول العملات المعرفة
        currencies_table_label = QLabel("📋 العملات المعرفة")
        currencies_table_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)
        
        self.currencies_table = QTableWidget()
        self.currencies_table.setColumnCount(5)
        self.currencies_table.setHorizontalHeaderLabels([
            "اسم العملة", "الرمز", "رمز العرض", "الكسور", "الحالة"
        ])
        
        # تنسيق الجدول
        self.currencies_table.horizontalHeader().setStretchLastSection(True)
        self.currencies_table.setAlternatingRowColors(True)
        self.currencies_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.currencies_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        layout.addWidget(section_title)
        layout.addWidget(currency_form)
        layout.addLayout(currency_buttons)
        layout.addWidget(currencies_table_label)
        layout.addWidget(self.currencies_table)
        
        panel.setLayout(layout)
        return panel
        
    def create_exchange_rates_panel(self):
        """إنشاء لوحة أسعار الصرف"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # عنوان القسم
        section_title = QLabel("📈 أسعار الصرف")
        section_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                padding: 10px;
                background-color: #d5dbdb;
                border-radius: 5px;
                margin-bottom: 15px;
            }
        """)
        section_title.setAlignment(Qt.AlignCenter)
        
        # نموذج إضافة سعر صرف
        rate_form = QGroupBox("💱 إضافة/تحديث سعر صرف")
        form_layout = QGridLayout()
        
        # العملة المصدر
        form_layout.addWidget(QLabel("من العملة:"), 0, 0)
        self.from_currency = QComboBox()
        form_layout.addWidget(self.from_currency, 0, 1)
        
        # العملة الهدف
        form_layout.addWidget(QLabel("إلى العملة:"), 1, 0)
        self.to_currency = QComboBox()
        form_layout.addWidget(self.to_currency, 1, 1)
        
        # سعر الصرف
        form_layout.addWidget(QLabel("سعر الصرف:"), 2, 0)
        self.exchange_rate = QDoubleSpinBox()
        self.exchange_rate.setRange(0.0001, 999999.9999)
        self.exchange_rate.setDecimals(4)
        self.exchange_rate.setValue(1.0000)
        form_layout.addWidget(self.exchange_rate, 2, 1)
        
        # تاريخ السعر
        form_layout.addWidget(QLabel("تاريخ السعر:"), 3, 0)
        self.rate_date = QDateEdit()
        self.rate_date.setDate(QDate.currentDate())
        self.rate_date.setCalendarPopup(True)
        form_layout.addWidget(self.rate_date, 3, 1)
        
        # ملاحظات
        form_layout.addWidget(QLabel("ملاحظات:"), 4, 0)
        self.rate_notes = QLineEdit()
        self.rate_notes.setPlaceholderText("ملاحظات اختيارية")
        form_layout.addWidget(self.rate_notes, 4, 1)
        
        rate_form.setLayout(form_layout)
        
        # أزرار إدارة أسعار الصرف
        rate_buttons = QHBoxLayout()
        
        add_rate_btn = QPushButton("💱 إضافة سعر")
        add_rate_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_rate_btn.clicked.connect(self.add_exchange_rate)
        
        update_rate_btn = QPushButton("🔄 تحديث سعر")
        update_rate_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        update_rate_btn.clicked.connect(self.update_exchange_rate)
        
        delete_rate_btn = QPushButton("🗑️ حذف سعر")
        delete_rate_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_rate_btn.clicked.connect(self.delete_exchange_rate)
        
        rate_buttons.addWidget(add_rate_btn)
        rate_buttons.addWidget(update_rate_btn)
        rate_buttons.addWidget(delete_rate_btn)
        rate_buttons.addStretch()
        
        # جدول أسعار الصرف
        rates_table_label = QLabel("📊 أسعار الصرف الحالية")
        rates_table_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """)
        
        self.rates_table = QTableWidget()
        self.rates_table.setColumnCount(6)
        self.rates_table.setHorizontalHeaderLabels([
            "من العملة", "إلى العملة", "السعر", "التاريخ", "ملاحظات", "آخر تحديث"
        ])
        
        # تنسيق الجدول
        self.rates_table.horizontalHeader().setStretchLastSection(True)
        self.rates_table.setAlternatingRowColors(True)
        self.rates_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.rates_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        layout.addWidget(section_title)
        layout.addWidget(rate_form)
        layout.addLayout(rate_buttons)
        layout.addWidget(rates_table_label)
        layout.addWidget(self.rates_table)
        
        panel.setLayout(layout)
        return panel
        
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر الحفظ
        save_btn = QPushButton("💾 حفظ جميع التغييرات")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_all_changes)
        
        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        # زر تحديث الأسعار من الإنترنت
        update_online_btn = QPushButton("🌐 تحديث من الإنترنت")
        update_online_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        update_online_btn.clicked.connect(self.update_rates_online)
        
        layout.addStretch()
        layout.addWidget(update_online_btn)
        layout.addWidget(save_btn)
        layout.addWidget(cancel_btn)
        
        return layout
        
    def load_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            # تحميل العملات الافتراضية
            default_currencies = [
                ("ريال يمني", "YER", "ر.ي", 2, True, True),
                ("دولار أمريكي", "USD", "$", 2, False, True),
                ("يورو", "EUR", "€", 2, False, True),
                ("ريال سعودي", "SAR", "ر.س", 2, False, True),
                ("درهم إماراتي", "AED", "د.إ", 2, False, True)
            ]
            
            self.currencies_table.setRowCount(len(default_currencies))
            for row, (name, code, symbol, decimals, is_base, is_active) in enumerate(default_currencies):
                self.currencies_table.setItem(row, 0, QTableWidgetItem(name))
                self.currencies_table.setItem(row, 1, QTableWidgetItem(code))
                self.currencies_table.setItem(row, 2, QTableWidgetItem(symbol))
                self.currencies_table.setItem(row, 3, QTableWidgetItem(str(decimals)))
                status = "أساسية" if is_base else "نشطة" if is_active else "غير نشطة"
                self.currencies_table.setItem(row, 4, QTableWidgetItem(status))
            
            # تحديث قوائم العملات في أسعار الصرف
            self.update_currency_combos()
            
            # تحميل أسعار صرف افتراضية
            self.load_sample_exchange_rates()
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            
    def update_currency_combos(self):
        """تحديث قوائم العملات"""
        self.from_currency.clear()
        self.to_currency.clear()
        
        for row in range(self.currencies_table.rowCount()):
            currency_name = self.currencies_table.item(row, 0).text()
            currency_code = self.currencies_table.item(row, 1).text()
            display_text = f"{currency_name} ({currency_code})"
            
            self.from_currency.addItem(display_text)
            self.to_currency.addItem(display_text)
            
    def load_sample_exchange_rates(self):
        """تحميل أسعار صرف نموذجية"""
        sample_rates = [
            ("ريال يمني (YER)", "دولار أمريكي (USD)", "0.0040", "2024-01-15", "السعر الرسمي", "2024-01-15 10:30"),
            ("دولار أمريكي (USD)", "ريال يمني (YER)", "250.0000", "2024-01-15", "السعر الرسمي", "2024-01-15 10:30"),
            ("ريال سعودي (SAR)", "ريال يمني (YER)", "66.6667", "2024-01-15", "تقديري", "2024-01-15 10:30"),
            ("يورو (EUR)", "دولار أمريكي (USD)", "1.0850", "2024-01-15", "السوق الأوروبي", "2024-01-15 10:30")
        ]
        
        self.rates_table.setRowCount(len(sample_rates))
        for row, (from_curr, to_curr, rate, date, notes, updated) in enumerate(sample_rates):
            self.rates_table.setItem(row, 0, QTableWidgetItem(from_curr))
            self.rates_table.setItem(row, 1, QTableWidgetItem(to_curr))
            self.rates_table.setItem(row, 2, QTableWidgetItem(rate))
            self.rates_table.setItem(row, 3, QTableWidgetItem(date))
            self.rates_table.setItem(row, 4, QTableWidgetItem(notes))
            self.rates_table.setItem(row, 5, QTableWidgetItem(updated))
            
    def add_currency(self):
        """إضافة عملة جديدة"""
        try:
            # التحقق من صحة البيانات
            if not self.currency_name.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم العملة")
                return
                
            if not self.currency_code.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز العملة")
                return
                
            # إضافة العملة للجدول
            row = self.currencies_table.rowCount()
            self.currencies_table.insertRow(row)
            
            self.currencies_table.setItem(row, 0, QTableWidgetItem(self.currency_name.text()))
            self.currencies_table.setItem(row, 1, QTableWidgetItem(self.currency_code.text().upper()))
            self.currencies_table.setItem(row, 2, QTableWidgetItem(self.currency_symbol.text()))
            self.currencies_table.setItem(row, 3, QTableWidgetItem(str(self.decimal_places.value())))
            
            status = "أساسية" if self.is_base_currency.isChecked() else "نشطة" if self.is_active.isChecked() else "غير نشطة"
            self.currencies_table.setItem(row, 4, QTableWidgetItem(status))
            
            # تنظيف النموذج
            self.clear_currency_form()
            
            # تحديث قوائم العملات
            self.update_currency_combos()
            
            QMessageBox.information(self, "نجح", "تم إضافة العملة بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة العملة:\n{str(e)}")
            
    def edit_currency(self):
        """تعديل عملة محددة"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة للتعديل")
            return
            
        # ملء النموذج ببيانات العملة المحددة
        self.currency_name.setText(self.currencies_table.item(current_row, 0).text())
        self.currency_code.setText(self.currencies_table.item(current_row, 1).text())
        self.currency_symbol.setText(self.currencies_table.item(current_row, 2).text())
        self.decimal_places.setValue(int(self.currencies_table.item(current_row, 3).text()))
        
        status = self.currencies_table.item(current_row, 4).text()
        self.is_base_currency.setChecked(status == "أساسية")
        self.is_active.setChecked(status in ["أساسية", "نشطة"])
        
    def delete_currency(self):
        """حذف عملة محددة"""
        current_row = self.currencies_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عملة للحذف")
            return
            
        currency_name = self.currencies_table.item(current_row, 0).text()
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   f"هل تريد حذف العملة '{currency_name}'؟\n"
                                   f"سيتم حذف جميع أسعار الصرف المرتبطة بها أيضاً",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.currencies_table.removeRow(current_row)
            self.update_currency_combos()
            QMessageBox.information(self, "تم", "تم حذف العملة بنجاح")
            
    def clear_currency_form(self):
        """تنظيف نموذج العملة"""
        self.currency_name.clear()
        self.currency_code.clear()
        self.currency_symbol.clear()
        self.decimal_places.setValue(2)
        self.is_base_currency.setChecked(False)
        self.is_active.setChecked(True)
        
    def add_exchange_rate(self):
        """إضافة سعر صرف جديد"""
        try:
            if self.from_currency.currentText() == self.to_currency.currentText():
                QMessageBox.warning(self, "تحذير", "لا يمكن أن تكون العملة المصدر والهدف نفسها")
                return
                
            # إضافة السعر للجدول
            row = self.rates_table.rowCount()
            self.rates_table.insertRow(row)
            
            self.rates_table.setItem(row, 0, QTableWidgetItem(self.from_currency.currentText()))
            self.rates_table.setItem(row, 1, QTableWidgetItem(self.to_currency.currentText()))
            self.rates_table.setItem(row, 2, QTableWidgetItem(f"{self.exchange_rate.value():.4f}"))
            self.rates_table.setItem(row, 3, QTableWidgetItem(self.rate_date.date().toString('yyyy-MM-dd')))
            self.rates_table.setItem(row, 4, QTableWidgetItem(self.rate_notes.text()))
            
            from datetime import datetime
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
            self.rates_table.setItem(row, 5, QTableWidgetItem(current_time))
            
            # تنظيف النموذج
            self.exchange_rate.setValue(1.0000)
            self.rate_notes.clear()
            
            QMessageBox.information(self, "نجح", "تم إضافة سعر الصرف بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة سعر الصرف:\n{str(e)}")
            
    def update_exchange_rate(self):
        """تحديث سعر صرف محدد"""
        current_row = self.rates_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار سعر صرف للتحديث")
            return
            
        # تحديث السعر
        self.rates_table.setItem(current_row, 2, QTableWidgetItem(f"{self.exchange_rate.value():.4f}"))
        self.rates_table.setItem(current_row, 3, QTableWidgetItem(self.rate_date.date().toString('yyyy-MM-dd')))
        self.rates_table.setItem(current_row, 4, QTableWidgetItem(self.rate_notes.text()))
        
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        self.rates_table.setItem(current_row, 5, QTableWidgetItem(current_time))
        
        QMessageBox.information(self, "تم", "تم تحديث سعر الصرف بنجاح")
        
    def delete_exchange_rate(self):
        """حذف سعر صرف محدد"""
        current_row = self.rates_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار سعر صرف للحذف")
            return
            
        from_currency = self.rates_table.item(current_row, 0).text()
        to_currency = self.rates_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   f"هل تريد حذف سعر الصرف من {from_currency} إلى {to_currency}؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.rates_table.removeRow(current_row)
            QMessageBox.information(self, "تم", "تم حذف سعر الصرف بنجاح")
            
    def update_rates_online(self):
        """تحديث أسعار الصرف من الإنترنت"""
        QMessageBox.information(self, "قيد التطوير", 
                               "🌐 ميزة التحديث من الإنترنت قيد التطوير\n"
                               "سيتم إضافتها في الإصدارات القادمة")
        
    def save_all_changes(self):
        """حفظ جميع التغييرات"""
        try:
            # جمع بيانات العملات
            currencies_data = []
            for row in range(self.currencies_table.rowCount()):
                currency = {
                    'name': self.currencies_table.item(row, 0).text(),
                    'code': self.currencies_table.item(row, 1).text(),
                    'symbol': self.currencies_table.item(row, 2).text(),
                    'decimals': int(self.currencies_table.item(row, 3).text()),
                    'status': self.currencies_table.item(row, 4).text()
                }
                currencies_data.append(currency)
            
            # جمع بيانات أسعار الصرف
            rates_data = []
            for row in range(self.rates_table.rowCount()):
                rate = {
                    'from_currency': self.rates_table.item(row, 0).text(),
                    'to_currency': self.rates_table.item(row, 1).text(),
                    'rate': float(self.rates_table.item(row, 2).text()),
                    'date': self.rates_table.item(row, 3).text(),
                    'notes': self.rates_table.item(row, 4).text(),
                    'updated': self.rates_table.item(row, 5).text()
                }
                rates_data.append(rate)
            
            # حفظ في قاعدة البيانات
            # هنا يتم حفظ البيانات في قاعدة البيانات
            
            summary = f"""✅ تم حفظ تهيئة العملات بنجاح!

💰 العملات المحفوظة: {len(currencies_data)}
💱 أسعار الصرف المحفوظة: {len(rates_data)}

📋 العملات النشطة:"""
            
            for currency in currencies_data:
                if currency['status'] in ['أساسية', 'نشطة']:
                    summary += f"\n   • {currency['name']} ({currency['code']})"
            
            QMessageBox.information(self, "نجح الحفظ", summary)
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ البيانات:\n{str(e)}")


def main():
    """اختبار الشاشة"""
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass
    
    dialog = CurrencySetupDialog(MockDBManager())
    dialog.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
