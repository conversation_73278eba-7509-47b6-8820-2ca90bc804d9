#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وحدة خصوصية المستخدم
User Privacy Module Test
"""

import sys
import os
from datetime import datetime

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_user_privacy_module():
    """اختبار وحدة خصوصية المستخدم"""
    print("🔒 اختبار وحدة خصوصية المستخدم...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.user_privacy_module import UserPrivacyWidget, PasswordChangeDialog, DataExportDialog
        from database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إنشاء الوحدة الرئيسية
        privacy_widget = UserPrivacyWidget(db_manager, current_user_id=1)
        print("   ✅ تم إنشاء وحدة خصوصية المستخدم بنجاح")
        
        # اختبار إنشاء حوار تغيير كلمة المرور
        password_dialog = PasswordChangeDialog(db_manager, user_id=1)
        print("   ✅ تم إنشاء حوار تغيير كلمة المرور بنجاح")
        
        # اختبار إنشاء حوار تصدير البيانات
        export_dialog = DataExportDialog(db_manager, user_id=1)
        print("   ✅ تم إنشاء حوار تصدير البيانات بنجاح")
        
        # اختبار التبويبات
        if hasattr(privacy_widget, 'tabs'):
            tab_count = privacy_widget.tabs.count()
            print(f"   ✅ عدد التبويبات: {tab_count}")
            
            # اختبار أسماء التبويبات
            expected_tabs = [
                "🔒 إعدادات الخصوصية",
                "🔐 كلمة المرور", 
                "📊 البيانات الشخصية",
                "📋 سجل النشاطات"
            ]
            
            for i, expected_tab in enumerate(expected_tabs):
                if i < tab_count:
                    actual_tab = privacy_widget.tabs.tabText(i)
                    if expected_tab in actual_tab:
                        print(f"   ✅ تبويب {expected_tab} موجود")
                    else:
                        print(f"   ⚠️ تبويب {expected_tab} غير موجود")
        
        # اختبار الوظائف الأساسية
        essential_functions = [
            'load_privacy_settings',
            'save_privacy_settings',
            'change_password',
            'export_personal_data',
            'load_activity_log'
        ]
        
        for func_name in essential_functions:
            if hasattr(privacy_widget, func_name):
                print(f"   ✅ دالة {func_name} موجودة")
            else:
                print(f"   ❌ دالة {func_name} غير موجودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار وحدة خصوصية المستخدم: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_columns():
    """اختبار أعمدة قاعدة البيانات المطلوبة"""
    print("\n🗄️ اختبار أعمدة قاعدة البيانات...")
    
    try:
        import sqlite3
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect("accounting_system.db")
        cursor = conn.cursor()
        
        # فحص أعمدة جدول المستخدمين
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        
        required_columns = [
            'two_factor_enabled',
            'password_expiry_days',
            'auto_logout_minutes',
            'data_collection_enabled',
            'analytics_enabled',
            'backup_enabled',
            'login_notifications',
            'security_notifications',
            'system_notifications',
            'password_changed_at'
        ]
        
        existing_columns = [col[1] for col in columns]
        
        for col_name in required_columns:
            if col_name in existing_columns:
                print(f"   ✅ عمود {col_name} موجود")
            else:
                print(f"   ❌ عمود {col_name} غير موجود")
        
        conn.close()
        return len([col for col in required_columns if col in existing_columns]) >= 8
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_privacy_operations():
    """اختبار العمليات الخاصة بالخصوصية"""
    print("\n🔧 اختبار عمليات الخصوصية...")
    
    try:
        from database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار تحديث إعدادات الخصوصية
        try:
            update_query = """
                UPDATE users SET 
                    two_factor_enabled = ?,
                    password_expiry_days = ?,
                    data_collection_enabled = ?,
                    updated_at = ?
                WHERE id = ?
            """
            
            db_manager.execute_update(update_query, (
                True,
                90,
                False,
                datetime.now().isoformat(),
                1
            ))
            print("   ✅ تحديث إعدادات الخصوصية يعمل")
            
        except Exception as e:
            print(f"   ⚠️ تعذر تحديث إعدادات الخصوصية: {e}")
        
        # اختبار قراءة إعدادات المستخدم
        try:
            user_query = """
                SELECT two_factor_enabled, password_expiry_days, 
                       data_collection_enabled, login_notifications
                FROM users WHERE id = ?
            """
            
            result = db_manager.execute_query(user_query, (1,))
            if result:
                print("   ✅ قراءة إعدادات المستخدم تعمل")
                settings = result[0]
                print(f"   📊 المصادقة الثنائية: {settings[0]}")
                print(f"   📊 انتهاء كلمة المرور: {settings[1]} يوم")
                print(f"   📊 جمع البيانات: {settings[2]}")
                print(f"   📊 إشعارات الدخول: {settings[3]}")
            else:
                print("   ⚠️ لم يتم العثور على بيانات المستخدم")
                
        except Exception as e:
            print(f"   ❌ خطأ في قراءة إعدادات المستخدم: {e}")
        
        # اختبار سجل النشاطات
        try:
            activity_query = """
                SELECT COUNT(*) FROM activity_log WHERE user_id = ?
            """
            
            result = db_manager.execute_query(activity_query, (1,))
            if result:
                activity_count = result[0][0]
                print(f"   📋 عدد النشاطات المسجلة: {activity_count}")
            
        except Exception as e:
            print(f"   ⚠️ تعذر قراءة سجل النشاطات: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار عمليات الخصوصية: {e}")
        return False

def test_main_system_integration():
    """اختبار تكامل خصوصية المستخدم مع النظام الرئيسي"""
    print("\n🔗 اختبار تكامل خصوصية المستخدم مع النظام الرئيسي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        # اختبار النظام الرئيسي
        from main_system import MainSystemWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainSystemWindow()
        print("   ✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار وجود دالة خصوصية المستخدم
        if hasattr(main_window, 'open_user_privacy'):
            print("   ✅ دالة open_user_privacy موجودة")
        else:
            print("   ❌ دالة open_user_privacy غير موجودة")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التكامل: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_password_security():
    """اختبار أمان كلمات المرور"""
    print("\n🔐 اختبار أمان كلمات المرور...")
    
    try:
        import hashlib
        
        # اختبار تشفير كلمة المرور
        test_password = "TestPassword123!"
        hashed = hashlib.sha256(test_password.encode()).hexdigest()
        
        print(f"   ✅ تشفير كلمة المرور يعمل")
        print(f"   🔒 طول الهاش: {len(hashed)} حرف")
        
        # اختبار التحقق من كلمة المرور
        verification_hash = hashlib.sha256(test_password.encode()).hexdigest()
        if hashed == verification_hash:
            print("   ✅ التحقق من كلمة المرور يعمل")
        else:
            print("   ❌ فشل في التحقق من كلمة المرور")
            return False
        
        # اختبار قوة كلمة المرور
        def check_password_strength(password):
            strength = 0
            if len(password) >= 8:
                strength += 25
            if any(c.isupper() for c in password):
                strength += 25
            if any(c.islower() for c in password):
                strength += 25
            if any(c.isdigit() for c in password):
                strength += 25
            return strength
        
        strength = check_password_strength(test_password)
        print(f"   💪 قوة كلمة المرور: {strength}%")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار أمان كلمات المرور: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار وحدة خصوصية المستخدم")
    print("=" * 70)
    
    tests = [
        ("وحدة خصوصية المستخدم", test_user_privacy_module),
        ("أعمدة قاعدة البيانات", test_database_columns),
        ("عمليات الخصوصية", test_privacy_operations),
        ("أمان كلمات المرور", test_password_security),
        ("تكامل النظام الرئيسي", test_main_system_integration),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم تفعيل وحدة خصوصية المستخدم بنجاح!")
        print("✅ جميع الوظائف تعمل بدون أخطاء")
        
        print("\n🔧 الوظائف المتاحة:")
        print("   🔒 إعدادات الخصوصية - مفعلة ✅")
        print("   🔐 تغيير كلمة المرور - مفعلة ✅")
        print("   📊 إدارة البيانات الشخصية - مفعلة ✅")
        print("   📋 سجل النشاطات - مفعل ✅")
        print("   📤 تصدير البيانات - مفعل ✅")
        print("   🛡️ أمان متقدم - مفعل ✅")
        
        print("\n🚀 كيفية الاستخدام:")
        print("   1. شغل النظام: python src/main_system.py")
        print("   2. انقر على 'إدارة النظام' في الشجرة")
        print("   3. اختر 'خصوصية المستخدم'")
        print("   4. استخدم التبويبات المختلفة:")
        print("      - إعدادات الخصوصية: للتحكم في الخصوصية")
        print("      - كلمة المرور: لتغيير كلمة المرور")
        print("      - البيانات الشخصية: لإدارة البيانات")
        print("      - سجل النشاطات: لمراجعة النشاطات")
        
    else:
        print("\n⚠️ بعض وظائف خصوصية المستخدم تحتاج إلى إصلاح")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
