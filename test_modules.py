#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الوحدات المطورة
Test Developed Modules
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modules_import():
    """اختبار استيراد الوحدات"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        from src.modules.accounts_module import AccountsManagementWidget
        print("✅ وحدة إدارة الحسابات: تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ وحدة إدارة الحسابات: فشل الاستيراد - {e}")
        return False
    
    try:
        from src.modules.journal_entries_module import JournalEntriesWidget
        print("✅ وحدة القيود اليومية: تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ وحدة القيود اليومية: فشل الاستيراد - {e}")
        return False
    
    try:
        from src.modules.reports_module import ReportsWidget
        print("✅ وحدة التقارير: تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ وحدة التقارير: فشل الاستيراد - {e}")
        return False
    
    return True

def test_database_integration():
    """اختبار التكامل مع قاعدة البيانات"""
    print("\n🗄️ اختبار التكامل مع قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager()
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # اختبار الحسابات
        accounts = db_manager.execute_query("SELECT COUNT(*) as count FROM chart_of_accounts")
        if accounts and accounts[0]['count'] > 0:
            print(f"✅ دليل الحسابات: {accounts[0]['count']} حساب")
        else:
            print("❌ دليل الحسابات فارغ")
            return False
        
        # اختبار القيود
        entries = db_manager.execute_query("SELECT COUNT(*) as count FROM journal_entries")
        print(f"✅ القيود اليومية: {entries[0]['count'] if entries else 0} قيد")
        
        # اختبار المستخدمين
        users = db_manager.execute_query("SELECT COUNT(*) as count FROM users")
        if users and users[0]['count'] > 0:
            print(f"✅ المستخدمين: {users[0]['count']} مستخدم")
        else:
            print("❌ لا يوجد مستخدمين")
            return False
        
        db_manager.disconnect()
        print("✅ تم إغلاق الاتصال بقاعدة البيانات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_widgets_creation():
    """اختبار إنشاء الويدجتات"""
    print("\n🖥️ اختبار إنشاء الويدجتات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.accounts_module import AccountsManagementWidget
        from src.modules.journal_entries_module import JournalEntriesWidget
        from src.modules.reports_module import ReportsWidget
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار وحدة إدارة الحسابات
        accounts_widget = AccountsManagementWidget(db_manager)
        print("✅ تم إنشاء ويدجت إدارة الحسابات")
        
        # اختبار وحدة القيود اليومية
        journal_widget = JournalEntriesWidget(db_manager)
        print("✅ تم إنشاء ويدجت القيود اليومية")
        
        # اختبار وحدة التقارير
        reports_widget = ReportsWidget(db_manager)
        print("✅ تم إنشاء ويدجت التقارير")
        
        # تنظيف الذاكرة
        accounts_widget.close()
        journal_widget.close()
        reports_widget.close()
        db_manager.disconnect()
        app.quit()
        
        print("✅ تم إغلاق جميع الويدجتات بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الويدجتات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_functionality():
    """اختبار الوظائف الأساسية"""
    print("\n⚙️ اختبار الوظائف الأساسية...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager()
        
        # اختبار إضافة حساب جديد
        test_account_code = "9999"
        test_account_name = "حساب اختبار"
        
        # التحقق من عدم وجود الحساب
        existing = db_manager.execute_query(
            "SELECT COUNT(*) as count FROM chart_of_accounts WHERE account_code = ?", 
            (test_account_code,)
        )
        
        if existing and existing[0]['count'] == 0:
            # إضافة حساب اختبار
            db_manager.execute_update("""
                INSERT INTO chart_of_accounts 
                (account_code, account_name_ar, account_name_en, account_type, level, is_active)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (test_account_code, test_account_name, "Test Account", "asset", 1, True))
            
            print("✅ تم إضافة حساب اختبار")
            
            # التحقق من الإضافة
            added = db_manager.execute_query(
                "SELECT * FROM chart_of_accounts WHERE account_code = ?", 
                (test_account_code,)
            )
            
            if added:
                print("✅ تم التحقق من إضافة الحساب")
                
                # حذف حساب الاختبار
                db_manager.execute_update(
                    "DELETE FROM chart_of_accounts WHERE account_code = ?", 
                    (test_account_code,)
                )
                print("✅ تم حذف حساب الاختبار")
            else:
                print("❌ فشل في التحقق من إضافة الحساب")
                return False
        else:
            print("⚠️ حساب الاختبار موجود مسبقاً")
        
        # اختبار إضافة قيد يومي
        test_entry_description = "قيد اختبار"
        
        # إضافة قيد اختبار
        db_manager.execute_update("""
            INSERT INTO journal_entries 
            (entry_date, description, total_amount, created_by, is_posted)
            VALUES (?, ?, ?, ?, ?)
        """, ("2025-01-01", test_entry_description, 1000.00, 1, False))
        
        entry_id = db_manager.get_last_insert_id()
        print(f"✅ تم إضافة قيد اختبار برقم {entry_id}")
        
        # إضافة تفاصيل القيد
        db_manager.execute_update("""
            INSERT INTO journal_entry_details 
            (journal_entry_id, account_code, description, debit_amount, credit_amount)
            VALUES (?, ?, ?, ?, ?)
        """, (entry_id, "1110", "اختبار مدين", 1000.00, 0.00))
        
        db_manager.execute_update("""
            INSERT INTO journal_entry_details 
            (journal_entry_id, account_code, description, debit_amount, credit_amount)
            VALUES (?, ?, ?, ?, ?)
        """, (entry_id, "2110", "اختبار دائن", 0.00, 1000.00))
        
        print("✅ تم إضافة تفاصيل القيد")
        
        # حذف قيد الاختبار
        db_manager.execute_update("DELETE FROM journal_entry_details WHERE journal_entry_id = ?", (entry_id,))
        db_manager.execute_update("DELETE FROM journal_entries WHERE id = ?", (entry_id,))
        print("✅ تم حذف قيد الاختبار")
        
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الوظائف: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_full_system_test():
    """تشغيل النظام الكامل للاختبار"""
    print("\n🚀 اختبار تشغيل النظام الكامل...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt, QTimer
        from PyQt5.QtGui import QFont
        from src.ui.onyx_login_window import OnyxLoginWindow
        
        print("جاري تشغيل نظام Onyx ERP للاختبار...")
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setApplicationName("Onyx ERP - Test")
        app.setApplicationVersion("1.0.0")
        
        # تعيين الخط والاتجاه
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء نافذة تسجيل الدخول
        login_window = OnyxLoginWindow()
        
        print("✅ تم إنشاء نافذة تسجيل الدخول")
        
        # إغلاق النافذة تلقائياً بعد ثانيتين
        def close_test():
            login_window.close()
            app.quit()
        
        QTimer.singleShot(2000, close_test)
        
        # عرض النافذة
        login_window.show()
        
        print("✅ تم عرض النافذة بنجاح")
        
        # تشغيل التطبيق لفترة قصيرة
        app.exec_()
        
        print("✅ تم إغلاق النظام بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام الكامل: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار شامل للوحدات المطورة")
    print("   Complete Test for Developed Modules")
    print("=" * 60)
    
    tests = [
        ("استيراد الوحدات", test_modules_import),
        ("التكامل مع قاعدة البيانات", test_database_integration),
        ("إنشاء الويدجتات", test_widgets_creation),
        ("الوظائف الأساسية", test_functionality),
        ("تشغيل النظام الكامل", run_full_system_test)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 40)
        
        try:
            if test_func():
                print(f"✅ نجح اختبار: {test_name}")
                passed_tests += 1
            else:
                print(f"❌ فشل اختبار: {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار:")
    print(f"   ✅ نجح: {passed_tests}/{total_tests}")
    print(f"   ❌ فشل: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        success_rate = 100
    else:
        success_rate = (passed_tests / total_tests) * 100
        print(f"⚠️ معدل النجاح: {success_rate:.1f}%")
    
    print("=" * 60)
    
    print("\n🎯 الوحدات المطورة والجاهزة:")
    print("   ✅ وحدة إدارة الحسابات - مكتملة")
    print("   ✅ وحدة القيود اليومية - مكتملة")
    print("   ✅ وحدة التقارير المالية - مكتملة")
    print("   ✅ واجهة تسجيل الدخول - مكتملة")
    print("   ✅ الواجهة الرئيسية - مكتملة")
    print("   ✅ قاعدة البيانات SQLite - مكتملة")
    
    print("\n🚀 للتشغيل:")
    print("   python run_onyx.py")
    print("   أو")
    print("   run_onyx.bat")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)