#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لجميع مشاكل وضوح النص في الواجهات
Comprehensive Text Visibility Fix for All Interfaces
"""

import os
import re

def fix_text_visibility_in_file(file_path):
    """إصلاح مشاكل وضوح النص في ملف واحد"""
    if not os.path.exists(file_path):
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixes_applied = 0
        
        # إصلاح 1: إضافة نمط واضح للليبلات العادية
        if 'QLabel(' in content and 'setStyleSheet' not in content:
            # البحث عن إنشاء ليبلات بدون نمط
            label_pattern = r'(\w+\s*=\s*QLabel\([^)]*\))'
            
            def add_label_style(match):
                nonlocal fixes_applied
                fixes_applied += 1
                return match.group(1) + '\n        ' + match.group(1).split('=')[0].strip() + '.setStyleSheet(get_clear_label_style())'
            
            content = re.sub(label_pattern, add_label_style, content)
        
        # إصلاح 2: إضافة دالة النمط الواضح إذا لم تكن موجودة
        if 'def get_clear_label_style' not in content and 'QLabel' in content:
            style_function = '''
def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """

'''
            # إضافة الدوال في بداية الملف بعد الاستيرادات
            import_end = content.find('\n\nclass')
            if import_end == -1:
                import_end = content.find('\ndef ')
            if import_end == -1:
                import_end = len(content) // 2
            
            content = content[:import_end] + style_function + content[import_end:]
            fixes_applied += 1
        
        # إصلاح 3: تحسين الليبلات الموجودة
        improvements = [
            # إصلاح الألوان المختفية
            (r'color:\s*white;([^}]*background-color:\s*white)', r'color: #2c3e50;\1'),
            (r'color:\s*#ffffff;([^}]*background-color:\s*#ffffff)', r'color: #2c3e50;\1'),
            
            # إضافة خط افتراضي
            (r'QLabel\s*\{([^}]*)\}', lambda m: f'QLabel {{\n                font-family: "Tahoma", "Arial", sans-serif;\n                {m.group(1)}\n            }}' if 'font-family' not in m.group(1) else m.group(0)),
            
            # إصلاح الحجم الصغير
            (r'font-size:\s*[1-8]px', 'font-size: 12px'),
            
            # إضافة حشو للليبلات
            (r'QLabel\s*\{([^}]*)\}', lambda m: m.group(0).replace('}', '\n                padding: 5px;\n            }') if 'padding' not in m.group(1) else m.group(0)),
        ]
        
        for pattern, replacement in improvements:
            old_content = content
            content = re.sub(pattern, replacement, content)
            if content != old_content:
                fixes_applied += 1
        
        # حفظ الملف إذا تم تطبيق إصلاحات
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return fixes_applied
        
        return 0
        
    except Exception as e:
        print(f"خطأ في معالجة {file_path}: {e}")
        return 0

def main():
    """الدالة الرئيسية لإصلاح جميع مشاكل النص"""
    print("🔤 إصلاح شامل لجميع مشاكل وضوح النص")
    print("=" * 50)
    
    # قائمة الملفات للإصلاح
    files_to_fix = [
        "src/modules/customers/customer_data_interface.py",
        "src/modules/customers_module.py",
        "src/modules/customers_suppliers_module.py",
        "src/modules/products_module.py",
        "src/modules/inventory_module.py",
        "src/modules/accounts_module.py",
        "src/modules/reports_module.py",
        "src/modules/system_management_module.py",
        "src/modules/backup_module.py",
        "src/modules/database_management_module.py",
        "src/modules/auto_numbering_module.py",
        "src/modules/system_settings_module.py",
        "src/modules/user_privacy_module.py",
        "src/modules/security_settings_module.py",
        "src/modules/closures_audits_module.py",
        "src/modules/login_interface_control_module.py",
        "src/modules/activity_tracking_module.py",
        "src/modules/data_synchronization_module.py",
    ]
    
    total_fixes = 0
    fixed_files = 0
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            print(f"\n🔧 معالجة: {os.path.basename(file_path)}")
            fixes = fix_text_visibility_in_file(file_path)
            
            if fixes > 0:
                print(f"   ✅ تم تطبيق {fixes} إصلاح")
                total_fixes += fixes
                fixed_files += 1
            else:
                print(f"   ℹ️ لا يحتاج إصلاح")
        else:
            print(f"   ⚠️ ملف غير موجود: {file_path}")
    
    print("\n" + "=" * 50)
    print("📊 ملخص الإصلاحات:")
    print(f"   ✅ الملفات المعالجة: {fixed_files}")
    print(f"   🔧 إجمالي الإصلاحات: {total_fixes}")
    
    if total_fixes > 0:
        print("\n🎉 تم إصلاح مشاكل وضوح النص بنجاح!")
        
        print("\n🔤 الإصلاحات المطبقة:")
        print("   ✅ إضافة خطوط واضحة (Tahoma/Arial)")
        print("   ✅ تحسين أحجام الخطوط (12px)")
        print("   ✅ إصلاح الألوان المختفية")
        print("   ✅ إضافة حشو مناسب")
        print("   ✅ تحديد عرض أدنى")
        print("   ✅ إضافة دوال الأنماط")
        
        print("\n🎯 النتائج المتوقعة:")
        print("   📝 نص واضح ومقروء")
        print("   🎨 تنسيق موحد")
        print("   👁️ رؤية أفضل")
        print("   📱 تجربة مستخدم محسنة")
        
        print("\n🚀 للتحقق من النتائج:")
        print("   1. شغل النظام: python src/main_system.py")
        print("   2. افتح أي واجهة")
        print("   3. تحقق من وضوح النص")
        
    else:
        print("\n✅ جميع الملفات محسنة مسبقاً")
    
    # إنشاء ملف CSS مساعد
    create_helper_css()
    
    return total_fixes

def create_helper_css():
    """إنشاء ملف CSS مساعد للأنماط"""
    print("\n🎨 إنشاء ملف CSS مساعد...")
    
    css_content = """
/* أنماط واضحة للنصوص - Onyx Pro ERP */

/* ليبلات عامة */
QLabel {
    font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
    font-size: 12px;
    font-weight: bold;
    color: #2c3e50;
    background-color: transparent;
    padding: 5px;
    min-width: 100px;
    border: none;
}

/* ليبلات النماذج */
QLabel.form-label {
    font-size: 12px;
    padding: 8px;
    min-width: 150px;
    text-align: right;
}

/* ليبلات الشبكة */
QLabel.grid-label {
    font-size: 11px;
    min-width: 140px;
    max-width: 200px;
}

/* ليبلات القيم */
QLabel.value-label {
    font-size: 11px;
    background-color: #ecf0f1;
    border: 1px solid #bdc3c7;
    border-radius: 3px;
    min-width: 100px;
}

/* ليبلات العناوين */
QLabel.title-label {
    font-size: 18px;
    color: white;
    padding: 15px;
    border-radius: 8px;
}

/* مربعات الاختيار */
QCheckBox {
    font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
    font-size: 12px;
    font-weight: bold;
    color: #2c3e50;
    padding: 5px;
}

/* أزرار */
QPushButton {
    font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
    font-size: 14px;
    font-weight: bold;
    padding: 10px 20px;
    border-radius: 5px;
}

/* حقول النص */
QLineEdit, QTextEdit {
    font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
    font-size: 12px;
    padding: 5px;
    border: 1px solid #bdc3c7;
    border-radius: 3px;
}

/* القوائم المنسدلة */
QComboBox {
    font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
    font-size: 12px;
    padding: 5px;
    border: 1px solid #bdc3c7;
    border-radius: 3px;
}
"""
    
    try:
        os.makedirs('src/ui/styles', exist_ok=True)
        with open('src/ui/styles/clear_text.css', 'w', encoding='utf-8') as f:
            f.write(css_content)
        print("   ✅ تم إنشاء ملف CSS: src/ui/styles/clear_text.css")
        return True
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء ملف CSS: {e}")
        return False

if __name__ == "__main__":
    main()
