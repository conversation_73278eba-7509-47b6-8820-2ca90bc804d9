#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام الجديد
Test New System Structure
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_system_structure():
    """اختبار هيكل النظام"""
    print("🧪 اختبار هيكل النظام الجديد...")
    
    try:
        from src.system_structure import SYSTEM_STRUCTURE
        
        print("✅ تم استيراد هيكل النظام بنجاح")
        
        # عد الأقسام والتبويبات والوحدات
        departments_count = len(SYSTEM_STRUCTURE)
        tabs_count = 0
        modules_count = 0
        
        print(f"\n📊 إحصائيات النظام:")
        print(f"   🏢 عدد الأقسام: {departments_count}")
        
        for dept_key, dept_data in SYSTEM_STRUCTURE.items():
            dept_tabs = len(dept_data["tabs"])
            tabs_count += dept_tabs
            
            print(f"\n🔹 {dept_data['name']} ({dept_data['icon']}):")
            print(f"   📑 التبويبات: {dept_tabs}")
            
            for tab_key, tab_data in dept_data["tabs"].items():
                tab_modules = len(tab_data["modules"])
                modules_count += tab_modules
                print(f"      • {tab_data['name']}: {tab_modules} وحدة")
        
        print(f"\n📈 الإجمالي:")
        print(f"   📑 إجمالي التبويبات: {tabs_count}")
        print(f"   🔧 إجمالي الوحدات: {modules_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار هيكل النظام: {e}")
        return False

def test_new_main_system():
    """اختبار النظام الرئيسي الجديد"""
    print("\n🧪 اختبار النظام الرئيسي الجديد...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.new_main_system import NewMainSystem
        
        # إنشاء تطبيق مؤقت
        app = QApplication([])
        
        # إنشاء النظام الرئيسي
        main_system = NewMainSystem()
        
        print("✅ تم إنشاء النظام الرئيسي الجديد بنجاح")
        
        # اختبار وجود التبويبات
        tabs_count = main_system.departments_tabs.count()
        print(f"   📑 عدد تبويبات الأقسام: {tabs_count}")
        
        # اختبار أسماء التبويبات
        print("   📋 أسماء الأقسام:")
        for i in range(tabs_count):
            tab_text = main_system.departments_tabs.tabText(i)
            print(f"      {i+1}. {tab_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار النظام الرئيسي: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_module_loading():
    """اختبار تحميل الوحدات"""
    print("\n🧪 اختبار تحميل الوحدات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.new_main_system import NewMainSystem
        
        # إنشاء تطبيق مؤقت
        app = QApplication([])
        
        # إنشاء النظام الرئيسي
        main_system = NewMainSystem()
        
        # اختبار تحميل وحدة موجودة
        test_modules = [
            ("customers_sales", "inputs", "customers_data"),
            ("accounting_systems", "inputs", "chart_of_accounts"),
            ("system_setup", "general_setup", "general_variables"),
        ]
        
        success_count = 0
        for dept, tab, module in test_modules:
            try:
                main_system.load_module(dept, tab, module)
                print(f"✅ تم تحميل الوحدة: {module}")
                success_count += 1
            except Exception as e:
                print(f"⚠️ فشل في تحميل الوحدة {module}: {e}")
        
        print(f"\n📊 نتيجة اختبار التحميل: {success_count}/{len(test_modules)} وحدة")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تحميل الوحدات: {e}")
        return False

def run_new_system():
    """تشغيل النظام الجديد"""
    print("\n🚀 تشغيل النظام الجديد...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.new_main_system import NewMainSystem
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إنشاء النظام الرئيسي
        main_system = NewMainSystem()
        main_system.show()
        
        print("✅ تم تشغيل النظام الجديد بنجاح!")
        print("\n🎯 الميزات الجديدة:")
        print("   🏢 6 أقسام رئيسية منظمة")
        print("   📑 تبويبات فرعية لكل قسم")
        print("   🔧 وحدات مرتبة حسب الوظيفة")
        print("   🎨 واجهة محسنة وسهلة الاستخدام")
        print("   🔍 بحث وتنقل سريع")
        
        print("\n📋 الأقسام المتاحة:")
        print("   🔧 تهيئة النظام")
        print("   ⚙️ إدارة النظام") 
        print("   💰 أنظمة الحسابات")
        print("   👥 إدارة العملاء والمبيعات")
        print("   🏪 إدارة الموردين والمشتريات")
        print("   📊 نظام إدارة المعلومات")
        print("   🛠️ الأنظمة المساعدة")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام الجديد: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 70)
    print("🧪 اختبار النظام المحدث - البنية الجديدة")
    print("=" * 70)
    
    tests = [
        ("هيكل النظام", test_system_structure),
        ("النظام الرئيسي", test_new_main_system),
        ("تحميل الوحدات", test_module_loading),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 نتائج الاختبار: {passed_tests}/{total_tests} اختبار نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n🚀 سيتم الآن تشغيل النظام الجديد...")
        print("=" * 70)
        
        # تشغيل النظام الجديد
        run_new_system()
    else:
        print("⚠️ بعض الاختبارات فشلت - يرجى مراجعة الأخطاء أعلاه")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
