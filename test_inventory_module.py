#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وحدة إدارة المخازن
Inventory Management Module Test
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_inventory_module():
    """اختبار وحدة إدارة المخازن"""
    print("📦 اختبار وحدة إدارة المخازن...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.inventory_module import InventoryManagementWidget, WarehouseDialog, InventoryMovementDialog
        from database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إنشاء الوحدة الرئيسية
        inventory_widget = InventoryManagementWidget(db_manager)
        print("   ✅ تم إنشاء وحدة إدارة المخازن بنجاح")
        
        # اختبار إنشاء حوار المخزن
        warehouse_dialog = WarehouseDialog(db_manager)
        print("   ✅ تم إنشاء حوار إضافة مخزن بنجاح")
        
        # اختبار إنشاء حوار الحركة المخزنية
        movement_dialog = InventoryMovementDialog(db_manager, "in")
        print("   ✅ تم إنشاء حوار الحركة المخزنية بنجاح")
        
        # اختبار الوظائف الأساسية
        if hasattr(inventory_widget, 'tabs'):
            print("   ✅ التبويبات موجودة")
            print(f"   📋 عدد التبويبات: {inventory_widget.tabs.count()}")
        
        if hasattr(inventory_widget, 'warehouses_table'):
            print("   ✅ جدول المخازن موجود")
        
        if hasattr(inventory_widget, 'movements_table'):
            print("   ✅ جدول الحركات المخزنية موجود")
        
        if hasattr(inventory_widget, 'balances_table'):
            print("   ✅ جدول أرصدة المخزون موجود")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار وحدة المخازن: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_tables():
    """اختبار جداول قاعدة البيانات للمخازن"""
    print("\n🗄️ اختبار جداول قاعدة البيانات للمخازن...")
    
    try:
        import sqlite3
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect("accounting_system.db")
        cursor = conn.cursor()
        
        # فحص الجداول المطلوبة
        required_tables = [
            'warehouses',
            'inventory_movements',
            'products'
        ]
        
        existing_tables = []
        for table in required_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                existing_tables.append(table)
                print(f"   ✅ جدول {table} موجود")
            else:
                print(f"   ⚠️ جدول {table} غير موجود")
        
        # فحص هيكل جدول المخازن
        if 'warehouses' in existing_tables:
            cursor.execute("PRAGMA table_info(warehouses)")
            columns = cursor.fetchall()
            print(f"   📋 جدول المخازن يحتوي على {len(columns)} عمود")
            
            required_columns = ['code', 'name', 'location', 'type', 'manager', 'is_active']
            for col_name in required_columns:
                if any(col[1] == col_name for col in columns):
                    print(f"   ✅ عمود {col_name} موجود")
                else:
                    print(f"   ⚠️ عمود {col_name} غير موجود")
        
        # فحص هيكل جدول حركات المخزون
        if 'inventory_movements' in existing_tables:
            cursor.execute("PRAGMA table_info(inventory_movements)")
            columns = cursor.fetchall()
            print(f"   📋 جدول حركات المخزون يحتوي على {len(columns)} عمود")
            
            required_columns = ['movement_number', 'movement_type', 'warehouse_id', 'product_id', 'quantity']
            for col_name in required_columns:
                if any(col[1] == col_name for col in columns):
                    print(f"   ✅ عمود {col_name} موجود")
                else:
                    print(f"   ⚠️ عمود {col_name} غير موجود")
        
        # إحصائيات
        print(f"\n📊 إحصائيات الجداول:")
        for table in existing_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   📋 {table}: {count} سجل")
        
        conn.close()
        return len(existing_tables) >= 2  # على الأقل جدولين مطلوبين
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_main_system_integration():
    """اختبار تكامل المخازن مع النظام الرئيسي"""
    print("\n🔗 اختبار تكامل المخازن مع النظام الرئيسي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        # اختبار النظام الرئيسي
        import main_system
        from main_system import MainSystemWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainSystemWindow()
        print("   ✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار وجود دوال المخازن
        inventory_functions = [
            'open_inventory',
            'open_warehouse_definition',
            'open_inventory_in',
            'open_inventory_out',
            'open_inventory_balances',
            'open_inventory_movements'
        ]
        
        all_functions_exist = True
        for func_name in inventory_functions:
            if hasattr(main_window, func_name):
                print(f"   ✅ دالة {func_name} موجودة")
            else:
                print(f"   ❌ دالة {func_name} غير موجودة")
                all_functions_exist = False
        
        return all_functions_exist
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التكامل: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inventory_operations():
    """اختبار العمليات المخزنية"""
    print("\n📋 اختبار العمليات المخزنية...")
    
    try:
        from database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إضافة مخزن تجريبي
        try:
            warehouse_query = """
            INSERT OR IGNORE INTO warehouses 
            (code, name, location, type, manager, is_active, notes, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
            """
            warehouse_params = (
                'TEST001',
                'مخزن تجريبي',
                'الموقع التجريبي',
                'مخزن رئيسي',
                'مدير تجريبي',
                True,
                'مخزن للاختبار'
            )
            
            db_manager.execute_update(warehouse_query, warehouse_params)
            print("   ✅ تم إضافة مخزن تجريبي بنجاح")
            
        except Exception as e:
            print(f"   ⚠️ تعذر إضافة مخزن تجريبي: {e}")
        
        # اختبار قراءة المخازن
        warehouses = db_manager.execute_query("SELECT COUNT(*) FROM warehouses")
        if warehouses:
            warehouse_count = warehouses[0][0]
            print(f"   📊 عدد المخازن في قاعدة البيانات: {warehouse_count}")
        
        # اختبار قراءة الحركات المخزنية
        movements = db_manager.execute_query("SELECT COUNT(*) FROM inventory_movements")
        if movements:
            movement_count = movements[0][0]
            print(f"   📊 عدد الحركات المخزنية: {movement_count}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار العمليات المخزنية: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار وحدة إدارة المخازن")
    print("=" * 70)
    
    tests = [
        ("وحدة إدارة المخازن", test_inventory_module),
        ("جداول قاعدة البيانات", test_database_tables),
        ("العمليات المخزنية", test_inventory_operations),
        ("تكامل النظام الرئيسي", test_main_system_integration),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم تفعيل وحدة المخازن بنجاح!")
        print("✅ جميع الوحدات تعمل بدون أخطاء")
        
        print("\n🔧 الوظائف المتاحة:")
        print("   🏪 تعريف المخازن - مفعلة ✅")
        print("   📥 إدخال مخزني - مفعلة ✅")
        print("   📤 إخراج مخزني - مفعلة ✅")
        print("   📊 أرصدة الأصناف - مفعلة ✅")
        print("   📋 حركات الأصناف - مفعلة ✅")
        print("   📦 إدارة المخزون الشاملة - مفعلة ✅")
        
        print("\n🚀 كيفية الاستخدام:")
        print("   1. شغل النظام: python src/main_system.py")
        print("   2. انقر على 'أنظمة المخازن' في الشجرة")
        print("   3. اختر الوظيفة المطلوبة:")
        print("      - تعريف المخازن: لإدارة المخازن")
        print("      - إدخال/إخراج مخزني: للحركات")
        print("      - أرصدة/حركات الأصناف: للتقارير")
        
    else:
        print("\n⚠️ بعض وظائف المخازن تحتاج إلى إصلاح")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
