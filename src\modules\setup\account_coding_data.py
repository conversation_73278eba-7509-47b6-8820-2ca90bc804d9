#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف البيانات والمعالجة لشاشة ترميز الحسابات
Data and Processing Functions for Account Coding
"""

from PyQt5.QtWidgets import QMessageBox, QTableWidgetItem
from PyQt5.QtCore import Qt
from datetime import datetime
import re

def load_data_function(self):
    """تحميل البيانات المحفوظة"""
    try:
        # تحميل قواعد المستويات
        self.load_coding_levels()
        
        # تحميل الحسابات للترميز
        self.load_accounts_for_coding()
        
        # تحديث المعاينة
        self.update_system_preview()
        
    except Exception as e:
        print(f"خطأ في تحميل البيانات: {e}")

def load_coding_levels_function(self):
    """تحميل قواعد مستويات الترميز"""
    # قواعد المستويات النموذجية
    levels_data = [
        ("المستوى الأول", "الحسابات الرئيسية", "X000", "1000", "9000", "1000 - الأصول"),
        ("المستوى الثاني", "المجموعات الفرعية", "XX00", "1100", "1900", "1100 - الأصول المتداولة"),
        ("المستوى الثالث", "الحسابات التفصيلية", "XXX0", "1110", "1190", "1110 - النقدية"),
        ("المستوى الرابع", "الحسابات الفرعية", "XXXX", "1111", "1119", "1111 - الصندوق الرئيسي")
    ]
    
    self.levels_table.setRowCount(len(levels_data))
    for row, (level, description, pattern, start, end, example) in enumerate(levels_data):
        self.levels_table.setItem(row, 0, QTableWidgetItem(level))
        self.levels_table.setItem(row, 1, QTableWidgetItem(description))
        self.levels_table.setItem(row, 2, QTableWidgetItem(pattern))
        self.levels_table.setItem(row, 3, QTableWidgetItem(start))
        self.levels_table.setItem(row, 4, QTableWidgetItem(end))
        self.levels_table.setItem(row, 5, QTableWidgetItem(example))

def load_accounts_for_coding_function(self):
    """تحميل الحسابات المتاحة للترميز"""
    # حسابات نموذجية للترميز
    accounts_data = [
        ("الأصول المتداولة", "1000", "1000", "حساب رئيسي", "1", "جاهز للترميز"),
        ("النقدية في الصندوق", "1001", "1110", "حساب فرعي", "3", "جاهز للترميز"),
        ("البنك الأهلي اليمني", "1002", "1120", "حساب فرعي", "3", "جاهز للترميز"),
        ("حساب العملاء", "1003", "1210", "حساب فرعي", "3", "جاهز للترميز"),
        ("المخزون", "1004", "1310", "حساب فرعي", "3", "جاهز للترميز"),
        ("الأصول الثابتة", "2000", "2000", "حساب رئيسي", "1", "جاهز للترميز"),
        ("الأثاث والمعدات", "2001", "2110", "حساب فرعي", "3", "جاهز للترميز"),
        ("السيارات", "2002", "2120", "حساب فرعي", "3", "جاهز للترميز"),
        ("الخصوم المتداولة", "3000", "3000", "حساب رئيسي", "1", "جاهز للترميز"),
        ("حساب الموردين", "3001", "3110", "حساب فرعي", "3", "جاهز للترميز"),
        ("المرتبات المستحقة", "3002", "3120", "حساب فرعي", "3", "جاهز للترميز"),
        ("رأس المال", "5000", "5000", "حساب رئيسي", "1", "جاهز للترميز"),
        ("رأس المال المدفوع", "5001", "5110", "حساب فرعي", "3", "جاهز للترميز"),
        ("الإيرادات", "6000", "6000", "حساب رئيسي", "1", "جاهز للترميز"),
        ("إيرادات المبيعات", "6001", "6110", "حساب فرعي", "3", "جاهز للترميز"),
        ("المصروفات التشغيلية", "7000", "7000", "حساب رئيسي", "1", "جاهز للترميز"),
        ("مصروفات الرواتب", "7001", "7110", "حساب فرعي", "3", "جاهز للترميز"),
        ("المصروفات الإدارية", "8000", "8000", "حساب رئيسي", "1", "جاهز للترميز"),
        ("مصروفات الإيجار", "8001", "8110", "حساب فرعي", "3", "جاهز للترميز")
    ]
    
    self.accounts_table.setRowCount(len(accounts_data))
    for row, (name, current_code, suggested_code, account_type, level, status) in enumerate(accounts_data):
        self.accounts_table.setItem(row, 0, QTableWidgetItem(name))
        self.accounts_table.setItem(row, 1, QTableWidgetItem(current_code))
        self.accounts_table.setItem(row, 2, QTableWidgetItem(suggested_code))
        self.accounts_table.setItem(row, 3, QTableWidgetItem(account_type))
        self.accounts_table.setItem(row, 4, QTableWidgetItem(level))
        self.accounts_table.setItem(row, 5, QTableWidgetItem(status))

def update_system_preview_function(self):
    """تحديث معاينة نظام الترميز"""
    try:
        # الحصول على النظام المختار
        selected_system = self.coding_system_group.checkedId()
        if selected_system == -1:  # لا يوجد اختيار
            selected_system = 1  # افتراضي للنظام العشري

        code_length = self.code_length.value()
        levels_count = self.levels_count.value()
        separator = self.level_separator.currentText()
        prefix = self.code_prefix.text()
        suffix = self.code_suffix.text()
        
        # تحديد فاصل المستويات
        sep_char = ""
        if separator == "نقطة (.)":
            sep_char = "."
        elif separator == "شرطة (-)":
            sep_char = "-"
        elif separator == "شرطة مائلة (/)":
            sep_char = "/"
        elif separator == "مسافة ( )":
            sep_char = " "
        
        # الحصول على اسم النظام بطريقة آمنة
        try:
            system_name = self.get_system_name(selected_system)
        except Exception as e:
            system_name = f"نظام رقم {selected_system}"

        preview_text = f"""
🔢 معاينة نظام الترميز
{'='*50}

📊 إعدادات النظام:
   🎯 النظام المختار: {system_name}
   📏 طول الرمز: {code_length} خانات
   📊 عدد المستويات: {levels_count} مستويات
   🔗 فاصل المستويات: {separator}
   🏷️ البادئة: {prefix if prefix else 'بدون'}
   🏷️ اللاحقة: {suffix if suffix else 'بدون'}

📋 أمثلة على الترميز:
"""
        
        # إنتاج أمثلة حسب النظام المختار
        if selected_system == 1:  # النظام العشري
            examples = ["10", "20", "30", "40", "50"]
        elif selected_system == 2:  # النظام المئوي
            examples = ["100", "200", "300", "400", "500"]
        elif selected_system == 3:  # النظام الألفي
            examples = ["1000", "2000", "3000", "4000", "5000"]
        elif selected_system == 4:  # النظام الهجين
            examples = ["1-100", "2-100", "3-100", "4-100", "5-100"]
        else:  # نظام مخصص
            examples = ["001", "002", "003", "004", "005"]
        
        # تطبيق التنسيق
        for i, example in enumerate(examples):
            formatted_code = f"{prefix}{sep_char if prefix else ''}{example}{sep_char if suffix else ''}{suffix}"
            account_names = ["الأصول", "الخصوم", "حقوق الملكية", "الإيرادات", "المصروفات"]
            preview_text += f"   {formatted_code} - {account_names[i]}\n"
        
        # إضافة أمثلة فرعية
        preview_text += f"""
📊 أمثلة على المستويات الفرعية:
   {prefix}{sep_char if prefix else ''}1100{sep_char if suffix else ''}{suffix} - الأصول المتداولة
   {prefix}{sep_char if prefix else ''}1110{sep_char if suffix else ''}{suffix} - النقدية
   {prefix}{sep_char if prefix else ''}1111{sep_char if suffix else ''}{suffix} - الصندوق الرئيسي
   {prefix}{sep_char if prefix else ''}1112{sep_char if suffix else ''}{suffix} - الصندوق الفرعي

✅ المميزات:
   🔢 ترقيم منطقي ومتسق
   📊 هيكل هرمي واضح
   🔍 سهولة البحث والفرز
   📈 قابلية التوسع المستقبلي
"""
        
        self.system_preview.setPlainText(preview_text)
        
    except Exception as e:
        self.system_preview.setPlainText(f"خطأ في إنتاج المعاينة: {str(e)}")

def get_system_name_function(self, system_id):
    """الحصول على اسم النظام"""
    try:
        # التأكد من أن system_id هو رقم
        if hasattr(system_id, 'isChecked'):  # إذا كان QCheckBox
            return "خطأ: تم تمرير QCheckBox بدلاً من الرقم"

        # تحويل إلى رقم إذا كان نص
        if isinstance(system_id, str):
            system_id = int(system_id)

        systems = {
            1: "النظام العشري",
            2: "النظام المئوي",
            3: "النظام الألفي",
            4: "النظام الهجين",
            5: "نظام مخصص"
        }
        return systems.get(system_id, f"نظام غير معروف ({system_id})")

    except Exception as e:
        return f"خطأ في تحديد النظام: {str(e)}"

def generate_account_codes_function(self):
    """إنتاج رموز الحسابات"""
    try:
        # الحصول على النظام المختار
        selected_system = self.coding_system_group.checkedId()
        
        # تحديث الرموز المقترحة في الجدول
        for row in range(self.accounts_table.rowCount()):
            account_name = self.accounts_table.item(row, 0).text()
            account_type = self.accounts_table.item(row, 3).text()
            level = int(self.accounts_table.item(row, 4).text())
            
            # إنتاج الرمز المقترح
            suggested_code = self.generate_code_for_account(account_name, account_type, level, selected_system)
            self.accounts_table.setItem(row, 2, QTableWidgetItem(suggested_code))
            self.accounts_table.setItem(row, 5, QTableWidgetItem("رمز مقترح"))
        
        # تحديث شريط التقدم
        self.coding_progress.setValue(50)
        self.progress_info.setText("تم إنتاج الرموز المقترحة بنجاح")
        
        QMessageBox.information(self, "تم", "تم إنتاج الرموز المقترحة لجميع الحسابات")
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إنتاج الرموز:\n{str(e)}")

def generate_code_for_account_function(self, account_name, account_type, level, system_id):
    """إنتاج رمز لحساب محدد"""
    try:
        # تحديد الرمز الأساسي حسب نوع الحساب
        base_codes = {
            "حساب رئيسي": {
                "الأصول": "1000",
                "الخصوم": "3000", 
                "حقوق الملكية": "5000",
                "الإيرادات": "6000",
                "المصروفات": "7000"
            }
        }
        
        # البحث عن نوع الحساب في اسم الحساب
        for account_category in base_codes["حساب رئيسي"]:
            if account_category in account_name:
                base_code = base_codes["حساب رئيسي"][account_category]
                break
        else:
            # تحديد حسب الكلمات المفتاحية
            if any(word in account_name for word in ["نقدية", "صندوق", "بنك"]):
                base_code = "1110"
            elif any(word in account_name for word in ["عملاء", "مدينون"]):
                base_code = "1210"
            elif any(word in account_name for word in ["مخزون", "بضاعة"]):
                base_code = "1310"
            elif any(word in account_name for word in ["أثاث", "معدات", "سيارات"]):
                base_code = "2110"
            elif any(word in account_name for word in ["موردين", "دائنون"]):
                base_code = "3110"
            elif any(word in account_name for word in ["مرتبات", "أجور"]):
                base_code = "3120"
            elif any(word in account_name for word in ["رأس المال"]):
                base_code = "5110"
            elif any(word in account_name for word in ["مبيعات", "إيرادات"]):
                base_code = "6110"
            elif any(word in account_name for word in ["رواتب", "أجور"]):
                base_code = "7110"
            elif any(word in account_name for word in ["إيجار", "كهرباء", "مياه"]):
                base_code = "8110"
            else:
                base_code = "9999"  # رمز افتراضي
        
        # تطبيق نظام الترميز المختار
        if system_id == 1:  # النظام العشري
            return str(int(base_code) // 100 * 10)
        elif system_id == 2:  # النظام المئوي
            return str(int(base_code) // 10 * 100)
        elif system_id == 3:  # النظام الألفي
            return base_code
        elif system_id == 4:  # النظام الهجين
            main_part = base_code[:1]
            sub_part = base_code[1:]
            return f"{main_part}-{sub_part}"
        else:  # نظام مخصص
            return base_code
            
    except Exception as e:
        return "0000"

def check_duplicates_function(self):
    """فحص التكرارات في الرموز"""
    try:
        codes = []
        duplicates = []
        
        # جمع جميع الرموز
        for row in range(self.accounts_table.rowCount()):
            code = self.accounts_table.item(row, 2).text()
            if code in codes:
                duplicates.append(code)
            else:
                codes.append(code)
        
        # إنتاج التقرير
        if duplicates:
            result = f"""
🔍 فحص التكرارات في الرموز
{'='*40}
تاريخ الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

❌ تم العثور على رموز مكررة:
"""
            for duplicate in set(duplicates):
                result += f"   🔴 الرمز {duplicate} مكرر\n"
            
            result += f"""
📊 إحصائيات:
   📝 إجمالي الرموز: {len(codes) + len(duplicates)}
   🔴 الرموز المكررة: {len(set(duplicates))}
   ✅ الرموز الفريدة: {len(codes)}

💡 التوصية: يجب إصلاح الرموز المكررة قبل تطبيق النظام
"""
        else:
            result = f"""
🔍 فحص التكرارات في الرموز
{'='*40}
تاريخ الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

✅ لا توجد رموز مكررة

📊 إحصائيات:
   📝 إجمالي الرموز: {len(codes)}
   ✅ جميع الرموز فريدة

🎉 النظام جاهز للتطبيق
"""
        
        self.validation_results.setPlainText(result)
        
    except Exception as e:
        self.validation_results.setPlainText(f"خطأ في فحص التكرارات: {str(e)}")

def check_gaps_function(self):
    """فحص الفجوات في التسلسل"""
    try:
        codes = []
        
        # جمع الرموز الرقمية
        for row in range(self.accounts_table.rowCount()):
            code = self.accounts_table.item(row, 2).text()
            if code.isdigit():
                codes.append(int(code))
        
        codes.sort()
        gaps = []
        
        # البحث عن الفجوات
        for i in range(len(codes) - 1):
            if codes[i + 1] - codes[i] > 1:
                for gap in range(codes[i] + 1, codes[i + 1]):
                    gaps.append(gap)
        
        # إنتاج التقرير
        result = f"""
📊 فحص الفجوات في التسلسل
{'='*40}
تاريخ الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

"""
        
        if gaps:
            result += f"⚠️ تم العثور على فجوات في التسلسل:\n"
            for gap in gaps[:10]:  # عرض أول 10 فجوات فقط
                result += f"   📍 الرمز {gap} مفقود\n"
            
            if len(gaps) > 10:
                result += f"   ... و {len(gaps) - 10} فجوة أخرى\n"
        else:
            result += "✅ لا توجد فجوات في التسلسل\n"
        
        result += f"""
📊 إحصائيات:
   📝 إجمالي الرموز: {len(codes)}
   📍 الفجوات الموجودة: {len(gaps)}
   📈 نسبة التغطية: {((len(codes) / (max(codes) - min(codes) + 1)) * 100):.1f}%

💡 التوصية: {'ملء الفجوات لضمان التسلسل المنطقي' if gaps else 'التسلسل مثالي'}
"""
        
        self.validation_results.setPlainText(result)
        
    except Exception as e:
        self.validation_results.setPlainText(f"خطأ في فحص الفجوات: {str(e)}")

def show_help_function(self):
    """عرض المساعدة"""
    help_text = """
🔢 مساعدة ترميز الحسابات

📋 الوظائف المتاحة:

🎯 أنظمة الترميز:
• النظام العشري: 10, 20, 30...
• النظام المئوي: 100, 200, 300...
• النظام الألفي: 1000, 2000, 3000...
• النظام الهجين: 1-100, 2-100...
• النظام المخصص: حسب الحاجة

📊 قواعد الترميز:
• تحديد قواعد كل مستوى
• أنماط الترميز المختلفة
• نطاقات البداية والنهاية
• أمثلة توضيحية

✅ تطبيق الترميز:
• إنتاج الرموز المقترحة
• تطبيق على حسابات محددة
• تطبيق شامل لجميع الحسابات
• معالجة التعارضات

🔍 التحقق والمراجعة:
• فحص التكرارات
• فحص الفجوات في التسلسل
• فحص الهيكل الهرمي
• إصلاح تلقائي للأخطاء

📊 التقارير:
• ملخص نظام الترميز
• قائمة شاملة بالرموز
• تقرير التحقق من الصحة
• إحصائيات مفصلة

💡 نصائح:
• اختر نظام ترميز مناسب لحجم الشركة
• تأكد من عدم وجود تكرارات
• احتفظ بمساحة للتوسع المستقبلي
• استخدم أرقام منطقية ومفهومة
• اختبر النظام قبل التطبيق النهائي
"""
    
    QMessageBox.information(self, "المساعدة", help_text)
