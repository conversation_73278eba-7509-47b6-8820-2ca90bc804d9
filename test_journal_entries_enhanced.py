#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الميزات المحسنة لوحدة القيود اليومية
Enhanced features test for journal entries module
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_journal_features():
    """اختبار الميزات المحسنة للقيود اليومية"""
    print("🧪 اختبار الميزات المحسنة للقيود اليومية...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.journal_entries_module import JournalEntriesWidget, JournalEntryPrintDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار إنشاء وحدة القيود اليومية
        journal_widget = JournalEntriesWidget(db_manager)
        print("✅ تم إنشاء وحدة القيود اليومية المحسنة")
        
        # اختبار وجود زر التحقق من التوازن
        if hasattr(journal_widget, 'validate_btn'):
            print("✅ زر التحقق من التوازن موجود")
        else:
            print("❌ زر التحقق من التوازن مفقود")
        
        # اختبار وظيفة التحقق من التوازن
        journal_widget.validate_journal_entries()
        print("✅ تم اختبار وظيفة التحقق من التوازن")
        
        # اختبار إنشاء حوار الطباعة (بدون معرف قيد حقيقي)
        try:
            print_dialog = JournalEntryPrintDialog(db_manager, "1")
            print("✅ تم إنشاء حوار الطباعة")
            print_dialog.close()
        except:
            print("⚠️ حوار الطباعة يحتاج قيد موجود للاختبار")
        
        # تنظيف
        journal_widget.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الميزات المحسنة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_journal_balance_validation():
    """اختبار التحقق من توازن القيود"""
    print("\n🧪 اختبار التحقق من توازن القيود...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار استعلام القيود غير المتوازنة
        query = """
            SELECT je.id, je.entry_date, je.description,
                   COALESCE(SUM(jed.debit_amount), 0) as total_debit,
                   COALESCE(SUM(jed.credit_amount), 0) as total_credit
            FROM journal_entries je
            LEFT JOIN journal_entry_details jed ON je.id = jed.journal_entry_id
            GROUP BY je.id, je.entry_date, je.description
            HAVING total_debit != total_credit
            ORDER BY je.entry_date DESC
        """
        
        unbalanced_entries = db_manager.execute_query(query)
        print(f"✅ تم العثور على {len(unbalanced_entries)} قيد غير متوازن")
        
        # اختبار استعلام إحصائيات التوازن
        balance_stats_query = """
            SELECT 
                COUNT(*) as total_entries,
                COUNT(CASE WHEN 
                    COALESCE((SELECT SUM(debit_amount) FROM journal_entry_details WHERE journal_entry_id = je.id), 0) = 
                    COALESCE((SELECT SUM(credit_amount) FROM journal_entry_details WHERE journal_entry_id = je.id), 0)
                    THEN 1 END) as balanced_entries
            FROM journal_entries je
        """
        
        balance_stats = db_manager.execute_query(balance_stats_query)
        if balance_stats:
            stats = balance_stats[0]
            total = stats['total_entries']
            balanced = stats['balanced_entries']
            unbalanced = total - balanced
            
            print(f"📊 إحصائيات التوازن:")
            print(f"   📝 إجمالي القيود: {total}")
            print(f"   ✅ القيود المتوازنة: {balanced}")
            print(f"   ⚠️ القيود غير المتوازنة: {unbalanced}")
            
            if total > 0:
                balance_percentage = (balanced / total) * 100
                print(f"   📊 نسبة التوازن: {balance_percentage:.1f}%")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التحقق من التوازن: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_journal_print_functionality():
    """اختبار وظيفة طباعة القيود"""
    print("\n🧪 اختبار وظيفة طباعة القيود...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار استعلام بيانات القيد للطباعة
        entry_query = """
            SELECT je.id, je.entry_date, je.description, je.total_amount,
                   je.is_posted, je.created_at, u.full_name as created_by_name
            FROM journal_entries je
            LEFT JOIN users u ON je.created_by = u.id
            LIMIT 1
        """
        
        entries = db_manager.execute_query(entry_query)
        print(f"✅ تم استعلام {len(entries)} قيد للطباعة")
        
        if entries:
            entry_id = entries[0]['id']
            
            # اختبار استعلام تفاصيل القيد للطباعة
            details_query = """
                SELECT jed.account_code, coa.account_name_ar, jed.description,
                       jed.debit_amount, jed.credit_amount
                FROM journal_entry_details jed
                LEFT JOIN chart_of_accounts coa ON jed.account_code = coa.account_code
                WHERE jed.journal_entry_id = ?
                ORDER BY jed.id
            """
            
            details = db_manager.execute_query(details_query, (entry_id,))
            print(f"✅ تم استعلام {len(details)} تفصيل للقيد")
            
            # اختبار إنشاء نص الطباعة
            if details:
                print("✅ يمكن إنشاء نص الطباعة للقيد")
            else:
                print("⚠️ لا توجد تفاصيل للقيد")
        else:
            print("⚠️ لا توجد قيود في النظام للطباعة")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وظيفة الطباعة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_journal_database_integrity():
    """اختبار سلامة قاعدة البيانات للقيود"""
    print("\n🧪 اختبار سلامة قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار وجود الجداول المطلوبة
        tables_query = "SELECT name FROM sqlite_master WHERE type='table' AND name IN ('journal_entries', 'journal_entry_details')"
        tables = db_manager.execute_query(tables_query)
        
        required_tables = ['journal_entries', 'journal_entry_details']
        existing_tables = [table['name'] for table in tables]
        
        for table in required_tables:
            if table in existing_tables:
                print(f"✅ جدول {table} موجود")
            else:
                print(f"❌ جدول {table} مفقود")
        
        # اختبار وجود الأعمدة المطلوبة في journal_entries
        je_columns_query = "PRAGMA table_info(journal_entries)"
        je_columns = db_manager.execute_query(je_columns_query)
        je_column_names = [col['name'] for col in je_columns]
        
        required_je_columns = ['id', 'entry_date', 'description', 'total_amount', 'is_posted', 'created_by', 'created_at']
        
        print("\n📋 أعمدة جدول journal_entries:")
        for col in required_je_columns:
            if col in je_column_names:
                print(f"   ✅ {col}")
            else:
                print(f"   ❌ {col} مفقود")
        
        # اختبار وجود الأعمدة المطلوبة في journal_entry_details
        jed_columns_query = "PRAGMA table_info(journal_entry_details)"
        jed_columns = db_manager.execute_query(jed_columns_query)
        jed_column_names = [col['name'] for col in jed_columns]
        
        required_jed_columns = ['id', 'journal_entry_id', 'account_code', 'description', 'debit_amount', 'credit_amount']
        
        print("\n📋 أعمدة جدول journal_entry_details:")
        for col in required_jed_columns:
            if col in jed_column_names:
                print(f"   ✅ {col}")
            else:
                print(f"   ❌ {col} مفقود")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار سلامة قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار الميزات المحسنة لوحدة القيود اليومية")
    print("   Enhanced journal entries module features test")
    print("=" * 60)
    
    # اختبار الميزات المحسنة
    enhanced_test = test_enhanced_journal_features()
    
    # اختبار التحقق من التوازن
    balance_test = test_journal_balance_validation()
    
    # اختبار وظيفة الطباعة
    print_test = test_journal_print_functionality()
    
    # اختبار سلامة قاعدة البيانات
    integrity_test = test_journal_database_integrity()
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار الميزات المحسنة:")
    print(f"   🚀 الميزات المحسنة: {'✅ نجح' if enhanced_test else '❌ فشل'}")
    print(f"   ⚖️ التحقق من التوازن: {'✅ نجح' if balance_test else '❌ فشل'}")
    print(f"   🖨️ وظيفة الطباعة: {'✅ نجح' if print_test else '❌ فشل'}")
    print(f"   🗄️ سلامة قاعدة البيانات: {'✅ نجح' if integrity_test else '❌ فشل'}")
    
    if enhanced_test and balance_test and print_test and integrity_test:
        print("\n🎉 جميع اختبارات الميزات المحسنة نجحت!")
        print("✅ وحدة القيود اليومية محسنة وجاهزة للاستخدام")
        
        print("\n🆕 الميزات الجديدة المضافة:")
        print("   ⚖️ التحقق التلقائي من توازن القيود")
        print("   🖨️ طباعة القيود بتنسيق احترافي")
        print("   📊 تقارير القيود غير المتوازنة")
        print("   🔍 إحصائيات التوازن المفصلة")
        print("   ⚠️ تحذيرات عند حفظ قيود غير متوازنة")
        print("   📋 معاينة القيد قبل الطباعة")
    else:
        print("\n⚠️ بعض اختبارات الميزات المحسنة فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)
    
    return 0 if (enhanced_test and balance_test and print_test and integrity_test) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
