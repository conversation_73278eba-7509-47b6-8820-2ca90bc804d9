#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة البحث عن الحسابات - نظام Onyx Pro ERP
Account Search Dialog - Onyx Pro ERP System
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QLineEdit, QComboBox, QPushButton, QTableWidget,
                             QTableWidgetItem, QGroupBox, QFrame, QCheckBox,
                             QHeaderView, QMessageBox, QShortcut)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QKeySequence

class AccountSearchDialog(QDialog):
    """نافذة البحث عن الحسابات"""
    
    account_selected = pyqtSignal(dict)  # إشارة عند اختيار حساب
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.accounts_data = []
        self.filtered_accounts = []
        
        self.setup_ui()
        self.load_accounts()
        self.setup_shortcuts()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("البحث عن الحسابات - يحتوي على")
        self.setGeometry(300, 300, 800, 600)
        self.setModal(True)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: "Tahoma", "Arial", sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #3498db;
                font-size: 14px;
            }
            QLabel {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
            QLineEdit, QComboBox {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                color: #2c3e50;
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #3498db;
                background-color: #ebf3fd;
            }
            QTableWidget {
                font-family: "Tahoma", "Arial", sans-serif;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 11px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QCheckBox {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 12px;
                color: #2c3e50;
                padding: 5px;
            }
        """)
        
        layout = QVBoxLayout()
        
        # منطقة البحث
        self.create_search_section()
        layout.addWidget(self.search_frame)
        
        # جدول النتائج
        self.create_results_table()
        layout.addWidget(self.results_frame)
        
        # أزرار التحكم
        self.create_control_buttons()
        layout.addWidget(self.buttons_frame)
        
        self.setLayout(layout)
    
    def create_search_section(self):
        """إنشاء منطقة البحث"""
        self.search_frame = QGroupBox("🔍 البحث في الحسابات")
        layout = QGridLayout()
        
        # حقل البحث
        layout.addWidget(QLabel("البحث:"), 0, 0)
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("اكتب جزء من رقم الحساب أو اسمه...")
        self.search_edit.textChanged.connect(self.on_search_text_changed)
        layout.addWidget(self.search_edit, 0, 1, 1, 2)
        
        # نوع البحث
        layout.addWidget(QLabel("البحث في:"), 1, 0)
        self.search_type_combo = QComboBox()
        self.search_type_combo.addItems([
            "الكل",
            "رقم الحساب", 
            "اسم الحساب",
            "نوع الحساب"
        ])
        self.search_type_combo.currentTextChanged.connect(self.filter_accounts)
        layout.addWidget(self.search_type_combo, 1, 1)
        
        # البحث التلقائي
        self.auto_search_checkbox = QCheckBox("تلقائي")
        self.auto_search_checkbox.setChecked(True)
        self.auto_search_checkbox.setToolTip("تحديث النتائج أثناء الكتابة")
        layout.addWidget(self.auto_search_checkbox, 1, 2)
        
        # عدد النتائج
        self.results_count_label = QLabel("عدد النتائج: 0")
        self.results_count_label.setStyleSheet("color: #7f8c8d; font-size: 11px;")
        layout.addWidget(self.results_count_label, 2, 0, 1, 3)
        
        self.search_frame.setLayout(layout)
        
        # تركيز على حقل البحث
        self.search_edit.setFocus()
    
    def create_results_table(self):
        """إنشاء جدول النتائج"""
        self.results_frame = QGroupBox("📋 نتائج البحث")
        layout = QVBoxLayout()
        
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(4)
        
        headers = ["رقم الحساب", "اسم الحساب", "نوع الحساب", "الحالة"]
        self.results_table.setHorizontalHeaderLabels(headers)
        
        # تعيين عرض الأعمدة
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)      # رقم الحساب
        header.setSectionResizeMode(1, QHeaderView.Stretch)    # اسم الحساب
        header.setSectionResizeMode(2, QHeaderView.Fixed)      # نوع الحساب
        header.setSectionResizeMode(3, QHeaderView.Fixed)      # الحالة
        
        self.results_table.setColumnWidth(0, 120)  # رقم الحساب
        self.results_table.setColumnWidth(2, 150)  # نوع الحساب
        self.results_table.setColumnWidth(3, 80)   # الحالة
        
        # تفعيل التحديد بالصف الكامل
        self.results_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.results_table.setSelectionMode(QTableWidget.SingleSelection)
        
        # ربط الإشارات
        self.results_table.itemDoubleClicked.connect(self.select_account)
        self.results_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        layout.addWidget(self.results_table)
        self.results_frame.setLayout(layout)
    
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        self.buttons_frame = QFrame()
        layout = QHBoxLayout()
        
        # زر الاختيار
        self.select_btn = QPushButton("✅ اختيار")
        self.select_btn.setEnabled(False)
        self.select_btn.clicked.connect(self.select_account)
        self.select_btn.setShortcut(QKeySequence(Qt.Key_Return))
        layout.addWidget(self.select_btn)
        
        # زر الإلغاء
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.clicked.connect(self.reject)
        self.cancel_btn.setShortcut(QKeySequence(Qt.Key_Escape))
        layout.addWidget(self.cancel_btn)
        
        layout.addStretch()
        
        # معلومات المساعدة
        help_label = QLabel("💡 نصائح: F9 للبحث | Enter للاختيار | Esc للإلغاء | النقر المزدوج للاختيار")
        help_label.setStyleSheet("color: #7f8c8d; font-size: 10px;")
        layout.addWidget(help_label)
        
        self.buttons_frame.setLayout(layout)
    
    def setup_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # F9 للتركيز على البحث
        f9_shortcut = QShortcut(QKeySequence(Qt.Key_F9), self)
        f9_shortcut.activated.connect(lambda: self.search_edit.setFocus())
        
        # Enter للاختيار
        enter_shortcut = QShortcut(QKeySequence(Qt.Key_Return), self.results_table)
        enter_shortcut.activated.connect(self.select_account)
        
        # Escape للإلغاء
        escape_shortcut = QShortcut(QKeySequence(Qt.Key_Escape), self)
        escape_shortcut.activated.connect(self.reject)
    
    def load_accounts(self):
        """تحميل الحسابات من قاعدة البيانات"""
        try:
            # استعلام الحسابات
            query = """
            SELECT account_code, account_name_ar, account_type, is_active, level, parent_id
            FROM chart_of_accounts 
            WHERE is_active = 1
            ORDER BY account_code
            """
            
            result = self.db_manager.execute_query(query)
            
            self.accounts_data = []
            for row in result:
                account = {
                    'code': str(row[0]) if row[0] else "",
                    'name': str(row[1]) if row[1] else "",
                    'type': str(row[2]) if row[2] else "",
                    'is_active': bool(row[3]) if row[3] is not None else True,
                    'level': int(row[4]) if row[4] is not None else 0,
                    'parent_id': str(row[5]) if row[5] else ""
                }
                self.accounts_data.append(account)
            
            # عرض جميع الحسابات في البداية
            self.filtered_accounts = self.accounts_data.copy()
            self.populate_table()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الحسابات:\n{str(e)}")
            # في حالة الخطأ، إنشاء بيانات تجريبية
            self.create_sample_accounts()
    
    def create_sample_accounts(self):
        """إنشاء بيانات تجريبية للحسابات"""
        self.accounts_data = [
            {'code': '1010001', 'name': 'صندوق المركز الرئيسي', 'type': 'أصول متداولة', 'is_active': True, 'level': 3, 'parent_id': '101000'},
            {'code': '1020001', 'name': 'البنك الأهلي', 'type': 'أصول متداولة', 'is_active': True, 'level': 3, 'parent_id': '102000'},
            {'code': '1020002', 'name': 'بنك الراجحي', 'type': 'أصول متداولة', 'is_active': True, 'level': 3, 'parent_id': '102000'},
            {'code': '1030001', 'name': 'العملاء المحليين', 'type': 'أصول متداولة', 'is_active': True, 'level': 3, 'parent_id': '103000'},
            {'code': '1030002', 'name': 'العملاء الأجانب', 'type': 'أصول متداولة', 'is_active': True, 'level': 3, 'parent_id': '103000'},
            {'code': '2010001', 'name': 'الموردين المحليين', 'type': 'خصوم متداولة', 'is_active': True, 'level': 3, 'parent_id': '201000'},
            {'code': '2010002', 'name': 'الموردين الأجانب', 'type': 'خصوم متداولة', 'is_active': True, 'level': 3, 'parent_id': '201000'},
            {'code': '4010001', 'name': 'مبيعات محلية', 'type': 'إيرادات', 'is_active': True, 'level': 3, 'parent_id': '401000'},
            {'code': '4010002', 'name': 'مبيعات تصدير', 'type': 'إيرادات', 'is_active': True, 'level': 3, 'parent_id': '401000'},
            {'code': '5010001', 'name': 'تكلفة البضاعة المباعة', 'type': 'مصروفات', 'is_active': True, 'level': 3, 'parent_id': '501000'},
        ]
        
        self.filtered_accounts = self.accounts_data.copy()
        self.populate_table()
    
    def populate_table(self):
        """ملء الجدول بالبيانات"""
        self.results_table.setRowCount(len(self.filtered_accounts))
        
        for row, account in enumerate(self.filtered_accounts):
            # رقم الحساب
            code_item = QTableWidgetItem(account['code'])
            code_item.setData(Qt.UserRole, account)  # حفظ بيانات الحساب
            self.results_table.setItem(row, 0, code_item)
            
            # اسم الحساب
            name_item = QTableWidgetItem(account['name'])
            self.results_table.setItem(row, 1, name_item)
            
            # نوع الحساب
            type_item = QTableWidgetItem(account['type'])
            self.results_table.setItem(row, 2, type_item)
            
            # الحالة
            status = "نشط" if account['is_active'] else "غير نشط"
            status_item = QTableWidgetItem(status)
            if account['is_active']:
                status_item.setBackground(Qt.green)
                status_item.setForeground(Qt.white)
            else:
                status_item.setBackground(Qt.red)
                status_item.setForeground(Qt.white)
            self.results_table.setItem(row, 3, status_item)
        
        # تحديث عدد النتائج
        self.results_count_label.setText(f"عدد النتائج: {len(self.filtered_accounts)}")
    
    def on_search_text_changed(self):
        """عند تغيير نص البحث"""
        if self.auto_search_checkbox.isChecked():
            # تأخير البحث قليلاً لتحسين الأداء
            QTimer.singleShot(300, self.filter_accounts)
    
    def filter_accounts(self):
        """فلترة الحسابات حسب البحث"""
        search_text = self.search_edit.text().strip().lower()
        search_type = self.search_type_combo.currentText()
        
        if not search_text:
            self.filtered_accounts = self.accounts_data.copy()
        else:
            self.filtered_accounts = []
            
            for account in self.accounts_data:
                match = False
                
                if search_type == "الكل":
                    if (search_text in account['code'].lower() or 
                        search_text in account['name'].lower() or 
                        search_text in account['type'].lower()):
                        match = True
                elif search_type == "رقم الحساب":
                    if search_text in account['code'].lower():
                        match = True
                elif search_type == "اسم الحساب":
                    if search_text in account['name'].lower():
                        match = True
                elif search_type == "نوع الحساب":
                    if search_text in account['type'].lower():
                        match = True
                
                if match:
                    self.filtered_accounts.append(account)
        
        self.populate_table()
    
    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        has_selection = len(self.results_table.selectedItems()) > 0
        self.select_btn.setEnabled(has_selection)
    
    def select_account(self):
        """اختيار الحساب المحدد"""
        current_row = self.results_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد حساب أولاً")
            return
        
        # الحصول على بيانات الحساب
        code_item = self.results_table.item(current_row, 0)
        if code_item:
            account_data = code_item.data(Qt.UserRole)
            if account_data:
                # إرسال إشارة الاختيار
                self.account_selected.emit(account_data)
                self.accept()
            else:
                QMessageBox.warning(self, "خطأ", "فشل في الحصول على بيانات الحساب")
    
    def keyPressEvent(self, event):
        """معالجة ضغطات المفاتيح"""
        if event.key() == Qt.Key_F9:
            self.search_edit.setFocus()
            self.search_edit.selectAll()
        elif event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
            if self.results_table.hasFocus():
                self.select_account()
            elif self.search_edit.hasFocus():
                # الانتقال للجدول عند الضغط على Enter في البحث
                if self.results_table.rowCount() > 0:
                    self.results_table.setFocus()
                    self.results_table.selectRow(0)
        else:
            super().keyPressEvent(event)

    def filter_cashbox_accounts(self):
        """تصفية الحسابات لإظهار الصناديق والبنوك فقط"""
        try:
            # تصفية الحسابات للصناديق والبنوك
            cashbox_accounts = []

            for account in self.accounts_data:
                # البحث عن الحسابات التي تبدأ بـ 101 (صناديق) أو 102 (بنوك)
                code = account['code']
                if (code.startswith('101') and len(code) >= 6) or (code.startswith('102') and len(code) >= 6):
                    cashbox_accounts.append(account)

            # تحديث القائمة المفلترة
            self.filtered_accounts = cashbox_accounts
            self.populate_table()

            # تحديث النص التوضيحي
            self.search_edit.setPlaceholderText("البحث في الصناديق والبنوك...")

        except Exception as e:
            print(f"خطأ في تصفية الصناديق: {e}")
            # في حالة الخطأ، عرض جميع الحسابات
            self.filtered_accounts = self.accounts_data.copy()
            self.populate_table()


# دالة اختبار نافذة البحث
def main():
    """دالة اختبار نافذة البحث عن الحسابات"""
    import sys
    from PyQt5.QtWidgets import QApplication, QMessageBox
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # تطبيق خط عربي
    font = QFont("Tahoma", 10)
    app.setFont(font)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        def __init__(self):
            self.cursor = type('obj', (object,), {'lastrowid': 1})()

        def execute_query(self, query, params=None):
            # إرجاع بيانات تجريبية للحسابات
            if "chart_of_accounts" in query:
                return [
                    ('1010001', 'صندوق المركز الرئيسي', 'أصول متداولة', 1, 3, '101000'),
                    ('1020001', 'البنك الأهلي', 'أصول متداولة', 1, 3, '102000'),
                    ('1020002', 'بنك الراجحي', 'أصول متداولة', 1, 3, '102000'),
                    ('1030001', 'العملاء المحليين', 'أصول متداولة', 1, 3, '103000'),
                    ('1030002', 'العملاء الأجانب', 'أصول متداولة', 1, 3, '103000'),
                    ('2010001', 'الموردين المحليين', 'خصوم متداولة', 1, 3, '201000'),
                    ('2010002', 'الموردين الأجانب', 'خصوم متداولة', 1, 3, '201000'),
                    ('4010001', 'مبيعات محلية', 'إيرادات', 1, 3, '401000'),
                    ('4010002', 'مبيعات تصدير', 'إيرادات', 1, 3, '401000'),
                    ('5010001', 'تكلفة البضاعة المباعة', 'مصروفات', 1, 3, '501000'),
                ]
            return []

        def execute_update(self, query, params=None):
            return True

    db_manager = MockDBManager()

    # إنشاء نافذة البحث
    search_dialog = AccountSearchDialog(db_manager)

    def on_account_selected(account_data):
        QMessageBox.information(
            search_dialog,
            "تم اختيار الحساب",
            f"تم اختيار الحساب بنجاح:\n\n"
            f"رقم الحساب: {account_data['code']}\n"
            f"اسم الحساب: {account_data['name']}\n"
            f"نوع الحساب: {account_data['type']}\n"
            f"الحالة: {'نشط' if account_data['is_active'] else 'غير نشط'}\n\n"
            f"سيتم الآن إغلاق نافذة البحث."
        )

    search_dialog.account_selected.connect(on_account_selected)
    search_dialog.show()

    print("🔍 تم تشغيل نافذة البحث عن الحسابات!")
    print("📋 الميزات المتاحة:")
    print("   ✅ البحث في رقم الحساب")
    print("   ✅ البحث في اسم الحساب")
    print("   ✅ البحث في نوع الحساب")
    print("   ✅ البحث التلقائي أثناء الكتابة")
    print("   ✅ عرض عدد النتائج")
    print("   ✅ اختصارات لوحة المفاتيح")
    print("")
    print("💡 للاختبار:")
    print("   • اكتب في حقل البحث (مثل: صندوق، بنك، عملاء)")
    print("   • غير نوع البحث من القائمة المنسدلة")
    print("   • انقر نقراً مزدوجاً على أي حساب")
    print("   • اضغط F9 للتركيز على البحث")
    print("   • اضغط Enter للاختيار أو Esc للإلغاء")

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
