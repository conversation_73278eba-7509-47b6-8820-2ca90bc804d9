#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوحدة القيود اليومية
Comprehensive test for journal entries module
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_journal_entries_module():
    """اختبار وحدة القيود اليومية"""
    print("🧪 اختبار وحدة القيود اليومية...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.journal_entries_module import JournalEntriesWidget, JournalEntryDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار إنشاء وحدة القيود اليومية
        journal_widget = JournalEntriesWidget(db_manager)
        print("✅ تم إنشاء وحدة القيود اليومية")
        
        # اختبار تحميل القيود
        journal_widget.load_journal_entries()
        print("✅ تم تحميل القيود من قاعدة البيانات")
        
        # اختبار عدد القيود المحملة
        row_count = journal_widget.entries_table.rowCount()
        print(f"📊 عدد القيود المحملة: {row_count}")
        
        # اختبار إنشاء حوار إضافة قيد
        add_dialog = JournalEntryDialog(db_manager)
        print("✅ تم إنشاء حوار إضافة قيد جديد")
        
        # اختبار إضافة سطر فارغ
        add_dialog.add_empty_row()
        print("✅ تم إضافة سطر فارغ في حوار القيد")
        
        # اختبار حساب الإجماليات
        add_dialog.calculate_totals()
        print("✅ تم اختبار حساب الإجماليات")
        
        # اختبار البحث
        journal_widget.search_entries()
        print("✅ تم اختبار وظيفة البحث")
        
        # اختبار مسح البحث
        journal_widget.clear_search()
        print("✅ تم اختبار مسح البحث")
        
        # تنظيف
        add_dialog.close()
        journal_widget.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وحدة القيود اليومية: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_journal_entry_dialog():
    """اختبار حوار القيد اليومي"""
    print("\n🧪 اختبار حوار القيد اليومي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt, QDate
        from src.modules.journal_entries_module import JournalEntryDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار حوار إضافة قيد جديد
        dialog = JournalEntryDialog(db_manager)
        
        # اختبار ملء البيانات الأساسية
        dialog.date_edit.setDate(QDate.currentDate())
        dialog.description_edit.setText("قيد اختبار")
        print("✅ تم ملء البيانات الأساسية للقيد")
        
        # اختبار إضافة عدة أسطر
        dialog.add_empty_row()
        dialog.add_empty_row()
        print(f"✅ تم إضافة أسطر، العدد الحالي: {dialog.details_table.rowCount()}")
        
        # اختبار تحميل الحسابات
        if dialog.details_table.rowCount() > 0:
            account_combo = dialog.details_table.cellWidget(0, 0)
            if account_combo:
                dialog.load_accounts_to_combo(account_combo)
                print(f"✅ تم تحميل {account_combo.count()} حساب في القائمة المنسدلة")
        
        # اختبار حساب الإجماليات
        dialog.calculate_totals()
        print("✅ تم اختبار حساب الإجماليات في الحوار")
        
        # تنظيف
        dialog.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حوار القيد اليومي: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_journal_database_operations():
    """اختبار عمليات قاعدة البيانات للقيود"""
    print("\n🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار استعلام القيود
        query = """
            SELECT je.id, je.entry_date, je.description, je.total_amount,
                   je.is_posted, je.created_by, je.created_at
            FROM journal_entries je
            ORDER BY je.entry_date DESC, je.id DESC
        """
        
        entries = db_manager.execute_query(query)
        print(f"✅ تم استعلام {len(entries)} قيد من قاعدة البيانات")
        
        # اختبار استعلام تفاصيل القيود
        details_query = """
            SELECT jed.account_code, coa.account_name_ar, jed.description,
                   jed.debit_amount, jed.credit_amount
            FROM journal_entry_details jed
            LEFT JOIN chart_of_accounts coa ON jed.account_code = coa.account_code
            ORDER BY jed.id
        """
        
        details = db_manager.execute_query(details_query)
        print(f"✅ تم استعلام {len(details)} تفصيل قيد من قاعدة البيانات")
        
        # اختبار استعلام الحسابات النشطة
        accounts_query = "SELECT account_code, account_name_ar FROM chart_of_accounts WHERE is_active = 1 ORDER BY account_code"
        accounts = db_manager.execute_query(accounts_query)
        print(f"✅ تم العثور على {len(accounts)} حساب نشط")
        
        # اختبار إحصائيات القيود
        stats_query = """
            SELECT 
                COUNT(*) as total_entries,
                COUNT(CASE WHEN is_posted = 1 THEN 1 END) as posted_entries,
                COUNT(CASE WHEN is_posted = 0 THEN 1 END) as unposted_entries,
                COALESCE(SUM(total_amount), 0) as total_amount
            FROM journal_entries
        """
        
        stats = db_manager.execute_query(stats_query)
        if stats:
            stat = stats[0]
            print(f"📊 إحصائيات القيود:")
            print(f"   📝 إجمالي القيود: {stat['total_entries']}")
            print(f"   ✅ القيود المرحلة: {stat['posted_entries']}")
            print(f"   ⏳ القيود غير المرحلة: {stat['unposted_entries']}")
            print(f"   💰 إجمالي المبالغ: {stat['total_amount']:,.2f}")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار شامل لوحدة القيود اليومية")
    print("   Comprehensive test for journal entries module")
    print("=" * 60)
    
    # اختبار وحدة القيود اليومية
    module_test = test_journal_entries_module()
    
    # اختبار حوار القيد
    dialog_test = test_journal_entry_dialog()
    
    # اختبار عمليات قاعدة البيانات
    database_test = test_journal_database_operations()
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار وحدة القيود اليومية:")
    print(f"   🏗️ اختبار الوحدة: {'✅ نجح' if module_test else '❌ فشل'}")
    print(f"   💬 اختبار الحوار: {'✅ نجح' if dialog_test else '❌ فشل'}")
    print(f"   🗄️ اختبار قاعدة البيانات: {'✅ نجح' if database_test else '❌ فشل'}")
    
    if module_test and dialog_test and database_test:
        print("\n🎉 جميع اختبارات وحدة القيود اليومية نجحت!")
        print("✅ وحدة القيود اليومية جاهزة للاستخدام")
        
        print("\n📋 الوظائف المتاحة:")
        print("   ➕ إضافة قيود يومية جديدة")
        print("   ✏️ تعديل القيود الموجودة")
        print("   🗑️ حذف القيود")
        print("   📤 ترحيل القيود")
        print("   📥 إلغاء ترحيل القيود")
        print("   🔍 البحث والتصفية")
        print("   📊 حساب الأرصدة والتوازن")
        print("   📋 عرض تفاصيل القيود")
    else:
        print("\n⚠️ بعض اختبارات وحدة القيود اليومية فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)
    
    return 0 if (module_test and dialog_test and database_test) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
