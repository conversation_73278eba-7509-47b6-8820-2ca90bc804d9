#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للأنظمة المساعدة المكتملة
Complete Helper Systems Test
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_helper_systems_main():
    """اختبار النظام الرئيسي للأنظمة المساعدة"""
    print("🛠️ اختبار النظام الرئيسي للأنظمة المساعدة...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.helper_systems.helper_systems_main import HelperSystemsMainDialog
        
        # إنشاء النظام الرئيسي
        dialog = HelperSystemsMainDialog()
        print("   ✅ تم إنشاء النظام الرئيسي بنجاح")
        
        # فحص وجود الدوال المطلوبة
        required_functions = [
            'open_calculator',
            'open_calendar', 
            'open_text_editor',
            'open_advanced_search',
            'open_report_generator',
            'open_system_tools',
            'open_currency_converter',
            'open_performance_monitor',
            'open_file_manager',
            'open_password_generator',
            'open_network_monitor',
            'open_clipboard_manager',
            'open_web_browser'
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if hasattr(dialog, func_name):
                print(f"   ✅ {func_name}")
            else:
                missing_functions.append(func_name)
                print(f"   ❌ {func_name}")
        
        if missing_functions:
            print(f"   ⚠️ الدوال المفقودة: {len(missing_functions)}")
            return False
        else:
            print(f"   🎉 جميع الدوال موجودة: {len(required_functions)}")
            return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار النظام الرئيسي: {e}")
        return False

def test_file_manager():
    """اختبار مدير الملفات"""
    print("\n🗂️ اختبار مدير الملفات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.helper_systems.file_manager_system import FileManagerDialog
        
        # إنشاء مدير الملفات
        dialog = FileManagerDialog()
        print("   ✅ تم إنشاء مدير الملفات بنجاح")
        
        # فحص الوظائف الأساسية
        functions = ['load_directory', 'copy_file', 'paste_file', 'delete_file', 'create_new_folder']
        for func in functions:
            if hasattr(dialog, func):
                print(f"   ✅ {func}")
            else:
                print(f"   ❌ {func}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار مدير الملفات: {e}")
        return False

def test_password_generator():
    """اختبار مولد كلمات المرور"""
    print("\n🔐 اختبار مولد كلمات المرور...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.helper_systems.password_generator_system import PasswordGeneratorDialog
        
        # إنشاء مولد كلمات المرور
        dialog = PasswordGeneratorDialog()
        print("   ✅ تم إنشاء مولد كلمات المرور بنجاح")
        
        # اختبار توليد كلمة مرور
        dialog.generate_password()
        password = dialog.password_edit.text()
        
        if password and len(password) > 0:
            print(f"   ✅ تم توليد كلمة مرور: {password[:8]}...")
            
            # اختبار حساب القوة
            score, level = dialog.calculate_strength(password)
            print(f"   ✅ قوة كلمة المرور: {level} ({score}%)")
            
            return True
        else:
            print("   ❌ فشل في توليد كلمة مرور")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار مولد كلمات المرور: {e}")
        return False

def test_network_monitor():
    """اختبار مراقب الشبكة"""
    print("\n🌐 اختبار مراقب الشبكة...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.helper_systems.network_monitor_system import NetworkMonitorDialog
        
        # إنشاء مراقب الشبكة
        dialog = NetworkMonitorDialog()
        print("   ✅ تم إنشاء مراقب الشبكة بنجاح")
        
        # فحص التبويبات
        tab_count = dialog.tabs.count()
        print(f"   ✅ عدد التبويبات: {tab_count}")
        
        # فحص الوظائف الأساسية
        functions = ['start_ping', 'start_port_scan', 'refresh_network_info', 'toggle_monitoring']
        for func in functions:
            if hasattr(dialog, func):
                print(f"   ✅ {func}")
            else:
                print(f"   ❌ {func}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار مراقب الشبكة: {e}")
        return False

def test_existing_systems():
    """اختبار الأنظمة الموجودة"""
    print("\n🧪 اختبار الأنظمة الموجودة...")
    
    systems_to_test = [
        ("🧮 الآلة الحاسبة", "modules.helper_systems.calculator_system", "CalculatorDialog"),
        ("📅 التقويم", "modules.helper_systems.calendar_system", "CalendarDialog"),
        ("📝 محرر النصوص", "modules.helper_systems.text_editor_system", "TextEditorDialog"),
        ("🔍 البحث المتقدم", "modules.helper_systems.search_system", "AdvancedSearchDialog"),
        ("📊 مولد التقارير", "modules.helper_systems.reports_generator_system", "ReportGeneratorDialog"),
        ("🔧 أدوات النظام", "modules.helper_systems.system_tools_system", "SystemToolsDialog"),
        ("💱 محول العملات", "modules.helper_systems.currency_converter_system", "CurrencyConverterDialog"),
        ("📈 مراقب الأداء", "modules.helper_systems.performance_monitor_system", "PerformanceMonitorDialog")
    ]
    
    working_systems = 0
    total_systems = len(systems_to_test)
    
    for system_name, module_name, class_name in systems_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            system_class = getattr(module, class_name)
            print(f"   ✅ {system_name}")
            working_systems += 1
        except Exception as e:
            print(f"   ❌ {system_name}: {e}")
    
    print(f"   📊 الأنظمة العاملة: {working_systems}/{total_systems}")
    return working_systems == total_systems

def test_integration():
    """اختبار التكامل بين الأنظمة"""
    print("\n🔗 اختبار التكامل بين الأنظمة...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.helper_systems.helper_systems_main import HelperSystemsMainDialog
        
        # إنشاء النظام الرئيسي
        main_dialog = HelperSystemsMainDialog()
        
        # اختبار فتح الأنظمة الجديدة
        new_systems = [
            ('open_file_manager', '🗂️ مدير الملفات'),
            ('open_password_generator', '🔐 مولد كلمات المرور'),
            ('open_network_monitor', '🌐 مراقب الشبكة')
        ]
        
        for func_name, system_name in new_systems:
            try:
                func = getattr(main_dialog, func_name)
                print(f"   ✅ {system_name}: دالة موجودة")
            except AttributeError:
                print(f"   ❌ {system_name}: دالة مفقودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التكامل: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار شامل للأنظمة المساعدة المكتملة")
    print("=" * 70)
    
    tests = [
        ("النظام الرئيسي للأنظمة المساعدة", test_helper_systems_main),
        ("مدير الملفات", test_file_manager),
        ("مولد كلمات المرور", test_password_generator),
        ("مراقب الشبكة", test_network_monitor),
        ("الأنظمة الموجودة", test_existing_systems),
        ("التكامل بين الأنظمة", test_integration),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم إكمال تطوير الأنظمة المساعدة بنجاح!")
        print("✅ جميع الأنظمة تعمل بدون مشاكل")
        
        print("\n🛠️ الأنظمة المساعدة المكتملة:")
        print("   ✅ 🧮 الآلة الحاسبة المتقدمة - مكتمل")
        print("   ✅ 📅 التقويم والمواعيد - مكتمل")
        print("   ✅ 📝 محرر النصوص - مكتمل")
        print("   ✅ 🔍 البحث المتقدم - مكتمل")
        print("   ✅ 📊 مولد التقارير - مكتمل")
        print("   ✅ 🔧 أدوات النظام - مكتمل")
        print("   ✅ 💱 محول العملات - مكتمل")
        print("   ✅ 📈 مراقب الأداء - مكتمل")
        print("   ✅ 🗂️ مدير الملفات - مكتمل حديثاً")
        print("   ✅ 🔐 مولد كلمات المرور - مكتمل حديثاً")
        print("   ✅ 🌐 مراقب الشبكة - مكتمل حديثاً")
        
        print("\n🔄 الأنظمة قيد التطوير:")
        print("   🔄 📋 مدير الحافظة المتقدم - قيد التطوير")
        print("   🔄 🌐 متصفح الويب - قيد التطوير")
        
        print("\n🚀 للوصول للأنظمة المساعدة:")
        print("   1. شغل النظام: python src/main_system.py")
        print("   2. انقر على 'الأنظمة المساعدة'")
        print("   3. اختر النظام المطلوب من البطاقات")
        
        print("\n🎯 الميزات الجديدة:")
        print("   🗂️ مدير ملفات متقدم مع عمليات شاملة")
        print("   🔐 مولد كلمات مرور بقوالب متعددة")
        print("   🌐 مراقب شبكة مع اختبار الاتصال وفحص المنافذ")
        print("   📊 واجهات محسنة وسهلة الاستخدام")
        print("   🔗 تكامل كامل مع النظام الرئيسي")
        
    else:
        print("\n⚠️ بعض الأنظمة تحتاج إلى مراجعة")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
