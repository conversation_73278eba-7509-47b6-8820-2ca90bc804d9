# 🧾 دليل فواتير العملاء - الميزات الجديدة

## 📋 **نظرة عامة**

تم إضافة ميزات جديدة لوحدة العملاء والموردين تتيح إنشاء فواتير المبيعات ومردود المبيعات مباشرة من شاشة العملاء.

---

## ✨ **الميزات المضافة**

### 🧾 **فاتورة المبيعات**
- **الوظيفة**: إنشاء فاتورة مبيعات جديدة للعميل المحدد
- **الزر**: `🧾 فاتورة مبيعات`
- **المتطلبات**: تحديد عميل من القائمة
- **النتيجة**: فتح نظام الفواتير المحسن مع تعبئة بيانات العميل تلقائياً

### ↩️ **مردود المبيعات**
- **الوظيفة**: إنشاء فاتورة مردود مبيعات للعميل المحدد
- **الزر**: `↩️ مردود مبيعات`
- **المتطلبات**: تحديد عميل من القائمة
- **النتيجة**: فتح نظام الفواتير مع تعيين نوع الفاتورة كمردود

---

## 🎯 **كيفية الاستخدام**

### 📝 **خطوات إنشاء فاتورة مبيعات:**

1. **فتح وحدة العملاء والموردين**
   ```
   النظام الرئيسي → العملاء والموردين
   ```

2. **تحديد العميل**
   - ابحث عن العميل في القائمة
   - انقر على صف العميل لتحديده
   - تأكد من أن نوع المحدد "عميل" أو "عميل ومورد"

3. **إنشاء الفاتورة**
   - اضغط على زر `🧾 فاتورة مبيعات`
   - سيتم فتح نظام الفواتير المحسن
   - بيانات العميل ستكون معبأة تلقائياً

4. **إكمال الفاتورة**
   - أضف الأصناف والكميات
   - احسب الضرائب والخصومات
   - احفظ الفاتورة

### 🔄 **خطوات إنشاء مردود مبيعات:**

1. **تحديد العميل** (نفس الخطوات أعلاه)

2. **إنشاء المردود**
   - اضغط على زر `↩️ مردود مبيعات`
   - سيتم فتح نظام الفواتير مع تعيين النوع كمردود

3. **إكمال المردود**
   - أدخل الأصناف المرتجعة
   - الكميات ستكون بالسالب تلقائياً
   - احفظ فاتورة المردود

---

## 🔧 **الميزات التقنية**

### 🎛️ **التحكم الذكي في الأزرار**

الأزرار تتفعل وتتعطل تلقائياً حسب:

| نوع المحدد | فاتورة مبيعات | مردود مبيعات | سند قبض | سند صرف |
|------------|----------------|---------------|----------|----------|
| **عميل** | ✅ مفعل | ✅ مفعل | ✅ مفعل | ❌ معطل |
| **مورد** | ❌ معطل | ❌ معطل | ❌ معطل | ✅ مفعل |
| **عميل ومورد** | ✅ مفعل | ✅ مفعل | ✅ مفعل | ✅ مفعل |
| **لا يوجد تحديد** | ❌ معطل | ❌ معطل | ❌ معطل | ❌ معطل |

### 🔗 **التكامل مع النظام**

- **ربط مع نظام الفواتير المحسن**: استخدام `EnhancedInvoiceDialog`
- **تعبئة تلقائية للبيانات**: كود العميل واسمه
- **التحقق من صحة البيانات**: التأكد من وجود العميل
- **معالجة الأخطاء**: رسائل واضحة للمستخدم

---

## 📊 **إحصائيات التحديث**

### ✅ **الملفات المحدثة:**
- `src/modules/customers_suppliers_module.py` - الملف الرئيسي
- `test_customers_invoices.py` - اختبارات شاملة
- `دليل_فواتير_العملاء.md` - هذا الدليل

### 🔢 **الأسطر المضافة:**
- **152 سطر جديد** من الكود
- **4 دوال جديدة**
- **2 زر جديد**
- **تحديث دالة التحكم في الأزرار**

### 🧪 **نتائج الاختبارات:**
- ✅ **5/5 اختبارات نجحت**
- ✅ **استيراد الوحدة**: نجح
- ✅ **وجود الأزرار**: نجح
- ✅ **وجود الدوال**: نجح
- ✅ **دالة ID العميل**: نجح
- ✅ **قاعدة البيانات**: نجح

---

## 🚀 **الاستخدام العملي**

### 💼 **سيناريو عملي:**

1. **عميل يريد شراء منتجات:**
   - افتح وحدة العملاء والموردين
   - ابحث عن العميل وحدده
   - اضغط "فاتورة مبيعات"
   - أضف المنتجات واحفظ الفاتورة

2. **عميل يريد إرجاع منتجات:**
   - حدد العميل من القائمة
   - اضغط "مردود مبيعات"
   - أدخل المنتجات المرتجعة
   - احفظ فاتورة المردود

### 🎯 **الفوائد:**
- **سرعة في العمل**: لا حاجة للانتقال بين الشاشات
- **دقة في البيانات**: تعبئة تلقائية لبيانات العميل
- **سهولة الاستخدام**: أزرار واضحة ومباشرة
- **تكامل شامل**: ربط مع نظام الفواتير المحسن

---

## 🔮 **التطويرات المستقبلية**

### 📋 **مخطط للتحسينات:**
- **فواتير المشتريات**: إضافة أزرار للموردين
- **تقارير سريعة**: عرض إحصائيات العميل
- **تاريخ المعاملات**: عرض آخر الفواتير
- **تنبيهات ذكية**: تذكير بالمستحقات

### 🎨 **تحسينات الواجهة:**
- **أيقونات محسنة**: تصميم أفضل للأزرار
- **ألوان تمييزية**: ألوان مختلفة لكل نوع فاتورة
- **اختصارات لوحة المفاتيح**: F2 للفاتورة، F3 للمردود
- **معاينة سريعة**: عرض معلومات العميل عند التحديد

---

## 📞 **الدعم والمساعدة**

### 🆘 **في حالة وجود مشاكل:**
1. **تأكد من تحديد عميل** قبل الضغط على الأزرار
2. **تحقق من نوع المحدد** (عميل/مورد)
3. **راجع رسائل الخطأ** للحصول على تفاصيل المشكلة
4. **أعد تشغيل النظام** إذا لزم الأمر

### 🔧 **اختبار النظام:**
```bash
# تشغيل اختبارات الفواتير
python test_customers_invoices.py

# تشغيل النظام الرئيسي
python src/main_system.py
```

---

## 🎉 **خلاصة**

تم بنجاح إضافة ميزات فواتير المبيعات ومردود المبيعات لوحدة العملاء والموردين، مما يوفر تجربة مستخدم محسنة وتكامل أفضل بين وحدات النظام.

**النظام الآن جاهز للاستخدام مع الميزات الجديدة! 🚀**
