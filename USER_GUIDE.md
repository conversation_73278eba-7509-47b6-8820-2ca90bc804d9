# دليل المستخدم - نظام Onyx ERP

## 🚀 البدء السريع

### 1. تشغيل النظام
```bash
# الطريقة الأولى - ملف التشغيل المحسن (مُوصى به)
python start_onyx.py

# الطريقة الثانية - ملف التشغيل العادي
python run_onyx.py

# الطريقة الثالثة - على Windows
run_onyx.bat
```

### 2. تسجيل الدخول
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **السنة المالية**: `2025`
- **اللغة**: `عربي`

## 📋 الوحدات الرئيسية

### 🏦 وحدة إدارة الحسابات

#### الوصول للوحدة
1. من القائمة الرئيسية: `المحاسبة` → `دليل الحسابات`
2. أو من الشريط الجانبي: `دليل الحسابات`

#### الوظائف المتاحة
- **عرض الحسابات**: جدول تفاعلي يعرض جميع الحسابات
- **إضافة حساب جديد**: زر `إضافة حساب`
- **تعديل حساب**: النقر المزدوج على الحساب أو زر `تعديل`
- **حذف حساب**: زر `حذف` (مع التحقق من عدم وجود حركات)
- **البحث**: مربع البحث في أعلى الجدول
- **الفلترة**: حسب نوع الحساب (أصول، خصوم، إيرادات، مصروفات)

#### إضافة حساب جديد
1. اضغط على زر `إضافة حساب`
2. املأ البيانات المطلوبة:
   - **رمز الحساب**: رقم فريد للحساب
   - **اسم الحساب (عربي)**: الاسم باللغة العربية
   - **اسم الحساب (إنجليزي)**: الاسم باللغة الإنجليزية
   - **نوع الحساب**: اختر من القائمة المنسدلة
   - **المستوى**: مستوى الحساب في الهيكل الشجري
   - **الحساب الأب**: الحساب الرئيسي (اختياري)
   - **حالة الحساب**: نشط/غير نشط
   - **ملاحظات**: ملاحظات إضافية (اختياري)
3. اضغط `حفظ`

### 📝 وحدة القيود اليومية

#### الوصول للوحدة
1. من القائمة الرئيسية: `المحاسبة` → `القيود اليومية`
2. أو من الشريط الجانبي: `القيود اليومية`

#### الوظائف المتاحة
- **عرض القيود**: جدول يعرض جميع القيود مع تفاصيلها
- **إضافة قيد جديد**: زر `قيد جديد`
- **تعديل قيد**: النقر المزدوج على القيد (للقيود غير المرحلة فقط)
- **حذف قيد**: زر `حذف` (للقيود غير المرحلة فقط)
- **ترحيل قيد**: زر `ترحيل`
- **إلغاء ترحيل**: زر `إلغاء ترحيل`
- **البحث**: بالتاريخ أو الوصف
- **الفلترة**: حسب الحالة (مرحل/غير مرحل)

#### إنشاء قيد يومي جديد
1. اضغط على زر `قيد جديد`
2. املأ البيانات الأساسية:
   - **التاريخ**: تاريخ القيد
   - **الوصف**: وصف القيد
3. أضف تفاصيل القيد:
   - **الحساب**: اختر من قائمة الحسابات
   - **الوصف**: وصف الحركة
   - **مدين**: المبلغ المدين
   - **دائن**: المبلغ الدائن
4. أضف المزيد من التفاصيل حسب الحاجة
5. تأكد من توازن القيد (إجمالي المدين = إجمالي الدائن)
6. اضغط `حفظ`

### 📊 وحدة التقارير المالية

#### الوصول للوحدة
1. من القائمة الرئيسية: `التقارير` → `التقارير المالية`
2. أو من الشريط الجانبي: `التقارير المالية`

#### التقارير المتاحة

##### 1. ميزان المراجعة
- **الوصف**: يعرض أرصدة جميع الحسابات
- **الفلاتر**:
  - فترة زمنية (من تاريخ - إلى تاريخ)
  - نوع الحساب
  - الحسابات النشطة فقط
- **خيارات العرض**:
  - عرض الحسابات بدون حركة
  - عرض الأرصدة الصفرية
- **التصدير**: PDF, Excel

##### 2. كشف حساب تفصيلي
- **الوصف**: يعرض جميع حركات حساب معين
- **الفلاتر**:
  - اختيار الحساب
  - فترة زمنية
- **المعلومات المعروضة**:
  - الرصيد الافتتاحي
  - تفاصيل الحركات
  - الرصيد الختامي

##### 3. تقرير القيود اليومية
- **الوصف**: يعرض القيود اليومية مع تفاصيلها
- **الفلاتر**:
  - فترة زمنية
  - حالة القيد (مرحل/غير مرحل)
  - رقم القيد

## ⚙️ الإعدادات والتخصيص

### إعدادات النظام
- **اللغة**: عربي/إنجليزي
- **السنة المالية**: تحديد بداية ونهاية السنة المالية
- **العملة**: الريال السعودي (افتراضي)
- **الشركة**: معلومات الشركة

### إعدادات المستخدم
- **تغيير كلمة المرور**: من قائمة النظام
- **تفضيلات العرض**: حجم الخط، الألوان
- **اللغة المفضلة**: عربي/إنجليزي

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. رسالة "هل تريد تسجيل الخروج" عند بدء التشغيل
**الحل**: تم إصلاح هذه المشكلة في الإصدار الحالي. إذا ظهرت مرة أخرى:
- تأكد من استخدام `start_onyx.py` للتشغيل
- أعد تشغيل النظام

#### 2. خطأ في الاتصال بقاعدة البيانات
**الحل**:
- تأكد من وجود مجلد `data`
- تأكد من صلاحيات الكتابة في مجلد المشروع
- أعد تشغيل النظام

#### 3. خطأ في استيراد المكتبات
**الحل**:
```bash
pip install PyQt5 bcrypt python-dateutil
```

#### 4. مشاكل في الواجهة الرسومية
**الحل**:
- تأكد من تثبيت PyQt5 بشكل صحيح
- في Linux: `sudo apt-get install python3-pyqt5`
- أعد تشغيل النظام

### رسائل الخطأ الشائعة

#### "خطأ في تنفيذ الاستعلام"
- **السبب**: مشكلة في قاعدة البيانات
- **الحل**: احذف ملف `data/onyx_erp.db` وأعد تشغيل النظام

#### "فشل في فتح النافذة الرئيسية"
- **السبب**: خطأ في تحميل الوحدات
- **الحل**: تأكد من سلامة ملفات المشروع

## 📋 نصائح للاستخدام الأمثل

### 1. إدارة الحسابات
- استخدم ترقيم منطقي للحسابات (مثل: 1000 للأصول، 2000 للخصوم)
- أضف وصف واضح لكل حساب
- استخدم الحسابات الفرعية لتنظيم أفضل

### 2. القيود اليومية
- تأكد من توازن كل قيد قبل الحفظ
- استخدم وصف واضح لكل قيد
- رحل القيود بانتظام
- لا تعدل القيود المرحلة

### 3. التقارير
- استخدم الفلاتر لتحديد البيانات المطلوبة
- صدر التقارير بانتظام للمراجعة
- احتفظ بنسخ من التقارير المهمة

### 4. النسخ الاحتياطي
- انسخ مجلد `data` بانتظام
- احتفظ بنسخ احتياطية في أماكن متعددة
- اختبر استعادة النسخ الاحتياطية

## 🆘 الحصول على المساعدة

### الدعم الفني
- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: https://onyx-erp.com
- **التوثيق**: https://docs.onyx-erp.com

### الإبلاغ عن مشاكل
عند الإبلاغ عن مشكلة، يرجى تضمين:
1. وصف المشكلة بالتفصيل
2. خطوات إعادة إنتاج المشكلة
3. رسائل الخطأ (إن وجدت)
4. معلومات النظام (نظام التشغيل، إصدار Python)
5. لقطة شاشة (إن أمكن)

## 📚 موارد إضافية

### دروس فيديو
- [كيفية إنشاء حساب جديد](link)
- [إنشاء قيد يومي](link)
- [إنتاج التقارير المالية](link)

### أمثلة عملية
- [مثال على دليل حسابات شركة تجارية](link)
- [قيود يومية نموذجية](link)
- [تقارير مالية شهرية](link)

---

**نظام Onyx ERP** - دليل المستخدم الشامل
*الإصدار 1.0.0*