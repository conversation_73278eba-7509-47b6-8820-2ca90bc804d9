# دليل البدء السريع - نظام المحاسبة الشامل
## Quick Start Guide - Comprehensive Accounting System

## 🚀 التشغيل السريع

### 1. التأكد من المتطلبات
```bash
python --version  # يجب أن يكون 3.7+
```

### 2. تثبيت المكتبات المطلوبة
```bash
pip install PyQt5 mysql-connector-python bcrypt python-dateutil
```

### 3. تشغيل النظام
```bash
# التشغيل المبسط (موصى به للاختبار)
python simple_run.py

# أو التشغيل الكامل
python main.py
```

## 📋 بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 🎯 المميزات المتاحة حالياً

### ✅ تم تطويرها
- [x] نافذة تسجيل الدخول الآمنة
- [x] قاعدة بيانات SQLite مع البيانات الأساسية
- [x] نظام المستخدمين مع تشفير كلمات المرور
- [x] دليل الحسابات الافتراضي
- [x] نظام الترجمة (عربي/إنجليزي)
- [x] واجهة المستخدم الأساسية
- [x] لوحة التحكم البسيطة

### 🔄 قيد التطوير
- [ ] إدارة القيود اليومية
- [ ] إدارة الفواتير
- [ ] إدارة المخزون
- [ ] إدارة العملاء والموردين
- [ ] التقارير المالية
- [ ] إدارة الرواتب
- [ ] التحليلات والإحصائيات

## 🗂️ هيكل قاعدة البيانات

### الجداول المتاحة:
1. **users** - المستخدمين
2. **chart_of_accounts** - دليل الحسابات
3. **journal_entries** - القيود اليومية
4. **journal_entry_details** - تفاصيل القيود
5. **customers_suppliers** - العملاء والموردين
6. **products_services** - المنتجات والخدمات
7. **settings** - إعدادات النظام

## 📊 البيانات الافتراضية

### دليل الحسابات:
- **1000** - الأصول
  - **1100** - الأصول المتداولة
    - **1110** - النقدية
    - **1120** - البنوك
    - **1130** - العملاء
    - **1140** - المخزون
  - **1200** - الأصول الثابتة
    - **1210** - الأراضي والمباني
    - **1220** - المعدات

- **2000** - الخصوم
  - **2100** - الخصوم المتداولة
    - **2110** - الموردون
    - **2120** - الضرائب المستحقة
  - **2200** - الخصوم طويلة الأجل
    - **2210** - القروض طويلة الأجل

- **3000** - حقوق الملكية
  - **3100** - رأس المال
  - **3200** - الأرباح المحتجزة

- **4000** - الإيرادات
  - **4100** - إيرادات المبيعات
  - **4200** - إيرادات أخرى

- **5000** - المصروفات
  - **5100** - تكلفة البضاعة المباعة
  - **5200** - مصروفات التشغيل
    - **5210** - الرواتب
    - **5220** - الإيجار
    - **5230** - الكهرباء والماء

## 🛠️ استكشاف الأخطاء

### مشكلة: "ModuleNotFoundError"
```bash
pip install --upgrade pip
pip install PyQt5 mysql-connector-python bcrypt python-dateutil
```

### مشكلة: "Permission denied"
- تشغيل Terminal كمدير
- أو استخدام: `pip install --user`

### مشكلة: خطأ في قاعدة البيانات
- سيتم إنشاء ملف `accounting_system.db` تلقائياً
- لإعادة تعيين قاعدة البيانات: احذف الملف وأعد التشغيل

## 🔧 التخصيص

### تغيير اللغة:
- من نافذة تسجيل الدخول
- أو تعديل `translator.set_language("en")` في الكود

### إضافة مستخدم جديد:
```sql
INSERT INTO users (username, password_hash, full_name, role) 
VALUES ('newuser', 'hashed_password', 'اسم المستخدم', 'user');
```

### إضافة حساب جديد:
```sql
INSERT INTO chart_of_accounts (account_code, account_name_ar, account_name_en, account_type) 
VALUES ('1150', 'حساب جديد', 'New Account', 'asset');
```

## 📁 الملفات المهمة

- `simple_run.py` - التشغيل المبسط
- `main.py` - التشغيل الكامل
- `accounting_system.db` - قاعدة البيانات (يتم إنشاؤها تلقائياً)
- `src/database/sqlite_manager.py` - إدارة قاعدة البيانات
- `src/ui/` - واجهات المستخدم
- `src/utils/` - الأدوات المساعدة

## 🎯 الخطوات التالية للتطوير

1. **إدارة القيود اليومية**
   - نافذة إدخال القيود
   - التحقق من توازن المدين والدائن
   - ترقيم تلقائي للقيود

2. **إدارة الفواتير**
   - فواتير البيع والشراء
   - حساب الضرائب
   - طباعة الفواتير

3. **التقارير المالية**
   - ميزان المراجعة
   - قائمة الدخل
   - الميزانية العمومية

4. **واجهة الويب**
   - استخدام Flask/Django
   - API للتطبيقات الخارجية

## 📞 الدعم

للحصول على المساعدة:
1. تحقق من الأخطاء في Terminal
2. راجع ملف `QUICK_START.md`
3. تأكد من تثبيت جميع المتطلبات

---

**ملاحظة**: هذا إصدار تجريبي للنظام. الوحدات الكاملة قيد التطوير.