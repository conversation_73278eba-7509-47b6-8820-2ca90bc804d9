#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أنواع الهيكل الإداري
Administrative Structure Types
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QMessageBox, 
                             QFrame, QSplitter, QTextEdit, QTabWidget, QWidget,
                             QTreeWidget, QTreeWidgetItem, QListWidget, QListWidgetItem,
                             QProgressBar, QSlider, QDateEdit, QRadioButton, QButtonGroup,
                             QApplication)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon, QPixmap

class AdministrativeStructureTypesDialog(QDialog):
    """حوار أنواع الهيكل الإداري"""
    
    def __init__(self, db_manager=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.structure_types_data = []
        self.positions_data = []
        self.setup_ui()
        self.load_sample_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("أنواع الهيكل الإداري - تنظيم الهيكل الوظيفي والإداري")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("🏗️ أنواع الهيكل الإداري - تنظيم الهيكل الوظيفي والإداري")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات النظام
        self.tabs = QTabWidget()
        
        # تبويب أنواع الهياكل
        structure_types_tab = self.create_structure_types_tab()
        self.tabs.addTab(structure_types_tab, "🏗️ أنواع الهياكل")
        
        # تبويب المناصب والوظائف
        positions_tab = self.create_positions_tab()
        self.tabs.addTab(positions_tab, "👔 المناصب والوظائف")
        
        # تبويب الهيكل التنظيمي
        org_chart_tab = self.create_org_chart_tab()
        self.tabs.addTab(org_chart_tab, "📊 الهيكل التنظيمي")
        
        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        self.tabs.addTab(reports_tab, "📋 التقارير")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_structure_types_tab(self):
        """إنشاء تبويب أنواع الهياكل"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إدارة أنواع الهياكل
        types_group = QGroupBox("🏗️ إدارة أنواع الهياكل الإدارية")
        types_layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        add_type_btn = QPushButton("➕ إضافة نوع هيكل")
        add_type_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_type_btn.clicked.connect(self.add_structure_type)
        
        edit_type_btn = QPushButton("✏️ تعديل نوع")
        edit_type_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_type_btn.clicked.connect(self.edit_structure_type)
        
        delete_type_btn = QPushButton("🗑️ حذف نوع")
        delete_type_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_type_btn.clicked.connect(self.delete_structure_type)
        
        toolbar_layout.addWidget(add_type_btn)
        toolbar_layout.addWidget(edit_type_btn)
        toolbar_layout.addWidget(delete_type_btn)
        toolbar_layout.addStretch()
        
        # جدول أنواع الهياكل
        self.structure_types_table = QTableWidget()
        self.structure_types_table.setColumnCount(6)
        self.structure_types_table.setHorizontalHeaderLabels([
            "رمز النوع", "اسم النوع", "الوصف", "عدد المستويات", "نوع التسلسل", "الحالة"
        ])
        
        # تنسيق الجدول
        self.structure_types_table.horizontalHeader().setStretchLastSection(True)
        self.structure_types_table.setAlternatingRowColors(True)
        self.structure_types_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.structure_types_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 12px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)
        
        types_layout.addLayout(toolbar_layout)
        types_layout.addWidget(self.structure_types_table)
        types_group.setLayout(types_layout)
        
        # مجموعة تفاصيل النوع المحدد
        details_group = QGroupBox("📋 تفاصيل النوع المحدد")
        details_layout = QGridLayout()
        
        # رمز النوع
        details_layout.addWidget(QLabel("رمز النوع:"), 0, 0)
        self.type_code_input = QLineEdit()
        self.type_code_input.setPlaceholderText("مثال: HIER001")
        details_layout.addWidget(self.type_code_input, 0, 1)
        
        # اسم النوع
        details_layout.addWidget(QLabel("اسم النوع:"), 0, 2)
        self.type_name_input = QLineEdit()
        self.type_name_input.setPlaceholderText("مثال: الهيكل الهرمي التقليدي")
        details_layout.addWidget(self.type_name_input, 0, 3)
        
        # عدد المستويات
        details_layout.addWidget(QLabel("عدد المستويات:"), 1, 0)
        self.levels_count_input = QSpinBox()
        self.levels_count_input.setRange(1, 20)
        self.levels_count_input.setValue(5)
        details_layout.addWidget(self.levels_count_input, 1, 1)
        
        # نوع التسلسل
        details_layout.addWidget(QLabel("نوع التسلسل:"), 1, 2)
        self.hierarchy_type_input = QComboBox()
        self.hierarchy_type_input.addItems(["هرمي", "مصفوفي", "شبكي", "مسطح", "وظيفي"])
        details_layout.addWidget(self.hierarchy_type_input, 1, 3)
        
        # الوصف
        details_layout.addWidget(QLabel("الوصف:"), 2, 0)
        self.type_description_input = QTextEdit()
        self.type_description_input.setMaximumHeight(80)
        self.type_description_input.setPlaceholderText("وصف تفصيلي لنوع الهيكل الإداري...")
        details_layout.addWidget(self.type_description_input, 2, 1, 1, 3)
        
        details_group.setLayout(details_layout)
        
        layout.addWidget(types_group)
        layout.addWidget(details_group)
        
        tab.setLayout(layout)
        return tab
        
    def create_positions_tab(self):
        """إنشاء تبويب المناصب والوظائف"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إدارة المناصب
        positions_group = QGroupBox("👔 إدارة المناصب والوظائف")
        positions_layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        add_position_btn = QPushButton("➕ إضافة منصب")
        add_position_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_position_btn.clicked.connect(self.add_position)
        
        edit_position_btn = QPushButton("✏️ تعديل منصب")
        edit_position_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_position_btn.clicked.connect(self.edit_position)
        
        delete_position_btn = QPushButton("🗑️ حذف منصب")
        delete_position_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_position_btn.clicked.connect(self.delete_position)
        
        toolbar_layout.addWidget(add_position_btn)
        toolbar_layout.addWidget(edit_position_btn)
        toolbar_layout.addWidget(delete_position_btn)
        toolbar_layout.addStretch()
        
        # جدول المناصب
        self.positions_table = QTableWidget()
        self.positions_table.setColumnCount(7)
        self.positions_table.setHorizontalHeaderLabels([
            "رمز المنصب", "اسم المنصب", "المستوى", "القسم", "المرتب الأساسي", "المسؤوليات", "الحالة"
        ])
        
        # تنسيق الجدول
        self.positions_table.horizontalHeader().setStretchLastSection(True)
        self.positions_table.setAlternatingRowColors(True)
        self.positions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.positions_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 12px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)
        
        positions_layout.addLayout(toolbar_layout)
        positions_layout.addWidget(self.positions_table)
        positions_group.setLayout(positions_layout)
        
        layout.addWidget(positions_group)
        
        tab.setLayout(layout)
        return tab

    def create_org_chart_tab(self):
        """إنشاء تبويب الهيكل التنظيمي"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة الهيكل التنظيمي
        org_chart_group = QGroupBox("📊 الهيكل التنظيمي المرئي")
        org_chart_layout = QVBoxLayout()

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        refresh_chart_btn = QPushButton("🔄 تحديث الهيكل")
        refresh_chart_btn.clicked.connect(self.refresh_org_chart)

        export_chart_btn = QPushButton("📤 تصدير الهيكل")
        export_chart_btn.clicked.connect(self.export_org_chart)

        print_chart_btn = QPushButton("🖨️ طباعة الهيكل")
        print_chart_btn.clicked.connect(self.print_org_chart)

        toolbar_layout.addWidget(refresh_chart_btn)
        toolbar_layout.addWidget(export_chart_btn)
        toolbar_layout.addWidget(print_chart_btn)
        toolbar_layout.addStretch()

        # شجرة الهيكل التنظيمي
        self.org_chart_tree = QTreeWidget()
        self.org_chart_tree.setHeaderLabels(["المنصب", "الموظف", "القسم", "المستوى"])
        self.org_chart_tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                border: 1px solid #bdc3c7;
                font-size: 12px;
            }
            QTreeWidget::item {
                padding: 5px;
                border-bottom: 1px solid #ecf0f1;
            }
            QTreeWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)

        org_chart_layout.addLayout(toolbar_layout)
        org_chart_layout.addWidget(self.org_chart_tree)
        org_chart_group.setLayout(org_chart_layout)

        layout.addWidget(org_chart_group)

        tab.setLayout(layout)
        return tab

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة تقارير الهيكل الإداري
        reports_group = QGroupBox("📋 تقارير الهيكل الإداري")
        reports_layout = QGridLayout()

        # أزرار التقارير
        structure_report_btn = QPushButton("🏗️ تقرير أنواع الهياكل")
        structure_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        structure_report_btn.clicked.connect(self.generate_structure_report)

        positions_report_btn = QPushButton("👔 تقرير المناصب")
        positions_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        positions_report_btn.clicked.connect(self.generate_positions_report)

        hierarchy_report_btn = QPushButton("📊 تقرير التسلسل الهرمي")
        hierarchy_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        hierarchy_report_btn.clicked.connect(self.generate_hierarchy_report)

        reports_layout.addWidget(structure_report_btn, 0, 0)
        reports_layout.addWidget(positions_report_btn, 0, 1)
        reports_layout.addWidget(hierarchy_report_btn, 0, 2)

        reports_group.setLayout(reports_layout)

        # منطقة عرض التقارير
        reports_display_group = QGroupBox("📄 عرض التقارير")
        reports_display_layout = QVBoxLayout()

        self.reports_display = QTextEdit()
        self.reports_display.setPlaceholderText("ستظهر التقارير هنا...")
        self.reports_display.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        """)

        # أزرار التحكم في التقارير
        report_buttons = QHBoxLayout()

        export_report_btn = QPushButton("📤 تصدير التقرير")
        export_report_btn.clicked.connect(self.export_report)

        print_report_btn = QPushButton("🖨️ طباعة التقرير")
        print_report_btn.clicked.connect(self.print_report)

        clear_report_btn = QPushButton("🗑️ مسح التقرير")
        clear_report_btn.clicked.connect(self.clear_report)

        report_buttons.addWidget(export_report_btn)
        report_buttons.addWidget(print_report_btn)
        report_buttons.addWidget(clear_report_btn)
        report_buttons.addStretch()

        reports_display_layout.addWidget(self.reports_display)
        reports_display_layout.addLayout(report_buttons)
        reports_display_group.setLayout(reports_display_layout)

        layout.addWidget(reports_group)
        layout.addWidget(reports_display_group)

        tab.setLayout(layout)
        return tab

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        from .administrative_structure_functions import create_control_buttons_function
        return create_control_buttons_function(self)

    # ربط الوظائف من الملف المنفصل
    def load_sample_data(self):
        """تحميل البيانات النموذجية"""
        from .administrative_structure_functions import load_sample_data_function
        load_sample_data_function(self)

    def update_structure_types_table(self):
        """تحديث جدول أنواع الهياكل"""
        from .administrative_structure_functions import update_structure_types_table_function
        update_structure_types_table_function(self)

    def update_positions_table(self):
        """تحديث جدول المناصب"""
        from .administrative_structure_functions import update_positions_table_function
        update_positions_table_function(self)

    def build_org_chart(self):
        """بناء الهيكل التنظيمي"""
        from .administrative_structure_functions import build_org_chart_function
        build_org_chart_function(self)

    def add_structure_type(self):
        """إضافة نوع هيكل"""
        from .administrative_structure_functions import add_structure_type_function
        add_structure_type_function(self)

    def edit_structure_type(self):
        """تعديل نوع هيكل"""
        from .administrative_structure_functions import edit_structure_type_function
        edit_structure_type_function(self)

    def delete_structure_type(self):
        """حذف نوع هيكل"""
        from .administrative_structure_functions import delete_structure_type_function
        delete_structure_type_function(self)

    def add_position(self):
        """إضافة منصب"""
        from .administrative_structure_functions import add_position_function
        add_position_function(self)

    def edit_position(self):
        """تعديل منصب"""
        from .administrative_structure_functions import edit_position_function
        edit_position_function(self)

    def delete_position(self):
        """حذف منصب"""
        from .administrative_structure_functions import delete_position_function
        delete_position_function(self)

    def refresh_org_chart(self):
        """تحديث الهيكل التنظيمي"""
        from .administrative_structure_functions import refresh_org_chart_function
        refresh_org_chart_function(self)

    def export_org_chart(self):
        """تصدير الهيكل التنظيمي"""
        from .administrative_structure_functions import export_org_chart_function
        export_org_chart_function(self)

    def print_org_chart(self):
        """طباعة الهيكل التنظيمي"""
        from .administrative_structure_functions import print_org_chart_function
        print_org_chart_function(self)

    def generate_structure_report(self):
        """تقرير أنواع الهياكل"""
        from .administrative_structure_functions import generate_structure_report_function
        generate_structure_report_function(self)

    def generate_positions_report(self):
        """تقرير المناصب"""
        from .administrative_structure_functions import generate_positions_report_function
        generate_positions_report_function(self)

    def generate_hierarchy_report(self):
        """تقرير التسلسل الهرمي"""
        from .administrative_structure_functions import generate_hierarchy_report_function
        generate_hierarchy_report_function(self)

    def export_report(self):
        """تصدير التقرير"""
        from .administrative_structure_functions import export_report_function
        export_report_function(self)

    def print_report(self):
        """طباعة التقرير"""
        from .administrative_structure_functions import print_report_function
        print_report_function(self)

    def clear_report(self):
        """مسح التقرير"""
        from .administrative_structure_functions import clear_report_function
        clear_report_function(self)

    def save_changes(self):
        """حفظ التغييرات"""
        from .administrative_structure_functions import save_changes_function
        save_changes_function(self)

    def show_help(self):
        """عرض المساعدة"""
        from .administrative_structure_functions import show_help_function
        show_help_function(self)


def main():
    """اختبار الشاشة"""
    import sys

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass

    dialog = AdministrativeStructureTypesDialog(MockDBManager())
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
