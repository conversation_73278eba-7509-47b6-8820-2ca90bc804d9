#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مولد التقارير
Reports Generator System
"""

import sys
import os
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QPushButton, QGroupBox, 
                             QTextEdit, QTabWidget, QWidget, QComboBox,
                             QApplication, QMessageBox, QListWidget,
                             QListWidgetItem, QCheckBox, QSpinBox,
                             QDateEdit, QProgressBar, QTableWidget,
                             QTableWidgetItem, QHeaderView, QSplitter,
                             QTreeWidget, QTreeWidgetItem, QRadioButton,
                             QButtonGroup, QSlider, QFrame, QScrollArea)
from PyQt5.QtCore import Qt, QDate, <PERSON><PERSON><PERSON><PERSON>, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QIcon, QPixmap
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog

class ReportGeneratorDialog(QDialog):
    """حوار مولد التقارير"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_report_data = None
        self.report_templates = {}
        self.setup_ui()
        self.load_report_templates()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📊 مولد التقارير المتقدم")
        self.setGeometry(200, 200, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("📊 مولد التقارير المتقدم - إنشاء تقارير احترافية ومخصصة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات النظام
        self.tabs = QTabWidget()
        
        # تبويب إنشاء التقرير
        create_report_tab = self.create_report_creation_tab()
        self.tabs.addTab(create_report_tab, "📝 إنشاء تقرير")
        
        # تبويب قوالب التقارير
        templates_tab = self.create_templates_tab()
        self.tabs.addTab(templates_tab, "📋 قوالب التقارير")
        
        # تبويب معاينة التقرير
        preview_tab = self.create_preview_tab()
        self.tabs.addTab(preview_tab, "👁️ معاينة التقرير")
        
        # تبويب إعدادات التقرير
        settings_tab = self.create_settings_tab()
        self.tabs.addTab(settings_tab, "⚙️ إعدادات التقرير")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_report_creation_tab(self):
        """إنشاء تبويب إنشاء التقرير"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة معلومات التقرير الأساسية
        basic_info_group = QGroupBox("📋 معلومات التقرير الأساسية")
        basic_info_layout = QGridLayout()
        
        # اسم التقرير
        basic_info_layout.addWidget(QLabel("اسم التقرير:"), 0, 0)
        self.report_name = QLineEdit()
        self.report_name.setPlaceholderText("ادخل اسم التقرير...")
        basic_info_layout.addWidget(self.report_name, 0, 1)
        
        # نوع التقرير
        basic_info_layout.addWidget(QLabel("نوع التقرير:"), 0, 2)
        self.report_type = QComboBox()
        self.report_type.addItems([
            "تقرير مالي", "تقرير مبيعات", "تقرير مخزون", "تقرير عملاء",
            "تقرير أرباح وخسائر", "تقرير ميزانية", "تقرير تدفق نقدي",
            "تقرير مخصص"
        ])
        self.report_type.currentTextChanged.connect(self.on_report_type_changed)
        basic_info_layout.addWidget(self.report_type, 0, 3)
        
        # فترة التقرير
        basic_info_layout.addWidget(QLabel("من تاريخ:"), 1, 0)
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        basic_info_layout.addWidget(self.date_from, 1, 1)
        
        basic_info_layout.addWidget(QLabel("إلى تاريخ:"), 1, 2)
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        basic_info_layout.addWidget(self.date_to, 1, 3)
        
        basic_info_group.setLayout(basic_info_layout)
        
        # مجموعة مصادر البيانات
        data_sources_group = QGroupBox("📂 مصادر البيانات")
        data_sources_layout = QVBoxLayout()
        
        # قائمة مصادر البيانات
        sources_scroll = QScrollArea()
        sources_widget = QWidget()
        sources_grid = QGridLayout()
        
        self.data_source_checkboxes = {}
        data_sources = [
            "العملاء والزبائن", "المنتجات والخدمات", "فواتير البيع", "فواتير الشراء",
            "المخزون والمستودعات", "الحسابات المالية", "المدفوعات والمقبوضات",
            "الموردين", "المصروفات", "الإيرادات", "الأصول", "الخصوم"
        ]
        
        for i, source in enumerate(data_sources):
            checkbox = QCheckBox(source)
            checkbox.setChecked(True)
            self.data_source_checkboxes[source] = checkbox
            sources_grid.addWidget(checkbox, i // 3, i % 3)
        
        sources_widget.setLayout(sources_grid)
        sources_scroll.setWidget(sources_widget)
        sources_scroll.setMaximumHeight(150)
        
        data_sources_layout.addWidget(sources_scroll)
        data_sources_group.setLayout(data_sources_layout)
        
        # مجموعة حقول التقرير
        fields_group = QGroupBox("📊 حقول التقرير")
        fields_layout = QHBoxLayout()
        
        # الحقول المتاحة
        available_fields_layout = QVBoxLayout()
        available_fields_layout.addWidget(QLabel("الحقول المتاحة:"))
        
        self.available_fields = QListWidget()
        self.available_fields.addItems([
            "التاريخ", "رقم الفاتورة", "اسم العميل", "اسم المنتج", "الكمية",
            "السعر", "الإجمالي", "الخصم", "الضريبة", "صافي المبلغ",
            "طريقة الدفع", "حالة الدفع", "المندوب", "الفرع"
        ])
        self.available_fields.setMaximumHeight(200)
        available_fields_layout.addWidget(self.available_fields)
        
        # أزرار النقل
        transfer_buttons_layout = QVBoxLayout()
        transfer_buttons_layout.addStretch()
        
        add_field_btn = QPushButton("➡️ إضافة")
        add_field_btn.clicked.connect(self.add_field_to_report)
        transfer_buttons_layout.addWidget(add_field_btn)
        
        remove_field_btn = QPushButton("⬅️ إزالة")
        remove_field_btn.clicked.connect(self.remove_field_from_report)
        transfer_buttons_layout.addWidget(remove_field_btn)
        
        transfer_buttons_layout.addStretch()
        
        # الحقول المحددة
        selected_fields_layout = QVBoxLayout()
        selected_fields_layout.addWidget(QLabel("حقول التقرير:"))
        
        self.selected_fields = QListWidget()
        self.selected_fields.setMaximumHeight(200)
        selected_fields_layout.addWidget(self.selected_fields)
        
        fields_layout.addLayout(available_fields_layout)
        fields_layout.addLayout(transfer_buttons_layout)
        fields_layout.addLayout(selected_fields_layout)
        fields_group.setLayout(fields_layout)
        
        # أزرار العمليات
        operations_layout = QHBoxLayout()
        
        generate_btn = QPushButton("📊 إنشاء التقرير")
        generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        generate_btn.clicked.connect(self.generate_report)
        
        save_template_btn = QPushButton("💾 حفظ كقالب")
        save_template_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        save_template_btn.clicked.connect(self.save_as_template)
        
        operations_layout.addWidget(generate_btn)
        operations_layout.addWidget(save_template_btn)
        operations_layout.addStretch()
        
        layout.addWidget(basic_info_group)
        layout.addWidget(data_sources_group)
        layout.addWidget(fields_group)
        layout.addLayout(operations_layout)
        
        tab.setLayout(layout)
        return tab
        
    def create_templates_tab(self):
        """إنشاء تبويب قوالب التقارير"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة القوالب المحفوظة
        templates_group = QGroupBox("📋 قوالب التقارير المحفوظة")
        templates_layout = QVBoxLayout()
        
        # قائمة القوالب
        self.templates_list = QListWidget()
        self.templates_list.setStyleSheet("""
            QListWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 10px;
                margin: 3px;
                background-color: white;
                border-radius: 5px;
                border-left: 4px solid #3498db;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        self.templates_list.itemClicked.connect(self.on_template_selected)
        
        # أزرار إدارة القوالب
        templates_buttons = QHBoxLayout()
        
        load_template_btn = QPushButton("📂 تحميل قالب")
        load_template_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        load_template_btn.clicked.connect(self.load_template)
        
        edit_template_btn = QPushButton("✏️ تعديل قالب")
        edit_template_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        edit_template_btn.clicked.connect(self.edit_template)
        
        delete_template_btn = QPushButton("🗑️ حذف قالب")
        delete_template_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        delete_template_btn.clicked.connect(self.delete_template)
        
        templates_buttons.addWidget(load_template_btn)
        templates_buttons.addWidget(edit_template_btn)
        templates_buttons.addWidget(delete_template_btn)
        templates_buttons.addStretch()
        
        templates_layout.addWidget(self.templates_list)
        templates_layout.addLayout(templates_buttons)
        templates_group.setLayout(templates_layout)
        
        # مجموعة تفاصيل القالب
        template_details_group = QGroupBox("📄 تفاصيل القالب المحدد")
        template_details_layout = QVBoxLayout()
        
        self.template_details = QTextEdit()
        self.template_details.setReadOnly(True)
        self.template_details.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
            }
        """)
        self.template_details.setPlainText("حدد قالباً من القائمة لعرض التفاصيل...")
        
        template_details_layout.addWidget(self.template_details)
        template_details_group.setLayout(template_details_layout)
        
        layout.addWidget(templates_group)
        layout.addWidget(template_details_group)
        
        tab.setLayout(layout)
        return tab

    def create_preview_tab(self):
        """إنشاء تبويب معاينة التقرير"""
        tab = QWidget()
        layout = QVBoxLayout()

        # شريط أدوات المعاينة
        preview_toolbar = QHBoxLayout()

        refresh_preview_btn = QPushButton("🔄 تحديث المعاينة")
        refresh_preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_preview_btn.clicked.connect(self.refresh_preview)

        print_preview_btn = QPushButton("🖨️ معاينة الطباعة")
        print_preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        print_preview_btn.clicked.connect(self.print_preview)

        export_pdf_btn = QPushButton("📄 تصدير PDF")
        export_pdf_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        export_pdf_btn.clicked.connect(self.export_pdf)

        export_excel_btn = QPushButton("📊 تصدير Excel")
        export_excel_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        export_excel_btn.clicked.connect(self.export_excel)

        preview_toolbar.addWidget(refresh_preview_btn)
        preview_toolbar.addWidget(print_preview_btn)
        preview_toolbar.addWidget(export_pdf_btn)
        preview_toolbar.addWidget(export_excel_btn)
        preview_toolbar.addStretch()

        # منطقة المعاينة
        preview_group = QGroupBox("👁️ معاينة التقرير")
        preview_layout = QVBoxLayout()

        self.report_preview = QTextEdit()
        self.report_preview.setReadOnly(True)
        self.report_preview.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                padding: 15px;
                font-family: 'Arial';
                font-size: 12px;
            }
        """)

        # نص المعاينة الافتراضي
        default_preview = """
📊 معاينة التقرير

لم يتم إنشاء تقرير بعد.
يرجى الانتقال إلى تبويب "إنشاء تقرير" وإنشاء تقرير جديد لعرض المعاينة هنا.

الخطوات:
1. حدد نوع التقرير
2. اختر فترة التقرير
3. حدد مصادر البيانات
4. اختر الحقول المطلوبة
5. انقر على "إنشاء التقرير"
        """

        self.report_preview.setPlainText(default_preview)

        preview_layout.addWidget(self.report_preview)
        preview_group.setLayout(preview_layout)

        layout.addLayout(preview_toolbar)
        layout.addWidget(preview_group)

        tab.setLayout(layout)
        return tab

    def create_settings_tab(self):
        """إنشاء تبويب إعدادات التقرير"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة إعدادات التنسيق
        format_group = QGroupBox("🎨 إعدادات التنسيق")
        format_layout = QGridLayout()

        # خط التقرير
        format_layout.addWidget(QLabel("خط التقرير:"), 0, 0)
        self.report_font = QComboBox()
        self.report_font.addItems(["Arial", "Times New Roman", "Calibri", "Tahoma"])
        format_layout.addWidget(self.report_font, 0, 1)

        # حجم الخط
        format_layout.addWidget(QLabel("حجم الخط:"), 0, 2)
        self.font_size = QSpinBox()
        self.font_size.setRange(8, 24)
        self.font_size.setValue(12)
        format_layout.addWidget(self.font_size, 0, 3)

        # اتجاه الصفحة
        format_layout.addWidget(QLabel("اتجاه الصفحة:"), 1, 0)
        self.page_orientation = QComboBox()
        self.page_orientation.addItems(["عمودي", "أفقي"])
        format_layout.addWidget(self.page_orientation, 1, 1)

        # حجم الصفحة
        format_layout.addWidget(QLabel("حجم الصفحة:"), 1, 2)
        self.page_size = QComboBox()
        self.page_size.addItems(["A4", "A3", "Letter", "Legal"])
        format_layout.addWidget(self.page_size, 1, 3)

        format_group.setLayout(format_layout)

        # مجموعة إعدادات المحتوى
        content_group = QGroupBox("📋 إعدادات المحتوى")
        content_layout = QGridLayout()

        # عنوان التقرير
        content_layout.addWidget(QLabel("عنوان التقرير:"), 0, 0)
        self.report_title = QLineEdit()
        self.report_title.setPlaceholderText("عنوان التقرير...")
        content_layout.addWidget(self.report_title, 0, 1, 1, 3)

        # اسم الشركة
        content_layout.addWidget(QLabel("اسم الشركة:"), 1, 0)
        self.company_name = QLineEdit()
        self.company_name.setText("شركة HGR للإدارة")
        content_layout.addWidget(self.company_name, 1, 1, 1, 3)

        # إظهار التاريخ
        self.show_date = QCheckBox("إظهار تاريخ التقرير")
        self.show_date.setChecked(True)
        content_layout.addWidget(self.show_date, 2, 0)

        # إظهار أرقام الصفحات
        self.show_page_numbers = QCheckBox("إظهار أرقام الصفحات")
        self.show_page_numbers.setChecked(True)
        content_layout.addWidget(self.show_page_numbers, 2, 1)

        # إظهار الإجماليات
        self.show_totals = QCheckBox("إظهار الإجماليات")
        self.show_totals.setChecked(True)
        content_layout.addWidget(self.show_totals, 2, 2)

        # إظهار الرسوم البيانية
        self.show_charts = QCheckBox("إظهار الرسوم البيانية")
        self.show_charts.setChecked(False)
        content_layout.addWidget(self.show_charts, 2, 3)

        content_group.setLayout(content_layout)

        layout.addWidget(format_group)
        layout.addWidget(content_group)
        layout.addStretch()

        tab.setLayout(layout)
        return tab

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        # زر المساعدة
        help_btn = QPushButton("❓ المساعدة")
        help_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        help_btn.clicked.connect(self.show_help)

        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_btn.clicked.connect(self.reject)

        layout.addWidget(help_btn)
        layout.addStretch()
        layout.addWidget(close_btn)

        return layout

    # ربط الوظائف من الملف المنفصل
    def load_report_templates(self):
        """تحميل قوالب التقارير"""
        from .reports_generator_functions import load_report_templates_function
        load_report_templates_function(self)

    def on_report_type_changed(self):
        """عند تغيير نوع التقرير"""
        from .reports_generator_functions import on_report_type_changed_function
        on_report_type_changed_function(self)

    def add_field_to_report(self):
        """إضافة حقل إلى التقرير"""
        from .reports_generator_functions import add_field_to_report_function
        add_field_to_report_function(self)

    def remove_field_from_report(self):
        """إزالة حقل من التقرير"""
        from .reports_generator_functions import remove_field_from_report_function
        remove_field_from_report_function(self)

    def generate_report(self):
        """إنشاء التقرير"""
        from .reports_generator_functions import generate_report_function
        generate_report_function(self)

    def create_report_content(self, criteria):
        """إنشاء محتوى التقرير"""
        from .reports_generator_functions import create_report_content_function
        return create_report_content_function(self, criteria)

    def create_sales_report_content(self, criteria):
        """إنشاء محتوى تقرير المبيعات"""
        from .reports_generator_functions import create_sales_report_content_function
        return create_sales_report_content_function(self, criteria)

    def create_inventory_report_content(self, criteria):
        """إنشاء محتوى تقرير المخزون"""
        from .reports_generator_functions import create_inventory_report_content_function
        return create_inventory_report_content_function(self, criteria)

    def create_customers_report_content(self, criteria):
        """إنشاء محتوى تقرير العملاء"""
        from .reports_generator_functions import create_customers_report_content_function
        return create_customers_report_content_function(self, criteria)

    def create_financial_report_content(self, criteria):
        """إنشاء محتوى التقرير المالي"""
        from .reports_generator_functions import create_financial_report_content_function
        return create_financial_report_content_function(self, criteria)

    def create_generic_report_content(self, criteria):
        """إنشاء محتوى تقرير عام"""
        from .reports_generator_functions import create_generic_report_content_function
        return create_generic_report_content_function(self, criteria)

    # وظائف إضافية
    def save_as_template(self):
        """حفظ التقرير كقالب"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حفظ القوالب قيد التطوير")

    def on_template_selected(self, item):
        """عند تحديد قالب"""
        template_name = item.text()
        if template_name in self.report_templates:
            template = self.report_templates[template_name]

            details_text = f"""
📋 تفاصيل القالب: {template_name}

🏷️ نوع التقرير: {template['type']}

📊 الحقول المتضمنة:
{chr(10).join('• ' + field for field in template['fields'])}

📂 مصادر البيانات:
{chr(10).join('• ' + source for source in template['sources'])}

📝 الوصف:
{template['description']}

💡 لاستخدام هذا القالب:
1. انقر على زر "تحميل قالب"
2. سيتم تطبيق إعدادات القالب تلقائياً
3. يمكنك تعديل الإعدادات حسب الحاجة
4. انقر على "إنشاء التقرير"
            """

            self.template_details.setPlainText(details_text.strip())

    def load_template(self):
        """تحميل قالب محدد"""
        current_item = self.templates_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد قالب أولاً")
            return

        template_name = current_item.text()
        if template_name in self.report_templates:
            template = self.report_templates[template_name]

            # تطبيق إعدادات القالب
            self.report_name.setText(template_name)

            # تحديد نوع التقرير
            type_index = self.report_type.findText(template['type'])
            if type_index >= 0:
                self.report_type.setCurrentIndex(type_index)

            # تحديد مصادر البيانات
            for source, checkbox in self.data_source_checkboxes.items():
                checkbox.setChecked(source in template['sources'])

            # تحديد الحقول
            self.selected_fields.clear()
            for field in template['fields']:
                self.selected_fields.addItem(field)

            # الانتقال إلى تبويب إنشاء التقرير
            self.tabs.setCurrentIndex(0)

            QMessageBox.information(self, "نجح", f"تم تحميل قالب '{template_name}' بنجاح")

    def edit_template(self):
        """تعديل قالب"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل القوالب قيد التطوير")

    def delete_template(self):
        """حذف قالب"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حذف القوالب قيد التطوير")

    def refresh_preview(self):
        """تحديث المعاينة"""
        if self.current_report_data:
            self.report_preview.setHtml(self.current_report_data['content'])
            QMessageBox.information(self, "تم", "تم تحديث المعاينة")
        else:
            QMessageBox.information(self, "تنبيه", "لا يوجد تقرير للمعاينة. يرجى إنشاء تقرير أولاً")

    def print_preview(self):
        """معاينة الطباعة"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة معاينة الطباعة قيد التطوير")

    def export_pdf(self):
        """تصدير PDF"""
        if not self.current_report_data:
            QMessageBox.warning(self, "تحذير", "لا يوجد تقرير للتصدير. يرجى إنشاء تقرير أولاً")
            return

        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير التقرير كـ PDF",
                f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf);;All Files (*)"
            )

            if file_path:
                # محاكاة تصدير PDF
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير إلى:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير PDF:\n{str(e)}")

    def export_excel(self):
        """تصدير Excel"""
        if not self.current_report_data:
            QMessageBox.warning(self, "تحذير", "لا يوجد تقرير للتصدير. يرجى إنشاء تقرير أولاً")
            return

        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير التقرير كـ Excel",
                f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                "Excel Files (*.xlsx);;All Files (*)"
            )

            if file_path:
                # محاكاة تصدير Excel
                QMessageBox.information(self, "نجح", f"تم تصدير التقرير إلى:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير Excel:\n{str(e)}")

    def save_settings(self):
        """حفظ الإعدادات"""
        QMessageBox.information(self, "تم", "تم حفظ إعدادات التقرير")

    def show_help(self):
        """عرض المساعدة"""
        help_text = """
📊 مساعدة مولد التقارير المتقدم

📋 الميزات المتاحة:

📝 إنشاء التقارير:
• تحديد نوع التقرير من 8 أنواع مختلفة
• اختيار فترة التقرير بالتواريخ
• تحديد مصادر البيانات المطلوبة
• اختيار الحقول المراد عرضها
• إنشاء تقارير مخصصة

📋 قوالب التقارير:
• 4 قوالب جاهزة للاستخدام
• تحميل القوالب المحفوظة
• حفظ التقارير كقوالب جديدة
• تعديل وحذف القوالب

👁️ معاينة التقارير:
• معاينة فورية للتقرير
• تحديث المعاينة
• معاينة الطباعة
• تصدير PDF و Excel

⚙️ إعدادات التقرير:
• تخصيص خط وحجم النص
• اختيار اتجاه وحجم الصفحة
• إعدادات المحتوى والعناوين
• خيارات التصدير المتقدمة

🎯 أنواع التقارير المتاحة:
• تقرير مالي: الحسابات والأرصدة
• تقرير مبيعات: فواتير البيع والعملاء
• تقرير مخزون: حالة المخزون والمنتجات
• تقرير عملاء: بيانات العملاء ونشاطهم
• تقرير أرباح وخسائر: التحليل المالي
• تقرير ميزانية: الأصول والخصوم
• تقرير تدفق نقدي: التدفقات المالية
• تقرير مخصص: حسب الاختيار

📊 مصادر البيانات:
• العملاء والزبائن
• المنتجات والخدمات
• فواتير البيع والشراء
• المخزون والمستودعات
• الحسابات المالية
• المدفوعات والمقبوضات

💡 نصائح الاستخدام:
• ابدأ بتحديد نوع التقرير
• اختر الفترة الزمنية المناسبة
• حدد مصادر البيانات ذات الصلة
• اختر الحقول المهمة فقط
• استخدم القوالب لتوفير الوقت

📤 التصدير والطباعة:
• تصدير PDF للمشاركة
• تصدير Excel للتحليل
• معاينة الطباعة قبل الطبع
• حفظ الإعدادات للاستخدام المستقبلي

🔧 الميزات المتقدمة:
• تقارير HTML منسقة
• رسوم بيانية (قيد التطوير)
• جدولة التقارير (قيد التطوير)
• إرسال التقارير بالبريد (قيد التطوير)
        """

        QMessageBox.information(self, "المساعدة", help_text)


def main():
    """اختبار نظام مولد التقارير"""
    import sys

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    dialog = ReportGeneratorDialog()
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
