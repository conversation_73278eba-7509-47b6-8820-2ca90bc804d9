#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أنواع السلامة المهنية
Occupational Safety Types Test
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    print("🚀 تشغيل شاشة أنواع السلامة المهنية...")
    
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    from modules.setup.occupational_safety_types import OccupationalSafetyTypesDialog
    
    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("✅ تم إنشاء التطبيق بنجاح")
    
    dialog = OccupationalSafetyTypesDialog(MockDBManager())
    print("✅ تم إنشاء شاشة أنواع السلامة المهنية بنجاح")
    
    dialog.show()
    print("🦺 شاشة أنواع السلامة المهنية جاهزة!")
    
    print("\n📋 الميزات المتاحة:")
    print("   ✓ إدارة أنواع السلامة المهنية")
    print("   ✓ 5 تبويبات شاملة للإدارة")
    print("   ✓ 6 أنواع سلامة نموذجية")
    print("   ✓ 3 إجراءات سلامة نموذجية")
    print("   ✓ 2 حادث نموذجي للمتابعة")
    print("   ✓ 2 برنامج تدريب نموذجي")
    print("   ✓ 4 أنواع تقارير شاملة")
    print("   ✓ 8 تصنيفات للسلامة")
    
    print("\n🎯 التبويبات المتاحة:")
    print("   🦺 أنواع السلامة - إدارة أنواع السلامة المختلفة")
    print("   🛡️ إجراءات السلامة - تخطيط وتنفيذ الإجراءات")
    print("   ⚠️ الحوادث والمخاطر - تسجيل ومتابعة الحوادث")
    print("   📚 برامج التدريب - تصميم وجدولة التدريب")
    print("   📊 التقارير - تقارير شاملة وإحصائيات")
    
    print("\n🦺 أنواع السلامة النموذجية:")
    print("   🔥 السلامة من الحرائق (عالي الخطورة)")
    print("   🧪 السلامة الكيميائية (حرج)")
    print("   ⚡ السلامة الكهربائية (عالي الخطورة)")
    print("   ⚙️ السلامة الميكانيكية (متوسط الخطورة)")
    print("   🧠 السلامة النفسية (متوسط الخطورة)")
    print("   🌱 السلامة البيئية (متوسط الخطورة)")
    
    print("\n🛡️ إجراءات السلامة النموذجية:")
    print("   🚨 تركيب أجهزة إنذار الحريق (50,000 ريال)")
    print("   🦺 توفير معدات الحماية الشخصية (75,000 ريال)")
    print("   🔍 فحص الأنظمة الكهربائية (30,000 ريال)")
    
    print("\n⚠️ الحوادث المسجلة:")
    print("   🔥 حريق صغير في المخزن الرئيسي (متوسط الخطورة)")
    print("   🧪 انسكاب مواد كيميائية في المختبر (عالي الخطورة)")
    
    print("\n📚 برامج التدريب:")
    print("   🔥 برنامج مكافحة الحرائق (25 متدرب، 15,000 ريال)")
    print("   🧪 التعامل مع المواد الخطرة (15 متدرب، 25,000 ريال)")
    
    print("\n📊 الإحصائيات:")
    print("   🦺 6 أنواع سلامة مهنية")
    print("   🛡️ 3 إجراءات سلامة (إجمالي 155,000 ريال)")
    print("   ⚠️ 2 حادث مسجل")
    print("   📚 2 برنامج تدريب (40 متدرب، 40,000 ريال)")
    print("   📋 8 تصنيفات سلامة مختلفة")
    
    print("\n🎉 الشاشة جاهزة للاستخدام!")
    
    sys.exit(app.exec_())
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من وجود ملفات الوحدة في المسار الصحيح")
    
except Exception as e:
    print(f"❌ خطأ في تشغيل الشاشة: {e}")
    import traceback
    traceback.print_exc()
