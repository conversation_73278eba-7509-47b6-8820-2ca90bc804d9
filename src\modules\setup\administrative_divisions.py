#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة بيانات المحافظات / المناطق / المدن
Administrative Divisions Setup Screen
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QMessageBox, 
                             QFrame, QSplitter, QTextEdit, QTabWidget, QWidget,
                             QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class AdministrativeDivisionsDialog(QDialog):
    """شاشة بيانات المحافظات / المناطق / المدن"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("بيانات المحافظات / المناطق / المدن - الهيكل الإداري الجغرافي")
        self.setGeometry(50, 50, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان الشاشة
        title_label = QLabel("🏙️ الهيكل الإداري الجغرافي الداخلي")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تقسيم الشاشة
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - الشجرة الهرمية
        left_panel = self.create_tree_panel()
        splitter.addWidget(left_panel)
        
        # الجانب الأيمن - نماذج الإدارة
        right_panel = self.create_management_panel()
        splitter.addWidget(right_panel)
        
        # تحديد نسب التقسيم
        splitter.setSizes([500, 900])
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(splitter)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_tree_panel(self):
        """إنشاء لوحة الشجرة الهرمية"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # عنوان القسم
        section_title = QLabel("🌳 الهيكل الهرمي")
        section_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                padding: 10px;
                background-color: #d5dbdb;
                border-radius: 5px;
                margin-bottom: 15px;
            }
        """)
        section_title.setAlignment(Qt.AlignCenter)
        
        # شجرة التقسيمات الإدارية
        self.admin_tree = QTreeWidget()
        self.admin_tree.setHeaderLabels(["التقسيم الإداري", "النوع", "الرمز", "الحالة"])
        self.admin_tree.setRootIsDecorated(True)
        self.admin_tree.setAnimated(True)
        
        self.admin_tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                font-size: 13px;
                outline: none;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
                min-height: 25px;
            }
            QTreeWidget::item:hover {
                background-color: #e8f4fd;
            }
            QTreeWidget::item:selected {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
        """)
        
        # ربط الأحداث
        self.admin_tree.itemClicked.connect(self.on_tree_item_clicked)
        self.admin_tree.itemDoubleClicked.connect(self.on_tree_item_double_clicked)
        
        # أزرار إدارة الشجرة
        tree_buttons = QHBoxLayout()
        
        expand_all_btn = QPushButton("📂 توسيع الكل")
        expand_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        expand_all_btn.clicked.connect(self.admin_tree.expandAll)
        
        collapse_all_btn = QPushButton("📁 طي الكل")
        collapse_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        collapse_all_btn.clicked.connect(self.admin_tree.collapseAll)
        
        refresh_tree_btn = QPushButton("🔄 تحديث")
        refresh_tree_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        refresh_tree_btn.clicked.connect(self.refresh_tree)
        
        tree_buttons.addWidget(expand_all_btn)
        tree_buttons.addWidget(collapse_all_btn)
        tree_buttons.addWidget(refresh_tree_btn)
        tree_buttons.addStretch()
        
        layout.addWidget(section_title)
        layout.addWidget(self.admin_tree)
        layout.addLayout(tree_buttons)
        
        panel.setLayout(layout)
        return panel
        
    def create_management_panel(self):
        """إنشاء لوحة الإدارة"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # تبويبات الإدارة
        tabs = QTabWidget()
        
        # تبويب المحافظات
        governorates_tab = self.create_governorates_tab()
        tabs.addTab(governorates_tab, "🏛️ المحافظات")
        
        # تبويب المناطق
        districts_tab = self.create_districts_tab()
        tabs.addTab(districts_tab, "🏘️ المناطق")
        
        # تبويب المدن
        cities_tab = self.create_cities_tab()
        tabs.addTab(cities_tab, "🏙️ المدن")
        
        # تبويب القرى
        villages_tab = self.create_villages_tab()
        tabs.addTab(villages_tab, "🏡 القرى")
        
        layout.addWidget(tabs)
        panel.setLayout(layout)
        return panel
        
    def create_governorates_tab(self):
        """إنشاء تبويب المحافظات"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # نموذج إضافة محافظة
        gov_form = QGroupBox("➕ إضافة محافظة جديدة")
        form_layout = QGridLayout()
        
        # اسم المحافظة
        form_layout.addWidget(QLabel("اسم المحافظة:"), 0, 0)
        self.gov_name = QLineEdit()
        self.gov_name.setPlaceholderText("مثال: محافظة صنعاء")
        form_layout.addWidget(self.gov_name, 0, 1)
        
        # اسم المحافظة بالإنجليزية
        form_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 0, 2)
        self.gov_name_en = QLineEdit()
        self.gov_name_en.setPlaceholderText("Sana'a Governorate")
        form_layout.addWidget(self.gov_name_en, 0, 3)
        
        # رمز المحافظة
        form_layout.addWidget(QLabel("رمز المحافظة:"), 1, 0)
        self.gov_code = QLineEdit()
        self.gov_code.setPlaceholderText("SA")
        self.gov_code.setMaxLength(5)
        form_layout.addWidget(self.gov_code, 1, 1)
        
        # الدولة التابعة لها
        form_layout.addWidget(QLabel("الدولة:"), 1, 2)
        self.gov_country = QComboBox()
        form_layout.addWidget(self.gov_country, 1, 3)
        
        # العاصمة
        form_layout.addWidget(QLabel("العاصمة:"), 2, 0)
        self.gov_capital = QLineEdit()
        self.gov_capital.setPlaceholderText("مثال: مدينة صنعاء")
        form_layout.addWidget(self.gov_capital, 2, 1)
        
        # المساحة (كم²)
        form_layout.addWidget(QLabel("المساحة (كم²):"), 2, 2)
        self.gov_area = QDoubleSpinBox()
        self.gov_area.setRange(0.0, 999999.99)
        self.gov_area.setSuffix(" كم²")
        form_layout.addWidget(self.gov_area, 2, 3)
        
        # محافظة نشطة
        self.gov_active = QCheckBox("محافظة نشطة")
        self.gov_active.setChecked(True)
        form_layout.addWidget(self.gov_active, 3, 0, 1, 2)
        
        # محافظة رئيسية
        self.gov_main = QCheckBox("محافظة رئيسية (عاصمة)")
        form_layout.addWidget(self.gov_main, 3, 2, 1, 2)
        
        gov_form.setLayout(form_layout)
        
        # أزرار إدارة المحافظات
        gov_buttons = QHBoxLayout()
        
        add_gov_btn = QPushButton("🏛️ إضافة محافظة")
        add_gov_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_gov_btn.clicked.connect(self.add_governorate)
        
        edit_gov_btn = QPushButton("✏️ تعديل")
        edit_gov_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_gov_btn.clicked.connect(self.edit_governorate)
        
        delete_gov_btn = QPushButton("🗑️ حذف")
        delete_gov_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_gov_btn.clicked.connect(self.delete_governorate)
        
        gov_buttons.addWidget(add_gov_btn)
        gov_buttons.addWidget(edit_gov_btn)
        gov_buttons.addWidget(delete_gov_btn)
        gov_buttons.addStretch()
        
        # جدول المحافظات
        self.governorates_table = QTableWidget()
        self.governorates_table.setColumnCount(7)
        self.governorates_table.setHorizontalHeaderLabels([
            "اسم المحافظة", "الاسم بالإنجليزية", "الرمز", "الدولة", "العاصمة", "المساحة", "الحالة"
        ])
        
        # تنسيق الجدول
        self.governorates_table.horizontalHeader().setStretchLastSection(True)
        self.governorates_table.setAlternatingRowColors(True)
        self.governorates_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.governorates_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        layout.addWidget(gov_form)
        layout.addLayout(gov_buttons)
        layout.addWidget(QLabel("📋 المحافظات المعرفة:"))
        layout.addWidget(self.governorates_table)
        
        tab.setLayout(layout)
        return tab
        
    def create_districts_tab(self):
        """إنشاء تبويب المناطق"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # نموذج إضافة منطقة
        dist_form = QGroupBox("➕ إضافة منطقة جديدة")
        form_layout = QGridLayout()
        
        # اسم المنطقة
        form_layout.addWidget(QLabel("اسم المنطقة:"), 0, 0)
        self.dist_name = QLineEdit()
        self.dist_name.setPlaceholderText("مثال: منطقة الثورة")
        form_layout.addWidget(self.dist_name, 0, 1)
        
        # اسم المنطقة بالإنجليزية
        form_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 0, 2)
        self.dist_name_en = QLineEdit()
        self.dist_name_en.setPlaceholderText("Al-Thawra District")
        form_layout.addWidget(self.dist_name_en, 0, 3)
        
        # رمز المنطقة
        form_layout.addWidget(QLabel("رمز المنطقة:"), 1, 0)
        self.dist_code = QLineEdit()
        self.dist_code.setPlaceholderText("TH")
        self.dist_code.setMaxLength(5)
        form_layout.addWidget(self.dist_code, 1, 1)
        
        # المحافظة التابعة لها
        form_layout.addWidget(QLabel("المحافظة:"), 1, 2)
        self.dist_governorate = QComboBox()
        form_layout.addWidget(self.dist_governorate, 1, 3)
        
        # نوع المنطقة
        form_layout.addWidget(QLabel("نوع المنطقة:"), 2, 0)
        self.dist_type = QComboBox()
        self.dist_type.addItems(["مديرية", "منطقة", "قضاء", "ناحية"])
        form_layout.addWidget(self.dist_type, 2, 1)
        
        # منطقة نشطة
        self.dist_active = QCheckBox("منطقة نشطة")
        self.dist_active.setChecked(True)
        form_layout.addWidget(self.dist_active, 2, 2, 1, 2)
        
        dist_form.setLayout(form_layout)
        
        # أزرار إدارة المناطق
        dist_buttons = QHBoxLayout()
        
        add_dist_btn = QPushButton("🏘️ إضافة منطقة")
        add_dist_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_dist_btn.clicked.connect(self.add_district)
        
        edit_dist_btn = QPushButton("✏️ تعديل")
        edit_dist_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_dist_btn.clicked.connect(self.edit_district)
        
        delete_dist_btn = QPushButton("🗑️ حذف")
        delete_dist_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_dist_btn.clicked.connect(self.delete_district)
        
        dist_buttons.addWidget(add_dist_btn)
        dist_buttons.addWidget(edit_dist_btn)
        dist_buttons.addWidget(delete_dist_btn)
        dist_buttons.addStretch()
        
        # جدول المناطق
        self.districts_table = QTableWidget()
        self.districts_table.setColumnCount(6)
        self.districts_table.setHorizontalHeaderLabels([
            "اسم المنطقة", "الاسم بالإنجليزية", "الرمز", "المحافظة", "النوع", "الحالة"
        ])
        
        # تنسيق الجدول
        self.districts_table.horizontalHeader().setStretchLastSection(True)
        self.districts_table.setAlternatingRowColors(True)
        self.districts_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.districts_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        layout.addWidget(dist_form)
        layout.addLayout(dist_buttons)
        layout.addWidget(QLabel("📋 المناطق المعرفة:"))
        layout.addWidget(self.districts_table)
        
        tab.setLayout(layout)
        return tab
        
    def create_cities_tab(self):
        """إنشاء تبويب المدن"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # نموذج إضافة مدينة
        city_form = QGroupBox("➕ إضافة مدينة جديدة")
        form_layout = QGridLayout()
        
        # اسم المدينة
        form_layout.addWidget(QLabel("اسم المدينة:"), 0, 0)
        self.city_name = QLineEdit()
        self.city_name.setPlaceholderText("مثال: مدينة صنعاء")
        form_layout.addWidget(self.city_name, 0, 1)
        
        # اسم المدينة بالإنجليزية
        form_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 0, 2)
        self.city_name_en = QLineEdit()
        self.city_name_en.setPlaceholderText("Sana'a City")
        form_layout.addWidget(self.city_name_en, 0, 3)
        
        # رمز المدينة
        form_layout.addWidget(QLabel("رمز المدينة:"), 1, 0)
        self.city_code = QLineEdit()
        self.city_code.setPlaceholderText("SAN")
        self.city_code.setMaxLength(5)
        form_layout.addWidget(self.city_code, 1, 1)
        
        # المنطقة التابعة لها
        form_layout.addWidget(QLabel("المنطقة:"), 1, 2)
        self.city_district = QComboBox()
        form_layout.addWidget(self.city_district, 1, 3)
        
        # نوع المدينة
        form_layout.addWidget(QLabel("نوع المدينة:"), 2, 0)
        self.city_type = QComboBox()
        self.city_type.addItems(["مدينة كبرى", "مدينة", "بلدة", "قصبة"])
        form_layout.addWidget(self.city_type, 2, 1)
        
        # عدد السكان (تقديري)
        form_layout.addWidget(QLabel("عدد السكان:"), 2, 2)
        self.city_population = QSpinBox()
        self.city_population.setRange(0, 99999999)
        self.city_population.setSuffix(" نسمة")
        form_layout.addWidget(self.city_population, 2, 3)
        
        # مدينة نشطة
        self.city_active = QCheckBox("مدينة نشطة")
        self.city_active.setChecked(True)
        form_layout.addWidget(self.city_active, 3, 0, 1, 2)
        
        # مدينة رئيسية
        self.city_main = QCheckBox("مدينة رئيسية")
        form_layout.addWidget(self.city_main, 3, 2, 1, 2)
        
        city_form.setLayout(form_layout)
        
        # أزرار إدارة المدن
        city_buttons = QHBoxLayout()
        
        add_city_btn = QPushButton("🏙️ إضافة مدينة")
        add_city_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_city_btn.clicked.connect(self.add_city)
        
        edit_city_btn = QPushButton("✏️ تعديل")
        edit_city_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_city_btn.clicked.connect(self.edit_city)
        
        delete_city_btn = QPushButton("🗑️ حذف")
        delete_city_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_city_btn.clicked.connect(self.delete_city)
        
        city_buttons.addWidget(add_city_btn)
        city_buttons.addWidget(edit_city_btn)
        city_buttons.addWidget(delete_city_btn)
        city_buttons.addStretch()
        
        # جدول المدن
        self.cities_table = QTableWidget()
        self.cities_table.setColumnCount(7)
        self.cities_table.setHorizontalHeaderLabels([
            "اسم المدينة", "الاسم بالإنجليزية", "الرمز", "المنطقة", "النوع", "عدد السكان", "الحالة"
        ])
        
        # تنسيق الجدول
        self.cities_table.horizontalHeader().setStretchLastSection(True)
        self.cities_table.setAlternatingRowColors(True)
        self.cities_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.cities_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        layout.addWidget(city_form)
        layout.addLayout(city_buttons)
        layout.addWidget(QLabel("📋 المدن المعرفة:"))
        layout.addWidget(self.cities_table)
        
        tab.setLayout(layout)
        return tab
        
    def create_villages_tab(self):
        """إنشاء تبويب القرى"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # نموذج إضافة قرية
        village_form = QGroupBox("➕ إضافة قرية جديدة")
        form_layout = QGridLayout()
        
        # اسم القرية
        form_layout.addWidget(QLabel("اسم القرية:"), 0, 0)
        self.village_name = QLineEdit()
        self.village_name.setPlaceholderText("مثال: قرية الروضة")
        form_layout.addWidget(self.village_name, 0, 1)
        
        # اسم القرية بالإنجليزية
        form_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 0, 2)
        self.village_name_en = QLineEdit()
        self.village_name_en.setPlaceholderText("Al-Rawda Village")
        form_layout.addWidget(self.village_name_en, 0, 3)
        
        # رمز القرية
        form_layout.addWidget(QLabel("رمز القرية:"), 1, 0)
        self.village_code = QLineEdit()
        self.village_code.setPlaceholderText("RW")
        self.village_code.setMaxLength(5)
        form_layout.addWidget(self.village_code, 1, 1)
        
        # المدينة التابعة لها
        form_layout.addWidget(QLabel("المدينة:"), 1, 2)
        self.village_city = QComboBox()
        form_layout.addWidget(self.village_city, 1, 3)
        
        # نوع القرية
        form_layout.addWidget(QLabel("نوع القرية:"), 2, 0)
        self.village_type = QComboBox()
        self.village_type.addItems(["قرية", "عزلة", "حي", "منطقة سكنية"])
        form_layout.addWidget(self.village_type, 2, 1)
        
        # قرية نشطة
        self.village_active = QCheckBox("قرية نشطة")
        self.village_active.setChecked(True)
        form_layout.addWidget(self.village_active, 2, 2, 1, 2)
        
        village_form.setLayout(form_layout)
        
        # أزرار إدارة القرى
        village_buttons = QHBoxLayout()
        
        add_village_btn = QPushButton("🏡 إضافة قرية")
        add_village_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_village_btn.clicked.connect(self.add_village)
        
        edit_village_btn = QPushButton("✏️ تعديل")
        edit_village_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_village_btn.clicked.connect(self.edit_village)
        
        delete_village_btn = QPushButton("🗑️ حذف")
        delete_village_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_village_btn.clicked.connect(self.delete_village)
        
        village_buttons.addWidget(add_village_btn)
        village_buttons.addWidget(edit_village_btn)
        village_buttons.addWidget(delete_village_btn)
        village_buttons.addStretch()
        
        # جدول القرى
        self.villages_table = QTableWidget()
        self.villages_table.setColumnCount(6)
        self.villages_table.setHorizontalHeaderLabels([
            "اسم القرية", "الاسم بالإنجليزية", "الرمز", "المدينة", "النوع", "الحالة"
        ])
        
        # تنسيق الجدول
        self.villages_table.horizontalHeader().setStretchLastSection(True)
        self.villages_table.setAlternatingRowColors(True)
        self.villages_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.villages_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        layout.addWidget(village_form)
        layout.addLayout(village_buttons)
        layout.addWidget(QLabel("📋 القرى المعرفة:"))
        layout.addWidget(self.villages_table)
        
        tab.setLayout(layout)
        return tab

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        # زر الحفظ
        save_btn = QPushButton("💾 حفظ جميع التغييرات")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_all_changes)

        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        # زر تحديث الشجرة
        refresh_btn = QPushButton("🌳 تحديث الشجرة")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_tree)

        layout.addWidget(refresh_btn)
        layout.addStretch()
        layout.addWidget(save_btn)
        layout.addWidget(cancel_btn)

        return layout

    def load_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            # تحميل الدول للقائمة المنسدلة
            self.load_countries()

            # تحميل بيانات نموذجية
            self.load_sample_data()

            # بناء الشجرة الهرمية
            self.build_admin_tree()

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")

    def load_countries(self):
        """تحميل قائمة الدول"""
        countries = [
            "الجمهورية اليمنية",
            "المملكة العربية السعودية",
            "دولة الإمارات العربية المتحدة",
            "جمهورية مصر العربية",
            "المملكة الأردنية الهاشمية"
        ]

        self.gov_country.clear()
        for country in countries:
            self.gov_country.addItem(country)

    def load_sample_data(self):
        """تحميل بيانات نموذجية"""
        # محافظات نموذجية
        sample_governorates = [
            ("محافظة صنعاء", "Sana'a Governorate", "SA", "الجمهورية اليمنية", "مدينة صنعاء", 13850.0, True, True),
            ("محافظة عدن", "Aden Governorate", "AD", "الجمهورية اليمنية", "مدينة عدن", 760.0, True, False),
            ("محافظة تعز", "Taiz Governorate", "TA", "الجمهورية اليمنية", "مدينة تعز", 10677.0, True, False),
            ("محافظة الحديدة", "Hodeidah Governorate", "HO", "الجمهورية اليمنية", "مدينة الحديدة", 17509.0, True, False),
            ("محافظة إب", "Ibb Governorate", "IB", "الجمهورية اليمنية", "مدينة إب", 5344.0, True, False)
        ]

        self.governorates_table.setRowCount(len(sample_governorates))
        for row, (name, name_en, code, country, capital, area, active, main) in enumerate(sample_governorates):
            self.governorates_table.setItem(row, 0, QTableWidgetItem(name))
            self.governorates_table.setItem(row, 1, QTableWidgetItem(name_en))
            self.governorates_table.setItem(row, 2, QTableWidgetItem(code))
            self.governorates_table.setItem(row, 3, QTableWidgetItem(country))
            self.governorates_table.setItem(row, 4, QTableWidgetItem(capital))
            self.governorates_table.setItem(row, 5, QTableWidgetItem(f"{area:.2f}"))

            status = "نشطة"
            if main:
                status += " (رئيسية)"
            elif not active:
                status = "غير نشطة"
            self.governorates_table.setItem(row, 6, QTableWidgetItem(status))

        # تحديث قوائم المحافظات
        self.update_governorate_combos()

        # مناطق نموذجية
        sample_districts = [
            ("مديرية الثورة", "Al-Thawra District", "TH", "محافظة صنعاء", "مديرية", True),
            ("مديرية الصافية", "As-Safiyah District", "SF", "محافظة صنعاء", "مديرية", True),
            ("مديرية كريتر", "Crater District", "CR", "محافظة عدن", "مديرية", True),
            ("مديرية المعلا", "Al-Mualla District", "ML", "محافظة عدن", "مديرية", True),
            ("مديرية صالة", "Salh District", "SL", "محافظة تعز", "مديرية", True)
        ]

        self.districts_table.setRowCount(len(sample_districts))
        for row, (name, name_en, code, gov, type_name, active) in enumerate(sample_districts):
            self.districts_table.setItem(row, 0, QTableWidgetItem(name))
            self.districts_table.setItem(row, 1, QTableWidgetItem(name_en))
            self.districts_table.setItem(row, 2, QTableWidgetItem(code))
            self.districts_table.setItem(row, 3, QTableWidgetItem(gov))
            self.districts_table.setItem(row, 4, QTableWidgetItem(type_name))
            status = "نشطة" if active else "غير نشطة"
            self.districts_table.setItem(row, 5, QTableWidgetItem(status))

        # تحديث قوائم المناطق
        self.update_district_combos()

        # مدن نموذجية
        sample_cities = [
            ("مدينة صنعاء", "Sana'a City", "SAN", "مديرية الثورة", "مدينة كبرى", 2500000, True, True),
            ("مدينة عدن", "Aden City", "ADE", "مديرية كريتر", "مدينة كبرى", 800000, True, False),
            ("مدينة تعز", "Taiz City", "TAI", "مديرية صالة", "مدينة", 600000, True, False),
            ("مدينة الحديدة", "Hodeidah City", "HOD", "مديرية الحديدة", "مدينة", 400000, True, False)
        ]

        self.cities_table.setRowCount(len(sample_cities))
        for row, (name, name_en, code, district, type_name, population, active, main) in enumerate(sample_cities):
            self.cities_table.setItem(row, 0, QTableWidgetItem(name))
            self.cities_table.setItem(row, 1, QTableWidgetItem(name_en))
            self.cities_table.setItem(row, 2, QTableWidgetItem(code))
            self.cities_table.setItem(row, 3, QTableWidgetItem(district))
            self.cities_table.setItem(row, 4, QTableWidgetItem(type_name))
            self.cities_table.setItem(row, 5, QTableWidgetItem(f"{population:,}"))

            status = "نشطة"
            if main:
                status += " (رئيسية)"
            elif not active:
                status = "غير نشطة"
            self.cities_table.setItem(row, 6, QTableWidgetItem(status))

        # تحديث قوائم المدن
        self.update_city_combos()

        # قرى نموذجية
        sample_villages = [
            ("قرية الروضة", "Al-Rawda Village", "RW", "مدينة صنعاء", "قرية", True),
            ("حي الزبيري", "Al-Zubairi Neighborhood", "ZU", "مدينة صنعاء", "حي", True),
            ("عزلة الشعب", "Al-Shaab Uzlah", "SH", "مدينة عدن", "عزلة", True)
        ]

        self.villages_table.setRowCount(len(sample_villages))
        for row, (name, name_en, code, city, type_name, active) in enumerate(sample_villages):
            self.villages_table.setItem(row, 0, QTableWidgetItem(name))
            self.villages_table.setItem(row, 1, QTableWidgetItem(name_en))
            self.villages_table.setItem(row, 2, QTableWidgetItem(code))
            self.villages_table.setItem(row, 3, QTableWidgetItem(city))
            self.villages_table.setItem(row, 4, QTableWidgetItem(type_name))
            status = "نشطة" if active else "غير نشطة"
            self.villages_table.setItem(row, 5, QTableWidgetItem(status))

    def update_governorate_combos(self):
        """تحديث قوائم المحافظات"""
        self.dist_governorate.clear()
        for row in range(self.governorates_table.rowCount()):
            gov_name = self.governorates_table.item(row, 0).text()
            self.dist_governorate.addItem(gov_name)

    def update_district_combos(self):
        """تحديث قوائم المناطق"""
        self.city_district.clear()
        for row in range(self.districts_table.rowCount()):
            dist_name = self.districts_table.item(row, 0).text()
            self.city_district.addItem(dist_name)

    def update_city_combos(self):
        """تحديث قوائم المدن"""
        self.village_city.clear()
        for row in range(self.cities_table.rowCount()):
            city_name = self.cities_table.item(row, 0).text()
            self.village_city.addItem(city_name)

    def build_admin_tree(self):
        """بناء الشجرة الهرمية للتقسيمات الإدارية"""
        self.admin_tree.clear()

        # إنشاء عقد المحافظات
        for gov_row in range(self.governorates_table.rowCount()):
            gov_name = self.governorates_table.item(gov_row, 0).text()
            gov_code = self.governorates_table.item(gov_row, 2).text()
            gov_status = self.governorates_table.item(gov_row, 6).text()

            gov_item = QTreeWidgetItem([gov_name, "محافظة", gov_code, gov_status])
            gov_item.setExpanded(True)

            # إضافة المناطق التابعة للمحافظة
            for dist_row in range(self.districts_table.rowCount()):
                dist_gov = self.districts_table.item(dist_row, 3).text()
                if dist_gov == gov_name:
                    dist_name = self.districts_table.item(dist_row, 0).text()
                    dist_code = self.districts_table.item(dist_row, 2).text()
                    dist_type = self.districts_table.item(dist_row, 4).text()
                    dist_status = self.districts_table.item(dist_row, 5).text()

                    dist_item = QTreeWidgetItem([dist_name, dist_type, dist_code, dist_status])
                    dist_item.setExpanded(True)

                    # إضافة المدن التابعة للمنطقة
                    for city_row in range(self.cities_table.rowCount()):
                        city_dist = self.cities_table.item(city_row, 3).text()
                        if city_dist == dist_name:
                            city_name = self.cities_table.item(city_row, 0).text()
                            city_code = self.cities_table.item(city_row, 2).text()
                            city_type = self.cities_table.item(city_row, 4).text()
                            city_status = self.cities_table.item(city_row, 6).text()

                            city_item = QTreeWidgetItem([city_name, city_type, city_code, city_status])
                            city_item.setExpanded(True)

                            # إضافة القرى التابعة للمدينة
                            for village_row in range(self.villages_table.rowCount()):
                                village_city = self.villages_table.item(village_row, 3).text()
                                if village_city == city_name:
                                    village_name = self.villages_table.item(village_row, 0).text()
                                    village_code = self.villages_table.item(village_row, 2).text()
                                    village_type = self.villages_table.item(village_row, 4).text()
                                    village_status = self.villages_table.item(village_row, 5).text()

                                    village_item = QTreeWidgetItem([village_name, village_type, village_code, village_status])
                                    city_item.addChild(village_item)

                            dist_item.addChild(city_item)

                    gov_item.addChild(dist_item)

            self.admin_tree.addTopLevelItem(gov_item)

    def refresh_tree(self):
        """تحديث الشجرة الهرمية"""
        self.build_admin_tree()
        QMessageBox.information(self, "تم", "تم تحديث الشجرة الهرمية بنجاح")

    def on_tree_item_clicked(self, item, column):
        """معالجة النقر على عنصر في الشجرة"""
        item_name = item.text(0)
        item_type = item.text(1)
        print(f"تم النقر على: {item_name} ({item_type})")

    def on_tree_item_double_clicked(self, item, column):
        """معالجة النقر المزدوج على عنصر في الشجرة"""
        item_name = item.text(0)
        item_type = item.text(1)
        QMessageBox.information(self, "معلومات",
                               f"📍 {item_type}: {item_name}\n"
                               f"🔤 الرمز: {item.text(2)}\n"
                               f"📊 الحالة: {item.text(3)}")

    # وظائف إدارة المحافظات
    def add_governorate(self):
        """إضافة محافظة جديدة"""
        try:
            if not self.gov_name.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المحافظة")
                return

            # إضافة المحافظة للجدول
            row = self.governorates_table.rowCount()
            self.governorates_table.insertRow(row)

            self.governorates_table.setItem(row, 0, QTableWidgetItem(self.gov_name.text()))
            self.governorates_table.setItem(row, 1, QTableWidgetItem(self.gov_name_en.text()))
            self.governorates_table.setItem(row, 2, QTableWidgetItem(self.gov_code.text().upper()))
            self.governorates_table.setItem(row, 3, QTableWidgetItem(self.gov_country.currentText()))
            self.governorates_table.setItem(row, 4, QTableWidgetItem(self.gov_capital.text()))
            self.governorates_table.setItem(row, 5, QTableWidgetItem(f"{self.gov_area.value():.2f}"))

            status = "نشطة"
            if self.gov_main.isChecked():
                status += " (رئيسية)"
            elif not self.gov_active.isChecked():
                status = "غير نشطة"
            self.governorates_table.setItem(row, 6, QTableWidgetItem(status))

            # تنظيف النموذج
            self.clear_governorate_form()

            # تحديث القوائم والشجرة
            self.update_governorate_combos()
            self.build_admin_tree()

            QMessageBox.information(self, "نجح", "تم إضافة المحافظة بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة المحافظة:\n{str(e)}")

    def edit_governorate(self):
        """تعديل محافظة محددة"""
        current_row = self.governorates_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار محافظة للتعديل")
            return

        # ملء النموذج ببيانات المحافظة المحددة
        self.gov_name.setText(self.governorates_table.item(current_row, 0).text())
        self.gov_name_en.setText(self.governorates_table.item(current_row, 1).text())
        self.gov_code.setText(self.governorates_table.item(current_row, 2).text())
        self.gov_country.setCurrentText(self.governorates_table.item(current_row, 3).text())
        self.gov_capital.setText(self.governorates_table.item(current_row, 4).text())
        self.gov_area.setValue(float(self.governorates_table.item(current_row, 5).text()))

        status = self.governorates_table.item(current_row, 6).text()
        self.gov_active.setChecked("نشطة" in status)
        self.gov_main.setChecked("رئيسية" in status)

    def delete_governorate(self):
        """حذف محافظة محددة"""
        current_row = self.governorates_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار محافظة للحذف")
            return

        gov_name = self.governorates_table.item(current_row, 0).text()

        # التحقق من وجود مناطق تابعة
        district_count = 0
        for row in range(self.districts_table.rowCount()):
            if self.districts_table.item(row, 3).text() == gov_name:
                district_count += 1

        if district_count > 0:
            QMessageBox.warning(self, "تحذير",
                               f"لا يمكن حذف المحافظة '{gov_name}'\n"
                               f"تحتوي على {district_count} منطقة\n"
                               f"يرجى حذف المناطق أولاً أو نقلها لمحافظة أخرى")
            return

        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   f"هل تريد حذف المحافظة '{gov_name}'؟",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            self.governorates_table.removeRow(current_row)
            self.update_governorate_combos()
            self.build_admin_tree()
            QMessageBox.information(self, "تم", "تم حذف المحافظة بنجاح")

    def clear_governorate_form(self):
        """تنظيف نموذج المحافظة"""
        self.gov_name.clear()
        self.gov_name_en.clear()
        self.gov_code.clear()
        self.gov_capital.clear()
        self.gov_area.setValue(0.0)
        self.gov_active.setChecked(True)
        self.gov_main.setChecked(False)

    # استيراد الوظائف الإضافية
    def add_district(self):
        """إضافة منطقة جديدة"""
        from .administrative_divisions_functions import add_district_function
        add_district_function(self)

    def edit_district(self):
        """تعديل منطقة محددة"""
        from .administrative_divisions_functions import edit_district_function
        edit_district_function(self)

    def delete_district(self):
        """حذف منطقة محددة"""
        from .administrative_divisions_functions import delete_district_function
        delete_district_function(self)

    def clear_district_form(self):
        """تنظيف نموذج المنطقة"""
        from .administrative_divisions_functions import clear_district_form_function
        clear_district_form_function(self)

    def add_city(self):
        """إضافة مدينة جديدة"""
        from .administrative_divisions_functions import add_city_function
        add_city_function(self)

    def edit_city(self):
        """تعديل مدينة محددة"""
        from .administrative_divisions_functions import edit_city_function
        edit_city_function(self)

    def delete_city(self):
        """حذف مدينة محددة"""
        from .administrative_divisions_functions import delete_city_function
        delete_city_function(self)

    def clear_city_form(self):
        """تنظيف نموذج المدينة"""
        from .administrative_divisions_functions import clear_city_form_function
        clear_city_form_function(self)

    def add_village(self):
        """إضافة قرية جديدة"""
        from .administrative_divisions_functions import add_village_function
        add_village_function(self)

    def edit_village(self):
        """تعديل قرية محددة"""
        from .administrative_divisions_functions import edit_village_function
        edit_village_function(self)

    def delete_village(self):
        """حذف قرية محددة"""
        from .administrative_divisions_functions import delete_village_function
        delete_village_function(self)

    def clear_village_form(self):
        """تنظيف نموذج القرية"""
        from .administrative_divisions_functions import clear_village_form_function
        clear_village_form_function(self)

    def save_all_changes(self):
        """حفظ جميع التغييرات"""
        from .administrative_divisions_functions import save_all_changes_function
        save_all_changes_function(self)


def main():
    """اختبار الشاشة"""
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass

    dialog = AdministrativeDivisionsDialog(MockDBManager())
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
