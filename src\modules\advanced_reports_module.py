#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة التقارير المالية المتقدمة
Advanced Financial Reports Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QDoubleSpinBox, QCheckBox,
                             QDateEdit, QSplitter, QFrame, QProgressBar,
                             QScrollArea, QApplication)
from PyQt5.QtCore import Qt, QDate, pyqtSignal, QThread, QTimer
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, Q<PERSON>ain<PERSON>
try:
    from PyQt5.QtPrintSupport import QPrintDialog, QPrinter
except ImportError:
    # في حالة عدم توفر وحدة الطباعة
    QPrintDialog = None
    QPrinter = None

import json
from datetime import datetime, timedelta
from ..database.sqlite_manager import SQLiteManager


class ReportGeneratorThread(QThread):
    """خيط منفصل لإنتاج التقارير"""
    
    progress_updated = pyqtSignal(int)
    report_ready = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, db_manager, report_type, filters):
        super().__init__()
        self.db_manager = db_manager
        self.report_type = report_type
        self.filters = filters
    
    def run(self):
        """تشغيل إنتاج التقرير"""
        try:
            if self.report_type == "trial_balance":
                report_data = self.generate_trial_balance()
            elif self.report_type == "account_statement":
                report_data = self.generate_account_statement()
            elif self.report_type == "journal_entries":
                report_data = self.generate_journal_entries_report()
            elif self.report_type == "customers_suppliers":
                report_data = self.generate_customers_suppliers_report()
            elif self.report_type == "financial_summary":
                report_data = self.generate_financial_summary()
            else:
                raise ValueError(f"نوع تقرير غير مدعوم: {self.report_type}")
            
            self.report_ready.emit(report_data)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def generate_trial_balance(self):
        """إنتاج ميزان المراجعة المتقدم"""
        self.progress_updated.emit(10)
        
        # استعلام الحسابات مع الأرصدة
        query = """
            SELECT 
                ca.account_code,
                ca.account_name_ar,
                ca.account_name_en,
                ca.account_type,
                ca.level,
                COALESCE(SUM(CASE WHEN jed.debit > 0 THEN jed.debit ELSE 0 END), 0) as total_debit,
                COALESCE(SUM(CASE WHEN jed.credit > 0 THEN jed.credit ELSE 0 END), 0) as total_credit
            FROM chart_of_accounts ca
            LEFT JOIN journal_entry_details jed ON ca.account_code = jed.account_code
            LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
            WHERE ca.is_active = 1
        """
        
        # إضافة فلاتر التاريخ
        params = []
        if self.filters.get('from_date'):
            query += " AND je.entry_date >= ?"
            params.append(self.filters['from_date'])
        
        if self.filters.get('to_date'):
            query += " AND je.entry_date <= ?"
            params.append(self.filters['to_date'])
        
        # إضافة فلتر نوع الحساب
        if self.filters.get('account_type') and self.filters['account_type'] != 'الكل':
            query += " AND ca.account_type = ?"
            params.append(self.filters['account_type'])
        
        query += " GROUP BY ca.account_code, ca.account_name_ar, ca.account_name_en, ca.account_type, ca.level"
        query += " ORDER BY ca.account_code"
        
        self.progress_updated.emit(50)
        
        accounts = self.db_manager.execute_query(query, params)
        
        self.progress_updated.emit(80)
        
        # حساب الأرصدة النهائية
        report_data = {
            'title': 'ميزان المراجعة',
            'subtitle': f"من {self.filters.get('from_date', 'البداية')} إلى {self.filters.get('to_date', 'النهاية')}",
            'columns': ['رمز الحساب', 'اسم الحساب', 'النوع', 'إجمالي المدين', 'إجمالي الدائن', 'الرصيد'],
            'data': [],
            'totals': {'total_debit': 0, 'total_credit': 0, 'total_balance': 0}
        }
        
        for account in accounts:
            debit = float(account.get('total_debit', 0))
            credit = float(account.get('total_credit', 0))
            balance = debit - credit
            
            # تحديد اتجاه الرصيد حسب نوع الحساب
            account_type = account.get('account_type', '')
            if account_type in ['liability', 'equity', 'revenue']:
                balance = credit - debit
            
            # إضافة البيانات فقط إذا كان هناك حركة أو رصيد
            if debit > 0 or credit > 0 or not self.filters.get('hide_zero_balances', False):
                report_data['data'].append({
                    'account_code': account.get('account_code', ''),
                    'account_name': account.get('account_name_ar', ''),
                    'account_type': self.get_account_type_arabic(account_type),
                    'total_debit': f"{debit:,.2f}",
                    'total_credit': f"{credit:,.2f}",
                    'balance': f"{balance:,.2f}",
                    'balance_raw': balance
                })
                
                report_data['totals']['total_debit'] += debit
                report_data['totals']['total_credit'] += credit
        
        self.progress_updated.emit(100)
        return report_data
    
    def generate_account_statement(self):
        """إنتاج كشف حساب تفصيلي"""
        self.progress_updated.emit(10)
        
        account_code = self.filters.get('account_code')
        if not account_code:
            raise ValueError("يجب تحديد رمز الحساب")
        
        # معلومات الحساب
        account_query = "SELECT * FROM chart_of_accounts WHERE account_code = ?"
        account_info = self.db_manager.execute_query(account_query, (account_code,))
        
        if not account_info:
            raise ValueError("الحساب غير موجود")
        
        account_info = account_info[0]
        
        self.progress_updated.emit(30)
        
        # حركات الحساب
        movements_query = """
            SELECT 
                je.entry_date,
                je.description,
                jed.description as detail_description,
                jed.debit,
                jed.credit,
                je.reference_number
            FROM journal_entry_details jed
            JOIN journal_entries je ON jed.journal_entry_id = je.id
            WHERE jed.account_code = ?
        """
        
        params = [account_code]
        
        if self.filters.get('from_date'):
            movements_query += " AND je.entry_date >= ?"
            params.append(self.filters['from_date'])
        
        if self.filters.get('to_date'):
            movements_query += " AND je.entry_date <= ?"
            params.append(self.filters['to_date'])
        
        movements_query += " ORDER BY je.entry_date, je.id"
        
        self.progress_updated.emit(60)
        
        movements = self.db_manager.execute_query(movements_query, params)
        
        self.progress_updated.emit(80)
        
        # حساب الرصيد الجاري
        running_balance = 0
        account_type = account_info.get('account_type', '')
        
        report_data = {
            'title': 'كشف حساب تفصيلي',
            'subtitle': f"{account_info.get('account_name_ar', '')} ({account_code})",
            'period': f"من {self.filters.get('from_date', 'البداية')} إلى {self.filters.get('to_date', 'النهاية')}",
            'columns': ['التاريخ', 'البيان', 'مدين', 'دائن', 'الرصيد'],
            'data': [],
            'account_info': account_info
        }
        
        for movement in movements:
            debit = float(movement.get('debit', 0))
            credit = float(movement.get('credit', 0))
            
            # حساب الرصيد الجاري
            if account_type in ['asset', 'expense']:
                running_balance += debit - credit
            else:
                running_balance += credit - debit
            
            description = movement.get('detail_description') or movement.get('description', '')
            
            report_data['data'].append({
                'date': movement.get('entry_date', ''),
                'description': description,
                'debit': f"{debit:,.2f}" if debit > 0 else "",
                'credit': f"{credit:,.2f}" if credit > 0 else "",
                'balance': f"{running_balance:,.2f}"
            })
        
        self.progress_updated.emit(100)
        return report_data
    
    def generate_customers_suppliers_report(self):
        """إنتاج تقرير العملاء والموردين"""
        self.progress_updated.emit(20)
        
        query = """
            SELECT 
                code,
                name_ar,
                name_en,
                type,
                phone,
                email,
                credit_limit,
                payment_terms,
                is_active,
                created_at
            FROM customers_suppliers
            WHERE 1=1
        """
        
        params = []
        
        # فلتر النوع
        if self.filters.get('type') and self.filters['type'] != 'الكل':
            type_mapping = {'عميل': 'customer', 'مورد': 'supplier', 'عميل ومورد': 'both'}
            query += " AND type = ?"
            params.append(type_mapping.get(self.filters['type'], self.filters['type']))
        
        # فلتر الحالة
        if self.filters.get('status') and self.filters['status'] != 'الكل':
            is_active = 1 if self.filters['status'] == 'نشط' else 0
            query += " AND is_active = ?"
            params.append(is_active)
        
        query += " ORDER BY name_ar"
        
        self.progress_updated.emit(60)
        
        customers_suppliers = self.db_manager.execute_query(query, params)
        
        self.progress_updated.emit(90)
        
        # تحويل النوع إلى العربية
        type_mapping = {'customer': 'عميل', 'supplier': 'مورد', 'both': 'عميل ومورد'}
        
        report_data = {
            'title': 'تقرير العملاء والموردين',
            'subtitle': f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}",
            'columns': ['الرمز', 'الاسم', 'النوع', 'الهاتف', 'البريد الإلكتروني', 'حد الائتمان', 'الحالة'],
            'data': [],
            'totals': {'count': len(customers_suppliers), 'total_credit_limit': 0}
        }
        
        for item in customers_suppliers:
            credit_limit = float(item.get('credit_limit', 0))
            report_data['totals']['total_credit_limit'] += credit_limit
            
            report_data['data'].append({
                'code': item.get('code', ''),
                'name': item.get('name_ar', ''),
                'type': type_mapping.get(item.get('type', ''), item.get('type', '')),
                'phone': item.get('phone', ''),
                'email': item.get('email', ''),
                'credit_limit': f"{credit_limit:,.2f}",
                'status': 'نشط' if item.get('is_active', True) else 'غير نشط'
            })
        
        self.progress_updated.emit(100)
        return report_data
    
    def generate_financial_summary(self):
        """إنتاج ملخص مالي"""
        self.progress_updated.emit(10)
        
        # حساب إجماليات الأصول
        assets_query = """
            SELECT COALESCE(SUM(jed.debit - jed.credit), 0) as total
            FROM journal_entry_details jed
            JOIN chart_of_accounts ca ON jed.account_code = ca.account_code
            WHERE ca.account_type = 'asset'
        """
        
        # حساب إجماليات الخصوم
        liabilities_query = """
            SELECT COALESCE(SUM(jed.credit - jed.debit), 0) as total
            FROM journal_entry_details jed
            JOIN chart_of_accounts ca ON jed.account_code = ca.account_code
            WHERE ca.account_type = 'liability'
        """
        
        # حساب إجماليات الإيرادات
        revenue_query = """
            SELECT COALESCE(SUM(jed.credit - jed.debit), 0) as total
            FROM journal_entry_details jed
            JOIN chart_of_accounts ca ON jed.account_code = ca.account_code
            WHERE ca.account_type = 'revenue'
        """
        
        # حساب إجماليات المصروفات
        expenses_query = """
            SELECT COALESCE(SUM(jed.debit - jed.credit), 0) as total
            FROM journal_entry_details jed
            JOIN chart_of_accounts ca ON jed.account_code = ca.account_code
            WHERE ca.account_type = 'expense'
        """
        
        self.progress_updated.emit(50)
        
        assets_result = self.db_manager.execute_query(assets_query)
        liabilities_result = self.db_manager.execute_query(liabilities_query)
        revenue_result = self.db_manager.execute_query(revenue_query)
        expenses_result = self.db_manager.execute_query(expenses_query)
        
        self.progress_updated.emit(80)
        
        assets_total = float(assets_result[0]['total']) if assets_result else 0
        liabilities_total = float(liabilities_result[0]['total']) if liabilities_result else 0
        revenue_total = float(revenue_result[0]['total']) if revenue_result else 0
        expenses_total = float(expenses_result[0]['total']) if expenses_result else 0
        
        net_income = revenue_total - expenses_total
        equity = assets_total - liabilities_total
        
        report_data = {
            'title': 'الملخص المالي',
            'subtitle': f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}",
            'data': {
                'assets': assets_total,
                'liabilities': liabilities_total,
                'equity': equity,
                'revenue': revenue_total,
                'expenses': expenses_total,
                'net_income': net_income
            }
        }
        
        self.progress_updated.emit(100)
        return report_data
    
    def get_account_type_arabic(self, account_type):
        """تحويل نوع الحساب إلى العربية"""
        mapping = {
            'asset': 'أصول',
            'liability': 'خصوم',
            'equity': 'حقوق الملكية',
            'revenue': 'إيرادات',
            'expense': 'مصروفات'
        }
        return mapping.get(account_type, account_type)


class ReportViewerDialog(QDialog):
    """حوار عرض التقارير"""
    
    def __init__(self, report_data, parent=None):
        super().__init__(parent)
        self.report_data = report_data
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"عرض التقرير - {self.report_data.get('title', 'تقرير')}")
        self.setMinimumSize(1000, 700)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        self.print_btn = QPushButton("طباعة")
        self.print_btn.setIcon(QIcon("🖨️"))
        self.print_btn.clicked.connect(self.print_report)
        
        self.export_btn = QPushButton("تصدير")
        self.export_btn.setIcon(QIcon("📄"))
        self.export_btn.clicked.connect(self.export_report)
        
        self.close_btn = QPushButton("إغلاق")
        self.close_btn.setIcon(QIcon("❌"))
        self.close_btn.clicked.connect(self.close)
        
        toolbar_layout.addWidget(self.print_btn)
        toolbar_layout.addWidget(self.export_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.close_btn)
        
        main_layout.addLayout(toolbar_layout)
        
        # منطقة عرض التقرير
        self.create_report_view()
        main_layout.addWidget(self.report_view)
        
        self.setLayout(main_layout)
        self.setStyleSheet(self.get_dialog_stylesheet())
    
    def create_report_view(self):
        """إنشاء منطقة عرض التقرير"""
        self.report_view = QScrollArea()
        self.report_view.setWidgetResizable(True)
        
        # إنشاء محتوى التقرير
        report_widget = QWidget()
        report_layout = QVBoxLayout()
        
        # عنوان التقرير
        title_label = QLabel(self.report_data.get('title', 'تقرير'))
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2196F3; margin: 20px;")
        report_layout.addWidget(title_label)
        
        # العنوان الفرعي
        if self.report_data.get('subtitle'):
            subtitle_label = QLabel(self.report_data['subtitle'])
            subtitle_label.setFont(QFont("Arial", 12))
            subtitle_label.setAlignment(Qt.AlignCenter)
            subtitle_label.setStyleSheet("color: #666; margin-bottom: 20px;")
            report_layout.addWidget(subtitle_label)
        
        # محتوى التقرير
        if self.report_data.get('title') == 'الملخص المالي':
            self.create_financial_summary_view(report_layout)
        else:
            self.create_table_view(report_layout)
        
        report_widget.setLayout(report_layout)
        self.report_view.setWidget(report_widget)
    
    def create_table_view(self, layout):
        """إنشاء عرض جدولي للتقرير"""
        if not self.report_data.get('data'):
            no_data_label = QLabel("لا توجد بيانات لعرضها")
            no_data_label.setAlignment(Qt.AlignCenter)
            no_data_label.setStyleSheet("color: #999; font-size: 14px; margin: 50px;")
            layout.addWidget(no_data_label)
            return
        
        # إنشاء الجدول
        table = QTableWidget()
        columns = self.report_data.get('columns', [])
        table.setColumnCount(len(columns))
        table.setHorizontalHeaderLabels(columns)
        table.setRowCount(len(self.report_data['data']))
        
        # ملء البيانات
        for row, record in enumerate(self.report_data['data']):
            if isinstance(record, dict):
                col = 0
                for key, value in record.items():
                    if key.endswith('_raw'):  # تجاهل القيم الخام
                        continue
                    item = QTableWidgetItem(str(value))
                    table.setItem(row, col, item)
                    col += 1
            else:
                for col, value in enumerate(record):
                    item = QTableWidgetItem(str(value))
                    table.setItem(row, col, item)
        
        # تنسيق الجدول
        table.setAlternatingRowColors(True)
        table.setSortingEnabled(True)
        table.horizontalHeader().setStretchLastSection(True)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f9f9f9;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 10px;
                border: 1px solid #ddd;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(table)
        
        # إضافة الإجماليات إذا وجدت
        if self.report_data.get('totals'):
            self.create_totals_view(layout)
    
    def create_financial_summary_view(self, layout):
        """إنشاء عرض الملخص المالي"""
        data = self.report_data.get('data', {})
        
        # إنشاء بطاقات الملخص
        cards_layout = QGridLayout()
        
        # بطاقة الأصول
        assets_card = self.create_summary_card("الأصول", f"{data.get('assets', 0):,.2f} ريال", "#4CAF50")
        cards_layout.addWidget(assets_card, 0, 0)
        
        # بطاقة الخصوم
        liabilities_card = self.create_summary_card("الخصوم", f"{data.get('liabilities', 0):,.2f} ريال", "#FF9800")
        cards_layout.addWidget(liabilities_card, 0, 1)
        
        # بطاقة حقوق الملكية
        equity_card = self.create_summary_card("حقوق الملكية", f"{data.get('equity', 0):,.2f} ريال", "#2196F3")
        cards_layout.addWidget(equity_card, 1, 0)
        
        # بطاقة صافي الدخل
        net_income = data.get('net_income', 0)
        color = "#4CAF50" if net_income >= 0 else "#F44336"
        net_income_card = self.create_summary_card("صافي الدخل", f"{net_income:,.2f} ريال", color)
        cards_layout.addWidget(net_income_card, 1, 1)
        
        layout.addLayout(cards_layout)
        
        # جدول تفصيلي
        details_table = QTableWidget(4, 2)
        details_table.setHorizontalHeaderLabels(["البند", "المبلغ"])
        
        items = [
            ("إجمالي الإيرادات", f"{data.get('revenue', 0):,.2f} ريال"),
            ("إجمالي المصروفات", f"{data.get('expenses', 0):,.2f} ريال"),
            ("صافي الدخل", f"{net_income:,.2f} ريال"),
            ("إجمالي الأصول", f"{data.get('assets', 0):,.2f} ريال")
        ]
        
        for row, (item, amount) in enumerate(items):
            details_table.setItem(row, 0, QTableWidgetItem(item))
            details_table.setItem(row, 1, QTableWidgetItem(amount))
        
        details_table.horizontalHeader().setStretchLastSection(True)
        details_table.setMaximumHeight(200)
        layout.addWidget(details_table)
    
    def create_summary_card(self, title, value, color):
        """إنشاء بطاقة ملخص"""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setStyleSheet(f"""
            QFrame {{
                border: 2px solid {color};
                border-radius: 10px;
                background-color: white;
                margin: 10px;
                padding: 20px;
            }}
        """)
        
        card_layout = QVBoxLayout()
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"color: {color};")
        
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 16, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("color: #333;")
        
        card_layout.addWidget(title_label)
        card_layout.addWidget(value_label)
        card.setLayout(card_layout)
        
        return card
    
    def create_totals_view(self, layout):
        """إنشاء عرض الإجماليات"""
        totals = self.report_data.get('totals', {})
        
        totals_frame = QFrame()
        totals_frame.setFrameStyle(QFrame.Box)
        totals_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: #f8f9fa;
                margin: 10px;
                padding: 15px;
            }
        """)
        
        totals_layout = QHBoxLayout()
        
        for key, value in totals.items():
            if key == 'count':
                label = QLabel(f"إجمالي السجلات: {value}")
            elif 'total' in key:
                label = QLabel(f"الإجمالي: {value:,.2f}")
            else:
                label = QLabel(f"{key}: {value}")
            
            label.setFont(QFont("Arial", 11, QFont.Bold))
            label.setStyleSheet("color: #495057;")
            totals_layout.addWidget(label)
        
        totals_frame.setLayout(totals_layout)
        layout.addWidget(totals_frame)
    
    def print_report(self):
        """طباعة التقرير"""
        if QPrinter is None or QPrintDialog is None:
            QMessageBox.information(self, "طباعة", "وظيفة الطباعة غير متاحة في هذا الإصدار")
            return
        
        try:
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            
            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec_() == QPrintDialog.Accepted:
                # هنا يمكن إضافة منطق الطباعة
                QMessageBox.information(self, "طباعة", "تم إرسال التقرير للطباعة")
        except Exception as e:
            QMessageBox.warning(self, "خطأ في الطباعة", f"فشل في طباعة التقرير:\n{str(e)}")
    
    def export_report(self):
        """تصدير التقرير"""
        # هنا يمكن إضافة منطق التصدير
        QMessageBox.information(self, "تصدير", "سيتم إضافة وظيفة التصدير قريباً")
    
    def get_dialog_stylesheet(self):
        """تنسيق الحوار"""
        return """
            QDialog {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 11px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QScrollArea {
                border: 1px solid #dee2e6;
                border-radius: 6px;
                background-color: white;
            }
        """


class AdvancedReportsModule(QWidget):
    """وحدة التقارير المالية المتقدمة"""
    
    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_report_thread = None
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان الوحدة
        title_label = QLabel("التقارير المالية المتقدمة")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2196F3; margin: 20px;")
        main_layout.addWidget(title_label)
        
        # منطقة اختيار التقارير والفلاتر
        self.create_reports_selection()
        main_layout.addWidget(self.reports_frame)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # منطقة الحالة
        self.status_label = QLabel("اختر نوع التقرير والفلاتر ثم اضغط 'إنتاج التقرير'")
        self.status_label.setStyleSheet("color: #666; padding: 10px;")
        main_layout.addWidget(self.status_label)
        
        self.setLayout(main_layout)
        self.setStyleSheet(self.get_module_stylesheet())
    
    def create_reports_selection(self):
        """إنشاء منطقة اختيار التقارير"""
        self.reports_frame = QFrame()
        reports_layout = QVBoxLayout()
        
        # اختيار نوع التقرير
        report_type_group = QGroupBox("نوع التقرير")
        report_type_layout = QGridLayout()
        
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "ميزان المراجعة المتقدم",
            "كشف حساب تفصيلي", 
            "تقرير القيود اليومية",
            "تقرير العملاء والموردين",
            "الملخص المالي"
        ])
        
        report_type_layout.addWidget(QLabel("نوع التقرير:"), 0, 0)
        report_type_layout.addWidget(self.report_type_combo, 0, 1)
        
        report_type_group.setLayout(report_type_layout)
        reports_layout.addWidget(report_type_group)
        
        # الفلاتر
        filters_group = QGroupBox("الفلاتر")
        filters_layout = QGridLayout()
        
        # فلتر التاريخ
        self.from_date = QDateEdit()
        self.from_date.setDate(QDate.currentDate().addMonths(-1))
        self.from_date.setCalendarPopup(True)
        
        self.to_date = QDateEdit()
        self.to_date.setDate(QDate.currentDate())
        self.to_date.setCalendarPopup(True)
        
        filters_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        filters_layout.addWidget(self.from_date, 0, 1)
        filters_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        filters_layout.addWidget(self.to_date, 0, 3)
        
        # فلتر نوع الحساب
        self.account_type_filter = QComboBox()
        self.account_type_filter.addItems(["الكل", "أصول", "خصوم", "حقوق الملكية", "إيرادات", "مصروفات"])
        
        filters_layout.addWidget(QLabel("نوع الحساب:"), 1, 0)
        filters_layout.addWidget(self.account_type_filter, 1, 1)
        
        # فلتر الحساب المحدد (لكشف الحساب)
        self.account_combo = QComboBox()
        self.account_combo.setEnabled(False)
        self.load_accounts()
        
        filters_layout.addWidget(QLabel("الحساب:"), 1, 2)
        filters_layout.addWidget(self.account_combo, 1, 3)
        
        # خيارات إضافية
        self.hide_zero_balances = QCheckBox("إخفاء الأرصدة الصفرية")
        self.include_inactive = QCheckBox("تضمين الحسابات غير النشطة")
        
        filters_layout.addWidget(self.hide_zero_balances, 2, 0, 1, 2)
        filters_layout.addWidget(self.include_inactive, 2, 2, 1, 2)
        
        filters_group.setLayout(filters_layout)
        reports_layout.addWidget(filters_group)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        self.generate_btn = QPushButton("إنتاج التقرير")
        self.generate_btn.setIcon(QIcon("📊"))
        self.generate_btn.setMinimumHeight(40)
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setIcon(QIcon("❌"))
        self.cancel_btn.setMinimumHeight(40)
        self.cancel_btn.setEnabled(False)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.generate_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        reports_layout.addLayout(buttons_layout)
        
        self.reports_frame.setLayout(reports_layout)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.report_type_combo.currentTextChanged.connect(self.on_report_type_changed)
        self.generate_btn.clicked.connect(self.generate_report)
        self.cancel_btn.clicked.connect(self.cancel_report)
    
    def load_accounts(self):
        """تحميل قائمة الحسابات"""
        try:
            accounts = self.db_manager.execute_query(
                "SELECT account_code, account_name_ar FROM chart_of_accounts WHERE is_active = 1 ORDER BY account_code"
            )
            
            self.account_combo.clear()
            self.account_combo.addItem("اختر الحساب", "")
            
            for account in accounts:
                display_text = f"{account['account_code']} - {account['account_name_ar']}"
                self.account_combo.addItem(display_text, account['account_code'])
                
        except Exception as e:
            print(f"خطأ في تحميل الحسابات: {e}")
    
    def on_report_type_changed(self):
        """عند تغيير نوع التقرير"""
        report_type = self.report_type_combo.currentText()
        
        # تفعيل/تعطيل فلتر الحساب حسب نوع التقرير
        self.account_combo.setEnabled(report_type == "كشف حساب تفصيلي")
        
        # تحديث النصائح
        if report_type == "كشف حساب تفصيلي":
            self.status_label.setText("اختر الحساب المطلوب إنتاج كشف له")
        elif report_type == "الملخص المالي":
            self.status_label.setText("سيتم إنتاج ملخص شامل للوضع المالي")
        else:
            self.status_label.setText("اختر الفلاتر المطلوبة ثم اضغط 'إنتاج التقرير'")
    
    def generate_report(self):
        """إنتاج التقرير"""
        # التحقق من صحة البيانات
        if not self.validate_inputs():
            return
        
        # جمع الفلاتر
        filters = self.collect_filters()
        
        # تحديد نوع التقرير
        report_type_mapping = {
            "ميزان المراجعة المتقدم": "trial_balance",
            "كشف حساب تفصيلي": "account_statement",
            "تقرير القيود اليومية": "journal_entries",
            "تقرير العملاء والموردين": "customers_suppliers",
            "الملخص المالي": "financial_summary"
        }
        
        report_type = report_type_mapping.get(self.report_type_combo.currentText())
        
        # بدء إنتاج التقرير في خيط منفصل
        self.start_report_generation(report_type, filters)
    
    def validate_inputs(self):
        """التحقق من صحة المدخلات"""
        if self.report_type_combo.currentText() == "كشف حساب تفصيلي":
            if not self.account_combo.currentData():
                QMessageBox.warning(self, "تحذير", "يرجى اختيار الحساب المطلوب")
                return False
        
        if self.from_date.date() > self.to_date.date():
            QMessageBox.warning(self, "تحذير", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
            return False
        
        return True
    
    def collect_filters(self):
        """جمع الفلاتر"""
        account_type_mapping = {
            "أصول": "asset",
            "خصوم": "liability", 
            "حقوق الملكية": "equity",
            "إيرادات": "revenue",
            "مصروفات": "expense"
        }
        
        return {
            'from_date': self.from_date.date().toString("yyyy-MM-dd"),
            'to_date': self.to_date.date().toString("yyyy-MM-dd"),
            'account_type': account_type_mapping.get(self.account_type_filter.currentText()),
            'account_code': self.account_combo.currentData(),
            'hide_zero_balances': self.hide_zero_balances.isChecked(),
            'include_inactive': self.include_inactive.isChecked(),
            'type': self.account_type_filter.currentText(),
            'status': 'الكل'
        }
    
    def start_report_generation(self, report_type, filters):
        """بدء إنتاج التقرير"""
        # تعطيل الواجهة
        self.generate_btn.setEnabled(False)
        self.cancel_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("جاري إنتاج التقرير...")
        
        # إنشاء وتشغيل خيط التقرير
        self.current_report_thread = ReportGeneratorThread(self.db_manager, report_type, filters)
        self.current_report_thread.progress_updated.connect(self.progress_bar.setValue)
        self.current_report_thread.report_ready.connect(self.on_report_ready)
        self.current_report_thread.error_occurred.connect(self.on_report_error)
        self.current_report_thread.finished.connect(self.on_report_finished)
        
        self.current_report_thread.start()
    
    def cancel_report(self):
        """إلغاء إنتاج التقرير"""
        if self.current_report_thread and self.current_report_thread.isRunning():
            self.current_report_thread.terminate()
            self.current_report_thread.wait()
        
        self.on_report_finished()
        self.status_label.setText("تم إلغاء إنتاج التقرير")
    
    def on_report_ready(self, report_data):
        """عند اكتمال التقرير"""
        self.status_label.setText("تم إنتاج التقرير بنجاح")
        
        # عرض التقرير
        viewer = ReportViewerDialog(report_data, self)
        viewer.exec_()
    
    def on_report_error(self, error_message):
        """عند حدوث خطأ في التقرير"""
        self.status_label.setText("حدث خطأ في إنتاج التقرير")
        QMessageBox.critical(self, "خطأ", f"فشل في إنتاج التقرير:\n{error_message}")
    
    def on_report_finished(self):
        """عند انتهاء عملية التقرير"""
        # إعادة تفعيل الواجهة
        self.generate_btn.setEnabled(True)
        self.cancel_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.current_report_thread = None
    
    def get_module_stylesheet(self):
        """تنسيق الوحدة"""
        return """
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #495057;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
            QLineEdit, QComboBox, QDateEdit {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 11px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus {
                border-color: #80bdff;
            }
            QProgressBar {
                border: 1px solid #dee2e6;
                border-radius: 4px;
                text-align: center;
                background-color: #e9ecef;
            }
            QProgressBar::chunk {
                background-color: #28a745;
                border-radius: 3px;
            }
        """