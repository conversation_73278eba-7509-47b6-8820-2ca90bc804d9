#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار هيكل إدارة المبيعات المحدث
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_sales_structure():
    """اختبار هيكل إدارة المبيعات"""
    print("🛍️ اختبار هيكل إدارة المبيعات المحدث...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system_restructured import MainSystemWindow
        
        main_window = MainSystemWindow()
        tree_data = main_window.get_restructured_tree_data()
        
        # البحث عن قسم إدارة المبيعات
        sales_section = None
        for section in tree_data:
            if 'إدارة المبيعات' in section['title']:
                sales_section = section
                break
        
        if not sales_section:
            print("   ❌ لم يتم العثور على قسم إدارة المبيعات")
            return False
        
        print("   ✅ تم العثور على قسم إدارة المبيعات")
        print(f"\n📋 هيكل {sales_section['title']}:")
        
        inputs_items = []
        operations_items = []
        reports_items = []
        
        for subsection in sales_section['children']:
            if isinstance(subsection, dict):
                print(f"\n  📂 {subsection['title']}:")
                for item in subsection['children']:
                    print(f"    📄 {item}")
                    
                    # تصنيف العناصر
                    if 'المدخلات' in subsection['title']:
                        inputs_items.append(item)
                    elif 'العمليات' in subsection['title']:
                        operations_items.append(item)
                    elif 'التقارير' in subsection['title']:
                        reports_items.append(item)
        
        # التحقق من وجود فواتير البيع في العمليات
        invoice_in_operations = any('فواتير البيع' in item or 'إنشاء فواتير البيع' in item for item in operations_items)
        invoice_in_inputs = any('فواتير البيع' in item for item in inputs_items)
        
        print(f"\n🔍 نتائج التحقق:")
        print(f"   📥 المدخلات ({len(inputs_items)} عناصر):")
        for item in inputs_items:
            print(f"      • {item}")
        
        print(f"   ⚙️ العمليات ({len(operations_items)} عناصر):")
        for item in operations_items:
            print(f"      • {item}")
            
        print(f"   📊 التقارير ({len(reports_items)} عناصر):")
        for item in reports_items:
            print(f"      • {item}")
        
        print(f"\n✅ التحقق من موقع فواتير البيع:")
        if invoice_in_operations and not invoice_in_inputs:
            print("   ✅ فواتير البيع موجودة في العمليات (صحيح)")
            print("   ✅ فواتير البيع غير موجودة في المدخلات (صحيح)")
            return True
        elif invoice_in_inputs:
            print("   ❌ فواتير البيع ما زالت في المدخلات (خطأ)")
            return False
        elif not invoice_in_operations:
            print("   ❌ فواتير البيع غير موجودة في العمليات (خطأ)")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار هيكل المبيعات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار التحديث على هيكل إدارة المبيعات")
    print("=" * 60)
    
    if test_sales_structure():
        print("\n🎉 نجح التحديث!")
        print("✅ فواتير البيع تم نقلها بنجاح إلى قسم العمليات")
        print("\n📋 الهيكل الجديد لإدارة المبيعات:")
        print("   📥 المدخلات:")
        print("      • بيانات العملاء للمبيعات")
        print("      • شروط الدفع للمبيعات") 
        print("      • الخصومات والعروض")
        print("      • قوائم الأسعار")
        print("   ⚙️ العمليات:")
        print("      • إنشاء فواتير البيع ← (تم النقل)")
        print("      • تنفيذ فواتير المبيعات")
        print("      • تسجيل مردودات المبيعات")
        print("      • تحديث المخزون من المبيعات")
        print("      • احتساب الضرائب")
        print("   📊 التقارير:")
        print("      • تقرير فواتير المبيعات")
        print("      • تقرير مردود المبيعات")
        print("      • المبيعات حسب الموظف")
        print("      • الفواتير غير المسددة")
        
        print("\n🎯 المنطق الجديد:")
        print("   📥 المدخلات = البيانات الأساسية والإعدادات")
        print("   ⚙️ العمليات = الأنشطة والمعاملات (فواتير البيع)")
        print("   📊 التقارير = المخرجات والتحليلات")
        
        return 0
    else:
        print("\n❌ فشل التحديث!")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
        return 1

if __name__ == "__main__":
    sys.exit(main())
