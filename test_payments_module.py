#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوحدة إدارة المدفوعات
Comprehensive test for payments module
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_payments_module():
    """اختبار وحدة إدارة المدفوعات"""
    print("🧪 اختبار وحدة إدارة المدفوعات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.payments_module import PaymentsWidget, PaymentDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار إنشاء وحدة إدارة المدفوعات
        payments_widget = PaymentsWidget(db_manager)
        print("✅ تم إنشاء وحدة إدارة المدفوعات")
        
        # اختبار تحميل المدفوعات
        payments_widget.load_payments()
        print("✅ تم تحميل المدفوعات من قاعدة البيانات")
        
        # اختبار عدد المدفوعات المحملة
        payment_count = payments_widget.payments_table.rowCount()
        print(f"📊 عدد المدفوعات المحملة: {payment_count}")
        
        # اختبار البحث والفلترة
        payments_widget.search_edit.setText("PAY")
        payments_widget.filter_payments()
        print("✅ تم اختبار وظيفة البحث")
        
        # اختبار فلترة طريقة الدفع
        payments_widget.payment_method_filter.setCurrentText("نقداً")
        payments_widget.filter_payments()
        print("✅ تم اختبار فلترة طريقة الدفع")
        
        # اختبار مسح الفلاتر
        payments_widget.search_edit.clear()
        payments_widget.payment_method_filter.setCurrentText("الكل")
        payments_widget.filter_payments()
        print("✅ تم اختبار مسح الفلاتر")
        
        # تنظيف
        payments_widget.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وحدة إدارة المدفوعات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_payment_dialog():
    """اختبار حوار المدفوعات"""
    print("\n🧪 اختبار حوار المدفوعات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt, QDate
        from src.modules.payments_module import PaymentDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار حوار تسجيل مدفوعات جديدة
        dialog = PaymentDialog(db_manager)
        print("✅ تم إنشاء حوار المدفوعات")
        
        # اختبار تحميل الفواتير
        dialog.load_invoices()
        invoice_count = dialog.invoice_combo.count()
        print(f"✅ تم تحميل {invoice_count} فاتورة")
        
        # اختبار إنشاء رقم مدفوعات
        dialog.generate_payment_number()
        payment_number = dialog.payment_number_edit.text()
        print(f"✅ تم إنشاء رقم المدفوعات: {payment_number}")
        
        # اختبار ملء البيانات الأساسية
        dialog.payment_date_edit.setDate(QDate.currentDate())
        dialog.amount_spin.setValue(1000.0)
        dialog.payment_method_combo.setCurrentText("نقداً")
        print("✅ تم ملء البيانات الأساسية")
        
        # اختبار تغيير طريقة الدفع
        dialog.payment_method_combo.setCurrentText("تحويل بنكي")
        dialog.on_payment_method_changed()
        print("✅ تم اختبار تغيير طريقة الدفع")
        
        # اختبار حساب المتبقي الجديد
        if dialog.invoice_combo.count() > 1:
            dialog.invoice_combo.setCurrentIndex(1)  # اختيار أول فاتورة
            dialog.on_invoice_changed()
            dialog.calculate_new_remaining()
            print("✅ تم اختبار حساب المتبقي الجديد")
        
        # تنظيف
        dialog.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حوار المدفوعات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_payments_database_operations():
    """اختبار عمليات قاعدة البيانات للمدفوعات"""
    print("\n🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار استعلام المدفوعات
        query = """
            SELECT p.*, si.invoice_number, c.customer_name
            FROM payments p
            LEFT JOIN sales_invoices si ON p.invoice_id = si.id
            LEFT JOIN customers c ON si.customer_id = c.id
            ORDER BY p.payment_date DESC, p.id DESC
        """
        
        payments = db_manager.execute_query(query)
        print(f"✅ تم استعلام {len(payments)} مدفوعات من قاعدة البيانات")
        
        # اختبار إحصائيات المدفوعات
        stats_query = """
            SELECT 
                COUNT(*) as total_payments,
                SUM(amount) as total_amount
            FROM payments
        """
        stats_result = db_manager.execute_query(stats_query)
        if stats_result:
            stats = stats_result[0]
            print(f"📊 إجمالي المدفوعات: {stats['total_payments'] or 0}")
            print(f"💰 إجمالي المبالغ: {stats['total_amount'] or 0:,.2f} ريال")
        
        # اختبار استعلام المدفوعات حسب طريقة الدفع
        method_query = """
            SELECT payment_method, COUNT(*) as count, SUM(amount) as total
            FROM payments 
            GROUP BY payment_method
            ORDER BY total DESC
        """
        method_result = db_manager.execute_query(method_query)
        print(f"📋 توزيع المدفوعات حسب طريقة الدفع:")
        method_names = {
            'cash': 'نقداً',
            'bank_transfer': 'تحويل بنكي',
            'check': 'شيك',
            'credit_card': 'بطاقة ائتمان',
            'other': 'أخرى'
        }
        for method_data in method_result:
            method_name = method_names.get(method_data['payment_method'], method_data['payment_method'])
            print(f"   {method_name}: {method_data['count']} مدفوعات - {method_data['total'] or 0:,.2f} ريال")
        
        # اختبار استعلام المبالغ المستحقة
        outstanding_query = """
            SELECT si.invoice_number, c.customer_name, si.invoice_date, 
                   si.due_date, si.total_amount, si.paid_amount, si.remaining_amount,
                   CASE 
                       WHEN si.due_date < date('now') THEN 'متأخرة'
                       ELSE 'مستحقة'
                   END as status
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            WHERE si.remaining_amount > 0 AND si.status != 'cancelled'
            ORDER BY si.due_date ASC
        """
        outstanding = db_manager.execute_query(outstanding_query)
        print(f"💸 المبالغ المستحقة: {len(outstanding)} فاتورة")
        
        total_outstanding = sum(float(item['remaining_amount']) for item in outstanding)
        overdue_count = sum(1 for item in outstanding if item['status'] == 'متأخرة')
        print(f"💰 إجمالي المبالغ المستحقة: {total_outstanding:,.2f} ريال")
        print(f"⏰ الفواتير المتأخرة: {overdue_count}")
        
        # اختبار استعلام أفضل العملاء في المدفوعات
        top_payers_query = """
            SELECT c.customer_name, COUNT(p.id) as payment_count, SUM(p.amount) as total_paid
            FROM payments p
            JOIN sales_invoices si ON p.invoice_id = si.id
            JOIN customers c ON si.customer_id = c.id
            GROUP BY c.id, c.customer_name
            ORDER BY total_paid DESC
            LIMIT 5
        """
        top_payers = db_manager.execute_query(top_payers_query)
        print(f"🏆 أفضل العملاء في المدفوعات:")
        for payer in top_payers:
            print(f"   {payer['customer_name']}: {payer['payment_count']} مدفوعات - {payer['total_paid'] or 0:,.2f} ريال")
        
        # اختبار استعلام المدفوعات الشهرية
        monthly_payments_query = """
            SELECT 
                strftime('%Y-%m', payment_date) as month,
                COUNT(*) as payment_count,
                SUM(amount) as total_amount
            FROM payments
            WHERE payment_date >= date('now', '-12 months')
            GROUP BY strftime('%Y-%m', payment_date)
            ORDER BY month DESC
            LIMIT 6
        """
        monthly_payments = db_manager.execute_query(monthly_payments_query)
        print(f"📅 المدفوعات الشهرية (آخر 6 أشهر):")
        for month_data in monthly_payments:
            print(f"   {month_data['month']}: {month_data['payment_count']} مدفوعات - {month_data['total_amount'] or 0:,.2f} ريال")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_structure():
    """اختبار هيكل قاعدة البيانات للمدفوعات"""
    print("\n🧪 اختبار هيكل قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار وجود جدول payments
        table_query = "SELECT name FROM sqlite_master WHERE type='table' AND name='payments'"
        table_result = db_manager.execute_query(table_query)
        
        if table_result:
            print("✅ جدول payments موجود")
            
            # اختبار أعمدة الجدول
            columns_query = "PRAGMA table_info(payments)"
            columns = db_manager.execute_query(columns_query)
            column_names = [col['name'] for col in columns]
            
            required_columns = [
                'id', 'payment_number', 'invoice_id', 'payment_date', 'amount',
                'payment_method', 'reference_number', 'bank_name', 'notes',
                'created_by', 'created_at'
            ]
            
            print("\n📋 أعمدة جدول payments:")
            for col in required_columns:
                if col in column_names:
                    print(f"   ✅ {col}")
                else:
                    print(f"   ❌ {col} مفقود")
            
            # عرض جميع الأعمدة الموجودة
            print(f"\n📊 إجمالي الأعمدة الموجودة: {len(column_names)}")
            print(f"الأعمدة: {', '.join(column_names)}")
        else:
            print("❌ جدول payments مفقود")
            return False
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار هيكل قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار شامل لوحدة إدارة المدفوعات")
    print("   Comprehensive test for payments module")
    print("=" * 60)
    
    # اختبار هيكل قاعدة البيانات
    structure_test = test_database_structure()
    
    # اختبار وحدة إدارة المدفوعات
    module_test = test_payments_module()
    
    # اختبار حوار المدفوعات
    dialog_test = test_payment_dialog()
    
    # اختبار عمليات قاعدة البيانات
    database_test = test_payments_database_operations()
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار وحدة إدارة المدفوعات:")
    print(f"   🗄️ هيكل قاعدة البيانات: {'✅ نجح' if structure_test else '❌ فشل'}")
    print(f"   🏗️ اختبار الوحدة: {'✅ نجح' if module_test else '❌ فشل'}")
    print(f"   💬 اختبار الحوار: {'✅ نجح' if dialog_test else '❌ فشل'}")
    print(f"   🗄️ اختبار قاعدة البيانات: {'✅ نجح' if database_test else '❌ فشل'}")
    
    if structure_test and module_test and dialog_test and database_test:
        print("\n🎉 جميع اختبارات وحدة إدارة المدفوعات نجحت!")
        print("✅ وحدة إدارة المدفوعات جاهزة للاستخدام")
        
        print("\n📋 الوظائف المتاحة:")
        print("   💳 تسجيل مدفوعات جديدة")
        print("   ✏️ تعديل المدفوعات الموجودة")
        print("   🗑️ حذف المدفوعات")
        print("   🔍 البحث والفلترة المتقدمة")
        print("   📊 عرض إحصائيات المدفوعات")
        print("   💰 تتبع المبالغ المستحقة")
        print("   🏦 دعم طرق دفع متعددة")
        print("   🔗 ربط تلقائي مع الفواتير")
        print("   📈 تحديث حالة الفواتير")
        print("   📤 تصدير المدفوعات (قيد التطوير)")
    else:
        print("\n⚠️ بعض اختبارات وحدة إدارة المدفوعات فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)
    
    return 0 if (structure_test and module_test and dialog_test and database_test) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
