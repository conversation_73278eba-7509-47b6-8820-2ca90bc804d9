#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة النسخ الاحتياطي
Backup Module
"""

import os
import shutil
import sqlite3
import zipfile
import json
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QDoubleSpinBox, QCheckBox,
                             QDateEdit, QSplitter, QFrame, QDialogButtonBox,
                             QScrollArea, QProgressBar, QSlider, QFileDialog,
                             QListWidget, QListWidgetItem, QTimeEdit)
from PyQt5.QtCore import Qt, QDate, QTime, pyqt<PERSON>ignal, QTimer, Q<PERSON>hread, pyqtSlot
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor

try:
    from ..database.sqlite_manager import SQLiteManager
except ImportError:
    # في حالة التشغيل المباشر
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class BackupWorker(QThread):
    """عامل النسخ الاحتياطي في خيط منفصل"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    backup_completed = pyqtSignal(bool, str)
    
    def __init__(self, db_path, backup_path, backup_type="full"):
        super().__init__()
        self.db_path = db_path
        self.backup_path = backup_path
        self.backup_type = backup_type
        self.is_cancelled = False
    
    def run(self):
        """تشغيل عملية النسخ الاحتياطي"""
        try:
            self.status_updated.emit("بدء عملية النسخ الاحتياطي...")
            self.progress_updated.emit(10)
            
            if self.backup_type == "full":
                success = self.create_full_backup()
            elif self.backup_type == "data_only":
                success = self.create_data_backup()
            elif self.backup_type == "structure_only":
                success = self.create_structure_backup()
            else:
                success = False
            
            if success and not self.is_cancelled:
                self.status_updated.emit("تم إنشاء النسخة الاحتياطية بنجاح")
                self.progress_updated.emit(100)
                self.backup_completed.emit(True, "تم إنشاء النسخة الاحتياطية بنجاح")
            elif self.is_cancelled:
                self.status_updated.emit("تم إلغاء العملية")
                self.backup_completed.emit(False, "تم إلغاء العملية بواسطة المستخدم")
            else:
                self.backup_completed.emit(False, "فشل في إنشاء النسخة الاحتياطية")
                
        except Exception as e:
            self.backup_completed.emit(False, f"خطأ في النسخ الاحتياطي: {str(e)}")
    
    def create_full_backup(self):
        """إنشاء نسخة احتياطية كاملة"""
        try:
            self.status_updated.emit("إنشاء نسخة احتياطية كاملة...")
            self.progress_updated.emit(20)
            
            # إنشاء مجلد النسخة الاحتياطية
            backup_dir = os.path.dirname(self.backup_path)
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            self.progress_updated.emit(30)
            
            # نسخ قاعدة البيانات
            if os.path.exists(self.db_path):
                shutil.copy2(self.db_path, self.backup_path.replace('.zip', '.db'))
                self.status_updated.emit("تم نسخ قاعدة البيانات...")
                self.progress_updated.emit(50)
            
            # إنشاء ملف معلومات النسخة الاحتياطية
            backup_info = {
                "backup_date": datetime.now().isoformat(),
                "backup_type": "full",
                "database_size": os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                "version": "1.0"
            }
            
            info_path = self.backup_path.replace('.zip', '_info.json')
            with open(info_path, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)
            
            self.progress_updated.emit(70)
            
            # ضغط الملفات
            self.status_updated.emit("ضغط الملفات...")
            with zipfile.ZipFile(self.backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                if os.path.exists(self.backup_path.replace('.zip', '.db')):
                    zipf.write(self.backup_path.replace('.zip', '.db'), 'database.db')
                    os.remove(self.backup_path.replace('.zip', '.db'))
                
                zipf.write(info_path, 'backup_info.json')
                os.remove(info_path)
            
            self.progress_updated.emit(90)
            return True
            
        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي الكامل: {e}")
            return False
    
    def create_data_backup(self):
        """إنشاء نسخة احتياطية للبيانات فقط"""
        try:
            self.status_updated.emit("إنشاء نسخة احتياطية للبيانات...")
            self.progress_updated.emit(20)
            
            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # الحصول على قائمة الجداول
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            backup_data = {}
            progress_step = 60 / len(tables) if tables else 60
            
            for i, (table_name,) in enumerate(tables):
                if self.is_cancelled:
                    break
                
                self.status_updated.emit(f"نسخ بيانات جدول {table_name}...")
                
                # استخراج البيانات
                cursor.execute(f"SELECT * FROM {table_name}")
                rows = cursor.fetchall()
                
                # الحصول على أسماء الأعمدة
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [col[1] for col in cursor.fetchall()]
                
                # تحويل البيانات إلى قاموس
                table_data = []
                for row in rows:
                    row_dict = {}
                    for j, value in enumerate(row):
                        row_dict[columns[j]] = value
                    table_data.append(row_dict)
                
                backup_data[table_name] = {
                    "columns": columns,
                    "data": table_data
                }
                
                self.progress_updated.emit(20 + int((i + 1) * progress_step))
            
            conn.close()
            
            # حفظ البيانات في ملف JSON
            backup_dir = os.path.dirname(self.backup_path)
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            data_path = self.backup_path.replace('.zip', '_data.json')
            with open(data_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2, default=str)
            
            self.progress_updated.emit(85)
            
            # ضغط الملف
            self.status_updated.emit("ضغط البيانات...")
            with zipfile.ZipFile(self.backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(data_path, 'data.json')
                os.remove(data_path)
            
            self.progress_updated.emit(95)
            return True
            
        except Exception as e:
            print(f"خطأ في نسخ البيانات: {e}")
            return False
    
    def create_structure_backup(self):
        """إنشاء نسخة احتياطية لهيكل قاعدة البيانات فقط"""
        try:
            self.status_updated.emit("إنشاء نسخة احتياطية للهيكل...")
            self.progress_updated.emit(20)
            
            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # استخراج هيكل قاعدة البيانات
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='table'")
            table_schemas = cursor.fetchall()
            
            cursor.execute("SELECT sql FROM sqlite_master WHERE type='index'")
            index_schemas = cursor.fetchall()
            
            conn.close()
            
            self.progress_updated.emit(50)
            
            # إنشاء ملف SQL للهيكل
            structure_sql = "-- هيكل قاعدة البيانات\n"
            structure_sql += "-- تم إنشاؤه في: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\n\n"
            
            # إضافة الجداول
            structure_sql += "-- الجداول\n"
            for schema in table_schemas:
                if schema[0]:
                    structure_sql += schema[0] + ";\n\n"
            
            # إضافة الفهارس
            structure_sql += "-- الفهارس\n"
            for schema in index_schemas:
                if schema[0]:
                    structure_sql += schema[0] + ";\n\n"
            
            self.progress_updated.emit(70)
            
            # حفظ الملف
            backup_dir = os.path.dirname(self.backup_path)
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            sql_path = self.backup_path.replace('.zip', '_structure.sql')
            with open(sql_path, 'w', encoding='utf-8') as f:
                f.write(structure_sql)
            
            self.progress_updated.emit(85)
            
            # ضغط الملف
            self.status_updated.emit("ضغط الهيكل...")
            with zipfile.ZipFile(self.backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                zipf.write(sql_path, 'structure.sql')
                os.remove(sql_path)
            
            self.progress_updated.emit(95)
            return True
            
        except Exception as e:
            print(f"خطأ في نسخ الهيكل: {e}")
            return False
    
    def cancel(self):
        """إلغاء العملية"""
        self.is_cancelled = True


class RestoreWorker(QThread):
    """عامل الاستعادة في خيط منفصل"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    restore_completed = pyqtSignal(bool, str)
    
    def __init__(self, backup_path, db_path, restore_type="full"):
        super().__init__()
        self.backup_path = backup_path
        self.db_path = db_path
        self.restore_type = restore_type
        self.is_cancelled = False
    
    def run(self):
        """تشغيل عملية الاستعادة"""
        try:
            self.status_updated.emit("بدء عملية الاستعادة...")
            self.progress_updated.emit(10)
            
            if self.restore_type == "full":
                success = self.restore_full_backup()
            elif self.restore_type == "data_only":
                success = self.restore_data_backup()
            else:
                success = False
            
            if success and not self.is_cancelled:
                self.status_updated.emit("تم استعادة النسخة الاحتياطية بنجاح")
                self.progress_updated.emit(100)
                self.restore_completed.emit(True, "تم استعادة النسخة الاحتياطية بنجاح")
            elif self.is_cancelled:
                self.status_updated.emit("تم إلغاء العملية")
                self.restore_completed.emit(False, "تم إلغاء العملية بواسطة المستخدم")
            else:
                self.restore_completed.emit(False, "فشل في استعادة النسخة الاحتياطية")
                
        except Exception as e:
            self.restore_completed.emit(False, f"خطأ في الاستعادة: {str(e)}")
    
    def restore_full_backup(self):
        """استعادة نسخة احتياطية كاملة"""
        try:
            self.status_updated.emit("استعادة النسخة الاحتياطية الكاملة...")
            self.progress_updated.emit(20)
            
            # استخراج الملفات
            temp_dir = os.path.dirname(self.db_path) + "/temp_restore"
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            
            with zipfile.ZipFile(self.backup_path, 'r') as zipf:
                zipf.extractall(temp_dir)
            
            self.progress_updated.emit(50)
            
            # نسخ قاعدة البيانات
            db_backup_path = os.path.join(temp_dir, 'database.db')
            if os.path.exists(db_backup_path):
                # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
                if os.path.exists(self.db_path):
                    backup_current = self.db_path + '.backup_' + datetime.now().strftime("%Y%m%d_%H%M%S")
                    shutil.copy2(self.db_path, backup_current)
                
                # استعادة قاعدة البيانات
                shutil.copy2(db_backup_path, self.db_path)
                self.status_updated.emit("تم استعادة قاعدة البيانات...")
                self.progress_updated.emit(80)
            
            # تنظيف الملفات المؤقتة
            shutil.rmtree(temp_dir)
            self.progress_updated.emit(95)
            
            return True
            
        except Exception as e:
            print(f"خطأ في استعادة النسخة الكاملة: {e}")
            return False
    
    def restore_data_backup(self):
        """استعادة بيانات فقط"""
        try:
            self.status_updated.emit("استعادة البيانات...")
            self.progress_updated.emit(20)
            
            # استخراج البيانات
            temp_dir = os.path.dirname(self.db_path) + "/temp_restore"
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)
            
            with zipfile.ZipFile(self.backup_path, 'r') as zipf:
                zipf.extractall(temp_dir)
            
            # قراءة البيانات
            data_path = os.path.join(temp_dir, 'data.json')
            with open(data_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
            
            self.progress_updated.emit(40)
            
            # الاتصال بقاعدة البيانات
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # استعادة البيانات
            total_tables = len(backup_data)
            progress_step = 50 / total_tables if total_tables else 50
            
            for i, (table_name, table_info) in enumerate(backup_data.items()):
                if self.is_cancelled:
                    break
                
                self.status_updated.emit(f"استعادة بيانات جدول {table_name}...")
                
                # حذف البيانات الحالية
                cursor.execute(f"DELETE FROM {table_name}")
                
                # إدراج البيانات المستعادة
                columns = table_info['columns']
                data = table_info['data']
                
                if data:
                    placeholders = ','.join(['?' for _ in columns])
                    insert_sql = f"INSERT INTO {table_name} ({','.join(columns)}) VALUES ({placeholders})"
                    
                    for row_data in data:
                        values = [row_data.get(col) for col in columns]
                        cursor.execute(insert_sql, values)
                
                self.progress_updated.emit(40 + int((i + 1) * progress_step))
            
            conn.commit()
            conn.close()
            
            # تنظيف الملفات المؤقتة
            shutil.rmtree(temp_dir)
            self.progress_updated.emit(95)
            
            return True
            
        except Exception as e:
            print(f"خطأ في استعادة البيانات: {e}")
            return False
    
    def cancel(self):
        """إلغاء العملية"""
        self.is_cancelled = True


class BackupScheduleDialog(QDialog):
    """حوار جدولة النسخ الاحتياطي"""

    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.setup_connections()
        self.load_current_schedule()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("جدولة النسخ الاحتياطي")
        self.setFixedSize(500, 400)
        self.setModal(True)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # العنوان
        title = QLabel("⏰ إعدادات جدولة النسخ الاحتياطي")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        main_layout.addWidget(title)

        # النموذج
        form_layout = QFormLayout()

        # تفعيل الجدولة التلقائية
        self.enable_schedule = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        form_layout.addRow("", self.enable_schedule)

        # تكرار النسخ الاحتياطي
        self.frequency_combo = QComboBox()
        self.frequency_combo.addItems(["يومي", "أسبوعي", "شهري"])
        form_layout.addRow("التكرار:", self.frequency_combo)

        # وقت النسخ الاحتياطي
        self.backup_time = QTimeEdit()
        self.backup_time.setTime(QTime(2, 0))  # 2:00 AM افتراضي
        form_layout.addRow("وقت النسخ:", self.backup_time)

        # نوع النسخة الاحتياطية
        self.backup_type_combo = QComboBox()
        self.backup_type_combo.addItems(["نسخة كاملة", "البيانات فقط", "الهيكل فقط"])
        form_layout.addRow("نوع النسخة:", self.backup_type_combo)

        # مجلد النسخ الاحتياطي
        backup_path_layout = QHBoxLayout()
        self.backup_path_edit = QLineEdit()
        self.backup_path_edit.setPlaceholderText("اختر مجلد النسخ الاحتياطي...")
        self.browse_btn = QPushButton("تصفح")
        backup_path_layout.addWidget(self.backup_path_edit)
        backup_path_layout.addWidget(self.browse_btn)
        form_layout.addRow("مجلد النسخ:", backup_path_layout)

        # عدد النسخ المحفوظة
        self.keep_backups_spin = QSpinBox()
        self.keep_backups_spin.setRange(1, 100)
        self.keep_backups_spin.setValue(7)
        self.keep_backups_spin.setSuffix(" نسخة")
        form_layout.addRow("عدد النسخ المحفوظة:", self.keep_backups_spin)

        # ضغط النسخ الاحتياطية
        self.compress_backups = QCheckBox("ضغط النسخ الاحتياطية")
        self.compress_backups.setChecked(True)
        form_layout.addRow("", self.compress_backups)

        # إشعارات النسخ الاحتياطي
        self.notify_success = QCheckBox("إشعار عند نجاح النسخ")
        self.notify_success.setChecked(True)
        form_layout.addRow("", self.notify_success)

        self.notify_failure = QCheckBox("إشعار عند فشل النسخ")
        self.notify_failure.setChecked(True)
        form_layout.addRow("", self.notify_failure)

        main_layout.addLayout(form_layout)

        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)

        main_layout.addWidget(button_box)

        self.setLayout(main_layout)

        # تطبيق التنسيق
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLineEdit, QComboBox, QSpinBox, QTimeEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QTimeEdit:focus {
                border-color: #2196F3;
            }
            QPushButton {
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-weight: bold;
                color: #333;
            
            
                padding: 5px;
            }
            QCheckBox {
                font-weight: normal;
                color: #555;
            }
        """)

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_schedule)
        self.cancel_btn.clicked.connect(self.reject)
        self.browse_btn.clicked.connect(self.browse_backup_path)

    def load_current_schedule(self):
        """تحميل الجدولة الحالية"""
        try:
            # تحميل الإعدادات من قاعدة البيانات
            settings_query = """
                SELECT setting_key, setting_value FROM system_settings
                WHERE setting_key IN (
                    'backup_enabled', 'backup_frequency', 'backup_time', 'backup_type',
                    'backup_path', 'backup_keep_count', 'backup_compress',
                    'backup_notify_success', 'backup_notify_failure'
                )
            """

            settings = self.db_manager.execute_query(settings_query)
            settings_dict = {s['setting_key']: s['setting_value'] for s in settings}

            # تطبيق الإعدادات على الواجهة
            self.enable_schedule.setChecked(settings_dict.get('backup_enabled', '0') == '1')

            frequency = settings_dict.get('backup_frequency', 'daily')
            frequency_mapping = {'daily': 0, 'weekly': 1, 'monthly': 2}
            self.frequency_combo.setCurrentIndex(frequency_mapping.get(frequency, 0))

            backup_time_str = settings_dict.get('backup_time', '02:00')
            try:
                hour, minute = map(int, backup_time_str.split(':'))
                self.backup_time.setTime(QTime(hour, minute))
            except:
                self.backup_time.setTime(QTime(2, 0))

            backup_type = settings_dict.get('backup_type', 'full')
            type_mapping = {'full': 0, 'data_only': 1, 'structure_only': 2}
            self.backup_type_combo.setCurrentIndex(type_mapping.get(backup_type, 0))

            self.backup_path_edit.setText(settings_dict.get('backup_path', ''))
            self.keep_backups_spin.setValue(int(settings_dict.get('backup_keep_count', '7')))
            self.compress_backups.setChecked(settings_dict.get('backup_compress', '1') == '1')
            self.notify_success.setChecked(settings_dict.get('backup_notify_success', '1') == '1')
            self.notify_failure.setChecked(settings_dict.get('backup_notify_failure', '1') == '1')

        except Exception as e:
            print(f"خطأ في تحميل جدولة النسخ الاحتياطي: {e}")

    def save_schedule(self):
        """حفظ الجدولة"""
        try:
            # التحقق من صحة البيانات
            if self.enable_schedule.isChecked() and not self.backup_path_edit.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى تحديد مجلد النسخ الاحتياطي")
                return

            # إعدادات الجدولة
            frequency_mapping = {0: 'daily', 1: 'weekly', 2: 'monthly'}
            type_mapping = {0: 'full', 1: 'data_only', 2: 'structure_only'}

            schedule_settings = [
                ('backup_enabled', '1' if self.enable_schedule.isChecked() else '0'),
                ('backup_frequency', frequency_mapping[self.frequency_combo.currentIndex()]),
                ('backup_time', self.backup_time.time().toString('HH:mm')),
                ('backup_type', type_mapping[self.backup_type_combo.currentIndex()]),
                ('backup_path', self.backup_path_edit.text().strip()),
                ('backup_keep_count', str(self.keep_backups_spin.value())),
                ('backup_compress', '1' if self.compress_backups.isChecked() else '0'),
                ('backup_notify_success', '1' if self.notify_success.isChecked() else '0'),
                ('backup_notify_failure', '1' if self.notify_failure.isChecked() else '0')
            ]

            # حفظ أو تحديث الإعدادات
            for key, value in schedule_settings:
                query = """
                    INSERT OR REPLACE INTO system_settings
                    (setting_key, setting_value, setting_type, description, category, is_editable)
                    VALUES (?, ?, 'string', ?, 'backup', 1)
                """
                description = self.get_setting_description(key)
                self.db_manager.execute_update(query, (key, value, description))

            QMessageBox.information(self, "نجح", "تم حفظ إعدادات الجدولة بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الجدولة:\n{str(e)}")

    def browse_backup_path(self):
        """تصفح مجلد النسخ الاحتياطي"""
        folder = QFileDialog.getExistingDirectory(self, "اختر مجلد النسخ الاحتياطي")
        if folder:
            self.backup_path_edit.setText(folder)

    def get_setting_description(self, key):
        """الحصول على وصف الإعداد"""
        descriptions = {
            'backup_enabled': 'تفعيل النسخ الاحتياطي التلقائي',
            'backup_frequency': 'تكرار النسخ الاحتياطي',
            'backup_time': 'وقت النسخ الاحتياطي',
            'backup_type': 'نوع النسخة الاحتياطية',
            'backup_path': 'مجلد النسخ الاحتياطي',
            'backup_keep_count': 'عدد النسخ المحفوظة',
            'backup_compress': 'ضغط النسخ الاحتياطية',
            'backup_notify_success': 'إشعار عند نجاح النسخ',
            'backup_notify_failure': 'إشعار عند فشل النسخ'
        }
        return descriptions.get(key, key)


class BackupWidget(QWidget):
    """وحدة النسخ الاحتياطي"""

    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.backup_worker = None
        self.restore_worker = None
        self.setup_ui()
        self.setup_connections()
        self.load_backup_history()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # العنوان
        title = QLabel("💾 النسخ الاحتياطي والاستعادة")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        main_layout.addWidget(title)

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        self.create_backup_btn = QPushButton("💾 إنشاء نسخة احتياطية")
        self.restore_backup_btn = QPushButton("🔄 استعادة نسخة احتياطية")
        self.schedule_btn = QPushButton("⏰ جدولة النسخ")
        self.refresh_btn = QPushButton("🔄 تحديث")

        # تنسيق الأزرار
        buttons = [self.create_backup_btn, self.restore_backup_btn, self.schedule_btn, self.refresh_btn]

        for btn in buttons:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 150px;
                }
                QPushButton:hover {
                    background-color: #229954;
                }
                QPushButton:pressed {
                    background-color: #1e8449;
                }
                QPushButton:disabled {
                    background-color: #bdc3c7;
                }
            """)

        # ألوان خاصة لبعض الأزرار
        self.restore_backup_btn.setStyleSheet(self.restore_backup_btn.styleSheet().replace("#27ae60", "#3498db"))
        self.schedule_btn.setStyleSheet(self.schedule_btn.styleSheet().replace("#27ae60", "#f39c12"))
        self.refresh_btn.setStyleSheet(self.refresh_btn.styleSheet().replace("#27ae60", "#95a5a6"))

        toolbar_layout.addWidget(self.create_backup_btn)
        toolbar_layout.addWidget(self.restore_backup_btn)
        toolbar_layout.addWidget(self.schedule_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.refresh_btn)

        main_layout.addLayout(toolbar_layout)

        # منطقة التقدم
        progress_group = QGroupBox("📊 حالة العملية")
        progress_layout = QVBoxLayout()

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        self.status_label = QLabel("جاهز")
        self.status_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
        progress_layout.addWidget(self.status_label)

        # أزرار التحكم في العملية
        control_layout = QHBoxLayout()
        self.cancel_btn = QPushButton("❌ إلغاء العملية")
        self.cancel_btn.setVisible(False)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        control_layout.addWidget(self.cancel_btn)
        control_layout.addStretch()

        progress_layout.addLayout(control_layout)
        progress_group.setLayout(progress_layout)
        main_layout.addWidget(progress_group)

        # جدول تاريخ النسخ الاحتياطية
        history_group = QGroupBox("📋 تاريخ النسخ الاحتياطية")
        history_layout = QVBoxLayout()

        # شريط البحث والفلترة
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في النسخ الاحتياطية...")
        search_layout.addWidget(self.search_edit)

        search_layout.addWidget(QLabel("النوع:"))
        self.type_filter = QComboBox()
        self.type_filter.addItems(["الكل", "نسخة كاملة", "البيانات فقط", "الهيكل فقط"])
        search_layout.addWidget(self.type_filter)

        history_layout.addLayout(search_layout)

        # جدول النسخ الاحتياطية
        self.backups_table = QTableWidget()
        columns = ["اسم الملف", "التاريخ", "النوع", "الحجم", "الحالة", "الإجراءات"]
        self.backups_table.setColumnCount(len(columns))
        self.backups_table.setHorizontalHeaderLabels(columns)

        # تنسيق الجدول
        self.backups_table.setAlternatingRowColors(True)
        self.backups_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.backups_table.setSelectionMode(QTableWidget.SingleSelection)
        self.backups_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.backups_table.horizontalHeader()
        header.resizeSection(0, 200)  # اسم الملف
        header.resizeSection(1, 150)  # التاريخ
        header.resizeSection(2, 100)  # النوع
        header.resizeSection(3, 80)   # الحجم
        header.resizeSection(4, 80)   # الحالة
        header.setStretchLastSection(True)  # الإجراءات

        history_layout.addWidget(self.backups_table)
        history_group.setLayout(history_layout)
        main_layout.addWidget(history_group)

        # شريط الحالة
        status_layout = QHBoxLayout()
        self.backup_count_label = QLabel("عدد النسخ: 0")
        self.total_size_label = QLabel("الحجم الإجمالي: 0 MB")

        status_layout.addWidget(self.backup_count_label)
        status_layout.addStretch()
        status_layout.addWidget(self.total_size_label)

        main_layout.addLayout(status_layout)

        self.setLayout(main_layout)
        self.setStyleSheet(self.get_module_stylesheet())

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.create_backup_btn.clicked.connect(self.create_backup)
        self.restore_backup_btn.clicked.connect(self.restore_backup)
        self.schedule_btn.clicked.connect(self.show_schedule_dialog)
        self.refresh_btn.clicked.connect(self.load_backup_history)
        self.cancel_btn.clicked.connect(self.cancel_operation)

        self.search_edit.textChanged.connect(self.filter_backups)
        self.type_filter.currentTextChanged.connect(self.filter_backups)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        # حوار اختيار نوع النسخة الاحتياطية
        backup_type, ok = QMessageBox.question(
            self, 'نوع النسخة الاحتياطية',
            'اختر نوع النسخة الاحتياطية:',
            QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
            QMessageBox.Yes
        ), True

        if not ok:
            return

        # تحديد نوع النسخة
        if backup_type == QMessageBox.Yes:
            backup_type_str = "full"
            type_name = "كاملة"
        elif backup_type == QMessageBox.No:
            backup_type_str = "data_only"
            type_name = "البيانات فقط"
        else:
            backup_type_str = "structure_only"
            type_name = "الهيكل فقط"

        # اختيار مكان الحفظ
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"onyx_backup_{type_name}_{timestamp}.zip"

        backup_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ النسخة الاحتياطية",
            default_filename,
            "ملفات مضغوطة (*.zip)"
        )

        if not backup_path:
            return

        # بدء عملية النسخ الاحتياطي
        self.start_backup_operation(backup_path, backup_type_str)

    def start_backup_operation(self, backup_path, backup_type):
        """بدء عملية النسخ الاحتياطي"""
        try:
            # تعطيل الأزرار
            self.create_backup_btn.setEnabled(False)
            self.restore_backup_btn.setEnabled(False)

            # إظهار شريط التقدم
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.cancel_btn.setVisible(True)

            # إنشاء عامل النسخ الاحتياطي
            db_path = self.db_manager.db_path if hasattr(self.db_manager, 'db_path') else "onyx_erp.db"
            self.backup_worker = BackupWorker(db_path, backup_path, backup_type)

            # ربط الإشارات
            self.backup_worker.progress_updated.connect(self.progress_bar.setValue)
            self.backup_worker.status_updated.connect(self.status_label.setText)
            self.backup_worker.backup_completed.connect(self.on_backup_completed)

            # بدء العملية
            self.backup_worker.start()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في بدء النسخ الاحتياطي:\n{str(e)}")
            self.reset_ui_state()

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        # اختيار ملف النسخة الاحتياطية
        backup_path, _ = QFileDialog.getOpenFileName(
            self, "اختر النسخة الاحتياطية للاستعادة",
            "",
            "ملفات مضغوطة (*.zip)"
        )

        if not backup_path:
            return

        # تأكيد الاستعادة
        reply = QMessageBox.question(
            self, 'تأكيد الاستعادة',
            'هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n\nسيتم استبدال البيانات الحالية.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # بدء عملية الاستعادة
        self.start_restore_operation(backup_path)

    def start_restore_operation(self, backup_path):
        """بدء عملية الاستعادة"""
        try:
            # تعطيل الأزرار
            self.create_backup_btn.setEnabled(False)
            self.restore_backup_btn.setEnabled(False)

            # إظهار شريط التقدم
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.cancel_btn.setVisible(True)

            # إنشاء عامل الاستعادة
            db_path = self.db_manager.db_path if hasattr(self.db_manager, 'db_path') else "onyx_erp.db"
            self.restore_worker = RestoreWorker(backup_path, db_path, "full")

            # ربط الإشارات
            self.restore_worker.progress_updated.connect(self.progress_bar.setValue)
            self.restore_worker.status_updated.connect(self.status_label.setText)
            self.restore_worker.restore_completed.connect(self.on_restore_completed)

            # بدء العملية
            self.restore_worker.start()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في بدء الاستعادة:\n{str(e)}")
            self.reset_ui_state()

    def show_schedule_dialog(self):
        """عرض حوار جدولة النسخ الاحتياطي"""
        dialog = BackupScheduleDialog(self.db_manager, self)
        dialog.exec_()

    def cancel_operation(self):
        """إلغاء العملية الجارية"""
        if self.backup_worker and self.backup_worker.isRunning():
            self.backup_worker.cancel()
            self.backup_worker.wait()

        if self.restore_worker and self.restore_worker.isRunning():
            self.restore_worker.cancel()
            self.restore_worker.wait()

        self.reset_ui_state()

    def on_backup_completed(self, success, message):
        """معالجة اكتمال النسخ الاحتياطي"""
        self.reset_ui_state()

        if success:
            QMessageBox.information(self, "نجح", message)
            self.load_backup_history()
        else:
            QMessageBox.critical(self, "فشل", message)

    def on_restore_completed(self, success, message):
        """معالجة اكتمال الاستعادة"""
        self.reset_ui_state()

        if success:
            QMessageBox.information(self, "نجح", message + "\n\nيرجى إعادة تشغيل البرنامج لتطبيق التغييرات.")
        else:
            QMessageBox.critical(self, "فشل", message)

    def reset_ui_state(self):
        """إعادة تعيين حالة الواجهة"""
        self.create_backup_btn.setEnabled(True)
        self.restore_backup_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.cancel_btn.setVisible(False)
        self.status_label.setText("جاهز")

    def load_backup_history(self):
        """تحميل تاريخ النسخ الاحتياطية"""
        try:
            # مسح الجدول
            self.backups_table.setRowCount(0)

            # البحث عن ملفات النسخ الاحتياطية في المجلد المحدد
            backup_path = self.get_backup_folder()
            if not backup_path or not os.path.exists(backup_path):
                self.backup_count_label.setText("عدد النسخ: 0")
                self.total_size_label.setText("الحجم الإجمالي: 0 MB")
                return

            backup_files = []
            total_size = 0

            for filename in os.listdir(backup_path):
                if filename.endswith('.zip') and 'onyx_backup' in filename:
                    file_path = os.path.join(backup_path, filename)
                    file_size = os.path.getsize(file_path)
                    file_date = datetime.fromtimestamp(os.path.getmtime(file_path))

                    # تحديد نوع النسخة من اسم الملف
                    if 'كاملة' in filename or 'full' in filename:
                        backup_type = "نسخة كاملة"
                    elif 'البيانات' in filename or 'data' in filename:
                        backup_type = "البيانات فقط"
                    elif 'الهيكل' in filename or 'structure' in filename:
                        backup_type = "الهيكل فقط"
                    else:
                        backup_type = "غير محدد"

                    backup_files.append({
                        'filename': filename,
                        'path': file_path,
                        'date': file_date,
                        'type': backup_type,
                        'size': file_size
                    })
                    total_size += file_size

            # ترتيب الملفات حسب التاريخ (الأحدث أولاً)
            backup_files.sort(key=lambda x: x['date'], reverse=True)

            # ملء الجدول
            self.backups_table.setRowCount(len(backup_files))

            for row, backup in enumerate(backup_files):
                # اسم الملف
                self.backups_table.setItem(row, 0, QTableWidgetItem(backup['filename']))

                # التاريخ
                date_str = backup['date'].strftime("%Y-%m-%d %H:%M")
                self.backups_table.setItem(row, 1, QTableWidgetItem(date_str))

                # النوع
                self.backups_table.setItem(row, 2, QTableWidgetItem(backup['type']))

                # الحجم
                size_mb = backup['size'] / (1024 * 1024)
                size_str = f"{size_mb:.1f} MB"
                self.backups_table.setItem(row, 3, QTableWidgetItem(size_str))

                # الحالة
                status_item = QTableWidgetItem("صالح")
                status_item.setBackground(QColor("#d4edda"))
                self.backups_table.setItem(row, 4, status_item)

                # الإجراءات
                actions_btn = QPushButton("استعادة")
                actions_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #007bff;
                        color: white;
                        border: none;
                        padding: 4px 8px;
                        border-radius: 3px;
                        font-size: 12px;
                    }
                    QPushButton:hover {
                        background-color: #0056b3;
                    }
                """)
                actions_btn.clicked.connect(lambda checked, path=backup['path']: self.restore_specific_backup(path))
                self.backups_table.setCellWidget(row, 5, actions_btn)

            # تحديث الإحصائيات
            self.backup_count_label.setText(f"عدد النسخ: {len(backup_files)}")
            total_size_mb = total_size / (1024 * 1024)
            self.total_size_label.setText(f"الحجم الإجمالي: {total_size_mb:.1f} MB")

        except Exception as e:
            print(f"خطأ في تحميل تاريخ النسخ الاحتياطية: {e}")

    def get_backup_folder(self):
        """الحصول على مجلد النسخ الاحتياطي"""
        try:
            query = "SELECT setting_value FROM system_settings WHERE setting_key = 'backup_path'"
            result = self.db_manager.execute_query(query)
            if result:
                return result[0]['setting_value']
        except:
            pass
        return ""

    def restore_specific_backup(self, backup_path):
        """استعادة نسخة احتياطية محددة"""
        # تأكيد الاستعادة
        reply = QMessageBox.question(
            self, 'تأكيد الاستعادة',
            f'هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n\n{os.path.basename(backup_path)}\n\nسيتم استبدال البيانات الحالية.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.start_restore_operation(backup_path)

    def filter_backups(self):
        """فلترة النسخ الاحتياطية"""
        search_text = self.search_edit.text().lower()
        type_filter = self.type_filter.currentText()

        for row in range(self.backups_table.rowCount()):
            show_row = True

            # فلترة النص
            if search_text:
                row_text = ""
                for col in range(self.backups_table.columnCount() - 1):  # استثناء عمود الإجراءات
                    item = self.backups_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                if search_text not in row_text:
                    show_row = False

            # فلترة النوع
            if type_filter != "الكل":
                type_item = self.backups_table.item(row, 2)
                if type_item and type_filter not in type_item.text():
                    show_row = False

            self.backups_table.setRowHidden(row, not show_row)

    def get_module_stylesheet(self):
        """تنسيق الوحدة"""
        return """
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLineEdit, QComboBox {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #007bff;
                outline: none;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #27ae60;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #27ae60;
            }
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #27ae60;
                border-radius: 3px;
            }
        """
