#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف إضافية لشاشة ترميز الحسابات
Additional Functions for Account Coding
"""

from PyQt5.QtWidgets import (QMessageBox, QTableWidgetItem, QGroupBox, QGridLayout,
                             QLabel, QCheckBox, QComboBox, QPushButton, QHBoxLayout,
                             QVBoxLayout, QWidget, QTextEdit, QProgressBar)
from PyQt5.QtCore import Qt
from datetime import datetime
import re

def safe_call(func):
    """استدعاء آمن للدوال مع معالجة الأخطاء"""
    try:
        if callable(func):
            return func()
        else:
            QMessageBox.warning(None, "خطأ", f"الدالة المطلوبة غير صالحة: {func}")
    except Exception as e:
        QMessageBox.critical(None, "خطأ", f"فشل في تنفيذ العملية:\n{str(e)}")
        import traceback
        traceback.print_exc()

def create_validation_tab_function(self):
    """إنشاء تبويب التحقق والمراجعة"""
    tab = QWidget()
    layout = QVBoxLayout()
    
    # مجموعة فحص الترميز
    check_group = QGroupBox("🔍 فحص نظام الترميز")
    check_layout = QVBoxLayout()
    
    # أزرار الفحص
    check_buttons = QHBoxLayout()
    
    check_duplicates_btn = QPushButton("🔍 فحص التكرارات")
    check_duplicates_btn.setStyleSheet("""
        QPushButton {
            background-color: #e74c3c;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #c0392b;
        }
    """)
    check_duplicates_btn.clicked.connect(lambda: safe_call(self.check_duplicates))
    
    check_gaps_btn = QPushButton("📊 فحص الفجوات")
    check_gaps_btn.setStyleSheet("""
        QPushButton {
            background-color: #f39c12;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #e67e22;
        }
    """)
    check_gaps_btn.clicked.connect(lambda: safe_call(self.check_gaps))

    check_hierarchy_btn = QPushButton("🌳 فحص الهيكل")
    check_hierarchy_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    check_hierarchy_btn.clicked.connect(lambda: safe_call(self.check_hierarchy_function))

    full_validation_btn = QPushButton("✅ فحص شامل")
    full_validation_btn.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    full_validation_btn.clicked.connect(lambda: safe_call(self.full_validation))
    
    check_buttons.addWidget(check_duplicates_btn)
    check_buttons.addWidget(check_gaps_btn)
    check_buttons.addWidget(check_hierarchy_btn)
    check_buttons.addWidget(full_validation_btn)
    check_buttons.addStretch()
    
    check_layout.addLayout(check_buttons)
    check_group.setLayout(check_layout)
    
    # مجموعة نتائج الفحص
    results_group = QGroupBox("📋 نتائج الفحص")
    results_layout = QVBoxLayout()
    
    # منطقة عرض النتائج
    self.validation_results = QTextEdit()
    self.validation_results.setPlaceholderText("ستظهر نتائج الفحص هنا...")
    self.validation_results.setStyleSheet("""
        QTextEdit {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    """)
    
    results_layout.addWidget(self.validation_results)
    results_group.setLayout(results_layout)
    
    # مجموعة الإصلاح التلقائي
    repair_group = QGroupBox("🔧 الإصلاح التلقائي")
    repair_layout = QGridLayout()
    
    # خيارات الإصلاح
    self.auto_fix_duplicates = QCheckBox("إصلاح التكرارات تلقائياً")
    repair_layout.addWidget(self.auto_fix_duplicates, 0, 0, 1, 2)
    
    self.auto_fill_gaps = QCheckBox("ملء الفجوات تلقائياً")
    repair_layout.addWidget(self.auto_fill_gaps, 1, 0, 1, 2)
    
    self.auto_fix_hierarchy = QCheckBox("إصلاح الهيكل تلقائياً")
    repair_layout.addWidget(self.auto_fix_hierarchy, 2, 0, 1, 2)
    
    # أزرار الإصلاح
    repair_selected_btn = QPushButton("🔧 إصلاح المحدد")
    repair_selected_btn.setStyleSheet("""
        QPushButton {
            background-color: #e67e22;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #d35400;
        }
    """)
    repair_selected_btn.clicked.connect(self.repair_selected)
    
    repair_all_btn = QPushButton("🔧 إصلاح الكل")
    repair_all_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    repair_all_btn.clicked.connect(self.repair_all)
    
    repair_layout.addWidget(repair_selected_btn, 0, 2)
    repair_layout.addWidget(repair_all_btn, 1, 2)
    
    repair_group.setLayout(repair_layout)
    
    layout.addWidget(check_group)
    layout.addWidget(results_group)
    layout.addWidget(repair_group)
    
    tab.setLayout(layout)
    return tab

def create_reports_tab_function(self):
    """إنشاء تبويب التقارير"""
    tab = QWidget()
    layout = QVBoxLayout()
    
    # مجموعة تقارير الترميز
    reports_group = QGroupBox("📊 تقارير نظام الترميز")
    reports_layout = QGridLayout()
    
    # أزرار التقارير
    coding_summary_btn = QPushButton("📋 ملخص الترميز")
    coding_summary_btn.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    coding_summary_btn.clicked.connect(self.generate_coding_summary)
    
    codes_list_btn = QPushButton("📝 قائمة الرموز")
    codes_list_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    codes_list_btn.clicked.connect(self.generate_codes_list)
    
    validation_report_btn = QPushButton("✅ تقرير التحقق")
    validation_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #e67e22;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #d35400;
        }
    """)
    validation_report_btn.clicked.connect(self.generate_validation_report)
    
    reports_layout.addWidget(coding_summary_btn, 0, 0)
    reports_layout.addWidget(codes_list_btn, 0, 1)
    reports_layout.addWidget(validation_report_btn, 0, 2)
    
    reports_group.setLayout(reports_layout)
    
    # منطقة عرض التقارير
    reports_display_group = QGroupBox("📄 عرض التقارير")
    reports_display_layout = QVBoxLayout()
    
    self.reports_display = QTextEdit()
    self.reports_display.setPlaceholderText("ستظهر التقارير هنا...")
    self.reports_display.setStyleSheet("""
        QTextEdit {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    """)
    
    # أزرار التحكم في التقارير
    report_buttons = QHBoxLayout()
    
    export_report_btn = QPushButton("📤 تصدير التقرير")
    export_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #17a2b8;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #138496;
        }
    """)
    export_report_btn.clicked.connect(self.export_report)
    
    print_report_btn = QPushButton("🖨️ طباعة التقرير")
    print_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #6c757d;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5a6268;
        }
    """)
    print_report_btn.clicked.connect(self.print_report)
    
    clear_report_btn = QPushButton("🗑️ مسح التقرير")
    clear_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #dc3545;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #c82333;
        }
    """)
    clear_report_btn.clicked.connect(self.clear_report)
    
    report_buttons.addWidget(export_report_btn)
    report_buttons.addWidget(print_report_btn)
    report_buttons.addWidget(clear_report_btn)
    report_buttons.addStretch()
    
    reports_display_layout.addWidget(self.reports_display)
    reports_display_layout.addLayout(report_buttons)
    reports_display_group.setLayout(reports_display_layout)
    
    layout.addWidget(reports_group)
    layout.addWidget(reports_display_group)
    
    tab.setLayout(layout)
    return tab

def create_control_buttons_function(self):
    """إنشاء أزرار التحكم"""
    layout = QHBoxLayout()
    
    # زر الحفظ
    save_btn = QPushButton("💾 حفظ نظام الترميز")
    save_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    save_btn.clicked.connect(self.save_coding_system)
    
    # زر الإلغاء
    cancel_btn = QPushButton("❌ إلغاء")
    cancel_btn.setStyleSheet("""
        QPushButton {
            background-color: #e74c3c;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #c0392b;
        }
    """)
    cancel_btn.clicked.connect(self.reject)
    
    # زر التطبيق
    apply_btn = QPushButton("✅ تطبيق النظام")
    apply_btn.setStyleSheet("""
        QPushButton {
            background-color: #f39c12;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #e67e22;
        }
    """)
    apply_btn.clicked.connect(self.apply_coding_system)
    
    # زر المساعدة
    help_btn = QPushButton("❓ المساعدة")
    help_btn.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    help_btn.clicked.connect(self.show_help)
    
    layout.addWidget(help_btn)
    layout.addWidget(apply_btn)
    layout.addStretch()
    layout.addWidget(save_btn)
    layout.addWidget(cancel_btn)
    
    return layout
