#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة المخازن والمخزون
Inventory and Warehouse Management Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QDoubleSpinBox, QCheckBox,
                             QDateEdit, QSplitter, QFrame, QDialogButtonBox,
                             QScrollArea, QProgressBar, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, Q<PERSON>con, <PERSON><PERSON><PERSON><PERSON>, QC<PERSON>r, Q<PERSON>ixmap, <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime

try:
    from ..database.sqlite_manager import SQLiteManager
except ImportError:
    # في حالة التشغيل المباشر
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class WarehouseDialog(QDialog):
    """حوار إضافة/تعديل مخزن"""
    
    def __init__(self, db_manager: SQLiteManager, warehouse_data=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.warehouse_data = warehouse_data
        self.is_edit_mode = warehouse_data is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_warehouse_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل مخزن" if self.is_edit_mode else "إضافة مخزن جديد"
        self.setWindowTitle(title)
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel(f"🏪 {title}")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            
            }
        """)
        main_layout.addWidget(title_label)
        
        # النموذج
        form_layout = QFormLayout()
        
        # رمز المخزن
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("رمز المخزن")
        form_layout.addRow("رمز المخزن:", self.code_edit)
        
        # اسم المخزن
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("اسم المخزن")
        form_layout.addRow("اسم المخزن:", self.name_edit)
        
        # الموقع
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("موقع المخزن")
        form_layout.addRow("الموقع:", self.location_edit)
        
        # نوع المخزن
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "مخزن رئيسي",
            "مخزن فرعي", 
            "مخزن مؤقت",
            "مخزن تالف",
            "مخزن مرتجعات"
        ])
        form_layout.addRow("نوع المخزن:", self.type_combo)
        
        # المسؤول
        self.manager_edit = QLineEdit()
        self.manager_edit.setPlaceholderText("اسم المسؤول عن المخزن")
        form_layout.addRow("المسؤول:", self.manager_edit)
        
        # الحالة
        self.is_active_check = QCheckBox("مخزن نشط")
        self.is_active_check.setChecked(True)
        form_layout.addRow("الحالة:", self.is_active_check)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        form_layout.addRow("ملاحظات:", self.notes_edit)
        
        main_layout.addLayout(form_layout)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        main_layout.addWidget(button_box)
        
        self.setLayout(main_layout)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_warehouse)
        self.cancel_btn.clicked.connect(self.reject)
    
    def load_warehouse_data(self):
        """تحميل بيانات المخزن للتعديل"""
        if self.warehouse_data:
            self.code_edit.setText(self.warehouse_data.get('code', ''))
            self.name_edit.setText(self.warehouse_data.get('name', ''))
            self.location_edit.setText(self.warehouse_data.get('location', ''))
            self.type_combo.setCurrentText(self.warehouse_data.get('type', 'مخزن رئيسي'))
            self.manager_edit.setText(self.warehouse_data.get('manager', ''))
            self.is_active_check.setChecked(self.warehouse_data.get('is_active', True))
            self.notes_edit.setPlainText(self.warehouse_data.get('notes', ''))
    
    def save_warehouse(self):
        """حفظ بيانات المخزن"""
        # التحقق من صحة البيانات
        if not self.code_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز المخزن")
            return
        
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المخزن")
            return
        
        try:
            if self.is_edit_mode:
                # تحديث المخزن
                query = """
                UPDATE warehouses SET 
                    code = ?, name = ?, location = ?, type = ?, 
                    manager = ?, is_active = ?, notes = ?, updated_at = ?
                WHERE id = ?
                """
                params = (
                    self.code_edit.text().strip(),
                    self.name_edit.text().strip(),
                    self.location_edit.text().strip(),
                    self.type_combo.currentText(),
                    self.manager_edit.text().strip(),
                    self.is_active_check.isChecked(),
                    self.notes_edit.toPlainText().strip(),
                    datetime.now().isoformat(),
                    self.warehouse_data['id']
                )
            else:
                # إضافة مخزن جديد
                query = """
                INSERT INTO warehouses 
                (code, name, location, type, manager, is_active, notes, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (
                    self.code_edit.text().strip(),
                    self.name_edit.text().strip(),
                    self.location_edit.text().strip(),
                    self.type_combo.currentText(),
                    self.manager_edit.text().strip(),
                    self.is_active_check.isChecked(),
                    self.notes_edit.toPlainText().strip(),
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                )
            
            self.db_manager.execute_update(query, params)
            
            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} المخزن بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المخزن:\n{str(e)}")


class InventoryMovementDialog(QDialog):
    """حوار حركة مخزنية (إدخال/إخراج)"""
    
    def __init__(self, db_manager: SQLiteManager, movement_type="in", parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.movement_type = movement_type  # "in" للإدخال، "out" للإخراج
        
        self.setup_ui()
        self.setup_connections()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "إدخال مخزني" if self.movement_type == "in" else "إخراج مخزني"
        icon = "📥" if self.movement_type == "in" else "📤"
        
        self.setWindowTitle(title)
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel(f"{icon} {title}")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            
            }
        """)
        main_layout.addWidget(title_label)
        
        # النموذج
        form_layout = QFormLayout()
        
        # رقم الحركة
        self.movement_number_edit = QLineEdit()
        self.movement_number_edit.setPlaceholderText("رقم الحركة (تلقائي)")
        self.movement_number_edit.setReadOnly(True)
        form_layout.addRow("رقم الحركة:", self.movement_number_edit)
        
        # التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        form_layout.addRow("التاريخ:", self.date_edit)
        
        # المخزن
        self.warehouse_combo = QComboBox()
        form_layout.addRow("المخزن:", self.warehouse_combo)
        
        # الصنف
        self.product_combo = QComboBox()
        form_layout.addRow("الصنف:", self.product_combo)
        
        # الكمية
        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setRange(0.01, 999999.99)
        self.quantity_spin.setDecimals(2)
        self.quantity_spin.setValue(1.00)
        form_layout.addRow("الكمية:", self.quantity_spin)
        
        # سعر الوحدة
        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setRange(0.00, 999999.99)
        self.unit_price_spin.setDecimals(2)
        form_layout.addRow("سعر الوحدة:", self.unit_price_spin)
        
        # المجموع
        self.total_edit = QLineEdit()
        self.total_edit.setReadOnly(True)
        form_layout.addRow("المجموع:", self.total_edit)
        
        # السبب
        self.reason_edit = QLineEdit()
        reason_placeholder = "سبب الإدخال" if self.movement_type == "in" else "سبب الإخراج"
        self.reason_edit.setPlaceholderText(reason_placeholder)
        form_layout.addRow("السبب:", self.reason_edit)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        form_layout.addRow("ملاحظات:", self.notes_edit)
        
        main_layout.addLayout(form_layout)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        main_layout.addWidget(button_box)
        
        self.setLayout(main_layout)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_movement)
        self.cancel_btn.clicked.connect(self.reject)
        self.quantity_spin.valueChanged.connect(self.calculate_total)
        self.unit_price_spin.valueChanged.connect(self.calculate_total)
    
    def load_data(self):
        """تحميل البيانات"""
        # تحميل المخازن
        warehouses = self.db_manager.execute_query(
            "SELECT id, name FROM warehouses WHERE is_active = 1"
        )
        for warehouse in warehouses:
            self.warehouse_combo.addItem(warehouse[1], warehouse[0])
        
        # تحميل الأصناف
        products = self.db_manager.execute_query(
            "SELECT id, COALESCE(name, product_name) as name FROM products WHERE is_active = 1"
        )
        for product in products:
            self.product_combo.addItem(product[1], product[0])
        
        # إنتاج رقم الحركة
        self.generate_movement_number()
    
    def generate_movement_number(self):
        """إنتاج رقم الحركة"""
        prefix = "IN" if self.movement_type == "in" else "OUT"
        date_str = datetime.now().strftime("%Y%m%d")
        
        # البحث عن آخر رقم
        query = """
        SELECT COUNT(*) FROM inventory_movements 
        WHERE movement_number LIKE ? AND DATE(created_at) = DATE('now')
        """
        result = self.db_manager.execute_query(query, (f"{prefix}{date_str}%",))
        count = result[0][0] if result else 0
        
        movement_number = f"{prefix}{date_str}{count + 1:03d}"
        self.movement_number_edit.setText(movement_number)
    
    def calculate_total(self):
        """حساب المجموع"""
        quantity = self.quantity_spin.value()
        unit_price = self.unit_price_spin.value()
        total = quantity * unit_price
        self.total_edit.setText(f"{total:.2f}")
    
    def save_movement(self):
        """حفظ الحركة المخزنية"""
        # التحقق من صحة البيانات
        if not self.warehouse_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار المخزن")
            return
        
        if not self.product_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار الصنف")
            return
        
        if self.quantity_spin.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كمية صحيحة")
            return
        
        try:
            # حفظ الحركة
            query = """
            INSERT INTO inventory_movements 
            (movement_number, movement_type, warehouse_id, product_id, quantity, 
             unit_price, total_amount, reason, notes, movement_date, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                self.movement_number_edit.text(),
                self.movement_type,
                self.warehouse_combo.currentData(),
                self.product_combo.currentData(),
                self.quantity_spin.value(),
                self.unit_price_spin.value(),
                self.quantity_spin.value() * self.unit_price_spin.value(),
                self.reason_edit.text().strip(),
                self.notes_edit.toPlainText().strip(),
                self.date_edit.date().toString("yyyy-MM-dd"),
                datetime.now().isoformat()
            )
            
            self.db_manager.execute_update(query, params)
            
            movement_type_ar = "الإدخال" if self.movement_type == "in" else "الإخراج"
            QMessageBox.information(self, "نجح", f"تم حفظ حركة {movement_type_ar} بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الحركة:\n{str(e)}")


class InventoryManagementWidget(QWidget):
    """وحدة إدارة المخازن والمخزون الرئيسية"""

    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # العنوان
        title = QLabel("📦 إدارة المخازن والمخزون")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        layout.addWidget(title)

        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب المخازن
        self.warehouses_tab = self.create_warehouses_tab()
        self.tabs.addTab(self.warehouses_tab, "🏪 المخازن")
        
        # تبويب الحركات المخزنية
        self.movements_tab = self.create_movements_tab()
        self.tabs.addTab(self.movements_tab, "📋 الحركات المخزنية")
        
        # تبويب أرصدة المخزون
        self.balances_tab = self.create_balances_tab()
        self.tabs.addTab(self.balances_tab, "📊 أرصدة المخزون")

        layout.addWidget(self.tabs)
        self.setLayout(layout)

    def create_warehouses_tab(self):
        """إنشاء تبويب المخازن"""
        tab = QWidget()
        layout = QVBoxLayout()

        # شريط الأدوات
        toolbar = QHBoxLayout()
        
        add_warehouse_btn = QPushButton("➕ إضافة مخزن")
        add_warehouse_btn.clicked.connect(self.add_warehouse)
        toolbar.addWidget(add_warehouse_btn)
        
        edit_warehouse_btn = QPushButton("✏️ تعديل")
        edit_warehouse_btn.clicked.connect(self.edit_warehouse)
        toolbar.addWidget(edit_warehouse_btn)
        
        delete_warehouse_btn = QPushButton("🗑️ حذف")
        delete_warehouse_btn.clicked.connect(self.delete_warehouse)
        toolbar.addWidget(delete_warehouse_btn)
        
        toolbar.addStretch()
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.load_warehouses)
        toolbar.addWidget(refresh_btn)

        layout.addLayout(toolbar)

        # جدول المخازن
        self.warehouses_table = QTableWidget()
        self.warehouses_table.setColumnCount(6)
        self.warehouses_table.setHorizontalHeaderLabels([
            "الرمز", "اسم المخزن", "الموقع", "النوع", "المسؤول", "الحالة"
        ])
        
        header = self.warehouses_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.warehouses_table)
        
        tab.setLayout(layout)
        return tab

    def create_movements_tab(self):
        """إنشاء تبويب الحركات المخزنية"""
        tab = QWidget()
        layout = QVBoxLayout()

        # شريط الأدوات
        toolbar = QHBoxLayout()
        
        in_movement_btn = QPushButton("📥 إدخال مخزني")
        in_movement_btn.clicked.connect(self.add_in_movement)
        toolbar.addWidget(in_movement_btn)
        
        out_movement_btn = QPushButton("📤 إخراج مخزني")
        out_movement_btn.clicked.connect(self.add_out_movement)
        toolbar.addWidget(out_movement_btn)
        
        toolbar.addStretch()
        
        refresh_movements_btn = QPushButton("🔄 تحديث")
        refresh_movements_btn.clicked.connect(self.load_movements)
        toolbar.addWidget(refresh_movements_btn)

        layout.addLayout(toolbar)

        # جدول الحركات
        self.movements_table = QTableWidget()
        self.movements_table.setColumnCount(7)
        self.movements_table.setHorizontalHeaderLabels([
            "رقم الحركة", "النوع", "التاريخ", "المخزن", "الصنف", "الكمية", "المبلغ"
        ])
        
        header = self.movements_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.movements_table)
        
        tab.setLayout(layout)
        return tab

    def create_balances_tab(self):
        """إنشاء تبويب أرصدة المخزون"""
        tab = QWidget()
        layout = QVBoxLayout()

        # شريط الأدوات
        toolbar = QHBoxLayout()
        
        refresh_balances_btn = QPushButton("🔄 تحديث الأرصدة")
        refresh_balances_btn.clicked.connect(self.load_balances)
        toolbar.addWidget(refresh_balances_btn)
        
        toolbar.addStretch()

        layout.addLayout(toolbar)

        # جدول الأرصدة
        self.balances_table = QTableWidget()
        self.balances_table.setColumnCount(5)
        self.balances_table.setHorizontalHeaderLabels([
            "المخزن", "الصنف", "الرصيد", "متوسط السعر", "القيمة الإجمالية"
        ])
        
        header = self.balances_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.balances_table)
        
        tab.setLayout(layout)
        return tab

    def load_data(self):
        """تحميل جميع البيانات"""
        self.load_warehouses()
        self.load_movements()
        self.load_balances()

    def load_warehouses(self):
        """تحميل المخازن"""
        try:
            warehouses = self.db_manager.execute_query("""
                SELECT code, name, location, type, manager, is_active 
                FROM warehouses ORDER BY name
            """)
            
            self.warehouses_table.setRowCount(len(warehouses))
            
            for row, warehouse in enumerate(warehouses):
                for col, value in enumerate(warehouse):
                    if col == 5:  # الحالة
                        status = "نشط" if value else "غير نشط"
                        item = QTableWidgetItem(status)
                        if not value:
                            item.setBackground(QColor("#ffebee"))
                    else:
                        item = QTableWidgetItem(str(value) if value else "")
                    
                    self.warehouses_table.setItem(row, col, item)
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المخازن:\n{str(e)}")

    def load_movements(self):
        """تحميل الحركات المخزنية"""
        try:
            movements = self.db_manager.execute_query("""
                SELECT im.movement_number, im.movement_type, im.movement_date,
                       w.name as warehouse_name, p.name as product_name,
                       im.quantity, im.total_amount
                FROM inventory_movements im
                LEFT JOIN warehouses w ON im.warehouse_id = w.id
                LEFT JOIN products p ON im.product_id = p.id
                ORDER BY im.created_at DESC
                LIMIT 100
            """)
            
            self.movements_table.setRowCount(len(movements))
            
            for row, movement in enumerate(movements):
                for col, value in enumerate(movement):
                    if col == 1:  # نوع الحركة
                        movement_type = "إدخال" if value == "in" else "إخراج"
                        item = QTableWidgetItem(movement_type)
                        if value == "in":
                            item.setBackground(QColor("#e8f5e8"))
                        else:
                            item.setBackground(QColor("#ffebee"))
                    else:
                        item = QTableWidgetItem(str(value) if value else "")
                    
                    self.movements_table.setItem(row, col, item)
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الحركات:\n{str(e)}")

    def load_balances(self):
        """تحميل أرصدة المخزون"""
        try:
            # هذا استعلام مبسط - يمكن تطويره لحساب الأرصدة الفعلية
            balances = self.db_manager.execute_query("""
                SELECT w.name as warehouse_name, COALESCE(p.name, p.product_name) as product_name,
                       COALESCE(SUM(CASE WHEN im.movement_type = 'in' THEN im.quantity ELSE -im.quantity END), 0) as balance,
                       COALESCE(AVG(im.unit_price), 0) as avg_price,
                       COALESCE(SUM(CASE WHEN im.movement_type = 'in' THEN im.quantity ELSE -im.quantity END), 0) * 
                       COALESCE(AVG(im.unit_price), 0) as total_value
                FROM warehouses w
                CROSS JOIN products p
                LEFT JOIN inventory_movements im ON w.id = im.warehouse_id AND p.id = im.product_id
                WHERE w.is_active = 1 AND p.is_active = 1
                GROUP BY w.id, p.id
                HAVING balance > 0
                ORDER BY w.name, p.name
            """)
            
            self.balances_table.setRowCount(len(balances))
            
            for row, balance in enumerate(balances):
                for col, value in enumerate(balance):
                    if col in [2, 3, 4]:  # الأرقام
                        item = QTableWidgetItem(f"{float(value):.2f}" if value else "0.00")
                    else:
                        item = QTableWidgetItem(str(value) if value else "")
                    
                    self.balances_table.setItem(row, col, item)
                    
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الأرصدة:\n{str(e)}")

    def add_warehouse(self):
        """إضافة مخزن جديد"""
        dialog = WarehouseDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_warehouses()

    def edit_warehouse(self):
        """تعديل مخزن"""
        current_row = self.warehouses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مخزن للتعديل")
            return
        
        # الحصول على بيانات المخزن (هذا مبسط - يحتاج لتطوير)
        warehouse_data = {
            'code': self.warehouses_table.item(current_row, 0).text(),
            'name': self.warehouses_table.item(current_row, 1).text(),
            'location': self.warehouses_table.item(current_row, 2).text(),
            'type': self.warehouses_table.item(current_row, 3).text(),
            'manager': self.warehouses_table.item(current_row, 4).text(),
            'is_active': self.warehouses_table.item(current_row, 5).text() == "نشط"
        }
        
        dialog = WarehouseDialog(self.db_manager, warehouse_data, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_warehouses()

    def delete_warehouse(self):
        """حذف مخزن"""
        current_row = self.warehouses_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مخزن للحذف")
            return
        
        warehouse_name = self.warehouses_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المخزن '{warehouse_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                warehouse_code = self.warehouses_table.item(current_row, 0).text()
                self.db_manager.execute_update(
                    "DELETE FROM warehouses WHERE code = ?", 
                    (warehouse_code,)
                )
                QMessageBox.information(self, "نجح", "تم حذف المخزن بنجاح")
                self.load_warehouses()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المخزن:\n{str(e)}")

    def add_in_movement(self):
        """إضافة حركة إدخال"""
        dialog = InventoryMovementDialog(self.db_manager, "in", parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_movements()
            self.load_balances()

    def add_out_movement(self):
        """إضافة حركة إخراج"""
        dialog = InventoryMovementDialog(self.db_manager, "out", parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_movements()
            self.load_balances()


def main():
    """اختبار الوحدة"""
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        def execute_query(self, query, params=None):
            return []
        
        def execute_update(self, query, params=None):
            pass
    
    widget = InventoryManagementWidget(MockDBManager())
    widget.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
