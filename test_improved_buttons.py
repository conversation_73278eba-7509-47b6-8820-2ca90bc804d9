#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الأزرار المحسنة
Improved Buttons Test
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QToolBar, QAction, QWidget, QLabel, QPushButton, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class ImprovedButtonsWindow(QMainWindow):
    """نافذة اختبار الأزرار المحسنة"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار الأزرار المحسنة - Onyx Pro ERP")
        self.setGeometry(200, 200, 1000, 600)
        
        # تطبيق تنسيق محسن للأزرار
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0e6f7;
                font-family: "Tahoma", "Arial", sans-serif;
            }
            QToolBar {
                background-color: #e8e8e8;
                border: 1px solid #bdc3c7;
                spacing: 3px;
                padding: 5px;
            }
            QToolBar QToolButton {
                background-color: #f8f9fa;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px 12px;
                margin: 2px;
                color: #2c3e50;
                font-size: 12px;
                font-weight: bold;
                min-width: 60px;
                min-height: 32px;
            }
            QToolBar QToolButton:hover {
                background-color: #e3f2fd;
                border: 2px solid #2196f3;
                color: #1976d2;
            }
            QToolBar QToolButton:pressed {
                background-color: #bbdefb;
                border: 2px solid #1565c0;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # شريط الأدوات المحسن
        self.create_improved_toolbar()
        layout.addWidget(self.toolbar)
        
        # العنوان
        title_label = QLabel("🔧 اختبار الأزرار المحسنة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                color: #e91e63;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 8px;
                padding: 15px;
                margin: 15px;
                text-align: center;
                font-weight: bold;
            }
        """)
        layout.addWidget(title_label)
        
        # معلومات التحسينات
        info_label = QLabel("""
✅ التحسينات المطبقة على الأزرار:

📏 الحجم:
• ارتفاع شريط الأدوات: 50px (بدلاً من 40px)
• عرض الأزرار: 60px كحد أدنى
• ارتفاع الأزرار: 32px كحد أدنى
• حشو داخلي: 8px × 12px

🎨 التصميم:
• خط أكبر: 12px (بدلاً من 10px)
• خط عريض: font-weight: bold
• حدود أوضح عند التمرير
• ألوان تفاعلية محسنة

📝 النصوص:
• نصوص واضحة مع الأيقونات
• تلميحات مفصلة للأزرار
• اختصارات لوحة المفاتيح

🔧 الوظائف:
• ربط الأحداث للأزرار المهمة
• رسائل تأكيد واضحة
• فواصل منطقية بين المجموعات
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin: 15px;
                font-size: 12px;
                line-height: 1.5;
            }
        """)
        layout.addWidget(info_label)
        
        # أزرار اختبار إضافية
        buttons_layout = QHBoxLayout()
        
        test_save_btn = QPushButton("🧪 اختبار الحفظ")
        test_save_btn.clicked.connect(self.test_save)
        buttons_layout.addWidget(test_save_btn)
        
        test_print_btn = QPushButton("🧪 اختبار الطباعة")
        test_print_btn.clicked.connect(self.test_print)
        buttons_layout.addWidget(test_print_btn)
        
        test_new_btn = QPushButton("🧪 اختبار سند جديد")
        test_new_btn.clicked.connect(self.test_new)
        buttons_layout.addWidget(test_new_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        central_widget.setLayout(layout)
    
    def create_improved_toolbar(self):
        """إنشاء شريط الأدوات المحسن"""
        self.toolbar = QToolBar()
        self.toolbar.setFixedHeight(50)  # ارتفاع أكبر
        
        # أزرار شريط الأدوات مع نصوص واضحة
        toolbar_buttons = [
            ("💾 حفظ", "حفظ السند (Ctrl+S)", self.save_action),
            ("📄 جديد", "سند جديد (Ctrl+N)", self.new_action),
            ("✏️ تعديل", "تعديل السند (F2)", self.edit_action),
            ("🗑️ حذف", "حذف السند (Delete)", self.delete_action),
            ("🖨️ طباعة", "طباعة السند (Ctrl+P)", self.print_action),
            ("👁️ معاينة", "معاينة الطباعة", self.preview_action),
            ("📂 فتح", "فتح سند موجود (Ctrl+O)", self.open_action),
            ("🔍 بحث", "البحث في السندات (Ctrl+F)", self.search_action),
            ("⏮️ الأول", "السند الأول", self.first_action),
            ("⏪ السابق", "السند السابق", self.previous_action),
            ("⏩ التالي", "السند التالي", self.next_action),
            ("⏭️ الأخير", "السند الأخير", self.last_action),
            ("📊 تقرير", "تقارير السندات", self.reports_action),
            ("⚙️ إعدادات", "إعدادات النظام", self.settings_action),
            ("❓ مساعدة", "المساعدة (F1)", self.help_action),
            ("❌ إغلاق", "إغلاق النافذة", self.close)
        ]
        
        for text, tooltip, callback in toolbar_buttons:
            action = QAction(text, self)
            action.setToolTip(tooltip)
            action.triggered.connect(callback)
            self.toolbar.addAction(action)
            
            # إضافة فواصل بعد مجموعات معينة
            if text in ["🗑️ حذف", "📂 فتح", "⏭️ الأخير"]:
                self.toolbar.addSeparator()
    
    # دوال الأحداث
    def save_action(self):
        QMessageBox.information(self, "حفظ", "✅ تم اختبار زر الحفظ!\nالزر واضح ومقروء الآن.")
    
    def new_action(self):
        QMessageBox.information(self, "جديد", "📄 تم اختبار زر السند الجديد!\nالحجم والخط محسنان.")
    
    def edit_action(self):
        QMessageBox.information(self, "تعديل", "✏️ تم اختبار زر التعديل!\nالأزرار أصبحت أكبر وأوضح.")
    
    def delete_action(self):
        QMessageBox.question(self, "حذف", "🗑️ تم اختبار زر الحذف!\nهل تريد المتابعة؟")
    
    def print_action(self):
        QMessageBox.information(self, "طباعة", "🖨️ تم اختبار زر الطباعة!\nالزر أصبح أكثر وضوحاً.")
    
    def preview_action(self):
        QMessageBox.information(self, "معاينة", "👁️ تم اختبار زر المعاينة!")
    
    def open_action(self):
        QMessageBox.information(self, "فتح", "📂 تم اختبار زر الفتح!")
    
    def search_action(self):
        QMessageBox.information(self, "بحث", "🔍 تم اختبار زر البحث!")
    
    def first_action(self):
        QMessageBox.information(self, "الأول", "⏮️ تم اختبار زر السند الأول!")
    
    def previous_action(self):
        QMessageBox.information(self, "السابق", "⏪ تم اختبار زر السند السابق!")
    
    def next_action(self):
        QMessageBox.information(self, "التالي", "⏩ تم اختبار زر السند التالي!")
    
    def last_action(self):
        QMessageBox.information(self, "الأخير", "⏭️ تم اختبار زر السند الأخير!")
    
    def reports_action(self):
        QMessageBox.information(self, "تقارير", "📊 تم اختبار زر التقارير!")
    
    def settings_action(self):
        QMessageBox.information(self, "إعدادات", "⚙️ تم اختبار زر الإعدادات!")
    
    def help_action(self):
        QMessageBox.information(self, "مساعدة", "❓ تم اختبار زر المساعدة!")
    
    def test_save(self):
        QMessageBox.information(self, "اختبار الحفظ", 
                              "💾 اختبار وظيفة الحفظ:\n\n"
                              "✅ الزر أصبح أكبر وأوضح\n"
                              "✅ النص واضح ومقروء\n"
                              "✅ التلميح يظهر الاختصار\n"
                              "✅ التفاعل محسن")
    
    def test_print(self):
        QMessageBox.information(self, "اختبار الطباعة", 
                              "🖨️ اختبار وظيفة الطباعة:\n\n"
                              "✅ الزر بحجم مناسب\n"
                              "✅ الأيقونة واضحة\n"
                              "✅ النص مقروء\n"
                              "✅ الألوان متناسقة")
    
    def test_new(self):
        QMessageBox.information(self, "اختبار سند جديد", 
                              "📄 اختبار وظيفة السند الجديد:\n\n"
                              "✅ الزر محسن بصرياً\n"
                              "✅ سهل النقر عليه\n"
                              "✅ التلميح مفيد\n"
                              "✅ التصميم احترافي")


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont("Tahoma", 11)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = ImprovedButtonsWindow()
    window.show()
    
    print("🔧 تم تشغيل اختبار الأزرار المحسنة!")
    print("✅ التحسينات:")
    print("   📏 أزرار أكبر وأوضح")
    print("   🎨 تصميم محسن")
    print("   📝 نصوص واضحة")
    print("   🔧 وظائف مربوطة")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
