#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة ربط الحسابات بإدخال المشاريع
Account-Project Linking Setup Screen
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QMessageBox, 
                             QFrame, QSplitter, QTextEdit, QTabWidget, QWidget,
                             QTreeWidget, QTreeWidgetItem, QListWidget, QListWidgetItem,
                             QProgressBar, QSlider, QDateEdit, QRadioButton, QButtonGroup)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QColor, QIcon

class AccountProjectLinkingDialog(QDialog):
    """شاشة ربط الحسابات بإدخال المشاريع"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("ربط الحسابات بإدخال المشاريع - نظام ربط الحسابات المحاسبية بالمشاريع")
        self.setGeometry(50, 50, 1600, 1000)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان الشاشة
        title_label = QLabel("🔗 ربط الحسابات بإدخال المشاريع - نظام الربط المحاسبي للمشاريع")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 22px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تقسيم الشاشة
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - شجرة الحسابات
        left_panel = self.create_accounts_panel()
        splitter.addWidget(left_panel)
        
        # الجانب الأيمن - إدارة الربط
        right_panel = self.create_linking_panel()
        splitter.addWidget(right_panel)
        
        # تحديد نسب التقسيم
        splitter.setSizes([600, 1000])
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(splitter)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_accounts_panel(self):
        """إنشاء لوحة الحسابات"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # عنوان القسم
        section_title = QLabel("📊 الحسابات المحاسبية")
        section_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #34495e;
                padding: 10px;
                background-color: #d5dbdb;
                border-radius: 5px;
                margin-bottom: 15px;
            }
        """)
        section_title.setAlignment(Qt.AlignCenter)
        
        # شجرة الحسابات
        self.accounts_tree = QTreeWidget()
        self.accounts_tree.setHeaderLabels(["اسم الحساب", "رقم الحساب", "النوع", "حالة الربط"])
        self.accounts_tree.setRootIsDecorated(True)
        self.accounts_tree.setAnimated(True)
        
        self.accounts_tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                font-size: 14px;
                outline: none;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
                min-height: 30px;
            }
            QTreeWidget::item:hover {
                background-color: #e8f4fd;
            }
            QTreeWidget::item:selected {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
        """)
        
        # ربط الأحداث
        self.accounts_tree.itemClicked.connect(self.on_account_selected)
        self.accounts_tree.itemDoubleClicked.connect(self.on_account_double_clicked)
        
        # أزرار إدارة الحسابات
        accounts_buttons = QHBoxLayout()
        
        expand_all_btn = QPushButton("📂 توسيع الكل")
        expand_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        expand_all_btn.clicked.connect(self.accounts_tree.expandAll)
        
        collapse_all_btn = QPushButton("📁 طي الكل")
        collapse_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        collapse_all_btn.clicked.connect(self.accounts_tree.collapseAll)
        
        refresh_accounts_btn = QPushButton("🔄 تحديث")
        refresh_accounts_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        refresh_accounts_btn.clicked.connect(self.refresh_accounts)
        
        accounts_buttons.addWidget(expand_all_btn)
        accounts_buttons.addWidget(collapse_all_btn)
        accounts_buttons.addWidget(refresh_accounts_btn)
        accounts_buttons.addStretch()
        
        # معلومات الحسابات
        self.accounts_info = QLabel("📊 إحصائيات: 0 حساب، 0 مربوط، 0 غير مربوط")
        self.accounts_info.setStyleSheet("""
            QLabel {
                background-color: #e8f4fd;
                border: 1px solid #bee5eb;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                color: #0c5460;
            }
        """)
        
        layout.addWidget(section_title)
        layout.addWidget(self.accounts_tree)
        layout.addLayout(accounts_buttons)
        layout.addWidget(self.accounts_info)
        
        panel.setLayout(layout)
        return panel
        
    def create_linking_panel(self):
        """إنشاء لوحة الربط"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # تبويبات الربط
        tabs = QTabWidget()
        
        # تبويب إعداد الربط
        linking_setup_tab = self.create_linking_setup_tab()
        tabs.addTab(linking_setup_tab, "🔗 إعداد الربط")
        
        # تبويب المشاريع
        projects_tab = self.create_projects_tab()
        tabs.addTab(projects_tab, "📁 المشاريع")
        
        # تبويب قواعد الربط
        linking_rules_tab = self.create_linking_rules_tab()
        tabs.addTab(linking_rules_tab, "📋 قواعد الربط")
        
        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        tabs.addTab(reports_tab, "📊 التقارير")
        
        layout.addWidget(tabs)
        panel.setLayout(layout)
        return panel
        
    def create_linking_setup_tab(self):
        """إنشاء تبويب إعداد الربط"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة معلومات الحساب المحدد
        account_info_group = QGroupBox("📊 معلومات الحساب المحدد")
        account_info_layout = QGridLayout()
        
        # اسم الحساب
        account_info_layout.addWidget(QLabel("اسم الحساب:"), 0, 0)
        self.selected_account_name = QLineEdit()
        self.selected_account_name.setReadOnly(True)
        self.selected_account_name.setPlaceholderText("لم يتم اختيار حساب...")
        account_info_layout.addWidget(self.selected_account_name, 0, 1)
        
        # رقم الحساب
        account_info_layout.addWidget(QLabel("رقم الحساب:"), 0, 2)
        self.selected_account_number = QLineEdit()
        self.selected_account_number.setReadOnly(True)
        account_info_layout.addWidget(self.selected_account_number, 0, 3)
        
        # نوع الحساب
        account_info_layout.addWidget(QLabel("نوع الحساب:"), 1, 0)
        self.selected_account_type = QLineEdit()
        self.selected_account_type.setReadOnly(True)
        account_info_layout.addWidget(self.selected_account_type, 1, 1)
        
        # حالة الربط الحالية
        account_info_layout.addWidget(QLabel("حالة الربط:"), 1, 2)
        self.current_linking_status = QLineEdit()
        self.current_linking_status.setReadOnly(True)
        account_info_layout.addWidget(self.current_linking_status, 1, 3)
        
        account_info_group.setLayout(account_info_layout)
        
        # مجموعة إعدادات الربط
        linking_settings_group = QGroupBox("🔗 إعدادات الربط")
        linking_settings_layout = QGridLayout()
        
        # تفعيل الربط
        self.enable_linking = QCheckBox("تفعيل ربط هذا الحساب بالمشاريع")
        linking_settings_layout.addWidget(self.enable_linking, 0, 0, 1, 4)
        
        # نوع الربط
        linking_settings_layout.addWidget(QLabel("نوع الربط:"), 1, 0)
        self.linking_type = QComboBox()
        self.linking_type.addItems([
            "ربط إجباري", "ربط اختياري", "ربط تلقائي", "ربط شرطي"
        ])
        linking_settings_layout.addWidget(self.linking_type, 1, 1)
        
        # مستوى الربط
        linking_settings_layout.addWidget(QLabel("مستوى الربط:"), 1, 2)
        self.linking_level = QComboBox()
        self.linking_level.addItems([
            "مشروع رئيسي", "مشروع فرعي", "مرحلة مشروع", "نشاط مشروع"
        ])
        linking_settings_layout.addWidget(self.linking_level, 1, 3)
        
        # المشروع الافتراضي
        linking_settings_layout.addWidget(QLabel("المشروع الافتراضي:"), 2, 0)
        self.default_project = QComboBox()
        self.default_project.addItem("-- اختر مشروع --")
        linking_settings_layout.addWidget(self.default_project, 2, 1)
        
        # مركز التكلفة
        linking_settings_layout.addWidget(QLabel("مركز التكلفة:"), 2, 2)
        self.cost_center = QComboBox()
        self.cost_center.addItems([
            "-- اختر مركز تكلفة --", "الإدارة العامة", "المبيعات", "الإنتاج", "التسويق"
        ])
        linking_settings_layout.addWidget(self.cost_center, 2, 3)
        
        # خيارات إضافية
        self.require_project_approval = QCheckBox("يتطلب موافقة مدير المشروع")
        linking_settings_layout.addWidget(self.require_project_approval, 3, 0, 1, 2)
        
        self.auto_create_entries = QCheckBox("إنشاء قيود تلقائية عند الربط")
        linking_settings_layout.addWidget(self.auto_create_entries, 3, 2, 1, 2)
        
        linking_settings_group.setLayout(linking_settings_layout)
        
        # أزرار الربط
        linking_buttons = QHBoxLayout()
        
        apply_linking_btn = QPushButton("✅ تطبيق الربط")
        apply_linking_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        apply_linking_btn.clicked.connect(self.apply_linking)
        
        remove_linking_btn = QPushButton("❌ إزالة الربط")
        remove_linking_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        remove_linking_btn.clicked.connect(self.remove_linking)
        
        test_linking_btn = QPushButton("🧪 اختبار الربط")
        test_linking_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        test_linking_btn.clicked.connect(self.test_linking)
        
        linking_buttons.addWidget(apply_linking_btn)
        linking_buttons.addWidget(remove_linking_btn)
        linking_buttons.addWidget(test_linking_btn)
        linking_buttons.addStretch()
        
        layout.addWidget(account_info_group)
        layout.addWidget(linking_settings_group)
        layout.addLayout(linking_buttons)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab

    def create_projects_tab(self):
        """إنشاء تبويب المشاريع"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة إدارة المشاريع
        projects_group = QGroupBox("📁 إدارة المشاريع")
        projects_layout = QVBoxLayout()

        # جدول المشاريع
        self.projects_table = QTableWidget()
        self.projects_table.setColumnCount(7)
        self.projects_table.setHorizontalHeaderLabels([
            "اسم المشروع", "رمز المشروع", "النوع", "الحالة", "تاريخ البداية", "تاريخ النهاية", "الحسابات المربوطة"
        ])

        # تنسيق الجدول
        self.projects_table.horizontalHeader().setStretchLastSection(True)
        self.projects_table.setAlternatingRowColors(True)
        self.projects_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.projects_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)

        projects_layout.addWidget(self.projects_table)

        # أزرار إدارة المشاريع
        projects_buttons = QHBoxLayout()

        add_project_btn = QPushButton("➕ إضافة مشروع")
        add_project_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_project_btn.clicked.connect(self.add_project)

        edit_project_btn = QPushButton("✏️ تعديل مشروع")
        edit_project_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_project_btn.clicked.connect(self.edit_project)

        delete_project_btn = QPushButton("🗑️ حذف مشروع")
        delete_project_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_project_btn.clicked.connect(self.delete_project)

        projects_buttons.addWidget(add_project_btn)
        projects_buttons.addWidget(edit_project_btn)
        projects_buttons.addWidget(delete_project_btn)
        projects_buttons.addStretch()

        projects_layout.addLayout(projects_buttons)
        projects_group.setLayout(projects_layout)

        # مجموعة تفاصيل المشروع المحدد
        project_details_group = QGroupBox("📋 تفاصيل المشروع المحدد")
        project_details_layout = QGridLayout()

        # اسم المشروع
        project_details_layout.addWidget(QLabel("اسم المشروع:"), 0, 0)
        self.project_name = QLineEdit()
        self.project_name.setPlaceholderText("مثال: مشروع تطوير النظام المحاسبي")
        project_details_layout.addWidget(self.project_name, 0, 1)

        # رمز المشروع
        project_details_layout.addWidget(QLabel("رمز المشروع:"), 0, 2)
        self.project_code = QLineEdit()
        self.project_code.setPlaceholderText("مثال: PROJ-001")
        self.project_code.setMaxLength(20)
        project_details_layout.addWidget(self.project_code, 0, 3)

        # نوع المشروع
        project_details_layout.addWidget(QLabel("نوع المشروع:"), 1, 0)
        self.project_type = QComboBox()
        self.project_type.addItems([
            "مشروع داخلي", "مشروع خارجي", "مشروع استثماري", "مشروع تطويري", "مشروع صيانة"
        ])
        project_details_layout.addWidget(self.project_type, 1, 1)

        # حالة المشروع
        project_details_layout.addWidget(QLabel("حالة المشروع:"), 1, 2)
        self.project_status = QComboBox()
        self.project_status.addItems([
            "مخطط", "قيد التنفيذ", "متوقف مؤقتاً", "مكتمل", "ملغي"
        ])
        project_details_layout.addWidget(self.project_status, 1, 3)

        # تاريخ البداية
        project_details_layout.addWidget(QLabel("تاريخ البداية:"), 2, 0)
        self.project_start_date = QDateEdit()
        self.project_start_date.setDate(QDate.currentDate())
        self.project_start_date.setCalendarPopup(True)
        project_details_layout.addWidget(self.project_start_date, 2, 1)

        # تاريخ النهاية
        project_details_layout.addWidget(QLabel("تاريخ النهاية:"), 2, 2)
        self.project_end_date = QDateEdit()
        self.project_end_date.setDate(QDate.currentDate().addDays(365))
        self.project_end_date.setCalendarPopup(True)
        project_details_layout.addWidget(self.project_end_date, 2, 3)

        # الوصف
        project_details_layout.addWidget(QLabel("وصف المشروع:"), 3, 0)
        self.project_description = QTextEdit()
        self.project_description.setMaximumHeight(80)
        self.project_description.setPlaceholderText("وصف مختصر للمشروع وأهدافه...")
        project_details_layout.addWidget(self.project_description, 3, 1, 1, 3)

        project_details_group.setLayout(project_details_layout)

        layout.addWidget(projects_group)
        layout.addWidget(project_details_group)

        tab.setLayout(layout)
        return tab

    def create_linking_rules_tab(self):
        """إنشاء تبويب قواعد الربط"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة القواعد العامة
        general_rules_group = QGroupBox("📋 القواعد العامة للربط")
        general_rules_layout = QGridLayout()

        # قواعد الربط الإجباري
        self.mandatory_linking_accounts = QCheckBox("ربط إجباري لحسابات المصروفات")
        self.mandatory_linking_accounts.setChecked(True)
        general_rules_layout.addWidget(self.mandatory_linking_accounts, 0, 0, 1, 2)

        self.mandatory_linking_revenues = QCheckBox("ربط إجباري لحسابات الإيرادات")
        self.mandatory_linking_revenues.setChecked(True)
        general_rules_layout.addWidget(self.mandatory_linking_revenues, 1, 0, 1, 2)

        # قواعد الربط الاختياري
        self.optional_linking_assets = QCheckBox("ربط اختياري لحسابات الأصول")
        general_rules_layout.addWidget(self.optional_linking_assets, 2, 0, 1, 2)

        self.optional_linking_liabilities = QCheckBox("ربط اختياري لحسابات الخصوم")
        general_rules_layout.addWidget(self.optional_linking_liabilities, 3, 0, 1, 2)

        # إعدادات التحقق
        general_rules_layout.addWidget(QLabel("مستوى التحقق:"), 0, 2)
        self.validation_level = QComboBox()
        self.validation_level.addItems(["أساسي", "متوسط", "صارم"])
        self.validation_level.setCurrentText("متوسط")
        general_rules_layout.addWidget(self.validation_level, 0, 3)

        general_rules_layout.addWidget(QLabel("إجراء عند عدم الربط:"), 1, 2)
        self.no_linking_action = QComboBox()
        self.no_linking_action.addItems(["تحذير", "منع", "سؤال المستخدم", "تجاهل"])
        self.no_linking_action.setCurrentText("سؤال المستخدم")
        general_rules_layout.addWidget(self.no_linking_action, 1, 3)

        general_rules_group.setLayout(general_rules_layout)

        # مجموعة قواعد مخصصة
        custom_rules_group = QGroupBox("⚙️ قواعد مخصصة")
        custom_rules_layout = QVBoxLayout()

        # جدول القواعد المخصصة
        self.custom_rules_table = QTableWidget()
        self.custom_rules_table.setColumnCount(5)
        self.custom_rules_table.setHorizontalHeaderLabels([
            "اسم القاعدة", "نوع الحساب", "شرط الربط", "المشروع الافتراضي", "الحالة"
        ])

        # تنسيق الجدول
        self.custom_rules_table.horizontalHeader().setStretchLastSection(True)
        self.custom_rules_table.setAlternatingRowColors(True)
        self.custom_rules_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.custom_rules_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)

        custom_rules_layout.addWidget(self.custom_rules_table)

        # أزرار إدارة القواعد
        rules_buttons = QHBoxLayout()

        add_rule_btn = QPushButton("➕ إضافة قاعدة")
        add_rule_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_rule_btn.clicked.connect(self.add_custom_rule)

        edit_rule_btn = QPushButton("✏️ تعديل قاعدة")
        edit_rule_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_rule_btn.clicked.connect(self.edit_custom_rule)

        delete_rule_btn = QPushButton("🗑️ حذف قاعدة")
        delete_rule_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_rule_btn.clicked.connect(self.delete_custom_rule)

        rules_buttons.addWidget(add_rule_btn)
        rules_buttons.addWidget(edit_rule_btn)
        rules_buttons.addWidget(delete_rule_btn)
        rules_buttons.addStretch()

        custom_rules_layout.addLayout(rules_buttons)
        custom_rules_group.setLayout(custom_rules_layout)

        layout.addWidget(general_rules_group)
        layout.addWidget(custom_rules_group)

        tab.setLayout(layout)
        return tab

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        from .account_project_linking_functions import create_reports_tab_function
        return create_reports_tab_function(self)

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        from .account_project_linking_functions import create_control_buttons_function
        return create_control_buttons_function(self)

    def load_data(self):
        """تحميل البيانات المحفوظة"""
        from .account_project_linking_data import load_data_function
        load_data_function(self)

    def load_accounts_data(self):
        """تحميل بيانات الحسابات"""
        from .account_project_linking_data import load_accounts_data_function
        load_accounts_data_function(self)

    def load_projects_data(self):
        """تحميل بيانات المشاريع"""
        from .account_project_linking_data import load_projects_data_function
        load_projects_data_function(self)

    def load_custom_rules(self):
        """تحميل القواعد المخصصة"""
        from .account_project_linking_data import load_custom_rules_function
        load_custom_rules_function(self)

    def build_accounts_tree(self):
        """بناء شجرة الحسابات"""
        from .account_project_linking_data import build_accounts_tree_function
        build_accounts_tree_function(self)

    def update_accounts_statistics(self):
        """تحديث إحصائيات الحسابات"""
        from .account_project_linking_data import update_accounts_statistics_function
        update_accounts_statistics_function(self)

    def on_account_selected(self, item, column):
        """معالجة اختيار حساب من الشجرة"""
        from .account_project_linking_data import on_account_selected_function
        on_account_selected_function(self, item, column)

    def on_account_double_clicked(self, item, column):
        """معالجة النقر المزدوج على حساب في الشجرة"""
        from .account_project_linking_data import on_account_double_clicked_function
        on_account_double_clicked_function(self, item, column)

    def refresh_accounts(self):
        """تحديث شجرة الحسابات"""
        from .account_project_linking_data import refresh_accounts_function
        refresh_accounts_function(self)

    def apply_linking(self):
        """تطبيق الربط"""
        from .account_project_linking_data import apply_linking_function
        apply_linking_function(self)

    def remove_linking(self):
        """إزالة الربط"""
        from .account_project_linking_data import remove_linking_function
        remove_linking_function(self)

    def test_linking(self):
        """اختبار الربط"""
        from .account_project_linking_data import test_linking_function
        test_linking_function(self)

    def generate_linking_summary(self):
        """إنتاج ملخص الربط"""
        from .account_project_linking_functions import generate_linking_summary_function
        generate_linking_summary_function(self)

    def generate_project_accounts(self):
        """إنتاج تقرير حسابات المشاريع"""
        from .account_project_linking_functions import generate_project_accounts_function
        generate_project_accounts_function(self)

    def generate_unlinked_accounts(self):
        """إنتاج تقرير الحسابات غير المربوطة"""
        from .account_project_linking_functions import generate_unlinked_accounts_function
        generate_unlinked_accounts_function(self)

    def show_help(self):
        """عرض المساعدة"""
        from .account_project_linking_functions import show_help_function
        show_help_function(self)

    # وظائف إضافية (ستكون في ملف منفصل)
    def add_project(self):
        """إضافة مشروع جديد"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة المشاريع قيد التطوير")

    def edit_project(self):
        """تعديل مشروع"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل المشاريع قيد التطوير")

    def delete_project(self):
        """حذف مشروع"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حذف المشاريع قيد التطوير")

    def add_custom_rule(self):
        """إضافة قاعدة مخصصة"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة القواعد المخصصة قيد التطوير")

    def edit_custom_rule(self):
        """تعديل قاعدة مخصصة"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل القواعد المخصصة قيد التطوير")

    def delete_custom_rule(self):
        """حذف قاعدة مخصصة"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حذف القواعد المخصصة قيد التطوير")

    def export_report(self):
        """تصدير التقرير"""
        QMessageBox.information(self, "قيد التطوير", "تصدير التقارير قيد التطوير")

    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "قيد التطوير", "طباعة التقارير قيد التطوير")

    def clear_report(self):
        """مسح التقرير"""
        if hasattr(self, 'reports_display'):
            self.reports_display.clear()

    def save_linking_settings(self):
        """حفظ إعدادات الربط"""
        QMessageBox.information(self, "قيد التطوير", "حفظ إعدادات الربط قيد التطوير")

    def validate_linking(self):
        """التحقق من الربط"""
        QMessageBox.information(self, "قيد التطوير", "التحقق من الربط قيد التطوير")


def main():
    """اختبار الشاشة"""
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass

    dialog = AccountProjectLinkingDialog(MockDBManager())
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
