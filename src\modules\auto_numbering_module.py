#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة الترقيم التلقائي للسندات والفواتير - نسخة مبسطة
Auto Numbering Module for Documents and Invoices - Simplified Version
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QGroupBox, QGridLayout, QHeaderView, QSpinBox, 
                             QCheckBox, QDialogButtonBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import datetime
import sqlite3

try:
    from ..database.sqlite_manager import SQLiteManager
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class SimpleNumberingDialog(QDialog):
    """حوار بسيط لإضافة/تعديل تسلسل الترقيم"""
    
    def __init__(self, db_manager, sequence_data=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.sequence_data = sequence_data
        self.is_edit_mode = sequence_data is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل تسلسل الترقيم" if self.is_edit_mode else "إضافة تسلسل ترقيم جديد"
        self.setWindowTitle(title)
        self.setFixedSize(400, 350)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel(f"🔢 {title}")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # النموذج
        form_layout = QFormLayout()
        
        # نوع المستند
        self.document_type_combo = QComboBox()
        self.document_type_combo.addItems([
            "فاتورة مبيعات",
            "فاتورة مشتريات", 
            "سند قبض",
            "سند صرف",
            "قيد يومية"
        ])
        form_layout.addRow("نوع المستند:", self.document_type_combo)
        
        # البادئة
        self.prefix_edit = QLineEdit()
        self.prefix_edit.setPlaceholderText("مثال: INV, REC, PAY")
        form_layout.addRow("البادئة:", self.prefix_edit)
        
        # الرقم الحالي
        self.current_number_spin = QSpinBox()
        self.current_number_spin.setRange(1, 999999)
        self.current_number_spin.setValue(1)
        form_layout.addRow("الرقم الحالي:", self.current_number_spin)
        
        # طول الرقم
        self.number_length_spin = QSpinBox()
        self.number_length_spin.setRange(1, 10)
        self.number_length_spin.setValue(6)
        form_layout.addRow("طول الرقم:", self.number_length_spin)
        
        # تضمين السنة
        self.include_year_check = QCheckBox("تضمين السنة في الرقم")
        self.include_year_check.setChecked(True)
        form_layout.addRow("", self.include_year_check)
        
        # الفاصل
        self.separator_edit = QLineEdit()
        self.separator_edit.setText("-")
        self.separator_edit.setMaxLength(3)
        form_layout.addRow("الفاصل:", self.separator_edit)
        
        # حالة التفعيل
        self.is_active_check = QCheckBox("مفعل")
        self.is_active_check.setChecked(True)
        form_layout.addRow("الحالة:", self.is_active_check)
        
        layout.addLayout(form_layout)
        
        # معاينة الرقم
        preview_group = QGroupBox("معاينة الرقم")
        preview_layout = QVBoxLayout()
        
        self.preview_label = QLabel("INV-2025-000001")
        self.preview_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 14px;
                font-weight: bold;
                color: #27ae60;
                padding: 10px;
                background-color: #f8f9fa;
                border: 2px solid #27ae60;
                border-radius: 5px;
                text-align: center;
            
            }
        """)
        self.preview_label.setAlignment(Qt.AlignCenter)
        preview_layout.addWidget(self.preview_label)
        
        preview_group.setLayout(preview_layout)
        layout.addWidget(preview_group)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        layout.addWidget(button_box)
        self.setLayout(layout)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_sequence)
        self.cancel_btn.clicked.connect(self.reject)
        
        # تحديث المعاينة عند تغيير أي قيمة
        self.prefix_edit.textChanged.connect(self.update_preview)
        self.current_number_spin.valueChanged.connect(self.update_preview)
        self.number_length_spin.valueChanged.connect(self.update_preview)
        self.include_year_check.toggled.connect(self.update_preview)
        self.separator_edit.textChanged.connect(self.update_preview)
    
    def update_preview(self):
        """تحديث معاينة الرقم"""
        try:
            prefix = self.prefix_edit.text().strip()
            number = self.current_number_spin.value()
            length = self.number_length_spin.value()
            separator = self.separator_edit.text().strip() or "-"
            include_year = self.include_year_check.isChecked()
            
            # تنسيق الرقم
            formatted_number = str(number).zfill(length)
            
            # بناء الرقم النهائي
            parts = []
            
            if prefix:
                parts.append(prefix)
            
            if include_year:
                current_year = datetime.now().year
                parts.append(str(current_year))
            
            parts.append(formatted_number)
            
            final_number = separator.join(parts)
            self.preview_label.setText(final_number)
            
        except Exception as e:
            self.preview_label.setText("خطأ في المعاينة")
    
    def load_data(self):
        """تحميل بيانات التسلسل للتعديل"""
        if self.sequence_data:
            doc_type = self.sequence_data.get('document_type', '')
            index = self.document_type_combo.findText(doc_type)
            if index >= 0:
                self.document_type_combo.setCurrentIndex(index)
            
            self.prefix_edit.setText(self.sequence_data.get('prefix', ''))
            self.current_number_spin.setValue(self.sequence_data.get('current_number', 1))
            self.number_length_spin.setValue(self.sequence_data.get('number_length', 6))
            self.include_year_check.setChecked(self.sequence_data.get('include_year', True))
            self.separator_edit.setText(self.sequence_data.get('separator', '-'))
            self.is_active_check.setChecked(self.sequence_data.get('is_active', True))
            
            self.update_preview()
    
    def save_sequence(self):
        """حفظ تسلسل الترقيم"""
        if not self.prefix_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال البادئة")
            return

        # التحقق من عدم وجود تسلسل مكرر (فقط عند الإضافة)
        if not self.is_edit_mode:
            try:
                check_query = "SELECT COUNT(*) FROM numbering_sequences WHERE document_type = ?"
                result = self.db_manager.execute_query(check_query, (self.document_type_combo.currentText(),))
                if result and result[0][0] > 0:
                    QMessageBox.warning(self, "تحذير",
                                      f"يوجد تسلسل بالفعل لنوع المستند '{self.document_type_combo.currentText()}'")
                    return
            except Exception as e:
                print(f"خطأ في فحص التكرار: {e}")

        try:
            if self.is_edit_mode:
                # تحديث التسلسل
                query = """
                UPDATE numbering_sequences SET 
                    document_type = ?, prefix = ?, current_number = ?, number_length = ?,
                    include_year = ?, separator = ?, is_active = ?, updated_at = ?
                WHERE id = ?
                """
                params = (
                    self.document_type_combo.currentText(),
                    self.prefix_edit.text().strip(),
                    self.current_number_spin.value(),
                    self.number_length_spin.value(),
                    self.include_year_check.isChecked(),
                    self.separator_edit.text().strip(),
                    self.is_active_check.isChecked(),
                    datetime.now().isoformat(),
                    self.sequence_data['id']
                )
            else:
                # إضافة تسلسل جديد
                query = """
                INSERT INTO numbering_sequences 
                (document_type, prefix, current_number, number_length, include_year, 
                 separator, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (
                    self.document_type_combo.currentText(),
                    self.prefix_edit.text().strip(),
                    self.current_number_spin.value(),
                    self.number_length_spin.value(),
                    self.include_year_check.isChecked(),
                    self.separator_edit.text().strip(),
                    self.is_active_check.isChecked(),
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                )
            
            self.db_manager.execute_update(query, params)

            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} تسلسل الترقيم بنجاح")

            # تحديث البيانات في الجدول الرئيسي
            if hasattr(self.parent(), 'load_data'):
                self.parent().load_data()

            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ تسلسل الترقيم:\n{str(e)}")


class AutoNumberingWidget(QWidget):
    """وحدة الترقيم التلقائي الرئيسية - نسخة مبسطة"""

    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_database()
        self.setup_ui()
        self.load_data()

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            # حذف الجدول القديم إذا كان موجوداً
            try:
                self.db_manager.execute_update("DROP TABLE IF EXISTS numbering_sequences")
                print("تم حذف الجدول القديم")
            except:
                pass

            # إنشاء الجدول الجديد
            create_query = """
            CREATE TABLE numbering_sequences (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                document_type TEXT NOT NULL UNIQUE,
                prefix TEXT NOT NULL,
                current_number INTEGER DEFAULT 1,
                number_length INTEGER DEFAULT 6,
                include_year BOOLEAN DEFAULT 1,
                separator TEXT DEFAULT '-',
                is_active BOOLEAN DEFAULT 1,
                created_at TEXT,
                updated_at TEXT
            )
            """
            self.db_manager.execute_update(create_query)
            print("تم إنشاء جدول التسلسلات الجديد")

            # إضافة التسلسلات الافتراضية
            self.add_default_sequences()

        except Exception as e:
            print(f"خطأ في إعداد قاعدة البيانات: {e}")
    
    def add_default_sequences(self):
        """إضافة التسلسلات الافتراضية"""
        try:
            default_sequences = [
                ('فاتورة مبيعات', 'INV'),
                ('فاتورة مشتريات', 'PUR'),
                ('سند قبض', 'REC'),
                ('سند صرف', 'PAY'),
                ('قيد يومية', 'JE')
            ]
            
            for doc_type, prefix in default_sequences:
                try:
                    insert_query = """
                    INSERT OR IGNORE INTO numbering_sequences 
                    (document_type, prefix, current_number, number_length, include_year, 
                     separator, is_active, created_at, updated_at)
                    VALUES (?, ?, 1, 6, 1, '-', 1, ?, ?)
                    """
                    
                    now = datetime.now().isoformat()
                    self.db_manager.execute_update(insert_query, (doc_type, prefix, now, now))
                    
                except Exception as e:
                    print(f"خطأ في إضافة التسلسل {doc_type}: {e}")
                    
        except Exception as e:
            print(f"خطأ في إضافة التسلسلات الافتراضية: {e}")

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # العنوان
        title = QLabel("🔢 إدارة الترقيم التلقائي")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        layout.addWidget(title)

        # شريط الأدوات
        toolbar = QHBoxLayout()

        add_btn = QPushButton("➕ إضافة تسلسل")
        add_btn.clicked.connect(self.add_sequence)
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        toolbar.addWidget(add_btn)

        edit_btn = QPushButton("✏️ تعديل")
        edit_btn.clicked.connect(self.edit_sequence)
        toolbar.addWidget(edit_btn)

        delete_btn = QPushButton("🗑️ حذف")
        delete_btn.clicked.connect(self.delete_sequence)
        toolbar.addWidget(delete_btn)

        toolbar.addStretch()

        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_data)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        toolbar.addWidget(refresh_btn)

        layout.addLayout(toolbar)

        # جدول التسلسلات
        self.sequences_table = QTableWidget()
        self.sequences_table.setColumnCount(6)
        self.sequences_table.setHorizontalHeaderLabels([
            "نوع المستند", "البادئة", "الرقم الحالي", "طول الرقم", "الحالة", "معاينة"
        ])

        header = self.sequences_table.horizontalHeader()
        header.setStretchLastSection(True)

        layout.addWidget(self.sequences_table)
        self.setLayout(layout)

    def load_data(self):
        """تحميل بيانات التسلسلات"""
        try:
            query = """
            SELECT id, document_type, prefix, current_number, number_length,
                   include_year, separator, is_active
            FROM numbering_sequences ORDER BY document_type
            """

            sequences = self.db_manager.execute_query(query)

            if not sequences:
                print("لا توجد تسلسلات في قاعدة البيانات")
                self.sequences_table.setRowCount(0)
                return

            self.sequences_table.setRowCount(len(sequences))

            for row, seq in enumerate(sequences):
                try:
                    # التعامل مع البيانات سواء كانت tuple أو dict
                    if isinstance(seq, dict):
                        doc_type = seq.get('document_type', '')
                        prefix = seq.get('prefix', '')
                        current_num = seq.get('current_number', 1)
                        num_length = seq.get('number_length', 6)
                        is_active = seq.get('is_active', True)
                    else:
                        doc_type = seq[1] if len(seq) > 1 else ""
                        prefix = seq[2] if len(seq) > 2 else ""
                        current_num = seq[3] if len(seq) > 3 else 1
                        num_length = seq[4] if len(seq) > 4 else 6
                        is_active = seq[7] if len(seq) > 7 else True

                    # نوع المستند
                    self.sequences_table.setItem(row, 0, QTableWidgetItem(str(doc_type)))

                    # البادئة
                    self.sequences_table.setItem(row, 1, QTableWidgetItem(str(prefix)))

                    # الرقم الحالي
                    self.sequences_table.setItem(row, 2, QTableWidgetItem(str(current_num)))

                    # طول الرقم
                    self.sequences_table.setItem(row, 3, QTableWidgetItem(str(num_length)))

                    # الحالة
                    status = "مفعل" if is_active else "معطل"
                    status_item = QTableWidgetItem(status)
                    if not is_active:
                        status_item.setBackground(QColor("#ffebee"))
                    self.sequences_table.setItem(row, 4, status_item)

                    # معاينة الرقم
                    preview = self.generate_preview(seq)
                    self.sequences_table.setItem(row, 5, QTableWidgetItem(preview))

                except Exception as row_error:
                    print(f"خطأ في معالجة الصف {row}: {row_error}")
                    continue

            print(f"تم تحميل {len(sequences)} تسلسل بنجاح")
            # إزالة رسالة التحديث المزعجة
            # QMessageBox.information(self, "تحديث البيانات", f"تم تحميل {len(sequences)} تسلسل بنجاح")

        except Exception as e:
            error_msg = f"فشل في تحميل التسلسلات:\n{str(e)}"
            print(error_msg)
            QMessageBox.critical(self, "خطأ", error_msg)

    def generate_preview(self, sequence):
        """إنتاج معاينة للرقم"""
        try:
            # التعامل مع البيانات سواء كانت tuple أو dict
            if isinstance(sequence, dict):
                prefix = sequence.get('prefix', '')
                number = sequence.get('current_number', 1)
                length = sequence.get('number_length', 6)
                include_year = sequence.get('include_year', True)
                separator = sequence.get('separator', '-')
            else:
                if len(sequence) < 6:
                    return "بيانات ناقصة"

                prefix = sequence[2] if sequence[2] else ""
                number = sequence[3] if sequence[3] else 1
                length = sequence[4] if sequence[4] else 6
                include_year = sequence[5] if len(sequence) > 5 else True
                separator = sequence[6] if len(sequence) > 6 else "-"

            # تنسيق الرقم
            formatted_number = str(number).zfill(length)

            # بناء الرقم النهائي
            parts = []

            if prefix:
                parts.append(prefix)

            if include_year:
                current_year = datetime.now().year
                parts.append(str(current_year))

            parts.append(formatted_number)

            return separator.join(parts)

        except Exception as e:
            print(f"خطأ في معاينة الرقم: {e}")
            return "خطأ في المعاينة"

    def refresh_data(self):
        """تحديث البيانات مع رسالة تأكيد"""
        try:
            self.load_data()

            # عرض رسالة تأكيد فقط عند الضغط على زر التحديث
            row_count = self.sequences_table.rowCount()
            QMessageBox.information(self, "تحديث البيانات",
                                  f"✅ تم تحديث البيانات بنجاح!\n\n"
                                  f"📊 عدد التسلسلات: {row_count}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث البيانات:\n{str(e)}")

    def add_sequence(self):
        """إضافة تسلسل جديد"""
        dialog = SimpleNumberingDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_data()

    def edit_sequence(self):
        """تعديل تسلسل موجود"""
        current_row = self.sequences_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار تسلسل للتعديل")
            return

        try:
            # الحصول على معرف التسلسل
            doc_type = self.sequences_table.item(current_row, 0).text()

            # جلب بيانات التسلسل من قاعدة البيانات
            query = """
            SELECT id, document_type, prefix, current_number, number_length,
                   include_year, separator, is_active
            FROM numbering_sequences WHERE document_type = ?
            """

            result = self.db_manager.execute_query(query, (doc_type,))

            if result:
                seq = result[0]
                sequence_data = {
                    'id': seq[0],
                    'document_type': seq[1],
                    'prefix': seq[2],
                    'current_number': seq[3],
                    'number_length': seq[4],
                    'include_year': seq[5],
                    'separator': seq[6],
                    'is_active': seq[7]
                }

                dialog = SimpleNumberingDialog(self.db_manager, sequence_data, parent=self)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_data()
            else:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على التسلسل")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات التسلسل:\n{str(e)}")

    def delete_sequence(self):
        """حذف تسلسل"""
        current_row = self.sequences_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار تسلسل للحذف")
            return

        doc_type = self.sequences_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف تسلسل '{doc_type}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                query = "DELETE FROM numbering_sequences WHERE document_type = ?"
                self.db_manager.execute_update(query, (doc_type,))

                QMessageBox.information(self, "نجح", "تم حذف التسلسل بنجاح")
                self.load_data()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف التسلسل:\n{str(e)}")

    @staticmethod
    def get_next_number(db_manager, document_type):
        """الحصول على الرقم التالي لنوع مستند معين"""
        try:
            # جلب بيانات التسلسل
            query = """
            SELECT prefix, current_number, number_length, include_year, separator, is_active
            FROM numbering_sequences WHERE document_type = ? AND is_active = 1
            """

            result = db_manager.execute_query(query, (document_type,))

            if not result:
                # إنشاء تسلسل افتراضي
                return f"AUTO-{datetime.now().strftime('%Y%m%d')}-001"

            seq = result[0]
            prefix = seq[0]
            current_number = seq[1]
            length = seq[2]
            include_year = seq[3]
            separator = seq[4]

            # تنسيق الرقم
            formatted_number = str(current_number).zfill(length)

            # بناء الرقم النهائي
            parts = []

            if prefix:
                parts.append(prefix)

            if include_year:
                current_year = datetime.now().year
                parts.append(str(current_year))

            parts.append(formatted_number)

            final_number = separator.join(parts)

            # تحديث الرقم الحالي
            update_query = """
            UPDATE numbering_sequences
            SET current_number = current_number + 1, updated_at = ?
            WHERE document_type = ?
            """

            db_manager.execute_update(update_query, (datetime.now().isoformat(), document_type))

            return final_number

        except Exception as e:
            print(f"خطأ في إنتاج الرقم: {e}")
            return f"AUTO-{datetime.now().strftime('%Y%m%d')}-001"
