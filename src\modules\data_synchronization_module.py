#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة مزامنة البيانات بين الفروع
Data Synchronization Module Between Branches
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QCheckBox, QDateTimeEdit,
                             QProgressBar, QSplitter, QFrame, QDialogButtonBox,
                             QTreeWidget, QTreeWidgetItem, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, QDateTime, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QIcon
from datetime import datetime, timedelta
import json
import socket
import threading

try:
    from ..database.sqlite_manager import SQLiteManager
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class BranchConfigDialog(QDialog):
    """حوار إعداد فرع"""
    
    def __init__(self, db_manager, branch_data=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.branch_data = branch_data
        self.is_edit_mode = branch_data is not None
        self.setup_ui()
        
        if self.is_edit_mode:
            self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🏢 إعداد فرع")
        self.setFixedSize(500, 600)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # العنوان
        title = "تعديل فرع" if self.is_edit_mode else "فرع جديد"
        title_label = QLabel(f"🏢 {title}")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: white;
                background-color: #2980b9;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # معلومات الفرع
        info_group = QGroupBox("معلومات الفرع")
        info_layout = QFormLayout()
        
        self.branch_name_edit = QLineEdit()
        self.branch_name_edit.setPlaceholderText("اسم الفرع...")
        info_layout.addRow("اسم الفرع:", self.branch_name_edit)
        
        self.branch_code_edit = QLineEdit()
        self.branch_code_edit.setPlaceholderText("رمز الفرع...")
        info_layout.addRow("رمز الفرع:", self.branch_code_edit)
        
        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("الموقع...")
        info_layout.addRow("الموقع:", self.location_edit)
        
        self.manager_edit = QLineEdit()
        self.manager_edit.setPlaceholderText("مدير الفرع...")
        info_layout.addRow("مدير الفرع:", self.manager_edit)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # إعدادات الاتصال
        connection_group = QGroupBox("إعدادات الاتصال")
        connection_layout = QFormLayout()
        
        self.server_ip_edit = QLineEdit()
        self.server_ip_edit.setPlaceholderText("*************")
        connection_layout.addRow("عنوان الخادم:", self.server_ip_edit)
        
        self.server_port_spin = QSpinBox()
        self.server_port_spin.setRange(1, 65535)
        self.server_port_spin.setValue(5432)
        connection_layout.addRow("منفذ الخادم:", self.server_port_spin)
        
        self.database_name_edit = QLineEdit()
        self.database_name_edit.setPlaceholderText("اسم قاعدة البيانات...")
        connection_layout.addRow("قاعدة البيانات:", self.database_name_edit)
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("اسم المستخدم...")
        connection_layout.addRow("اسم المستخدم:", self.username_edit)
        
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("كلمة المرور...")
        connection_layout.addRow("كلمة المرور:", self.password_edit)
        
        connection_group.setLayout(connection_layout)
        layout.addWidget(connection_group)
        
        # إعدادات المزامنة
        sync_group = QGroupBox("إعدادات المزامنة")
        sync_layout = QFormLayout()
        
        self.sync_interval_spin = QSpinBox()
        self.sync_interval_spin.setRange(1, 1440)
        self.sync_interval_spin.setValue(60)
        self.sync_interval_spin.setSuffix(" دقيقة")
        sync_layout.addRow("فترة المزامنة:", self.sync_interval_spin)
        
        self.auto_sync_check = QCheckBox("مزامنة تلقائية")
        self.auto_sync_check.setChecked(True)
        sync_layout.addRow("", self.auto_sync_check)
        
        self.bidirectional_check = QCheckBox("مزامنة ثنائية الاتجاه")
        self.bidirectional_check.setChecked(True)
        sync_layout.addRow("", self.bidirectional_check)
        
        self.compress_data_check = QCheckBox("ضغط البيانات")
        self.compress_data_check.setChecked(True)
        sync_layout.addRow("", self.compress_data_check)
        
        sync_group.setLayout(sync_layout)
        layout.addWidget(sync_group)
        
        # أزرار الاختبار
        test_layout = QHBoxLayout()
        
        test_connection_btn = QPushButton("🔗 اختبار الاتصال")
        test_connection_btn.clicked.connect(self.test_connection)
        test_layout.addWidget(test_connection_btn)
        
        test_sync_btn = QPushButton("🔄 اختبار المزامنة")
        test_sync_btn.clicked.connect(self.test_sync)
        test_layout.addWidget(test_sync_btn)
        
        test_layout.addStretch()
        layout.addLayout(test_layout)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("💾 حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        self.save_btn.clicked.connect(self.save_branch)
        self.cancel_btn.clicked.connect(self.reject)
        
        layout.addWidget(button_box)
        self.setLayout(layout)
    
    def load_data(self):
        """تحميل بيانات الفرع للتعديل"""
        if self.branch_data:
            self.branch_name_edit.setText(self.branch_data.get('name', ''))
            self.branch_code_edit.setText(self.branch_data.get('code', ''))
            self.location_edit.setText(self.branch_data.get('location', ''))
            self.manager_edit.setText(self.branch_data.get('manager', ''))
            self.server_ip_edit.setText(self.branch_data.get('server_ip', ''))
            self.server_port_spin.setValue(self.branch_data.get('server_port', 5432))
            self.database_name_edit.setText(self.branch_data.get('database_name', ''))
            self.username_edit.setText(self.branch_data.get('username', ''))
            self.sync_interval_spin.setValue(self.branch_data.get('sync_interval', 60))
            self.auto_sync_check.setChecked(self.branch_data.get('auto_sync', True))
            self.bidirectional_check.setChecked(self.branch_data.get('bidirectional', True))
            self.compress_data_check.setChecked(self.branch_data.get('compress_data', True))
    
    def test_connection(self):
        """اختبار الاتصال"""
        try:
            server_ip = self.server_ip_edit.text().strip()
            server_port = self.server_port_spin.value()
            
            if not server_ip:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال عنوان الخادم")
                return
            
            # محاولة الاتصال
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((server_ip, server_port))
            sock.close()
            
            if result == 0:
                QMessageBox.information(self, "نجح", "تم الاتصال بالخادم بنجاح")
            else:
                QMessageBox.warning(self, "فشل", "فشل في الاتصال بالخادم")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في اختبار الاتصال:\n{str(e)}")
    
    def test_sync(self):
        """اختبار المزامنة"""
        QMessageBox.information(self, "اختبار المزامنة", "تم اختبار المزامنة بنجاح\n(محاكاة)")
    
    def save_branch(self):
        """حفظ الفرع"""
        try:
            # التحقق من البيانات المطلوبة
            if not self.branch_name_edit.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الفرع")
                return
            
            if not self.branch_code_edit.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز الفرع")
                return
            
            # إنشاء جدول الفروع إذا لم يكن موجوداً
            create_query = """
            CREATE TABLE IF NOT EXISTS branches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                code TEXT UNIQUE NOT NULL,
                location TEXT,
                manager TEXT,
                server_ip TEXT,
                server_port INTEGER,
                database_name TEXT,
                username TEXT,
                password TEXT,
                sync_interval INTEGER DEFAULT 60,
                auto_sync BOOLEAN DEFAULT 1,
                bidirectional BOOLEAN DEFAULT 1,
                compress_data BOOLEAN DEFAULT 1,
                is_active BOOLEAN DEFAULT 1,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_sync DATETIME
            )
            """
            self.db_manager.execute_update(create_query)
            
            # حفظ البيانات
            if self.is_edit_mode:
                # تحديث
                query = """
                UPDATE branches SET
                    name = ?, location = ?, manager = ?, server_ip = ?, 
                    server_port = ?, database_name = ?, username = ?, password = ?,
                    sync_interval = ?, auto_sync = ?, bidirectional = ?, compress_data = ?
                WHERE id = ?
                """
                params = (
                    self.branch_name_edit.text().strip(),
                    self.location_edit.text().strip(),
                    self.manager_edit.text().strip(),
                    self.server_ip_edit.text().strip(),
                    self.server_port_spin.value(),
                    self.database_name_edit.text().strip(),
                    self.username_edit.text().strip(),
                    self.password_edit.text(),  # يجب تشفيرها في التطبيق الحقيقي
                    self.sync_interval_spin.value(),
                    self.auto_sync_check.isChecked(),
                    self.bidirectional_check.isChecked(),
                    self.compress_data_check.isChecked(),
                    self.branch_data['id']
                )
            else:
                # إدراج جديد
                query = """
                INSERT INTO branches 
                (name, code, location, manager, server_ip, server_port, 
                 database_name, username, password, sync_interval, auto_sync, 
                 bidirectional, compress_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (
                    self.branch_name_edit.text().strip(),
                    self.branch_code_edit.text().strip(),
                    self.location_edit.text().strip(),
                    self.manager_edit.text().strip(),
                    self.server_ip_edit.text().strip(),
                    self.server_port_spin.value(),
                    self.database_name_edit.text().strip(),
                    self.username_edit.text().strip(),
                    self.password_edit.text(),  # يجب تشفيرها في التطبيق الحقيقي
                    self.sync_interval_spin.value(),
                    self.auto_sync_check.isChecked(),
                    self.bidirectional_check.isChecked(),
                    self.compress_data_check.isChecked()
                )
            
            self.db_manager.execute_update(query, params)
            
            QMessageBox.information(self, "تم", "تم حفظ الفرع بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الفرع:\n{str(e)}")


class SyncProgressDialog(QDialog):
    """حوار تقدم المزامنة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔄 مزامنة البيانات")
        self.setFixedSize(500, 300)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🔄 جاري مزامنة البيانات...")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: white;
                background-color: #3498db;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # معلومات المزامنة
        self.sync_info_label = QLabel("جاري التحضير...")
        layout.addWidget(self.sync_info_label)
        
        # شريط التقدم الإجمالي
        self.overall_progress = QProgressBar()
        self.overall_progress.setRange(0, 100)
        layout.addWidget(QLabel("التقدم الإجمالي:"))
        layout.addWidget(self.overall_progress)
        
        # شريط التقدم الحالي
        self.current_progress = QProgressBar()
        self.current_progress.setRange(0, 100)
        layout.addWidget(QLabel("العملية الحالية:"))
        layout.addWidget(self.current_progress)
        
        # سجل المزامنة
        self.sync_log = QTextEdit()
        self.sync_log.setMaximumHeight(100)
        self.sync_log.setReadOnly(True)
        layout.addWidget(QLabel("سجل المزامنة:"))
        layout.addWidget(self.sync_log)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.pause_btn = QPushButton("⏸️ إيقاف مؤقت")
        self.pause_btn.clicked.connect(self.pause_sync)
        buttons_layout.addWidget(self.pause_btn)
        
        self.cancel_btn = QPushButton("❌ إلغاء")
        self.cancel_btn.clicked.connect(self.cancel_sync)
        buttons_layout.addWidget(self.cancel_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
    
    def update_progress(self, overall, current, message):
        """تحديث التقدم"""
        self.overall_progress.setValue(overall)
        self.current_progress.setValue(current)
        self.sync_info_label.setText(message)
        self.sync_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
    
    def pause_sync(self):
        """إيقاف المزامنة مؤقتاً"""
        if self.pause_btn.text() == "⏸️ إيقاف مؤقت":
            self.pause_btn.setText("▶️ متابعة")
            self.sync_log.append("تم إيقاف المزامنة مؤقتاً")
        else:
            self.pause_btn.setText("⏸️ إيقاف مؤقت")
            self.sync_log.append("تم استئناف المزامنة")
    
    def cancel_sync(self):
        """إلغاء المزامنة"""
        reply = QMessageBox.question(
            self, "تأكيد الإلغاء",
            "هل أنت متأكد من إلغاء المزامنة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.sync_log.append("تم إلغاء المزامنة")
            self.reject()


class DataSynchronizationWidget(QWidget):
    """وحدة مزامنة البيانات الرئيسية"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_branches()
        self.setup_auto_sync()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🔄 مزامنة البيانات بين الفروع")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 20px;
                font-weight: bold;
                color: white;
                background-color: #2980b9;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب الفروع
        branches_tab = self.create_branches_tab()
        self.tabs.addTab(branches_tab, "🏢 الفروع")
        
        # تبويب المزامنة
        sync_tab = self.create_sync_tab()
        self.tabs.addTab(sync_tab, "🔄 المزامنة")
        
        # تبويب السجل
        log_tab = self.create_log_tab()
        self.tabs.addTab(log_tab, "📋 السجل")
        
        # تبويب الإعدادات
        settings_tab = self.create_settings_tab()
        self.tabs.addTab(settings_tab, "⚙️ الإعدادات")
        
        layout.addWidget(self.tabs)
        self.setLayout(layout)
    
    def create_branches_tab(self):
        """إنشاء تبويب الفروع"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        add_branch_btn = QPushButton("➕ إضافة فرع")
        add_branch_btn.clicked.connect(self.add_branch)
        add_branch_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        buttons_layout.addWidget(add_branch_btn)
        
        edit_branch_btn = QPushButton("✏️ تعديل")
        edit_branch_btn.clicked.connect(self.edit_branch)
        buttons_layout.addWidget(edit_branch_btn)
        
        delete_branch_btn = QPushButton("🗑️ حذف")
        delete_branch_btn.clicked.connect(self.delete_branch)
        buttons_layout.addWidget(delete_branch_btn)
        
        test_connection_btn = QPushButton("🔗 اختبار الاتصال")
        test_connection_btn.clicked.connect(self.test_branch_connection)
        buttons_layout.addWidget(test_connection_btn)
        
        buttons_layout.addStretch()
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.load_branches)
        buttons_layout.addWidget(refresh_btn)
        
        layout.addLayout(buttons_layout)
        
        # جدول الفروع
        self.branches_table = QTableWidget()
        self.branches_table.setColumnCount(8)
        self.branches_table.setHorizontalHeaderLabels([
            "اسم الفرع", "الرمز", "الموقع", "المدير", "عنوان الخادم", 
            "الحالة", "آخر مزامنة", "المزامنة التلقائية"
        ])
        
        header = self.branches_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.branches_table)
        
        widget.setLayout(layout)
        return widget
    
    def create_sync_tab(self):
        """إنشاء تبويب المزامنة"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # حالة المزامنة
        status_group = QGroupBox("🔄 حالة المزامنة")
        status_layout = QGridLayout()
        
        self.sync_status_label = QLabel("متوقف")
        self.sync_status_label.setStyleSheet("""
            QLabel {
                background-color: #95a5a6;
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 14px;
            }
        """)
        status_layout.addWidget(QLabel("الحالة:"), 0, 0)
        status_layout.addWidget(self.sync_status_label, 0, 1)
        
        self.last_sync_label = QLabel("لم تتم مزامنة بعد")
        status_layout.addWidget(QLabel("آخر مزامنة:"), 1, 0)
        status_layout.addWidget(self.last_sync_label, 1, 1)
        
        self.next_sync_label = QLabel("غير محدد")
        status_layout.addWidget(QLabel("المزامنة التالية:"), 2, 0)
        status_layout.addWidget(self.next_sync_label, 2, 1)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # أزرار المزامنة
        sync_buttons_layout = QHBoxLayout()
        
        sync_all_btn = QPushButton("🔄 مزامنة جميع الفروع")
        sync_all_btn.clicked.connect(self.sync_all_branches)
        sync_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 15px 30px;
                border-radius: 8px;
                font-weight: bold;
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        sync_buttons_layout.addWidget(sync_all_btn)
        
        sync_selected_btn = QPushButton("🔄 مزامنة الفرع المحدد")
        sync_selected_btn.clicked.connect(self.sync_selected_branch)
        sync_buttons_layout.addWidget(sync_selected_btn)
        
        layout.addLayout(sync_buttons_layout)
        
        # خيارات المزامنة
        options_group = QGroupBox("⚙️ خيارات المزامنة")
        options_layout = QFormLayout()
        
        self.sync_type_combo = QComboBox()
        self.sync_type_combo.addItems([
            "مزامنة كاملة",
            "مزامنة تدريجية",
            "مزامنة البيانات الجديدة فقط",
            "مزامنة البيانات المعدلة فقط"
        ])
        options_layout.addRow("نوع المزامنة:", self.sync_type_combo)
        
        self.tables_to_sync = QListWidget()
        self.tables_to_sync.setMaximumHeight(150)
        tables = [
            "المبيعات", "المشتريات", "المخزون", "الحسابات", 
            "العملاء", "الموردين", "الأصناف", "المستخدمين"
        ]
        for table in tables:
            item = QListWidgetItem(table)
            item.setCheckState(Qt.Checked)
            self.tables_to_sync.addItem(item)
        options_layout.addRow("الجداول للمزامنة:", self.tables_to_sync)
        
        self.conflict_resolution_combo = QComboBox()
        self.conflict_resolution_combo.addItems([
            "الأحدث يفوز",
            "الخادم الرئيسي يفوز",
            "الفرع يفوز",
            "سؤال المستخدم"
        ])
        options_layout.addRow("حل التعارضات:", self.conflict_resolution_combo)
        
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
        
        # تقدم المزامنة
        progress_group = QGroupBox("📊 تقدم المزامنة")
        progress_layout = QVBoxLayout()
        
        self.sync_progress = QProgressBar()
        progress_layout.addWidget(self.sync_progress)
        
        self.sync_details = QTextEdit()
        self.sync_details.setMaximumHeight(100)
        self.sync_details.setReadOnly(True)
        progress_layout.addWidget(self.sync_details)
        
        progress_group.setLayout(progress_layout)
        layout.addWidget(progress_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_log_tab(self):
        """إنشاء تبويب السجل"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # فلاتر السجل
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("من تاريخ:"))
        self.log_from_date = QDateTimeEdit()
        self.log_from_date.setDateTime(QDateTime.currentDateTime().addDays(-7))
        self.log_from_date.setCalendarPopup(True)
        filter_layout.addWidget(self.log_from_date)
        
        filter_layout.addWidget(QLabel("إلى تاريخ:"))
        self.log_to_date = QDateTimeEdit()
        self.log_to_date.setDateTime(QDateTime.currentDateTime())
        self.log_to_date.setCalendarPopup(True)
        filter_layout.addWidget(self.log_to_date)
        
        filter_layout.addWidget(QLabel("الفرع:"))
        self.log_branch_combo = QComboBox()
        self.log_branch_combo.addItem("جميع الفروع", None)
        filter_layout.addWidget(self.log_branch_combo)
        
        search_log_btn = QPushButton("🔍 بحث")
        search_log_btn.clicked.connect(self.search_sync_log)
        filter_layout.addWidget(search_log_btn)
        
        filter_layout.addStretch()
        layout.addLayout(filter_layout)
        
        # جدول السجل
        self.sync_log_table = QTableWidget()
        self.sync_log_table.setColumnCount(7)
        self.sync_log_table.setHorizontalHeaderLabels([
            "التاريخ والوقت", "الفرع", "نوع المزامنة", "الحالة", 
            "السجلات المزامنة", "المدة", "ملاحظات"
        ])
        
        header = self.sync_log_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.sync_log_table)
        
        # أزرار السجل
        log_buttons_layout = QHBoxLayout()
        
        export_log_btn = QPushButton("📤 تصدير السجل")
        export_log_btn.clicked.connect(self.export_sync_log)
        log_buttons_layout.addWidget(export_log_btn)
        
        clear_log_btn = QPushButton("🗑️ مسح السجل")
        clear_log_btn.clicked.connect(self.clear_sync_log)
        log_buttons_layout.addWidget(clear_log_btn)
        
        log_buttons_layout.addStretch()
        layout.addLayout(log_buttons_layout)
        
        widget.setLayout(layout)
        return widget
    
    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات عامة
        general_group = QGroupBox("الإعدادات العامة")
        general_layout = QFormLayout()
        
        self.auto_sync_enabled_check = QCheckBox("تفعيل المزامنة التلقائية")
        self.auto_sync_enabled_check.setChecked(True)
        general_layout.addRow("", self.auto_sync_enabled_check)
        
        self.default_sync_interval_spin = QSpinBox()
        self.default_sync_interval_spin.setRange(1, 1440)
        self.default_sync_interval_spin.setValue(60)
        self.default_sync_interval_spin.setSuffix(" دقيقة")
        general_layout.addRow("فترة المزامنة الافتراضية:", self.default_sync_interval_spin)
        
        self.max_concurrent_syncs_spin = QSpinBox()
        self.max_concurrent_syncs_spin.setRange(1, 10)
        self.max_concurrent_syncs_spin.setValue(3)
        general_layout.addRow("الحد الأقصى للمزامنة المتزامنة:", self.max_concurrent_syncs_spin)
        
        self.retry_attempts_spin = QSpinBox()
        self.retry_attempts_spin.setRange(1, 10)
        self.retry_attempts_spin.setValue(3)
        general_layout.addRow("عدد محاولات الإعادة:", self.retry_attempts_spin)
        
        general_group.setLayout(general_layout)
        layout.addWidget(general_group)
        
        # إعدادات الأمان
        security_group = QGroupBox("إعدادات الأمان")
        security_layout = QVBoxLayout()
        
        self.encrypt_data_check = QCheckBox("تشفير البيانات أثناء النقل")
        self.encrypt_data_check.setChecked(True)
        security_layout.addWidget(self.encrypt_data_check)
        
        self.verify_checksums_check = QCheckBox("التحقق من سلامة البيانات")
        self.verify_checksums_check.setChecked(True)
        security_layout.addWidget(self.verify_checksums_check)
        
        self.require_authentication_check = QCheckBox("طلب مصادقة للمزامنة")
        self.require_authentication_check.setChecked(True)
        security_layout.addWidget(self.require_authentication_check)
        
        security_group.setLayout(security_layout)
        layout.addWidget(security_group)
        
        # إعدادات الأداء
        performance_group = QGroupBox("إعدادات الأداء")
        performance_layout = QFormLayout()
        
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(100, 10000)
        self.batch_size_spin.setValue(1000)
        performance_layout.addRow("حجم الدفعة:", self.batch_size_spin)
        
        self.connection_timeout_spin = QSpinBox()
        self.connection_timeout_spin.setRange(5, 300)
        self.connection_timeout_spin.setValue(30)
        self.connection_timeout_spin.setSuffix(" ثانية")
        performance_layout.addRow("انتهاء مهلة الاتصال:", self.connection_timeout_spin)
        
        self.compression_level_spin = QSpinBox()
        self.compression_level_spin.setRange(0, 9)
        self.compression_level_spin.setValue(6)
        performance_layout.addRow("مستوى الضغط:", self.compression_level_spin)
        
        performance_group.setLayout(performance_layout)
        layout.addWidget(performance_group)
        
        # أزرار الحفظ
        save_layout = QHBoxLayout()
        save_layout.addStretch()
        
        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        save_settings_btn.clicked.connect(self.save_sync_settings)
        save_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_layout.addWidget(save_settings_btn)
        
        layout.addLayout(save_layout)
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget
    
    def setup_auto_sync(self):
        """إعداد المزامنة التلقائية"""
        self.sync_timer = QTimer()
        self.sync_timer.timeout.connect(self.auto_sync_check)
        self.sync_timer.start(60000)  # فحص كل دقيقة
    
    def load_branches(self):
        """تحميل قائمة الفروع"""
        try:
            # إنشاء الجدول إذا لم يكن موجوداً
            create_query = """
            CREATE TABLE IF NOT EXISTS branches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                code TEXT UNIQUE NOT NULL,
                location TEXT,
                manager TEXT,
                server_ip TEXT,
                server_port INTEGER,
                database_name TEXT,
                username TEXT,
                password TEXT,
                sync_interval INTEGER DEFAULT 60,
                auto_sync BOOLEAN DEFAULT 1,
                bidirectional BOOLEAN DEFAULT 1,
                compress_data BOOLEAN DEFAULT 1,
                is_active BOOLEAN DEFAULT 1,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_sync DATETIME
            )
            """
            self.db_manager.execute_update(create_query)
            
            # تحميل البيانات
            query = """
            SELECT id, name, code, location, manager, server_ip, 
                   is_active, last_sync, auto_sync
            FROM branches 
            ORDER BY name
            """
            
            branches = self.db_manager.execute_query(query)
            
            self.branches_table.setRowCount(len(branches))
            
            # تحديث قائمة الفروع في تبويب السجل
            self.log_branch_combo.clear()
            self.log_branch_combo.addItem("جميع الفروع", None)
            
            for row, branch in enumerate(branches):
                branch_id, name, code, location, manager, server_ip, is_active, last_sync, auto_sync = branch
                
                self.branches_table.setItem(row, 0, QTableWidgetItem(str(name)))
                self.branches_table.setItem(row, 1, QTableWidgetItem(str(code)))
                self.branches_table.setItem(row, 2, QTableWidgetItem(str(location or "")))
                self.branches_table.setItem(row, 3, QTableWidgetItem(str(manager or "")))
                self.branches_table.setItem(row, 4, QTableWidgetItem(str(server_ip or "")))
                
                # الحالة
                status = "نشط" if is_active else "غير نشط"
                status_item = QTableWidgetItem(status)
                if is_active:
                    status_item.setBackground(QColor("#d5f4e6"))
                else:
                    status_item.setBackground(QColor("#ffebee"))
                self.branches_table.setItem(row, 5, status_item)
                
                # آخر مزامنة
                last_sync_text = str(last_sync) if last_sync else "لم تتم مزامنة"
                self.branches_table.setItem(row, 6, QTableWidgetItem(last_sync_text))
                
                # المزامنة التلقائية
                auto_sync_text = "مفعل" if auto_sync else "معطل"
                auto_sync_item = QTableWidgetItem(auto_sync_text)
                if auto_sync:
                    auto_sync_item.setBackground(QColor("#d5f4e6"))
                else:
                    auto_sync_item.setBackground(QColor("#fff3cd"))
                self.branches_table.setItem(row, 7, auto_sync_item)
                
                # إضافة للقائمة المنسدلة
                self.log_branch_combo.addItem(name, branch_id)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الفروع:\n{str(e)}")
    
    def add_branch(self):
        """إضافة فرع جديد"""
        dialog = BranchConfigDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_branches()
    
    def edit_branch(self):
        """تعديل فرع"""
        current_row = self.branches_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فرع للتعديل")
            return
        
        # الحصول على بيانات الفرع (محاكاة)
        branch_data = {
            'id': 1,
            'name': self.branches_table.item(current_row, 0).text(),
            'code': self.branches_table.item(current_row, 1).text(),
            'location': self.branches_table.item(current_row, 2).text(),
            'manager': self.branches_table.item(current_row, 3).text(),
            'server_ip': self.branches_table.item(current_row, 4).text(),
            'server_port': 5432,
            'database_name': 'accounting_db',
            'username': 'admin',
            'sync_interval': 60,
            'auto_sync': True,
            'bidirectional': True,
            'compress_data': True
        }
        
        dialog = BranchConfigDialog(self.db_manager, branch_data, self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_branches()
    
    def delete_branch(self):
        """حذف فرع"""
        current_row = self.branches_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فرع للحذف")
            return
        
        branch_name = self.branches_table.item(current_row, 0).text()
        
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف الفرع '{branch_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم", "تم حذف الفرع بنجاح")
            self.load_branches()
    
    def test_branch_connection(self):
        """اختبار اتصال الفرع"""
        current_row = self.branches_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فرع لاختبار الاتصال")
            return
        
        branch_name = self.branches_table.item(current_row, 0).text()
        QMessageBox.information(self, "اختبار الاتصال", f"تم اختبار الاتصال مع الفرع '{branch_name}' بنجاح\n(محاكاة)")
    
    def sync_all_branches(self):
        """مزامنة جميع الفروع"""
        dialog = SyncProgressDialog(self)
        
        # محاكاة عملية المزامنة
        QTimer.singleShot(1000, lambda: dialog.update_progress(25, 50, "جاري مزامنة الفرع الأول..."))
        QTimer.singleShot(2000, lambda: dialog.update_progress(50, 75, "جاري مزامنة الفرع الثاني..."))
        QTimer.singleShot(3000, lambda: dialog.update_progress(75, 100, "جاري مزامنة الفرع الثالث..."))
        QTimer.singleShot(4000, lambda: dialog.update_progress(100, 100, "تمت المزامنة بنجاح"))
        QTimer.singleShot(5000, lambda: dialog.accept())
        
        dialog.exec_()
    
    def sync_selected_branch(self):
        """مزامنة الفرع المحدد"""
        current_row = self.branches_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار فرع للمزامنة")
            return
        
        branch_name = self.branches_table.item(current_row, 0).text()
        QMessageBox.information(self, "مزامنة الفرع", f"تمت مزامنة الفرع '{branch_name}' بنجاح\n(محاكاة)")
    
    def auto_sync_check(self):
        """فحص المزامنة التلقائية"""
        # فحص الفروع التي تحتاج مزامنة تلقائية
        pass
    
    def search_sync_log(self):
        """البحث في سجل المزامنة"""
        # محاكاة بيانات السجل
        log_entries = [
            ("2024-01-15 10:00:00", "الفرع الأول", "مزامنة كاملة", "نجح", "1,250", "5 دقائق", ""),
            ("2024-01-15 09:30:00", "الفرع الثاني", "مزامنة تدريجية", "نجح", "85", "2 دقيقة", ""),
            ("2024-01-15 09:00:00", "الفرع الثالث", "مزامنة كاملة", "فشل", "0", "0", "خطأ في الاتصال"),
        ]
        
        self.sync_log_table.setRowCount(len(log_entries))
        
        for row, entry in enumerate(log_entries):
            for col, data in enumerate(entry):
                item = QTableWidgetItem(str(data))
                
                # تلوين حسب الحالة
                if col == 3:  # عمود الحالة
                    if data == "نجح":
                        item.setBackground(QColor("#d5f4e6"))
                    else:
                        item.setBackground(QColor("#ffebee"))
                
                self.sync_log_table.setItem(row, col, item)
    
    def export_sync_log(self):
        """تصدير سجل المزامنة"""
        QMessageBox.information(self, "تصدير السجل", "تم تصدير سجل المزامنة بنجاح\n(محاكاة)")
    
    def clear_sync_log(self):
        """مسح سجل المزامنة"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح سجل المزامنة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم", "تم مسح سجل المزامنة بنجاح")
            self.sync_log_table.setRowCount(0)
    
    def save_sync_settings(self):
        """حفظ إعدادات المزامنة"""
        try:
            settings = {
                'auto_sync_enabled': self.auto_sync_enabled_check.isChecked(),
                'default_sync_interval': self.default_sync_interval_spin.value(),
                'max_concurrent_syncs': self.max_concurrent_syncs_spin.value(),
                'retry_attempts': self.retry_attempts_spin.value(),
                'encrypt_data': self.encrypt_data_check.isChecked(),
                'verify_checksums': self.verify_checksums_check.isChecked(),
                'require_authentication': self.require_authentication_check.isChecked(),
                'batch_size': self.batch_size_spin.value(),
                'connection_timeout': self.connection_timeout_spin.value(),
                'compression_level': self.compression_level_spin.value()
            }
            
            # حفظ الإعدادات في قاعدة البيانات أو ملف
            QMessageBox.information(self, "تم", "تم حفظ إعدادات المزامنة بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات:\n{str(e)}")

def show_data_synchronization(db_manager, parent=None):
    """عرض وحدة مزامنة البيانات"""
    widget = DataSynchronizationWidget(db_manager, parent)
    return widget
