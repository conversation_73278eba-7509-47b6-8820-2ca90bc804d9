#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوحدة النسخ الاحتياطي
Comprehensive test for backup module
"""

import sys
import os
import tempfile
import shutil

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_backup_module():
    """اختبار وحدة النسخ الاحتياطي"""
    print("🧪 اختبار وحدة النسخ الاحتياطي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.backup_module import BackupWidget, BackupScheduleDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار إنشاء وحدة النسخ الاحتياطي
        backup_widget = BackupWidget(db_manager)
        print("✅ تم إنشاء وحدة النسخ الاحتياطي")
        
        # اختبار تحميل تاريخ النسخ الاحتياطية
        backup_widget.load_backup_history()
        print("✅ تم تحميل تاريخ النسخ الاحتياطية")
        
        # اختبار عدد النسخ المحملة
        backup_count = backup_widget.backups_table.rowCount()
        print(f"📊 عدد النسخ الاحتياطية المحملة: {backup_count}")
        
        # اختبار البحث والفلترة
        backup_widget.search_edit.setText("onyx")
        backup_widget.filter_backups()
        print("✅ تم اختبار وظيفة البحث")
        
        # اختبار مسح البحث
        backup_widget.search_edit.clear()
        backup_widget.filter_backups()
        print("✅ تم اختبار مسح البحث")
        
        # تنظيف
        backup_widget.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وحدة النسخ الاحتياطي: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_schedule_dialog():
    """اختبار حوار جدولة النسخ الاحتياطي"""
    print("\n🧪 اختبار حوار جدولة النسخ الاحتياطي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt, QTime
        from src.modules.backup_module import BackupScheduleDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار حوار جدولة النسخ الاحتياطي
        dialog = BackupScheduleDialog(db_manager)
        print("✅ تم إنشاء حوار جدولة النسخ الاحتياطي")
        
        # اختبار تحميل الجدولة الحالية
        dialog.load_current_schedule()
        print("✅ تم تحميل الجدولة الحالية")
        
        # اختبار إعدادات الجدولة
        enabled = dialog.enable_schedule.isChecked()
        frequency = dialog.frequency_combo.currentText()
        backup_time = dialog.backup_time.time().toString('HH:mm')
        
        print(f"⏰ إعدادات الجدولة:")
        print(f"   مفعل: {'نعم' if enabled else 'لا'}")
        print(f"   التكرار: {frequency}")
        print(f"   الوقت: {backup_time}")
        
        # اختبار تعديل الإعدادات
        dialog.enable_schedule.setChecked(True)
        dialog.frequency_combo.setCurrentIndex(0)  # يومي
        dialog.backup_time.setTime(QTime(3, 0))  # 3:00 AM
        print("✅ تم تعديل إعدادات الجدولة")
        
        # تنظيف
        dialog.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حوار جدولة النسخ الاحتياطي: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_workers():
    """اختبار عمال النسخ الاحتياطي والاستعادة"""
    print("\n🧪 اختبار عمال النسخ الاحتياطي...")
    
    try:
        from src.modules.backup_module import BackupWorker, RestoreWorker
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # إنشاء مجلد مؤقت للاختبار
        temp_dir = tempfile.mkdtemp()
        print(f"📁 مجلد الاختبار المؤقت: {temp_dir}")
        
        # مسار قاعدة البيانات
        db_path = "onyx_erp.db"
        backup_path = os.path.join(temp_dir, "test_backup.zip")
        
        # اختبار إنشاء عامل النسخ الاحتياطي
        backup_worker = BackupWorker(db_path, backup_path, "full")
        print("✅ تم إنشاء عامل النسخ الاحتياطي")
        
        # اختبار إنشاء عامل الاستعادة
        restore_worker = RestoreWorker(backup_path, db_path, "full")
        print("✅ تم إنشاء عامل الاستعادة")
        
        # تنظيف المجلد المؤقت
        shutil.rmtree(temp_dir)
        print("✅ تم تنظيف الملفات المؤقتة")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمال النسخ الاحتياطي: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_database_operations():
    """اختبار عمليات قاعدة البيانات للنسخ الاحتياطي"""
    print("\n🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار استعلام إعدادات النسخ الاحتياطي
        backup_settings_query = """
            SELECT setting_key, setting_value FROM system_settings 
            WHERE category = 'backup'
            ORDER BY setting_key
        """
        backup_settings = db_manager.execute_query(backup_settings_query)
        print(f"💾 إعدادات النسخ الاحتياطي: {len(backup_settings)}")
        
        for setting in backup_settings:
            key = setting['setting_key']
            value = setting['setting_value']
            print(f"   {key}: {value}")
        
        # اختبار إدراج إعدادات النسخ الاحتياطي الافتراضية
        default_settings = [
            ('backup_enabled', '0', 'تفعيل النسخ الاحتياطي التلقائي'),
            ('backup_frequency', 'daily', 'تكرار النسخ الاحتياطي'),
            ('backup_time', '02:00', 'وقت النسخ الاحتياطي'),
            ('backup_type', 'full', 'نوع النسخة الاحتياطية'),
            ('backup_path', '', 'مجلد النسخ الاحتياطي'),
            ('backup_keep_count', '7', 'عدد النسخ المحفوظة'),
            ('backup_compress', '1', 'ضغط النسخ الاحتياطية'),
            ('backup_notify_success', '1', 'إشعار عند نجاح النسخ'),
            ('backup_notify_failure', '1', 'إشعار عند فشل النسخ')
        ]
        
        for key, value, description in default_settings:
            query = """
                INSERT OR IGNORE INTO system_settings 
                (setting_key, setting_value, setting_type, description, category, is_editable)
                VALUES (?, ?, 'string', ?, 'backup', 1)
            """
            db_manager.execute_update(query, (key, value, description))
        
        print("✅ تم إدراج الإعدادات الافتراضية للنسخ الاحتياطي")
        
        # اختبار استعلام الإعدادات المحدثة
        updated_settings = db_manager.execute_query(backup_settings_query)
        print(f"📊 إجمالي إعدادات النسخ الاحتياطي: {len(updated_settings)}")
        
        # اختبار استعلام حجم قاعدة البيانات
        db_size_query = "SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()"
        try:
            size_result = db_manager.execute_query(db_size_query)
            if size_result:
                db_size = size_result[0]['size']
                db_size_mb = db_size / (1024 * 1024)
                print(f"📊 حجم قاعدة البيانات: {db_size_mb:.2f} MB")
        except:
            print("📊 لا يمكن حساب حجم قاعدة البيانات")
        
        # اختبار عدد الجداول
        tables_query = "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'"
        tables_result = db_manager.execute_query(tables_query)
        tables_count = tables_result[0]['count'] if tables_result else 0
        print(f"📊 عدد الجداول في قاعدة البيانات: {tables_count}")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار شامل لوحدة النسخ الاحتياطي")
    print("   Comprehensive test for backup module")
    print("=" * 60)
    
    # اختبار وحدة النسخ الاحتياطي
    module_test = test_backup_module()
    
    # اختبار حوار جدولة النسخ الاحتياطي
    schedule_test = test_backup_schedule_dialog()
    
    # اختبار عمال النسخ الاحتياطي
    workers_test = test_backup_workers()
    
    # اختبار عمليات قاعدة البيانات
    database_test = test_backup_database_operations()
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار وحدة النسخ الاحتياطي:")
    print(f"   🏗️ اختبار الوحدة: {'✅ نجح' if module_test else '❌ فشل'}")
    print(f"   ⏰ اختبار الجدولة: {'✅ نجح' if schedule_test else '❌ فشل'}")
    print(f"   👷 اختبار العمال: {'✅ نجح' if workers_test else '❌ فشل'}")
    print(f"   🗄️ اختبار قاعدة البيانات: {'✅ نجح' if database_test else '❌ فشل'}")
    
    if module_test and schedule_test and workers_test and database_test:
        print("\n🎉 جميع اختبارات وحدة النسخ الاحتياطي نجحت!")
        print("✅ وحدة النسخ الاحتياطي جاهزة للاستخدام")
        
        print("\n📋 الوظائف المتاحة:")
        print("   💾 إنشاء نسخ احتياطية (كاملة، البيانات فقط، الهيكل فقط)")
        print("   🔄 استعادة النسخ الاحتياطية")
        print("   ⏰ جدولة النسخ الاحتياطي التلقائي")
        print("   📊 عرض تاريخ النسخ الاحتياطية")
        print("   🔍 البحث والفلترة في النسخ")
        print("   📈 مؤشرات التقدم للعمليات")
        print("   ❌ إلغاء العمليات الجارية")
        print("   🗜️ ضغط النسخ الاحتياطية")
        print("   🔔 إشعارات النجاح والفشل")
    else:
        print("\n⚠️ بعض اختبارات وحدة النسخ الاحتياطي فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)
    
    return 0 if (module_test and schedule_test and workers_test and database_test) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
