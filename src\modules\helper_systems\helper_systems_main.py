#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الأنظمة المساعدة الرئيسية
Main Helper Systems
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QMessageBox, 
                             QFrame, QSplitter, QTextEdit, QTabWidget, QWidget,
                             QTreeWidget, QTreeWidgetItem, QListWidget, QListWidgetItem,
                             QProgressBar, QSlider, QDateEdit, QRadioButton, QButtonGroup,
                             QApplication, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon, QPixmap

class HelperSystemsMainDialog(QDialog):
    """حوار الأنظمة المساعدة الرئيسية"""
    
    def __init__(self, db_manager=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🛠️ الأنظمة المساعدة - مجموعة شاملة من الأدوات المساعدة")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setMinimumSize(1000, 600)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("🛠️ الأنظمة المساعدة - مجموعة شاملة من الأدوات والأنظمة المساعدة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # منطقة التمرير للأنظمة
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #f8f9fa;
            }
        """)

        scroll_widget = QWidget()
        scroll_layout = QGridLayout()
        scroll_layout.setSpacing(15)
        scroll_layout.setContentsMargins(20, 20, 20, 20)
        
        # إنشاء بطاقات الأنظمة المساعدة
        systems = [
            {
                "title": "🧮 الآلة الحاسبة المتقدمة",
                "description": "آلة حاسبة علمية ومالية متقدمة",
                "icon": "🧮",
                "color": "#3498db",
                "action": self.open_calculator
            },
            {
                "title": "📅 التقويم والمواعيد",
                "description": "تقويم هجري وميلادي مع إدارة المواعيد",
                "icon": "📅",
                "color": "#e74c3c",
                "action": self.open_calendar
            },
            {
                "title": "📝 محرر النصوص",
                "description": "محرر نصوص متقدم مع دعم العربية",
                "icon": "📝",
                "color": "#27ae60",
                "action": self.open_text_editor
            },
            {
                "title": "🔍 البحث المتقدم",
                "description": "أداة بحث شاملة في جميع البيانات",
                "icon": "🔍",
                "color": "#f39c12",
                "action": self.open_advanced_search
            },
            {
                "title": "📊 مولد التقارير",
                "description": "إنشاء تقارير مخصصة ومتقدمة",
                "icon": "📊",
                "color": "#9b59b6",
                "action": self.open_report_generator
            },
            {
                "title": "🔧 أدوات النظام",
                "description": "أدوات صيانة وتحسين النظام",
                "icon": "🔧",
                "color": "#34495e",
                "action": self.open_system_tools
            },
            {
                "title": "💱 محول العملات",
                "description": "تحويل العملات مع أسعار محدثة",
                "icon": "💱",
                "color": "#16a085",
                "action": self.open_currency_converter
            },
            {
                "title": "📈 مراقب الأداء",
                "description": "مراقبة أداء النظام والإحصائيات",
                "icon": "📈",
                "color": "#e67e22",
                "action": self.open_performance_monitor
            },
            {
                "title": "🗂️ مدير الملفات",
                "description": "إدارة وتنظيم الملفات والمجلدات",
                "icon": "🗂️",
                "color": "#2ecc71",
                "action": self.open_file_manager
            },
            {
                "title": "🔐 مولد كلمات المرور",
                "description": "إنشاء كلمات مرور قوية وآمنة",
                "icon": "🔐",
                "color": "#c0392b",
                "action": self.open_password_generator
            },
            {
                "title": "📋 الحافظة المتقدمة",
                "description": "إدارة متقدمة للحافظة والنسخ",
                "icon": "📋",
                "color": "#8e44ad",
                "action": self.open_clipboard_manager
            },
            {
                "title": "🌐 مراقب الشبكة",
                "description": "مراقبة الشبكة واختبار الاتصال",
                "icon": "🌐",
                "color": "#3498db",
                "action": self.open_network_monitor
            },
            {
                "title": "🌐 متصفح الويب",
                "description": "متصفح ويب مدمج للمراجع",
                "icon": "🌐",
                "color": "#2980b9",
                "action": self.open_web_browser
            }
        ]
        
        # إنشاء البطاقات في شبكة مرنة
        row = 0
        col = 0
        max_cols = 3  # عدد الأعمدة القصوى

        for system in systems:
            card = self.create_system_card(system)
            scroll_layout.addWidget(card, row, col)

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        # إضافة مساحة مرنة في النهاية
        scroll_layout.setRowStretch(row + 1, 1)
        scroll_layout.setColumnStretch(max_cols, 1)
        
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(scroll_area)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_system_card(self, system):
        """إنشاء بطاقة نظام مساعد"""
        card = QGroupBox()
        card.setMinimumSize(350, 180)
        card.setMaximumSize(450, 220)
        card.setStyleSheet(f"""
            QGroupBox {{
                border: 2px solid {system['color']};
                border-radius: 10px;
                margin: 5px;
                padding: 10px;
                background-color: white;
            }}
            QGroupBox:hover {{
                background-color: #f8f9fa;
                border-width: 3px;
                transform: scale(1.02);
            }}
        """)
        
        layout = QVBoxLayout()
        
        # أيقونة وعنوان
        header_layout = QHBoxLayout()
        header_layout.setSpacing(10)

        icon_label = QLabel(system['icon'])
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 40px;
                margin: 5px;
                min-width: 50px;
                max-width: 50px;
                text-align: center;
            }
        """)
        icon_label.setAlignment(Qt.AlignCenter)

        title_label = QLabel(system['title'])
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                font-weight: bold;
                color: {system['color']};
                margin: 5px;
                text-align: right;
            }}
        """)
        title_label.setWordWrap(True)
        title_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label, 1)
        header_layout.addStretch()
        
        # الوصف
        desc_label = QLabel(system['description'])
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #7f8c8d;
                margin: 5px;
                text-align: right;
                line-height: 1.4;
            }
        """)
        desc_label.setWordWrap(True)
        desc_label.setAlignment(Qt.AlignRight | Qt.AlignTop)
        desc_label.setMinimumHeight(40)

        # زر التشغيل
        launch_btn = QPushButton("🚀 تشغيل")
        launch_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {system['color']};
                color: white;
                padding: 8px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
                margin: 5px;
                min-height: 35px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(system['color'])};
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            }}
            QPushButton:pressed {{
                transform: translateY(0px);
                box-shadow: 0 1px 2px rgba(0,0,0,0.2);
            }}
        """)
        launch_btn.clicked.connect(system['action'])
        launch_btn.setCursor(Qt.PointingHandCursor)
        
        # تجميع العناصر
        layout.setSpacing(8)
        layout.setContentsMargins(10, 10, 10, 10)

        layout.addLayout(header_layout)
        layout.addWidget(desc_label)
        layout.addStretch()
        layout.addWidget(launch_btn)

        card.setLayout(layout)
        return card
        
    def darken_color(self, color):
        """تغميق اللون للتأثير عند التمرير"""
        color_map = {
            "#3498db": "#2980b9",
            "#e74c3c": "#c0392b",
            "#27ae60": "#229954",
            "#f39c12": "#e67e22",
            "#9b59b6": "#8e44ad",
            "#34495e": "#2c3e50",
            "#16a085": "#138d75",
            "#e67e22": "#d35400",
            "#2ecc71": "#27ae60",
            "#c0392b": "#a93226",
            "#8e44ad": "#7d3c98",
            "#2980b9": "#2471a3"
        }
        return color_map.get(color, color)
        
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر المساعدة
        help_btn = QPushButton("❓ المساعدة")
        help_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        help_btn.clicked.connect(self.show_help)
        
        # زر الإعدادات
        settings_btn = QPushButton("⚙️ الإعدادات")
        settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        settings_btn.clicked.connect(self.open_settings)
        
        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        close_btn.clicked.connect(self.reject)
        
        layout.addWidget(help_btn)
        layout.addWidget(settings_btn)
        layout.addStretch()
        layout.addWidget(close_btn)
        
        return layout

    # وظائف الأنظمة المساعدة
    def open_calculator(self):
        """فتح الآلة الحاسبة المتقدمة"""
        try:
            from .calculator_system import CalculatorDialog
            dialog = CalculatorDialog(parent=self)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "قيد التطوير", "🧮 الآلة الحاسبة المتقدمة قيد التطوير")

    def open_calendar(self):
        """فتح التقويم والمواعيد"""
        try:
            from .calendar_system import CalendarDialog
            dialog = CalendarDialog(parent=self)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "مطور جزئياً", "📅 التقويم والمواعيد - مطور جزئياً\nالميزات المتاحة: التقويم، المواعيد، الأحداث، التذكيرات")

    def open_text_editor(self):
        """فتح محرر النصوص"""
        try:
            from .text_editor_system import TextEditorDialog
            dialog = TextEditorDialog(parent=self)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "مطور جزئياً", "📝 محرر النصوص - مطور جزئياً\nالميزات المتاحة: تحرير النصوص، التنسيق، حفظ الملفات")

    def open_advanced_search(self):
        """فتح البحث المتقدم"""
        try:
            from .search_system import AdvancedSearchDialog
            dialog = AdvancedSearchDialog(parent=self)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "مطور جزئياً", "🔍 البحث المتقدم - مطور جزئياً\nالميزات المتاحة: بحث شامل، فلاتر متقدمة، تصدير النتائج")

    def open_report_generator(self):
        """فتح مولد التقارير"""
        try:
            from .reports_generator_system import ReportGeneratorDialog
            dialog = ReportGeneratorDialog(parent=self)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "مطور جزئياً", "📊 مولد التقارير - مطور جزئياً\nالميزات المتاحة: إنشاء تقارير، قوالب جاهزة، تصدير PDF/Excel")

    def open_system_tools(self):
        """فتح أدوات النظام"""
        try:
            from .system_tools_system import SystemToolsDialog
            dialog = SystemToolsDialog(parent=self)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "مطور بالكامل", "🔧 أدوات النظام - مطور بالكامل\nالميزات المتاحة: مراقبة النظام، إدارة العمليات، أدوات الصيانة")

    def open_currency_converter(self):
        """فتح محول العملات"""
        try:
            from .currency_converter_system import CurrencyConverterDialog
            dialog = CurrencyConverterDialog(parent=self)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "مطور بالكامل", "💱 محول العملات - مطور بالكامل\nالميزات المتاحة: تحويل 16 عملة، أسعار محدثة، تاريخ التحويلات")

    def open_performance_monitor(self):
        """فتح مراقب الأداء"""
        try:
            from .performance_monitor_system import PerformanceMonitorDialog
            dialog = PerformanceMonitorDialog(parent=self)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "مطور بالكامل", "📈 مراقب الأداء - مطور بالكامل\nالميزات المتاحة: مراقبة مباشرة، رسوم بيانية، تنبيهات، تقارير")

    def open_file_manager(self):
        """فتح مدير الملفات"""
        try:
            from .file_manager_system import FileManagerDialog
            dialog = FileManagerDialog(parent=self)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "قيد التطوير", "🗂️ مدير الملفات قيد التطوير")

    def open_password_generator(self):
        """فتح مولد كلمات المرور"""
        try:
            from .password_generator_system import PasswordGeneratorDialog
            dialog = PasswordGeneratorDialog(parent=self)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "قيد التطوير", "🔐 مولد كلمات المرور قيد التطوير")

    def open_clipboard_manager(self):
        """فتح مدير الحافظة"""
        try:
            from .clipboard_manager_system import ClipboardManagerDialog
            dialog = ClipboardManagerDialog(parent=self)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "قيد التطوير", "📋 مدير الحافظة قيد التطوير")

    def open_network_monitor(self):
        """فتح مراقب الشبكة"""
        try:
            from .network_monitor_system import NetworkMonitorDialog
            dialog = NetworkMonitorDialog(parent=self)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "مطور بالكامل", "🌐 مراقب الشبكة - مطور بالكامل\nالميزات المتاحة: اختبار الاتصال، فحص المنافذ، مراقبة الشبكة، معلومات الشبكة")

    def open_web_browser(self):
        """فتح متصفح الويب"""
        try:
            from .web_browser_system import WebBrowserDialog
            dialog = WebBrowserDialog(parent=self)
            dialog.exec_()
        except ImportError:
            QMessageBox.information(self, "قيد التطوير", "🌐 متصفح الويب قيد التطوير")

    def open_settings(self):
        """فتح إعدادات الأنظمة المساعدة"""
        QMessageBox.information(self, "قيد التطوير", "⚙️ إعدادات الأنظمة المساعدة قيد التطوير")

    def show_help(self):
        """عرض المساعدة"""
        help_text = """
🛠️ مساعدة الأنظمة المساعدة

📋 الأنظمة المتاحة:

🧮 الآلة الحاسبة المتقدمة:
• حاسبة علمية ومالية متقدمة
• عمليات حسابية معقدة
• حفظ واسترجاع العمليات
• تحويل الوحدات

📅 التقويم والمواعيد:
• تقويم هجري وميلادي
• إدارة المواعيد والأحداث
• تذكيرات ومنبهات
• تصدير واستيراد المواعيد

📝 محرر النصوص:
• محرر نصوص متقدم
• دعم كامل للغة العربية
• تنسيق النصوص والخطوط
• حفظ بصيغ متعددة

🔍 البحث المتقدم:
• بحث شامل في جميع البيانات
• فلاتر متقدمة
• حفظ نتائج البحث
• تصدير النتائج

📊 مولد التقارير:
• إنشاء تقارير مخصصة
• قوالب جاهزة
• رسوم بيانية متقدمة
• تصدير بصيغ متعددة

🔧 أدوات النظام:
• صيانة وتحسين النظام
• تنظيف الملفات المؤقتة
• فحص الأخطاء
• نسخ احتياطية

💱 محول العملات:
• تحويل العملات المختلفة
• أسعار محدثة
• حفظ التحويلات المفضلة
• رسوم بيانية للأسعار

📈 مراقب الأداء:
• مراقبة أداء النظام
• إحصائيات الاستخدام
• تقارير الأداء
• تحسينات مقترحة

🗂️ مدير الملفات:
• إدارة الملفات والمجلدات
• نسخ ونقل وحذف
• ضغط وفك الضغط
• بحث في الملفات

🔐 مولد كلمات المرور:
• إنشاء كلمات مرور قوية
• معايير أمان متقدمة
• حفظ آمن للكلمات
• اختبار قوة كلمة المرور

📋 مدير الحافظة:
• إدارة متقدمة للحافظة
• حفظ تاريخ النسخ
• تنظيم المحتوى
• بحث في المحفوظات

🌐 متصفح الويب:
• متصفح ويب مدمج
• حفظ المفضلة
• تاريخ التصفح
• أدوات المطورين

💡 نصائح الاستخدام:
• استخدم الأنظمة المساعدة لتحسين الإنتاجية
• احفظ إعداداتك المفضلة
• استفد من الاختصارات
• راجع التحديثات بانتظام

🔧 الدعم الفني:
• جميع الأنظمة قابلة للتخصيص
• دعم كامل للغة العربية
• تحديثات مستمرة
• دعم فني متاح
"""

        QMessageBox.information(self, "المساعدة", help_text)


def main():
    """اختبار الأنظمة المساعدة"""
    import sys

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass

    dialog = HelperSystemsMainDialog(MockDBManager())
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
