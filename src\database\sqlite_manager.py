#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات SQLite (للتجربة بدون MySQL)
SQLite Database Manager (for testing without MySQL)
"""

import sqlite3
import bcrypt
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import json
import os

class SQLiteManager:
    """فئة إدارة قاعدة البيانات SQLite"""
    
    def __init__(self, db_path: str = "accounting_system.db"):
        self.db_path = db_path
        self.connection = None
        self.cursor = None
        self.connect()
        self.create_tables()
        self.create_default_data()
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            self.cursor = self.connection.cursor()
            print("تم الاتصال بقاعدة البيانات SQLite بنجاح")
            
        except sqlite3.Error as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            raise
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        try:
            if self.cursor:
                self.cursor.close()
                self.cursor = None
        except:
            pass
        
        try:
            if self.connection:
                self.connection.close()
                self.connection = None
        except:
            pass
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المستخدمين
        users_table = """
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            full_name TEXT NOT NULL,
            email TEXT,
            role TEXT DEFAULT 'user' CHECK(role IN ('admin', 'accountant', 'user')),
            is_active BOOLEAN DEFAULT 1,
            last_login TIMESTAMP,
            failed_attempts INTEGER DEFAULT 0,
            locked_until TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول دليل الحسابات
        chart_of_accounts_table = """
        CREATE TABLE IF NOT EXISTS chart_of_accounts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            account_code TEXT UNIQUE NOT NULL,
            account_name_ar TEXT NOT NULL,
            account_name_en TEXT NOT NULL,
            account_type TEXT NOT NULL CHECK(account_type IN ('asset', 'liability', 'equity', 'revenue', 'expense')),
            parent_id INTEGER,
            level INTEGER DEFAULT 1,
            is_active BOOLEAN DEFAULT 1,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES chart_of_accounts(id)
        )
        """
        
        # جدول القيود اليومية
        journal_entries_table = """
        CREATE TABLE IF NOT EXISTS journal_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            entry_date DATE NOT NULL,
            description TEXT,
            total_amount DECIMAL(15,2) DEFAULT 0,
            is_posted BOOLEAN DEFAULT 0,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users(id)
        )
        """
        
        # جدول تفاصيل القيود اليومية
        journal_entry_details_table = """
        CREATE TABLE IF NOT EXISTS journal_entry_details (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            journal_entry_id INTEGER NOT NULL,
            account_code TEXT NOT NULL,
            debit_amount DECIMAL(15,2) DEFAULT 0,
            credit_amount DECIMAL(15,2) DEFAULT 0,
            description TEXT,
            FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
            FOREIGN KEY (account_code) REFERENCES chart_of_accounts(account_code)
        )
        """
        
        # جدول العملاء والموردين المحدث
        customers_suppliers_table = """
        CREATE TABLE IF NOT EXISTS customers_suppliers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            name_ar TEXT NOT NULL,
            name_en TEXT,
            type TEXT NOT NULL DEFAULT 'عميل',
            category TEXT DEFAULT 'عادي',
            tax_number TEXT,
            commercial_register TEXT,
            is_active BOOLEAN DEFAULT 1,
            
            -- معلومات الاتصال
            address TEXT,
            city TEXT,
            country TEXT DEFAULT 'السعودية',
            postal_code TEXT,
            phone1 TEXT,
            phone2 TEXT,
            email TEXT,
            website TEXT,
            contact_person TEXT,
            
            -- المعلومات المالية
            credit_limit DECIMAL(15,2) DEFAULT 0,
            payment_terms INTEGER DEFAULT 30,
            payment_method TEXT DEFAULT 'نقداً',
            currency TEXT DEFAULT 'ريال سعودي',
            discount_rate DECIMAL(5,2) DEFAULT 0,
            
            -- معلومات البنك
            bank_name TEXT,
            bank_account TEXT,
            iban TEXT,
            
            -- ملاحظات
            notes TEXT,
            
            -- تواريخ النظام
            created_date DATE DEFAULT CURRENT_DATE,
            updated_date DATE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول المنتجات والخدمات
        products_services_table = """
        CREATE TABLE IF NOT EXISTS products_services (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE NOT NULL,
            name_ar TEXT NOT NULL,
            name_en TEXT,
            type TEXT NOT NULL CHECK(type IN ('product', 'service')),
            category_ar TEXT,
            category_en TEXT,
            unit_ar TEXT,
            unit_en TEXT,
            purchase_price DECIMAL(15,2) DEFAULT 0,
            selling_price DECIMAL(15,2) NOT NULL,
            current_stock DECIMAL(15,3) DEFAULT 0,
            minimum_stock DECIMAL(15,3) DEFAULT 0,
            tax_rate DECIMAL(5,2) DEFAULT 0,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        # جدول الإعدادات
        settings_table = """
        CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            setting_key TEXT UNIQUE NOT NULL,
            setting_value TEXT,
            description_ar TEXT,
            description_en TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول العملاء المحدث للمبيعات
        customers_sales_table = """
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_code TEXT UNIQUE NOT NULL,
            customer_name TEXT NOT NULL,
            customer_type TEXT DEFAULT 'individual' CHECK(customer_type IN ('individual', 'company')),
            contact_person TEXT,
            phone TEXT,
            mobile TEXT,
            email TEXT,
            address TEXT,
            city TEXT,
            country TEXT DEFAULT 'Saudi Arabia',
            tax_number TEXT,
            credit_limit DECIMAL(15,2) DEFAULT 0,
            payment_terms INTEGER DEFAULT 30,
            is_active BOOLEAN DEFAULT 1,
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول المنتجات المحدث للمبيعات
        products_sales_table = """
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_code TEXT UNIQUE NOT NULL,
            product_name TEXT NOT NULL,
            product_type TEXT DEFAULT 'product' CHECK(product_type IN ('product', 'service')),
            category TEXT,
            unit TEXT DEFAULT 'piece',
            cost_price DECIMAL(15,2) DEFAULT 0,
            selling_price DECIMAL(15,2) NOT NULL,
            tax_rate DECIMAL(5,2) DEFAULT 15.00,
            stock_quantity INTEGER DEFAULT 0,
            min_stock_level INTEGER DEFAULT 0,
            max_stock_level INTEGER DEFAULT 0,
            barcode TEXT,
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """

        # جدول فواتير المبيعات
        sales_invoices_table = """
        CREATE TABLE IF NOT EXISTS sales_invoices (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_number TEXT UNIQUE NOT NULL,
            customer_id INTEGER NOT NULL,
            invoice_date DATE NOT NULL,
            due_date DATE,
            subtotal DECIMAL(15,2) DEFAULT 0,
            discount_amount DECIMAL(15,2) DEFAULT 0,
            discount_percentage DECIMAL(5,2) DEFAULT 0,
            tax_amount DECIMAL(15,2) DEFAULT 0,
            total_amount DECIMAL(15,2) DEFAULT 0,
            paid_amount DECIMAL(15,2) DEFAULT 0,
            remaining_amount DECIMAL(15,2) DEFAULT 0,
            status TEXT DEFAULT 'draft' CHECK(status IN ('draft', 'sent', 'paid', 'partial', 'overdue', 'cancelled')),
            payment_method TEXT,
            notes TEXT,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        """

        # جدول تفاصيل فواتير المبيعات
        sales_invoice_items_table = """
        CREATE TABLE IF NOT EXISTS sales_invoice_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            invoice_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity DECIMAL(10,3) NOT NULL,
            unit_price DECIMAL(15,2) NOT NULL,
            discount_amount DECIMAL(15,2) DEFAULT 0,
            tax_rate DECIMAL(5,2) DEFAULT 15.00,
            tax_amount DECIMAL(15,2) DEFAULT 0,
            line_total DECIMAL(15,2) NOT NULL,
            notes TEXT,
            FOREIGN KEY (invoice_id) REFERENCES sales_invoices (id) ON DELETE CASCADE,
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
        """

        # جدول المدفوعات
        payments_table = """
        CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            payment_number TEXT UNIQUE NOT NULL,
            invoice_id INTEGER NOT NULL,
            payment_date DATE NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            payment_method TEXT NOT NULL CHECK(payment_method IN ('cash', 'bank_transfer', 'check', 'credit_card', 'other')),
            reference_number TEXT,
            bank_name TEXT,
            notes TEXT,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES sales_invoices (id),
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
        """

        tables = [
            users_table,
            chart_of_accounts_table,
            journal_entries_table,
            journal_entry_details_table,
            customers_suppliers_table,
            products_services_table,
            settings_table,
            customers_sales_table,
            products_sales_table,
            sales_invoices_table,
            sales_invoice_items_table,
            payments_table
        ]
        
        try:
            for table in tables:
                self.cursor.execute(table)
            
            self.connection.commit()
            print("تم إنشاء جداول قاعدة البيانات بنجاح")

            # تحديث قاعدة البيانات الموجودة
            self.update_existing_database()

        except sqlite3.Error as e:
            print(f"خطأ في إنشاء الجداول: {e}")
            self.connection.rollback()
    
    def create_default_data(self):
        """إنشاء البيانات الافتراضية"""
        
        # إنشاء المستخدم الافتراضي
        self.create_default_user()
        
        # إنشاء دليل الحسابات الافتراضي
        self.create_default_chart_of_accounts()
        
        # إنشاء الإعدادات الافتراضية
        self.create_default_settings()
    
    def create_default_user(self):
        """إنشاء المستخدم الافتراضي"""
        try:
            # التحقق من وجود مستخدمين
            self.cursor.execute("SELECT COUNT(*) as count FROM users")
            result = self.cursor.fetchone()
            
            if result['count'] == 0:
                # إنشاء كلمة مرور مشفرة
                password = "admin123"
                password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
                
                # إدراج المستخدم الافتراضي
                query = """
                INSERT INTO users (username, password_hash, full_name, email, role)
                VALUES (?, ?, ?, ?, ?)
                """
                values = ("admin", password_hash, "مدير النظام", "<EMAIL>", "admin")
                
                self.cursor.execute(query, values)
                self.connection.commit()
                print("تم إنشاء المستخدم الافتراضي: admin / admin123")
                
        except sqlite3.Error as e:
            print(f"خطأ في إنشاء المستخدم الافتراضي: {e}")
    
    def create_default_chart_of_accounts(self):
        """إنشاء دليل الحسابات الافتراضي"""
        try:
            # التحقق من وجود حسابات
            self.cursor.execute("SELECT COUNT(*) as count FROM chart_of_accounts")
            result = self.cursor.fetchone()
            
            if result['count'] == 0:
                accounts = [
                    # الأصول
                    ("1000", "الأصول", "Assets", "asset", None, 1),
                    ("1100", "الأصول المتداولة", "Current Assets", "asset", None, 2),
                    ("1110", "النقدية", "Cash", "asset", None, 3),
                    ("1120", "البنوك", "Banks", "asset", None, 3),
                    ("1130", "العملاء", "Accounts Receivable", "asset", None, 3),
                    ("1140", "المخزون", "Inventory", "asset", None, 3),
                    ("1200", "الأصول الثابتة", "Fixed Assets", "asset", None, 2),
                    ("1210", "الأراضي والمباني", "Land & Buildings", "asset", None, 3),
                    ("1220", "المعدات", "Equipment", "asset", None, 3),
                    
                    # الخصوم
                    ("2000", "الخصوم", "Liabilities", "liability", None, 1),
                    ("2100", "الخصوم المتداولة", "Current Liabilities", "liability", None, 2),
                    ("2110", "الموردون", "Accounts Payable", "liability", None, 3),
                    ("2120", "الضرائب المستحقة", "Taxes Payable", "liability", None, 3),
                    ("2200", "الخصوم طويلة الأجل", "Long-term Liabilities", "liability", None, 2),
                    ("2210", "القروض طويلة الأجل", "Long-term Loans", "liability", None, 3),
                    
                    # حقوق الملكية
                    ("3000", "حقوق الملكية", "Equity", "equity", None, 1),
                    ("3100", "رأس المال", "Capital", "equity", None, 2),
                    ("3200", "الأرباح المحتجزة", "Retained Earnings", "equity", None, 2),
                    
                    # الإيرادات
                    ("4000", "الإيرادات", "Revenue", "revenue", None, 1),
                    ("4100", "إيرادات المبيعات", "Sales Revenue", "revenue", None, 2),
                    ("4200", "إيرادات أخرى", "Other Revenue", "revenue", None, 2),
                    
                    # المصروفات
                    ("5000", "المصروفات", "Expenses", "expense", None, 1),
                    ("5100", "تكلفة البضاعة المباعة", "Cost of Goods Sold", "expense", None, 2),
                    ("5200", "مصروفات التشغيل", "Operating Expenses", "expense", None, 2),
                    ("5210", "الرواتب", "Salaries", "expense", None, 3),
                    ("5220", "الإيجار", "Rent", "expense", None, 3),
                    ("5230", "الكهرباء والماء", "Utilities", "expense", None, 3),
                ]
                
                query = """
                INSERT INTO chart_of_accounts (account_code, account_name_ar, account_name_en, account_type, parent_id, level)
                VALUES (?, ?, ?, ?, ?, ?)
                """
                
                # إدراج الحسابات
                for account in accounts:
                    values = (account[0], account[1], account[2], account[3], account[4], account[5])
                    self.cursor.execute(query, values)
                
                self.connection.commit()
                print("تم إنشاء دليل الحسابات الافتراضي")
                
        except sqlite3.Error as e:
            print(f"خطأ في إنشاء دليل الحسابات: {e}")
            self.connection.rollback()
    
    def create_default_settings(self):
        """إنشاء الإعدادات الافتراضية"""
        try:
            # التحقق من وجود إعدادات
            self.cursor.execute("SELECT COUNT(*) as count FROM settings")
            result = self.cursor.fetchone()
            
            if result['count'] == 0:
                settings = [
                    ("company_name_ar", "شركة المحاسبة", "اسم الشركة بالعربية", "Company name in Arabic"),
                    ("company_name_en", "Accounting Company", "اسم الشركة بالإنجليزية", "Company name in English"),
                    ("currency", "SAR", "العملة الافتراضية", "Default currency"),
                    ("currency_symbol", "ر.س", "رمز العملة", "Currency symbol"),
                    ("tax_rate", "15.0", "معدل الضريبة الافتراضي", "Default tax rate"),
                    ("decimal_places", "2", "عدد الخانات العشرية", "Number of decimal places"),
                    ("fiscal_year_start", "01-01", "بداية السنة المالية", "Fiscal year start"),
                    ("language", "ar", "اللغة الافتراضية", "Default language"),
                ]
                
                query = """
                INSERT INTO settings (setting_key, setting_value, description_ar, description_en)
                VALUES (?, ?, ?, ?)
                """
                
                for setting in settings:
                    self.cursor.execute(query, setting)
                
                self.connection.commit()
                print("تم إنشاء الإعدادات الافتراضية")
                
        except sqlite3.Error as e:
            print(f"خطأ في إنشاء الإعدادات: {e}")
            self.connection.rollback()
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """التحقق من صحة بيانات المستخدم"""
        try:
            query = "SELECT * FROM users WHERE username = ? AND is_active = 1"
            self.cursor.execute(query, (username,))
            user = self.cursor.fetchone()
            
            if user and bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
                # تحويل النتيجة إلى قاموس وإزالة كلمة المرور
                user_dict = dict(user)
                user_dict.pop('password_hash', None)
                return user_dict
            
            return None
            
        except sqlite3.Error as e:
            print(f"خطأ في التحقق من المستخدم: {e}")
            return None
    
    def execute_query(self, query: str, params: Tuple = None) -> List[Dict]:
        """تنفيذ استعلام وإرجاع النتائج"""
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            results = self.cursor.fetchall()
            return [dict(row) for row in results]
            
        except sqlite3.Error as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return []
    
    def execute_update(self, query: str, params: Tuple = None) -> bool:
        """تنفيذ استعلام تحديث"""
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)
            
            self.connection.commit()
            return True
            
        except sqlite3.Error as e:
            print(f"خطأ في تنفيذ التحديث: {e}")
            self.connection.rollback()
            return False
    
    def get_last_insert_id(self) -> int:
        """الحصول على آخر ID تم إدراجه"""
        return self.cursor.lastrowid

    def execute_insert(self, query: str, params: Tuple = None) -> int:
        """تنفيذ استعلام إدراج وإرجاع معرف السجل الجديد"""
        try:
            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)

            self.connection.commit()
            return self.cursor.lastrowid

        except sqlite3.Error as e:
            print(f"خطأ في تنفيذ الإدراج: {e}")
            self.connection.rollback()
            return None

    def update_existing_database(self):
        """تحديث قاعدة البيانات الموجودة لإضافة الأعمدة المفقودة"""
        try:
            # التحقق من وجود عمود notes في جدول chart_of_accounts
            cursor = self.cursor.execute("PRAGMA table_info(chart_of_accounts)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'notes' not in columns:
                self.cursor.execute("ALTER TABLE chart_of_accounts ADD COLUMN notes TEXT")
                print("تم إضافة عمود notes إلى جدول chart_of_accounts")

            # التحقق من وجود عمود description في جدول journal_entries
            cursor = self.cursor.execute("PRAGMA table_info(journal_entries)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'description' not in columns:
                self.cursor.execute("ALTER TABLE journal_entries ADD COLUMN description TEXT")
                print("تم إضافة عمود description إلى جدول journal_entries")

            # التحقق من وجود عمود entity_type في جدول customers_suppliers
            cursor = self.cursor.execute("PRAGMA table_info(customers_suppliers)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'entity_type' not in columns:
                self.cursor.execute("ALTER TABLE customers_suppliers ADD COLUMN entity_type TEXT DEFAULT 'both'")
                print("تم إضافة عمود entity_type إلى جدول customers_suppliers")

            # التحقق من وجود عمود total_amount في جدول journal_entries
            cursor = self.cursor.execute("PRAGMA table_info(journal_entries)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'total_amount' not in columns:
                self.cursor.execute("ALTER TABLE journal_entries ADD COLUMN total_amount DECIMAL(15,2) DEFAULT 0")
                print("تم إضافة عمود total_amount إلى جدول journal_entries")

            if 'is_posted' not in columns:
                self.cursor.execute("ALTER TABLE journal_entries ADD COLUMN is_posted BOOLEAN DEFAULT 0")
                print("تم إضافة عمود is_posted إلى جدول journal_entries")

            # التحقق من وجود الأعمدة في جدول journal_entry_details
            cursor = self.cursor.execute("PRAGMA table_info(journal_entry_details)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'account_code' not in columns:
                self.cursor.execute("ALTER TABLE journal_entry_details ADD COLUMN account_code TEXT")
                print("تم إضافة عمود account_code إلى جدول journal_entry_details")

            if 'description' not in columns:
                self.cursor.execute("ALTER TABLE journal_entry_details ADD COLUMN description TEXT")
                print("تم إضافة عمود description إلى جدول journal_entry_details")

            if 'debit_amount' not in columns:
                self.cursor.execute("ALTER TABLE journal_entry_details ADD COLUMN debit_amount DECIMAL(15,2) DEFAULT 0")
                print("تم إضافة عمود debit_amount إلى جدول journal_entry_details")

            if 'credit_amount' not in columns:
                self.cursor.execute("ALTER TABLE journal_entry_details ADD COLUMN credit_amount DECIMAL(15,2) DEFAULT 0")
                print("تم إضافة عمود credit_amount إلى جدول journal_entry_details")

            # التحقق من وجود عمود city في جدول customers_suppliers
            cursor = self.cursor.execute("PRAGMA table_info(customers_suppliers)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'city' not in columns:
                self.cursor.execute("ALTER TABLE customers_suppliers ADD COLUMN city TEXT")
                print("تم إضافة عمود city إلى جدول customers_suppliers")

            # التحقق من وجود الأعمدة الجديدة في جدول المستخدمين وإضافتها
            users_columns_query = "PRAGMA table_info(users)"
            users_columns_info = self.execute_query(users_columns_query)
            users_columns = [col['name'] for col in users_columns_info]

            if 'last_login' not in users_columns:
                self.cursor.execute("ALTER TABLE users ADD COLUMN last_login TIMESTAMP")
                print("تم إضافة عمود last_login إلى جدول users")

            if 'failed_attempts' not in users_columns:
                self.cursor.execute("ALTER TABLE users ADD COLUMN failed_attempts INTEGER DEFAULT 0")
                print("تم إضافة عمود failed_attempts إلى جدول users")

            if 'locked_until' not in users_columns:
                self.cursor.execute("ALTER TABLE users ADD COLUMN locked_until TIMESTAMP")
                print("تم إضافة عمود locked_until إلى جدول users")

            # إنشاء جدول إعدادات النظام
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key TEXT UNIQUE NOT NULL,
                    setting_value TEXT,
                    setting_type TEXT DEFAULT 'string',
                    description TEXT,
                    category TEXT DEFAULT 'general',
                    is_editable BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # إدراج الإعدادات الافتراضية
            default_settings = [
                ('company_name', 'صقالات أبو سيف للنقل', 'string', 'اسم الشركة', 'company', 1),
                ('company_name_en', 'Abu Saif Transport Company', 'string', 'اسم الشركة بالإنجليزية', 'company', 1),
                ('company_address', 'المملكة العربية السعودية', 'string', 'عنوان الشركة', 'company', 1),
                ('company_phone', '+966-XX-XXXXXXX', 'string', 'هاتف الشركة', 'company', 1),
                ('company_email', '<EMAIL>', 'string', 'بريد الشركة الإلكتروني', 'company', 1),
                ('tax_number', '*********012345', 'string', 'الرقم الضريبي', 'company', 1),
                ('commercial_register', 'CR-*********', 'string', 'السجل التجاري', 'company', 1),
                ('fiscal_year_start', '01-01', 'string', 'بداية السنة المالية (شهر-يوم)', 'accounting', 1),
                ('default_currency', 'SAR', 'string', 'العملة الافتراضية', 'accounting', 1),
                ('decimal_places', '2', 'integer', 'عدد المنازل العشرية', 'accounting', 1),
                ('backup_frequency', 'daily', 'string', 'تكرار النسخ الاحتياطي', 'system', 1),
                ('session_timeout', '30', 'integer', 'انتهاء الجلسة (دقيقة)', 'security', 1),
                ('password_min_length', '6', 'integer', 'الحد الأدنى لطول كلمة المرور', 'security', 1),
                ('enable_audit_log', '1', 'boolean', 'تفعيل سجل المراجعة', 'security', 1),
                ('theme', 'default', 'string', 'المظهر', 'ui', 1),
                ('language', 'ar', 'string', 'اللغة', 'ui', 1),
                ('date_format', 'yyyy-MM-dd', 'string', 'تنسيق التاريخ', 'ui', 1),
                ('time_format', 'HH:mm:ss', 'string', 'تنسيق الوقت', 'ui', 1)
            ]

            for setting in default_settings:
                self.cursor.execute("""
                    INSERT OR IGNORE INTO system_settings
                    (setting_key, setting_value, setting_type, description, category, is_editable)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, setting)

            # إدراج بيانات تجريبية للعملاء
            sample_customers = [
                ('CUST001', 'شركة الرياض للتجارة', 'company', 'أحمد محمد', '011-1234567', '0501234567', '<EMAIL>', 'شارع الملك فهد، الرياض', 'الرياض', 'السعودية', '*********0123', 50000.00, 30),
                ('CUST002', 'محمد علي السعيد', 'individual', 'محمد علي', '012-2345678', '0502345678', '<EMAIL>', 'حي النزهة، جدة', 'جدة', 'السعودية', '', 10000.00, 15),
                ('CUST003', 'مؤسسة الخليج للمقاولات', 'company', 'سعد الخليج', '013-3456789', '0503456789', '<EMAIL>', 'الدمام، المنطقة الشرقية', 'الدمام', 'السعودية', '9876543210987', 100000.00, 45),
                ('CUST004', 'فاطمة أحمد النور', 'individual', 'فاطمة أحمد', '014-4567890', '0504567890', '<EMAIL>', 'حي الملز، الرياض', 'الرياض', 'السعودية', '', 5000.00, 7),
                ('CUST005', 'شركة النجم الذهبي', 'company', 'خالد النجم', '015-5678901', '0505678901', '<EMAIL>', 'مكة المكرمة', 'مكة', 'السعودية', '5555666677778', 75000.00, 30)
            ]

            for customer in sample_customers:
                self.cursor.execute("""
                    INSERT OR IGNORE INTO customers
                    (customer_code, customer_name, customer_type, contact_person, phone, mobile, email, address, city, country, tax_number, credit_limit, payment_terms)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, customer)

            # إدراج بيانات تجريبية للمنتجات
            sample_products = [
                ('PROD001', 'جهاز كمبيوتر محمول', 'product', 'إلكترونيات', 'جهاز', 2500.00, 3500.00, 15.00, 50, 10, 100, '*********0123', 'جهاز كمبيوتر محمول عالي الأداء'),
                ('PROD002', 'خدمة صيانة كمبيوتر', 'service', 'خدمات تقنية', 'ساعة', 0.00, 150.00, 15.00, 0, 0, 0, '', 'خدمة صيانة وإصلاح أجهزة الكمبيوتر'),
                ('PROD003', 'طابعة ليزر', 'product', 'إلكترونيات', 'جهاز', 800.00, 1200.00, 15.00, 25, 5, 50, '2345678901234', 'طابعة ليزر عالية الجودة'),
                ('PROD004', 'برنامج محاسبة', 'service', 'برمجيات', 'رخصة', 500.00, 1000.00, 15.00, 0, 0, 0, '', 'برنامج محاسبة متكامل'),
                ('PROD005', 'شاشة كمبيوتر 24 بوصة', 'product', 'إلكترونيات', 'جهاز', 400.00, 650.00, 15.00, 30, 5, 60, '3456789012345', 'شاشة كمبيوتر عالية الدقة')
            ]

            for product in sample_products:
                self.cursor.execute("""
                    INSERT OR IGNORE INTO products
                    (product_code, product_name, product_type, category, unit, cost_price, selling_price, tax_rate, stock_quantity, min_stock_level, max_stock_level, barcode, description)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, product)

            self.connection.commit()

        except sqlite3.Error as e:
            print(f"خطأ في تحديث قاعدة البيانات: {e}")

    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
            self.cursor = None