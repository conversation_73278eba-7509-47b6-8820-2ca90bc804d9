#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة ترميز الحسابات
Account Coding Fix Test
"""

import sys
import os
from datetime import datetime

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_account_coding_module():
    """اختبار وحدة ترميز الحسابات"""
    print("🔢 اختبار وحدة ترميز الحسابات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.setup.account_coding import AccountCodingDialog
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            def execute_query(self, query, params=None):
                return []
        
        db_manager = MockDBManager()
        
        # إنشاء الحوار
        dialog = AccountCodingDialog(db_manager)
        print("   ✅ تم إنشاء حوار ترميز الحسابات بنجاح")
        
        # اختبار الوظائف الأساسية
        if hasattr(dialog, 'coding_system_group'):
            print("   ✅ مجموعة أنظمة الترميز موجودة")
        
        if hasattr(dialog, 'update_system_preview'):
            print("   ✅ دالة تحديث المعاينة موجودة")
        
        if hasattr(dialog, 'get_system_name'):
            print("   ✅ دالة الحصول على اسم النظام موجودة")
        
        # اختبار دالة get_system_name مع قيم مختلفة
        try:
            name1 = dialog.get_system_name(1)
            print(f"   ✅ النظام 1: {name1}")
            
            name2 = dialog.get_system_name(2)
            print(f"   ✅ النظام 2: {name2}")
            
            # اختبار مع قيمة غير صحيحة
            name_invalid = dialog.get_system_name("invalid")
            print(f"   ⚠️ قيمة غير صحيحة: {name_invalid}")
            
        except Exception as e:
            print(f"   ❌ خطأ في اختبار get_system_name: {e}")
        
        # اختبار تحديث المعاينة
        try:
            dialog.update_system_preview()
            print("   ✅ تحديث المعاينة يعمل بدون أخطاء")
        except Exception as e:
            print(f"   ❌ خطأ في تحديث المعاينة: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار ترميز الحسابات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_account_coding_functions():
    """اختبار وظائف ترميز الحسابات"""
    print("\n🔧 اختبار وظائف ترميز الحسابات...")
    
    try:
        from modules.setup.account_coding_data import get_system_name_function
        
        # محاكاة كائن self
        class MockSelf:
            pass
        
        mock_self = MockSelf()
        
        # اختبار الدالة مع قيم مختلفة
        test_cases = [
            (1, "النظام العشري"),
            (2, "النظام المئوي"),
            (3, "النظام الألفي"),
            (4, "النظام الهجين"),
            (5, "نظام مخصص"),
            (99, "نظام غير معروف (99)"),
            ("1", "النظام العشري"),
            ("invalid", "خطأ في تحديد النظام"),
        ]
        
        for test_input, expected_type in test_cases:
            try:
                result = get_system_name_function(mock_self, test_input)
                if expected_type in result or "خطأ" in result:
                    print(f"   ✅ المدخل {test_input}: {result}")
                else:
                    print(f"   ⚠️ المدخل {test_input}: {result} (غير متوقع)")
            except Exception as e:
                print(f"   ❌ خطأ مع المدخل {test_input}: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الوظائف: {e}")
        return False

def test_main_system_integration():
    """اختبار تكامل ترميز الحسابات مع النظام الرئيسي"""
    print("\n🔗 اختبار تكامل ترميز الحسابات مع النظام الرئيسي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        # اختبار النظام الرئيسي
        import main_system
        from main_system import MainSystemWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainSystemWindow()
        print("   ✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار وجود دالة فتح ترميز الحسابات
        if hasattr(main_window, 'open_account_coding'):
            print("   ✅ دالة فتح ترميز الحسابات موجودة")
        else:
            print("   ⚠️ دالة فتح ترميز الحسابات غير موجودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التكامل: {e}")
        return False

def test_ui_display():
    """اختبار عرض واجهة ترميز الحسابات"""
    print("\n🖥️ اختبار عرض واجهة ترميز الحسابات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt, QTimer
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.setup.account_coding import AccountCodingDialog
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            def execute_query(self, query, params=None):
                return []
        
        db_manager = MockDBManager()
        
        # إنشاء وعرض الحوار
        dialog = AccountCodingDialog(db_manager)
        dialog.show()
        print("   ✅ تم عرض واجهة ترميز الحسابات")
        
        # إغلاق الحوار بعد ثانيتين
        QTimer.singleShot(2000, dialog.close)
        
        # تشغيل حلقة الأحداث لفترة قصيرة
        QTimer.singleShot(2500, app.quit)
        app.exec_()
        
        print("   ✅ تم إغلاق الواجهة بنجاح")
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في عرض الواجهة: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار إصلاح مشكلة ترميز الحسابات")
    print("=" * 70)
    
    tests = [
        ("وحدة ترميز الحسابات", test_account_coding_module),
        ("وظائف ترميز الحسابات", test_account_coding_functions),
        ("تكامل النظام الرئيسي", test_main_system_integration),
        ("عرض الواجهة", test_ui_display),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم إصلاح مشكلة ترميز الحسابات بنجاح!")
        print("✅ الوحدة تعمل بدون أخطاء")
        
        print("\n🔧 كيفية الاستخدام:")
        print("   1. شغل النظام: python src/main_system.py")
        print("   2. انقر على 'ترميز الحسابات' في شاشات التهيئة")
        print("   3. اختر نظام الترميز المطلوب")
        print("   4. اضبط الإعدادات حسب الحاجة")
        
    else:
        print("\n⚠️ لا تزال هناك مشاكل تحتاج إلى إصلاح")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
