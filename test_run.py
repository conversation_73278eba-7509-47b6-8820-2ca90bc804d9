#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل تجريبي للنظام
Test Run for the System
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import QApplication, QMessageBox
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    
    from src.ui.login_window import LoginWindow
    from src.database.sqlite_manager import SQLiteManager
    from src.utils.translations import translator
    
    print("=== نظام المحاسبة الشامل ===")
    print("Comprehensive Accounting System")
    print("=" * 40)
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setApplicationName("نظام المحاسبة الشامل")
    app.setApplicationVersion("1.0.0")
    
    # تعيين الخط
    font = QFont("Arial", 10)
    app.setFont(font)
    
    # تعيين اتجاه النص للعربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("جاري تهيئة قاعدة البيانات...")
    
    # تهيئة قاعدة البيانات
    try:
        db_manager = SQLiteManager()
        print("تم تهيئة قاعدة البيانات بنجاح")
    except Exception as e:
        print(f"خطأ في تهيئة قاعدة البيانات: {e}")
        QMessageBox.critical(None, "خطأ", f"فشل في تهيئة قاعدة البيانات:\n{str(e)}")
        sys.exit(1)
    
    print("جاري فتح نافذة تسجيل الدخول...")
    
    # إنشاء نافذة تسجيل الدخول
    login_window = LoginWindow()
    login_window.show()
    
    print("تم تشغيل النظام بنجاح!")
    print("بيانات تسجيل الدخول الافتراضية:")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")
    print("=" * 40)
    
    # تشغيل التطبيق
    sys.exit(app.exec_())
    
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    print("يرجى تثبيت المتطلبات باستخدام: pip install PyQt5 bcrypt")
    sys.exit(1)
    
except Exception as e:
    print(f"خطأ عام: {e}")
    sys.exit(1)