#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد وتثبيت النظام
Setup and Installation Script
"""

import os
import sys
import subprocess

def install_requirements():
    """تثبيت المتطلبات"""
    print("جاري تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"خطأ في تثبيت المتطلبات: {e}")
        return False

def check_mysql_connection():
    """التحقق من الاتصال بـ MySQL"""
    print("جاري التحقق من الاتصال بقاعدة البيانات...")
    
    try:
        import mysql.connector
        from mysql.connector import Error
        
        # محاولة الاتصال بـ MySQL
        connection = mysql.connector.connect(
            host='localhost',
            port=3306,
            user='root',
            password=''
        )
        
        if connection.is_connected():
            print("تم الاتصال بـ MySQL بنجاح")
            connection.close()
            return True
            
    except ImportError:
        print("خطأ: مكتبة mysql-connector-python غير مثبتة")
        return False
    except Exception as e:
        print(f"خطأ في الاتصال بـ MySQL: {e}")
        print("\nيرجى التأكد من:")
        print("1. تثبيت MySQL Server")
        print("2. تشغيل خدمة MySQL")
        print("3. صحة بيانات الاتصال في ملف config.json")
        return False

def create_config_file():
    """إنشاء ملف الإعدادات"""
    config_content = """{
    "database": {
        "host": "localhost",
        "port": 3306,
        "user": "root",
        "password": "",
        "database": "accounting_system"
    },
    "ui": {
        "language": "ar",
        "theme": "light",
        "font_size": 10
    },
    "company": {
        "name": "شركة المحاسبة",
        "name_en": "Accounting Company",
        "address": "",
        "phone": "",
        "email": "",
        "tax_number": "",
        "logo_path": ""
    },
    "accounting": {
        "currency": "SAR",
        "currency_symbol": "ر.س",
        "decimal_places": 2,
        "tax_rate": 15.0,
        "fiscal_year_start": "01-01",
        "backup_interval": 24
    }
}"""
    
    try:
        with open("config.json", "w", encoding="utf-8") as f:
            f.write(config_content)
        print("تم إنشاء ملف الإعدادات")
        return True
    except Exception as e:
        print(f"خطأ في إنشاء ملف الإعدادات: {e}")
        return False

def main():
    """الدالة الرئيسية للإعداد"""
    print("=== إعداد نظام المحاسبة الشامل ===")
    print("Comprehensive Accounting System Setup")
    print("=" * 40)
    
    # التحقق من Python
    if sys.version_info < (3, 7):
        print("خطأ: يتطلب النظام Python 3.7 أو أحدث")
        return False
    
    print(f"إصدار Python: {sys.version}")
    
    # إنشاء ملف الإعدادات
    if not os.path.exists("config.json"):
        if not create_config_file():
            return False
    
    # تثبيت المتطلبات
    if not install_requirements():
        return False
    
    # التحقق من MySQL
    if not check_mysql_connection():
        print("\nتحذير: لم يتم العثور على MySQL أو فشل الاتصال")
        print("يمكنك المتابعة ولكن ستحتاج لإعداد قاعدة البيانات لاحقاً")
        
        response = input("هل تريد المتابعة؟ (y/n): ")
        if response.lower() != 'y':
            return False
    
    print("\n" + "=" * 40)
    print("تم إعداد النظام بنجاح!")
    print("يمكنك الآن تشغيل النظام باستخدام: python main.py")
    print("بيانات تسجيل الدخول الافتراضية:")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")
    print("=" * 40)
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)