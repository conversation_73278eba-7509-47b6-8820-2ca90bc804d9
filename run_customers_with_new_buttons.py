#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, <PERSON><PERSON>abel
from PyQt5.QtCore import Qt
from src.modules.customers_suppliers_module import CustomersSupplierModule
from src.database.sqlite_manager import SQLiteManager

def main():
    app = QApplication(sys.argv)
    
    # إنشاء قاعدة البيانات
    db_manager = SQLiteManager()
    
    # إنشاء النافذة الرئيسية
    window = QMainWindow()
    window.setWindowTitle("وحدة العملاء والموردين - مع أزرار الفواتير الجديدة")
    window.setGeometry(100, 100, 1400, 900)
    
    # إنشاء widget رئيسي
    main_widget = QWidget()
    layout = QVBoxLayout()
    
    # إضافة عنوان
    title = QLabel("🧾 وحدة العملاء والموردين - مع أزرار الفواتير الجديدة")
    title.setAlignment(Qt.AlignCenter)
    title.setStyleSheet("font-size: 18px; font-weight: bold; padding: 10px; background-color: #3498db; color: white;")
    layout.addWidget(title)
    
    # إنشاء وحدة العملاء
    customers_module = CustomersSupplierModule(db_manager)
    layout.addWidget(customers_module)
    
    main_widget.setLayout(layout)
    window.setCentralWidget(main_widget)
    
    # عرض النافذة
    window.show()
    
    print("=" * 70)
    print("🎉 تم فتح وحدة العملاء والموردين مع الأزرار الجديدة!")
    print("=" * 70)
    print("\n🎯 الأزرار الجديدة المضافة:")
    print("   🧾 فاتورة مبيعات - لإنشاء فاتورة مبيعات للعميل المحدد")
    print("   ↩️ مردود مبيعات - لإنشاء فاتورة مردود للعميل المحدد")
    print("\n💡 كيفية الاستخدام:")
    print("   1. حدد عميل من القائمة")
    print("   2. اضغط على أحد الأزرار الجديدة")
    print("   3. ستظهر رسالة تأكيد أو نافذة الفاتورة")
    print("\n📋 ملاحظات:")
    print("   • الأزرار تظهر فقط للعملاء (ليس للموردين)")
    print("   • الأزرار معطلة عند عدم تحديد عميل")
    print("   • النظام يتكامل مع نظام الفواتير المحسن")
    print("=" * 70)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
