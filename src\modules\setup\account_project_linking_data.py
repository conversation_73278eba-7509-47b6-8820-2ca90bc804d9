#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف البيانات والمعالجة لشاشة ربط الحسابات بالمشاريع
Data and Processing Functions for Account-Project Linking
"""

from PyQt5.QtWidgets import QMessageBox, QTableWidgetItem, QTreeWidgetItem
from PyQt5.QtCore import Qt
from datetime import datetime

def load_data_function(self):
    """تحميل البيانات المحفوظة"""
    try:
        # تحميل الحسابات
        self.load_accounts_data()
        
        # تحميل المشاريع
        self.load_projects_data()
        
        # تحميل القواعد المخصصة
        self.load_custom_rules()
        
        # بناء شجرة الحسابات
        self.build_accounts_tree()
        
        # تحديث الإحصائيات
        self.update_accounts_statistics()
        
    except Exception as e:
        print(f"خطأ في تحميل البيانات: {e}")

def load_accounts_data_function(self):
    """تحميل بيانات الحسابات"""
    # بيانات الحسابات مع حالة الربط
    self.accounts_data = [
        # الحسابات الرئيسية
        ("الأصول المتداولة", "1000", "حساب رئيسي", "غير مربوط", None),
        ("الأصول الثابتة", "2000", "حساب رئيسي", "غير مربوط", None),
        ("الخصوم المتداولة", "3000", "حساب رئيسي", "غير مربوط", None),
        ("رأس المال", "5000", "حساب رئيسي", "غير مربوط", None),
        ("الإيرادات", "6000", "حساب رئيسي", "غير مربوط", None),
        ("المصروفات التشغيلية", "7000", "حساب رئيسي", "غير مربوط", None),
        ("المصروفات الإدارية", "8000", "حساب رئيسي", "غير مربوط", None),
        
        # الحسابات الفرعية المربوطة
        ("النقدية في الصندوق", "1001", "حساب فرعي", "مربوط", "مشروع تحديث الأنظمة"),
        ("البنك الأهلي اليمني", "1002", "حساب فرعي", "مربوط", "مشروع تحديث الأنظمة"),
        ("حساب العملاء", "1003", "حساب فرعي", "مربوط", "مشروع تطوير المباني"),
        ("المخزون", "1004", "حساب فرعي", "مربوط", "مشروع تطوير النظام المحاسبي"),
        ("الأثاث والمعدات", "2001", "حساب فرعي", "مربوط", "مشروع تطوير النظام المحاسبي"),
        ("السيارات", "2002", "حساب فرعي", "مربوط", "مشروع تطوير المباني"),
        ("حساب الموردين", "3001", "حساب فرعي", "مربوط", "مشروع تطوير المباني"),
        ("المرتبات المستحقة", "3002", "حساب فرعي", "غير مربوط", None),
        ("رأس المال المدفوع", "5001", "حساب فرعي", "غير مربوط", None),
        ("إيرادات المبيعات", "6001", "حساب فرعي", "مربوط", "مشروع تطوير النظام المحاسبي"),
        ("مصروفات الرواتب", "7001", "حساب فرعي", "مربوط", "مشروع تطوير النظام المحاسبي"),
        ("مصروفات الإيجار", "8001", "حساب فرعي", "مربوط", "مشروع تطوير النظام المحاسبي")
    ]

def load_projects_data_function(self):
    """تحميل بيانات المشاريع"""
    # بيانات المشاريع النموذجية
    projects_data = [
        ("مشروع تطوير النظام المحاسبي", "PROJ-001", "مشروع تطويري", "قيد التنفيذ", "2024-01-01", "2024-12-31", "5"),
        ("مشروع تطوير المباني", "PROJ-002", "مشروع استثماري", "قيد التنفيذ", "2024-02-01", "2024-11-30", "3"),
        ("مشروع تحديث الأنظمة", "PROJ-003", "مشروع داخلي", "قيد التنفيذ", "2024-03-01", "2024-09-30", "2"),
        ("مشروع تحليل البيانات", "PROJ-004", "مشروع تطويري", "مخطط", "2024-06-01", "2025-05-31", "1"),
        ("مشروع الصيانة العامة", "PROJ-005", "مشروع صيانة", "قيد التنفيذ", "2024-01-01", "2024-12-31", "1")
    ]
    
    self.projects_table.setRowCount(len(projects_data))
    for row, (name, code, type_name, status, start_date, end_date, linked_accounts) in enumerate(projects_data):
        self.projects_table.setItem(row, 0, QTableWidgetItem(name))
        self.projects_table.setItem(row, 1, QTableWidgetItem(code))
        self.projects_table.setItem(row, 2, QTableWidgetItem(type_name))
        self.projects_table.setItem(row, 3, QTableWidgetItem(status))
        self.projects_table.setItem(row, 4, QTableWidgetItem(start_date))
        self.projects_table.setItem(row, 5, QTableWidgetItem(end_date))
        self.projects_table.setItem(row, 6, QTableWidgetItem(linked_accounts))
    
    # تحديث قائمة المشاريع في الكومبو بوكس
    self.default_project.clear()
    self.default_project.addItem("-- اختر مشروع --")
    for name, code, _, _, _, _, _ in projects_data:
        self.default_project.addItem(f"{name} ({code})")

def load_custom_rules_function(self):
    """تحميل القواعد المخصصة"""
    # قواعد مخصصة نموذجية
    custom_rules_data = [
        ("ربط حسابات المصروفات", "المصروفات", "إجباري", "مشروع تطوير النظام المحاسبي", "مفعل"),
        ("ربط حسابات الإيرادات", "الإيرادات", "إجباري", "مشروع تطوير النظام المحاسبي", "مفعل"),
        ("ربط الحسابات المالية", "الأصول المتداولة", "اختياري", "مشروع تحديث الأنظمة", "مفعل"),
        ("ربط حسابات الأصول الثابتة", "الأصول الثابتة", "شرطي", "مشروع تطوير المباني", "غير مفعل")
    ]
    
    self.custom_rules_table.setRowCount(len(custom_rules_data))
    for row, (rule_name, account_type, condition, default_project, status) in enumerate(custom_rules_data):
        self.custom_rules_table.setItem(row, 0, QTableWidgetItem(rule_name))
        self.custom_rules_table.setItem(row, 1, QTableWidgetItem(account_type))
        self.custom_rules_table.setItem(row, 2, QTableWidgetItem(condition))
        self.custom_rules_table.setItem(row, 3, QTableWidgetItem(default_project))
        self.custom_rules_table.setItem(row, 4, QTableWidgetItem(status))

def build_accounts_tree_function(self):
    """بناء شجرة الحسابات"""
    self.accounts_tree.clear()
    
    # تجميع الحسابات حسب النوع
    main_accounts = {}
    sub_accounts = {}
    
    for name, number, account_type, linking_status, linked_project in self.accounts_data:
        if account_type == "حساب رئيسي":
            main_accounts[number] = (name, number, account_type, linking_status, linked_project)
        else:
            parent_number = number[:1] + "000"
            if parent_number not in sub_accounts:
                sub_accounts[parent_number] = []
            sub_accounts[parent_number].append((name, number, account_type, linking_status, linked_project))
    
    # إنشاء عقد الحسابات الرئيسية
    for number, (name, acc_number, acc_type, status, project) in main_accounts.items():
        main_item = QTreeWidgetItem([name, acc_number, acc_type, status])
        main_item.setExpanded(True)
        
        # تلوين العقدة حسب حالة الربط
        if status == "مربوط":
            main_item.setBackground(0, Qt.green)
            main_item.setBackground(1, Qt.green)
            main_item.setBackground(2, Qt.green)
            main_item.setBackground(3, Qt.green)
        else:
            main_item.setBackground(0, Qt.yellow)
            main_item.setBackground(1, Qt.yellow)
            main_item.setBackground(2, Qt.yellow)
            main_item.setBackground(3, Qt.yellow)
        
        # إضافة الحسابات الفرعية
        if number in sub_accounts:
            for sub_name, sub_number, sub_type, sub_status, sub_project in sub_accounts[number]:
                sub_item = QTreeWidgetItem([sub_name, sub_number, sub_type, sub_status])
                
                # تلوين العقدة الفرعية حسب حالة الربط
                if sub_status == "مربوط":
                    sub_item.setBackground(0, Qt.lightGreen)
                    sub_item.setBackground(1, Qt.lightGreen)
                    sub_item.setBackground(2, Qt.lightGreen)
                    sub_item.setBackground(3, Qt.lightGreen)
                else:
                    sub_item.setBackground(0, Qt.lightGray)
                    sub_item.setBackground(1, Qt.lightGray)
                    sub_item.setBackground(2, Qt.lightGray)
                    sub_item.setBackground(3, Qt.lightGray)
                
                main_item.addChild(sub_item)
        
        self.accounts_tree.addTopLevelItem(main_item)

def update_accounts_statistics_function(self):
    """تحديث إحصائيات الحسابات"""
    total_accounts = len(self.accounts_data)
    linked_accounts = sum(1 for _, _, _, status, _ in self.accounts_data if status == "مربوط")
    unlinked_accounts = total_accounts - linked_accounts
    
    stats_text = f"📊 إحصائيات: {total_accounts} حساب، {linked_accounts} مربوط، {unlinked_accounts} غير مربوط"
    self.accounts_info.setText(stats_text)

def on_account_selected_function(self, item, column):
    """معالجة اختيار حساب من الشجرة"""
    account_name = item.text(0)
    account_number = item.text(1)
    account_type = item.text(2)
    linking_status = item.text(3)
    
    # تحديث معلومات الحساب المحدد
    self.selected_account_name.setText(account_name)
    self.selected_account_number.setText(account_number)
    self.selected_account_type.setText(account_type)
    self.current_linking_status.setText(linking_status)
    
    # البحث عن المشروع المربوط
    linked_project = None
    for name, number, _, status, project in self.accounts_data:
        if number == account_number and status == "مربوط":
            linked_project = project
            break
    
    # تحديث إعدادات الربط
    if linking_status == "مربوط":
        self.enable_linking.setChecked(True)
        if linked_project:
            # البحث عن المشروع في القائمة
            for i in range(self.default_project.count()):
                if linked_project in self.default_project.itemText(i):
                    self.default_project.setCurrentIndex(i)
                    break
    else:
        self.enable_linking.setChecked(False)
        self.default_project.setCurrentIndex(0)

def on_account_double_clicked_function(self, item, column):
    """معالجة النقر المزدوج على حساب في الشجرة"""
    account_name = item.text(0)
    account_number = item.text(1)
    account_type = item.text(2)
    linking_status = item.text(3)
    
    # البحث عن تفاصيل الحساب
    linked_project = "غير محدد"
    for name, number, _, status, project in self.accounts_data:
        if number == account_number and project:
            linked_project = project
            break
    
    QMessageBox.information(self, "تفاصيل الحساب", 
                           f"📊 الحساب: {account_name}\n"
                           f"🔢 الرقم: {account_number}\n"
                           f"🏷️ النوع: {account_type}\n"
                           f"🔗 حالة الربط: {linking_status}\n"
                           f"📁 المشروع المربوط: {linked_project}")

def refresh_accounts_function(self):
    """تحديث شجرة الحسابات"""
    self.build_accounts_tree()
    self.update_accounts_statistics()
    QMessageBox.information(self, "تم", "تم تحديث شجرة الحسابات بنجاح")

def apply_linking_function(self):
    """تطبيق الربط"""
    if not self.selected_account_number.text():
        QMessageBox.warning(self, "تحذير", "يرجى اختيار حساب أولاً")
        return
    
    if not self.enable_linking.isChecked():
        QMessageBox.warning(self, "تحذير", "يرجى تفعيل الربط أولاً")
        return
    
    if self.default_project.currentIndex() == 0:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار مشروع للربط")
        return
    
    account_number = self.selected_account_number.text()
    project_name = self.default_project.currentText().split(" (")[0]
    linking_type = self.linking_type.currentText()
    
    # تحديث حالة الربط في البيانات
    for i, (name, number, acc_type, status, project) in enumerate(self.accounts_data):
        if number == account_number:
            self.accounts_data[i] = (name, number, acc_type, "مربوط", project_name)
            break
    
    # تحديث الشجرة والإحصائيات
    self.build_accounts_tree()
    self.update_accounts_statistics()
    
    QMessageBox.information(self, "تم", 
                           f"تم ربط الحساب {account_number} بالمشروع {project_name}\n"
                           f"نوع الربط: {linking_type}")

def remove_linking_function(self):
    """إزالة الربط"""
    if not self.selected_account_number.text():
        QMessageBox.warning(self, "تحذير", "يرجى اختيار حساب أولاً")
        return
    
    account_number = self.selected_account_number.text()
    
    # تحديث حالة الربط في البيانات
    for i, (name, number, acc_type, status, project) in enumerate(self.accounts_data):
        if number == account_number:
            self.accounts_data[i] = (name, number, acc_type, "غير مربوط", None)
            break
    
    # تحديث الشجرة والإحصائيات
    self.build_accounts_tree()
    self.update_accounts_statistics()
    
    # مسح إعدادات الربط
    self.enable_linking.setChecked(False)
    self.default_project.setCurrentIndex(0)
    self.current_linking_status.setText("غير مربوط")
    
    QMessageBox.information(self, "تم", f"تم إزالة ربط الحساب {account_number}")

def test_linking_function(self):
    """اختبار الربط"""
    if not self.selected_account_number.text():
        QMessageBox.warning(self, "تحذير", "يرجى اختيار حساب أولاً")
        return
    
    account_name = self.selected_account_name.text()
    account_number = self.selected_account_number.text()
    linking_type = self.linking_type.currentText()
    project_name = self.default_project.currentText()
    
    test_result = f"""
🧪 نتيجة اختبار الربط
{'='*40}

📊 الحساب المختبر:
   الاسم: {account_name}
   الرقم: {account_number}

🔗 إعدادات الربط:
   النوع: {linking_type}
   المشروع: {project_name}

✅ نتائج الاختبار:
   ✓ الحساب موجود في النظام
   ✓ المشروع متاح للربط
   ✓ نوع الربط صحيح
   ✓ لا توجد تعارضات

🎯 التوصية: يمكن تطبيق الربط بأمان
"""
    
    QMessageBox.information(self, "نتيجة الاختبار", test_result)
