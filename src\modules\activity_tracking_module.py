#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة تتبع النشاطات (سجل الأحداث)
Activity Tracking Module (Event Log)
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QCheckBox, QDateTimeEdit,
                             QDateEdit, QSplitter, QFrame, QDialogButtonBox,
                             QProgressBar, QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt, QDate, QDateTime, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QIcon
from datetime import datetime, timedelta
import json
import os

try:
    from ..database.sqlite_manager import SQLiteManager
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class ActivityLogger:
    """مسجل النشاطات"""
    
    def __init__(self, db_manager):
        self.db_manager = db_manager
        self.setup_database()
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            create_query = """
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                username TEXT,
                activity_type TEXT NOT NULL,
                activity_category TEXT NOT NULL,
                description TEXT NOT NULL,
                details TEXT,
                ip_address TEXT,
                user_agent TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                severity_level TEXT DEFAULT 'INFO',
                module_name TEXT,
                function_name TEXT,
                success BOOLEAN DEFAULT 1,
                error_message TEXT,
                session_id TEXT,
                affected_records INTEGER DEFAULT 0,
                before_data TEXT,
                after_data TEXT
            )
            """
            self.db_manager.execute_update(create_query)
            
            # إنشاء فهارس للأداء
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_activity_timestamp ON activity_log(timestamp)",
                "CREATE INDEX IF NOT EXISTS idx_activity_user ON activity_log(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_activity_type ON activity_log(activity_type)",
                "CREATE INDEX IF NOT EXISTS idx_activity_category ON activity_log(activity_category)"
            ]
            
            for index_query in indexes:
                self.db_manager.execute_update(index_query)
                
        except Exception as e:
            print(f"خطأ في إعداد قاعدة بيانات سجل النشاطات: {e}")
    
    def log_activity(self, activity_type, category, description, **kwargs):
        """تسجيل نشاط"""
        try:
            query = """
            INSERT INTO activity_log 
            (user_id, username, activity_type, activity_category, description, 
             details, ip_address, user_agent, severity_level, module_name, 
             function_name, success, error_message, session_id, affected_records,
             before_data, after_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                kwargs.get('user_id'),
                kwargs.get('username', 'النظام'),
                activity_type,
                category,
                description,
                kwargs.get('details'),
                kwargs.get('ip_address'),
                kwargs.get('user_agent'),
                kwargs.get('severity_level', 'INFO'),
                kwargs.get('module_name'),
                kwargs.get('function_name'),
                kwargs.get('success', True),
                kwargs.get('error_message'),
                kwargs.get('session_id'),
                kwargs.get('affected_records', 0),
                kwargs.get('before_data'),
                kwargs.get('after_data')
            )
            
            self.db_manager.execute_update(query, params)
            
        except Exception as e:
            print(f"خطأ في تسجيل النشاط: {e}")


class ActivityFilterDialog(QDialog):
    """حوار فلترة النشاطات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔍 فلترة النشاطات")
        self.setFixedSize(400, 500)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🔍 فلترة وبحث النشاطات")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: white;
                background-color: #3498db;
                padding: 10px;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # فلاتر التاريخ
        date_group = QGroupBox("فترة زمنية")
        date_layout = QFormLayout()
        
        self.from_date_edit = QDateTimeEdit()
        self.from_date_edit.setDateTime(QDateTime.currentDateTime().addDays(-7))
        self.from_date_edit.setCalendarPopup(True)
        date_layout.addRow("من تاريخ:", self.from_date_edit)
        
        self.to_date_edit = QDateTimeEdit()
        self.to_date_edit.setDateTime(QDateTime.currentDateTime())
        self.to_date_edit.setCalendarPopup(True)
        date_layout.addRow("إلى تاريخ:", self.to_date_edit)
        
        date_group.setLayout(date_layout)
        layout.addWidget(date_group)
        
        # فلاتر النشاط
        activity_group = QGroupBox("نوع النشاط")
        activity_layout = QFormLayout()
        
        self.activity_type_combo = QComboBox()
        self.activity_type_combo.addItems([
            "جميع الأنواع",
            "تسجيل دخول",
            "تسجيل خروج",
            "إنشاء",
            "تعديل",
            "حذف",
            "عرض",
            "طباعة",
            "تصدير",
            "نسخ احتياطي",
            "استعادة",
            "إعدادات",
            "خطأ"
        ])
        activity_layout.addRow("نوع النشاط:", self.activity_type_combo)
        
        self.category_combo = QComboBox()
        self.category_combo.addItems([
            "جميع الفئات",
            "المصادقة",
            "المبيعات",
            "المشتريات",
            "المخزون",
            "الحسابات",
            "التقارير",
            "النظام",
            "المستخدمين",
            "النسخ الاحتياطي"
        ])
        activity_layout.addRow("فئة النشاط:", self.category_combo)
        
        self.severity_combo = QComboBox()
        self.severity_combo.addItems([
            "جميع المستويات",
            "INFO",
            "WARNING", 
            "ERROR",
            "CRITICAL"
        ])
        activity_layout.addRow("مستوى الخطورة:", self.severity_combo)
        
        activity_group.setLayout(activity_layout)
        layout.addWidget(activity_group)
        
        # فلاتر المستخدم
        user_group = QGroupBox("المستخدم")
        user_layout = QFormLayout()
        
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("اسم المستخدم...")
        user_layout.addRow("اسم المستخدم:", self.username_edit)
        
        self.ip_address_edit = QLineEdit()
        self.ip_address_edit.setPlaceholderText("عنوان IP...")
        user_layout.addRow("عنوان IP:", self.ip_address_edit)
        
        user_group.setLayout(user_layout)
        layout.addWidget(user_group)
        
        # بحث نصي
        search_group = QGroupBox("البحث النصي")
        search_layout = QVBoxLayout()
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في الوصف والتفاصيل...")
        search_layout.addWidget(self.search_edit)
        
        search_group.setLayout(search_layout)
        layout.addWidget(search_group)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.apply_btn = button_box.addButton("🔍 تطبيق الفلتر", QDialogButtonBox.AcceptRole)
        self.reset_btn = button_box.addButton("🔄 إعادة تعيين", QDialogButtonBox.ResetRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        self.apply_btn.clicked.connect(self.accept)
        self.reset_btn.clicked.connect(self.reset_filters)
        self.cancel_btn.clicked.connect(self.reject)
        
        layout.addWidget(button_box)
        self.setLayout(layout)
    
    def reset_filters(self):
        """إعادة تعيين الفلاتر"""
        self.from_date_edit.setDateTime(QDateTime.currentDateTime().addDays(-7))
        self.to_date_edit.setDateTime(QDateTime.currentDateTime())
        self.activity_type_combo.setCurrentIndex(0)
        self.category_combo.setCurrentIndex(0)
        self.severity_combo.setCurrentIndex(0)
        self.username_edit.clear()
        self.ip_address_edit.clear()
        self.search_edit.clear()
    
    def get_filters(self):
        """الحصول على الفلاتر المحددة"""
        return {
            'from_date': self.from_date_edit.dateTime().toString("yyyy-MM-dd hh:mm:ss"),
            'to_date': self.to_date_edit.dateTime().toString("yyyy-MM-dd hh:mm:ss"),
            'activity_type': self.activity_type_combo.currentText() if self.activity_type_combo.currentIndex() > 0 else None,
            'category': self.category_combo.currentText() if self.category_combo.currentIndex() > 0 else None,
            'severity': self.severity_combo.currentText() if self.severity_combo.currentIndex() > 0 else None,
            'username': self.username_edit.text().strip() or None,
            'ip_address': self.ip_address_edit.text().strip() or None,
            'search_text': self.search_edit.text().strip() or None
        }


class ActivityTrackingWidget(QWidget):
    """وحدة تتبع النشاطات الرئيسية"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.activity_logger = ActivityLogger(db_manager)
        self.current_filters = {}
        self.setup_ui()
        self.load_activities()
        self.setup_auto_refresh()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("📋 تتبع النشاطات (سجل الأحداث)")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 20px;
                font-weight: bold;
                color: white;
                background-color: #9b59b6;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب سجل النشاطات
        activities_tab = self.create_activities_tab()
        self.tabs.addTab(activities_tab, "📋 سجل النشاطات")
        
        # تبويب الإحصائيات
        statistics_tab = self.create_statistics_tab()
        self.tabs.addTab(statistics_tab, "📊 الإحصائيات")
        
        # تبويب التنبيهات
        alerts_tab = self.create_alerts_tab()
        self.tabs.addTab(alerts_tab, "🚨 التنبيهات")
        
        # تبويب الإعدادات
        settings_tab = self.create_settings_tab()
        self.tabs.addTab(settings_tab, "⚙️ الإعدادات")
        
        layout.addWidget(self.tabs)
        self.setLayout(layout)
    
    def create_activities_tab(self):
        """إنشاء تبويب سجل النشاطات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        # إحصائيات سريعة
        self.total_activities_label = QLabel("إجمالي النشاطات: 0")
        self.total_activities_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                background-color: #3498db;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            
            }
        """)
        toolbar_layout.addWidget(self.total_activities_label)
        
        self.today_activities_label = QLabel("نشاطات اليوم: 0")
        self.today_activities_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            
            }
        """)
        toolbar_layout.addWidget(self.today_activities_label)
        
        self.errors_label = QLabel("الأخطاء: 0")
        self.errors_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            
            }
        """)
        toolbar_layout.addWidget(self.errors_label)
        
        toolbar_layout.addStretch()
        
        # أزرار العمليات
        filter_btn = QPushButton("🔍 فلترة")
        filter_btn.clicked.connect(self.show_filter_dialog)
        toolbar_layout.addWidget(filter_btn)
        
        export_btn = QPushButton("📤 تصدير")
        export_btn.clicked.connect(self.export_activities)
        toolbar_layout.addWidget(export_btn)
        
        clear_btn = QPushButton("🗑️ مسح السجل")
        clear_btn.clicked.connect(self.clear_log)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        toolbar_layout.addWidget(clear_btn)
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.load_activities)
        toolbar_layout.addWidget(refresh_btn)
        
        layout.addLayout(toolbar_layout)
        
        # جدول النشاطات
        self.activities_table = QTableWidget()
        self.activities_table.setColumnCount(9)
        self.activities_table.setHorizontalHeaderLabels([
            "الوقت", "المستخدم", "النوع", "الفئة", "الوصف", 
            "المستوى", "عنوان IP", "الحالة", "التفاصيل"
        ])
        
        # تخصيص عرض الأعمدة
        header = self.activities_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.resizeSection(0, 150)  # الوقت
        header.resizeSection(1, 100)  # المستخدم
        header.resizeSection(2, 80)   # النوع
        header.resizeSection(3, 80)   # الفئة
        header.resizeSection(4, 200)  # الوصف
        header.resizeSection(5, 80)   # المستوى
        header.resizeSection(6, 120)  # IP
        header.resizeSection(7, 80)   # الحالة
        
        # تفعيل الترتيب
        self.activities_table.setSortingEnabled(True)
        
        # تفعيل التحديد المتعدد
        self.activities_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        layout.addWidget(self.activities_table)
        
        widget.setLayout(layout)
        return widget
    
    def create_statistics_tab(self):
        """إنشاء تبويب الإحصائيات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إحصائيات عامة
        stats_group = QGroupBox("📊 الإحصائيات العامة")
        stats_layout = QGridLayout()
        
        # إحصائيات النشاطات
        self.stats_text = QTextEdit()
        self.stats_text.setMaximumHeight(200)
        self.stats_text.setReadOnly(True)
        stats_layout.addWidget(self.stats_text, 0, 0, 1, 2)
        
        # زر تحديث الإحصائيات
        refresh_stats_btn = QPushButton("🔄 تحديث الإحصائيات")
        refresh_stats_btn.clicked.connect(self.update_statistics)
        stats_layout.addWidget(refresh_stats_btn, 1, 0)
        
        generate_report_btn = QPushButton("📊 تقرير مفصل")
        generate_report_btn.clicked.connect(self.generate_detailed_report)
        stats_layout.addWidget(generate_report_btn, 1, 1)
        
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)
        
        # رسم بياني للنشاطات (محاكاة)
        chart_group = QGroupBox("📈 الرسم البياني")
        chart_layout = QVBoxLayout()
        
        chart_placeholder = QLabel("📈 رسم بياني لتوزيع النشاطات حسب الوقت\n(قيد التطوير)")
        chart_placeholder.setStyleSheet("""
            QLabel {
                border: 2px dashed #bdc3c7;
                padding: 50px;
                text-align: center;
                color: #7f8c8d;
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 14px;
            }
        """)
        chart_placeholder.setAlignment(Qt.AlignCenter)
        chart_layout.addWidget(chart_placeholder)
        
        chart_group.setLayout(chart_layout)
        layout.addWidget(chart_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def create_alerts_tab(self):
        """إنشاء تبويب التنبيهات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات التنبيهات
        alerts_group = QGroupBox("🚨 إعدادات التنبيهات")
        alerts_layout = QFormLayout()
        
        self.enable_alerts_check = QCheckBox("تفعيل التنبيهات")
        self.enable_alerts_check.setChecked(True)
        alerts_layout.addRow("", self.enable_alerts_check)
        
        self.failed_login_threshold_spin = QSpinBox()
        self.failed_login_threshold_spin.setRange(1, 100)
        self.failed_login_threshold_spin.setValue(5)
        alerts_layout.addRow("تنبيه عند فشل تسجيل الدخول:", self.failed_login_threshold_spin)
        
        self.error_threshold_spin = QSpinBox()
        self.error_threshold_spin.setRange(1, 1000)
        self.error_threshold_spin.setValue(10)
        alerts_layout.addRow("تنبيه عند تجاوز عدد الأخطاء:", self.error_threshold_spin)
        
        self.suspicious_activity_check = QCheckBox("تنبيه عند النشاط المشبوه")
        self.suspicious_activity_check.setChecked(True)
        alerts_layout.addRow("", self.suspicious_activity_check)
        
        alerts_group.setLayout(alerts_layout)
        layout.addWidget(alerts_group)
        
        # قائمة التنبيهات النشطة
        active_alerts_group = QGroupBox("🔔 التنبيهات النشطة")
        active_alerts_layout = QVBoxLayout()
        
        self.alerts_list = QTreeWidget()
        self.alerts_list.setHeaderLabels(["النوع", "الوصف", "الوقت", "الحالة"])
        active_alerts_layout.addWidget(self.alerts_list)
        
        # أزرار إدارة التنبيهات
        alerts_buttons_layout = QHBoxLayout()
        
        mark_read_btn = QPushButton("✅ تحديد كمقروء")
        mark_read_btn.clicked.connect(self.mark_alert_read)
        alerts_buttons_layout.addWidget(mark_read_btn)
        
        dismiss_btn = QPushButton("❌ إزالة")
        dismiss_btn.clicked.connect(self.dismiss_alert)
        alerts_buttons_layout.addWidget(dismiss_btn)
        
        alerts_buttons_layout.addStretch()
        
        refresh_alerts_btn = QPushButton("🔄 تحديث")
        refresh_alerts_btn.clicked.connect(self.load_alerts)
        alerts_buttons_layout.addWidget(refresh_alerts_btn)
        
        active_alerts_layout.addLayout(alerts_buttons_layout)
        active_alerts_group.setLayout(active_alerts_layout)
        layout.addWidget(active_alerts_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات التسجيل
        logging_group = QGroupBox("📝 إعدادات التسجيل")
        logging_layout = QFormLayout()
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["INFO", "WARNING", "ERROR", "CRITICAL"])
        logging_layout.addRow("مستوى التسجيل:", self.log_level_combo)
        
        self.retention_days_spin = QSpinBox()
        self.retention_days_spin.setRange(1, 3650)
        self.retention_days_spin.setValue(365)
        self.retention_days_spin.setSuffix(" يوم")
        logging_layout.addRow("الاحتفاظ بالسجلات لمدة:", self.retention_days_spin)
        
        self.auto_cleanup_check = QCheckBox("تنظيف تلقائي للسجلات القديمة")
        self.auto_cleanup_check.setChecked(True)
        logging_layout.addRow("", self.auto_cleanup_check)
        
        self.detailed_logging_check = QCheckBox("تسجيل مفصل (يتضمن البيانات)")
        logging_layout.addRow("", self.detailed_logging_check)
        
        logging_group.setLayout(logging_layout)
        layout.addWidget(logging_group)
        
        # إعدادات الأداء
        performance_group = QGroupBox("⚡ إعدادات الأداء")
        performance_layout = QFormLayout()
        
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(100, 10000)
        self.batch_size_spin.setValue(1000)
        performance_layout.addRow("حجم الدفعة للتحميل:", self.batch_size_spin)
        
        self.refresh_interval_spin = QSpinBox()
        self.refresh_interval_spin.setRange(5, 300)
        self.refresh_interval_spin.setValue(30)
        self.refresh_interval_spin.setSuffix(" ثانية")
        performance_layout.addRow("فترة التحديث التلقائي:", self.refresh_interval_spin)
        
        performance_group.setLayout(performance_layout)
        layout.addWidget(performance_group)
        
        # أزرار الحفظ
        save_layout = QHBoxLayout()
        save_layout.addStretch()
        
        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        save_settings_btn.clicked.connect(self.save_settings)
        save_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_layout.addWidget(save_settings_btn)
        
        layout.addLayout(save_layout)
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget
    
    def setup_auto_refresh(self):
        """إعداد التحديث التلقائي"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_activities)
        self.refresh_timer.start(30000)  # تحديث كل 30 ثانية
    
    def load_activities(self):
        """تحميل النشاطات"""
        try:
            # بناء الاستعلام مع الفلاتر
            query = """
            SELECT timestamp, username, activity_type, activity_category, 
                   description, severity_level, ip_address, success, details
            FROM activity_log 
            WHERE 1=1
            """
            params = []
            
            # تطبيق الفلاتر
            if self.current_filters.get('from_date'):
                query += " AND timestamp >= ?"
                params.append(self.current_filters['from_date'])
            
            if self.current_filters.get('to_date'):
                query += " AND timestamp <= ?"
                params.append(self.current_filters['to_date'])
            
            if self.current_filters.get('activity_type'):
                query += " AND activity_type = ?"
                params.append(self.current_filters['activity_type'])
            
            if self.current_filters.get('category'):
                query += " AND activity_category = ?"
                params.append(self.current_filters['category'])
            
            if self.current_filters.get('severity'):
                query += " AND severity_level = ?"
                params.append(self.current_filters['severity'])
            
            if self.current_filters.get('username'):
                query += " AND username LIKE ?"
                params.append(f"%{self.current_filters['username']}%")
            
            if self.current_filters.get('ip_address'):
                query += " AND ip_address LIKE ?"
                params.append(f"%{self.current_filters['ip_address']}%")
            
            if self.current_filters.get('search_text'):
                query += " AND (description LIKE ? OR details LIKE ?)"
                search_term = f"%{self.current_filters['search_text']}%"
                params.extend([search_term, search_term])
            
            query += " ORDER BY timestamp DESC LIMIT 1000"
            
            activities = self.db_manager.execute_query(query, params)
            
            self.activities_table.setRowCount(len(activities))
            
            for row, activity in enumerate(activities):
                timestamp, username, activity_type, category, description, severity, ip_address, success, details = activity
                
                # تنسيق الوقت
                time_item = QTableWidgetItem(str(timestamp))
                self.activities_table.setItem(row, 0, time_item)
                
                # المستخدم
                user_item = QTableWidgetItem(str(username or "النظام"))
                self.activities_table.setItem(row, 1, user_item)
                
                # النوع
                type_item = QTableWidgetItem(str(activity_type))
                self.activities_table.setItem(row, 2, type_item)
                
                # الفئة
                category_item = QTableWidgetItem(str(category))
                self.activities_table.setItem(row, 3, category_item)
                
                # الوصف
                desc_item = QTableWidgetItem(str(description))
                self.activities_table.setItem(row, 4, desc_item)
                
                # المستوى مع تلوين
                severity_item = QTableWidgetItem(str(severity))
                if severity == "ERROR" or severity == "CRITICAL":
                    severity_item.setBackground(QColor("#ffebee"))
                elif severity == "WARNING":
                    severity_item.setBackground(QColor("#fff3cd"))
                else:
                    severity_item.setBackground(QColor("#d5f4e6"))
                self.activities_table.setItem(row, 5, severity_item)
                
                # عنوان IP
                ip_item = QTableWidgetItem(str(ip_address or ""))
                self.activities_table.setItem(row, 6, ip_item)
                
                # الحالة
                status = "نجح" if success else "فشل"
                status_item = QTableWidgetItem(status)
                if not success:
                    status_item.setBackground(QColor("#ffebee"))
                else:
                    status_item.setBackground(QColor("#d5f4e6"))
                self.activities_table.setItem(row, 7, status_item)
                
                # التفاصيل
                details_item = QTableWidgetItem(str(details or ""))
                self.activities_table.setItem(row, 8, details_item)
            
            # تحديث الإحصائيات
            self.update_activity_stats()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل النشاطات:\n{str(e)}")
    
    def update_activity_stats(self):
        """تحديث إحصائيات النشاطات"""
        try:
            # إجمالي النشاطات
            total_query = "SELECT COUNT(*) FROM activity_log"
            total_result = self.db_manager.execute_query(total_query)
            total_count = total_result[0][0] if total_result else 0
            
            # نشاطات اليوم
            today_query = "SELECT COUNT(*) FROM activity_log WHERE DATE(timestamp) = DATE('now')"
            today_result = self.db_manager.execute_query(today_query)
            today_count = today_result[0][0] if today_result else 0
            
            # الأخطاء
            errors_query = "SELECT COUNT(*) FROM activity_log WHERE severity_level IN ('ERROR', 'CRITICAL')"
            errors_result = self.db_manager.execute_query(errors_query)
            errors_count = errors_result[0][0] if errors_result else 0
            
            # تحديث التسميات
            self.total_activities_label.setText(f"إجمالي النشاطات: {total_count:,}")
            self.today_activities_label.setText(f"نشاطات اليوم: {today_count:,}")
            self.errors_label.setText(f"الأخطاء: {errors_count:,}")
            
        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")
    
    def show_filter_dialog(self):
        """عرض حوار الفلترة"""
        dialog = ActivityFilterDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.current_filters = dialog.get_filters()
            self.load_activities()
    
    def export_activities(self):
        """تصدير النشاطات"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            
            filename, _ = QFileDialog.getSaveFileName(
                self, "تصدير سجل النشاطات",
                f"activity_log_{datetime.now().strftime('%Y%m%d')}.csv",
                "CSV Files (*.csv);;Text Files (*.txt);;All Files (*.*)"
            )
            
            if filename:
                # تصدير البيانات المعروضة حالياً
                with open(filename, 'w', encoding='utf-8') as f:
                    # كتابة العناوين
                    headers = []
                    for col in range(self.activities_table.columnCount()):
                        headers.append(self.activities_table.horizontalHeaderItem(col).text())
                    f.write(','.join(headers) + '\n')
                    
                    # كتابة البيانات
                    for row in range(self.activities_table.rowCount()):
                        row_data = []
                        for col in range(self.activities_table.columnCount()):
                            item = self.activities_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        f.write(','.join(row_data) + '\n')
                
                QMessageBox.information(self, "تم", "تم تصدير سجل النشاطات بنجاح")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير السجل:\n{str(e)}")
    
    def clear_log(self):
        """مسح السجل"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح جميع سجلات النشاطات؟\n\n"
            "تحذير: هذا الإجراء لا يمكن التراجع عنه!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db_manager.execute_update("DELETE FROM activity_log")
                QMessageBox.information(self, "تم", "تم مسح سجل النشاطات بنجاح")
                self.load_activities()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في مسح السجل:\n{str(e)}")
    
    def update_statistics(self):
        """تحديث الإحصائيات المفصلة"""
        try:
            stats_text = []
            stats_text.append("📊 إحصائيات مفصلة لسجل النشاطات")
            stats_text.append("=" * 50)
            stats_text.append("")
            
            # إحصائيات حسب النوع
            type_query = """
            SELECT activity_type, COUNT(*) as count 
            FROM activity_log 
            GROUP BY activity_type 
            ORDER BY count DESC
            """
            type_results = self.db_manager.execute_query(type_query)
            
            if type_results:
                stats_text.append("📋 النشاطات حسب النوع:")
                for activity_type, count in type_results:
                    stats_text.append(f"   {activity_type}: {count:,}")
                stats_text.append("")
            
            # إحصائيات حسب المستخدم
            user_query = """
            SELECT username, COUNT(*) as count 
            FROM activity_log 
            WHERE username IS NOT NULL
            GROUP BY username 
            ORDER BY count DESC 
            LIMIT 10
            """
            user_results = self.db_manager.execute_query(user_query)
            
            if user_results:
                stats_text.append("👥 أكثر المستخدمين نشاطاً:")
                for username, count in user_results:
                    stats_text.append(f"   {username}: {count:,}")
                stats_text.append("")
            
            # إحصائيات حسب الوقت
            time_query = """
            SELECT DATE(timestamp) as date, COUNT(*) as count 
            FROM activity_log 
            WHERE timestamp >= datetime('now', '-7 days')
            GROUP BY DATE(timestamp) 
            ORDER BY date DESC
            """
            time_results = self.db_manager.execute_query(time_query)
            
            if time_results:
                stats_text.append("📅 النشاطات خلال آخر 7 أيام:")
                for date, count in time_results:
                    stats_text.append(f"   {date}: {count:,}")
            
            self.stats_text.setText("\n".join(stats_text))
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث الإحصائيات:\n{str(e)}")
    
    def generate_detailed_report(self):
        """إنتاج تقرير مفصل"""
        QMessageBox.information(self, "قريباً", "تقرير النشاطات المفصل قيد التطوير")
    
    def load_alerts(self):
        """تحميل التنبيهات"""
        # محاكاة التنبيهات
        alerts = [
            ("تسجيل دخول فاشل", "5 محاولات فاشلة من IP: *************", "2024-01-15 10:30", "جديد"),
            ("خطأ في النظام", "خطأ في قاعدة البيانات", "2024-01-15 09:15", "مقروء"),
            ("نشاط مشبوه", "محاولة وصول غير مصرح", "2024-01-15 08:45", "جديد"),
        ]
        
        self.alerts_list.clear()
        
        for alert_type, description, timestamp, status in alerts:
            item = QTreeWidgetItem([alert_type, description, timestamp, status])
            
            # تلوين حسب الحالة
            if status == "جديد":
                item.setBackground(0, QColor("#ffebee"))
                item.setBackground(1, QColor("#ffebee"))
                item.setBackground(2, QColor("#ffebee"))
                item.setBackground(3, QColor("#ffebee"))
            
            self.alerts_list.addTopLevelItem(item)
    
    def mark_alert_read(self):
        """تحديد التنبيه كمقروء"""
        current_item = self.alerts_list.currentItem()
        if current_item:
            current_item.setText(3, "مقروء")
            # إزالة التلوين
            for col in range(4):
                current_item.setBackground(col, QColor())
    
    def dismiss_alert(self):
        """إزالة التنبيه"""
        current_item = self.alerts_list.currentItem()
        if current_item:
            index = self.alerts_list.indexOfTopLevelItem(current_item)
            self.alerts_list.takeTopLevelItem(index)
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            settings = {
                'log_level': self.log_level_combo.currentText(),
                'retention_days': self.retention_days_spin.value(),
                'auto_cleanup': self.auto_cleanup_check.isChecked(),
                'detailed_logging': self.detailed_logging_check.isChecked(),
                'batch_size': self.batch_size_spin.value(),
                'refresh_interval': self.refresh_interval_spin.value(),
                'enable_alerts': self.enable_alerts_check.isChecked(),
                'failed_login_threshold': self.failed_login_threshold_spin.value(),
                'error_threshold': self.error_threshold_spin.value(),
                'suspicious_activity': self.suspicious_activity_check.isChecked()
            }
            
            # حفظ الإعدادات (يمكن حفظها في قاعدة البيانات أو ملف)
            QMessageBox.information(self, "تم", "تم حفظ الإعدادات بنجاح")
            
            # تحديث فترة التحديث التلقائي
            self.refresh_timer.setInterval(settings['refresh_interval'] * 1000)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات:\n{str(e)}")

def show_activity_tracking(db_manager, parent=None):
    """عرض وحدة تتبع النشاطات"""
    widget = ActivityTrackingWidget(db_manager, parent)
    return widget
