#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف إدارة العملاء
Customer Management Functions
"""

from PyQt5.QtWidgets import QMessageBox, QTableWidgetItem, QInputDialog, QFileDialog
from PyQt5.QtCore import Qt, QDate
from datetime import datetime
import json
import csv

def create_reports_tab_function(self):
    """إنشاء تبويب التقارير"""
    from PyQt5.QtWidgets import QWidget, QVBoxLayout, QGroupBox, QGridLayout, QPushButton, QTextEdit, QHBoxLayout, QLabel
    
    tab = QWidget()
    layout = QVBoxLayout()
    
    # مجموعة تقارير العملاء
    reports_group = QGroupBox("📊 تقارير العملاء")
    reports_layout = QGridLayout()
    
    # أزرار التقارير
    all_customers_btn = QPushButton("📋 تقرير جميع العملاء")
    all_customers_btn.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    all_customers_btn.clicked.connect(self.generate_all_customers_report)
    
    customers_by_category_btn = QPushButton("🏷️ تقرير حسب التصنيف")
    customers_by_category_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    customers_by_category_btn.clicked.connect(self.generate_customers_by_category_report)
    
    customers_statistics_btn = QPushButton("📊 إحصائيات العملاء")
    customers_statistics_btn.setStyleSheet("""
        QPushButton {
            background-color: #e67e22;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #d35400;
        }
    """)
    customers_statistics_btn.clicked.connect(self.generate_customers_statistics)
    
    reports_layout.addWidget(all_customers_btn, 0, 0)
    reports_layout.addWidget(customers_by_category_btn, 0, 1)
    reports_layout.addWidget(customers_statistics_btn, 0, 2)
    
    reports_group.setLayout(reports_layout)
    
    # منطقة عرض التقارير
    reports_display_group = QGroupBox("📄 عرض التقارير")
    reports_display_layout = QVBoxLayout()
    
    self.reports_display = QTextEdit()
    self.reports_display.setPlaceholderText("ستظهر التقارير هنا...")
    self.reports_display.setStyleSheet("""
        QTextEdit {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    """)
    
    # أزرار التحكم في التقارير
    report_buttons = QHBoxLayout()
    
    export_report_btn = QPushButton("📤 تصدير التقرير")
    export_report_btn.clicked.connect(self.export_report)
    
    print_report_btn = QPushButton("🖨️ طباعة التقرير")
    print_report_btn.clicked.connect(self.print_report)
    
    clear_report_btn = QPushButton("🗑️ مسح التقرير")
    clear_report_btn.clicked.connect(self.clear_report)
    
    report_buttons.addWidget(export_report_btn)
    report_buttons.addWidget(print_report_btn)
    report_buttons.addWidget(clear_report_btn)
    report_buttons.addStretch()
    
    reports_display_layout.addWidget(self.reports_display)
    reports_display_layout.addLayout(report_buttons)
    reports_display_group.setLayout(reports_display_layout)
    
    layout.addWidget(reports_group)
    layout.addWidget(reports_display_group)
    
    tab.setLayout(layout)
    return tab

def load_sample_data_function(self):
    """تحميل البيانات النموذجية"""
    # بيانات عملاء نموذجية يمنية
    sample_customers = [
        {
            "id": "C001",
            "customer_id": "C001",
            "name": "أحمد محمد علي الشامي",
            "company": "شركة الشامي للتجارة",
            "phone": "*********",
            "email": "<EMAIL>",
            "governorate": "صنعاء",
            "city": "الثورة",
            "address": "شارع الزبيري، بجانب البنك الأهلي",
            "category": "عميل مميز",
            "credit_limit": 50000.0,
            "discount_rate": 5.0,
            "balance": 15000.0,
            "status": "نشط",
            "registration_date": "2024-01-15",
            "last_transaction": "2024-01-20"
        },
        {
            "id": "C002",
            "customer_id": "C002",
            "name": "فاطمة سالم أحمد",
            "company": "مؤسسة سالم التجارية",
            "phone": "*********",
            "email": "<EMAIL>",
            "governorate": "عدن",
            "city": "كريتر",
            "address": "شارع الملكة أروى، عمارة النور",
            "category": "عميل VIP",
            "credit_limit": 100000.0,
            "discount_rate": 10.0,
            "balance": 25000.0,
            "status": "نشط",
            "registration_date": "2024-02-01",
            "last_transaction": "2024-02-05"
        },
        {
            "id": "C003",
            "customer_id": "C003",
            "name": "محمد عبدالله الحوثي",
            "company": "مكتب الحوثي للخدمات",
            "phone": "*********",
            "email": "<EMAIL>",
            "governorate": "صعدة",
            "city": "صعدة",
            "address": "حي الثورة، شارع الجمهورية",
            "category": "عميل عادي",
            "credit_limit": 20000.0,
            "discount_rate": 2.0,
            "balance": 5000.0,
            "status": "نشط",
            "registration_date": "2024-01-20",
            "last_transaction": "2024-01-25"
        },
        {
            "id": "C004",
            "customer_id": "C004",
            "name": "عائشة علي محمد",
            "company": "شركة علي للاستيراد",
            "phone": "*********",
            "email": "<EMAIL>",
            "governorate": "تعز",
            "city": "التربة",
            "address": "شارع جمال عبدالناصر، بجانب المسجد الكبير",
            "category": "عميل تجاري",
            "credit_limit": 75000.0,
            "discount_rate": 7.5,
            "balance": 30000.0,
            "status": "نشط",
            "registration_date": "2024-02-10",
            "last_transaction": "2024-02-15"
        },
        {
            "id": "C005",
            "customer_id": "C005",
            "name": "خالد أحمد الزبيري",
            "company": "مؤسسة الزبيري التجارية",
            "phone": "*********",
            "email": "<EMAIL>",
            "governorate": "الحديدة",
            "city": "الحديدة",
            "address": "كورنيش الحديدة، عمارة البحر الأحمر",
            "category": "عميل مميز",
            "credit_limit": 60000.0,
            "discount_rate": 6.0,
            "balance": 18000.0,
            "status": "نشط",
            "registration_date": "2024-01-25",
            "last_transaction": "2024-01-30"
        },
        {
            "id": "C006",
            "customer_id": "C006",
            "name": "نادية سعد الدين",
            "company": "مكتب سعد الدين للخدمات",
            "phone": "*********",
            "email": "<EMAIL>",
            "governorate": "إب",
            "city": "إب",
            "address": "شارع الوحدة، مجمع الأندلس التجاري",
            "category": "عميل عادي",
            "credit_limit": 25000.0,
            "discount_rate": 3.0,
            "balance": 8000.0,
            "status": "نشط",
            "registration_date": "2024-02-05",
            "last_transaction": "2024-02-08"
        },
        {
            "id": "C007",
            "customer_id": "C007",
            "name": "عبدالرحمن محمد الأهدل",
            "company": "شركة الأهدل للاستثمار",
            "phone": "*********",
            "email": "<EMAIL>",
            "governorate": "ذمار",
            "city": "ذمار",
            "address": "شارع الثورة، بجانب مستشفى الثورة",
            "category": "عميل VIP",
            "credit_limit": 120000.0,
            "discount_rate": 12.0,
            "balance": 45000.0,
            "status": "نشط",
            "registration_date": "2024-01-30",
            "last_transaction": "2024-02-02"
        },
        {
            "id": "C008",
            "customer_id": "C008",
            "name": "سميرة عبدالله باشا",
            "company": "مؤسسة باشا التجارية",
            "phone": "*********",
            "email": "<EMAIL>",
            "governorate": "حضرموت",
            "city": "المكلا",
            "address": "شارع الكورنيش، برج المكلا التجاري",
            "category": "عميل تجاري",
            "credit_limit": 80000.0,
            "discount_rate": 8.0,
            "balance": 35000.0,
            "status": "نشط",
            "registration_date": "2024-02-15",
            "last_transaction": "2024-02-18"
        }
    ]
    
    self.customers_data = sample_customers
    self.update_customers_table()
    self.update_categories_table()
    self.update_status_bar()

    # تحديث جدول بيانات العملاء الجديد
    if hasattr(self, 'customer_data_table'):
        self.update_customer_data_table()
        self.update_customer_data_stats()

def update_customers_table_function(self):
    """تحديث جدول العملاء"""
    self.customers_table.setRowCount(len(self.customers_data))
    
    for row, customer in enumerate(self.customers_data):
        self.customers_table.setItem(row, 0, QTableWidgetItem(customer["id"]))
        self.customers_table.setItem(row, 1, QTableWidgetItem(customer["name"]))
        self.customers_table.setItem(row, 2, QTableWidgetItem(customer["phone"]))
        self.customers_table.setItem(row, 3, QTableWidgetItem(customer["email"]))
        self.customers_table.setItem(row, 4, QTableWidgetItem(f"{customer['governorate']} - {customer['city']}"))
        self.customers_table.setItem(row, 5, QTableWidgetItem(customer["category"]))
        self.customers_table.setItem(row, 6, QTableWidgetItem(f"{customer['balance']:,.0f} ريال"))
        self.customers_table.setItem(row, 7, QTableWidgetItem(customer["registration_date"]))

def update_categories_table_function(self):
    """تحديث جدول التصنيفات"""
    # حساب إحصائيات التصنيفات
    categories_stats = {}
    for customer in self.customers_data:
        category = customer["category"]
        if category not in categories_stats:
            categories_stats[category] = {
                "count": 0,
                "total_discount": 0,
                "total_credit": 0
            }
        categories_stats[category]["count"] += 1
        categories_stats[category]["total_discount"] += customer["discount_rate"]
        categories_stats[category]["total_credit"] += customer["credit_limit"]
    
    # تحديث الجدول
    self.categories_table.setRowCount(len(categories_stats))
    
    categories_info = {
        "عميل عادي": "عملاء عاديون بخدمات أساسية",
        "عميل مميز": "عملاء مميزون بخصومات إضافية",
        "عميل VIP": "عملاء كبار بخدمات متميزة",
        "عميل تجاري": "عملاء تجاريون بحدود ائتمان عالية"
    }
    
    for row, (category, stats) in enumerate(categories_stats.items()):
        avg_discount = stats["total_discount"] / stats["count"] if stats["count"] > 0 else 0
        avg_credit = stats["total_credit"] / stats["count"] if stats["count"] > 0 else 0
        
        self.categories_table.setItem(row, 0, QTableWidgetItem(category))
        self.categories_table.setItem(row, 1, QTableWidgetItem(categories_info.get(category, "")))
        self.categories_table.setItem(row, 2, QTableWidgetItem(f"{avg_discount:.1f}%"))
        self.categories_table.setItem(row, 3, QTableWidgetItem(f"{avg_credit:,.0f} ريال"))
        self.categories_table.setItem(row, 4, QTableWidgetItem(str(stats["count"])))

def update_status_bar_function(self):
    """تحديث شريط الحالة"""
    total_customers = len(self.customers_data)
    self.customers_count_label.setText(f"عدد العملاء: {total_customers}")

def filter_customers_function(self):
    """تصفية العملاء حسب البحث والتصنيف"""
    search_text = self.search_input.text().lower()
    selected_category = self.category_filter.currentText()
    
    filtered_data = []
    for customer in self.customers_data:
        # فلترة النص
        text_match = (search_text in customer["name"].lower() or 
                     search_text in customer["phone"] or 
                     search_text in customer["email"].lower())
        
        # فلترة التصنيف
        category_match = (selected_category == "الكل" or 
                         customer["category"] == selected_category)
        
        if text_match and category_match:
            filtered_data.append(customer)
    
    # تحديث الجدول بالبيانات المفلترة
    self.customers_table.setRowCount(len(filtered_data))
    
    for row, customer in enumerate(filtered_data):
        self.customers_table.setItem(row, 0, QTableWidgetItem(customer["id"]))
        self.customers_table.setItem(row, 1, QTableWidgetItem(customer["name"]))
        self.customers_table.setItem(row, 2, QTableWidgetItem(customer["phone"]))
        self.customers_table.setItem(row, 3, QTableWidgetItem(customer["email"]))
        self.customers_table.setItem(row, 4, QTableWidgetItem(f"{customer['governorate']} - {customer['city']}"))
        self.customers_table.setItem(row, 5, QTableWidgetItem(customer["category"]))
        self.customers_table.setItem(row, 6, QTableWidgetItem(f"{customer['balance']:,.0f} ريال"))
        self.customers_table.setItem(row, 7, QTableWidgetItem(customer["registration_date"]))

def clear_search_function(self):
    """مسح البحث"""
    self.search_input.clear()
    self.category_filter.setCurrentText("الكل")
    self.update_customers_table()

def new_customer_function(self):
    """إضافة عميل جديد"""
    # الانتقال إلى تبويب النموذج
    self.tabs.setCurrentIndex(1)
    
    # مسح النموذج
    self.clear_form()
    
    # إنشاء رقم عميل جديد
    new_id = f"C{len(self.customers_data) + 1:03d}"
    self.customer_id_input.setText(new_id)

def clear_form_function(self):
    """مسح نموذج العميل"""
    self.customer_id_input.clear()
    self.customer_name_input.clear()
    self.phone_input.clear()
    self.email_input.clear()
    self.birth_date_input.setDate(QDate.currentDate().addYears(-25))
    self.gender_input.setCurrentIndex(0)
    self.governorate_input.setCurrentIndex(0)
    self.city_input.clear()
    self.address_input.clear()
    self.phone2_input.clear()
    self.website_input.clear()
    self.category_input.setCurrentIndex(0)
    self.credit_limit_input.setValue(0)
    self.discount_rate_input.setValue(0)
    self.payment_method_input.setCurrentIndex(0)
    self.notes_input.clear()
