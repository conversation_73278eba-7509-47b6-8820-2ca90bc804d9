#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار السند المبسط (بدون عمود الدائن)
Simplified Voucher Test (Without Credit Column)
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QPushButton, QWidget, QLabel, QMessageBox, QTextEdit
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SimplifiedVoucherTestWindow(QMainWindow):
    """نافذة اختبار السند المبسط"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_mock_db()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار السند المبسط - بدون عمود الدائن")
        self.setGeometry(200, 200, 900, 700)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QMainWindow {
                background-color: #fdf2f8;
                font-family: "Tahoma", "Arial", sans-serif;
            }
            QPushButton {
                background-color: #e91e63;
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                margin: 10px;
                min-width: 300px;
            }
            QPushButton:hover {
                background-color: #c2185b;
            }
            QPushButton:pressed {
                background-color: #ad1457;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 14px;
                color: #2c3e50;
                padding: 10px;
            }
            QTextEdit {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 12px;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("📄 اختبار السند المبسط - بدون عمود الدائن")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 22px;
                color: #e91e63;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 10px;
                padding: 20px;
                margin: 20px;
                text-align: center;
            }
        """)
        layout.addWidget(title_label)
        
        # أزرار الاختبار
        buttons_layout = QVBoxLayout()
        
        # اختبار سند القبض المبسط
        receipt_btn = QPushButton("📄 سند قبض مبسط")
        receipt_btn.clicked.connect(self.test_receipt_simplified)
        buttons_layout.addWidget(receipt_btn)
        
        # اختبار سند الصرف المبسط
        payment_btn = QPushButton("📄 سند صرف مبسط")
        payment_btn.clicked.connect(self.test_payment_simplified)
        buttons_layout.addWidget(payment_btn)
        
        # اختبار مع بيانات تجريبية
        sample_data_btn = QPushButton("📋 اختبار مع بيانات تجريبية")
        sample_data_btn.clicked.connect(self.test_with_sample_data)
        buttons_layout.addWidget(sample_data_btn)
        
        layout.addLayout(buttons_layout)
        
        # معلومات التحديث
        info = QTextEdit()
        info.setReadOnly(True)
        info.setMaximumHeight(350)
        info.setHtml("""
        <h3 style="color: #e91e63;">📋 التحديثات المطبقة على السند:</h3>
        
        <h4 style="color: #27ae60;">✅ تبسيط الجدول:</h4>
        <ul>
            <li><strong>حذف عمود الدائن:</strong> لأن السند وليس قيد محاسبي</li>
            <li><strong>عمود واحد للمبلغ:</strong> أبسط وأوضح</li>
            <li><strong>تركيز على الغرض:</strong> سند قبض أو صرف فقط</li>
        </ul>
        
        <h4 style="color: #3498db;">📊 تخطيط الجدول الجديد:</h4>
        <table border="1" style="border-collapse: collapse; width: 100%;">
            <tr style="background-color: #34495e; color: white;">
                <th>م</th>
                <th>رقم الحساب</th>
                <th>اسم الحساب</th>
                <th>البيان</th>
                <th>المبلغ</th>
                <th>مركز التكلفة</th>
                <th>ملاحظات</th>
            </tr>
            <tr>
                <td>60px</td>
                <td>150px</td>
                <td>250px</td>
                <td>مرن</td>
                <td>150px</td>
                <td>120px</td>
                <td>مرن</td>
            </tr>
        </table>
        
        <h4 style="color: #9b59b6;">💰 قسم الإجماليات المحدث:</h4>
        <ul>
            <li><strong>إجمالي المقبوض/المصروف:</strong> حسب نوع السند</li>
            <li><strong>عدد البنود:</strong> عدد الحسابات في السند</li>
            <li><strong>حالة السند:</strong> صحيح أو فارغ</li>
        </ul>
        
        <h4 style="color: #e74c3c;">🎯 الفوائد المحققة:</h4>
        <ul>
            <li>✅ <strong>بساطة أكثر:</strong> عمود واحد للمبلغ</li>
            <li>✅ <strong>وضوح أكبر:</strong> لا التباس في الاستخدام</li>
            <li>✅ <strong>سرعة في الإدخال:</strong> أقل خطوات</li>
            <li>✅ <strong>مناسب للغرض:</strong> سند وليس قيد</li>
            <li>✅ <strong>مساحة أكبر:</strong> للأعمدة المهمة</li>
        </ul>
        
        <h4 style="color: #f39c12;">📝 مثال على السند:</h4>
        <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
سند قبض رقم: RC2025000001
التاريخ: 2025/01/28
المستفيد: شركة الأمل للتجارة

تفاصيل السند:
1. 1010001 - صندوق المركز الرئيسي - مبلغ مقبوض نقداً - 15,000.00
2. 1030001 - العملاء المحليين - تحصيل من العميل - 15,000.00

إجمالي المقبوض: 30,000.00
عدد البنود: 2
حالة السند: سند صحيح
        </pre>
        """)
        layout.addWidget(info)
        
        central_widget.setLayout(layout)
    
    def setup_mock_db(self):
        """إعداد قاعدة بيانات وهمية للاختبار"""
        class MockDBManager:
            def __init__(self):
                self.cursor = type('obj', (object,), {'lastrowid': 1})()
            
            def execute_query(self, query, params=None):
                # محاكاة نتائج الاستعلام
                if "MAX" in query:
                    return [(0,)]
                elif "chart_of_accounts" in query:
                    # إرجاع بيانات الحسابات التجريبية
                    return [
                        ('1010001', 'صندوق المركز الرئيسي', 'أصول متداولة', 1, 3, '101000'),
                        ('1020001', 'البنك الأهلي التجاري', 'أصول متداولة', 1, 3, '102000'),
                        ('1030001', 'العملاء المحليين', 'أصول متداولة', 1, 3, '103000'),
                        ('2010001', 'الموردين المحليين', 'خصوم متداولة', 1, 3, '201000'),
                        ('4010001', 'مبيعات محلية', 'إيرادات', 1, 3, '401000'),
                        ('5010001', 'مصروفات عمومية', 'مصروفات', 1, 3, '501000'),
                    ]
                return []
            
            def execute_update(self, query, params=None):
                return True
        
        self.db_manager = MockDBManager()
    
    def test_receipt_simplified(self):
        """اختبار سند القبض المبسط"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            receipt_dialog = VoucherInterface(self.db_manager, "receipt", self)
            receipt_dialog.show()
            
            QMessageBox.information(
                self, 
                "سند القبض المبسط",
                "✅ تم فتح سند القبض المبسط\n\n"
                "📊 التحديثات:\n"
                "• حذف عمود الدائن\n"
                "• عمود واحد للمبلغ (150px)\n"
                "• إجمالي المقبوض بدلاً من مدين/دائن\n"
                "• عدد البنود في السند\n"
                "• حالة السند (صحيح/فارغ)\n\n"
                "🔍 جرب:\n"
                "• إضافة حسابات مختلفة\n"
                "• استخدام F9 للبحث\n"
                "• إدخال مبالغ مختلفة\n"
                "• لاحظ بساطة الاستخدام"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند القبض:\n{str(e)}")
    
    def test_payment_simplified(self):
        """اختبار سند الصرف المبسط"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            payment_dialog = VoucherInterface(self.db_manager, "payment", self)
            payment_dialog.show()
            
            QMessageBox.information(
                self, 
                "سند الصرف المبسط",
                "✅ تم فتح سند الصرف المبسط\n\n"
                "📊 التحديثات:\n"
                "• حذف عمود الدائن\n"
                "• عمود واحد للمبلغ (150px)\n"
                "• إجمالي المصروف بدلاً من مدين/دائن\n"
                "• عدد البنود في السند\n"
                "• حالة السند (صحيح/فارغ)\n\n"
                "🔍 جرب:\n"
                "• إضافة حسابات مختلفة\n"
                "• استخدام F9 للبحث\n"
                "• إدخال مبالغ مختلفة\n"
                "• لاحظ بساطة الاستخدام"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند الصرف:\n{str(e)}")
    
    def test_with_sample_data(self):
        """اختبار مع بيانات تجريبية"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            from PyQt5.QtWidgets import QTableWidgetItem
            from PyQt5.QtCore import Qt
            
            # إنشاء سند قبض
            receipt_dialog = VoucherInterface(self.db_manager, "receipt", self)
            
            # تعبئة بيانات تجريبية
            receipt_dialog.beneficiary_edit.setText("شركة النجاح التجارية")
            receipt_dialog.beneficiary_id_edit.setText("C002")
            receipt_dialog.description_edit.setPlainText("سند قبض - تحصيل فواتير مبيعات")
            
            # إضافة بيانات للجدول
            table = receipt_dialog.details_table
            
            # الصف الأول - النقدية
            table.setItem(0, 1, QTableWidgetItem("1010001"))
            table.setItem(0, 2, QTableWidgetItem("صندوق المركز الرئيسي"))
            table.setItem(0, 3, QTableWidgetItem("مبلغ مقبوض نقداً"))
            amount_item1 = QTableWidgetItem("25,000.00")
            amount_item1.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            table.setItem(0, 4, amount_item1)
            table.setItem(0, 5, QTableWidgetItem("المركز الرئيسي"))
            
            # إضافة صف ثاني
            receipt_dialog.add_detail_row()
            table.setItem(1, 1, QTableWidgetItem("1020001"))
            table.setItem(1, 2, QTableWidgetItem("البنك الأهلي التجاري"))
            table.setItem(1, 3, QTableWidgetItem("تحويل بنكي"))
            amount_item2 = QTableWidgetItem("15,000.00")
            amount_item2.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            table.setItem(1, 4, amount_item2)
            table.setItem(1, 5, QTableWidgetItem("المركز الرئيسي"))
            
            # حساب الإجماليات
            receipt_dialog.calculate_totals()
            
            receipt_dialog.show()
            
            QMessageBox.information(
                self, 
                "بيانات تجريبية",
                "✅ تم تعبئة السند ببيانات تجريبية\n\n"
                "📋 البيانات المعبأة:\n"
                "• المستفيد: شركة النجاح التجارية\n"
                "• إجمالي المقبوض: 40,000.00 ريال\n"
                "• عدد البنود: 2\n"
                "• الحسابات: صندوق + بنك\n\n"
                "📊 لاحظ التحسينات:\n"
                "• بساطة الجدول (عمود واحد للمبلغ)\n"
                "• وضوح الإجماليات\n"
                "• عدد البنود واضح\n"
                "• حالة السند محددة\n"
                "• سهولة الاستخدام"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء البيانات التجريبية:\n{str(e)}")


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont("Tahoma", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = SimplifiedVoucherTestWindow()
    window.show()
    
    print("📄 تم تشغيل اختبار السند المبسط!")
    print("✅ التحديثات المطبقة:")
    print("   ❌ حذف عمود الدائن")
    print("   📊 عمود واحد للمبلغ (150px)")
    print("   💰 إجمالي المقبوض/المصروف")
    print("   📈 عدد البنود في السند")
    print("   ✅ حالة السند (صحيح/فارغ)")
    print("   🎯 تبسيط الاستخدام")
    print("")
    print("💡 الفوائد:")
    print("   • أبسط في الاستخدام")
    print("   • أوضح في الغرض")
    print("   • أسرع في الإدخال")
    print("   • مناسب لطبيعة السند")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
