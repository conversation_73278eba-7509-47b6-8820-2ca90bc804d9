#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة خصوصية المستخدم
User Privacy Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QDoubleSpinBox, QCheckBox,
                             QDateEdit, QSplitter, QFrame, QDialogButtonBox,
                             QScrollArea, QProgressBar, QListWidget, QListWidgetItem,
                             QSlider, QRadioButton, QButtonGroup, QFileDialog)
from PyQt5.QtCore import Qt, QDate, pyqt<PERSON><PERSON>al, QTimer
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QPixmap, QPainter
from datetime import datetime, timedelta
import json
import hashlib
import os

try:
    from ..database.sqlite_manager import SQLiteManager
except ImportError:
    # في حالة التشغيل المباشر
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class PasswordChangeDialog(QDialog):
    """حوار تغيير كلمة المرور"""
    
    def __init__(self, db_manager: SQLiteManager, user_id, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_id = user_id
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔐 تغيير كلمة المرور")
        self.setFixedSize(450, 350)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🔐 تغيير كلمة المرور")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 15px;
            
            }
        """)
        main_layout.addWidget(title_label)
        
        # النموذج
        form_layout = QFormLayout()
        
        # كلمة المرور الحالية
        self.current_password_edit = QLineEdit()
        self.current_password_edit.setEchoMode(QLineEdit.Password)
        self.current_password_edit.setPlaceholderText("أدخل كلمة المرور الحالية")
        form_layout.addRow("كلمة المرور الحالية:", self.current_password_edit)
        
        # كلمة المرور الجديدة
        self.new_password_edit = QLineEdit()
        self.new_password_edit.setEchoMode(QLineEdit.Password)
        self.new_password_edit.setPlaceholderText("أدخل كلمة المرور الجديدة")
        form_layout.addRow("كلمة المرور الجديدة:", self.new_password_edit)
        
        # تأكيد كلمة المرور الجديدة
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        self.confirm_password_edit.setPlaceholderText("أكد كلمة المرور الجديدة")
        form_layout.addRow("تأكيد كلمة المرور:", self.confirm_password_edit)
        
        main_layout.addLayout(form_layout)
        
        # مؤشر قوة كلمة المرور
        password_strength_group = QGroupBox("قوة كلمة المرور")
        strength_layout = QVBoxLayout()
        
        self.strength_bar = QProgressBar()
        self.strength_bar.setRange(0, 100)
        self.strength_bar.setValue(0)
        strength_layout.addWidget(self.strength_bar)
        
        self.strength_label = QLabel("ضعيفة")
        self.strength_label.setAlignment(Qt.AlignCenter)
        strength_layout.addWidget(self.strength_label)
        
        # نصائح كلمة المرور
        tips_label = QLabel("""
💡 نصائح لكلمة مرور قوية:
• استخدم 8 أحرف على الأقل
• امزج بين الأحرف الكبيرة والصغيرة
• أضف أرقام ورموز خاصة
• تجنب المعلومات الشخصية
        """)
        tips_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-size: 11px;
            
            }
        """)
        strength_layout.addWidget(tips_label)
        
        password_strength_group.setLayout(strength_layout)
        main_layout.addWidget(password_strength_group)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        main_layout.addWidget(button_box)
        
        self.setLayout(main_layout)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.change_password)
        self.cancel_btn.clicked.connect(self.reject)
        self.new_password_edit.textChanged.connect(self.check_password_strength)
    
    def check_password_strength(self):
        """فحص قوة كلمة المرور"""
        password = self.new_password_edit.text()
        strength = 0
        
        # طول كلمة المرور
        if len(password) >= 8:
            strength += 25
        elif len(password) >= 6:
            strength += 15
        
        # الأحرف الكبيرة والصغيرة
        if any(c.isupper() for c in password) and any(c.islower() for c in password):
            strength += 25
        
        # الأرقام
        if any(c.isdigit() for c in password):
            strength += 25
        
        # الرموز الخاصة
        if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            strength += 25
        
        self.strength_bar.setValue(strength)
        
        if strength < 25:
            self.strength_label.setText("ضعيفة جداً")
            self.strength_bar.setStyleSheet("QProgressBar::chunk { background-color: #e74c3c; }")
        elif strength < 50:
            self.strength_label.setText("ضعيفة")
            self.strength_bar.setStyleSheet("QProgressBar::chunk { background-color: #f39c12; }")
        elif strength < 75:
            self.strength_label.setText("متوسطة")
            self.strength_bar.setStyleSheet("QProgressBar::chunk { background-color: #f1c40f; }")
        else:
            self.strength_label.setText("قوية")
            self.strength_bar.setStyleSheet("QProgressBar::chunk { background-color: #27ae60; }")
    
    def change_password(self):
        """تغيير كلمة المرور"""
        current_password = self.current_password_edit.text()
        new_password = self.new_password_edit.text()
        confirm_password = self.confirm_password_edit.text()
        
        # التحقق من صحة البيانات
        if not current_password:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كلمة المرور الحالية")
            return
        
        if not new_password:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كلمة المرور الجديدة")
            return
        
        if new_password != confirm_password:
            QMessageBox.warning(self, "تحذير", "كلمة المرور الجديدة وتأكيدها غير متطابقين")
            return
        
        if len(new_password) < 6:
            QMessageBox.warning(self, "تحذير", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return
        
        try:
            # التحقق من كلمة المرور الحالية
            user_query = "SELECT password_hash FROM users WHERE id = ?"
            user_result = self.db_manager.execute_query(user_query, (self.user_id,))
            
            if not user_result:
                QMessageBox.critical(self, "خطأ", "المستخدم غير موجود")
                return
            
            stored_hash = user_result[0][0]
            current_hash = hashlib.sha256(current_password.encode()).hexdigest()
            
            if stored_hash != current_hash:
                QMessageBox.warning(self, "تحذير", "كلمة المرور الحالية غير صحيحة")
                return
            
            # تحديث كلمة المرور
            new_hash = hashlib.sha256(new_password.encode()).hexdigest()
            update_query = """
                UPDATE users SET 
                    password_hash = ?, 
                    password_changed_at = ?,
                    updated_at = ?
                WHERE id = ?
            """
            
            self.db_manager.execute_update(update_query, (
                new_hash,
                datetime.now().isoformat(),
                datetime.now().isoformat(),
                self.user_id
            ))
            
            QMessageBox.information(self, "نجح", "تم تغيير كلمة المرور بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تغيير كلمة المرور:\n{str(e)}")


class DataExportDialog(QDialog):
    """حوار تصدير البيانات الشخصية"""
    
    def __init__(self, db_manager: SQLiteManager, user_id, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_id = user_id
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📤 تصدير البيانات الشخصية")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("📤 تصدير البيانات الشخصية")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 15px;
            
            }
        """)
        main_layout.addWidget(title_label)
        
        # اختيار البيانات للتصدير
        data_group = QGroupBox("اختر البيانات للتصدير")
        data_layout = QVBoxLayout()
        
        self.export_profile = QCheckBox("البيانات الشخصية")
        self.export_profile.setChecked(True)
        data_layout.addWidget(self.export_profile)
        
        self.export_activities = QCheckBox("سجل النشاطات")
        self.export_activities.setChecked(True)
        data_layout.addWidget(self.export_activities)
        
        self.export_settings = QCheckBox("الإعدادات والتفضيلات")
        self.export_settings.setChecked(True)
        data_layout.addWidget(self.export_settings)
        
        self.export_permissions = QCheckBox("الصلاحيات والأدوار")
        self.export_permissions.setChecked(True)
        data_layout.addWidget(self.export_permissions)
        
        data_group.setLayout(data_layout)
        main_layout.addWidget(data_group)
        
        # تنسيق التصدير
        format_group = QGroupBox("تنسيق التصدير")
        format_layout = QVBoxLayout()
        
        self.format_group = QButtonGroup()
        
        self.json_radio = QRadioButton("JSON")
        self.json_radio.setChecked(True)
        self.format_group.addButton(self.json_radio)
        format_layout.addWidget(self.json_radio)
        
        self.csv_radio = QRadioButton("CSV")
        self.format_group.addButton(self.csv_radio)
        format_layout.addWidget(self.csv_radio)
        
        self.txt_radio = QRadioButton("نص عادي")
        self.format_group.addButton(self.txt_radio)
        format_layout.addWidget(self.txt_radio)
        
        format_group.setLayout(format_layout)
        main_layout.addWidget(format_group)
        
        # معلومات إضافية
        info_label = QLabel("""
ℹ️ معلومات مهمة:
• سيتم تصدير البيانات المحددة فقط
• البيانات ستكون مشفرة ومحمية
• يمكنك استخدام هذه البيانات لأغراض شخصية
• لن يتم تضمين كلمات المرور في التصدير
        """)
        info_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                background-color: #e8f4fd;
                border: 1px solid #bee5eb;
                border-radius: 5px;
                padding: 10px;
                font-size: 11px;
            
            }
        """)
        main_layout.addWidget(info_label)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.export_btn = button_box.addButton("تصدير", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        main_layout.addWidget(button_box)
        
        self.setLayout(main_layout)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.export_btn.clicked.connect(self.export_data)
        self.cancel_btn.clicked.connect(self.reject)
    
    def export_data(self):
        """تصدير البيانات"""
        try:
            # اختيار مكان الحفظ
            file_extension = "json" if self.json_radio.isChecked() else "csv" if self.csv_radio.isChecked() else "txt"
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "حفظ البيانات الشخصية",
                f"my_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{file_extension}",
                f"{file_extension.upper()} Files (*.{file_extension})"
            )
            
            if not file_path:
                return
            
            # جمع البيانات
            export_data = {
                "export_date": datetime.now().isoformat(),
                "user_id": self.user_id,
                "data_types": []
            }
            
            if self.export_profile.isChecked():
                profile_data = self.get_profile_data()
                export_data["profile"] = profile_data
                export_data["data_types"].append("profile")
            
            if self.export_activities.isChecked():
                activities_data = self.get_activities_data()
                export_data["activities"] = activities_data
                export_data["data_types"].append("activities")
            
            if self.export_settings.isChecked():
                settings_data = self.get_settings_data()
                export_data["settings"] = settings_data
                export_data["data_types"].append("settings")
            
            if self.export_permissions.isChecked():
                permissions_data = self.get_permissions_data()
                export_data["permissions"] = permissions_data
                export_data["data_types"].append("permissions")
            
            # حفظ البيانات
            if self.json_radio.isChecked():
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)
            elif self.csv_radio.isChecked():
                self.save_as_csv(export_data, file_path)
            else:
                self.save_as_text(export_data, file_path)
            
            QMessageBox.information(self, "نجح", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير البيانات:\n{str(e)}")
    
    def get_profile_data(self):
        """الحصول على البيانات الشخصية"""
        query = """
            SELECT username, full_name, email, phone, department, 
                   position, is_active, created_at, last_login
            FROM users WHERE id = ?
        """
        result = self.db_manager.execute_query(query, (self.user_id,))
        
        if result:
            user = result[0]
            return {
                "username": user[0],
                "full_name": user[1],
                "email": user[2],
                "phone": user[3],
                "department": user[4],
                "position": user[5],
                "is_active": user[6],
                "created_at": user[7],
                "last_login": user[8]
            }
        return {}
    
    def get_activities_data(self):
        """الحصول على سجل النشاطات"""
        query = """
            SELECT action, description, timestamp, ip_address
            FROM activity_log WHERE user_id = ?
            ORDER BY timestamp DESC LIMIT 100
        """
        result = self.db_manager.execute_query(query, (self.user_id,))
        
        activities = []
        for activity in result:
            activities.append({
                "action": activity[0],
                "description": activity[1],
                "timestamp": activity[2],
                "ip_address": activity[3]
            })
        
        return activities
    
    def get_settings_data(self):
        """الحصول على الإعدادات"""
        # هذا مثال - يمكن تطويره حسب الحاجة
        return {
            "language": "ar",
            "theme": "default",
            "notifications": True
        }
    
    def get_permissions_data(self):
        """الحصول على الصلاحيات"""
        query = """
            SELECT role_name, permissions
            FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = ?
        """
        result = self.db_manager.execute_query(query, (self.user_id,))
        
        permissions = []
        for perm in result:
            permissions.append({
                "role": perm[0],
                "permissions": perm[1]
            })
        
        return permissions
    
    def save_as_csv(self, data, file_path):
        """حفظ البيانات كـ CSV"""
        import csv
        
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(["نوع البيانات", "المحتوى"])
            
            for key, value in data.items():
                if key != "data_types":
                    writer.writerow([key, str(value)])
    
    def save_as_text(self, data, file_path):
        """حفظ البيانات كنص عادي"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("البيانات الشخصية\n")
            f.write("=" * 50 + "\n\n")
            
            for key, value in data.items():
                if key != "data_types":
                    f.write(f"{key}: {value}\n\n")


class UserPrivacyWidget(QWidget):
    """وحدة خصوصية المستخدم الرئيسية"""

    def __init__(self, db_manager: SQLiteManager, current_user_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_user_id = current_user_id or 1  # افتراضي
        
        self.setup_ui()
        self.load_privacy_settings()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # العنوان
        title = QLabel("🔒 خصوصية المستخدم")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        layout.addWidget(title)

        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب إعدادات الخصوصية
        self.privacy_settings_tab = self.create_privacy_settings_tab()
        self.tabs.addTab(self.privacy_settings_tab, "🔒 إعدادات الخصوصية")
        
        # تبويب كلمة المرور
        self.password_tab = self.create_password_tab()
        self.tabs.addTab(self.password_tab, "🔐 كلمة المرور")
        
        # تبويب البيانات الشخصية
        self.data_tab = self.create_data_tab()
        self.tabs.addTab(self.data_tab, "📊 البيانات الشخصية")
        
        # تبويب سجل النشاطات
        self.activity_tab = self.create_activity_tab()
        self.tabs.addTab(self.activity_tab, "📋 سجل النشاطات")

        layout.addWidget(self.tabs)
        self.setLayout(layout)

    def create_privacy_settings_tab(self):
        """إنشاء تبويب إعدادات الخصوصية"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة إعدادات الحساب
        account_group = QGroupBox("🔐 إعدادات الحساب")
        account_layout = QGridLayout()

        # تفعيل المصادقة الثنائية
        self.two_factor_check = QCheckBox("تفعيل المصادقة الثنائية")
        account_layout.addWidget(self.two_factor_check, 0, 0, 1, 2)

        # انتهاء صلاحية كلمة المرور
        account_layout.addWidget(QLabel("انتهاء صلاحية كلمة المرور:"), 1, 0)
        self.password_expiry_combo = QComboBox()
        self.password_expiry_combo.addItems([
            "لا تنتهي", "30 يوم", "60 يوم", "90 يوم", "180 يوم", "365 يوم"
        ])
        account_layout.addWidget(self.password_expiry_combo, 1, 1)

        # تسجيل الخروج التلقائي
        account_layout.addWidget(QLabel("تسجيل الخروج التلقائي:"), 2, 0)
        self.auto_logout_combo = QComboBox()
        self.auto_logout_combo.addItems([
            "معطل", "15 دقيقة", "30 دقيقة", "1 ساعة", "2 ساعة", "4 ساعات"
        ])
        account_layout.addWidget(self.auto_logout_combo, 2, 1)

        account_group.setLayout(account_layout)
        layout.addWidget(account_group)

        # مجموعة إعدادات البيانات
        data_group = QGroupBox("📊 إعدادات البيانات")
        data_layout = QVBoxLayout()

        self.data_collection_check = QCheckBox("السماح بجمع بيانات الاستخدام لتحسين النظام")
        data_layout.addWidget(self.data_collection_check)

        self.analytics_check = QCheckBox("السماح بتحليل النشاطات لأغراض إحصائية")
        data_layout.addWidget(self.analytics_check)

        self.backup_check = QCheckBox("تضمين بياناتي في النسخ الاحتياطية")
        self.backup_check.setChecked(True)
        data_layout.addWidget(self.backup_check)

        data_group.setLayout(data_layout)
        layout.addWidget(data_group)

        # مجموعة إعدادات الإشعارات
        notifications_group = QGroupBox("🔔 إعدادات الإشعارات")
        notifications_layout = QVBoxLayout()

        self.login_notifications_check = QCheckBox("إشعار عند تسجيل الدخول من جهاز جديد")
        self.login_notifications_check.setChecked(True)
        notifications_layout.addWidget(self.login_notifications_check)

        self.security_notifications_check = QCheckBox("إشعارات الأمان والخصوصية")
        self.security_notifications_check.setChecked(True)
        notifications_layout.addWidget(self.security_notifications_check)

        self.system_notifications_check = QCheckBox("إشعارات النظام والتحديثات")
        notifications_layout.addWidget(self.system_notifications_check)

        notifications_group.setLayout(notifications_layout)
        layout.addWidget(notifications_group)

        # أزرار الحفظ
        buttons_layout = QHBoxLayout()

        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        save_settings_btn.clicked.connect(self.save_privacy_settings)
        save_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        buttons_layout.addWidget(save_settings_btn)

        reset_settings_btn = QPushButton("🔄 إعادة تعيين")
        reset_settings_btn.clicked.connect(self.reset_privacy_settings)
        buttons_layout.addWidget(reset_settings_btn)

        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)

        layout.addStretch()
        tab.setLayout(layout)
        return tab

    def create_password_tab(self):
        """إنشاء تبويب كلمة المرور"""
        tab = QWidget()
        layout = QVBoxLayout()

        # معلومات كلمة المرور الحالية
        current_info_group = QGroupBox("📋 معلومات كلمة المرور الحالية")
        current_info_layout = QFormLayout()

        self.last_changed_label = QLabel("غير محدد")
        current_info_layout.addRow("آخر تغيير:", self.last_changed_label)

        self.password_age_label = QLabel("غير محدد")
        current_info_layout.addRow("عمر كلمة المرور:", self.password_age_label)

        self.strength_status_label = QLabel("غير محدد")
        current_info_layout.addRow("حالة القوة:", self.strength_status_label)

        current_info_group.setLayout(current_info_layout)
        layout.addWidget(current_info_group)

        # أزرار إدارة كلمة المرور
        password_actions_group = QGroupBox("🔧 إجراءات كلمة المرور")
        password_actions_layout = QVBoxLayout()

        change_password_btn = QPushButton("🔐 تغيير كلمة المرور")
        change_password_btn.clicked.connect(self.change_password)
        change_password_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 15px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        password_actions_layout.addWidget(change_password_btn)

        generate_password_btn = QPushButton("🎲 إنتاج كلمة مرور قوية")
        generate_password_btn.clicked.connect(self.generate_strong_password)
        password_actions_layout.addWidget(generate_password_btn)

        password_actions_group.setLayout(password_actions_layout)
        layout.addWidget(password_actions_group)

        # نصائح الأمان
        security_tips_group = QGroupBox("💡 نصائح الأمان")
        security_tips_layout = QVBoxLayout()

        tips_text = QTextEdit()
        tips_text.setReadOnly(True)
        tips_text.setMaximumHeight(150)
        tips_text.setPlainText("""
🔒 نصائح لحماية حسابك:

• غير كلمة المرور بانتظام (كل 3-6 أشهر)
• استخدم كلمة مرور فريدة لا تستخدمها في مواقع أخرى
• لا تشارك كلمة المرور مع أي شخص
• استخدم مدير كلمات المرور لحفظ كلمات مرور قوية
• فعل المصادقة الثنائية إذا كانت متاحة
• سجل خروج من الحساب عند الانتهاء من العمل
• تجنب استخدام معلومات شخصية في كلمة المرور
        """)
        security_tips_layout.addWidget(tips_text)

        security_tips_group.setLayout(security_tips_layout)
        layout.addWidget(security_tips_group)

        layout.addStretch()
        tab.setLayout(layout)
        return tab

    def create_data_tab(self):
        """إنشاء تبويب البيانات الشخصية"""
        tab = QWidget()
        layout = QVBoxLayout()

        # معلومات البيانات المخزنة
        data_info_group = QGroupBox("📊 البيانات المخزنة")
        data_info_layout = QFormLayout()

        self.profile_data_label = QLabel("محملة")
        data_info_layout.addRow("البيانات الشخصية:", self.profile_data_label)

        self.activity_data_label = QLabel("محملة")
        data_info_layout.addRow("سجل النشاطات:", self.activity_data_label)

        self.settings_data_label = QLabel("محملة")
        data_info_layout.addRow("الإعدادات:", self.settings_data_label)

        data_info_group.setLayout(data_info_layout)
        layout.addWidget(data_info_group)

        # إجراءات البيانات
        data_actions_group = QGroupBox("🔧 إجراءات البيانات")
        data_actions_layout = QVBoxLayout()

        export_data_btn = QPushButton("📤 تصدير بياناتي الشخصية")
        export_data_btn.clicked.connect(self.export_personal_data)
        export_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 15px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        data_actions_layout.addWidget(export_data_btn)

        clear_activity_btn = QPushButton("🗑️ مسح سجل النشاطات")
        clear_activity_btn.clicked.connect(self.clear_activity_log)
        data_actions_layout.addWidget(clear_activity_btn)

        data_actions_group.setLayout(data_actions_layout)
        layout.addWidget(data_actions_group)

        # معلومات حقوق البيانات
        rights_group = QGroupBox("⚖️ حقوقك في البيانات")
        rights_layout = QVBoxLayout()

        rights_text = QTextEdit()
        rights_text.setReadOnly(True)
        rights_text.setMaximumHeight(120)
        rights_text.setPlainText("""
📋 حقوقك في البيانات الشخصية:

• الحق في الوصول: يمكنك طلب نسخة من بياناتك المخزنة
• الحق في التصحيح: يمكنك تعديل البيانات غير الصحيحة
• الحق في المحو: يمكنك طلب حذف بياناتك في ظروف معينة
• الحق في النقل: يمكنك تصدير بياناتك بتنسيق قابل للقراءة
• الحق في الاعتراض: يمكنك الاعتراض على معالجة بياناتك
        """)
        rights_layout.addWidget(rights_text)

        rights_group.setLayout(rights_layout)
        layout.addWidget(rights_group)

        layout.addStretch()
        tab.setLayout(layout)
        return tab

    def create_activity_tab(self):
        """إنشاء تبويب سجل النشاطات"""
        tab = QWidget()
        layout = QVBoxLayout()

        # شريط الأدوات
        toolbar = QHBoxLayout()

        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.load_activity_log)
        toolbar.addWidget(refresh_btn)

        clear_btn = QPushButton("🗑️ مسح السجل")
        clear_btn.clicked.connect(self.clear_activity_log)
        toolbar.addWidget(clear_btn)

        export_btn = QPushButton("📤 تصدير")
        export_btn.clicked.connect(self.export_activity_log)
        toolbar.addWidget(export_btn)

        toolbar.addStretch()

        # فلتر التاريخ
        toolbar.addWidget(QLabel("من:"))
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        toolbar.addWidget(self.date_from)

        toolbar.addWidget(QLabel("إلى:"))
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        toolbar.addWidget(self.date_to)

        filter_btn = QPushButton("🔍 فلترة")
        filter_btn.clicked.connect(self.filter_activity_log)
        toolbar.addWidget(filter_btn)

        layout.addLayout(toolbar)

        # جدول سجل النشاطات
        self.activity_table = QTableWidget()
        self.activity_table.setColumnCount(4)
        self.activity_table.setHorizontalHeaderLabels([
            "التاريخ والوقت", "النشاط", "الوصف", "عنوان IP"
        ])

        header = self.activity_table.horizontalHeader()
        header.setStretchLastSection(True)

        layout.addWidget(self.activity_table)

        tab.setLayout(layout)
        return tab

    def load_privacy_settings(self):
        """تحميل إعدادات الخصوصية"""
        try:
            # تحميل إعدادات المستخدم
            query = """
                SELECT two_factor_enabled, password_expiry_days, auto_logout_minutes,
                       data_collection_enabled, analytics_enabled, backup_enabled,
                       login_notifications, security_notifications, system_notifications,
                       password_changed_at
                FROM users WHERE id = ?
            """
            result = self.db_manager.execute_query(query, (self.current_user_id,))

            if result:
                user_settings = result[0]

                # تطبيق الإعدادات على الواجهة
                self.two_factor_check.setChecked(user_settings[0] or False)

                # إعداد انتهاء صلاحية كلمة المرور
                expiry_days = user_settings[1] or 0
                if expiry_days == 0:
                    self.password_expiry_combo.setCurrentText("لا تنتهي")
                elif expiry_days == 30:
                    self.password_expiry_combo.setCurrentText("30 يوم")
                elif expiry_days == 60:
                    self.password_expiry_combo.setCurrentText("60 يوم")
                elif expiry_days == 90:
                    self.password_expiry_combo.setCurrentText("90 يوم")
                elif expiry_days == 180:
                    self.password_expiry_combo.setCurrentText("180 يوم")
                elif expiry_days == 365:
                    self.password_expiry_combo.setCurrentText("365 يوم")

                # إعداد تسجيل الخروج التلقائي
                logout_minutes = user_settings[2] or 0
                if logout_minutes == 0:
                    self.auto_logout_combo.setCurrentText("معطل")
                elif logout_minutes == 15:
                    self.auto_logout_combo.setCurrentText("15 دقيقة")
                elif logout_minutes == 30:
                    self.auto_logout_combo.setCurrentText("30 دقيقة")
                elif logout_minutes == 60:
                    self.auto_logout_combo.setCurrentText("1 ساعة")
                elif logout_minutes == 120:
                    self.auto_logout_combo.setCurrentText("2 ساعة")
                elif logout_minutes == 240:
                    self.auto_logout_combo.setCurrentText("4 ساعات")

                # إعدادات البيانات
                self.data_collection_check.setChecked(user_settings[3] or False)
                self.analytics_check.setChecked(user_settings[4] or False)
                self.backup_check.setChecked(user_settings[5] if user_settings[5] is not None else True)

                # إعدادات الإشعارات
                self.login_notifications_check.setChecked(user_settings[6] if user_settings[6] is not None else True)
                self.security_notifications_check.setChecked(user_settings[7] if user_settings[7] is not None else True)
                self.system_notifications_check.setChecked(user_settings[8] or False)

                # معلومات كلمة المرور
                password_changed_at = user_settings[9]
                if password_changed_at:
                    changed_date = datetime.fromisoformat(password_changed_at)
                    self.last_changed_label.setText(changed_date.strftime("%Y-%m-%d %H:%M"))

                    # حساب عمر كلمة المرور
                    age_days = (datetime.now() - changed_date).days
                    self.password_age_label.setText(f"{age_days} يوم")

                    # تقييم قوة كلمة المرور حسب العمر
                    if age_days < 30:
                        self.strength_status_label.setText("حديثة ✅")
                        self.strength_status_label.setStyleSheet("color: green;")
                    elif age_days < 90:
                        self.strength_status_label.setText("جيدة ⚠️")
                        self.strength_status_label.setStyleSheet("color: orange;")
                    else:
                        self.strength_status_label.setText("قديمة ❌")
                        self.strength_status_label.setStyleSheet("color: red;")
                else:
                    self.last_changed_label.setText("غير محدد")
                    self.password_age_label.setText("غير محدد")
                    self.strength_status_label.setText("غير محدد")

            # تحميل سجل النشاطات
            self.load_activity_log()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل إعدادات الخصوصية:\n{str(e)}")

    def save_privacy_settings(self):
        """حفظ إعدادات الخصوصية"""
        try:
            # تحويل النصوص إلى قيم رقمية
            expiry_text = self.password_expiry_combo.currentText()
            expiry_days = {
                "لا تنتهي": 0,
                "30 يوم": 30,
                "60 يوم": 60,
                "90 يوم": 90,
                "180 يوم": 180,
                "365 يوم": 365
            }.get(expiry_text, 0)

            logout_text = self.auto_logout_combo.currentText()
            logout_minutes = {
                "معطل": 0,
                "15 دقيقة": 15,
                "30 دقيقة": 30,
                "1 ساعة": 60,
                "2 ساعة": 120,
                "4 ساعات": 240
            }.get(logout_text, 0)

            # تحديث قاعدة البيانات
            query = """
                UPDATE users SET
                    two_factor_enabled = ?,
                    password_expiry_days = ?,
                    auto_logout_minutes = ?,
                    data_collection_enabled = ?,
                    analytics_enabled = ?,
                    backup_enabled = ?,
                    login_notifications = ?,
                    security_notifications = ?,
                    system_notifications = ?,
                    updated_at = ?
                WHERE id = ?
            """

            self.db_manager.execute_update(query, (
                self.two_factor_check.isChecked(),
                expiry_days,
                logout_minutes,
                self.data_collection_check.isChecked(),
                self.analytics_check.isChecked(),
                self.backup_check.isChecked(),
                self.login_notifications_check.isChecked(),
                self.security_notifications_check.isChecked(),
                self.system_notifications_check.isChecked(),
                datetime.now().isoformat(),
                self.current_user_id
            ))

            QMessageBox.information(self, "نجح", "تم حفظ إعدادات الخصوصية بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ إعدادات الخصوصية:\n{str(e)}")

    def reset_privacy_settings(self):
        """إعادة تعيين إعدادات الخصوصية"""
        reply = QMessageBox.question(
            self, "تأكيد إعادة التعيين",
            "هل أنت متأكد من إعادة تعيين جميع إعدادات الخصوصية إلى القيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # إعادة تعيين القيم الافتراضية
            self.two_factor_check.setChecked(False)
            self.password_expiry_combo.setCurrentText("90 يوم")
            self.auto_logout_combo.setCurrentText("1 ساعة")
            self.data_collection_check.setChecked(False)
            self.analytics_check.setChecked(False)
            self.backup_check.setChecked(True)
            self.login_notifications_check.setChecked(True)
            self.security_notifications_check.setChecked(True)
            self.system_notifications_check.setChecked(False)

            QMessageBox.information(self, "تم", "تم إعادة تعيين الإعدادات إلى القيم الافتراضية")

    def change_password(self):
        """تغيير كلمة المرور"""
        dialog = PasswordChangeDialog(self.db_manager, self.current_user_id, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_privacy_settings()  # إعادة تحميل البيانات

    def generate_strong_password(self):
        """إنتاج كلمة مرور قوية"""
        import random
        import string

        # مكونات كلمة المرور
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        symbols = "!@#$%^&*()_+-=[]{}|;:,.<>?"

        # إنتاج كلمة مرور قوية
        password = []
        password.append(random.choice(uppercase))  # حرف كبير
        password.append(random.choice(lowercase))  # حرف صغير
        password.append(random.choice(digits))     # رقم
        password.append(random.choice(symbols))    # رمز

        # إضافة أحرف عشوائية
        all_chars = lowercase + uppercase + digits + symbols
        for _ in range(8):  # إجمالي 12 حرف
            password.append(random.choice(all_chars))

        # خلط كلمة المرور
        random.shuffle(password)
        generated_password = ''.join(password)

        # عرض كلمة المرور المُنتجة
        msg = QMessageBox(self)
        msg.setWindowTitle("كلمة مرور قوية")
        msg.setText(f"كلمة المرور المُنتجة:\n\n{generated_password}")
        msg.setDetailedText("""
نصائح لاستخدام كلمة المرور:
• احفظها في مكان آمن
• لا تشاركها مع أي شخص
• استخدمها فقط لهذا النظام
• غيرها بانتظام
        """)

        copy_btn = msg.addButton("📋 نسخ", QMessageBox.ActionRole)
        use_btn = msg.addButton("✅ استخدام", QMessageBox.AcceptRole)
        cancel_btn = msg.addButton("❌ إلغاء", QMessageBox.RejectRole)

        msg.exec_()

        if msg.clickedButton() == copy_btn:
            # نسخ كلمة المرور إلى الحافظة
            from PyQt5.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(generated_password)
            QMessageBox.information(self, "تم", "تم نسخ كلمة المرور إلى الحافظة")
        elif msg.clickedButton() == use_btn:
            # فتح حوار تغيير كلمة المرور مع كلمة المرور المُنتجة
            self.change_password()

    def export_personal_data(self):
        """تصدير البيانات الشخصية"""
        dialog = DataExportDialog(self.db_manager, self.current_user_id, parent=self)
        dialog.exec_()

    def clear_activity_log(self):
        """مسح سجل النشاطات"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح سجل النشاطات؟\nهذا الإجراء لا يمكن التراجع عنه.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                query = "DELETE FROM activity_log WHERE user_id = ?"
                self.db_manager.execute_update(query, (self.current_user_id,))

                QMessageBox.information(self, "تم", "تم مسح سجل النشاطات بنجاح")
                self.load_activity_log()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في مسح سجل النشاطات:\n{str(e)}")

    def load_activity_log(self):
        """تحميل سجل النشاطات"""
        try:
            query = """
                SELECT timestamp, action, description, ip_address
                FROM activity_log
                WHERE user_id = ?
                ORDER BY timestamp DESC
                LIMIT 100
            """

            activities = self.db_manager.execute_query(query, (self.current_user_id,))

            self.activity_table.setRowCount(len(activities))

            for row, activity in enumerate(activities):
                # التاريخ والوقت
                timestamp = activity[0]
                if timestamp:
                    try:
                        dt = datetime.fromisoformat(timestamp)
                        formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                    except:
                        formatted_time = timestamp
                else:
                    formatted_time = "غير محدد"

                self.activity_table.setItem(row, 0, QTableWidgetItem(formatted_time))
                self.activity_table.setItem(row, 1, QTableWidgetItem(activity[1] or ""))
                self.activity_table.setItem(row, 2, QTableWidgetItem(activity[2] or ""))
                self.activity_table.setItem(row, 3, QTableWidgetItem(activity[3] or ""))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل سجل النشاطات:\n{str(e)}")

    def filter_activity_log(self):
        """فلترة سجل النشاطات حسب التاريخ"""
        try:
            date_from = self.date_from.date().toString("yyyy-MM-dd")
            date_to = self.date_to.date().toString("yyyy-MM-dd")

            query = """
                SELECT timestamp, action, description, ip_address
                FROM activity_log
                WHERE user_id = ? AND DATE(timestamp) BETWEEN ? AND ?
                ORDER BY timestamp DESC
            """

            activities = self.db_manager.execute_query(query, (self.current_user_id, date_from, date_to))

            self.activity_table.setRowCount(len(activities))

            for row, activity in enumerate(activities):
                timestamp = activity[0]
                if timestamp:
                    try:
                        dt = datetime.fromisoformat(timestamp)
                        formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                    except:
                        formatted_time = timestamp
                else:
                    formatted_time = "غير محدد"

                self.activity_table.setItem(row, 0, QTableWidgetItem(formatted_time))
                self.activity_table.setItem(row, 1, QTableWidgetItem(activity[1] or ""))
                self.activity_table.setItem(row, 2, QTableWidgetItem(activity[2] or ""))
                self.activity_table.setItem(row, 3, QTableWidgetItem(activity[3] or ""))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فلترة سجل النشاطات:\n{str(e)}")

    def export_activity_log(self):
        """تصدير سجل النشاطات"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "تصدير سجل النشاطات",
                f"activity_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if not file_path:
                return

            query = """
                SELECT timestamp, action, description, ip_address
                FROM activity_log
                WHERE user_id = ?
                ORDER BY timestamp DESC
            """

            activities = self.db_manager.execute_query(query, (self.current_user_id,))

            import csv
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(["التاريخ والوقت", "النشاط", "الوصف", "عنوان IP"])

                for activity in activities:
                    writer.writerow([
                        activity[0] or "",
                        activity[1] or "",
                        activity[2] or "",
                        activity[3] or ""
                    ])

            QMessageBox.information(self, "نجح", f"تم تصدير سجل النشاطات إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير سجل النشاطات:\n{str(e)}")


def main():
    """اختبار الوحدة"""
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        def execute_query(self, query, params=None):
            return []

        def execute_update(self, query, params=None):
            pass

    widget = UserPrivacyWidget(MockDBManager())
    widget.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
