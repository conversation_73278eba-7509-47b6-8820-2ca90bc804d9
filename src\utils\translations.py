#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الترجمة
Translation System
"""

from typing import Dict, Any

class Translator:
    """فئة إدارة الترجمات"""
    
    def __init__(self):
        self.current_language = "ar"
        self.translations = {
            # نافذة تسجيل الدخول
            "login_title": {
                "ar": "تسجيل الدخول - نظام المحاسبة",
                "en": "Login - Accounting System"
            },
            "username": {
                "ar": "اسم المستخدم",
                "en": "Username"
            },
            "password": {
                "ar": "كلمة المرور",
                "en": "Password"
            },
            "language": {
                "ar": "اللغة",
                "en": "Language"
            },
            "arabic": {
                "ar": "العربية",
                "en": "Arabic"
            },
            "english": {
                "ar": "الإنجليزية",
                "en": "English"
            },
            "login": {
                "ar": "دخول",
                "en": "Login"
            },
            "forgot_password": {
                "ar": "نسيت كلمة المرور؟",
                "en": "Forgot Password?"
            },
            "company_logo": {
                "ar": "شعار الشركة",
                "en": "Company Logo"
            },
            
            # اللوحة الرئيسية
            "dashboard": {
                "ar": "اللوحة الرئيسية",
                "en": "Dashboard"
            },
            "welcome": {
                "ar": "مرحباً",
                "en": "Welcome"
            },
            "logout": {
                "ar": "تسجيل الخروج",
                "en": "Logout"
            },
            "settings": {
                "ar": "الإعدادات",
                "en": "Settings"
            },
            "notifications": {
                "ar": "التنبيهات",
                "en": "Notifications"
            },
            
            # القوائم الرئيسية
            "accounts_management": {
                "ar": "إدارة الحسابات",
                "en": "Accounts Management"
            },
            "invoices_management": {
                "ar": "إدارة الفواتير",
                "en": "Invoices Management"
            },
            "inventory": {
                "ar": "المخزون",
                "en": "Inventory"
            },
            "financial_reports": {
                "ar": "التقارير المالية",
                "en": "Financial Reports"
            },
            "customers_suppliers": {
                "ar": "العملاء والموردين",
                "en": "Customers & Suppliers"
            },
            "payroll": {
                "ar": "الرواتب",
                "en": "Payroll"
            },
            "analytics": {
                "ar": "التحليلات",
                "en": "Analytics"
            },
            
            # مؤشرات الأداء
            "total_revenue": {
                "ar": "إجمالي الإيرادات",
                "en": "Total Revenue"
            },
            "total_expenses": {
                "ar": "إجمالي المصروفات",
                "en": "Total Expenses"
            },
            "net_profit": {
                "ar": "الربح الصافي",
                "en": "Net Profit"
            },
            "accounts_receivable": {
                "ar": "المبالغ المستحقة من العملاء",
                "en": "Accounts Receivable"
            },
            "accounts_payable": {
                "ar": "المبالغ المستحقة للموردين",
                "en": "Accounts Payable"
            },
            "taxes_due": {
                "ar": "الضرائب المستحقة",
                "en": "Taxes Due"
            },
            
            # الأزرار والعمليات
            "add": {
                "ar": "إضافة",
                "en": "Add"
            },
            "edit": {
                "ar": "تعديل",
                "en": "Edit"
            },
            "delete": {
                "ar": "حذف",
                "en": "Delete"
            },
            "save": {
                "ar": "حفظ",
                "en": "Save"
            },
            "cancel": {
                "ar": "إلغاء",
                "en": "Cancel"
            },
            "search": {
                "ar": "بحث",
                "en": "Search"
            },
            "filter": {
                "ar": "تصفية",
                "en": "Filter"
            },
            "print": {
                "ar": "طباعة",
                "en": "Print"
            },
            "export": {
                "ar": "تصدير",
                "en": "Export"
            },
            
            # الرسائل
            "success": {
                "ar": "نجح",
                "en": "Success"
            },
            "error": {
                "ar": "خطأ",
                "en": "Error"
            },
            "warning": {
                "ar": "تحذير",
                "en": "Warning"
            },
            "info": {
                "ar": "معلومات",
                "en": "Information"
            },
            "confirm": {
                "ar": "تأكيد",
                "en": "Confirm"
            },
            "yes": {
                "ar": "نعم",
                "en": "Yes"
            },
            "no": {
                "ar": "لا",
                "en": "No"
            },
            
            # الحقول العامة
            "name": {
                "ar": "الاسم",
                "en": "Name"
            },
            "description": {
                "ar": "الوصف",
                "en": "Description"
            },
            "date": {
                "ar": "التاريخ",
                "en": "Date"
            },
            "amount": {
                "ar": "المبلغ",
                "en": "Amount"
            },
            "quantity": {
                "ar": "الكمية",
                "en": "Quantity"
            },
            "price": {
                "ar": "السعر",
                "en": "Price"
            },
            "total": {
                "ar": "الإجمالي",
                "en": "Total"
            },
            "status": {
                "ar": "الحالة",
                "en": "Status"
            },
            
            # حالات الفواتير
            "paid": {
                "ar": "مدفوع",
                "en": "Paid"
            },
            "pending": {
                "ar": "معلق",
                "en": "Pending"
            },
            "overdue": {
                "ar": "متأخر",
                "en": "Overdue"
            },
            "cancelled": {
                "ar": "ملغي",
                "en": "Cancelled"
            }
        }
    
    def set_language(self, language: str):
        """تعيين اللغة الحالية"""
        if language in ["ar", "en"]:
            self.current_language = language
    
    def get(self, key: str, language: str = None) -> str:
        """الحصول على الترجمة"""
        if language is None:
            language = self.current_language
        
        if key in self.translations and language in self.translations[key]:
            return self.translations[key][language]
        else:
            # إرجاع المفتاح إذا لم توجد الترجمة
            return key
    
    def get_direction(self) -> str:
        """الحصول على اتجاه النص"""
        return "rtl" if self.current_language == "ar" else "ltr"
    
    def is_rtl(self) -> bool:
        """التحقق من اتجاه النص من اليمين لليسار"""
        return self.current_language == "ar"

# إنشاء مثيل عام للترجمة
translator = Translator()