#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة قاعدة البيانات المتقدمة
Advanced Database Administration GUI
"""

import sys
import os
import json
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QLabel, QLineEdit, QPushButton, 
                             QGroupBox, QTextEdit, QTabWidget, QComboBox,
                             QApplication, QMessageBox, QListWidget,
                             QListWidgetItem, QCheckBox, QSpinBox,
                             QProgressBar, QTableWidget, QTableWidgetItem,
                             QHeaderView, QSplitter, QTreeWidget, QTreeWidgetItem,
                             QScrollArea, QFrame, QFileDialog, QMenu, QAction,
                             QToolBar, QStatusBar, QDoubleSpinBox, QDateTimeEdit,
                             QFormLayout, QDialogButtonBox, QDialog, QSlider)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QDateTime, QSize
from PyQt5.QtGui import QFont, QColor, QIcon, QPalette, QPixmap, QPainter

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from database.enhanced_database_manager import EnhancedDatabaseManager, get_db_manager
    from database.data_manager import DataManager, get_data_manager
except ImportError:
    print("تحذير: لم يتم العثور على مدير قاعدة البيانات المحسن")
    EnhancedDatabaseManager = None
    DataManager = None

class DatabaseStatsWidget(QWidget):
    """ودجت إحصائيات قاعدة البيانات"""
    
    def __init__(self, db_manager=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_stats()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QHBoxLayout()
        
        # إحصائيات الاستعلامات
        queries_card = self.create_stat_card(
            "📊 الاستعلامات", "0", "#007bff", "إجمالي الاستعلامات المنفذة"
        )
        layout.addWidget(queries_card)
        
        # معدل نجاح التخزين المؤقت
        cache_card = self.create_stat_card(
            "⚡ التخزين المؤقت", "0%", "#28a745", "معدل نجاح التخزين المؤقت"
        )
        layout.addWidget(cache_card)
        
        # متوسط وقت التنفيذ
        time_card = self.create_stat_card(
            "⏱️ وقت التنفيذ", "0ms", "#ffc107", "متوسط وقت تنفيذ الاستعلامات"
        )
        layout.addWidget(time_card)
        
        # عدد الأخطاء
        errors_card = self.create_stat_card(
            "❌ الأخطاء", "0", "#dc3545", "عدد الأخطاء"
        )
        layout.addWidget(errors_card)
        
        # حجم قاعدة البيانات
        size_card = self.create_stat_card(
            "💾 الحجم", "0 MB", "#6f42c1", "حجم قاعدة البيانات"
        )
        layout.addWidget(size_card)
        
        self.setLayout(layout)
        
        # حفظ مراجع البطاقات للتحديث
        self.stat_cards = {
            'queries': queries_card,
            'cache': cache_card,
            'time': time_card,
            'errors': errors_card,
            'size': size_card
        }
    
    def create_stat_card(self, title, value, color, description):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-left: 4px solid {color};
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }}
            QFrame:hover {{
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }}
        """)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                font-weight: bold;
                color: {color};
                margin-bottom: 5px;
            }}
        """)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        
        # الوصف
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #6c757d;
            }
        """)
        desc_label.setWordWrap(True)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addWidget(desc_label)
        
        card.setLayout(layout)
        
        # حفظ مرجع للتسمية للتحديث لاحقاً
        card.value_label = value_label
        
        return card
    
    def load_stats(self):
        """تحميل الإحصائيات"""
        try:
            if self.db_manager:
                stats = self.db_manager.get_statistics()
                self.update_stats(stats)
            else:
                # بيانات وهمية للاختبار
                demo_stats = {
                    'queries_executed': 1234,
                    'cache_hit_rate': 85.5,
                    'average_execution_time': 0.025,
                    'errors': 3,
                    'database_size': 15728640  # 15 MB
                }
                self.update_stats(demo_stats)
        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")
    
    def update_stats(self, stats):
        """تحديث الإحصائيات"""
        self.stat_cards['queries'].value_label.setText(f"{stats.get('queries_executed', 0):,}")
        self.stat_cards['cache'].value_label.setText(f"{stats.get('cache_hit_rate', 0):.1f}%")
        self.stat_cards['time'].value_label.setText(f"{stats.get('average_execution_time', 0)*1000:.1f}ms")
        self.stat_cards['errors'].value_label.setText(str(stats.get('errors', 0)))
        
        # تحويل حجم قاعدة البيانات
        size_bytes = stats.get('database_size', 0)
        if size_bytes > 1024*1024:
            size_text = f"{size_bytes/(1024*1024):.1f} MB"
        elif size_bytes > 1024:
            size_text = f"{size_bytes/1024:.1f} KB"
        else:
            size_text = f"{size_bytes} B"
        
        self.stat_cards['size'].value_label.setText(size_text)

class QueryExecutorWidget(QWidget):
    """ودجت تنفيذ الاستعلامات"""
    
    def __init__(self, db_manager=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.query_history = []
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # شريط أدوات الاستعلام
        toolbar_layout = QHBoxLayout()
        
        execute_btn = QPushButton("▶️ تنفيذ")
        execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        execute_btn.clicked.connect(self.execute_query)
        
        clear_btn = QPushButton("🗑️ مسح")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        clear_btn.clicked.connect(self.clear_query)
        
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        save_btn.clicked.connect(self.save_query)
        
        load_btn = QPushButton("📂 تحميل")
        load_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e8650e;
            }
        """)
        load_btn.clicked.connect(self.load_query)
        
        toolbar_layout.addWidget(execute_btn)
        toolbar_layout.addWidget(clear_btn)
        toolbar_layout.addWidget(save_btn)
        toolbar_layout.addWidget(load_btn)
        toolbar_layout.addStretch()
        
        layout.addLayout(toolbar_layout)
        
        # منطقة كتابة الاستعلام
        query_group = QGroupBox("📝 كتابة الاستعلام")
        query_layout = QVBoxLayout()
        
        self.query_text = QTextEdit()
        self.query_text.setPlaceholderText("اكتب استعلام SQL هنا...")
        self.query_text.setStyleSheet("""
            QTextEdit {
                font-family: 'Courier New', monospace;
                font-size: 12px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        self.query_text.setMaximumHeight(150)
        
        query_layout.addWidget(self.query_text)
        query_group.setLayout(query_layout)
        layout.addWidget(query_group)
        
        # منطقة النتائج
        results_group = QGroupBox("📊 النتائج")
        results_layout = QVBoxLayout()
        
        # معلومات التنفيذ
        self.execution_info = QLabel("جاهز لتنفيذ الاستعلامات")
        self.execution_info.setStyleSheet("""
            QLabel {
                background-color: #e9ecef;
                padding: 8px;
                border-radius: 3px;
                font-weight: bold;
            }
        """)
        results_layout.addWidget(self.execution_info)
        
        # جدول النتائج
        self.results_table = QTableWidget()
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                font-size: 11px;
            }
            QHeaderView::section {
                background-color: #343a40;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)
        results_layout.addWidget(self.results_table)
        
        results_group.setLayout(results_layout)
        layout.addWidget(results_group)
        
        self.setLayout(layout)
    
    def execute_query(self):
        """تنفيذ الاستعلام"""
        query = self.query_text.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "تحذير", "يرجى كتابة استعلام أولاً")
            return
        
        try:
            start_time = datetime.now()
            
            if self.db_manager:
                result = self.db_manager.execute_query(query)
                
                if result.success:
                    execution_time = (datetime.now() - start_time).total_seconds()
                    
                    if result.data:
                        # عرض النتائج في الجدول
                        self.display_results(result.data)
                        self.execution_info.setText(
                            f"✅ تم التنفيذ بنجاح - {len(result.data)} صف - {execution_time:.3f} ثانية"
                        )
                        self.execution_info.setStyleSheet("""
                            QLabel {
                                background-color: #d4edda;
                                color: #155724;
                                padding: 8px;
                                border-radius: 3px;
                                font-weight: bold;
                            }
                        """)
                    else:
                        # استعلام تحديث أو حذف
                        self.results_table.setRowCount(0)
                        self.results_table.setColumnCount(0)
                        self.execution_info.setText(
                            f"✅ تم التنفيذ بنجاح - {result.affected_rows} صف متأثر - {execution_time:.3f} ثانية"
                        )
                        self.execution_info.setStyleSheet("""
                            QLabel {
                                background-color: #d4edda;
                                color: #155724;
                                padding: 8px;
                                border-radius: 3px;
                                font-weight: bold;
                            }
                        """)
                else:
                    # خطأ في التنفيذ
                    self.execution_info.setText(f"❌ خطأ: {result.error}")
                    self.execution_info.setStyleSheet("""
                        QLabel {
                            background-color: #f8d7da;
                            color: #721c24;
                            padding: 8px;
                            border-radius: 3px;
                            font-weight: bold;
                        }
                    """)
            else:
                # محاكاة للاختبار
                self.simulate_query_execution(query)
            
            # إضافة للتاريخ
            self.query_history.append({
                'query': query,
                'timestamp': datetime.now(),
                'success': True
            })
            
        except Exception as e:
            self.execution_info.setText(f"❌ خطأ: {str(e)}")
            self.execution_info.setStyleSheet("""
                QLabel {
                    background-color: #f8d7da;
                    color: #721c24;
                    padding: 8px;
                    border-radius: 3px;
                    font-weight: bold;
                }
            """)
    
    def simulate_query_execution(self, query):
        """محاكاة تنفيذ الاستعلام للاختبار"""
        query_upper = query.upper().strip()
        
        if query_upper.startswith('SELECT'):
            # محاكاة نتائج SELECT
            demo_data = [
                {'id': 1, 'name': 'أحمد محمد', 'email': '<EMAIL>', 'status': 'نشط'},
                {'id': 2, 'name': 'فاطمة علي', 'email': '<EMAIL>', 'status': 'نشط'},
                {'id': 3, 'name': 'محمد حسن', 'email': '<EMAIL>', 'status': 'غير نشط'},
            ]
            self.display_results(demo_data)
            self.execution_info.setText("✅ تم التنفيذ بنجاح (محاكاة) - 3 صف - 0.025 ثانية")
            self.execution_info.setStyleSheet("""
                QLabel {
                    background-color: #d4edda;
                    color: #155724;
                    padding: 8px;
                    border-radius: 3px;
                    font-weight: bold;
                }
            """)
        else:
            # محاكاة استعلامات أخرى
            self.results_table.setRowCount(0)
            self.results_table.setColumnCount(0)
            self.execution_info.setText("✅ تم التنفيذ بنجاح (محاكاة) - 1 صف متأثر - 0.015 ثانية")
            self.execution_info.setStyleSheet("""
                QLabel {
                    background-color: #d4edda;
                    color: #155724;
                    padding: 8px;
                    border-radius: 3px;
                    font-weight: bold;
                }
            """)
    
    def display_results(self, data):
        """عرض النتائج في الجدول"""
        if not data:
            self.results_table.setRowCount(0)
            self.results_table.setColumnCount(0)
            return
        
        # إعداد الجدول
        first_row = data[0]
        columns = list(first_row.keys())
        
        self.results_table.setColumnCount(len(columns))
        self.results_table.setRowCount(len(data))
        self.results_table.setHorizontalHeaderLabels(columns)
        
        # ملء البيانات
        for row_idx, row_data in enumerate(data):
            for col_idx, column in enumerate(columns):
                value = str(row_data.get(column, ''))
                item = QTableWidgetItem(value)
                self.results_table.setItem(row_idx, col_idx, item)
        
        # تنسيق الجدول
        header = self.results_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.results_table.resizeColumnsToContents()
    
    def clear_query(self):
        """مسح الاستعلام"""
        self.query_text.clear()
        self.results_table.setRowCount(0)
        self.results_table.setColumnCount(0)
        self.execution_info.setText("جاهز لتنفيذ الاستعلامات")
        self.execution_info.setStyleSheet("""
            QLabel {
                background-color: #e9ecef;
                padding: 8px;
                border-radius: 3px;
                font-weight: bold;
            }
        """)
    
    def save_query(self):
        """حفظ الاستعلام"""
        query = self.query_text.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "تحذير", "لا يوجد استعلام لحفظه")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ الاستعلام",
            f"query_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql",
            "SQL Files (*.sql);;Text Files (*.txt);;All Files (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(query)
                QMessageBox.information(self, "تم الحفظ", f"تم حفظ الاستعلام في:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حفظ الاستعلام:\n{str(e)}")
    
    def load_query(self):
        """تحميل استعلام"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "تحميل استعلام",
            "",
            "SQL Files (*.sql);;Text Files (*.txt);;All Files (*)"
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    query = f.read()
                self.query_text.setPlainText(query)
                QMessageBox.information(self, "تم التحميل", "تم تحميل الاستعلام بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تحميل الاستعلام:\n{str(e)}")

class BackupManagerWidget(QWidget):
    """ودجت إدارة النسخ الاحتياطية"""

    def __init__(self, db_manager=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_backups()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # شريط أدوات النسخ الاحتياطية
        toolbar_layout = QHBoxLayout()

        create_backup_btn = QPushButton("💾 إنشاء نسخة احتياطية")
        create_backup_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        create_backup_btn.clicked.connect(self.create_backup)

        restore_backup_btn = QPushButton("📥 استعادة نسخة احتياطية")
        restore_backup_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        restore_backup_btn.clicked.connect(self.restore_backup)

        delete_backup_btn = QPushButton("🗑️ حذف نسخة احتياطية")
        delete_backup_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        delete_backup_btn.clicked.connect(self.delete_backup)

        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        refresh_btn.clicked.connect(self.load_backups)

        toolbar_layout.addWidget(create_backup_btn)
        toolbar_layout.addWidget(restore_backup_btn)
        toolbar_layout.addWidget(delete_backup_btn)
        toolbar_layout.addWidget(refresh_btn)
        toolbar_layout.addStretch()

        layout.addLayout(toolbar_layout)

        # قائمة النسخ الاحتياطية
        backups_group = QGroupBox("📋 النسخ الاحتياطية المتاحة")
        backups_layout = QVBoxLayout()

        self.backups_table = QTableWidget()
        self.backups_table.setColumnCount(4)
        self.backups_table.setHorizontalHeaderLabels([
            "اسم النسخة", "تاريخ الإنشاء", "الحجم", "المسار"
        ])

        # تنسيق الجدول
        header = self.backups_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)

        self.backups_table.setAlternatingRowColors(True)
        self.backups_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.backups_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #343a40;
                color: white;
                padding: 10px;
                font-weight: bold;
            }
        """)

        backups_layout.addWidget(self.backups_table)
        backups_group.setLayout(backups_layout)
        layout.addWidget(backups_group)

        # معلومات النسخة المحددة
        info_group = QGroupBox("ℹ️ معلومات النسخة المحددة")
        info_layout = QFormLayout()

        self.selected_backup_info = QTextEdit()
        self.selected_backup_info.setMaximumHeight(100)
        self.selected_backup_info.setReadOnly(True)
        self.selected_backup_info.setPlaceholderText("اختر نسخة احتياطية لعرض معلوماتها...")

        info_layout.addRow(self.selected_backup_info)
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        self.setLayout(layout)

        # ربط الأحداث
        self.backups_table.itemSelectionChanged.connect(self.on_backup_selected)

    def load_backups(self):
        """تحميل قائمة النسخ الاحتياطية"""
        try:
            if self.db_manager:
                backups = self.db_manager.get_backup_list()
            else:
                # بيانات وهمية للاختبار
                backups = [
                    {
                        'name': 'backup_20241201_120000.db.gz',
                        'created': datetime.now() - timedelta(days=1),
                        'size': 1024*1024*5,  # 5 MB
                        'path': '/backups/backup_20241201_120000.db.gz'
                    },
                    {
                        'name': 'auto_backup_20241130_000000.db.gz',
                        'created': datetime.now() - timedelta(days=2),
                        'size': 1024*1024*4.8,  # 4.8 MB
                        'path': '/backups/auto_backup_20241130_000000.db.gz'
                    }
                ]

            self.populate_backups_table(backups)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل النسخ الاحتياطية:\n{str(e)}")

    def populate_backups_table(self, backups):
        """ملء جدول النسخ الاحتياطية"""
        self.backups_table.setRowCount(len(backups))

        for row, backup in enumerate(backups):
            # اسم النسخة
            self.backups_table.setItem(row, 0, QTableWidgetItem(backup['name']))

            # تاريخ الإنشاء
            created_str = backup['created'].strftime('%Y-%m-%d %H:%M:%S')
            self.backups_table.setItem(row, 1, QTableWidgetItem(created_str))

            # الحجم
            size_mb = backup['size'] / (1024 * 1024)
            size_str = f"{size_mb:.2f} MB"
            self.backups_table.setItem(row, 2, QTableWidgetItem(size_str))

            # المسار
            self.backups_table.setItem(row, 3, QTableWidgetItem(backup['path']))

            # حفظ بيانات النسخة في العنصر
            self.backups_table.item(row, 0).setData(Qt.UserRole, backup)

    def on_backup_selected(self):
        """عند اختيار نسخة احتياطية"""
        current_row = self.backups_table.currentRow()
        if current_row >= 0:
            backup_item = self.backups_table.item(current_row, 0)
            backup_data = backup_item.data(Qt.UserRole)

            if backup_data:
                info_text = f"""
اسم النسخة: {backup_data['name']}
تاريخ الإنشاء: {backup_data['created'].strftime('%Y-%m-%d %H:%M:%S')}
الحجم: {backup_data['size'] / (1024 * 1024):.2f} MB
المسار: {backup_data['path']}
                """.strip()

                self.selected_backup_info.setPlainText(info_text)

    def create_backup(self):
        """إنشاء نسخة احتياطية جديدة"""
        try:
            backup_name = f"manual_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"

            if self.db_manager:
                backup_path = self.db_manager.create_backup(backup_name)
                QMessageBox.information(self, "تم الإنشاء",
                    f"تم إنشاء النسخة الاحتياطية بنجاح:\n{backup_path}")
            else:
                QMessageBox.information(self, "محاكاة",
                    f"تم إنشاء النسخة الاحتياطية (محاكاة):\n{backup_name}")

            self.load_backups()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}")

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        current_row = self.backups_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار نسخة احتياطية للاستعادة")
            return

        backup_item = self.backups_table.item(current_row, 0)
        backup_data = backup_item.data(Qt.UserRole)

        reply = QMessageBox.question(
            self, "تأكيد الاستعادة",
            f"هل أنت متأكد من استعادة النسخة الاحتياطية:\n{backup_data['name']}\n\n"
            "تحذير: سيتم استبدال قاعدة البيانات الحالية!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if self.db_manager:
                    self.db_manager.restore_backup(backup_data['path'])
                    QMessageBox.information(self, "تم الاستعادة",
                        "تم استعادة النسخة الاحتياطية بنجاح")
                else:
                    QMessageBox.information(self, "محاكاة",
                        f"تم استعادة النسخة الاحتياطية (محاكاة):\n{backup_data['name']}")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في استعادة النسخة الاحتياطية:\n{str(e)}")

    def delete_backup(self):
        """حذف نسخة احتياطية"""
        current_row = self.backups_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار نسخة احتياطية للحذف")
            return

        backup_item = self.backups_table.item(current_row, 0)
        backup_data = backup_item.data(Qt.UserRole)

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف النسخة الاحتياطية:\n{backup_data['name']}\n\n"
            "لا يمكن التراجع عن هذا الإجراء!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if self.db_manager:
                    os.remove(backup_data['path'])
                    QMessageBox.information(self, "تم الحذف", "تم حذف النسخة الاحتياطية بنجاح")
                else:
                    QMessageBox.information(self, "محاكاة",
                        f"تم حذف النسخة الاحتياطية (محاكاة):\n{backup_data['name']}")

                self.load_backups()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف النسخة الاحتياطية:\n{str(e)}")

class DatabaseMaintenanceWidget(QWidget):
    """ودجت صيانة قاعدة البيانات"""

    def __init__(self, db_manager=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # مجموعة فحص قاعدة البيانات
        check_group = QGroupBox("🔍 فحص قاعدة البيانات")
        check_layout = QGridLayout()

        integrity_btn = QPushButton("🔍 فحص السلامة")
        integrity_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        integrity_btn.clicked.connect(self.check_integrity)

        analyze_btn = QPushButton("📊 تحليل الجداول")
        analyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        analyze_btn.clicked.connect(self.analyze_tables)

        check_layout.addWidget(integrity_btn, 0, 0)
        check_layout.addWidget(analyze_btn, 0, 1)

        check_group.setLayout(check_layout)
        layout.addWidget(check_group)

        # مجموعة تحسين قاعدة البيانات
        optimize_group = QGroupBox("⚡ تحسين قاعدة البيانات")
        optimize_layout = QGridLayout()

        vacuum_btn = QPushButton("🗜️ ضغط قاعدة البيانات")
        vacuum_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        vacuum_btn.clicked.connect(self.vacuum_database)

        reindex_btn = QPushButton("🔄 إعادة بناء الفهارس")
        reindex_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e8650e;
            }
        """)
        reindex_btn.clicked.connect(self.reindex_database)

        optimize_all_btn = QPushButton("🚀 تحسين شامل")
        optimize_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 15px 30px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        optimize_all_btn.clicked.connect(self.optimize_all)

        optimize_layout.addWidget(vacuum_btn, 0, 0)
        optimize_layout.addWidget(reindex_btn, 0, 1)
        optimize_layout.addWidget(optimize_all_btn, 1, 0, 1, 2)

        optimize_group.setLayout(optimize_layout)
        layout.addWidget(optimize_group)

        # سجل الصيانة
        log_group = QGroupBox("📋 سجل الصيانة")
        log_layout = QVBoxLayout()

        self.maintenance_log = QTextEdit()
        self.maintenance_log.setMaximumHeight(200)
        self.maintenance_log.setReadOnly(True)
        self.maintenance_log.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        self.maintenance_log.append("جاهز لبدء عمليات الصيانة...")

        log_layout.addWidget(self.maintenance_log)
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)

        self.setLayout(layout)

    def check_integrity(self):
        """فحص سلامة قاعدة البيانات"""
        try:
            self.log_message("بدء فحص سلامة قاعدة البيانات...")

            if self.db_manager:
                is_valid = self.db_manager.check_database_integrity()
                if is_valid:
                    self.log_message("✅ قاعدة البيانات سليمة")
                    QMessageBox.information(self, "فحص السلامة", "قاعدة البيانات سليمة ولا تحتوي على أخطاء")
                else:
                    self.log_message("❌ قاعدة البيانات تحتوي على أخطاء")
                    QMessageBox.warning(self, "فحص السلامة", "قاعدة البيانات تحتوي على أخطاء")
            else:
                self.log_message("✅ فحص السلامة (محاكاة) - قاعدة البيانات سليمة")
                QMessageBox.information(self, "فحص السلامة", "قاعدة البيانات سليمة (محاكاة)")

        except Exception as e:
            self.log_message(f"❌ خطأ في فحص السلامة: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في فحص سلامة قاعدة البيانات:\n{str(e)}")

    def analyze_tables(self):
        """تحليل الجداول"""
        try:
            self.log_message("بدء تحليل الجداول...")

            if self.db_manager:
                self.db_manager.execute_query("ANALYZE")
                self.log_message("✅ تم تحليل الجداول بنجاح")
                QMessageBox.information(self, "تحليل الجداول", "تم تحليل جميع الجداول بنجاح")
            else:
                self.log_message("✅ تحليل الجداول (محاكاة) - تم بنجاح")
                QMessageBox.information(self, "تحليل الجداول", "تم تحليل الجداول بنجاح (محاكاة)")

        except Exception as e:
            self.log_message(f"❌ خطأ في تحليل الجداول: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في تحليل الجداول:\n{str(e)}")

    def vacuum_database(self):
        """ضغط قاعدة البيانات"""
        try:
            self.log_message("بدء ضغط قاعدة البيانات...")

            if self.db_manager:
                self.db_manager.execute_query("VACUUM")
                self.log_message("✅ تم ضغط قاعدة البيانات بنجاح")
                QMessageBox.information(self, "ضغط قاعدة البيانات", "تم ضغط قاعدة البيانات بنجاح")
            else:
                self.log_message("✅ ضغط قاعدة البيانات (محاكاة) - تم بنجاح")
                QMessageBox.information(self, "ضغط قاعدة البيانات", "تم ضغط قاعدة البيانات بنجاح (محاكاة)")

        except Exception as e:
            self.log_message(f"❌ خطأ في ضغط قاعدة البيانات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في ضغط قاعدة البيانات:\n{str(e)}")

    def reindex_database(self):
        """إعادة بناء الفهارس"""
        try:
            self.log_message("بدء إعادة بناء الفهارس...")

            if self.db_manager:
                self.db_manager.execute_query("REINDEX")
                self.log_message("✅ تم إعادة بناء الفهارس بنجاح")
                QMessageBox.information(self, "إعادة بناء الفهارس", "تم إعادة بناء جميع الفهارس بنجاح")
            else:
                self.log_message("✅ إعادة بناء الفهارس (محاكاة) - تم بنجاح")
                QMessageBox.information(self, "إعادة بناء الفهارس", "تم إعادة بناء الفهارس بنجاح (محاكاة)")

        except Exception as e:
            self.log_message(f"❌ خطأ في إعادة بناء الفهارس: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في إعادة بناء الفهارس:\n{str(e)}")

    def optimize_all(self):
        """تحسين شامل لقاعدة البيانات"""
        reply = QMessageBox.question(
            self, "تأكيد التحسين الشامل",
            "هل أنت متأكد من تنفيذ التحسين الشامل؟\n\n"
            "سيتم تنفيذ:\n"
            "• فحص سلامة قاعدة البيانات\n"
            "• تحليل الجداول\n"
            "• ضغط قاعدة البيانات\n"
            "• إعادة بناء الفهارس\n\n"
            "قد تستغرق هذه العملية وقتاً طويلاً.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.log_message("بدء التحسين الشامل لقاعدة البيانات...")

                if self.db_manager:
                    self.db_manager.optimize_database()
                    self.log_message("✅ تم التحسين الشامل بنجاح")
                    QMessageBox.information(self, "التحسين الشامل",
                        "تم تنفيذ التحسين الشامل لقاعدة البيانات بنجاح")
                else:
                    self.log_message("✅ التحسين الشامل (محاكاة) - تم بنجاح")
                    QMessageBox.information(self, "التحسين الشامل",
                        "تم تنفيذ التحسين الشامل بنجاح (محاكاة)")

            except Exception as e:
                self.log_message(f"❌ خطأ في التحسين الشامل: {str(e)}")
                QMessageBox.critical(self, "خطأ", f"فشل في التحسين الشامل:\n{str(e)}")

    def log_message(self, message):
        """إضافة رسالة لسجل الصيانة"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.maintenance_log.append(f"[{timestamp}] {message}")

class DatabaseAdminMainWindow(QMainWindow):
    """النافذة الرئيسية لإدارة قاعدة البيانات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = None
        self.setup_ui()
        self.setup_connections()
        self.initialize_database()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🗄️ إدارة قاعدة البيانات المتقدمة")
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء القوائم
        self.create_menus()

        # إنشاء شريط الأدوات
        self.create_toolbar()

        # الودجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # عنوان النظام
        title_label = QLabel("🗄️ إدارة قاعدة البيانات المتقدمة - مراقبة وصيانة وإدارة شاملة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # لوحة الإحصائيات
        self.stats_widget = DatabaseStatsWidget(self.db_manager)
        main_layout.addWidget(self.stats_widget)

        # التبويبات الرئيسية
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                padding: 10px 15px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #007bff;
            }
        """)

        # تبويب تنفيذ الاستعلامات
        self.query_executor = QueryExecutorWidget(self.db_manager)
        self.tabs.addTab(self.query_executor, "📝 تنفيذ الاستعلامات")

        # تبويب إدارة النسخ الاحتياطية
        self.backup_manager = BackupManagerWidget(self.db_manager)
        self.tabs.addTab(self.backup_manager, "💾 النسخ الاحتياطية")

        # تبويب صيانة قاعدة البيانات
        self.maintenance_widget = DatabaseMaintenanceWidget(self.db_manager)
        self.tabs.addTab(self.maintenance_widget, "🔧 صيانة قاعدة البيانات")

        # تبويب معلومات قاعدة البيانات
        database_info_tab = self.create_database_info_tab()
        self.tabs.addTab(database_info_tab, "ℹ️ معلومات قاعدة البيانات")

        # تبويب إعدادات الاتصال
        connection_settings_tab = self.create_connection_settings_tab()
        self.tabs.addTab(connection_settings_tab, "⚙️ إعدادات الاتصال")

        main_layout.addWidget(self.tabs)

        central_widget.setLayout(main_layout)

        # شريط الحالة
        self.create_status_bar()

    def create_menus(self):
        """إنشاء القوائم"""
        menubar = self.menuBar()

        # قائمة قاعدة البيانات
        db_menu = menubar.addMenu("🗄️ قاعدة البيانات")

        connect_action = QAction("🔗 اتصال", self)
        connect_action.triggered.connect(self.connect_database)
        db_menu.addAction(connect_action)

        disconnect_action = QAction("❌ قطع الاتصال", self)
        disconnect_action.triggered.connect(self.disconnect_database)
        db_menu.addAction(disconnect_action)

        db_menu.addSeparator()

        create_backup_action = QAction("💾 إنشاء نسخة احتياطية", self)
        create_backup_action.triggered.connect(self.create_backup)
        db_menu.addAction(create_backup_action)

        # قائمة الصيانة
        maintenance_menu = menubar.addMenu("🔧 صيانة")

        check_integrity_action = QAction("🔍 فحص السلامة", self)
        check_integrity_action.triggered.connect(self.check_integrity)
        maintenance_menu.addAction(check_integrity_action)

        optimize_action = QAction("⚡ تحسين قاعدة البيانات", self)
        optimize_action.triggered.connect(self.optimize_database)
        maintenance_menu.addAction(optimize_action)

        # قائمة العرض
        view_menu = menubar.addMenu("👁️ عرض")

        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_all)
        view_menu.addAction(refresh_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("❓ مساعدة")

        about_action = QAction("ℹ️ حول", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # زر الاتصال
        connect_action = QAction("🔗 اتصال", self)
        connect_action.setToolTip("الاتصال بقاعدة البيانات")
        connect_action.triggered.connect(self.connect_database)
        toolbar.addAction(connect_action)

        toolbar.addSeparator()

        # زر النسخ الاحتياطي
        backup_action = QAction("💾 نسخ احتياطي", self)
        backup_action.setToolTip("إنشاء نسخة احتياطية")
        backup_action.triggered.connect(self.create_backup)
        toolbar.addAction(backup_action)

        # زر التحسين
        optimize_action = QAction("⚡ تحسين", self)
        optimize_action.setToolTip("تحسين قاعدة البيانات")
        optimize_action.triggered.connect(self.optimize_database)
        toolbar.addAction(optimize_action)

        toolbar.addSeparator()

        # زر التحديث
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setToolTip("تحديث جميع البيانات")
        refresh_action.triggered.connect(self.refresh_all)
        toolbar.addAction(refresh_action)

    def create_database_info_tab(self):
        """إنشاء تبويب معلومات قاعدة البيانات"""
        tab = QWidget()
        layout = QVBoxLayout()

        # معلومات قاعدة البيانات
        info_group = QGroupBox("ℹ️ معلومات عامة")
        info_layout = QFormLayout()

        self.db_type_label = QLabel("غير متصل")
        self.db_size_label = QLabel("غير متاح")
        self.db_tables_label = QLabel("غير متاح")
        self.db_version_label = QLabel("غير متاح")

        info_layout.addRow("نوع قاعدة البيانات:", self.db_type_label)
        info_layout.addRow("حجم قاعدة البيانات:", self.db_size_label)
        info_layout.addRow("عدد الجداول:", self.db_tables_label)
        info_layout.addRow("إصدار قاعدة البيانات:", self.db_version_label)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        # قائمة الجداول
        tables_group = QGroupBox("📋 الجداول")
        tables_layout = QVBoxLayout()

        self.tables_tree = QTreeWidget()
        self.tables_tree.setHeaderLabels(["اسم الجدول", "عدد الصفوف", "الحجم"])
        self.tables_tree.setAlternatingRowColors(True)
        self.tables_tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #343a40;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)

        tables_layout.addWidget(self.tables_tree)

        # زر تحديث الجداول
        refresh_tables_btn = QPushButton("🔄 تحديث قائمة الجداول")
        refresh_tables_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        refresh_tables_btn.clicked.connect(self.refresh_tables_info)
        tables_layout.addWidget(refresh_tables_btn)

        tables_group.setLayout(tables_layout)
        layout.addWidget(tables_group)

        tab.setLayout(layout)
        return tab

    def create_connection_settings_tab(self):
        """إنشاء تبويب إعدادات الاتصال"""
        tab = QWidget()
        layout = QVBoxLayout()

        # إعدادات الاتصال
        connection_group = QGroupBox("⚙️ إعدادات الاتصال")
        connection_layout = QFormLayout()

        self.db_type_combo = QComboBox()
        self.db_type_combo.addItems(["SQLite", "MySQL", "PostgreSQL", "MongoDB", "Redis"])

        self.db_host_edit = QLineEdit("localhost")
        self.db_port_edit = QLineEdit("3306")
        self.db_name_edit = QLineEdit("enhanced_accounting_system.db")
        self.db_user_edit = QLineEdit("")
        self.db_password_edit = QLineEdit("")
        self.db_password_edit.setEchoMode(QLineEdit.Password)

        connection_layout.addRow("نوع قاعدة البيانات:", self.db_type_combo)
        connection_layout.addRow("الخادم:", self.db_host_edit)
        connection_layout.addRow("المنفذ:", self.db_port_edit)
        connection_layout.addRow("اسم قاعدة البيانات:", self.db_name_edit)
        connection_layout.addRow("اسم المستخدم:", self.db_user_edit)
        connection_layout.addRow("كلمة المرور:", self.db_password_edit)

        connection_group.setLayout(connection_layout)
        layout.addWidget(connection_group)

        # أزرار الاتصال
        buttons_layout = QHBoxLayout()

        test_connection_btn = QPushButton("🔍 اختبار الاتصال")
        test_connection_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        test_connection_btn.clicked.connect(self.test_connection)

        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        save_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        save_settings_btn.clicked.connect(self.save_connection_settings)

        buttons_layout.addWidget(test_connection_btn)
        buttons_layout.addWidget(save_settings_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # سجل الاتصال
        log_group = QGroupBox("📋 سجل الاتصال")
        log_layout = QVBoxLayout()

        self.connection_log = QTextEdit()
        self.connection_log.setMaximumHeight(150)
        self.connection_log.setReadOnly(True)
        self.connection_log.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        self.connection_log.append("جاهز للاتصال بقاعدة البيانات...")

        log_layout.addWidget(self.connection_log)
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)

        tab.setLayout(layout)
        return tab

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()

        # تسمية الحالة
        self.status_label = QLabel("جاهز")
        status_bar.addWidget(self.status_label)

        # مؤشر الاتصال
        self.connection_indicator = QLabel("🔴 غير متصل")
        status_bar.addPermanentWidget(self.connection_indicator)

        # الوقت الحالي
        self.time_label = QLabel()
        self.update_time()
        status_bar.addPermanentWidget(self.time_label)

        # مؤقت تحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)

    def setup_connections(self):
        """إعداد الاتصالات"""
        pass

    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            # محاولة استخدام مدير البيانات المتكامل أولاً
            if DataManager:
                self.data_manager = get_data_manager()
                if self.data_manager.connect():
                    self.connection_indicator.setText("🟢 متصل (مدير البيانات)")
                    self.status_label.setText("تم الاتصال بقاعدة البيانات عبر مدير البيانات")

                    # تحديث البيانات
                    self.refresh_all()
                    return

            # محاولة استخدام مدير قاعدة البيانات المحسن
            if EnhancedDatabaseManager:
                self.db_manager = get_db_manager()
                self.connection_indicator.setText("🟢 متصل (محسن)")
                self.status_label.setText("تم الاتصال بقاعدة البيانات المحسنة")

                # تحديث الودجات
                self.stats_widget.db_manager = self.db_manager
                self.query_executor.db_manager = self.db_manager
                self.backup_manager.db_manager = self.db_manager
                self.maintenance_widget.db_manager = self.db_manager

                # تحديث البيانات
                self.refresh_all()
            else:
                self.connection_indicator.setText("🟡 وضع المحاكاة")
                self.status_label.setText("تشغيل في وضع المحاكاة")

        except Exception as e:
            self.connection_indicator.setText("🔴 خطأ في الاتصال")
            self.status_label.setText(f"خطأ: {str(e)}")
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")

    def connect_database(self):
        """الاتصال بقاعدة البيانات"""
        try:
            # هنا يتم الاتصال بقاعدة البيانات باستخدام الإعدادات
            self.log_connection_message("محاولة الاتصال بقاعدة البيانات...")

            if EnhancedDatabaseManager:
                config = self.get_connection_config()
                self.db_manager = EnhancedDatabaseManager(config)

                self.connection_indicator.setText("🟢 متصل")
                self.status_label.setText("تم الاتصال بقاعدة البيانات بنجاح")
                self.log_connection_message("✅ تم الاتصال بنجاح")

                # تحديث الودجات
                self.update_widgets_db_manager()
                self.refresh_all()
            else:
                self.log_connection_message("⚠️ تشغيل في وضع المحاكاة")

        except Exception as e:
            self.connection_indicator.setText("🔴 فشل الاتصال")
            self.status_label.setText(f"فشل الاتصال: {str(e)}")
            self.log_connection_message(f"❌ فشل الاتصال: {str(e)}")
            QMessageBox.critical(self, "خطأ في الاتصال", f"فشل في الاتصال بقاعدة البيانات:\n{str(e)}")

    def disconnect_database(self):
        """قطع الاتصال بقاعدة البيانات"""
        try:
            if self.db_manager:
                self.db_manager.close()
                self.db_manager = None

            self.connection_indicator.setText("🔴 غير متصل")
            self.status_label.setText("تم قطع الاتصال")
            self.log_connection_message("تم قطع الاتصال بقاعدة البيانات")

            # تحديث الودجات
            self.update_widgets_db_manager()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في قطع الاتصال:\n{str(e)}")

    def get_connection_config(self):
        """الحصول على إعدادات الاتصال"""
        db_type_map = {
            "SQLite": "sqlite",
            "MySQL": "mysql",
            "PostgreSQL": "postgresql",
            "MongoDB": "mongodb",
            "Redis": "redis"
        }

        config = {
            'type': db_type_map.get(self.db_type_combo.currentText(), 'sqlite'),
            'connection': {
                'host': self.db_host_edit.text(),
                'port': int(self.db_port_edit.text()) if self.db_port_edit.text().isdigit() else 3306,
                'database': self.db_name_edit.text(),
                'user': self.db_user_edit.text(),
                'password': self.db_password_edit.text()
            }
        }

        return config

    def update_widgets_db_manager(self):
        """تحديث مدير قاعدة البيانات في الودجات"""
        self.stats_widget.db_manager = self.db_manager
        self.query_executor.db_manager = self.db_manager
        self.backup_manager.db_manager = self.db_manager
        self.maintenance_widget.db_manager = self.db_manager

    def test_connection(self):
        """اختبار الاتصال"""
        try:
            self.log_connection_message("اختبار الاتصال...")

            config = self.get_connection_config()

            if config['type'] == 'sqlite':
                # اختبار SQLite
                import sqlite3
                conn = sqlite3.connect(config['connection']['database'])
                conn.close()
                self.log_connection_message("✅ اختبار الاتصال نجح")
                QMessageBox.information(self, "اختبار الاتصال", "تم الاتصال بنجاح!")
            else:
                self.log_connection_message("⚠️ اختبار الاتصال غير مدعوم لهذا النوع")
                QMessageBox.information(self, "اختبار الاتصال", "اختبار الاتصال غير مدعوم لهذا النوع من قواعد البيانات")

        except Exception as e:
            self.log_connection_message(f"❌ فشل اختبار الاتصال: {str(e)}")
            QMessageBox.critical(self, "فشل الاتصال", f"فشل في اختبار الاتصال:\n{str(e)}")

    def save_connection_settings(self):
        """حفظ إعدادات الاتصال"""
        try:
            config = self.get_connection_config()

            # حفظ الإعدادات في ملف
            settings_file = "database_settings.json"
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            self.log_connection_message("✅ تم حفظ الإعدادات")
            QMessageBox.information(self, "حفظ الإعدادات", "تم حفظ إعدادات الاتصال بنجاح")

        except Exception as e:
            self.log_connection_message(f"❌ فشل حفظ الإعدادات: {str(e)}")
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات:\n{str(e)}")

    def log_connection_message(self, message):
        """إضافة رسالة لسجل الاتصال"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.connection_log.append(f"[{timestamp}] {message}")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        self.backup_manager.create_backup()

    def check_integrity(self):
        """فحص سلامة قاعدة البيانات"""
        self.maintenance_widget.check_integrity()

    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        self.maintenance_widget.optimize_all()

    def refresh_all(self):
        """تحديث جميع البيانات"""
        try:
            # تحديث الإحصائيات
            self.stats_widget.load_stats()

            # تحديث النسخ الاحتياطية
            self.backup_manager.load_backups()

            # تحديث معلومات قاعدة البيانات
            self.refresh_database_info()

            # تحديث معلومات الجداول
            self.refresh_tables_info()

            self.status_label.setText("تم تحديث جميع البيانات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث البيانات:\n{str(e)}")

    def refresh_database_info(self):
        """تحديث معلومات قاعدة البيانات"""
        try:
            if self.db_manager:
                stats = self.db_manager.get_statistics()

                # نوع قاعدة البيانات
                self.db_type_label.setText(self.db_manager.db_type.value.upper())

                # حجم قاعدة البيانات
                size_bytes = stats.get('database_size', 0)
                if size_bytes > 1024*1024:
                    size_text = f"{size_bytes/(1024*1024):.2f} MB"
                elif size_bytes > 1024:
                    size_text = f"{size_bytes/1024:.2f} KB"
                else:
                    size_text = f"{size_bytes} B"
                self.db_size_label.setText(size_text)

                # عدد الجداول
                tables = stats.get('tables', [])
                self.db_tables_label.setText(str(len(tables)))

                # إصدار قاعدة البيانات
                if self.db_manager.db_type.value == 'sqlite':
                    version_result = self.db_manager.fetch_one("SELECT sqlite_version() as version")
                    if version_result:
                        self.db_version_label.setText(f"SQLite {version_result['version']}")
                    else:
                        self.db_version_label.setText("غير متاح")
                else:
                    self.db_version_label.setText("غير متاح")
            else:
                # وضع المحاكاة
                self.db_type_label.setText("SQLite (محاكاة)")
                self.db_size_label.setText("15.5 MB")
                self.db_tables_label.setText("12")
                self.db_version_label.setText("SQLite 3.39.0")

        except Exception as e:
            print(f"خطأ في تحديث معلومات قاعدة البيانات: {e}")

    def refresh_tables_info(self):
        """تحديث معلومات الجداول"""
        try:
            self.tables_tree.clear()

            if self.db_manager:
                # الحصول على قائمة الجداول
                tables_result = self.db_manager.fetch_all("""
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name NOT LIKE 'sqlite_%'
                    ORDER BY name
                """)

                for table_row in tables_result:
                    table_name = table_row['name']

                    # عدد الصفوف
                    count_result = self.db_manager.fetch_one(f"SELECT COUNT(*) as count FROM {table_name}")
                    row_count = count_result['count'] if count_result else 0

                    # إنشاء عنصر الجدول
                    table_item = QTreeWidgetItem([table_name, str(row_count), "غير متاح"])
                    self.tables_tree.addTopLevelItem(table_item)

                    # الحصول على معلومات الأعمدة
                    columns_info = self.db_manager.get_table_info(table_name)
                    for column in columns_info:
                        column_name = column.get('name', '')
                        column_type = column.get('type', '')
                        column_item = QTreeWidgetItem([f"  {column_name}", column_type, ""])
                        table_item.addChild(column_item)
            else:
                # بيانات وهمية للمحاكاة
                demo_tables = [
                    ("users", 25, [("id", "INTEGER"), ("username", "VARCHAR"), ("email", "VARCHAR")]),
                    ("customers", 150, [("id", "INTEGER"), ("name", "VARCHAR"), ("phone", "VARCHAR")]),
                    ("products", 300, [("id", "INTEGER"), ("name", "VARCHAR"), ("price", "DECIMAL")]),
                    ("sales_invoices", 1200, [("id", "INTEGER"), ("number", "VARCHAR"), ("total", "DECIMAL")])
                ]

                for table_name, row_count, columns in demo_tables:
                    table_item = QTreeWidgetItem([table_name, str(row_count), "غير متاح"])
                    self.tables_tree.addTopLevelItem(table_item)

                    for column_name, column_type in columns:
                        column_item = QTreeWidgetItem([f"  {column_name}", column_type, ""])
                        table_item.addChild(column_item)

            # توسيع جميع العناصر
            self.tables_tree.expandAll()

        except Exception as e:
            print(f"خطأ في تحديث معلومات الجداول: {e}")

    def update_time(self):
        """تحديث الوقت"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = """
🗄️ إدارة قاعدة البيانات المتقدمة

الإصدار: 2.0
المطور: نظام إدارة الحسابات المتقدم

الميزات:
• إدارة شاملة لقواعد البيانات
• تنفيذ الاستعلامات التفاعلي
• نظام نسخ احتياطية متقدم
• صيانة وتحسين قاعدة البيانات
• مراقبة الأداء والإحصائيات
• واجهة عربية احترافية

قواعد البيانات المدعومة:
• SQLite
• MySQL
• PostgreSQL
• MongoDB
• Redis

© 2024 جميع الحقوق محفوظة
        """
        QMessageBox.about(self, "حول إدارة قاعدة البيانات", about_text)

    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        try:
            if self.db_manager:
                self.db_manager.close()
        except:
            pass
        event.accept()


def main():
    """تشغيل إدارة قاعدة البيانات"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = DatabaseAdminMainWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
