#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام Onyx ERP - ملف التشغيل الرئيسي
Onyx ERP System - Main Startup File
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import QApplication, QMessageBox
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    from src.ui.onyx_login_window import OnyxLoginWindow

    def main():
        """الدالة الرئيسية لتشغيل نظام Onyx ERP"""
        try:
            print("مرحباً بك في نظام Onyx ERP")
            print("نظام تخطيط موارد المؤسسات المتكامل")
            print("=" * 60)
        except UnicodeEncodeError:
            print("Welcome to Onyx ERP System")
            print("Enterprise Resource Planning System")
            print("=" * 60)

        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("Onyx ERP")
        app.setApplicationVersion("1.0.0")

        # تعيين الخط والاتجاه
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        app.setLayoutDirection(Qt.RightToLeft)

        print("جاري تشغيل نظام Onyx ERP...")

        # إنشاء نافذة تسجيل الدخول
        login_window = OnyxLoginWindow()
        login_window.show()

        try:
            print("تم تشغيل النظام بنجاح!")
            print("\nبيانات تسجيل الدخول:")
            print("   المستخدم: admin")
            print("   كلمة المرور: admin123")
            print("   السنة المالية: 2025")
            print("   اللغة: عربي")
            print("   الشركة: صقالات ابو سيف للنقل")
            print("   الفرع: 1 - الفرع الرئيسي")
        except UnicodeEncodeError:
            print("System started successfully!")
            print("\nLogin credentials:")
            print("   User: admin")
            print("   Password: admin123")
            print("   Fiscal Year: 2025")
            print("   Language: Arabic")
            print("   Company: Saqalat Abu Saif Transport")
            print("   Branch: 1 - Main Branch")
        print("=" * 60)

        # تشغيل التطبيق
        return app.exec_()

    if __name__ == "__main__":
        try:
            exit_code = main()
            sys.exit(exit_code)
        except KeyboardInterrupt:
            try:
                print("\nتم إيقاف النظام بواسطة المستخدم")
            except UnicodeEncodeError:
                print("\nSystem stopped by user")
            sys.exit(0)
        except Exception as e:
            try:
                print(f"\nخطأ في تشغيل النظام: {e}")
            except UnicodeEncodeError:
                print(f"\nSystem error: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)

except ImportError as e:
    try:
        print(f"خطأ في استيراد المكتبات: {e}")
        print("يرجى تثبيت المتطلبات باستخدام:")
        print("   pip install PyQt5 bcrypt python-dateutil")
    except UnicodeEncodeError:
        print(f"Import error: {e}")
        print("Please install requirements using:")
        print("   pip install PyQt5 bcrypt python-dateutil")
    sys.exit(1)

except Exception as e:
    try:
        print(f"خطأ عام: {e}")
    except UnicodeEncodeError:
        print(f"General error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
