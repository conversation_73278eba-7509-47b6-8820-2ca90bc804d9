#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام Onyx ERP - ملف التشغيل الرئيسي
Onyx ERP System - Main Startup File
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from PyQt5.QtWidgets import QApplication, QMessageBox
    from PyQt5.QtCore import Qt
    from PyQt5.QtGui import QFont
    from src.ui.onyx_login_window import OnyxLoginWindow

    def main():
        """الدالة الرئيسية لتشغيل نظام Onyx ERP"""
        print("🚀 مرحباً بك في نظام Onyx ERP")
        print("   نظام تخطيط موارد المؤسسات المتكامل")
        print("=" * 60)

        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("Onyx ERP")
        app.setApplicationVersion("1.0.0")

        # تعيين الخط والاتجاه
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        app.setLayoutDirection(Qt.RightToLeft)

        print("جاري تشغيل نظام Onyx ERP...")

        # إنشاء نافذة تسجيل الدخول
        login_window = OnyxLoginWindow()
        login_window.show()

        print("✅ تم تشغيل النظام بنجاح!")
        print("\n📋 بيانات تسجيل الدخول:")
        print("   👤 المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        print("   📅 السنة المالية: 2025")
        print("   🌐 اللغة: عربي")
        print("   🏪 الشركة: صقالات ابو سيف للنقل")
        print("   🏢 الفرع: 1 - الفرع الرئيسي")
        print("=" * 60)

        # تشغيل التطبيق
        return app.exec_()

    if __name__ == "__main__":
        try:
            exit_code = main()
            sys.exit(exit_code)
        except KeyboardInterrupt:
            print("\n⚠️ تم إيقاف النظام بواسطة المستخدم")
            sys.exit(0)
        except Exception as e:
            print(f"\n❌ خطأ في تشغيل النظام: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("💡 يرجى تثبيت المتطلبات باستخدام:")
    print("   pip install PyQt5 bcrypt python-dateutil")
    sys.exit(1)

except Exception as e:
    print(f"❌ خطأ عام: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
