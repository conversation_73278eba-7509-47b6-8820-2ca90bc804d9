#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام محول العملات
Currency Converter System
"""

import sys
import json
from datetime import datetime, date
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QPushButton, QGroupBox, 
                             QTextEdit, QTabWidget, QWidget, QComboBox,
                             QApplication, QMessageBox, QListWidget,
                             QListWidgetItem, QCheckBox, QSpinBox,
                             QDoubleSpinBox, QTableWidget, QTableWidgetItem,
                             QHeaderView, QSplitter, QFrame, QScrollArea)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QIcon

class CurrencyUpdateThread(QThread):
    """خيط تحديث أسعار العملات"""
    rates_updated = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.is_running = True
        
    def run(self):
        """تشغيل تحديث الأسعار"""
        while self.is_running:
            try:
                # محاكاة تحديث أسعار العملات (في التطبيق الحقيقي، استخدم API)
                rates = self.get_mock_exchange_rates()
                self.rates_updated.emit(rates)
                self.msleep(30000)  # تحديث كل 30 ثانية
                
            except Exception as e:
                print(f"خطأ في تحديث أسعار العملات: {e}")
                self.msleep(60000)
                
    def get_mock_exchange_rates(self):
        """الحصول على أسعار صرف وهمية"""
        import random
        
        # أسعار أساسية مع تذبذب عشوائي
        base_rates = {
            'USD': 1.0,  # الدولار الأمريكي كعملة أساس
            'EUR': 0.85 + random.uniform(-0.02, 0.02),
            'GBP': 0.73 + random.uniform(-0.02, 0.02),
            'JPY': 110.0 + random.uniform(-5, 5),
            'SAR': 3.75 + random.uniform(-0.1, 0.1),
            'AED': 3.67 + random.uniform(-0.1, 0.1),
            'YER': 250.0 + random.uniform(-10, 10),
            'EGP': 30.9 + random.uniform(-1, 1),
            'JOD': 0.71 + random.uniform(-0.02, 0.02),
            'KWD': 0.30 + random.uniform(-0.01, 0.01),
            'QAR': 3.64 + random.uniform(-0.1, 0.1),
            'BHD': 0.38 + random.uniform(-0.01, 0.01),
            'OMR': 0.38 + random.uniform(-0.01, 0.01),
            'CNY': 6.45 + random.uniform(-0.2, 0.2),
            'INR': 74.5 + random.uniform(-2, 2),
            'TRY': 8.5 + random.uniform(-0.5, 0.5)
        }
        
        return base_rates
    
    def stop(self):
        """إيقاف التحديث"""
        self.is_running = False

class CurrencyConverterDialog(QDialog):
    """حوار محول العملات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.exchange_rates = {}
        self.conversion_history = []
        self.favorite_currencies = ['USD', 'EUR', 'SAR', 'YER']
        self.currency_updater = None
        self.setup_ui()
        self.load_currency_data()
        self.start_currency_updates()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("💱 محول العملات المتقدم")
        self.setGeometry(200, 200, 900, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("💱 محول العملات المتقدم - تحويل العملات مع أسعار محدثة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات النظام
        self.tabs = QTabWidget()
        
        # تبويب التحويل السريع
        quick_convert_tab = self.create_quick_convert_tab()
        self.tabs.addTab(quick_convert_tab, "⚡ التحويل السريع")
        
        # تبويب أسعار العملات
        rates_tab = self.create_rates_tab()
        self.tabs.addTab(rates_tab, "📊 أسعار العملات")
        
        # تبويب تاريخ التحويلات
        history_tab = self.create_history_tab()
        self.tabs.addTab(history_tab, "📋 تاريخ التحويلات")
        
        # تبويب الإعدادات
        settings_tab = self.create_settings_tab()
        self.tabs.addTab(settings_tab, "⚙️ الإعدادات")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_quick_convert_tab(self):
        """إنشاء تبويب التحويل السريع"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة التحويل الرئيسي
        convert_group = QGroupBox("💱 تحويل العملات")
        convert_layout = QGridLayout()
        
        # المبلغ
        convert_layout.addWidget(QLabel("المبلغ:"), 0, 0)
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0.01, 999999999.99)
        self.amount_input.setValue(100.0)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" ")
        self.amount_input.setStyleSheet("""
            QDoubleSpinBox {
                padding: 8px;
                border: 2px solid #3498db;
                border-radius: 5px;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        self.amount_input.valueChanged.connect(self.auto_convert)
        convert_layout.addWidget(self.amount_input, 0, 1)
        
        # العملة المصدر
        convert_layout.addWidget(QLabel("من:"), 0, 2)
        self.from_currency = QComboBox()
        self.from_currency.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #27ae60;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        self.from_currency.currentTextChanged.connect(self.auto_convert)
        convert_layout.addWidget(self.from_currency, 0, 3)
        
        # زر التبديل
        swap_btn = QPushButton("🔄")
        swap_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 10px;
                border-radius: 25px;
                font-size: 16px;
                font-weight: bold;
                max-width: 50px;
                max-height: 50px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        swap_btn.clicked.connect(self.swap_currencies)
        convert_layout.addWidget(swap_btn, 1, 1, Qt.AlignCenter)
        
        # العملة الهدف
        convert_layout.addWidget(QLabel("إلى:"), 1, 2)
        self.to_currency = QComboBox()
        self.to_currency.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #e74c3c;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        self.to_currency.currentTextChanged.connect(self.auto_convert)
        convert_layout.addWidget(self.to_currency, 1, 3)
        
        # النتيجة
        convert_layout.addWidget(QLabel("النتيجة:"), 2, 0)
        self.result_label = QLabel("0.00")
        self.result_label.setStyleSheet("""
            QLabel {
                padding: 15px;
                border: 3px solid #9b59b6;
                border-radius: 8px;
                font-size: 20px;
                font-weight: bold;
                color: #9b59b6;
                background-color: #f8f9fa;
            }
        """)
        self.result_label.setAlignment(Qt.AlignCenter)
        convert_layout.addWidget(self.result_label, 2, 1, 1, 3)
        
        # معلومات السعر
        convert_layout.addWidget(QLabel("سعر الصرف:"), 3, 0)
        self.rate_info_label = QLabel("1 USD = 1 USD")
        self.rate_info_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #ecf0f1;
                border-radius: 5px;
                font-size: 12px;
                color: #7f8c8d;
            }
        """)
        convert_layout.addWidget(self.rate_info_label, 3, 1, 1, 3)
        
        convert_group.setLayout(convert_layout)
        
        # مجموعة التحويلات السريعة
        quick_group = QGroupBox("⚡ تحويلات سريعة")
        quick_layout = QGridLayout()
        
        # أزرار المبالغ السريعة
        quick_amounts = [1, 10, 100, 1000, 10000]
        for i, amount in enumerate(quick_amounts):
            btn = QPushButton(f"{amount:,}")
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #17a2b8;
                    color: white;
                    padding: 8px 15px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #138496;
                }
            """)
            btn.clicked.connect(lambda checked, amt=amount: self.set_quick_amount(amt))
            quick_layout.addWidget(btn, 0, i)
        
        quick_group.setLayout(quick_layout)
        
        # مجموعة العملات المفضلة
        favorites_group = QGroupBox("⭐ العملات المفضلة")
        favorites_layout = QHBoxLayout()
        
        self.favorites_list = QListWidget()
        self.favorites_list.setMaximumHeight(100)
        self.favorites_list.setStyleSheet("""
            QListWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 5px;
                margin: 2px;
                background-color: white;
                border-radius: 3px;
            }
            QListWidget::item:selected {
                background-color: #ffc107;
                color: white;
            }
        """)
        self.favorites_list.itemDoubleClicked.connect(self.use_favorite_currency)
        
        favorites_layout.addWidget(self.favorites_list)
        favorites_group.setLayout(favorites_layout)
        
        layout.addWidget(convert_group)
        layout.addWidget(quick_group)
        layout.addWidget(favorites_group)
        
        tab.setLayout(layout)
        return tab

    def create_simple_rates_tab(self):
        """إنشاء تبويب أسعار العملات البسيط"""
        tab = QWidget()
        layout = QVBoxLayout()

        info_label = QLabel("📊 أسعار العملات\n\nسيتم عرض أسعار الصرف الحالية هنا")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                padding: 50px;
                font-size: 16px;
                color: #7f8c8d;
                background-color: #f8f9fa;
                border-radius: 10px;
                border: 2px dashed #bdc3c7;
            }
        """)

        layout.addWidget(info_label)
        tab.setLayout(layout)
        return tab

    def create_rates_tab(self):
        """إنشاء تبويب أسعار العملات"""
        tab = QWidget()
        layout = QVBoxLayout()

        # شريط أدوات الأسعار
        rates_toolbar = QHBoxLayout()

        refresh_rates_btn = QPushButton("🔄 تحديث الأسعار")
        refresh_rates_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        refresh_rates_btn.clicked.connect(self.refresh_rates)

        # آخر تحديث
        self.last_update_label = QLabel("آخر تحديث: جاري التحميل...")
        self.last_update_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #d1ecf1;
                border-radius: 5px;
                color: #0c5460;
            }
        """)

        rates_toolbar.addWidget(refresh_rates_btn)
        rates_toolbar.addStretch()
        rates_toolbar.addWidget(self.last_update_label)

        # جدول أسعار العملات
        rates_group = QGroupBox("📊 أسعار الصرف الحالية")
        rates_layout = QVBoxLayout()

        self.rates_table = QTableWidget()
        self.rates_table.setColumnCount(4)
        self.rates_table.setHorizontalHeaderLabels([
            "العملة", "الرمز", "السعر (مقابل USD)", "التغيير"
        ])

        # تنسيق الجدول
        self.rates_table.horizontalHeader().setStretchLastSection(True)
        self.rates_table.setAlternatingRowColors(True)
        self.rates_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.rates_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 12px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)

        rates_layout.addWidget(self.rates_table)
        rates_group.setLayout(rates_layout)

        layout.addLayout(rates_toolbar)
        layout.addWidget(rates_group)

        tab.setLayout(layout)
        return tab

    def create_history_tab(self):
        """إنشاء تبويب تاريخ التحويلات"""
        tab = QWidget()
        layout = QVBoxLayout()

        # شريط أدوات التاريخ
        history_toolbar = QHBoxLayout()

        clear_history_btn = QPushButton("🗑️ مسح التاريخ")
        clear_history_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        clear_history_btn.clicked.connect(self.clear_history)

        export_history_btn = QPushButton("📤 تصدير التاريخ")
        export_history_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        export_history_btn.clicked.connect(self.export_history)

        history_toolbar.addWidget(clear_history_btn)
        history_toolbar.addWidget(export_history_btn)
        history_toolbar.addStretch()

        # جدول تاريخ التحويلات
        history_group = QGroupBox("📋 تاريخ التحويلات")
        history_layout = QVBoxLayout()

        self.history_table = QTableWidget()
        self.history_table.setColumnCount(6)
        self.history_table.setHorizontalHeaderLabels([
            "التاريخ", "الوقت", "المبلغ", "من", "إلى", "النتيجة"
        ])

        # تنسيق الجدول
        self.history_table.horizontalHeader().setStretchLastSection(True)
        self.history_table.setAlternatingRowColors(True)
        self.history_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.history_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 11px;
            }
            QTableWidget::item:selected {
                background-color: #17a2b8;
                color: white;
            }
            QHeaderView::section {
                background-color: #17a2b8;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)

        history_layout.addWidget(self.history_table)
        history_group.setLayout(history_layout)

        layout.addLayout(history_toolbar)
        layout.addWidget(history_group)

        tab.setLayout(layout)
        return tab

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة إعدادات العرض
        display_group = QGroupBox("🎨 إعدادات العرض")
        display_layout = QGridLayout()

        # عدد الخانات العشرية
        display_layout.addWidget(QLabel("عدد الخانات العشرية:"), 0, 0)
        self.decimal_places = QSpinBox()
        self.decimal_places.setRange(0, 8)
        self.decimal_places.setValue(2)
        display_layout.addWidget(self.decimal_places, 0, 1)

        # العملة الافتراضية
        display_layout.addWidget(QLabel("العملة الافتراضية:"), 0, 2)
        self.default_currency = QComboBox()
        display_layout.addWidget(self.default_currency, 0, 3)

        # تحديث تلقائي
        self.auto_update = QCheckBox("تحديث الأسعار تلقائياً")
        self.auto_update.setChecked(True)
        display_layout.addWidget(self.auto_update, 1, 0, 1, 2)

        # إظهار التغييرات
        self.show_changes = QCheckBox("إظهار تغييرات الأسعار")
        self.show_changes.setChecked(True)
        display_layout.addWidget(self.show_changes, 1, 2, 1, 2)

        display_group.setLayout(display_layout)

        # مجموعة العملات المفضلة
        favorites_group = QGroupBox("⭐ إدارة العملات المفضلة")
        favorites_layout = QVBoxLayout()

        # قائمة العملات المتاحة
        available_currencies_layout = QHBoxLayout()

        available_currencies_layout.addWidget(QLabel("العملات المتاحة:"))
        self.available_currencies = QComboBox()
        available_currencies_layout.addWidget(self.available_currencies)

        add_favorite_btn = QPushButton("➕ إضافة للمفضلة")
        add_favorite_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_favorite_btn.clicked.connect(self.add_to_favorites)
        available_currencies_layout.addWidget(add_favorite_btn)

        # قائمة المفضلة
        favorites_management_layout = QHBoxLayout()

        self.favorites_management_list = QListWidget()
        self.favorites_management_list.setMaximumHeight(120)
        self.favorites_management_list.setStyleSheet("""
            QListWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                margin: 2px;
                background-color: white;
                border-radius: 3px;
            }
            QListWidget::item:selected {
                background-color: #ffc107;
                color: white;
            }
        """)

        remove_favorite_btn = QPushButton("➖ إزالة من المفضلة")
        remove_favorite_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        remove_favorite_btn.clicked.connect(self.remove_from_favorites)

        favorites_buttons_layout = QVBoxLayout()
        favorites_buttons_layout.addWidget(remove_favorite_btn)
        favorites_buttons_layout.addStretch()

        favorites_management_layout.addWidget(self.favorites_management_list)
        favorites_management_layout.addLayout(favorites_buttons_layout)

        favorites_layout.addLayout(available_currencies_layout)
        favorites_layout.addLayout(favorites_management_layout)
        favorites_group.setLayout(favorites_layout)

        # زر حفظ الإعدادات
        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        save_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        save_settings_btn.clicked.connect(self.save_settings)

        layout.addWidget(display_group)
        layout.addWidget(favorites_group)
        layout.addWidget(save_settings_btn)
        layout.addStretch()

        tab.setLayout(layout)
        return tab

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        # زر المساعدة
        help_btn = QPushButton("❓ المساعدة")
        help_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        help_btn.clicked.connect(self.show_help)

        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_btn.clicked.connect(self.close_converter)

        layout.addWidget(help_btn)
        layout.addStretch()
        layout.addWidget(close_btn)

        return layout

    # الوظائف الأساسية
    def load_currency_data(self):
        """تحميل بيانات العملات"""
        # قائمة العملات المدعومة
        self.currencies = {
            'USD': 'الدولار الأمريكي',
            'EUR': 'اليورو الأوروبي',
            'GBP': 'الجنيه الإسترليني',
            'JPY': 'الين الياباني',
            'SAR': 'الريال السعودي',
            'AED': 'الدرهم الإماراتي',
            'YER': 'الريال اليمني',
            'EGP': 'الجنيه المصري',
            'JOD': 'الدينار الأردني',
            'KWD': 'الدينار الكويتي',
            'QAR': 'الريال القطري',
            'BHD': 'الدينار البحريني',
            'OMR': 'الريال العماني',
            'CNY': 'اليوان الصيني',
            'INR': 'الروبية الهندية',
            'TRY': 'الليرة التركية'
        }

        # تحديث قوائم العملات
        currency_items = [f"{code} - {name}" for code, name in self.currencies.items()]

        self.from_currency.addItems(currency_items)
        self.to_currency.addItems(currency_items)
        self.default_currency.addItems(currency_items)
        self.available_currencies.addItems(currency_items)

        # تعيين القيم الافتراضية
        self.from_currency.setCurrentText("USD - الدولار الأمريكي")
        self.to_currency.setCurrentText("YER - الريال اليمني")

        # تحديث قائمة المفضلة
        self.update_favorites_display()

    def start_currency_updates(self):
        """بدء تحديث أسعار العملات"""
        self.currency_updater = CurrencyUpdateThread()
        self.currency_updater.rates_updated.connect(self.update_exchange_rates)
        self.currency_updater.start()

    def update_exchange_rates(self, rates):
        """تحديث أسعار الصرف"""
        self.exchange_rates = rates
        self.update_rates_table()
        self.auto_convert()

        # تحديث وقت آخر تحديث
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.last_update_label.setText(f"آخر تحديث: {current_time}")

    def update_rates_table(self):
        """تحديث جدول أسعار العملات"""
        self.rates_table.setRowCount(len(self.exchange_rates))

        for row, (code, rate) in enumerate(self.exchange_rates.items()):
            # اسم العملة
            currency_name = self.currencies.get(code, code)
            self.rates_table.setItem(row, 0, QTableWidgetItem(currency_name))

            # رمز العملة
            self.rates_table.setItem(row, 1, QTableWidgetItem(code))

            # السعر
            self.rates_table.setItem(row, 2, QTableWidgetItem(f"{rate:.4f}"))

            # التغيير (محاكاة)
            change = "📈 +0.5%" if row % 2 == 0 else "📉 -0.3%"
            change_item = QTableWidgetItem(change)
            if "📈" in change:
                change_item.setBackground(QColor("#d4edda"))
            else:
                change_item.setBackground(QColor("#f8d7da"))
            self.rates_table.setItem(row, 3, change_item)

    def auto_convert(self):
        """التحويل التلقائي"""
        try:
            amount = self.amount_input.value()
            from_code = self.from_currency.currentText().split(' - ')[0]
            to_code = self.to_currency.currentText().split(' - ')[0]

            if from_code in self.exchange_rates and to_code in self.exchange_rates:
                # تحويل عبر الدولار الأمريكي كعملة أساس
                from_rate = self.exchange_rates[from_code]
                to_rate = self.exchange_rates[to_code]

                # تحويل إلى دولار أولاً ثم إلى العملة المطلوبة
                usd_amount = amount / from_rate if from_code != 'USD' else amount
                result = usd_amount * to_rate if to_code != 'USD' else usd_amount

                # عرض النتيجة
                decimal_places = self.decimal_places.value()
                self.result_label.setText(f"{result:.{decimal_places}f} {to_code}")

                # عرض معلومات السعر
                rate = to_rate / from_rate if from_code != 'USD' else to_rate
                self.rate_info_label.setText(f"1 {from_code} = {rate:.4f} {to_code}")

                # إضافة للتاريخ
                self.add_to_history(amount, from_code, to_code, result)

        except (ValueError, KeyError, ZeroDivisionError):
            self.result_label.setText("خطأ في التحويل")
            self.rate_info_label.setText("سعر غير متاح")

    def add_to_history(self, amount, from_code, to_code, result):
        """إضافة التحويل للتاريخ"""
        conversion = {
            'date': datetime.now().strftime("%Y-%m-%d"),
            'time': datetime.now().strftime("%H:%M:%S"),
            'amount': amount,
            'from_currency': from_code,
            'to_currency': to_code,
            'result': result
        }

        self.conversion_history.append(conversion)

        # الاحتفاظ بآخر 100 تحويل فقط
        if len(self.conversion_history) > 100:
            self.conversion_history.pop(0)

        self.update_history_table()

    def update_history_table(self):
        """تحديث جدول التاريخ"""
        self.history_table.setRowCount(len(self.conversion_history))

        for row, conversion in enumerate(reversed(self.conversion_history)):
            self.history_table.setItem(row, 0, QTableWidgetItem(conversion['date']))
            self.history_table.setItem(row, 1, QTableWidgetItem(conversion['time']))
            self.history_table.setItem(row, 2, QTableWidgetItem(f"{conversion['amount']:.2f}"))
            self.history_table.setItem(row, 3, QTableWidgetItem(conversion['from_currency']))
            self.history_table.setItem(row, 4, QTableWidgetItem(conversion['to_currency']))
            self.history_table.setItem(row, 5, QTableWidgetItem(f"{conversion['result']:.2f}"))

    def swap_currencies(self):
        """تبديل العملات"""
        from_index = self.from_currency.currentIndex()
        to_index = self.to_currency.currentIndex()

        self.from_currency.setCurrentIndex(to_index)
        self.to_currency.setCurrentIndex(from_index)

    def set_quick_amount(self, amount):
        """تعيين مبلغ سريع"""
        self.amount_input.setValue(amount)

    def update_favorites_display(self):
        """تحديث عرض المفضلة"""
        self.favorites_list.clear()
        self.favorites_management_list.clear()

        for currency_code in self.favorite_currencies:
            if currency_code in self.currencies:
                display_text = f"{currency_code} - {self.currencies[currency_code]}"
                self.favorites_list.addItem(display_text)
                self.favorites_management_list.addItem(display_text)

    def use_favorite_currency(self, item):
        """استخدام عملة مفضلة"""
        currency_code = item.text().split(' - ')[0]
        currency_text = item.text()

        # تعيين كعملة مصدر
        self.from_currency.setCurrentText(currency_text)

    def add_to_favorites(self):
        """إضافة عملة للمفضلة"""
        selected_currency = self.available_currencies.currentText()
        currency_code = selected_currency.split(' - ')[0]

        if currency_code not in self.favorite_currencies:
            self.favorite_currencies.append(currency_code)
            self.update_favorites_display()
            QMessageBox.information(self, "تم", f"تم إضافة {selected_currency} للمفضلة")
        else:
            QMessageBox.information(self, "تنبيه", "العملة موجودة في المفضلة مسبقاً")

    def remove_from_favorites(self):
        """إزالة عملة من المفضلة"""
        current_item = self.favorites_management_list.currentItem()
        if current_item:
            currency_code = current_item.text().split(' - ')[0]
            if currency_code in self.favorite_currencies:
                self.favorite_currencies.remove(currency_code)
                self.update_favorites_display()
                QMessageBox.information(self, "تم", f"تم إزالة {current_item.text()} من المفضلة")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عملة لإزالتها")

    def refresh_rates(self):
        """تحديث الأسعار يدوياً"""
        if self.currency_updater:
            # محاكاة تحديث فوري
            rates = self.currency_updater.get_mock_exchange_rates()
            self.update_exchange_rates(rates)
            QMessageBox.information(self, "تم", "تم تحديث أسعار العملات")

    def clear_history(self):
        """مسح تاريخ التحويلات"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح جميع تاريخ التحويلات؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.conversion_history.clear()
            self.update_history_table()
            QMessageBox.information(self, "تم", "تم مسح تاريخ التحويلات")

    def export_history(self):
        """تصدير تاريخ التحويلات"""
        if not self.conversion_history:
            QMessageBox.information(self, "تنبيه", "لا يوجد تاريخ للتصدير")
            return

        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير تاريخ التحويلات",
                f"currency_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv);;All Files (*)"
            )

            if file_path:
                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['التاريخ', 'الوقت', 'المبلغ', 'من', 'إلى', 'النتيجة']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()

                    for conversion in self.conversion_history:
                        writer.writerow({
                            'التاريخ': conversion['date'],
                            'الوقت': conversion['time'],
                            'المبلغ': conversion['amount'],
                            'من': conversion['from_currency'],
                            'إلى': conversion['to_currency'],
                            'النتيجة': conversion['result']
                        })

                QMessageBox.information(self, "نجح", f"تم تصدير التاريخ إلى:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التاريخ:\n{str(e)}")

    def save_settings(self):
        """حفظ الإعدادات"""
        # تطبيق عدد الخانات العشرية
        self.amount_input.setDecimals(self.decimal_places.value())

        # تطبيق العملة الافتراضية
        default_currency_text = self.default_currency.currentText()
        self.from_currency.setCurrentText(default_currency_text)

        QMessageBox.information(self, "تم", "تم حفظ الإعدادات بنجاح")

    def close_converter(self):
        """إغلاق محول العملات"""
        if self.currency_updater:
            self.currency_updater.stop()
            self.currency_updater.wait()
        self.reject()

    def show_help(self):
        """عرض المساعدة"""
        help_text = """
💱 مساعدة محول العملات المتقدم

📋 الميزات المتاحة:

⚡ التحويل السريع:
• تحويل فوري بين 16 عملة مختلفة
• تحديث تلقائي للنتائج عند تغيير المبلغ
• أزرار مبالغ سريعة (1, 10, 100, 1000, 10000)
• زر تبديل العملات السريع
• عرض سعر الصرف الحالي

📊 أسعار العملات:
• جدول شامل لجميع أسعار الصرف
• تحديث تلقائي كل 30 ثانية
• عرض التغييرات في الأسعار
• إمكانية التحديث اليدوي

📋 تاريخ التحويلات:
• حفظ آخر 100 تحويل
• عرض التاريخ والوقت لكل تحويل
• تصدير التاريخ إلى CSV
• إمكانية مسح التاريخ

⚙️ الإعدادات:
• تخصيص عدد الخانات العشرية (0-8)
• تحديد العملة الافتراضية
• إدارة العملات المفضلة
• تفعيل/إلغاء التحديث التلقائي

💰 العملات المدعومة:
• USD - الدولار الأمريكي
• EUR - اليورو الأوروبي
• GBP - الجنيه الإسترليني
• JPY - الين الياباني
• SAR - الريال السعودي
• AED - الدرهم الإماراتي
• YER - الريال اليمني
• EGP - الجنيه المصري
• JOD - الدينار الأردني
• KWD - الدينار الكويتي
• QAR - الريال القطري
• BHD - الدينار البحريني
• OMR - الريال العماني
• CNY - اليوان الصيني
• INR - الروبية الهندية
• TRY - الليرة التركية

⭐ العملات المفضلة:
• إضافة العملات المستخدمة بكثرة
• وصول سريع للعملات المفضلة
• إدارة سهلة للقائمة

💡 نصائح الاستخدام:
• استخدم الأزرار السريعة للمبالغ الشائعة
• انقر مزدوج على العملة المفضلة لاستخدامها
• راقب التغييرات في الأسعار
• صدر التاريخ للاحتفاظ بسجل التحويلات

🔄 التحديث التلقائي:
• الأسعار تتحدث كل 30 ثانية
• يمكن إيقاف التحديث التلقائي
• إمكانية التحديث اليدوي في أي وقت

⚠️ ملاحظة:
الأسعار المعروضة هي أسعار تجريبية لأغراض التوضيح.
في التطبيق الحقيقي، يتم الحصول على الأسعار من مصادر موثوقة.
        """

        QMessageBox.information(self, "المساعدة", help_text)


def main():
    """اختبار محول العملات"""
    import sys

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    dialog = CurrencyConverterDialog()
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
