#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة المنتجات والخدمات
Products and Services Management Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QDoubleSpinBox, QCheckBox,
                             QDateEdit, QSplitter, QFrame, QDialogButtonBox,
                             QScrollArea, QProgressBar)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QP<PERSON>map, QPainter
from datetime import datetime

from ..database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class ProductDialog(QDialog):
    """حوار إضافة/تعديل المنتجات والخدمات"""
    
    def __init__(self, db_manager: SQLiteManager, product_data=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.product_data = product_data
        self.is_edit_mode = product_data is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_product_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل المنتج/الخدمة" if self.is_edit_mode else "إضافة منتج/خدمة جديد"
        self.setWindowTitle(title)
        self.setFixedSize(700, 800)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel(f"📦 {title}")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        main_layout.addWidget(title_label)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()
        
        # معلومات أساسية
        basic_group = QGroupBox("📋 المعلومات الأساسية")
        basic_layout = QFormLayout()
        
        # كود المنتج
        self.product_code_edit = QLineEdit()
        self.product_code_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً إذا ترك فارغاً")
        basic_layout.addRow("كود المنتج:", self.product_code_edit)
        
        # اسم المنتج
        self.product_name_edit = QLineEdit()
        self.product_name_edit.setPlaceholderText("أدخل اسم المنتج أو الخدمة...")
        basic_layout.addRow("اسم المنتج/الخدمة *:", self.product_name_edit)
        
        # نوع المنتج
        self.product_type_combo = QComboBox()
        self.product_type_combo.addItem("منتج", "product")
        self.product_type_combo.addItem("خدمة", "service")
        basic_layout.addRow("النوع:", self.product_type_combo)
        
        # الفئة
        self.category_combo = QComboBox()
        self.category_combo.setEditable(True)
        self.load_categories()
        basic_layout.addRow("الفئة:", self.category_combo)
        
        # الوحدة
        self.unit_combo = QComboBox()
        self.unit_combo.setEditable(True)
        self.unit_combo.addItems([
            "قطعة", "كيلو", "جرام", "لتر", "متر", "متر مربع", "متر مكعب",
            "صندوق", "كرتون", "باكيت", "علبة", "زجاجة", "كيس", "ساعة",
            "يوم", "شهر", "سنة", "خدمة", "استشارة", "دورة", "رخصة"
        ])
        self.unit_combo.setCurrentText("قطعة")
        basic_layout.addRow("الوحدة:", self.unit_combo)
        
        basic_group.setLayout(basic_layout)
        scroll_layout.addWidget(basic_group)
        
        # معلومات الأسعار
        pricing_group = QGroupBox("💰 معلومات الأسعار")
        pricing_layout = QFormLayout()
        
        # سعر التكلفة
        self.cost_price_spin = QDoubleSpinBox()
        self.cost_price_spin.setRange(0, 9999999.99)
        self.cost_price_spin.setSuffix(" ريال")
        self.cost_price_spin.setDecimals(2)
        pricing_layout.addRow("سعر التكلفة:", self.cost_price_spin)
        
        # سعر البيع
        self.selling_price_spin = QDoubleSpinBox()
        self.selling_price_spin.setRange(0, 9999999.99)
        self.selling_price_spin.setSuffix(" ريال")
        self.selling_price_spin.setDecimals(2)
        pricing_layout.addRow("سعر البيع *:", self.selling_price_spin)
        
        # نسبة الضريبة
        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setRange(0, 100)
        self.tax_rate_spin.setSuffix(" %")
        self.tax_rate_spin.setValue(15.00)
        self.tax_rate_spin.setDecimals(2)
        pricing_layout.addRow("نسبة الضريبة:", self.tax_rate_spin)
        
        # هامش الربح (للعرض فقط)
        self.profit_margin_label = QLabel("0.00 %")
        self.profit_margin_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        pricing_layout.addRow("هامش الربح:", self.profit_margin_label)
        
        pricing_group.setLayout(pricing_layout)
        scroll_layout.addWidget(pricing_group)
        
        # معلومات المخزون
        inventory_group = QGroupBox("📦 معلومات المخزون")
        inventory_layout = QFormLayout()
        
        # الكمية الحالية
        self.stock_quantity_spin = QSpinBox()
        self.stock_quantity_spin.setRange(0, 999999)
        self.stock_quantity_spin.setValue(0)
        inventory_layout.addRow("الكمية الحالية:", self.stock_quantity_spin)
        
        # الحد الأدنى
        self.min_stock_spin = QSpinBox()
        self.min_stock_spin.setRange(0, 999999)
        self.min_stock_spin.setValue(0)
        inventory_layout.addRow("الحد الأدنى:", self.min_stock_spin)
        
        # الحد الأقصى
        self.max_stock_spin = QSpinBox()
        self.max_stock_spin.setRange(0, 999999)
        self.max_stock_spin.setValue(0)
        inventory_layout.addRow("الحد الأقصى:", self.max_stock_spin)
        
        # الباركود
        self.barcode_edit = QLineEdit()
        self.barcode_edit.setPlaceholderText("أدخل الباركود...")
        inventory_layout.addRow("الباركود:", self.barcode_edit)
        
        inventory_group.setLayout(inventory_layout)
        scroll_layout.addWidget(inventory_group)
        
        # معلومات إضافية
        additional_group = QGroupBox("📝 معلومات إضافية")
        additional_layout = QFormLayout()
        
        # الوصف
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        self.description_edit.setPlaceholderText("وصف تفصيلي للمنتج أو الخدمة...")
        additional_layout.addRow("الوصف:", self.description_edit)
        
        # نشط
        self.is_active_check = QCheckBox("المنتج/الخدمة نشط")
        self.is_active_check.setChecked(True)
        additional_layout.addRow("", self.is_active_check)
        
        additional_group.setLayout(additional_layout)
        scroll_layout.addWidget(additional_group)
        
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        main_layout.addWidget(button_box)
        
        self.setLayout(main_layout)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #2196F3;
            }
            QPushButton {
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_product)
        self.cancel_btn.clicked.connect(self.reject)
        self.product_type_combo.currentTextChanged.connect(self.on_product_type_changed)
        self.cost_price_spin.valueChanged.connect(self.calculate_profit_margin)
        self.selling_price_spin.valueChanged.connect(self.calculate_profit_margin)
    
    def load_categories(self):
        """تحميل الفئات من قاعدة البيانات"""
        try:
            # الحصول على الفئات الموجودة
            query = "SELECT DISTINCT category FROM products WHERE category IS NOT NULL AND category != '' ORDER BY category"
            result = self.db_manager.execute_query(query)
            
            categories = ["إلكترونيات", "خدمات تقنية", "برمجيات", "إكسسوارات", "استشارات", "تدريب"]
            
            if result:
                db_categories = [row['category'] for row in result]
                categories.extend([cat for cat in db_categories if cat not in categories])
            
            self.category_combo.addItems(categories)
            
        except Exception as e:
            print(f"خطأ في تحميل الفئات: {e}")
    
    def on_product_type_changed(self):
        """معالجة تغيير نوع المنتج"""
        product_type = self.product_type_combo.currentData()
        
        # تعديل الحقول حسب النوع
        if product_type == "service":
            # للخدمات
            self.stock_quantity_spin.setValue(0)
            self.stock_quantity_spin.setEnabled(False)
            self.min_stock_spin.setValue(0)
            self.min_stock_spin.setEnabled(False)
            self.max_stock_spin.setValue(0)
            self.max_stock_spin.setEnabled(False)
            self.barcode_edit.setEnabled(False)
            self.barcode_edit.clear()
            
            # تغيير الوحدة للخدمات
            if self.unit_combo.currentText() in ["قطعة", "كيلو", "جرام"]:
                self.unit_combo.setCurrentText("ساعة")
        else:
            # للمنتجات
            self.stock_quantity_spin.setEnabled(True)
            self.min_stock_spin.setEnabled(True)
            self.max_stock_spin.setEnabled(True)
            self.barcode_edit.setEnabled(True)
            
            # تغيير الوحدة للمنتجات
            if self.unit_combo.currentText() in ["ساعة", "يوم", "خدمة"]:
                self.unit_combo.setCurrentText("قطعة")
    
    def calculate_profit_margin(self):
        """حساب هامش الربح"""
        try:
            cost_price = self.cost_price_spin.value()
            selling_price = self.selling_price_spin.value()
            
            if cost_price > 0:
                profit_margin = ((selling_price - cost_price) / cost_price) * 100
                self.profit_margin_label.setText(f"{profit_margin:.2f} %")
                
                # تغيير اللون حسب الهامش
                if profit_margin > 20:
                    color = "#27ae60"  # أخضر
                elif profit_margin > 10:
                    color = "#f39c12"  # برتقالي
                else:
                    color = "#e74c3c"  # أحمر
                
                self.profit_margin_label.setStyleSheet(f"color: {color}; font-weight: bold;")
            else:
                self.profit_margin_label.setText("0.00 %")
                self.profit_margin_label.setStyleSheet("color: #7f8c8d; font-weight: bold;")
                
        except:
            self.profit_margin_label.setText("0.00 %")
    
    def load_product_data(self):
        """تحميل بيانات المنتج للتعديل"""
        if not self.product_data:
            return
        
        data = self.product_data
        
        # المعلومات الأساسية
        self.product_code_edit.setText(data.get('product_code', ''))
        self.product_code_edit.setEnabled(False)  # لا يمكن تعديل الكود
        self.product_name_edit.setText(data.get('product_name', ''))
        
        # نوع المنتج
        product_type = data.get('product_type', 'product')
        for i in range(self.product_type_combo.count()):
            if self.product_type_combo.itemData(i) == product_type:
                self.product_type_combo.setCurrentIndex(i)
                break
        
        # الفئة والوحدة
        category = data.get('category', '')
        if category:
            index = self.category_combo.findText(category)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)
            else:
                self.category_combo.setCurrentText(category)
        
        unit = data.get('unit', 'قطعة')
        index = self.unit_combo.findText(unit)
        if index >= 0:
            self.unit_combo.setCurrentIndex(index)
        else:
            self.unit_combo.setCurrentText(unit)
        
        # الأسعار
        self.cost_price_spin.setValue(float(data.get('cost_price', 0)))
        self.selling_price_spin.setValue(float(data.get('selling_price', 0)))
        self.tax_rate_spin.setValue(float(data.get('tax_rate', 15)))
        
        # المخزون
        self.stock_quantity_spin.setValue(int(data.get('stock_quantity', 0)))
        self.min_stock_spin.setValue(int(data.get('min_stock_level', 0)))
        self.max_stock_spin.setValue(int(data.get('max_stock_level', 0)))
        self.barcode_edit.setText(data.get('barcode', ''))
        
        # معلومات إضافية
        self.description_edit.setPlainText(data.get('description', ''))
        self.is_active_check.setChecked(bool(data.get('is_active', True)))
        
        # حساب هامش الربح
        self.calculate_profit_margin()
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.product_name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المنتج أو الخدمة")
            self.product_name_edit.setFocus()
            return False
        
        if self.selling_price_spin.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال سعر بيع صحيح")
            self.selling_price_spin.setFocus()
            return False
        
        # التحقق من الباركود إذا تم إدخاله
        barcode = self.barcode_edit.text().strip()
        if barcode:
            # التحقق من عدم تكرار الباركود
            query = "SELECT COUNT(*) as count FROM products WHERE barcode = ?"
            params = [barcode]
            
            if self.is_edit_mode:
                query += " AND id != ?"
                params.append(self.product_data['id'])
            
            result = self.db_manager.execute_query(query, params)
            if result and result[0]['count'] > 0:
                QMessageBox.warning(self, "تحذير", "هذا الباركود مستخدم بالفعل")
                self.barcode_edit.setFocus()
                return False
        
        return True
    
    def generate_product_code(self):
        """إنشاء كود منتج تلقائي"""
        try:
            # الحصول على آخر كود منتج
            query = "SELECT product_code FROM products ORDER BY id DESC LIMIT 1"
            result = self.db_manager.execute_query(query)
            
            if result:
                last_code = result[0]['product_code']
                # استخراج الرقم من الكود
                if last_code.startswith('PROD'):
                    try:
                        last_number = int(last_code[4:])
                        new_number = last_number + 1
                        return f"PROD{new_number:03d}"
                    except:
                        pass
            
            # إذا لم يوجد كود سابق أو حدث خطأ
            return "PROD001"
            
        except:
            return "PROD001"
    
    def save_product(self):
        """حفظ بيانات المنتج"""
        if not self.validate_data():
            return
        
        try:
            # جمع البيانات
            product_code = self.product_code_edit.text().strip()
            if not product_code:
                product_code = self.generate_product_code()
            
            data = {
                'product_code': product_code,
                'product_name': self.product_name_edit.text().strip(),
                'product_type': self.product_type_combo.currentData(),
                'category': self.category_combo.currentText().strip(),
                'unit': self.unit_combo.currentText().strip(),
                'cost_price': self.cost_price_spin.value(),
                'selling_price': self.selling_price_spin.value(),
                'tax_rate': self.tax_rate_spin.value(),
                'stock_quantity': self.stock_quantity_spin.value(),
                'min_stock_level': self.min_stock_spin.value(),
                'max_stock_level': self.max_stock_spin.value(),
                'barcode': self.barcode_edit.text().strip(),
                'description': self.description_edit.toPlainText().strip(),
                'is_active': self.is_active_check.isChecked()
            }
            
            if self.is_edit_mode:
                # تحديث المنتج
                query = """
                    UPDATE products SET
                        product_name = ?, product_type = ?, category = ?, unit = ?,
                        cost_price = ?, selling_price = ?, tax_rate = ?, stock_quantity = ?,
                        min_stock_level = ?, max_stock_level = ?, barcode = ?, description = ?,
                        is_active = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """
                params = (
                    data['product_name'], data['product_type'], data['category'], data['unit'],
                    data['cost_price'], data['selling_price'], data['tax_rate'], data['stock_quantity'],
                    data['min_stock_level'], data['max_stock_level'], data['barcode'], data['description'],
                    data['is_active'], self.product_data['id']
                )
            else:
                # إضافة منتج جديد
                query = """
                    INSERT INTO products (
                        product_code, product_name, product_type, category, unit,
                        cost_price, selling_price, tax_rate, stock_quantity,
                        min_stock_level, max_stock_level, barcode, description, is_active
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (
                    data['product_code'], data['product_name'], data['product_type'], data['category'],
                    data['unit'], data['cost_price'], data['selling_price'], data['tax_rate'],
                    data['stock_quantity'], data['min_stock_level'], data['max_stock_level'],
                    data['barcode'], data['description'], data['is_active']
                )
            
            self.db_manager.execute_update(query, params)
            
            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} المنتج/الخدمة بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المنتج/الخدمة:\n{str(e)}")


class ProductsWidget(QWidget):
    """وحدة إدارة المنتجات والخدمات"""

    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.setup_connections()
        self.load_products()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # العنوان
        title = QLabel("📦 إدارة المنتجات والخدمات")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        main_layout.addWidget(title)

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        self.add_btn = QPushButton("➕ إضافة منتج/خدمة")
        self.edit_btn = QPushButton("✏️ تعديل")
        self.delete_btn = QPushButton("🗑️ حذف")
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.export_btn = QPushButton("📤 تصدير")
        self.import_btn = QPushButton("📥 استيراد")

        # تنسيق الأزرار
        buttons = [self.add_btn, self.edit_btn, self.delete_btn, self.refresh_btn, self.export_btn, self.import_btn]

        for btn in buttons:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QPushButton:pressed {
                    background-color: #21618c;
                }
                QPushButton:disabled {
                    background-color: #bdc3c7;
                }
            """)

        # ألوان خاصة لبعض الأزرار
        self.add_btn.setStyleSheet(self.add_btn.styleSheet().replace("#3498db", "#27ae60"))
        self.delete_btn.setStyleSheet(self.delete_btn.styleSheet().replace("#3498db", "#e74c3c"))
        self.export_btn.setStyleSheet(self.export_btn.styleSheet().replace("#3498db", "#f39c12"))
        self.import_btn.setStyleSheet(self.import_btn.styleSheet().replace("#3498db", "#9b59b6"))

        toolbar_layout.addWidget(self.add_btn)
        toolbar_layout.addWidget(self.edit_btn)
        toolbar_layout.addWidget(self.delete_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addWidget(self.export_btn)
        toolbar_layout.addWidget(self.import_btn)
        toolbar_layout.addStretch()

        main_layout.addLayout(toolbar_layout)

        # منطقة البحث والفلترة
        search_layout = QHBoxLayout()

        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في المنتجات والخدمات...")
        search_layout.addWidget(self.search_edit)

        search_layout.addWidget(QLabel("النوع:"))
        self.type_filter = QComboBox()
        self.type_filter.addItems(["الكل", "منتجات", "خدمات"])
        search_layout.addWidget(self.type_filter)

        search_layout.addWidget(QLabel("الفئة:"))
        self.category_filter = QComboBox()
        self.load_category_filter()
        search_layout.addWidget(self.category_filter)

        search_layout.addWidget(QLabel("الحالة:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "نشط", "غير نشط", "مخزون منخفض"])
        search_layout.addWidget(self.status_filter)

        main_layout.addLayout(search_layout)

        # جدول المنتجات
        self.products_table = QTableWidget()
        columns = ["الكود", "اسم المنتج/الخدمة", "النوع", "الفئة", "الوحدة", "سعر التكلفة", "سعر البيع", "المخزون", "الحالة"]
        self.products_table.setColumnCount(len(columns))
        self.products_table.setHorizontalHeaderLabels(columns)

        # تنسيق الجدول
        self.products_table.setAlternatingRowColors(True)
        self.products_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.products_table.setSelectionMode(QTableWidget.SingleSelection)
        self.products_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.products_table.horizontalHeader()
        header.resizeSection(0, 100)  # الكود
        header.resizeSection(1, 250)  # اسم المنتج
        header.resizeSection(2, 80)   # النوع
        header.resizeSection(3, 120)  # الفئة
        header.resizeSection(4, 80)   # الوحدة
        header.resizeSection(5, 100)  # سعر التكلفة
        header.resizeSection(6, 100)  # سعر البيع
        header.resizeSection(7, 80)   # المخزون
        header.setStretchLastSection(True)  # الحالة

        # ربط النقر المزدوج
        self.products_table.itemDoubleClicked.connect(self.edit_product)

        main_layout.addWidget(self.products_table)

        # شريط الحالة
        status_layout = QHBoxLayout()
        self.products_count_label = QLabel("عدد المنتجات: 0")
        self.active_products_label = QLabel("النشطة: 0")
        self.low_stock_label = QLabel("مخزون منخفض: 0")
        self.total_value_label = QLabel("إجمالي قيمة المخزون: 0 ريال")

        status_layout.addWidget(self.products_count_label)
        status_layout.addWidget(self.active_products_label)
        status_layout.addWidget(self.low_stock_label)
        status_layout.addStretch()
        status_layout.addWidget(self.total_value_label)

        main_layout.addLayout(status_layout)

        self.setLayout(main_layout)
        self.setStyleSheet(self.get_module_stylesheet())

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.add_btn.clicked.connect(self.add_product)
        self.edit_btn.clicked.connect(self.edit_product)
        self.delete_btn.clicked.connect(self.delete_product)
        self.refresh_btn.clicked.connect(self.load_products)
        self.export_btn.clicked.connect(self.export_products)
        self.import_btn.clicked.connect(self.import_products)

        self.search_edit.textChanged.connect(self.filter_products)
        self.type_filter.currentTextChanged.connect(self.filter_products)
        self.category_filter.currentTextChanged.connect(self.filter_products)
        self.status_filter.currentTextChanged.connect(self.filter_products)

    def load_category_filter(self):
        """تحميل فلتر الفئات"""
        try:
            self.category_filter.clear()
            self.category_filter.addItem("الكل")

            query = "SELECT DISTINCT category FROM products WHERE category IS NOT NULL AND category != '' ORDER BY category"
            result = self.db_manager.execute_query(query)

            if result:
                for row in result:
                    self.category_filter.addItem(row['category'])

        except Exception as e:
            print(f"خطأ في تحميل فلتر الفئات: {e}")

    def load_products(self):
        """تحميل المنتجات والخدمات"""
        try:
            query = """
                SELECT id, product_code, product_name, product_type, category, unit,
                       cost_price, selling_price, stock_quantity, min_stock_level, is_active
                FROM products
                ORDER BY product_name
            """

            products = self.db_manager.execute_query(query)
            self.products_table.setRowCount(len(products))

            total_value = 0
            active_count = 0
            low_stock_count = 0

            for row, product in enumerate(products):
                # الكود
                code_item = QTableWidgetItem(product.get('product_code', ''))
                code_item.setData(Qt.UserRole, product.get('id'))
                self.products_table.setItem(row, 0, code_item)

                # اسم المنتج
                self.products_table.setItem(row, 1, QTableWidgetItem(product.get('product_name', '')))

                # النوع
                product_type = product.get('product_type', 'product')
                type_text = "خدمة" if product_type == "service" else "منتج"
                self.products_table.setItem(row, 2, QTableWidgetItem(type_text))

                # الفئة
                self.products_table.setItem(row, 3, QTableWidgetItem(product.get('category', '')))

                # الوحدة
                self.products_table.setItem(row, 4, QTableWidgetItem(product.get('unit', '')))

                # سعر التكلفة
                cost_price = float(product.get('cost_price', 0))
                cost_text = f"{cost_price:,.2f}"
                self.products_table.setItem(row, 5, QTableWidgetItem(cost_text))

                # سعر البيع
                selling_price = float(product.get('selling_price', 0))
                selling_text = f"{selling_price:,.2f}"
                self.products_table.setItem(row, 6, QTableWidgetItem(selling_text))

                # المخزون
                stock_quantity = int(product.get('stock_quantity', 0))
                min_stock = int(product.get('min_stock_level', 0))

                if product_type == "service":
                    stock_text = "غير محدود"
                else:
                    stock_text = str(stock_quantity)
                    total_value += stock_quantity * cost_price

                    # فحص المخزون المنخفض
                    if min_stock > 0 and stock_quantity <= min_stock:
                        low_stock_count += 1

                stock_item = QTableWidgetItem(stock_text)

                # تلوين المخزون المنخفض
                if product_type != "service" and min_stock > 0 and stock_quantity <= min_stock:
                    stock_item.setBackground(QColor("#ffebee"))
                    stock_item.setForeground(QColor("#d32f2f"))

                self.products_table.setItem(row, 7, stock_item)

                # الحالة
                is_active = product.get('is_active', True)
                status_text = "نشط" if is_active else "غير نشط"
                status_item = QTableWidgetItem(status_text)

                if is_active:
                    status_item.setBackground(QColor("#d4edda"))
                    active_count += 1
                else:
                    status_item.setBackground(QColor("#f8d7da"))

                self.products_table.setItem(row, 8, status_item)

            # تحديث الإحصائيات
            self.products_count_label.setText(f"عدد المنتجات: {len(products)}")
            self.active_products_label.setText(f"النشطة: {active_count}")
            self.low_stock_label.setText(f"مخزون منخفض: {low_stock_count}")
            self.total_value_label.setText(f"إجمالي قيمة المخزون: {total_value:,.2f} ريال")

            # تحديث فلتر الفئات
            self.load_category_filter()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المنتجات:\n{str(e)}")

    def add_product(self):
        """إضافة منتج/خدمة جديد"""
        dialog = ProductDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_products()

    def edit_product(self):
        """تعديل المنتج/الخدمة المحدد"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج/خدمة للتعديل")
            return

        try:
            # الحصول على معرف المنتج
            product_id = self.products_table.item(current_row, 0).data(Qt.UserRole)

            # استعلام البيانات الكاملة
            query = "SELECT * FROM products WHERE id = ?"
            result = self.db_manager.execute_query(query, (product_id,))

            if result:
                product_data = result[0]
                dialog = ProductDialog(self.db_manager, product_data, self)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_products()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات المنتج:\n{str(e)}")

    def delete_product(self):
        """حذف المنتج/الخدمة المحدد"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج/خدمة للحذف")
            return

        product_name = self.products_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, 'تأكيد الحذف',
            f'هل أنت متأكد من حذف "{product_name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                product_id = self.products_table.item(current_row, 0).data(Qt.UserRole)

                # التحقق من وجود معاملات للمنتج
                check_query = "SELECT COUNT(*) as count FROM sales_invoice_items WHERE product_id = ?"
                check_result = self.db_manager.execute_query(check_query, (product_id,))

                if check_result and check_result[0]['count'] > 0:
                    QMessageBox.warning(
                        self, "تحذير",
                        "لا يمكن حذف هذا المنتج لأنه مستخدم في فواتير.\n\nيمكنك إلغاء تفعيله بدلاً من الحذف."
                    )
                    return

                # حذف المنتج
                delete_query = "DELETE FROM products WHERE id = ?"
                self.db_manager.execute_update(delete_query, (product_id,))

                QMessageBox.information(self, "نجح", "تم حذف المنتج/الخدمة بنجاح")
                self.load_products()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المنتج:\n{str(e)}")

    def filter_products(self):
        """فلترة المنتجات"""
        search_text = self.search_edit.text().lower()
        type_filter = self.type_filter.currentText()
        category_filter = self.category_filter.currentText()
        status_filter = self.status_filter.currentText()

        for row in range(self.products_table.rowCount()):
            show_row = True

            # فلترة النص
            if search_text:
                row_text = ""
                for col in range(self.products_table.columnCount()):
                    item = self.products_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                if search_text not in row_text:
                    show_row = False

            # فلترة النوع
            if type_filter != "الكل":
                type_item = self.products_table.item(row, 2)
                if type_item:
                    if type_filter == "منتجات" and type_item.text() != "منتج":
                        show_row = False
                    elif type_filter == "خدمات" and type_item.text() != "خدمة":
                        show_row = False

            # فلترة الفئة
            if category_filter != "الكل":
                category_item = self.products_table.item(row, 3)
                if category_item and category_item.text() != category_filter:
                    show_row = False

            # فلترة الحالة
            if status_filter != "الكل":
                if status_filter == "مخزون منخفض":
                    stock_item = self.products_table.item(row, 7)
                    if stock_item and stock_item.background().color() != QColor("#ffebee"):
                        show_row = False
                else:
                    status_item = self.products_table.item(row, 8)
                    if status_item:
                        if status_filter == "نشط" and status_item.text() != "نشط":
                            show_row = False
                        elif status_filter == "غير نشط" and status_item.text() != "غير نشط":
                            show_row = False

            self.products_table.setRowHidden(row, not show_row)

    def export_products(self):
        """تصدير المنتجات"""
        QMessageBox.information(self, "تصدير المنتجات", "وظيفة تصدير المنتجات قيد التطوير")

    def import_products(self):
        """استيراد المنتجات"""
        QMessageBox.information(self, "استيراد المنتجات", "وظيفة استيراد المنتجات قيد التطوير")

    def get_module_stylesheet(self):
        """تنسيق الوحدة"""
        return """
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLineEdit, QComboBox {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #007bff;
                outline: none;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """
