#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الفواتير المحسن - Onyx Pro ERP
Enhanced Invoice System - Onyx Pro ERP
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QLineEdit, QComboBox, QDateEdit, QTableWidget,
                             QTableWidgetItem, QHeaderView, QPushButton, QToolBar, QAction,
                             QFrame, QGroupBox, QTextEdit, QSpinBox, QMessageBox, QTabWidget,
                             QSplitter, QScrollArea, QCheckBox, QProgressBar)
from PyQt5.QtCore import Qt, QDate, QDateTime, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QPalette, QPixmap, QPainter
from PyQt5.QtPrintSupport import QPrintDialog, QPrinter
import sys
import os
import json
from datetime import datetime, timedelta
from decimal import Decimal

class EnhancedInvoiceSystem(QMainWindow):
    """نظام الفواتير المحسن مع جميع الميزات المتقدمة"""

    invoice_saved = pyqtSignal(dict)
    invoice_printed = pyqtSignal(str)

    def __init__(self, db_manager=None, invoice_data=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_invoice_id = None
        self.invoice_items = []
        self.auto_save_timer = QTimer()
        # self.auto_save_timer.timeout.connect(self.auto_save_draft)  # سيتم تفعيله لاحقاً
        self.is_edit_mode = invoice_data is not None

        self.setup_ui()
        self.setup_database()
        self.load_settings()
        self.setup_shortcuts()
        # self.start_auto_save()  # معطل مؤقتاً

        # تحميل بيانات الفاتورة إذا كانت موجودة
        if invoice_data:
            if isinstance(invoice_data, dict) and 'id' in invoice_data:
                self.load_invoice_from_database(invoice_data['id'])
            else:
                self.load_invoice_data(invoice_data)
        else:
            # توليد رقم فاتورة جديد
            self.invoice_number_edit.setText(self.generate_invoice_number())

    def setup_ui(self):
        """إعداد واجهة المستخدم المحسنة"""
        self.setWindowTitle("نظام الفواتير المحسن - Onyx Pro ERP")
        self.setGeometry(50, 50, 1600, 1000)
        self.showMaximized()

        # تطبيق تنسيق احترافي
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
                font-family: "Segoe UI", "Tahoma", "Arial", sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #007bff;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #007bff;
                font-size: 14px;
            }
            QLabel {
                font-size: 12px;
                color: #495057;
                font-weight: 500;
            }
            QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox {
                font-size: 12px;
                padding: 8px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QDateEdit:focus {
                border: 2px solid #007bff;
                background-color: #f8f9ff;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QTableWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                gridline-color: #dee2e6;
            }
            QHeaderView::section {
                background-color: #007bff;
                color: white;
                padding: 10px;
                border: 1px solid #0056b3;
                font-weight: bold;
            }
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e9ecef;
                color: #495057;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            QTabBar::tab:selected {
                background-color: #007bff;
                color: white;
            }
        """)

        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # شريط الأدوات المحسن
        self.create_enhanced_toolbar()

        # منطقة التبويبات الرئيسية
        self.create_main_tabs()
        main_layout.addWidget(self.main_tabs)

        # شريط الحالة المحسن
        self.create_enhanced_status_bar()

        central_widget.setLayout(main_layout)

    def create_enhanced_toolbar(self):
        """إنشاء شريط أدوات محسن"""
        self.toolbar = QToolBar()
        self.toolbar.setFixedHeight(60)
        self.toolbar.setStyleSheet("""
            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #007bff, stop:1 #0056b3);
                border: none;
                spacing: 5px;
                padding: 5px;
            }
            QToolBar QToolButton {
                background-color: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                padding: 10px 15px;
                margin: 2px;
                color: white;
                font-size: 12px;
                font-weight: bold;
                min-width: 80px;
                min-height: 40px;
            }
            QToolBar QToolButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.5);
            }
            QToolBar QToolButton:pressed {
                background-color: rgba(255, 255, 255, 0.3);
            }
        """)

        # أزرار شريط الأدوات المحسنة
        toolbar_actions = [
            ("💾 حفظ", "حفظ الفاتورة (Ctrl+S)", self.save_invoice),
            ("📄 جديد", "فاتورة جديدة (Ctrl+N)", self.new_invoice),
            ("✏️ تعديل", "تعديل الفاتورة (F2)", self.edit_invoice),
            ("🗑️ حذف", "حذف الفاتورة (Delete)", self.delete_invoice),
            ("🖨️ طباعة", "طباعة الفاتورة (Ctrl+P)", self.print_invoice),
            ("👁️ معاينة", "معاينة الطباعة", self.preview_invoice),
            ("📧 إرسال", "إرسال بالبريد الإلكتروني", self.email_invoice),
            ("📊 تقرير", "تقارير الفواتير", self.show_reports),
            ("🔍 بحث", "البحث في الفواتير (Ctrl+F)", self.search_invoices),
            ("⚙️ إعدادات", "إعدادات النظام", self.show_settings),
            ("❓ مساعدة", "المساعدة (F1)", self.show_help)
        ]

        for text, tooltip, callback in toolbar_actions:
            action = QAction(text, self)
            action.setToolTip(tooltip)
            action.triggered.connect(callback)
            self.toolbar.addAction(action)

            # إضافة فواصل
            if text in ["🗑️ حذف", "📧 إرسال", "⚙️ إعدادات"]:
                self.toolbar.addSeparator()

        self.addToolBar(self.toolbar)
"""
نظام الفواتير المحسن والمتطور
Enhanced Invoice System
"""

import sys
import json
from datetime import datetime, date, timedelta
from decimal import Decimal
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QLabel, QLineEdit, QPushButton, 
                             QTableWidget, QTableWidgetItem, QComboBox, 
                             QTextEdit, QGroupBox, QTabWidget, QDateEdit,
                             QDoubleSpinBox, QSpinBox, QCheckBox, QMessageBox,
                             QDialog, QDialogButtonBox, QFormLayout, QFrame,
                             QSplitter, QScrollArea, QProgressBar, QHeaderView,
                             QApplication, QFileDialog, QMenu, QAction,
                             QToolBar, QStatusBar, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QIcon, QPalette, QPixmap, QPainter
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog

class InvoiceValidationEngine:
    """محرك التحقق من صحة الفواتير"""
    
    @staticmethod
    def validate_invoice_data(invoice_data):
        """التحقق من صحة بيانات الفاتورة"""
        errors = []
        warnings = []
        
        # التحقق من البيانات الأساسية
        if not invoice_data.get('invoice_number'):
            errors.append("رقم الفاتورة مطلوب")
            
        if not invoice_data.get('customer_id'):
            errors.append("العميل مطلوب")
            
        if not invoice_data.get('invoice_date'):
            errors.append("تاريخ الفاتورة مطلوب")
            
        # التحقق من الأصناف
        items = invoice_data.get('items', [])
        if not items:
            errors.append("يجب إضافة صنف واحد على الأقل")
        else:
            for i, item in enumerate(items):
                if not item.get('product_id'):
                    errors.append(f"الصنف في السطر {i+1}: رقم الصنف مطلوب")
                    
                if not item.get('quantity') or item.get('quantity') <= 0:
                    errors.append(f"الصنف في السطر {i+1}: الكمية يجب أن تكون أكبر من صفر")
                    
                if not item.get('unit_price') or item.get('unit_price') <= 0:
                    errors.append(f"الصنف في السطر {i+1}: السعر يجب أن تكون أكبر من صفر")
        
        # التحقق من المبالغ
        total_amount = invoice_data.get('total_amount', 0)
        if total_amount <= 0:
            warnings.append("إجمالي الفاتورة يساوي صفر")
            
        return {
            'is_valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }

class InvoiceCalculationEngine:
    """محرك حساب الفواتير"""
    
    @staticmethod
    def calculate_line_total(quantity, unit_price, discount_percentage=0, tax_rate=0):
        """حساب إجمالي السطر"""
        subtotal = Decimal(str(quantity)) * Decimal(str(unit_price))
        discount_amount = subtotal * Decimal(str(discount_percentage)) / 100
        after_discount = subtotal - discount_amount
        tax_amount = after_discount * Decimal(str(tax_rate)) / 100
        total = after_discount + tax_amount
        
        return {
            'subtotal': float(subtotal),
            'discount_amount': float(discount_amount),
            'after_discount': float(after_discount),
            'tax_amount': float(tax_amount),
            'total': float(total)
        }
    
    @staticmethod
    def calculate_invoice_totals(items, global_discount_percentage=0, global_tax_rate=0):
        """حساب إجماليات الفاتورة"""
        subtotal = Decimal('0')
        total_discount = Decimal('0')
        total_tax = Decimal('0')
        
        for item in items:
            line_calc = InvoiceCalculationEngine.calculate_line_total(
                item.get('quantity', 0),
                item.get('unit_price', 0),
                item.get('discount_percentage', 0),
                item.get('tax_rate', global_tax_rate)
            )
            
            subtotal += Decimal(str(line_calc['subtotal']))
            total_discount += Decimal(str(line_calc['discount_amount']))
            total_tax += Decimal(str(line_calc['tax_amount']))
        
        # تطبيق الخصم العام
        global_discount_amount = subtotal * Decimal(str(global_discount_percentage)) / 100
        after_global_discount = subtotal - global_discount_amount
        
        # إعادة حساب الضريبة بعد الخصم العام
        final_tax = after_global_discount * Decimal(str(global_tax_rate)) / 100
        final_total = after_global_discount + final_tax
        
        return {
            'subtotal': float(subtotal),
            'total_discount': float(total_discount + global_discount_amount),
            'global_discount_amount': float(global_discount_amount),
            'total_tax': float(final_tax),
            'total_amount': float(final_total)
        }

class InvoiceNumberGenerator:
    """مولد أرقام الفواتير"""
    
    @staticmethod
    def generate_invoice_number(prefix="INV", date_format="%Y%m", sequence_length=4):
        """توليد رقم فاتورة جديد"""
        current_date = datetime.now()
        date_part = current_date.strftime(date_format)
        
        # هنا يجب الحصول على آخر رقم من قاعدة البيانات
        # مؤقتاً سنستخدم رقم عشوائي
        import random
        sequence = random.randint(1, 9999)
        sequence_part = str(sequence).zfill(sequence_length)
        
        return f"{prefix}-{date_part}-{sequence_part}"

class InvoiceTemplateEngine:
    """محرك قوالب الفواتير"""
    
    @staticmethod
    def get_available_templates():
        """الحصول على القوالب المتاحة"""
        return {
            'classic': {
                'name': 'القالب الكلاسيكي',
                'description': 'قالب بسيط ونظيف',
                'preview': 'classic_preview.png'
            },
            'modern': {
                'name': 'القالب العصري',
                'description': 'قالب حديث وأنيق',
                'preview': 'modern_preview.png'
            },
            'professional': {
                'name': 'القالب المهني',
                'description': 'قالب مهني للشركات',
                'preview': 'professional_preview.png'
            },
            'minimal': {
                'name': 'القالب البسيط',
                'description': 'قالب بسيط جداً',
                'preview': 'minimal_preview.png'
            }
        }
    
    @staticmethod
    def generate_html_invoice(invoice_data, template='classic'):
        """توليد فاتورة HTML"""
        templates = {
            'classic': InvoiceTemplateEngine._generate_classic_template,
            'modern': InvoiceTemplateEngine._generate_modern_template,
            'professional': InvoiceTemplateEngine._generate_professional_template,
            'minimal': InvoiceTemplateEngine._generate_minimal_template
        }
        
        generator = templates.get(template, templates['classic'])
        return generator(invoice_data)
    
    @staticmethod
    def _generate_classic_template(invoice_data):
        """توليد القالب الكلاسيكي"""
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة رقم {invoice_data.get('invoice_number', '')}</title>
            <style>
                body {{
                    font-family: 'Arial', sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f8f9fa;
                }}
                .invoice-container {{
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                }}
                .header {{
                    text-align: center;
                    border-bottom: 3px solid #007bff;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .company-name {{
                    font-size: 28px;
                    font-weight: bold;
                    color: #007bff;
                    margin-bottom: 10px;
                }}
                .invoice-title {{
                    font-size: 24px;
                    color: #333;
                    margin-bottom: 10px;
                }}
                .invoice-number {{
                    font-size: 18px;
                    color: #666;
                }}
                .info-section {{
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 30px;
                }}
                .info-box {{
                    width: 45%;
                    padding: 15px;
                    background-color: #f8f9fa;
                    border-radius: 5px;
                }}
                .info-title {{
                    font-weight: bold;
                    color: #007bff;
                    margin-bottom: 10px;
                    font-size: 16px;
                }}
                .items-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 30px;
                }}
                .items-table th {{
                    background-color: #007bff;
                    color: white;
                    padding: 12px;
                    text-align: center;
                    font-weight: bold;
                }}
                .items-table td {{
                    padding: 10px;
                    text-align: center;
                    border-bottom: 1px solid #dee2e6;
                }}
                .items-table tr:nth-child(even) {{
                    background-color: #f8f9fa;
                }}
                .totals-section {{
                    text-align: left;
                    margin-top: 20px;
                }}
                .total-row {{
                    display: flex;
                    justify-content: space-between;
                    padding: 8px 0;
                    border-bottom: 1px solid #dee2e6;
                }}
                .total-row.final {{
                    font-weight: bold;
                    font-size: 18px;
                    color: #007bff;
                    border-bottom: 3px solid #007bff;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 40px;
                    padding-top: 20px;
                    border-top: 1px solid #dee2e6;
                    color: #666;
                }}
            </style>
        </head>
        <body>
            <div class="invoice-container">
                <div class="header">
                    <div class="company-name">{invoice_data.get('company_name', 'اسم الشركة')}</div>
                    <div class="invoice-title">فاتورة مبيعات</div>
                    <div class="invoice-number">رقم الفاتورة: {invoice_data.get('invoice_number', '')}</div>
                </div>
                
                <div class="info-section">
                    <div class="info-box">
                        <div class="info-title">بيانات العميل:</div>
                        <div><strong>الاسم:</strong> {invoice_data.get('customer_name', '')}</div>
                        <div><strong>الهاتف:</strong> {invoice_data.get('customer_phone', '')}</div>
                        <div><strong>العنوان:</strong> {invoice_data.get('customer_address', '')}</div>
                    </div>
                    <div class="info-box">
                        <div class="info-title">بيانات الفاتورة:</div>
                        <div><strong>التاريخ:</strong> {invoice_data.get('invoice_date', '')}</div>
                        <div><strong>تاريخ الاستحقاق:</strong> {invoice_data.get('due_date', '')}</div>
                        <div><strong>طريقة الدفع:</strong> {invoice_data.get('payment_method', '')}</div>
                    </div>
                </div>
                
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>م</th>
                            <th>اسم الصنف</th>
                            <th>الوحدة</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        
        # إضافة الأصناف
        items = invoice_data.get('items', [])
        for i, item in enumerate(items, 1):
            total = item.get('quantity', 0) * item.get('unit_price', 0)
            html += f"""
                        <tr>
                            <td>{i}</td>
                            <td>{item.get('product_name', '')}</td>
                            <td>{item.get('unit', '')}</td>
                            <td>{item.get('quantity', 0):,.2f}</td>
                            <td>{item.get('unit_price', 0):,.2f}</td>
                            <td>{total:,.2f}</td>
                        </tr>
            """
        
        # إضافة الإجماليات
        html += f"""
                    </tbody>
                </table>
                
                <div class="totals-section">
                    <div class="total-row">
                        <span>المجموع الفرعي:</span>
                        <span>{invoice_data.get('subtotal', 0):,.2f} ريال</span>
                    </div>
                    <div class="total-row">
                        <span>الخصم:</span>
                        <span>{invoice_data.get('discount_amount', 0):,.2f} ريال</span>
                    </div>
                    <div class="total-row">
                        <span>الضريبة:</span>
                        <span>{invoice_data.get('tax_amount', 0):,.2f} ريال</span>
                    </div>
                    <div class="total-row final">
                        <span>الإجمالي النهائي:</span>
                        <span>{invoice_data.get('total_amount', 0):,.2f} ريال</span>
                    </div>
                </div>
                
                <div class="footer">
                    <p>شكراً لتعاملكم معنا</p>
                    <p>تم إنشاء هذه الفاتورة بواسطة نظام إدارة الحسابات</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html
    
    @staticmethod
    def _generate_modern_template(invoice_data):
        """توليد القالب العصري"""
        # سيتم تطوير القوالب الأخرى لاحقاً
        return InvoiceTemplateEngine._generate_classic_template(invoice_data)
    
    @staticmethod
    def _generate_professional_template(invoice_data):
        """توليد القالب المهني"""
        return InvoiceTemplateEngine._generate_classic_template(invoice_data)
    
    @staticmethod
    def _generate_minimal_template(invoice_data):
        """توليد القالب البسيط"""
        return InvoiceTemplateEngine._generate_classic_template(invoice_data)

class InvoiceStatusManager:
    """مدير حالات الفواتير"""
    
    STATUSES = {
        'draft': {'name': 'مسودة', 'color': '#6c757d', 'icon': '📝'},
        'sent': {'name': 'مرسلة', 'color': '#007bff', 'icon': '📤'},
        'paid': {'name': 'مدفوعة', 'color': '#28a745', 'icon': '✅'},
        'partial': {'name': 'مدفوعة جزئياً', 'color': '#ffc107', 'icon': '⚠️'},
        'overdue': {'name': 'متأخرة', 'color': '#dc3545', 'icon': '⏰'},
        'cancelled': {'name': 'ملغية', 'color': '#6c757d', 'icon': '❌'}
    }
    
    @staticmethod
    def get_status_info(status):
        """الحصول على معلومات الحالة"""
        return InvoiceStatusManager.STATUSES.get(status, InvoiceStatusManager.STATUSES['draft'])
    
    @staticmethod
    def calculate_status(total_amount, paid_amount, due_date):
        """حساب حالة الفاتورة تلقائياً"""
        if paid_amount >= total_amount:
            return 'paid'
        elif paid_amount > 0:
            return 'partial'
        elif due_date and datetime.strptime(due_date, '%Y-%m-%d').date() < date.today():
            return 'overdue'
        else:
            return 'sent'

class InvoiceSearchEngine:
    """محرك البحث في الفواتير"""
    
    @staticmethod
    def build_search_query(search_criteria):
        """بناء استعلام البحث"""
        base_query = """
            SELECT si.*, c.customer_name, c.phone, c.email
            FROM sales_invoices si
            LEFT JOIN customers c ON si.customer_id = c.id
            WHERE 1=1
        """
        
        params = []
        
        # البحث بالنص
        if search_criteria.get('search_text'):
            base_query += " AND (si.invoice_number LIKE ? OR c.customer_name LIKE ?)"
            search_text = f"%{search_criteria['search_text']}%"
            params.extend([search_text, search_text])
        
        # البحث بالتاريخ
        if search_criteria.get('date_from'):
            base_query += " AND si.invoice_date >= ?"
            params.append(search_criteria['date_from'])
            
        if search_criteria.get('date_to'):
            base_query += " AND si.invoice_date <= ?"
            params.append(search_criteria['date_to'])
        
        # البحث بالحالة
        if search_criteria.get('status'):
            base_query += " AND si.status = ?"
            params.append(search_criteria['status'])
        
        # البحث بالعميل
        if search_criteria.get('customer_id'):
            base_query += " AND si.customer_id = ?"
            params.append(search_criteria['customer_id'])
        
        # البحث بالمبلغ
        if search_criteria.get('amount_from'):
            base_query += " AND si.total_amount >= ?"
            params.append(search_criteria['amount_from'])
            
        if search_criteria.get('amount_to'):
            base_query += " AND si.total_amount <= ?"
            params.append(search_criteria['amount_to'])
        
        # الترتيب
        order_by = search_criteria.get('order_by', 'invoice_date')
        order_direction = search_criteria.get('order_direction', 'DESC')
        base_query += f" ORDER BY si.{order_by} {order_direction}"
        
        # الحد الأقصى للنتائج
        limit = search_criteria.get('limit', 100)
        base_query += f" LIMIT {limit}"
        
        return base_query, params

class EnhancedInvoiceDialog(QDialog):
    """حوار الفاتورة المحسن"""

    invoice_saved = pyqtSignal(dict)

    def __init__(self, db_manager=None, invoice_data=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.invoice_data = invoice_data or {}
        self.is_edit_mode = bool(invoice_data)
        self.items_data = []
        self.customers_data = []
        self.products_data = []

        self.setup_ui()
        self.load_data()
        self.setup_connections()

        if self.is_edit_mode:
            self.load_invoice_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("فاتورة مبيعات محسنة")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # شريط العنوان
        title_frame = self.create_title_frame()
        main_layout.addWidget(title_frame)

        # المحتوى الرئيسي
        content_splitter = QSplitter(Qt.Horizontal)

        # الجانب الأيسر - معلومات الفاتورة
        left_widget = self.create_invoice_info_widget()
        content_splitter.addWidget(left_widget)

        # الجانب الأيمن - الأصناف والإجماليات
        right_widget = self.create_items_widget()
        content_splitter.addWidget(right_widget)

        content_splitter.setSizes([400, 800])
        main_layout.addWidget(content_splitter)

        # شريط الأزرار
        buttons_layout = self.create_buttons_layout()
        main_layout.addLayout(buttons_layout)

        # شريط الحالة
        self.status_bar = QLabel("جاهز")
        self.status_bar.setStyleSheet("""
            QLabel {
                padding: 5px;
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
            }
        """)
        main_layout.addWidget(self.status_bar)

        self.setLayout(main_layout)

    def create_title_frame(self):
        """إنشاء إطار العنوان"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                margin-bottom: 10px;
            }
        """)

        layout = QHBoxLayout()

        # أيقونة ونص العنوان
        title_label = QLabel("🧾 فاتورة مبيعات محسنة")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 20px;
                font-weight: bold;
                padding: 15px;
            }
        """)

        # معلومات الحالة
        mode_text = "تعديل فاتورة" if self.is_edit_mode else "فاتورة جديدة"
        self.mode_label = QLabel(mode_text)
        self.mode_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                padding: 15px;
                background-color: rgba(255,255,255,0.2);
                border-radius: 5px;
            }
        """)

        layout.addWidget(title_label)
        layout.addStretch()
        layout.addWidget(self.mode_label)

        frame.setLayout(layout)
        return frame

    def create_invoice_info_widget(self):
        """إنشاء ودجت معلومات الفاتورة"""
        widget = QWidget()
        layout = QVBoxLayout()

        # مجموعة معلومات الفاتورة الأساسية
        basic_info_group = QGroupBox("📋 معلومات الفاتورة الأساسية")
        basic_info_layout = QFormLayout()

        # رقم الفاتورة
        self.invoice_number_edit = QLineEdit()
        self.invoice_number_edit.setPlaceholderText("سيتم توليده تلقائياً")
        self.invoice_number_edit.setReadOnly(True)
        basic_info_layout.addRow("رقم الفاتورة:", self.invoice_number_edit)

        # تاريخ الفاتورة
        self.invoice_date_edit = QDateEdit()
        self.invoice_date_edit.setDate(QDate.currentDate())
        self.invoice_date_edit.setCalendarPopup(True)
        basic_info_layout.addRow("تاريخ الفاتورة:", self.invoice_date_edit)

        # تاريخ الاستحقاق
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setDate(QDate.currentDate().addDays(30))
        self.due_date_edit.setCalendarPopup(True)
        basic_info_layout.addRow("تاريخ الاستحقاق:", self.due_date_edit)

        # حالة الفاتورة
        self.status_combo = QComboBox()
        for status, info in InvoiceStatusManager.STATUSES.items():
            self.status_combo.addItem(f"{info['icon']} {info['name']}", status)
        basic_info_layout.addRow("حالة الفاتورة:", self.status_combo)

        basic_info_group.setLayout(basic_info_layout)
        layout.addWidget(basic_info_group)

        # مجموعة معلومات العميل
        customer_group = QGroupBox("👤 معلومات العميل")
        customer_layout = QFormLayout()

        # اختيار العميل
        self.customer_combo = QComboBox()
        self.customer_combo.setEditable(True)
        self.customer_combo.setCurrentText("اختر أو ابحث عن عميل...")
        customer_layout.addRow("العميل:", self.customer_combo)

        # زر إضافة عميل جديد
        add_customer_btn = QPushButton("➕ عميل جديد")
        add_customer_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 5px 10px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        customer_layout.addRow("", add_customer_btn)

        # معلومات العميل المختار
        self.customer_info_text = QTextEdit()
        self.customer_info_text.setMaximumHeight(100)
        self.customer_info_text.setReadOnly(True)
        self.customer_info_text.setPlainText("ستظهر معلومات العميل هنا...")
        customer_layout.addRow("معلومات العميل:", self.customer_info_text)

        customer_group.setLayout(customer_layout)
        layout.addWidget(customer_group)

        # مجموعة إعدادات الدفع
        payment_group = QGroupBox("💳 إعدادات الدفع والضرائب")
        payment_layout = QFormLayout()

        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقدي", "آجل", "بنكي", "شيك", "بطاقة ائتمان"])
        payment_layout.addRow("طريقة الدفع:", self.payment_method_combo)

        # العملة
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["ريال يمني", "ريال سعودي", "دولار أمريكي", "يورو"])
        payment_layout.addRow("العملة:", self.currency_combo)

        # معدل الضريبة
        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setRange(0, 100)
        self.tax_rate_spin.setValue(0)
        self.tax_rate_spin.setSuffix("%")
        payment_layout.addRow("معدل الضريبة:", self.tax_rate_spin)

        # خصم عام
        self.global_discount_spin = QDoubleSpinBox()
        self.global_discount_spin.setRange(0, 100)
        self.global_discount_spin.setValue(0)
        self.global_discount_spin.setSuffix("%")
        payment_layout.addRow("خصم عام:", self.global_discount_spin)

        payment_group.setLayout(payment_layout)
        layout.addWidget(payment_group)

        # مجموعة الملاحظات
        notes_group = QGroupBox("📝 ملاحظات")
        notes_layout = QVBoxLayout()

        self.notes_text = QTextEdit()
        self.notes_text.setMaximumHeight(80)
        self.notes_text.setPlainText("أدخل أي ملاحظات إضافية...")
        notes_layout.addWidget(self.notes_text)

        notes_group.setLayout(notes_layout)
        layout.addWidget(notes_group)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_items_widget(self):
        """إنشاء ودجت الأصناف"""
        widget = QWidget()
        layout = QVBoxLayout()

        # شريط أدوات الأصناف
        items_toolbar = QHBoxLayout()

        add_item_btn = QPushButton("➕ إضافة صنف")
        add_item_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)

        remove_item_btn = QPushButton("➖ حذف صنف")
        remove_item_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)

        clear_items_btn = QPushButton("🗑️ مسح الكل")
        clear_items_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)

        items_toolbar.addWidget(add_item_btn)
        items_toolbar.addWidget(remove_item_btn)
        items_toolbar.addWidget(clear_items_btn)
        items_toolbar.addStretch()

        layout.addLayout(items_toolbar)

        # جدول الأصناف
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(8)
        self.items_table.setHorizontalHeaderLabels([
            "م", "اسم الصنف", "الوحدة", "الكمية", "السعر", "خصم%", "ضريبة%", "الإجمالي"
        ])

        # تنسيق الجدول
        header = self.items_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم الصنف

        self.items_table.setAlternatingRowColors(True)
        self.items_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.items_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QHeaderView::section {
                background-color: #343a40;
                color: white;
                padding: 10px;
                font-weight: bold;
                border: none;
            }
        """)

        layout.addWidget(self.items_table)

        # مجموعة الإجماليات
        totals_group = QGroupBox("💰 الإجماليات")
        totals_layout = QGridLayout()

        # المجموع الفرعي
        totals_layout.addWidget(QLabel("المجموع الفرعي:"), 0, 0)
        self.subtotal_label = QLabel("0.00 ريال")
        self.subtotal_label.setStyleSheet("font-weight: bold; color: #495057;")
        totals_layout.addWidget(self.subtotal_label, 0, 1)

        # إجمالي الخصم
        totals_layout.addWidget(QLabel("إجمالي الخصم:"), 1, 0)
        self.discount_label = QLabel("0.00 ريال")
        self.discount_label.setStyleSheet("font-weight: bold; color: #dc3545;")
        totals_layout.addWidget(self.discount_label, 1, 1)

        # إجمالي الضريبة
        totals_layout.addWidget(QLabel("إجمالي الضريبة:"), 2, 0)
        self.tax_label = QLabel("0.00 ريال")
        self.tax_label.setStyleSheet("font-weight: bold; color: #ffc107;")
        totals_layout.addWidget(self.tax_label, 2, 1)

        # الإجمالي النهائي
        totals_layout.addWidget(QLabel("الإجمالي النهائي:"), 3, 0)
        self.total_label = QLabel("0.00 ريال")
        self.total_label.setStyleSheet("""
            font-weight: bold;
            font-size: 16px;
            color: #28a745;
            padding: 5px;
            background-color: #d4edda;
            border-radius: 3px;
        """)
        totals_layout.addWidget(self.total_label, 3, 1)

        totals_group.setLayout(totals_layout)
        layout.addWidget(totals_group)

        widget.setLayout(layout)
        return widget

    def create_buttons_layout(self):
        """إنشاء تخطيط الأزرار"""
        layout = QHBoxLayout()

        # زر الحفظ
        save_btn = QPushButton("💾 حفظ الفاتورة")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        save_btn.clicked.connect(self.save_invoice)

        # زر المعاينة
        preview_btn = QPushButton("👁️ معاينة")
        preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        preview_btn.clicked.connect(self.preview_invoice)

        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        print_btn.clicked.connect(self.print_invoice)

        # زر التصدير
        export_btn = QPushButton("📤 تصدير")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e8650e;
            }
        """)
        export_btn.clicked.connect(self.export_invoice)

        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        layout.addWidget(save_btn)
        layout.addWidget(preview_btn)
        layout.addWidget(print_btn)
        layout.addWidget(export_btn)
        layout.addStretch()
        layout.addWidget(cancel_btn)

        return layout

    def setup_connections(self):
        """إعداد الاتصالات"""
        # اتصالات العميل
        self.customer_combo.currentTextChanged.connect(self.on_customer_changed)

        # اتصالات الحسابات
        self.tax_rate_spin.valueChanged.connect(self.calculate_totals)
        self.global_discount_spin.valueChanged.connect(self.calculate_totals)

        # اتصالات الجدول
        self.items_table.cellChanged.connect(self.on_item_changed)

    def load_data(self):
        """تحميل البيانات"""
        self.load_customers()
        self.load_products()

        # توليد رقم فاتورة جديد إذا لم يكن في وضع التعديل
        if not self.is_edit_mode:
            invoice_number = InvoiceNumberGenerator.generate_invoice_number()
            self.invoice_number_edit.setText(invoice_number)

    def load_customers(self):
        """تحميل العملاء"""
        try:
            if self.db_manager:
                query = "SELECT id, customer_name, phone, email, address FROM customers ORDER BY customer_name"
                customers = self.db_manager.execute_query(query)

                self.customer_combo.clear()
                self.customer_combo.addItem("اختر عميل...", None)

                for customer in customers:
                    display_text = f"{customer['customer_name']} - {customer.get('phone', '')}"
                    self.customer_combo.addItem(display_text, customer)

                self.customers_data = customers
        except Exception as e:
            print(f"خطأ في تحميل العملاء: {e}")

    def load_products(self):
        """تحميل المنتجات"""
        try:
            if self.db_manager:
                query = "SELECT id, product_name, unit, selling_price FROM products ORDER BY product_name"
                products = self.db_manager.execute_query(query)
                self.products_data = products
        except Exception as e:
            print(f"خطأ في تحميل المنتجات: {e}")

    def on_customer_changed(self):
        """عند تغيير العميل"""
        customer_data = self.customer_combo.currentData()
        if customer_data:
            info_text = f"""
الاسم: {customer_data.get('customer_name', '')}
الهاتف: {customer_data.get('phone', '')}
البريد: {customer_data.get('email', '')}
العنوان: {customer_data.get('address', '')}
            """.strip()
            self.customer_info_text.setPlainText(info_text)
        else:
            self.customer_info_text.clear()

    def add_item_row(self, item_data=None):
        """إضافة صف جديد للأصناف"""
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)

        # رقم متسلسل
        self.items_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))

        # اسم الصنف (قائمة منسدلة)
        product_combo = QComboBox()
        product_combo.addItem("اختر صنف...", None)
        for product in self.products_data:
            product_combo.addItem(product['product_name'], product)

        if item_data:
            # تحديد الصنف المحدد
            for i in range(product_combo.count()):
                if product_combo.itemData(i) and product_combo.itemData(i)['id'] == item_data.get('product_id'):
                    product_combo.setCurrentIndex(i)
                    break

        product_combo.currentTextChanged.connect(lambda: self.on_product_changed(row))
        self.items_table.setCellWidget(row, 1, product_combo)

        # باقي الحقول
        fields = ['unit', 'quantity', 'unit_price', 'discount_percentage', 'tax_rate', 'total']
        default_values = ['', '1', '0', '0', '0', '0']

        if item_data:
            values = [
                item_data.get('unit', ''),
                str(item_data.get('quantity', 1)),
                str(item_data.get('unit_price', 0)),
                str(item_data.get('discount_percentage', 0)),
                str(item_data.get('tax_rate', 0)),
                str(item_data.get('total', 0))
            ]
        else:
            values = default_values

        for i, (field, value) in enumerate(zip(fields, values), 2):
            item = QTableWidgetItem(value)
            if field == 'total':
                item.setFlags(item.flags() & ~Qt.ItemIsEditable)  # للقراءة فقط
                item.setBackground(QColor("#f8f9fa"))
            self.items_table.setItem(row, i, item)

    def on_product_changed(self, row):
        """عند تغيير المنتج"""
        product_combo = self.items_table.cellWidget(row, 1)
        product_data = product_combo.currentData()

        if product_data:
            # تحديث الوحدة والسعر
            self.items_table.setItem(row, 2, QTableWidgetItem(product_data.get('unit', '')))
            self.items_table.setItem(row, 4, QTableWidgetItem(str(product_data.get('selling_price', 0))))

            # إعادة حساب الإجمالي
            self.calculate_line_total(row)

    def on_item_changed(self, row, column):
        """عند تغيير بيانات الصنف"""
        if column in [3, 4, 5, 6]:  # الكمية، السعر، الخصم، الضريبة
            self.calculate_line_total(row)

    def calculate_line_total(self, row):
        """حساب إجمالي السطر"""
        try:
            quantity = float(self.items_table.item(row, 3).text() or 0)
            unit_price = float(self.items_table.item(row, 4).text() or 0)
            discount_percentage = float(self.items_table.item(row, 5).text() or 0)
            tax_rate = float(self.items_table.item(row, 6).text() or 0)

            calc = InvoiceCalculationEngine.calculate_line_total(
                quantity, unit_price, discount_percentage, tax_rate
            )

            # تحديث الإجمالي
            total_item = QTableWidgetItem(f"{calc['total']:.2f}")
            total_item.setFlags(total_item.flags() & ~Qt.ItemIsEditable)
            total_item.setBackground(QColor("#f8f9fa"))
            self.items_table.setItem(row, 7, total_item)

            # إعادة حساب إجماليات الفاتورة
            self.calculate_totals()

        except (ValueError, AttributeError):
            pass

    def calculate_totals(self):
        """حساب إجماليات الفاتورة"""
        items = []

        for row in range(self.items_table.rowCount()):
            try:
                quantity = float(self.items_table.item(row, 3).text() or 0)
                unit_price = float(self.items_table.item(row, 4).text() or 0)
                discount_percentage = float(self.items_table.item(row, 5).text() or 0)
                tax_rate = float(self.items_table.item(row, 6).text() or 0)

                if quantity > 0 and unit_price > 0:
                    items.append({
                        'quantity': quantity,
                        'unit_price': unit_price,
                        'discount_percentage': discount_percentage,
                        'tax_rate': tax_rate
                    })
            except (ValueError, AttributeError):
                continue

        if items:
            global_discount = self.global_discount_spin.value()
            global_tax = self.tax_rate_spin.value()

            totals = InvoiceCalculationEngine.calculate_invoice_totals(
                items, global_discount, global_tax
            )

            # تحديث التسميات
            self.subtotal_label.setText(f"{totals['subtotal']:,.2f} ريال")
            self.discount_label.setText(f"{totals['total_discount']:,.2f} ريال")
            self.tax_label.setText(f"{totals['total_tax']:,.2f} ريال")
            self.total_label.setText(f"{totals['total_amount']:,.2f} ريال")
        else:
            # إعادة تعيين القيم
            self.subtotal_label.setText("0.00 ريال")
            self.discount_label.setText("0.00 ريال")
            self.tax_label.setText("0.00 ريال")
            self.total_label.setText("0.00 ريال")

    def collect_invoice_data(self):
        """جمع بيانات الفاتورة"""
        # البيانات الأساسية
        invoice_data = {
            'invoice_number': self.invoice_number_edit.text(),
            'customer_id': self.customer_combo.currentData()['id'] if self.customer_combo.currentData() else None,
            'customer_name': self.customer_combo.currentData()['customer_name'] if self.customer_combo.currentData() else '',
            'invoice_date': self.invoice_date_edit.date().toString('yyyy-MM-dd'),
            'due_date': self.due_date_edit.date().toString('yyyy-MM-dd'),
            'status': self.status_combo.currentData(),
            'payment_method': self.payment_method_combo.currentText(),
            'currency': self.currency_combo.currentText(),
            'tax_rate': self.tax_rate_spin.value(),
            'global_discount': self.global_discount_spin.value(),
            'notes': self.notes_text.toPlainText(),
            'items': []
        }

        # جمع الأصناف
        for row in range(self.items_table.rowCount()):
            try:
                product_combo = self.items_table.cellWidget(row, 1)
                product_data = product_combo.currentData()

                if product_data:
                    item = {
                        'product_id': product_data['id'],
                        'product_name': product_data['product_name'],
                        'unit': self.items_table.item(row, 2).text(),
                        'quantity': float(self.items_table.item(row, 3).text() or 0),
                        'unit_price': float(self.items_table.item(row, 4).text() or 0),
                        'discount_percentage': float(self.items_table.item(row, 5).text() or 0),
                        'tax_rate': float(self.items_table.item(row, 6).text() or 0),
                        'total': float(self.items_table.item(row, 7).text() or 0)
                    }

                    if item['quantity'] > 0 and item['unit_price'] > 0:
                        invoice_data['items'].append(item)
            except (ValueError, AttributeError):
                continue

        # حساب الإجماليات النهائية
        if invoice_data['items']:
            totals = InvoiceCalculationEngine.calculate_invoice_totals(
                invoice_data['items'],
                invoice_data['global_discount'],
                invoice_data['tax_rate']
            )
            invoice_data.update(totals)

        return invoice_data

    def validate_invoice(self):
        """التحقق من صحة الفاتورة"""
        invoice_data = self.collect_invoice_data()
        validation = InvoiceValidationEngine.validate_invoice_data(invoice_data)

        if not validation['is_valid']:
            error_message = "يرجى تصحيح الأخطاء التالية:\n\n"
            error_message += "\n".join(f"• {error}" for error in validation['errors'])

            if validation['warnings']:
                error_message += "\n\nتحذيرات:\n"
                error_message += "\n".join(f"• {warning}" for warning in validation['warnings'])

            QMessageBox.warning(self, "أخطاء في الفاتورة", error_message)
            return False

        return True

    def save_invoice(self):
        """حفظ الفاتورة"""
        if not self.validate_invoice():
            return

        try:
            invoice_data = self.collect_invoice_data()

            # حفظ الفاتورة في قاعدة البيانات
            if self.current_invoice_id:
                # تحديث فاتورة موجودة
                invoice_id = self.update_invoice_in_database(invoice_data)
            else:
                # إنشاء فاتورة جديدة
                invoice_id = self.save_invoice_to_database(invoice_data)

            if invoice_id:
                self.current_invoice_id = invoice_id
                QMessageBox.information(self, "نجح الحفظ",
                    f"تم حفظ الفاتورة رقم {invoice_data['invoice_number']} بنجاح!\n"
                    f"إجمالي الفاتورة: {invoice_data.get('total_amount', 0):,.2f} ريال")

                self.invoice_saved.emit(invoice_data)
                self.accept()
            else:
                QMessageBox.critical(self, "خطأ في الحفظ", "فشل في حفظ الفاتورة في قاعدة البيانات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"فشل في حفظ الفاتورة:\n{str(e)}")

    def save_invoice_to_database(self, invoice_data):
        """حفظ فاتورة جديدة في قاعدة البيانات"""
        try:
            # بدء معاملة قاعدة البيانات
            self.db_manager.connection.execute("BEGIN TRANSACTION")

            # إدراج الفاتورة الرئيسية
            invoice_query = """
                INSERT INTO sales_invoices (
                    invoice_number, customer_id, invoice_date, due_date,
                    subtotal, discount_amount, discount_percentage, tax_amount, total_amount,
                    paid_amount, remaining_amount, status, payment_method, notes, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            invoice_params = (
                invoice_data['invoice_number'],
                invoice_data['customer_id'],
                invoice_data['invoice_date'],
                invoice_data['due_date'],
                invoice_data.get('subtotal', 0),
                invoice_data.get('total_discount', 0),
                invoice_data.get('global_discount', 0),
                invoice_data.get('total_tax', 0),
                invoice_data.get('total_amount', 0),
                0,  # paid_amount
                invoice_data.get('total_amount', 0),  # remaining_amount
                invoice_data.get('status', 'draft'),
                invoice_data.get('payment_method', ''),
                invoice_data.get('notes', ''),
                1  # created_by (يجب ربطه بالمستخدم الحالي)
            )

            result = self.db_manager.execute_insert(invoice_query, invoice_params)
            if not result:
                raise Exception("فشل في إدراج الفاتورة الرئيسية")

            # الحصول على ID الفاتورة الجديدة
            invoice_id = self.db_manager.cursor.lastrowid

            # إدراج تفاصيل الفاتورة
            for item in invoice_data.get('items', []):
                item_query = """
                    INSERT INTO sales_invoice_items (
                        invoice_id, product_id, quantity, unit_price,
                        discount_amount, tax_rate, tax_amount, line_total, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                # حساب تفاصيل السطر
                line_calc = InvoiceCalculationEngine.calculate_line_total(
                    item['quantity'],
                    item['unit_price'],
                    item.get('discount_percentage', 0),
                    item.get('tax_rate', 0)
                )

                item_params = (
                    invoice_id,
                    item['product_id'],
                    item['quantity'],
                    item['unit_price'],
                    line_calc['discount_amount'],
                    item.get('tax_rate', 0),
                    line_calc['tax_amount'],
                    line_calc['total'],
                    item.get('notes', '')
                )

                item_result = self.db_manager.execute_insert(item_query, item_params)
                if not item_result:
                    raise Exception(f"فشل في إدراج الصنف: {item['product_name']}")

            # تأكيد المعاملة
            self.db_manager.connection.commit()
            return invoice_id

        except Exception as e:
            # إلغاء المعاملة في حالة الخطأ
            self.db_manager.connection.rollback()
            print(f"خطأ في حفظ الفاتورة: {e}")
            raise e

    def update_invoice_in_database(self, invoice_data):
        """تحديث فاتورة موجودة في قاعدة البيانات"""
        try:
            # بدء معاملة قاعدة البيانات
            self.db_manager.connection.execute("BEGIN TRANSACTION")

            # تحديث الفاتورة الرئيسية
            invoice_query = """
                UPDATE sales_invoices SET
                    customer_id = ?, invoice_date = ?, due_date = ?,
                    subtotal = ?, discount_amount = ?, discount_percentage = ?,
                    tax_amount = ?, total_amount = ?, remaining_amount = ?,
                    status = ?, payment_method = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """

            invoice_params = (
                invoice_data['customer_id'],
                invoice_data['invoice_date'],
                invoice_data['due_date'],
                invoice_data.get('subtotal', 0),
                invoice_data.get('total_discount', 0),
                invoice_data.get('global_discount', 0),
                invoice_data.get('total_tax', 0),
                invoice_data.get('total_amount', 0),
                invoice_data.get('total_amount', 0),  # remaining_amount (سيتم تعديله حسب المدفوعات)
                invoice_data.get('status', 'draft'),
                invoice_data.get('payment_method', ''),
                invoice_data.get('notes', ''),
                self.current_invoice_id
            )

            result = self.db_manager.execute_update(invoice_query, invoice_params)
            if not result:
                raise Exception("فشل في تحديث الفاتورة الرئيسية")

            # حذف التفاصيل القديمة
            delete_items_query = "DELETE FROM sales_invoice_items WHERE invoice_id = ?"
            self.db_manager.execute_update(delete_items_query, (self.current_invoice_id,))

            # إدراج التفاصيل الجديدة
            for item in invoice_data.get('items', []):
                item_query = """
                    INSERT INTO sales_invoice_items (
                        invoice_id, product_id, quantity, unit_price,
                        discount_amount, tax_rate, tax_amount, line_total, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                # حساب تفاصيل السطر
                line_calc = InvoiceCalculationEngine.calculate_line_total(
                    item['quantity'],
                    item['unit_price'],
                    item.get('discount_percentage', 0),
                    item.get('tax_rate', 0)
                )

                item_params = (
                    self.current_invoice_id,
                    item['product_id'],
                    item['quantity'],
                    item['unit_price'],
                    line_calc['discount_amount'],
                    item.get('tax_rate', 0),
                    line_calc['tax_amount'],
                    line_calc['total'],
                    item.get('notes', '')
                )

                item_result = self.db_manager.execute_insert(item_query, item_params)
                if not item_result:
                    raise Exception(f"فشل في إدراج الصنف: {item['product_name']}")

            # تأكيد المعاملة
            self.db_manager.connection.commit()
            return self.current_invoice_id

        except Exception as e:
            # إلغاء المعاملة في حالة الخطأ
            self.db_manager.connection.rollback()
            print(f"خطأ في تحديث الفاتورة: {e}")
            raise e

    def load_invoice_from_database(self, invoice_id):
        """تحميل فاتورة من قاعدة البيانات"""
        try:
            # تحميل بيانات الفاتورة الرئيسية
            invoice_query = """
                SELECT si.*, c.customer_name, c.customer_code
                FROM sales_invoices si
                LEFT JOIN customers_suppliers c ON si.customer_id = c.id
                WHERE si.id = ?
            """

            invoice_result = self.db_manager.execute_query(invoice_query, (invoice_id,))
            if not invoice_result:
                raise Exception("لم يتم العثور على الفاتورة")

            invoice_data = invoice_result[0]

            # تحميل تفاصيل الفاتورة
            items_query = """
                SELECT sii.*, p.product_name, p.unit
                FROM sales_invoice_items sii
                LEFT JOIN products p ON sii.product_id = p.id
                WHERE sii.invoice_id = ?
                ORDER BY sii.id
            """

            items_result = self.db_manager.execute_query(items_query, (invoice_id,))

            # تعبئة البيانات في الواجهة
            self.current_invoice_id = invoice_id
            self.invoice_number_edit.setText(invoice_data['invoice_number'])
            self.invoice_date_edit.setDate(QDate.fromString(invoice_data['invoice_date'], 'yyyy-MM-dd'))
            self.due_date_edit.setDate(QDate.fromString(invoice_data['due_date'], 'yyyy-MM-dd'))

            # تعيين العميل
            for i in range(self.customer_combo.count()):
                customer_data = self.customer_combo.itemData(i)
                if customer_data and customer_data['id'] == invoice_data['customer_id']:
                    self.customer_combo.setCurrentIndex(i)
                    break

            # تعيين الحالة
            for i in range(self.status_combo.count()):
                if self.status_combo.itemData(i) == invoice_data['status']:
                    self.status_combo.setCurrentIndex(i)
                    break

            # تعيين طريقة الدفع
            payment_method_index = self.payment_method_combo.findText(invoice_data['payment_method'])
            if payment_method_index >= 0:
                self.payment_method_combo.setCurrentIndex(payment_method_index)

            # تعبئة الملاحظات
            self.notes_text.setPlainText(invoice_data['notes'] or '')

            # تعبئة الأصناف
            self.items_table.setRowCount(0)
            for item in items_result:
                self.add_item_row()
                row = self.items_table.rowCount() - 1

                # تعيين المنتج
                product_combo = self.items_table.cellWidget(row, 1)
                for i in range(product_combo.count()):
                    product_data = product_combo.itemData(i)
                    if product_data and product_data['id'] == item['product_id']:
                        product_combo.setCurrentIndex(i)
                        break

                # تعبئة باقي البيانات
                self.items_table.setItem(row, 2, QTableWidgetItem(item['unit'] or ''))
                self.items_table.setItem(row, 3, QTableWidgetItem(str(item['quantity'])))
                self.items_table.setItem(row, 4, QTableWidgetItem(str(item['unit_price'])))

                # حساب نسبة الخصم من المبلغ
                if item['unit_price'] > 0:
                    discount_percentage = (item['discount_amount'] / (item['quantity'] * item['unit_price'])) * 100
                    self.items_table.setItem(row, 5, QTableWidgetItem(f"{discount_percentage:.2f}"))
                else:
                    self.items_table.setItem(row, 5, QTableWidgetItem("0"))

                self.items_table.setItem(row, 6, QTableWidgetItem(str(item['tax_rate'])))
                self.items_table.setItem(row, 7, QTableWidgetItem(str(item['line_total'])))

            # إعادة حساب الإجماليات
            self.calculate_totals()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في التحميل", f"فشل في تحميل الفاتورة:\n{str(e)}")

    def generate_invoice_number(self):
        """توليد رقم فاتورة جديد"""
        try:
            # الحصول على آخر رقم فاتورة
            query = """
                SELECT invoice_number FROM sales_invoices
                WHERE invoice_number LIKE 'INV-%'
                ORDER BY id DESC LIMIT 1
            """

            result = self.db_manager.execute_query(query)

            if result:
                last_number = result[0]['invoice_number']
                # استخراج الرقم من النمط INV-YYYY-NNNN
                parts = last_number.split('-')
                if len(parts) >= 3:
                    try:
                        last_seq = int(parts[-1])
                        new_seq = last_seq + 1
                        current_year = datetime.now().year
                        new_number = f"INV-{current_year}-{new_seq:04d}"
                    except ValueError:
                        # في حالة فشل التحويل، ابدأ من جديد
                        current_year = datetime.now().year
                        new_number = f"INV-{current_year}-0001"
                else:
                    current_year = datetime.now().year
                    new_number = f"INV-{current_year}-0001"
            else:
                # أول فاتورة
                current_year = datetime.now().year
                new_number = f"INV-{current_year}-0001"

            return new_number

        except Exception as e:
            print(f"خطأ في توليد رقم الفاتورة: {e}")
            # رقم افتراضي في حالة الخطأ
            current_year = datetime.now().year
            import random
            random_num = random.randint(1000, 9999)
            return f"INV-{current_year}-{random_num}"

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        # يتم إعداد قاعدة البيانات تلقائياً في SQLiteManager
        pass

    def load_settings(self):
        """تحميل الإعدادات"""
        # تحميل الإعدادات من قاعدة البيانات أو ملف الإعدادات
        pass

    def setup_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        # إعداد اختصارات مثل Ctrl+S للحفظ، F9 للبحث، إلخ
        pass

    def start_auto_save(self):
        """بدء الحفظ التلقائي"""
        # بدء مؤقت الحفظ التلقائي كل 5 دقائق
        self.auto_save_timer.start(300000)  # 5 دقائق

    def auto_save_draft(self):
        """حفظ تلقائي للمسودة"""
        try:
            if self.invoice_number_edit.text():
                # حفظ المسودة في قاعدة البيانات
                pass
        except Exception as e:
            print(f"خطأ في الحفظ التلقائي: {e}")

    def load_invoice_data(self, invoice_data):
        """تحميل بيانات الفاتورة من قاموس"""
        try:
            if isinstance(invoice_data, dict):
                # تعبئة البيانات الأساسية
                self.invoice_number_edit.setText(invoice_data.get('invoice_number', ''))

                if 'invoice_date' in invoice_data:
                    date = QDate.fromString(invoice_data['invoice_date'], 'yyyy-MM-dd')
                    self.invoice_date_edit.setDate(date)

                if 'due_date' in invoice_data:
                    date = QDate.fromString(invoice_data['due_date'], 'yyyy-MM-dd')
                    self.due_date_edit.setDate(date)

                # تعبئة باقي البيانات...
                self.notes_text.setPlainText(invoice_data.get('notes', ''))

        except Exception as e:
            print(f"خطأ في تحميل بيانات الفاتورة: {e}")


class EnhancedInvoiceDialog(QDialog):
    """حوار الفاتورة المحسن"""

    invoice_saved = pyqtSignal(dict)

    def __init__(self, db_manager, invoice_data=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.invoice_data = invoice_data
        self.is_edit_mode = invoice_data is not None

        self.setup_ui()
        self.setup_connections()

        if self.is_edit_mode:
            self.load_invoice_data()
        else:
            self.generate_new_invoice_number()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل الفاتورة" if self.is_edit_mode else "فاتورة جديدة"
        self.setWindowTitle(title)
        self.setModal(True)
        self.resize(1200, 800)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # إنشاء نظام الفواتير المحسن
        self.invoice_system = EnhancedInvoiceSystem(self.db_manager, self.invoice_data, self)
        main_layout.addWidget(self.invoice_system)

        # أزرار الحوار
        buttons_layout = QHBoxLayout()

        self.save_button = QPushButton("💾 حفظ")
        self.save_button.clicked.connect(self.save_invoice)
        buttons_layout.addWidget(self.save_button)

        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)

        buttons_layout.addStretch()

        self.print_button = QPushButton("🖨️ طباعة")
        self.print_button.clicked.connect(self.print_invoice)
        buttons_layout.addWidget(self.print_button)

        main_layout.addLayout(buttons_layout)
        self.setLayout(main_layout)

    def setup_connections(self):
        """إعداد الاتصالات"""
        pass

    def load_invoice_data(self):
        """تحميل بيانات الفاتورة"""
        if self.invoice_data and isinstance(self.invoice_data, dict):
            if 'id' in self.invoice_data:
                self.invoice_system.load_invoice_from_database(self.invoice_data['id'])

    def generate_new_invoice_number(self):
        """توليد رقم فاتورة جديد"""
        new_number = self.invoice_system.generate_invoice_number()
        self.invoice_system.invoice_number_edit.setText(new_number)

    def save_invoice(self):
        """حفظ الفاتورة"""
        self.invoice_system.save_invoice()

    def print_invoice(self):
        """طباعة الفاتورة"""
        self.invoice_system.print_invoice()

    def preview_invoice(self):
        """معاينة الفاتورة"""
        if not self.validate_invoice():
            return

        try:
            invoice_data = self.collect_invoice_data()
            html_content = InvoiceTemplateEngine.generate_html_invoice(invoice_data)

            # إنشاء نافذة معاينة
            preview_dialog = InvoicePreviewDialog(html_content, self)
            preview_dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في المعاينة", f"فشل في إنشاء معاينة الفاتورة:\n{str(e)}")

    def print_invoice(self):
        """طباعة الفاتورة"""
        if not self.validate_invoice():
            return

        try:
            invoice_data = self.collect_invoice_data()
            html_content = InvoiceTemplateEngine.generate_html_invoice(invoice_data)

            # إنشاء حوار الطباعة
            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QDialog.Accepted:
                # هنا يتم تنفيذ الطباعة
                QMessageBox.information(self, "طباعة", "تم إرسال الفاتورة للطباعة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الطباعة", f"فشل في طباعة الفاتورة:\n{str(e)}")

    def export_invoice(self):
        """تصدير الفاتورة"""
        if not self.validate_invoice():
            return

        try:
            invoice_data = self.collect_invoice_data()

            # خيارات التصدير
            export_options = [
                "HTML",
                "PDF",
                "Excel",
                "JSON"
            ]

            option, ok = QMessageBox.question(
                self, "تصدير الفاتورة",
                "اختر صيغة التصدير:",
                "|".join(export_options)
            )

            if ok:
                # هنا يتم تنفيذ التصدير
                QMessageBox.information(self, "تصدير", f"تم تصدير الفاتورة بصيغة {option}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في التصدير", f"فشل في تصدير الفاتورة:\n{str(e)}")

class InvoicePreviewDialog(QDialog):
    """حوار معاينة الفاتورة"""

    def __init__(self, html_content, parent=None):
        super().__init__(parent)
        self.html_content = html_content
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("معاينة الفاتورة")
        self.setGeometry(150, 150, 900, 700)
        self.setLayoutDirection(Qt.RightToLeft)

        layout = QVBoxLayout()

        # شريط الأدوات
        toolbar = QHBoxLayout()

        print_btn = QPushButton("🖨️ طباعة")
        save_btn = QPushButton("💾 حفظ HTML")
        close_btn = QPushButton("❌ إغلاق")

        toolbar.addWidget(print_btn)
        toolbar.addWidget(save_btn)
        toolbar.addStretch()
        toolbar.addWidget(close_btn)

        layout.addLayout(toolbar)

        # منطقة المعاينة
        from PyQt5.QtWebEngineWidgets import QWebEngineView
        self.web_view = QWebEngineView()
        self.web_view.setHtml(self.html_content)
        layout.addWidget(self.web_view)

        # الاتصالات
        print_btn.clicked.connect(self.print_preview)
        save_btn.clicked.connect(self.save_html)
        close_btn.clicked.connect(self.accept)

        self.setLayout(layout)

    def print_preview(self):
        """طباعة المعاينة"""
        QMessageBox.information(self, "طباعة", "سيتم تطوير وظيفة الطباعة")

    def save_html(self):
        """حفظ HTML"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ الفاتورة",
                f"invoice_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html",
                "HTML Files (*.html);;All Files (*)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.html_content)

                QMessageBox.information(self, "تم الحفظ", f"تم حفظ الفاتورة في:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الملف:\n{str(e)}")


def main():
    """اختبار النظام"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    dialog = EnhancedInvoiceDialog()
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
