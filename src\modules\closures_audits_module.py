#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة الإغلاقات والتوثيقات
Closures and Audits Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QDateEdit, QCheckBox,
                             QProgressBar, QSplitter, QFrame, QDialogButtonBox)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from datetime import datetime, timedelta

try:
    from ..database.sqlite_manager import SQLiteManager
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class ClosureDialog(QDialog):
    """حوار إغلاق فترة محاسبية"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔒 إغلاق فترة محاسبية")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🔒 إغلاق فترة محاسبية")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: white;
                background-color: #e74c3c;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # معلومات الإغلاق
        info_group = QGroupBox("معلومات الإغلاق")
        info_layout = QFormLayout()
        
        self.closure_type_combo = QComboBox()
        self.closure_type_combo.addItems([
            "إغلاق شهري",
            "إغلاق ربع سنوي", 
            "إغلاق نصف سنوي",
            "إغلاق سنوي"
        ])
        info_layout.addRow("نوع الإغلاق:", self.closure_type_combo)
        
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.start_date_edit.setCalendarPopup(True)
        info_layout.addRow("تاريخ البداية:", self.start_date_edit)
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        info_layout.addRow("تاريخ النهاية:", self.end_date_edit)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات الإغلاق...")
        info_layout.addRow("الملاحظات:", self.notes_edit)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # خيارات الإغلاق
        options_group = QGroupBox("خيارات الإغلاق")
        options_layout = QVBoxLayout()
        
        self.backup_check = QCheckBox("إنشاء نسخة احتياطية قبل الإغلاق")
        self.backup_check.setChecked(True)
        options_layout.addWidget(self.backup_check)
        
        self.validate_check = QCheckBox("التحقق من صحة البيانات")
        self.validate_check.setChecked(True)
        options_layout.addWidget(self.validate_check)
        
        self.lock_period_check = QCheckBox("منع التعديل على الفترة المغلقة")
        self.lock_period_check.setChecked(True)
        options_layout.addWidget(self.lock_period_check)
        
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.close_btn = button_box.addButton("🔒 إغلاق", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        self.close_btn.clicked.connect(self.perform_closure)
        self.cancel_btn.clicked.connect(self.reject)
        
        layout.addWidget(button_box)
        self.setLayout(layout)
    
    def perform_closure(self):
        """تنفيذ عملية الإغلاق"""
        try:
            # التحقق من التواريخ
            start_date = self.start_date_edit.date().toPyDate()
            end_date = self.end_date_edit.date().toPyDate()
            
            if start_date >= end_date:
                QMessageBox.warning(self, "تحذير", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
                return
            
            # تأكيد الإغلاق
            reply = QMessageBox.question(
                self, "تأكيد الإغلاق",
                f"هل أنت متأكد من إغلاق الفترة من {start_date} إلى {end_date}؟\n\n"
                "تحذير: لن تتمكن من تعديل البيانات في هذه الفترة بعد الإغلاق!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # عرض شريط التقدم
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # تنفيذ خطوات الإغلاق
            steps = [
                ("إنشاء نسخة احتياطية", self.create_backup),
                ("التحقق من صحة البيانات", self.validate_data),
                ("حفظ معلومات الإغلاق", self.save_closure_info),
                ("تطبيق قفل الفترة", self.apply_period_lock)
            ]
            
            for i, (step_name, step_func) in enumerate(steps):
                self.progress_bar.setValue(int((i / len(steps)) * 100))
                
                if not step_func():
                    QMessageBox.critical(self, "خطأ", f"فشل في خطوة: {step_name}")
                    self.progress_bar.setVisible(False)
                    return
            
            self.progress_bar.setValue(100)
            QMessageBox.information(self, "نجح", "تم إغلاق الفترة المحاسبية بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في عملية الإغلاق:\n{str(e)}")
            self.progress_bar.setVisible(False)
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        if not self.backup_check.isChecked():
            return True
        
        try:
            import shutil
            import os
            from datetime import datetime
            
            # إنشاء مجلد النسخ الاحتياطية
            backup_dir = "backups/closures"
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            # اسم النسخة الاحتياطية
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"closure_backup_{timestamp}.db"
            backup_path = os.path.join(backup_dir, backup_name)
            
            # نسخ قاعدة البيانات
            shutil.copy2("accounting_system.db", backup_path)
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.validate_check.isChecked():
            return True
        
        try:
            # فحص توازن القيود
            query = """
            SELECT SUM(debit_amount) - SUM(credit_amount) as balance
            FROM journal_entries 
            WHERE entry_date BETWEEN ? AND ?
            """
            
            start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
            end_date = self.end_date_edit.date().toString("yyyy-MM-dd")
            
            result = self.db_manager.execute_query(query, (start_date, end_date))
            
            if result and abs(result[0][0] or 0) > 0.01:
                QMessageBox.warning(self, "تحذير", "يوجد عدم توازن في القيود المحاسبية")
                return False
            
            return True
            
        except Exception as e:
            print(f"خطأ في التحقق من البيانات: {e}")
            return True  # متابعة في حالة الخطأ
    
    def save_closure_info(self):
        """حفظ معلومات الإغلاق"""
        try:
            # إنشاء جدول الإغلاقات إذا لم يكن موجوداً
            create_query = """
            CREATE TABLE IF NOT EXISTS period_closures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                closure_type TEXT NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                closure_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                created_by TEXT,
                is_locked BOOLEAN DEFAULT 1
            )
            """
            self.db_manager.execute_update(create_query)
            
            # إدراج معلومات الإغلاق
            insert_query = """
            INSERT INTO period_closures 
            (closure_type, start_date, end_date, notes, created_by, is_locked)
            VALUES (?, ?, ?, ?, ?, ?)
            """
            
            params = (
                self.closure_type_combo.currentText(),
                self.start_date_edit.date().toString("yyyy-MM-dd"),
                self.end_date_edit.date().toString("yyyy-MM-dd"),
                self.notes_edit.toPlainText(),
                "النظام",  # يمكن تحديث هذا بمعرف المستخدم الحالي
                self.lock_period_check.isChecked()
            )
            
            self.db_manager.execute_update(insert_query, params)
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ معلومات الإغلاق: {e}")
            return False
    
    def apply_period_lock(self):
        """تطبيق قفل الفترة"""
        if not self.lock_period_check.isChecked():
            return True
        
        try:
            # يمكن إضافة منطق قفل الفترة هنا
            # مثل إضافة علامات في الجداول أو إنشاء جدول للفترات المقفلة
            return True
            
        except Exception as e:
            print(f"خطأ في تطبيق قفل الفترة: {e}")
            return False


class AuditReportDialog(QDialog):
    """حوار تقرير التدقيق"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📊 تقرير التدقيق")
        self.setGeometry(100, 100, 800, 600)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("📊 تقرير التدقيق والمراجعة")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: white;
                background-color: #3498db;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # إعدادات التقرير
        settings_group = QGroupBox("إعدادات التقرير")
        settings_layout = QFormLayout()
        
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "تقرير شامل",
            "تدقيق القيود",
            "تدقيق المبيعات",
            "تدقيق المشتريات",
            "تدقيق المخزون",
            "تدقيق الحسابات"
        ])
        settings_layout.addRow("نوع التقرير:", self.report_type_combo)
        
        self.from_date_edit = QDateEdit()
        self.from_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.from_date_edit.setCalendarPopup(True)
        settings_layout.addRow("من تاريخ:", self.from_date_edit)
        
        self.to_date_edit = QDateEdit()
        self.to_date_edit.setDate(QDate.currentDate())
        self.to_date_edit.setCalendarPopup(True)
        settings_layout.addRow("إلى تاريخ:", self.to_date_edit)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # أزرار التقرير
        buttons_layout = QHBoxLayout()
        
        self.generate_btn = QPushButton("📊 إنتاج التقرير")
        self.generate_btn.clicked.connect(self.generate_report)
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        buttons_layout.addWidget(self.generate_btn)
        
        self.export_btn = QPushButton("📤 تصدير")
        self.export_btn.clicked.connect(self.export_report)
        buttons_layout.addWidget(self.export_btn)
        
        self.print_btn = QPushButton("🖨️ طباعة")
        self.print_btn.clicked.connect(self.print_report)
        buttons_layout.addWidget(self.print_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # منطقة التقرير
        self.report_text = QTextEdit()
        self.report_text.setFont(QFont("Courier", 10))
        layout.addWidget(self.report_text)
        
        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
        
        self.setLayout(layout)
    
    def generate_report(self):
        """إنتاج التقرير"""
        try:
            report_type = self.report_type_combo.currentText()
            from_date = self.from_date_edit.date().toString("yyyy-MM-dd")
            to_date = self.to_date_edit.date().toString("yyyy-MM-dd")
            
            report_content = []
            report_content.append("=" * 80)
            report_content.append(f"تقرير التدقيق: {report_type}")
            report_content.append(f"الفترة: من {from_date} إلى {to_date}")
            report_content.append(f"تاريخ الإنتاج: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_content.append("=" * 80)
            report_content.append("")
            
            if report_type == "تقرير شامل":
                report_content.extend(self.generate_comprehensive_report(from_date, to_date))
            elif report_type == "تدقيق القيود":
                report_content.extend(self.generate_journal_audit(from_date, to_date))
            elif report_type == "تدقيق المبيعات":
                report_content.extend(self.generate_sales_audit(from_date, to_date))
            elif report_type == "تدقيق المشتريات":
                report_content.extend(self.generate_purchases_audit(from_date, to_date))
            elif report_type == "تدقيق المخزون":
                report_content.extend(self.generate_inventory_audit(from_date, to_date))
            elif report_type == "تدقيق الحسابات":
                report_content.extend(self.generate_accounts_audit(from_date, to_date))
            
            self.report_text.setText("\n".join(report_content))
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنتاج التقرير:\n{str(e)}")
    
    def generate_comprehensive_report(self, from_date, to_date):
        """إنتاج تقرير شامل"""
        content = []
        
        try:
            # إحصائيات عامة
            content.append("📊 الإحصائيات العامة:")
            content.append("-" * 40)
            
            # عدد القيود
            query = "SELECT COUNT(*) FROM journal_entries WHERE entry_date BETWEEN ? AND ?"
            result = self.db_manager.execute_query(query, (from_date, to_date))
            entries_count = result[0][0] if result else 0
            content.append(f"عدد القيود: {entries_count}")
            
            # عدد الفواتير
            query = "SELECT COUNT(*) FROM sales_invoices WHERE invoice_date BETWEEN ? AND ?"
            result = self.db_manager.execute_query(query, (from_date, to_date))
            invoices_count = result[0][0] if result else 0
            content.append(f"عدد فواتير المبيعات: {invoices_count}")
            
            # إجمالي المبيعات
            query = "SELECT SUM(total_amount) FROM sales_invoices WHERE invoice_date BETWEEN ? AND ?"
            result = self.db_manager.execute_query(query, (from_date, to_date))
            total_sales = result[0][0] if result and result[0][0] else 0
            content.append(f"إجمالي المبيعات: {total_sales:,.2f}")
            
            content.append("")
            
        except Exception as e:
            content.append(f"خطأ في الإحصائيات العامة: {e}")
        
        return content
    
    def generate_journal_audit(self, from_date, to_date):
        """تدقيق القيود"""
        content = []
        content.append("📝 تدقيق القيود:")
        content.append("-" * 40)
        
        try:
            # فحص توازن القيود
            query = """
            SELECT entry_id, SUM(debit_amount) as total_debit, SUM(credit_amount) as total_credit
            FROM journal_entries 
            WHERE entry_date BETWEEN ? AND ?
            GROUP BY entry_id
            HAVING ABS(SUM(debit_amount) - SUM(credit_amount)) > 0.01
            """
            
            result = self.db_manager.execute_query(query, (from_date, to_date))
            
            if result:
                content.append("⚠️ قيود غير متوازنة:")
                for row in result:
                    entry_id, debit, credit = row
                    content.append(f"   قيد رقم {entry_id}: مدين={debit:.2f}, دائن={credit:.2f}")
            else:
                content.append("✅ جميع القيود متوازنة")
            
        except Exception as e:
            content.append(f"خطأ في تدقيق القيود: {e}")
        
        return content
    
    def generate_sales_audit(self, from_date, to_date):
        """تدقيق المبيعات"""
        content = []
        content.append("💰 تدقيق المبيعات:")
        content.append("-" * 40)
        
        try:
            # أكبر الفواتير
            query = """
            SELECT invoice_number, customer_name, total_amount, invoice_date
            FROM sales_invoices 
            WHERE invoice_date BETWEEN ? AND ?
            ORDER BY total_amount DESC
            LIMIT 10
            """
            
            result = self.db_manager.execute_query(query, (from_date, to_date))
            
            if result:
                content.append("🔝 أكبر 10 فواتير:")
                for row in result:
                    invoice_num, customer, amount, date = row
                    content.append(f"   {invoice_num}: {customer} - {amount:,.2f} ({date})")
            
        except Exception as e:
            content.append(f"خطأ في تدقيق المبيعات: {e}")
        
        return content
    
    def generate_purchases_audit(self, from_date, to_date):
        """تدقيق المشتريات"""
        content = []
        content.append("🛒 تدقيق المشتريات:")
        content.append("-" * 40)
        content.append("قيد التطوير...")
        return content
    
    def generate_inventory_audit(self, from_date, to_date):
        """تدقيق المخزون"""
        content = []
        content.append("📦 تدقيق المخزون:")
        content.append("-" * 40)
        content.append("قيد التطوير...")
        return content
    
    def generate_accounts_audit(self, from_date, to_date):
        """تدقيق الحسابات"""
        content = []
        content.append("🏦 تدقيق الحسابات:")
        content.append("-" * 40)
        content.append("قيد التطوير...")
        return content
    
    def export_report(self):
        """تصدير التقرير"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            
            filename, _ = QFileDialog.getSaveFileName(
                self, "تصدير التقرير", 
                f"audit_report_{datetime.now().strftime('%Y%m%d')}.txt",
                "Text Files (*.txt);;All Files (*.*)"
            )
            
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.report_text.toPlainText())
                
                QMessageBox.information(self, "تم", "تم تصدير التقرير بنجاح")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")
    
    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "قريباً", "ميزة الطباعة قيد التطوير")


class ClosuresAuditsWidget(QWidget):
    """وحدة الإغلاقات والتوثيقات الرئيسية"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_closures()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🔒 الإغلاقات والتوثيقات")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 20px;
                font-weight: bold;
                color: white;
                background-color: #8e44ad;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب الإغلاقات
        closures_tab = self.create_closures_tab()
        self.tabs.addTab(closures_tab, "🔒 الإغلاقات")
        
        # تبويب التوثيقات
        audits_tab = self.create_audits_tab()
        self.tabs.addTab(audits_tab, "📊 التوثيقات")
        
        layout.addWidget(self.tabs)
        self.setLayout(layout)
    
    def create_closures_tab(self):
        """إنشاء تبويب الإغلاقات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        new_closure_btn = QPushButton("🔒 إغلاق جديد")
        new_closure_btn.clicked.connect(self.new_closure)
        new_closure_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        buttons_layout.addWidget(new_closure_btn)
        
        reopen_btn = QPushButton("🔓 إعادة فتح")
        reopen_btn.clicked.connect(self.reopen_period)
        buttons_layout.addWidget(reopen_btn)
        
        buttons_layout.addStretch()
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.load_closures)
        buttons_layout.addWidget(refresh_btn)
        
        layout.addLayout(buttons_layout)
        
        # جدول الإغلاقات
        self.closures_table = QTableWidget()
        self.closures_table.setColumnCount(6)
        self.closures_table.setHorizontalHeaderLabels([
            "النوع", "تاريخ البداية", "تاريخ النهاية", "تاريخ الإغلاق", "الحالة", "الملاحظات"
        ])
        
        header = self.closures_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.closures_table)
        
        widget.setLayout(layout)
        return widget
    
    def create_audits_tab(self):
        """إنشاء تبويب التوثيقات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # أزرار التقارير
        buttons_layout = QHBoxLayout()
        
        audit_report_btn = QPushButton("📊 تقرير تدقيق")
        audit_report_btn.clicked.connect(self.generate_audit_report)
        audit_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        buttons_layout.addWidget(audit_report_btn)
        
        compliance_btn = QPushButton("📋 تقرير الامتثال")
        compliance_btn.clicked.connect(self.generate_compliance_report)
        buttons_layout.addWidget(compliance_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # معلومات التوثيق
        info_text = QTextEdit()
        info_text.setHtml("""
        <h3>📊 التوثيقات والتقارير</h3>
        <p><b>تقرير التدقيق:</b> يتضمن مراجعة شاملة للبيانات المالية والمحاسبية</p>
        <p><b>تقرير الامتثال:</b> يتضمن التحقق من الامتثال للمعايير المحاسبية</p>
        <p><b>تقارير الإغلاق:</b> تقارير مفصلة عن عمليات الإغلاق المحاسبي</p>
        
        <h4>🔍 عناصر التدقيق:</h4>
        <ul>
        <li>توازن القيود المحاسبية</li>
        <li>صحة البيانات المالية</li>
        <li>مطابقة الأرصدة</li>
        <li>التحقق من العمليات</li>
        <li>مراجعة الصلاحيات</li>
        </ul>
        """)
        info_text.setMaximumHeight(300)
        layout.addWidget(info_text)
        
        widget.setLayout(layout)
        return widget
    
    def load_closures(self):
        """تحميل قائمة الإغلاقات"""
        try:
            # إنشاء الجدول إذا لم يكن موجوداً
            create_query = """
            CREATE TABLE IF NOT EXISTS period_closures (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                closure_type TEXT NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                closure_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                created_by TEXT,
                is_locked BOOLEAN DEFAULT 1
            )
            """
            self.db_manager.execute_update(create_query)
            
            # تحميل البيانات
            query = """
            SELECT closure_type, start_date, end_date, closure_date, is_locked, notes
            FROM period_closures 
            ORDER BY closure_date DESC
            """
            
            closures = self.db_manager.execute_query(query)
            
            self.closures_table.setRowCount(len(closures))
            
            for row, closure in enumerate(closures):
                self.closures_table.setItem(row, 0, QTableWidgetItem(str(closure[0])))
                self.closures_table.setItem(row, 1, QTableWidgetItem(str(closure[1])))
                self.closures_table.setItem(row, 2, QTableWidgetItem(str(closure[2])))
                self.closures_table.setItem(row, 3, QTableWidgetItem(str(closure[3])))
                
                status = "مقفل" if closure[4] else "مفتوح"
                status_item = QTableWidgetItem(status)
                if closure[4]:
                    status_item.setBackground(QColor("#ffebee"))
                self.closures_table.setItem(row, 4, status_item)
                
                self.closures_table.setItem(row, 5, QTableWidgetItem(str(closure[5] or "")))
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الإغلاقات:\n{str(e)}")
    
    def new_closure(self):
        """إغلاق جديد"""
        dialog = ClosureDialog(self.db_manager, self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_closures()
    
    def reopen_period(self):
        """إعادة فتح فترة"""
        current_row = self.closures_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار إغلاق لإعادة فتحه")
            return
        
        reply = QMessageBox.question(
            self, "تأكيد إعادة الفتح",
            "هل أنت متأكد من إعادة فتح هذه الفترة؟\n\n"
            "سيتم السماح بالتعديل على البيانات في هذه الفترة مرة أخرى.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم", "تم إعادة فتح الفترة بنجاح")
            self.load_closures()
    
    def generate_audit_report(self):
        """إنتاج تقرير التدقيق"""
        dialog = AuditReportDialog(self.db_manager, self)
        dialog.exec_()
    
    def generate_compliance_report(self):
        """إنتاج تقرير الامتثال"""
        QMessageBox.information(self, "قريباً", "تقرير الامتثال قيد التطوير")

def show_closures_audits(db_manager, parent=None):
    """عرض وحدة الإغلاقات والتوثيقات"""
    widget = ClosuresAuditsWidget(db_manager, parent)
    return widget
