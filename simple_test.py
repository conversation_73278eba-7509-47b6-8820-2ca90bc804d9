#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لنظام تسجيل الدخول والخروج
Simple Test for Login/Logout System
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_login():
    """اختبار بسيط لتسجيل الدخول"""
    print("🔐 اختبار بسيط لتسجيل الدخول...")
    
    try:
        from src.ui.onyx_login_window import OnyxLoginWindow
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء نافذة تسجيل الدخول
        login_window = OnyxLoginWindow()
        login_window.show()
        
        print("✅ تم عرض نافذة تسجيل الدخول بنجاح")
        print("📝 يمكنك الآن اختبار النظام يدوياً")
        print("   - كلمة المرور الافتراضية: admin123")
        print("   - اضغط Ctrl+C لإيقاف الاختبار")
        
        # تشغيل التطبيق
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        test_simple_login()
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        sys.exit(1)