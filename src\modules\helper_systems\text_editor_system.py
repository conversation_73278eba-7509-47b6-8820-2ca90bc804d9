#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام محرر النصوص
Text Editor System
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QPushButton, QGroupBox, 
                             QTextEdit, QTabWidget, QWidget, QComboBox,
                             QApplication, QMessageBox, QMenuBar, QMenu,
                             QAction, QToolBar, QFontComboBox, QSpinBox,
                             QColorDialog, QFileDialog, QCheckBox)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QColor, QTextCharFormat, QTextCursor, QIcon

class TextEditorDialog(QDialog):
    """حوار محرر النصوص"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_file = None
        self.is_modified = False
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📝 محرر النصوص المتقدم")
        self.setGeometry(200, 200, 1000, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("📝 محرر النصوص المتقدم - محرر نصوص احترافي مع دعم العربية")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        
        # تبويبات المحرر
        self.tabs = QTabWidget()
        
        # تبويب المحرر الرئيسي
        editor_tab = self.create_editor_tab()
        self.tabs.addTab(editor_tab, "📝 المحرر")
        
        # تبويب المعاينة
        preview_tab = self.create_preview_tab()
        self.tabs.addTab(preview_tab, "👁️ المعاينة")
        
        # تبويب الإعدادات
        settings_tab = self.create_settings_tab()
        self.tabs.addTab(settings_tab, "⚙️ الإعدادات")
        
        # شريط الحالة
        status_bar = self.create_status_bar()
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(toolbar)
        main_layout.addWidget(self.tabs)
        main_layout.addWidget(status_bar)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout()
        
        # مجموعة الملف
        file_group = QGroupBox("📁 الملف")
        file_layout = QHBoxLayout()
        
        new_btn = QPushButton("📄 جديد")
        new_btn.setStyleSheet(self.get_button_style("#3498db"))
        new_btn.clicked.connect(self.new_file)
        
        open_btn = QPushButton("📂 فتح")
        open_btn.setStyleSheet(self.get_button_style("#27ae60"))
        open_btn.clicked.connect(self.open_file)
        
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet(self.get_button_style("#f39c12"))
        save_btn.clicked.connect(self.save_file)
        
        save_as_btn = QPushButton("💾 حفظ باسم")
        save_as_btn.setStyleSheet(self.get_button_style("#e67e22"))
        save_as_btn.clicked.connect(self.save_as_file)
        
        file_layout.addWidget(new_btn)
        file_layout.addWidget(open_btn)
        file_layout.addWidget(save_btn)
        file_layout.addWidget(save_as_btn)
        file_group.setLayout(file_layout)
        
        # مجموعة التنسيق
        format_group = QGroupBox("🎨 التنسيق")
        format_layout = QHBoxLayout()
        
        # اختيار الخط
        self.font_combo = QFontComboBox()
        self.font_combo.setCurrentFont(QFont("Arial"))
        self.font_combo.currentFontChanged.connect(self.change_font)
        
        # حجم الخط
        self.font_size = QSpinBox()
        self.font_size.setRange(8, 72)
        self.font_size.setValue(12)
        self.font_size.valueChanged.connect(self.change_font_size)
        
        # أزرار التنسيق
        bold_btn = QPushButton("B")
        bold_btn.setStyleSheet(self.get_button_style("#34495e") + "font-weight: bold;")
        bold_btn.setCheckable(True)
        bold_btn.clicked.connect(self.toggle_bold)
        
        italic_btn = QPushButton("I")
        italic_btn.setStyleSheet(self.get_button_style("#34495e") + "font-style: italic;")
        italic_btn.setCheckable(True)
        italic_btn.clicked.connect(self.toggle_italic)
        
        underline_btn = QPushButton("U")
        underline_btn.setStyleSheet(self.get_button_style("#34495e") + "text-decoration: underline;")
        underline_btn.setCheckable(True)
        underline_btn.clicked.connect(self.toggle_underline)
        
        color_btn = QPushButton("🎨 لون")
        color_btn.setStyleSheet(self.get_button_style("#9b59b6"))
        color_btn.clicked.connect(self.change_text_color)
        
        format_layout.addWidget(QLabel("الخط:"))
        format_layout.addWidget(self.font_combo)
        format_layout.addWidget(QLabel("الحجم:"))
        format_layout.addWidget(self.font_size)
        format_layout.addWidget(bold_btn)
        format_layout.addWidget(italic_btn)
        format_layout.addWidget(underline_btn)
        format_layout.addWidget(color_btn)
        format_group.setLayout(format_layout)
        
        toolbar_layout.addWidget(file_group)
        toolbar_layout.addWidget(format_group)
        toolbar_layout.addStretch()
        
        toolbar_widget.setLayout(toolbar_layout)
        return toolbar_widget
        
    def create_editor_tab(self):
        """إنشاء تبويب المحرر الرئيسي"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # منطقة التحرير
        self.text_editor = QTextEdit()
        self.text_editor.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                padding: 15px;
                font-family: 'Arial';
                font-size: 12px;
                line-height: 1.5;
            }
            QTextEdit:focus {
                border-color: #3498db;
            }
        """)
        
        # نص نموذجي
        sample_text = """مرحباً بك في محرر النصوص المتقدم!

هذا محرر نصوص احترافي يدعم:
• الكتابة باللغة العربية والإنجليزية
• تنسيق النصوص (خط، حجم، لون)
• حفظ وفتح الملفات
• معاينة النص المنسق
• إعدادات قابلة للتخصيص

يمكنك البدء بالكتابة مباشرة أو فتح ملف موجود.

ميزات المحرر:
1. دعم كامل للغة العربية
2. تنسيق متقدم للنصوص
3. حفظ بصيغ متعددة
4. واجهة سهلة الاستخدام
5. معاينة فورية للتنسيق

استمتع بالكتابة! 📝
"""
        
        self.text_editor.setPlainText(sample_text)
        self.text_editor.textChanged.connect(self.on_text_changed)
        
        layout.addWidget(self.text_editor)
        
        tab.setLayout(layout)
        return tab
        
    def create_preview_tab(self):
        """إنشاء تبويب المعاينة"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # منطقة المعاينة
        preview_group = QGroupBox("👁️ معاينة النص المنسق")
        preview_layout = QVBoxLayout()
        
        self.preview_area = QTextEdit()
        self.preview_area.setReadOnly(True)
        self.preview_area.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        # زر تحديث المعاينة
        update_preview_btn = QPushButton("🔄 تحديث المعاينة")
        update_preview_btn.setStyleSheet(self.get_button_style("#17a2b8"))
        update_preview_btn.clicked.connect(self.update_preview)
        
        preview_layout.addWidget(self.preview_area)
        preview_layout.addWidget(update_preview_btn)
        preview_group.setLayout(preview_layout)
        
        layout.addWidget(preview_group)
        
        tab.setLayout(layout)
        return tab
        
    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إعدادات المحرر
        editor_settings_group = QGroupBox("⚙️ إعدادات المحرر")
        editor_settings_layout = QGridLayout()
        
        # إعدادات الخط الافتراضي
        editor_settings_layout.addWidget(QLabel("الخط الافتراضي:"), 0, 0)
        self.default_font_combo = QFontComboBox()
        self.default_font_combo.setCurrentFont(QFont("Arial"))
        editor_settings_layout.addWidget(self.default_font_combo, 0, 1)
        
        editor_settings_layout.addWidget(QLabel("حجم الخط الافتراضي:"), 0, 2)
        self.default_font_size = QSpinBox()
        self.default_font_size.setRange(8, 72)
        self.default_font_size.setValue(12)
        editor_settings_layout.addWidget(self.default_font_size, 0, 3)
        
        # إعدادات أخرى
        self.auto_save_check = QCheckBox("حفظ تلقائي كل 5 دقائق")
        self.auto_save_check.setChecked(True)
        editor_settings_layout.addWidget(self.auto_save_check, 1, 0, 1, 2)
        
        self.word_wrap_check = QCheckBox("التفاف النص")
        self.word_wrap_check.setChecked(True)
        self.word_wrap_check.toggled.connect(self.toggle_word_wrap)
        editor_settings_layout.addWidget(self.word_wrap_check, 1, 2, 1, 2)
        
        # زر تطبيق الإعدادات
        apply_settings_btn = QPushButton("✅ تطبيق الإعدادات")
        apply_settings_btn.setStyleSheet(self.get_button_style("#28a745"))
        apply_settings_btn.clicked.connect(self.apply_settings)
        editor_settings_layout.addWidget(apply_settings_btn, 2, 0, 1, 4)
        
        editor_settings_group.setLayout(editor_settings_layout)
        
        layout.addWidget(editor_settings_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_widget = QWidget()
        status_layout = QHBoxLayout()

        # معلومات الملف
        self.file_status = QLabel("📄 ملف جديد")
        self.file_status.setStyleSheet("""
            QLabel {
                padding: 5px 10px;
                background-color: #ecf0f1;
                border-radius: 3px;
                font-weight: bold;
            }
        """)

        # معلومات النص
        self.text_stats = QLabel("📊 الكلمات: 0 | الأحرف: 0")
        self.text_stats.setStyleSheet("""
            QLabel {
                padding: 5px 10px;
                background-color: #d5f4e6;
                border-radius: 3px;
            }
        """)

        # حالة التعديل
        self.modified_status = QLabel("💾 محفوظ")
        self.modified_status.setStyleSheet("""
            QLabel {
                padding: 5px 10px;
                background-color: #d4edda;
                border-radius: 3px;
            }
        """)

        status_layout.addWidget(self.file_status)
        status_layout.addWidget(self.text_stats)
        status_layout.addStretch()
        status_layout.addWidget(self.modified_status)

        status_widget.setLayout(status_layout)
        return status_widget

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        # زر المساعدة
        help_btn = QPushButton("❓ المساعدة")
        help_btn.setStyleSheet(self.get_button_style("#3498db"))
        help_btn.clicked.connect(self.show_help)

        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet(self.get_button_style("#e74c3c"))
        close_btn.clicked.connect(self.close_editor)

        layout.addWidget(help_btn)
        layout.addStretch()
        layout.addWidget(close_btn)

        return layout

    def get_button_style(self, color):
        """الحصول على تنسيق الأزرار"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                border: none;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color)};
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.8)};
            }}
        """

    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        color_map = {
            "#3498db": "#2980b9",
            "#27ae60": "#229954",
            "#f39c12": "#e67e22",
            "#e67e22": "#d35400",
            "#34495e": "#2c3e50",
            "#9b59b6": "#8e44ad",
            "#17a2b8": "#138496",
            "#28a745": "#218838",
            "#e74c3c": "#c0392b"
        }
        return color_map.get(color, color)

    # الوظائف الأساسية
    def new_file(self):
        """إنشاء ملف جديد"""
        if self.is_modified:
            reply = QMessageBox.question(self, "حفظ التغييرات",
                                       "هل تريد حفظ التغييرات قبل إنشاء ملف جديد؟",
                                       QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel)
            if reply == QMessageBox.Yes:
                self.save_file()
            elif reply == QMessageBox.Cancel:
                return

        self.text_editor.clear()
        self.current_file = None
        self.is_modified = False
        self.update_status()

    def open_file(self):
        """فتح ملف"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "فتح ملف", "",
            "Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                    self.text_editor.setPlainText(content)
                    self.current_file = file_path
                    self.is_modified = False
                    self.update_status()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في فتح الملف:\n{str(e)}")

    def save_file(self):
        """حفظ الملف"""
        if self.current_file:
            try:
                with open(self.current_file, 'w', encoding='utf-8') as file:
                    file.write(self.text_editor.toPlainText())
                    self.is_modified = False
                    self.update_status()
                    QMessageBox.information(self, "نجح", "تم حفظ الملف بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حفظ الملف:\n{str(e)}")
        else:
            self.save_as_file()

    def save_as_file(self):
        """حفظ الملف باسم جديد"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ الملف", "",
            "Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(self.text_editor.toPlainText())
                    self.current_file = file_path
                    self.is_modified = False
                    self.update_status()
                    QMessageBox.information(self, "نجح", "تم حفظ الملف بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حفظ الملف:\n{str(e)}")

    def on_text_changed(self):
        """عند تغيير النص"""
        self.is_modified = True
        self.update_status()
        self.update_text_stats()

    def update_status(self):
        """تحديث شريط الحالة"""
        # حالة الملف
        if self.current_file:
            filename = os.path.basename(self.current_file)
            self.file_status.setText(f"📄 {filename}")
        else:
            self.file_status.setText("📄 ملف جديد")

        # حالة التعديل
        if self.is_modified:
            self.modified_status.setText("✏️ معدل")
            self.modified_status.setStyleSheet("""
                QLabel {
                    padding: 5px 10px;
                    background-color: #f8d7da;
                    border-radius: 3px;
                }
            """)
        else:
            self.modified_status.setText("💾 محفوظ")
            self.modified_status.setStyleSheet("""
                QLabel {
                    padding: 5px 10px;
                    background-color: #d4edda;
                    border-radius: 3px;
                }
            """)

    def update_text_stats(self):
        """تحديث إحصائيات النص"""
        text = self.text_editor.toPlainText()
        words = len(text.split()) if text.strip() else 0
        chars = len(text)
        self.text_stats.setText(f"📊 الكلمات: {words} | الأحرف: {chars}")

    def change_font(self, font):
        """تغيير الخط"""
        cursor = self.text_editor.textCursor()
        if cursor.hasSelection():
            format = QTextCharFormat()
            format.setFontFamily(font.family())
            cursor.mergeCharFormat(format)
        else:
            self.text_editor.setCurrentFont(font)

    def change_font_size(self, size):
        """تغيير حجم الخط"""
        cursor = self.text_editor.textCursor()
        if cursor.hasSelection():
            format = QTextCharFormat()
            format.setFontPointSize(size)
            cursor.mergeCharFormat(format)
        else:
            font = self.text_editor.currentFont()
            font.setPointSize(size)
            self.text_editor.setCurrentFont(font)

    def toggle_bold(self, checked):
        """تبديل الخط العريض"""
        cursor = self.text_editor.textCursor()
        format = QTextCharFormat()
        format.setFontWeight(QFont.Bold if checked else QFont.Normal)
        cursor.mergeCharFormat(format)

    def toggle_italic(self, checked):
        """تبديل الخط المائل"""
        cursor = self.text_editor.textCursor()
        format = QTextCharFormat()
        format.setFontItalic(checked)
        cursor.mergeCharFormat(format)

    def toggle_underline(self, checked):
        """تبديل الخط المسطر"""
        cursor = self.text_editor.textCursor()
        format = QTextCharFormat()
        format.setFontUnderline(checked)
        cursor.mergeCharFormat(format)

    def change_text_color(self):
        """تغيير لون النص"""
        color = QColorDialog.getColor(Qt.black, self)
        if color.isValid():
            cursor = self.text_editor.textCursor()
            format = QTextCharFormat()
            format.setForeground(color)
            cursor.mergeCharFormat(format)

    def update_preview(self):
        """تحديث المعاينة"""
        html_content = self.text_editor.toHtml()
        self.preview_area.setHtml(html_content)

    def toggle_word_wrap(self, checked):
        """تبديل التفاف النص"""
        if checked:
            self.text_editor.setLineWrapMode(QTextEdit.WidgetWidth)
        else:
            self.text_editor.setLineWrapMode(QTextEdit.NoWrap)

    def apply_settings(self):
        """تطبيق الإعدادات"""
        # تطبيق الخط الافتراضي
        font = self.default_font_combo.currentFont()
        font.setPointSize(self.default_font_size.value())
        self.text_editor.setCurrentFont(font)

        # تطبيق التفاف النص
        self.toggle_word_wrap(self.word_wrap_check.isChecked())

        QMessageBox.information(self, "نجح", "تم تطبيق الإعدادات بنجاح")

    def close_editor(self):
        """إغلاق المحرر"""
        if self.is_modified:
            reply = QMessageBox.question(self, "حفظ التغييرات",
                                       "هل تريد حفظ التغييرات قبل الإغلاق؟",
                                       QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel)
            if reply == QMessageBox.Yes:
                self.save_file()
            elif reply == QMessageBox.Cancel:
                return

        self.reject()

    def show_help(self):
        """عرض المساعدة"""
        help_text = """
📝 مساعدة محرر النصوص المتقدم

📋 الميزات المتاحة:

📁 إدارة الملفات:
• إنشاء ملف جديد
• فتح ملفات نصية موجودة
• حفظ الملف الحالي
• حفظ باسم جديد
• دعم ترميز UTF-8 للعربية

🎨 تنسيق النصوص:
• اختيار نوع الخط وحجمه
• خط عريض، مائل، مسطر
• تغيير لون النص
• تنسيق النصوص المحددة

👁️ المعاينة:
• معاينة النص المنسق
• عرض HTML للنص
• تحديث فوري للتغييرات

⚙️ الإعدادات:
• تخصيص الخط الافتراضي
• حفظ تلقائي
• التفاف النص
• إعدادات قابلة للحفظ

📊 شريط الحالة:
• اسم الملف الحالي
• عدد الكلمات والأحرف
• حالة التعديل والحفظ

💡 نصائح الاستخدام:
• حدد النص لتطبيق التنسيق عليه
• استخدم Ctrl+S للحفظ السريع
• راجع المعاينة قبل الحفظ النهائي
• احفظ عملك بانتظام

🎯 الاختصارات:
• Ctrl+N: ملف جديد
• Ctrl+O: فتح ملف
• Ctrl+S: حفظ
• Ctrl+B: خط عريض
• Ctrl+I: خط مائل
• Ctrl+U: خط مسطر

🌟 المميزات:
• دعم كامل للغة العربية
• واجهة سهلة الاستخدام
• تنسيق احترافي للنصوص
• حفظ آمن للملفات
"""

        QMessageBox.information(self, "المساعدة", help_text)


def main():
    """اختبار محرر النصوص"""
    import sys

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    dialog = TextEditorDialog()
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
