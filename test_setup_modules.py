#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شاشات التهيئة
Test Setup Modules
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_general_indicators():
    """اختبار شاشة المؤشرات العامة"""
    try:
        print("🧪 اختبار شاشة المؤشرات العامة...")
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.setup.general_indicators import GeneralIndicatorsDialog
        
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass
        
        dialog = GeneralIndicatorsDialog(MockDBManager())
        print("✅ تم إنشاء شاشة المؤشرات العامة بنجاح")
        
        dialog.show()
        print("✅ تم عرض الشاشة بنجاح")
        
        print("📋 الميزات المتاحة:")
        print("   ✓ تبويب المعلومات الأساسية")
        print("   ✓ تبويب الإعدادات المالية") 
        print("   ✓ تبويب إعدادات النظام")
        print("   ✓ حفظ وتحميل الإعدادات")
        print("   ✓ القيم الافتراضية")
        
        app.exec_()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المؤشرات العامة: {e}")
        return False

def test_system_rules():
    """اختبار شاشة إعداد قواعد النظام"""
    try:
        print("\n🧪 اختبار شاشة إعداد قواعد النظام...")
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.setup.system_rules import SystemRulesDialog
        
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass
        
        dialog = SystemRulesDialog(MockDBManager())
        print("✅ تم إنشاء شاشة قواعد النظام بنجاح")
        
        dialog.show()
        print("✅ تم عرض الشاشة بنجاح")
        
        print("📋 الميزات المتاحة:")
        print("   ✓ تبويب القواعد المحاسبية")
        print("   ✓ تبويب قواعد المخزون")
        print("   ✓ تبويب قواعد المبيعات")
        print("   ✓ تبويب قواعد الأمان")
        print("   ✓ حفظ وتحميل القواعد")
        
        app.exec_()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قواعد النظام: {e}")
        return False

def test_currency_setup():
    """اختبار شاشة تهيئة العملات"""
    try:
        print("\n🧪 اختبار شاشة تهيئة العملات...")
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.setup.currency_setup import CurrencySetupDialog
        
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass
        
        dialog = CurrencySetupDialog(MockDBManager())
        print("✅ تم إنشاء شاشة تهيئة العملات بنجاح")
        
        dialog.show()
        print("✅ تم عرض الشاشة بنجاح")
        
        print("📋 الميزات المتاحة:")
        print("   ✓ إدارة العملات (إضافة، تعديل، حذف)")
        print("   ✓ إدارة أسعار الصرف")
        print("   ✓ تحديث الأسعار")
        print("   ✓ جداول تفاعلية")
        print("   ✓ واجهة مقسمة")
        
        app.exec_()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تهيئة العملات: {e}")
        return False

def test_international_regions():
    """اختبار شاشة الأقاليم الدولية / الدول"""
    try:
        print("\n🧪 اختبار شاشة الأقاليم الدولية / الدول...")

        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.setup.international_regions import InternationalRegionsDialog

        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)

        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass

        dialog = InternationalRegionsDialog(MockDBManager())
        print("✅ تم إنشاء شاشة الأقاليم الدولية بنجاح")

        dialog.show()
        print("✅ تم عرض الشاشة بنجاح")

        print("📋 الميزات المتاحة:")
        print("   ✓ تبويب الأقاليم الدولية")
        print("   ✓ تبويب الدول")
        print("   ✓ تبويب إعدادات التقسيم")
        print("   ✓ إدارة الأقاليم (إضافة، تعديل، حذف)")
        print("   ✓ إدارة الدول مع ربطها بالأقاليم")
        print("   ✓ جداول تفاعلية")
        print("   ✓ بيانات افتراضية شاملة")

        app.exec_()
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار الأقاليم الدولية: {e}")
        return False

def test_administrative_divisions():
    """اختبار شاشة بيانات المحافظات / المناطق / المدن"""
    try:
        print("\n🧪 اختبار شاشة بيانات المحافظات / المناطق / المدن...")

        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.setup.administrative_divisions import AdministrativeDivisionsDialog

        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)

        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass

        dialog = AdministrativeDivisionsDialog(MockDBManager())
        print("✅ تم إنشاء شاشة التقسيمات الإدارية بنجاح")

        dialog.show()
        print("✅ تم عرض الشاشة بنجاح")

        print("📋 الميزات المتاحة:")
        print("   ✓ شجرة هرمية للتقسيمات الإدارية")
        print("   ✓ تبويب المحافظات")
        print("   ✓ تبويب المناطق")
        print("   ✓ تبويب المدن")
        print("   ✓ تبويب القرى")
        print("   ✓ إدارة شاملة (إضافة، تعديل، حذف)")
        print("   ✓ ربط هرمي بين التقسيمات")
        print("   ✓ بيانات نموذجية يمنية")

        app.exec_()
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار التقسيمات الإدارية: {e}")
        return False

def test_governorate_groups():
    """اختبار شاشة مجموعات المحافظات"""
    try:
        print("\n🧪 اختبار شاشة مجموعات المحافظات...")

        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.setup.governorate_groups import GovernorateGroupsDialog

        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)

        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass

        dialog = GovernorateGroupsDialog(MockDBManager())
        print("✅ تم إنشاء شاشة مجموعات المحافظات بنجاح")

        dialog.show()
        print("✅ تم عرض الشاشة بنجاح")

        print("📋 الميزات المتاحة:")
        print("   ✓ تبويب إدارة المجموعات")
        print("   ✓ تبويب تعيين المحافظات")
        print("   ✓ تبويب التقارير والإحصائيات")
        print("   ✓ إدارة المجموعات (إضافة، تعديل، حذف)")
        print("   ✓ تعيين المحافظات للمجموعات")
        print("   ✓ واجهة سحب وإفلات للتعيين")
        print("   ✓ تقارير شاملة وإحصائيات")
        print("   ✓ 8 مجموعات افتراضية")
        print("   ✓ تغطية جميع المحافظات اليمنية")

        app.exec_()
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار مجموعات المحافظات: {e}")
        return False

def test_branch_tree():
    """اختبار شاشة شجرة الفروع"""
    try:
        print("\n🧪 اختبار شاشة شجرة الفروع...")

        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.setup.branch_tree import BranchTreeDialog

        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)

        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass

        dialog = BranchTreeDialog(MockDBManager())
        print("✅ تم إنشاء شاشة شجرة الفروع بنجاح")

        dialog.show()
        print("✅ تم عرض الشاشة بنجاح")

        print("📋 الميزات المتاحة:")
        print("   ✓ شجرة هرمية تفاعلية للفروع والمكاتب")
        print("   ✓ تبويب الفروع الرئيسية")
        print("   ✓ تبويب الفروع الفرعية")
        print("   ✓ تبويب المكاتب")
        print("   ✓ تبويب إعدادات الشجرة")
        print("   ✓ إدارة شاملة (إضافة، تعديل، حذف)")
        print("   ✓ ربط هرمي بين الفروع والمكاتب")
        print("   ✓ بيانات نموذجية يمنية")
        print("   ✓ إحصائيات ديناميكية")
        print("   ✓ أدوات الشجرة (توسيع، طي، تحديث)")

        app.exec_()
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار شجرة الفروع: {e}")
        return False

def test_branch_definition():
    """اختبار شاشة تعريف الفرع"""
    try:
        print("\n🧪 اختبار شاشة تعريف الفرع...")

        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.setup.branch_definition import BranchDefinitionDialog

        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)

        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass

        dialog = BranchDefinitionDialog(MockDBManager())
        print("✅ تم إنشاء شاشة تعريف الفرع بنجاح")

        dialog.show()
        print("✅ تم عرض الشاشة بنجاح")

        print("📋 الميزات المتاحة:")
        print("   ✓ 6 تبويبات شاملة للإعداد")
        print("   ✓ المعلومات الأساسية والحالة")
        print("   ✓ الموقع والاتصال مع الإحداثيات")
        print("   ✓ الإعدادات المالية والضرائب")
        print("   ✓ أوقات العمل والاستراحات")
        print("   ✓ الخدمات والمرافق المتاحة")
        print("   ✓ الأمان والصلاحيات")
        print("   ✓ بيانات نموذجية يمنية شاملة")
        print("   ✓ معاينة التعريف قبل الحفظ")
        print("   ✓ تحديد الموقع على الخريطة")

        app.exec_()
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار تعريف الفرع: {e}")
        return False

def test_accounting_guide():
    """اختبار شاشة إعداد الدليل المحاسبي"""
    try:
        print("\n🧪 اختبار شاشة إعداد الدليل المحاسبي...")

        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.setup.accounting_guide import AccountingGuideDialog

        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)

        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass

        dialog = AccountingGuideDialog(MockDBManager())
        print("✅ تم إنشاء شاشة الدليل المحاسبي بنجاح")

        dialog.show()
        print("✅ تم عرض الشاشة بنجاح")

        print("📋 الميزات المتاحة:")
        print("   ✓ شجرة حسابات هرمية تفاعلية")
        print("   ✓ 5 تبويبات شاملة للإدارة")
        print("   ✓ الحسابات الرئيسية والفرعية")
        print("   ✓ أنواع الحسابات وقوالب جاهزة")
        print("   ✓ إعدادات الدليل والأمان")
        print("   ✓ تقارير مالية متقدمة")
        print("   ✓ دليل محاسبي نموذجي يمني")
        print("   ✓ ميزان مراجعة وأرصدة حسابات")
        print("   ✓ 8 حسابات رئيسية و12 حساب فرعي")
        print("   ✓ نظام ترقيم متقدم ومرن")

        app.exec_()
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار الدليل المحاسبي: {e}")
        return False

def test_account_coding():
    """اختبار شاشة ترميز الحسابات"""
    try:
        print("\n🧪 اختبار شاشة ترميز الحسابات...")

        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.setup.account_coding import AccountCodingDialog

        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)

        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass

        dialog = AccountCodingDialog(MockDBManager())
        print("✅ تم إنشاء شاشة ترميز الحسابات بنجاح")

        dialog.show()
        print("✅ تم عرض الشاشة بنجاح")

        print("📋 الميزات المتاحة:")
        print("   ✓ 5 أنظمة ترميز مختلفة ومرنة")
        print("   ✓ 5 تبويبات شاملة للإدارة")
        print("   ✓ إعدادات متقدمة للتخصيص")
        print("   ✓ قواعد ترميز ذكية مع أمثلة")
        print("   ✓ تطبيق تدريجي أو فوري")
        print("   ✓ فحص شامل مع إصلاح تلقائي")
        print("   ✓ تقارير مفصلة وإحصائيات")
        print("   ✓ معاينة فورية للنظام")
        print("   ✓ معالجة ذكية للتعارضات")
        print("   ✓ 19 حساب جاهز للترميز")
        print("   ✓ 4 مستويات هرمية محددة")

        app.exec_()
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار ترميز الحسابات: {e}")
        return False

def test_account_project_linking():
    """اختبار شاشة ربط الحسابات بإدخال المشاريع"""
    try:
        print("\n🧪 اختبار شاشة ربط الحسابات بإدخال المشاريع...")

        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.setup.account_project_linking import AccountProjectLinkingDialog

        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)

        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass

        dialog = AccountProjectLinkingDialog(MockDBManager())
        print("✅ تم إنشاء شاشة ربط الحسابات بالمشاريع بنجاح")

        dialog.show()
        print("✅ تم عرض الشاشة بنجاح")

        print("📋 الميزات المتاحة:")
        print("   ✓ شجرة حسابات تفاعلية مع تلوين مرئي")
        print("   ✓ 4 تبويبات شاملة للإدارة")
        print("   ✓ 4 أنواع ربط مختلفة (إجباري، اختياري، تلقائي، شرطي)")
        print("   ✓ إدارة مشاريع متكاملة مع 5 أنواع و5 حالات")
        print("   ✓ قواعد ربط مرنة عامة ومخصصة")
        print("   ✓ تقارير شاملة مع إحصائيات مفصلة")
        print("   ✓ اختبار الربط قبل التطبيق")
        print("   ✓ إعدادات متقدمة للموافقة والقيود")
        print("   ✓ 19 حساب محاسبي (12 مربوط، 7 غير مربوط)")
        print("   ✓ 5 مشاريع نموذجية جاهزة")
        print("   ✓ 4 قواعد ربط مخصصة")

        app.exec_()
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار ربط الحسابات بالمشاريع: {e}")
        return False

def test_main_system_integration():
    """اختبار التكامل مع النظام الرئيسي"""
    try:
        print("\n🧪 اختبار التكامل مع النظام الرئيسي...")

        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.main_system import MainSystemWindow

        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)

        window = MainSystemWindow()
        print("✅ تم إنشاء النظام الرئيسي بنجاح")

        window.show()
        print("✅ تم عرض النظام الرئيسي بنجاح")

        print("📋 شاشات التهيئة المتكاملة:")
        print("   ✓ المؤشرات العامة - متاحة")
        print("   ✓ إعداد قواعد النظام - متاحة")
        print("   ✓ تهيئة العملات - متاحة")
        print("   ✓ الأقاليم الدولية / الدول - متاحة")
        print("   ✓ بيانات المحافظات / المناطق / المدن - متاحة")
        print("   ✓ مجموعات المحافظات - متاحة")
        print("   ✓ شجرة الفروع - متاحة")
        print("   ✓ تعريف الفرع - متاحة")
        print("   ✓ إعداد الدليل المحاسبي - متاحة")
        print("   ✓ ترميز الحسابات - متاحة")
        print("   ✓ ربط الحسابات بإدخال المشاريع - متاحة")
        print("   🚧 باقي شاشات التهيئة (4 شاشات) - قيد التطوير")

        app.exec_()
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار التكامل: {e}")
        return False

def main():
    """الوظيفة الرئيسية للاختبار"""
    print("=" * 70)
    print("🧪 اختبار شاشات التهيئة - مجلد التهيئة")
    print("   Setup Modules Test")
    print("=" * 70)
    
    # اختبار الشاشات
    tests = [
        ("المؤشرات العامة", test_general_indicators),
        ("إعداد قواعد النظام", test_system_rules),
        ("تهيئة العملات", test_currency_setup),
        ("الأقاليم الدولية / الدول", test_international_regions),
        ("بيانات المحافظات / المناطق / المدن", test_administrative_divisions),
        ("مجموعات المحافظات", test_governorate_groups),
        ("شجرة الفروع", test_branch_tree),
        ("تعريف الفرع", test_branch_definition),
        ("إعداد الدليل المحاسبي", test_accounting_guide),
        ("ترميز الحسابات", test_account_coding),
        ("ربط الحسابات بإدخال المشاريع", test_account_project_linking),
        ("التكامل مع النظام الرئيسي", test_main_system_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🎯 اختبار: {test_name}")
        print(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ نجح" if result else "❌ فشل"
            print(f"\n📊 نتيجة اختبار {test_name}: {status}")
        except KeyboardInterrupt:
            print(f"\n⚠️ تم إيقاف اختبار {test_name} بواسطة المستخدم")
            results[test_name] = False
            break
        except Exception as e:
            print(f"\n❌ خطأ في اختبار {test_name}: {e}")
            results[test_name] = False
    
    # عرض النتائج النهائية
    print("\n" + "=" * 70)
    print("📊 ملخص نتائج الاختبارات:")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة الإجمالية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ شاشات التهيئة جاهزة للاستخدام")
        
        print("\n📋 الشاشات المتاحة في مجلد التهيئة:")
        print("   1️⃣ المؤشرات العامة - تحديد خصائص النظام العامة")
        print("   2️⃣ إعداد قواعد النظام - تفعيل وتعطيل خصائص النظام")
        print("   3️⃣ تهيئة العملات - تعريف العملات وأسعار الصرف")
        print("   4️⃣ الأقاليم الدولية / الدول - التقسيم الجغرافي الدولي")
        print("   5️⃣ بيانات المحافظات / المناطق / المدن - الهيكل الإداري الداخلي")
        print("   6️⃣ مجموعات المحافظات - تجميع المحافظات حسب المعايير")
        print("   7️⃣ شجرة الفروع - الهيكل التنظيمي للفروع والمكاتب")
        print("   8️⃣ تعريف الفرع - الإعداد الشامل للفرع")
        print("   9️⃣ إعداد الدليل المحاسبي - شجرة الحسابات المتكاملة")
        print("   🔟 ترميز الحسابات - نظام الترقيم والتصنيف المحاسبي")
        print("   1️⃣1️⃣ ربط الحسابات بإدخال المشاريع - نظام الربط المحاسبي للمشاريع")
        print("   🚧 باقي الشاشات (4 شاشات) - قيد التطوير")
        
        print("\n🎯 كيفية الوصول:")
        print("   • شغل النظام الرئيسي")
        print("   • انقر على مجلد 'التهيئة' في شجرة النظام")
        print("   • اختر الشاشة المطلوبة")
        
    else:
        print(f"\n⚠️ {total - passed} اختبارات فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 70)
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبارات بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبارات: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
