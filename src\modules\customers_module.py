#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة العملاء
Customers Management Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QDoubleSpinBox, QCheckBox,
                             QDateEdit, QSplitter, QFrame, QDialogButtonBox,
                             QScrollArea)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor
from datetime import datetime

from ..database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class CustomerDialog(QDialog):
    """حوار إضافة/تعديل العملاء"""
    
    def __init__(self, db_manager: SQLiteManager, customer_data=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.customer_data = customer_data
        self.is_edit_mode = customer_data is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_customer_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل العميل" if self.is_edit_mode else "إضافة عميل جديد"
        self.setWindowTitle(title)
        self.setFixedSize(600, 700)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel(f"👤 {title}")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        main_layout.addWidget(title_label)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()
        
        # معلومات أساسية
        basic_group = QGroupBox("📋 المعلومات الأساسية")
        basic_layout = QFormLayout()
        
        # كود العميل
        self.customer_code_edit = QLineEdit()
        self.customer_code_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً إذا ترك فارغاً")
        basic_layout.addRow("كود العميل:", self.customer_code_edit)
        
        # اسم العميل
        self.customer_name_edit = QLineEdit()
        self.customer_name_edit.setPlaceholderText("أدخل اسم العميل...")
        basic_layout.addRow("اسم العميل *:", self.customer_name_edit)
        
        # نوع العميل
        self.customer_type_combo = QComboBox()
        self.customer_type_combo.addItem("فرد", "individual")
        self.customer_type_combo.addItem("شركة", "company")
        basic_layout.addRow("نوع العميل:", self.customer_type_combo)
        
        # الشخص المسؤول
        self.contact_person_edit = QLineEdit()
        self.contact_person_edit.setPlaceholderText("اسم الشخص المسؤول...")
        basic_layout.addRow("الشخص المسؤول:", self.contact_person_edit)
        
        basic_group.setLayout(basic_layout)
        scroll_layout.addWidget(basic_group)
        
        # معلومات الاتصال
        contact_group = QGroupBox("📞 معلومات الاتصال")
        contact_layout = QFormLayout()
        
        # الهاتف
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("011-1234567")
        contact_layout.addRow("الهاتف:", self.phone_edit)
        
        # الجوال
        self.mobile_edit = QLineEdit()
        self.mobile_edit.setPlaceholderText("0501234567")
        contact_layout.addRow("الجوال:", self.mobile_edit)
        
        # البريد الإلكتروني
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("<EMAIL>")
        contact_layout.addRow("البريد الإلكتروني:", self.email_edit)
        
        contact_group.setLayout(contact_layout)
        scroll_layout.addWidget(contact_group)
        
        # العنوان
        address_group = QGroupBox("📍 العنوان")
        address_layout = QFormLayout()
        
        # العنوان
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        self.address_edit.setPlaceholderText("أدخل العنوان التفصيلي...")
        address_layout.addRow("العنوان:", self.address_edit)
        
        # المدينة
        self.city_edit = QLineEdit()
        self.city_edit.setPlaceholderText("الرياض")
        address_layout.addRow("المدينة:", self.city_edit)
        
        # الدولة
        self.country_combo = QComboBox()
        self.country_combo.addItems(["السعودية", "الإمارات", "الكويت", "قطر", "البحرين", "عمان", "الأردن", "مصر", "أخرى"])
        address_layout.addRow("الدولة:", self.country_combo)
        
        address_group.setLayout(address_layout)
        scroll_layout.addWidget(address_group)
        
        # المعلومات المالية
        financial_group = QGroupBox("💰 المعلومات المالية")
        financial_layout = QFormLayout()
        
        # الرقم الضريبي
        self.tax_number_edit = QLineEdit()
        self.tax_number_edit.setPlaceholderText("1234567890123")
        financial_layout.addRow("الرقم الضريبي:", self.tax_number_edit)
        
        # حد الائتمان
        self.credit_limit_spin = QDoubleSpinBox()
        self.credit_limit_spin.setRange(0, 9999999.99)
        self.credit_limit_spin.setSuffix(" ريال")
        self.credit_limit_spin.setValue(0)
        financial_layout.addRow("حد الائتمان:", self.credit_limit_spin)
        
        # شروط الدفع
        self.payment_terms_spin = QSpinBox()
        self.payment_terms_spin.setRange(0, 365)
        self.payment_terms_spin.setSuffix(" يوم")
        self.payment_terms_spin.setValue(30)
        financial_layout.addRow("شروط الدفع:", self.payment_terms_spin)
        
        financial_group.setLayout(financial_layout)
        scroll_layout.addWidget(financial_group)
        
        # إعدادات إضافية
        settings_group = QGroupBox("⚙️ إعدادات إضافية")
        settings_layout = QFormLayout()
        
        # نشط
        self.is_active_check = QCheckBox("العميل نشط")
        self.is_active_check.setChecked(True)
        settings_layout.addRow("", self.is_active_check)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        settings_layout.addRow("ملاحظات:", self.notes_edit)
        
        settings_group.setLayout(settings_layout)
        scroll_layout.addWidget(settings_group)
        
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        main_layout.addWidget(button_box)
        
        self.setLayout(main_layout)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #2196F3;
            }
            QPushButton {
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_customer)
        self.cancel_btn.clicked.connect(self.reject)
        self.customer_type_combo.currentTextChanged.connect(self.on_customer_type_changed)
    
    def on_customer_type_changed(self):
        """معالجة تغيير نوع العميل"""
        customer_type = self.customer_type_combo.currentData()
        
        # إظهار/إخفاء حقل الشخص المسؤول حسب نوع العميل
        if customer_type == "company":
            self.contact_person_edit.setEnabled(True)
            self.contact_person_edit.setPlaceholderText("اسم الشخص المسؤول...")
        else:
            self.contact_person_edit.setEnabled(False)
            self.contact_person_edit.clear()
            self.contact_person_edit.setPlaceholderText("غير مطلوب للأفراد")
    
    def load_customer_data(self):
        """تحميل بيانات العميل للتعديل"""
        if not self.customer_data:
            return
        
        data = self.customer_data
        
        # المعلومات الأساسية
        self.customer_code_edit.setText(data.get('customer_code', ''))
        self.customer_code_edit.setEnabled(False)  # لا يمكن تعديل الكود
        self.customer_name_edit.setText(data.get('customer_name', ''))
        
        # نوع العميل
        customer_type = data.get('customer_type', 'individual')
        for i in range(self.customer_type_combo.count()):
            if self.customer_type_combo.itemData(i) == customer_type:
                self.customer_type_combo.setCurrentIndex(i)
                break
        
        self.contact_person_edit.setText(data.get('contact_person', ''))
        
        # معلومات الاتصال
        self.phone_edit.setText(data.get('phone', ''))
        self.mobile_edit.setText(data.get('mobile', ''))
        self.email_edit.setText(data.get('email', ''))
        
        # العنوان
        self.address_edit.setPlainText(data.get('address', ''))
        self.city_edit.setText(data.get('city', ''))
        
        # الدولة
        country = data.get('country', 'السعودية')
        index = self.country_combo.findText(country)
        if index >= 0:
            self.country_combo.setCurrentIndex(index)
        
        # المعلومات المالية
        self.tax_number_edit.setText(data.get('tax_number', ''))
        self.credit_limit_spin.setValue(float(data.get('credit_limit', 0)))
        self.payment_terms_spin.setValue(int(data.get('payment_terms', 30)))
        
        # الإعدادات
        self.is_active_check.setChecked(bool(data.get('is_active', True)))
        self.notes_edit.setPlainText(data.get('notes', ''))
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.customer_name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم العميل")
            self.customer_name_edit.setFocus()
            return False
        
        # التحقق من البريد الإلكتروني
        email = self.email_edit.text().strip()
        if email and '@' not in email:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال بريد إلكتروني صحيح")
            self.email_edit.setFocus()
            return False
        
        # التحقق من الرقم الضريبي للشركات
        if (self.customer_type_combo.currentData() == "company" and 
            self.tax_number_edit.text().strip() and 
            len(self.tax_number_edit.text().strip()) != 15):
            QMessageBox.warning(self, "تحذير", "الرقم الضريبي يجب أن يكون 15 رقم")
            self.tax_number_edit.setFocus()
            return False
        
        return True
    
    def generate_customer_code(self):
        """إنشاء كود عميل تلقائي"""
        try:
            # الحصول على آخر كود عميل
            query = "SELECT customer_code FROM customers ORDER BY id DESC LIMIT 1"
            result = self.db_manager.execute_query(query)
            
            if result:
                last_code = result[0]['customer_code']
                # استخراج الرقم من الكود
                if last_code.startswith('CUST'):
                    try:
                        last_number = int(last_code[4:])
                        new_number = last_number + 1
                        return f"CUST{new_number:03d}"
                    except:
                        pass
            
            # إذا لم يوجد كود سابق أو حدث خطأ
            return "CUST001"
            
        except:
            return "CUST001"
    
    def save_customer(self):
        """حفظ بيانات العميل"""
        if not self.validate_data():
            return
        
        try:
            # جمع البيانات
            customer_code = self.customer_code_edit.text().strip()
            if not customer_code:
                customer_code = self.generate_customer_code()
            
            data = {
                'customer_code': customer_code,
                'customer_name': self.customer_name_edit.text().strip(),
                'customer_type': self.customer_type_combo.currentData(),
                'contact_person': self.contact_person_edit.text().strip(),
                'phone': self.phone_edit.text().strip(),
                'mobile': self.mobile_edit.text().strip(),
                'email': self.email_edit.text().strip(),
                'address': self.address_edit.toPlainText().strip(),
                'city': self.city_edit.text().strip(),
                'country': self.country_combo.currentText(),
                'tax_number': self.tax_number_edit.text().strip(),
                'credit_limit': self.credit_limit_spin.value(),
                'payment_terms': self.payment_terms_spin.value(),
                'is_active': self.is_active_check.isChecked(),
                'notes': self.notes_edit.toPlainText().strip()
            }
            
            if self.is_edit_mode:
                # تحديث العميل
                query = """
                    UPDATE customers SET
                        customer_name = ?, customer_type = ?, contact_person = ?,
                        phone = ?, mobile = ?, email = ?, address = ?, city = ?,
                        country = ?, tax_number = ?, credit_limit = ?, payment_terms = ?,
                        is_active = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """
                params = (
                    data['customer_name'], data['customer_type'], data['contact_person'],
                    data['phone'], data['mobile'], data['email'], data['address'],
                    data['city'], data['country'], data['tax_number'], data['credit_limit'],
                    data['payment_terms'], data['is_active'], data['notes'],
                    self.customer_data['id']
                )
            else:
                # إضافة عميل جديد
                query = """
                    INSERT INTO customers (
                        customer_code, customer_name, customer_type, contact_person,
                        phone, mobile, email, address, city, country, tax_number,
                        credit_limit, payment_terms, is_active, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (
                    data['customer_code'], data['customer_name'], data['customer_type'],
                    data['contact_person'], data['phone'], data['mobile'], data['email'],
                    data['address'], data['city'], data['country'], data['tax_number'],
                    data['credit_limit'], data['payment_terms'], data['is_active'], data['notes']
                )
            
            self.db_manager.execute_update(query, params)
            
            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} العميل بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ العميل:\n{str(e)}")


class CustomersWidget(QWidget):
    """وحدة إدارة العملاء"""

    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.setup_connections()
        self.load_customers()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # العنوان
        title = QLabel("👥 إدارة العملاء")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        main_layout.addWidget(title)

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        self.add_btn = QPushButton("➕ إضافة عميل")
        self.edit_btn = QPushButton("✏️ تعديل")
        self.delete_btn = QPushButton("🗑️ حذف")
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.export_btn = QPushButton("📤 تصدير")

        # تنسيق الأزرار
        buttons = [self.add_btn, self.edit_btn, self.delete_btn, self.refresh_btn, self.export_btn]

        for btn in buttons:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QPushButton:pressed {
                    background-color: #21618c;
                }
                QPushButton:disabled {
                    background-color: #bdc3c7;
                }
            """)

        # ألوان خاصة لبعض الأزرار
        self.add_btn.setStyleSheet(self.add_btn.styleSheet().replace("#3498db", "#27ae60"))
        self.delete_btn.setStyleSheet(self.delete_btn.styleSheet().replace("#3498db", "#e74c3c"))
        self.export_btn.setStyleSheet(self.export_btn.styleSheet().replace("#3498db", "#f39c12"))

        toolbar_layout.addWidget(self.add_btn)
        toolbar_layout.addWidget(self.edit_btn)
        toolbar_layout.addWidget(self.delete_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addWidget(self.export_btn)
        toolbar_layout.addStretch()

        main_layout.addLayout(toolbar_layout)

        # منطقة البحث والفلترة
        search_layout = QHBoxLayout()

        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في العملاء...")
        search_layout.addWidget(self.search_edit)

        search_layout.addWidget(QLabel("النوع:"))
        self.type_filter = QComboBox()
        self.type_filter.addItems(["الكل", "أفراد", "شركات"])
        search_layout.addWidget(self.type_filter)

        search_layout.addWidget(QLabel("الحالة:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "نشط", "غير نشط"])
        search_layout.addWidget(self.status_filter)

        main_layout.addLayout(search_layout)

        # جدول العملاء
        self.customers_table = QTableWidget()
        columns = ["الكود", "اسم العميل", "النوع", "الهاتف", "البريد الإلكتروني", "المدينة", "حد الائتمان", "الحالة"]
        self.customers_table.setColumnCount(len(columns))
        self.customers_table.setHorizontalHeaderLabels(columns)

        # تنسيق الجدول
        self.customers_table.setAlternatingRowColors(True)
        self.customers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.customers_table.setSelectionMode(QTableWidget.SingleSelection)
        self.customers_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.customers_table.horizontalHeader()
        header.resizeSection(0, 100)  # الكود
        header.resizeSection(1, 200)  # اسم العميل
        header.resizeSection(2, 80)   # النوع
        header.resizeSection(3, 120)  # الهاتف
        header.resizeSection(4, 180)  # البريد الإلكتروني
        header.resizeSection(5, 100)  # المدينة
        header.resizeSection(6, 120)  # حد الائتمان
        header.setStretchLastSection(True)  # الحالة

        # ربط النقر المزدوج
        self.customers_table.itemDoubleClicked.connect(self.edit_customer)

        main_layout.addWidget(self.customers_table)

        # شريط الحالة
        status_layout = QHBoxLayout()
        self.customers_count_label = QLabel("عدد العملاء: 0")
        self.active_customers_label = QLabel("النشطين: 0")
        self.total_credit_label = QLabel("إجمالي حدود الائتمان: 0 ريال")

        status_layout.addWidget(self.customers_count_label)
        status_layout.addWidget(self.active_customers_label)
        status_layout.addStretch()
        status_layout.addWidget(self.total_credit_label)

        main_layout.addLayout(status_layout)

        self.setLayout(main_layout)
        self.setStyleSheet(self.get_module_stylesheet())

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.add_btn.clicked.connect(self.add_customer)
        self.edit_btn.clicked.connect(self.edit_customer)
        self.delete_btn.clicked.connect(self.delete_customer)
        self.refresh_btn.clicked.connect(self.load_customers)
        self.export_btn.clicked.connect(self.export_customers)

        self.search_edit.textChanged.connect(self.filter_customers)
        self.type_filter.currentTextChanged.connect(self.filter_customers)
        self.status_filter.currentTextChanged.connect(self.filter_customers)

    def load_customers(self):
        """تحميل العملاء"""
        try:
            query = """
                SELECT id, customer_code, customer_name, customer_type, phone,
                       email, city, credit_limit, is_active
                FROM customers
                ORDER BY customer_name
            """

            customers = self.db_manager.execute_query(query)
            self.customers_table.setRowCount(len(customers))

            total_credit = 0
            active_count = 0

            for row, customer in enumerate(customers):
                # الكود
                code_item = QTableWidgetItem(customer.get('customer_code', ''))
                code_item.setData(Qt.UserRole, customer.get('id'))
                self.customers_table.setItem(row, 0, code_item)

                # اسم العميل
                self.customers_table.setItem(row, 1, QTableWidgetItem(customer.get('customer_name', '')))

                # النوع
                customer_type = customer.get('customer_type', 'individual')
                type_text = "شركة" if customer_type == "company" else "فرد"
                self.customers_table.setItem(row, 2, QTableWidgetItem(type_text))

                # الهاتف
                self.customers_table.setItem(row, 3, QTableWidgetItem(customer.get('phone', '')))

                # البريد الإلكتروني
                self.customers_table.setItem(row, 4, QTableWidgetItem(customer.get('email', '')))

                # المدينة
                self.customers_table.setItem(row, 5, QTableWidgetItem(customer.get('city', '')))

                # حد الائتمان
                credit_limit = float(customer.get('credit_limit', 0))
                credit_text = f"{credit_limit:,.2f}"
                self.customers_table.setItem(row, 6, QTableWidgetItem(credit_text))
                total_credit += credit_limit

                # الحالة
                is_active = customer.get('is_active', True)
                status_text = "نشط" if is_active else "غير نشط"
                status_item = QTableWidgetItem(status_text)

                if is_active:
                    status_item.setBackground(QColor("#d4edda"))
                    active_count += 1
                else:
                    status_item.setBackground(QColor("#f8d7da"))

                self.customers_table.setItem(row, 7, status_item)

            # تحديث الإحصائيات
            self.customers_count_label.setText(f"عدد العملاء: {len(customers)}")
            self.active_customers_label.setText(f"النشطين: {active_count}")
            self.total_credit_label.setText(f"إجمالي حدود الائتمان: {total_credit:,.2f} ريال")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل العملاء:\n{str(e)}")

    def add_customer(self):
        """إضافة عميل جديد"""
        dialog = CustomerDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_customers()

    def edit_customer(self):
        """تعديل العميل المحدد"""
        current_row = self.customers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للتعديل")
            return

        try:
            # الحصول على معرف العميل
            customer_id = self.customers_table.item(current_row, 0).data(Qt.UserRole)

            # استعلام البيانات الكاملة
            query = "SELECT * FROM customers WHERE id = ?"
            result = self.db_manager.execute_query(query, (customer_id,))

            if result:
                customer_data = result[0]
                dialog = CustomerDialog(self.db_manager, customer_data, self)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_customers()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات العميل:\n{str(e)}")

    def delete_customer(self):
        """حذف العميل المحدد"""
        current_row = self.customers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للحذف")
            return

        customer_name = self.customers_table.item(current_row, 1).text()

        reply = QMessageBox.question(
            self, 'تأكيد الحذف',
            f'هل أنت متأكد من حذف العميل "{customer_name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                customer_id = self.customers_table.item(current_row, 0).data(Qt.UserRole)

                # التحقق من وجود فواتير للعميل
                check_query = "SELECT COUNT(*) as count FROM sales_invoices WHERE customer_id = ?"
                check_result = self.db_manager.execute_query(check_query, (customer_id,))

                if check_result and check_result[0]['count'] > 0:
                    QMessageBox.warning(
                        self, "تحذير",
                        "لا يمكن حذف هذا العميل لأنه يحتوي على فواتير مبيعات.\n\nيمكنك إلغاء تفعيله بدلاً من الحذف."
                    )
                    return

                # حذف العميل
                delete_query = "DELETE FROM customers WHERE id = ?"
                self.db_manager.execute_update(delete_query, (customer_id,))

                QMessageBox.information(self, "نجح", "تم حذف العميل بنجاح")
                self.load_customers()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف العميل:\n{str(e)}")

    def filter_customers(self):
        """فلترة العملاء"""
        search_text = self.search_edit.text().lower()
        type_filter = self.type_filter.currentText()
        status_filter = self.status_filter.currentText()

        for row in range(self.customers_table.rowCount()):
            show_row = True

            # فلترة النص
            if search_text:
                row_text = ""
                for col in range(self.customers_table.columnCount()):
                    item = self.customers_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                if search_text not in row_text:
                    show_row = False

            # فلترة النوع
            if type_filter != "الكل":
                type_item = self.customers_table.item(row, 2)
                if type_item:
                    if type_filter == "أفراد" and type_item.text() != "فرد":
                        show_row = False
                    elif type_filter == "شركات" and type_item.text() != "شركة":
                        show_row = False

            # فلترة الحالة
            if status_filter != "الكل":
                status_item = self.customers_table.item(row, 7)
                if status_item:
                    if status_filter == "نشط" and status_item.text() != "نشط":
                        show_row = False
                    elif status_filter == "غير نشط" and status_item.text() != "غير نشط":
                        show_row = False

            self.customers_table.setRowHidden(row, not show_row)

    def export_customers(self):
        """تصدير العملاء"""
        QMessageBox.information(self, "تصدير العملاء", "وظيفة تصدير العملاء قيد التطوير")

    def get_module_stylesheet(self):
        """تنسيق الوحدة"""
        return """
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLineEdit, QComboBox {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #007bff;
                outline: none;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """
