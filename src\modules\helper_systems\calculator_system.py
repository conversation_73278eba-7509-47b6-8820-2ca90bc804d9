#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الآلة الحاسبة المتقدمة
Advanced Calculator System
"""

import sys
import math
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QPushButton, QGroupBox, 
                             QTextEdit, QTabWidget, QWidget, QComboBox,
                             QApplication, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class CalculatorDialog(QDialog):
    """حوار الآلة الحاسبة المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_input = ""
        self.result = 0
        self.operation = ""
        self.history = []
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🧮 الآلة الحاسبة المتقدمة")
        self.setGeometry(200, 200, 500, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("🧮 الآلة الحاسبة المتقدمة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات الحاسبة
        self.tabs = QTabWidget()
        
        # تبويب الحاسبة الأساسية
        basic_tab = self.create_basic_calculator()
        self.tabs.addTab(basic_tab, "🧮 أساسية")
        
        # تبويب الحاسبة العلمية
        scientific_tab = self.create_scientific_calculator()
        self.tabs.addTab(scientific_tab, "🔬 علمية")
        
        # تبويب محول الوحدات
        converter_tab = self.create_unit_converter()
        self.tabs.addTab(converter_tab, "🔄 محول الوحدات")
        
        # تبويب التاريخ
        history_tab = self.create_history_tab()
        self.tabs.addTab(history_tab, "📜 التاريخ")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_basic_calculator(self):
        """إنشاء الحاسبة الأساسية"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # شاشة العرض
        self.display = QLineEdit()
        self.display.setReadOnly(True)
        self.display.setAlignment(Qt.AlignRight)
        self.display.setStyleSheet("""
            QLineEdit {
                font-size: 24px;
                font-weight: bold;
                padding: 15px;
                border: 2px solid #3498db;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)
        self.display.setText("0")
        
        # أزرار الحاسبة
        buttons_layout = QGridLayout()
        
        # تعريف الأزرار
        buttons = [
            ['C', '±', '%', '÷'],
            ['7', '8', '9', '×'],
            ['4', '5', '6', '-'],
            ['1', '2', '3', '+'],
            ['0', '.', '=', '=']
        ]
        
        # إنشاء الأزرار
        for row, button_row in enumerate(buttons):
            for col, button_text in enumerate(button_row):
                if button_text == '=':
                    if col == 2:  # زر = الأول
                        continue
                    else:  # زر = الثاني (يمتد عبر عمودين)
                        btn = QPushButton(button_text)
                        buttons_layout.addWidget(btn, row, col, 1, 2)
                else:
                    btn = QPushButton(button_text)
                    buttons_layout.addWidget(btn, row, col)
                
                # تنسيق الأزرار
                if button_text in ['C', '±', '%']:
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #95a5a6;
                            color: white;
                            font-size: 18px;
                            font-weight: bold;
                            padding: 15px;
                            border-radius: 8px;
                        }
                        QPushButton:hover {
                            background-color: #7f8c8d;
                        }
                    """)
                elif button_text in ['÷', '×', '-', '+', '=']:
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #e74c3c;
                            color: white;
                            font-size: 18px;
                            font-weight: bold;
                            padding: 15px;
                            border-radius: 8px;
                        }
                        QPushButton:hover {
                            background-color: #c0392b;
                        }
                    """)
                else:
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #34495e;
                            color: white;
                            font-size: 18px;
                            font-weight: bold;
                            padding: 15px;
                            border-radius: 8px;
                        }
                        QPushButton:hover {
                            background-color: #2c3e50;
                        }
                    """)
                
                # ربط الأزرار بالوظائف
                btn.clicked.connect(lambda checked, text=button_text: self.button_clicked(text))
        
        layout.addWidget(self.display)
        layout.addLayout(buttons_layout)
        
        tab.setLayout(layout)
        return tab
        
    def create_scientific_calculator(self):
        """إنشاء الحاسبة العلمية"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # شاشة العرض العلمية
        self.scientific_display = QLineEdit()
        self.scientific_display.setReadOnly(True)
        self.scientific_display.setAlignment(Qt.AlignRight)
        self.scientific_display.setStyleSheet("""
            QLineEdit {
                font-size: 20px;
                font-weight: bold;
                padding: 10px;
                border: 2px solid #9b59b6;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)
        self.scientific_display.setText("0")
        
        # أزرار الحاسبة العلمية
        scientific_buttons_layout = QGridLayout()
        
        # تعريف الأزرار العلمية
        scientific_buttons = [
            ['sin', 'cos', 'tan', 'log', 'ln'],
            ['x²', 'x³', 'xʸ', '√', '∛'],
            ['π', 'e', '(', ')', 'C'],
            ['7', '8', '9', '÷', 'AC'],
            ['4', '5', '6', '×', 'M+'],
            ['1', '2', '3', '-', 'M-'],
            ['0', '.', '±', '+', '=']
        ]
        
        # إنشاء الأزرار العلمية
        for row, button_row in enumerate(scientific_buttons):
            for col, button_text in enumerate(button_row):
                btn = QPushButton(button_text)
                scientific_buttons_layout.addWidget(btn, row, col)
                
                # تنسيق الأزرار العلمية
                if button_text in ['sin', 'cos', 'tan', 'log', 'ln', 'x²', 'x³', 'xʸ', '√', '∛', 'π', 'e']:
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #9b59b6;
                            color: white;
                            font-size: 14px;
                            font-weight: bold;
                            padding: 10px;
                            border-radius: 6px;
                        }
                        QPushButton:hover {
                            background-color: #8e44ad;
                        }
                    """)
                elif button_text in ['C', 'AC', 'M+', 'M-']:
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #95a5a6;
                            color: white;
                            font-size: 14px;
                            font-weight: bold;
                            padding: 10px;
                            border-radius: 6px;
                        }
                        QPushButton:hover {
                            background-color: #7f8c8d;
                        }
                    """)
                elif button_text in ['÷', '×', '-', '+', '=']:
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #e74c3c;
                            color: white;
                            font-size: 14px;
                            font-weight: bold;
                            padding: 10px;
                            border-radius: 6px;
                        }
                        QPushButton:hover {
                            background-color: #c0392b;
                        }
                    """)
                else:
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #34495e;
                            color: white;
                            font-size: 14px;
                            font-weight: bold;
                            padding: 10px;
                            border-radius: 6px;
                        }
                        QPushButton:hover {
                            background-color: #2c3e50;
                        }
                    """)
                
                # ربط الأزرار بالوظائف
                btn.clicked.connect(lambda checked, text=button_text: self.scientific_button_clicked(text))
        
        layout.addWidget(self.scientific_display)
        layout.addLayout(scientific_buttons_layout)
        
        tab.setLayout(layout)
        return tab

    def create_unit_converter(self):
        """إنشاء محول الوحدات"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة محول الوحدات
        converter_group = QGroupBox("🔄 محول الوحدات")
        converter_layout = QGridLayout()

        # نوع التحويل
        converter_layout.addWidget(QLabel("نوع التحويل:"), 0, 0)
        self.conversion_type = QComboBox()
        self.conversion_type.addItems([
            "الطول", "الوزن", "المساحة", "الحجم", "درجة الحرارة", "السرعة"
        ])
        converter_layout.addWidget(self.conversion_type, 0, 1)

        # من وحدة
        converter_layout.addWidget(QLabel("من:"), 1, 0)
        self.from_unit = QComboBox()
        converter_layout.addWidget(self.from_unit, 1, 1)

        # إلى وحدة
        converter_layout.addWidget(QLabel("إلى:"), 2, 0)
        self.to_unit = QComboBox()
        converter_layout.addWidget(self.to_unit, 2, 1)

        # القيمة المدخلة
        converter_layout.addWidget(QLabel("القيمة:"), 3, 0)
        self.input_value = QLineEdit()
        self.input_value.setPlaceholderText("أدخل القيمة للتحويل")
        converter_layout.addWidget(self.input_value, 3, 1)

        # النتيجة
        converter_layout.addWidget(QLabel("النتيجة:"), 4, 0)
        self.conversion_result = QLineEdit()
        self.conversion_result.setReadOnly(True)
        self.conversion_result.setStyleSheet("""
            QLineEdit {
                background-color: #f8f9fa;
                border: 2px solid #27ae60;
                border-radius: 5px;
                padding: 8px;
                font-weight: bold;
            }
        """)
        converter_layout.addWidget(self.conversion_result, 4, 1)

        # زر التحويل
        convert_btn = QPushButton("🔄 تحويل")
        convert_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        convert_btn.clicked.connect(self.convert_units)
        converter_layout.addWidget(convert_btn, 5, 0, 1, 2)

        converter_group.setLayout(converter_layout)

        # تحديث الوحدات عند تغيير النوع
        self.conversion_type.currentTextChanged.connect(self.update_units)
        self.update_units()

        layout.addWidget(converter_group)
        layout.addStretch()

        tab.setLayout(layout)
        return tab

    def create_history_tab(self):
        """إنشاء تبويب التاريخ"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة التاريخ
        history_group = QGroupBox("📜 تاريخ العمليات")
        history_layout = QVBoxLayout()

        # منطقة عرض التاريخ
        self.history_display = QTextEdit()
        self.history_display.setReadOnly(True)
        self.history_display.setPlaceholderText("سيظهر تاريخ العمليات الحسابية هنا...")
        self.history_display.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 2px solid #3498db;
                border-radius: 8px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        """)

        # أزرار التحكم في التاريخ
        history_buttons = QHBoxLayout()

        clear_history_btn = QPushButton("🗑️ مسح التاريخ")
        clear_history_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        clear_history_btn.clicked.connect(self.clear_history)

        save_history_btn = QPushButton("💾 حفظ التاريخ")
        save_history_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 8px 15px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_history_btn.clicked.connect(self.save_history)

        history_buttons.addWidget(clear_history_btn)
        history_buttons.addWidget(save_history_btn)
        history_buttons.addStretch()

        history_layout.addWidget(self.history_display)
        history_layout.addLayout(history_buttons)
        history_group.setLayout(history_layout)

        layout.addWidget(history_group)

        tab.setLayout(layout)
        return tab

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        # زر المساعدة
        help_btn = QPushButton("❓ المساعدة")
        help_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        help_btn.clicked.connect(self.show_help)

        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        close_btn.clicked.connect(self.reject)

        layout.addWidget(help_btn)
        layout.addStretch()
        layout.addWidget(close_btn)

        return layout

    # ربط الوظائف من الملف المنفصل
    def button_clicked(self, text):
        """معالجة النقر على أزرار الحاسبة الأساسية"""
        from .calculator_functions import button_clicked_function
        button_clicked_function(self, text)

    def scientific_button_clicked(self, text):
        """معالجة النقر على أزرار الحاسبة العلمية"""
        from .calculator_functions import scientific_button_clicked_function
        scientific_button_clicked_function(self, text)

    def calculate_result(self, first, second, operation):
        """حساب نتيجة العملية"""
        from .calculator_functions import calculate_result_function
        return calculate_result_function(self, first, second, operation)

    def update_units(self):
        """تحديث قوائم الوحدات"""
        from .calculator_functions import update_units_function
        update_units_function(self)

    def convert_units(self):
        """تحويل الوحدات"""
        from .calculator_functions import convert_units_function
        convert_units_function(self)

    def convert_temperature(self, value, from_unit, to_unit):
        """تحويل درجات الحرارة"""
        from .calculator_functions import convert_temperature_function
        return convert_temperature_function(self, value, from_unit, to_unit)

    def add_to_history(self, operation):
        """إضافة عملية إلى التاريخ"""
        from .calculator_functions import add_to_history_function
        add_to_history_function(self, operation)

    def clear_history(self):
        """مسح التاريخ"""
        from .calculator_functions import clear_history_function
        clear_history_function(self)

    def save_history(self):
        """حفظ التاريخ"""
        from .calculator_functions import save_history_function
        save_history_function(self)

    def show_help(self):
        """عرض المساعدة"""
        from .calculator_functions import show_help_function
        show_help_function(self)


def main():
    """اختبار الآلة الحاسبة"""
    import sys

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    dialog = CalculatorDialog()
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
