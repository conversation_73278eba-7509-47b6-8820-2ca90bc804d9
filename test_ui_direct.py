#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر لواجهة المستخدم
Direct UI Test
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ui():
    """اختبار واجهة المستخدم مباشرة"""
    try:
        print("Starting UI test...")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("Onyx ERP Test")
        app.setLayoutDirection(Qt.RightToLeft)
        
        # محاولة استيراد واجهة تسجيل الدخول
        try:
            from src.ui.onyx_login_window import OnyxLoginWindow
            print("Successfully imported OnyxLoginWindow")
        except ImportError as e:
            print(f"Failed to import OnyxLoginWindow: {e}")
            QMessageBox.critical(None, "Import Error", f"Failed to import login window: {e}")
            return 1
        
        # إنشاء نافذة تسجيل الدخول
        try:
            login_window = OnyxLoginWindow()
            print("Successfully created login window")
        except Exception as e:
            print(f"Failed to create login window: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(None, "Creation Error", f"Failed to create login window: {e}")
            return 1
        
        # إظهار النافذة
        try:
            login_window.show()
            login_window.raise_()
            login_window.activateWindow()
            print("Login window should now be visible")
        except Exception as e:
            print(f"Failed to show login window: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(None, "Display Error", f"Failed to show login window: {e}")
            return 1
        
        # تشغيل التطبيق
        print("Starting application event loop...")
        return app.exec_()
        
    except Exception as e:
        print(f"General error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = test_ui()
    print(f"Application exited with code: {exit_code}")
    sys.exit(exit_code)
