#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة إعداد قواعد النظام
System Rules Setup Screen
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QCheckBox, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QFrame, QMessageBox, 
                             QTabWidget, QWidget, QTextEdit, QButtonGroup)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SystemRulesDialog(QDialog):
    """شاشة إعداد قواعد النظام"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إعداد قواعد النظام - تفعيل وتعطيل خصائص النظام")
        self.setGeometry(200, 100, 900, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان الشاشة
        title_label = QLabel("🔧 إعداد قواعد النظام")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات القواعد
        tabs = QTabWidget()
        
        # تبويب القواعد المحاسبية
        accounting_tab = self.create_accounting_rules_tab()
        tabs.addTab(accounting_tab, "📊 القواعد المحاسبية")
        
        # تبويب قواعد المخزون
        inventory_tab = self.create_inventory_rules_tab()
        tabs.addTab(inventory_tab, "📦 قواعد المخزون")
        
        # تبويب قواعد المبيعات
        sales_tab = self.create_sales_rules_tab()
        tabs.addTab(sales_tab, "🛒 قواعد المبيعات")
        
        # تبويب قواعد الأمان
        security_tab = self.create_security_rules_tab()
        tabs.addTab(security_tab, "🔒 قواعد الأمان")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_accounting_rules_tab(self):
        """إنشاء تبويب القواعد المحاسبية"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة الترحيل التلقائي
        posting_group = QGroupBox("📝 الترحيل التلقائي")
        posting_layout = QVBoxLayout()
        
        # تفعيل الترحيل التلقائي
        self.auto_posting = QCheckBox("تفعيل الترحيل التلقائي للقيود")
        self.auto_posting.setChecked(True)
        posting_layout.addWidget(self.auto_posting)
        
        # ترحيل فواتير المبيعات تلقائياً
        self.auto_post_sales = QCheckBox("ترحيل فواتير المبيعات تلقائياً")
        self.auto_post_sales.setChecked(True)
        posting_layout.addWidget(self.auto_post_sales)
        
        # ترحيل فواتير المشتريات تلقائياً
        self.auto_post_purchases = QCheckBox("ترحيل فواتير المشتريات تلقائياً")
        self.auto_post_purchases.setChecked(True)
        posting_layout.addWidget(self.auto_post_purchases)
        
        # ترحيل سندات القبض والصرف تلقائياً
        self.auto_post_vouchers = QCheckBox("ترحيل سندات القبض والصرف تلقائياً")
        self.auto_post_vouchers.setChecked(True)
        posting_layout.addWidget(self.auto_post_vouchers)
        
        posting_group.setLayout(posting_layout)
        
        # مجموعة التحقق من الأرصدة
        balance_group = QGroupBox("⚖️ التحقق من الأرصدة")
        balance_layout = QVBoxLayout()
        
        # التحقق من رصيد العميل
        self.check_customer_balance = QCheckBox("التحقق من رصيد العميل قبل البيع")
        self.check_customer_balance.setChecked(False)
        balance_layout.addWidget(self.check_customer_balance)
        
        # التحقق من الحد الائتماني
        self.check_credit_limit = QCheckBox("التحقق من الحد الائتماني للعميل")
        self.check_credit_limit.setChecked(True)
        balance_layout.addWidget(self.check_credit_limit)
        
        # التحقق من رصيد الصندوق
        self.check_cash_balance = QCheckBox("التحقق من رصيد الصندوق قبل الصرف")
        self.check_cash_balance.setChecked(True)
        balance_layout.addWidget(self.check_cash_balance)
        
        balance_group.setLayout(balance_layout)
        
        # مجموعة إعدادات القيود
        entries_group = QGroupBox("📋 إعدادات القيود")
        entries_layout = QGridLayout()
        
        # السماح بقيود غير متوازنة
        entries_layout.addWidget(QLabel("السماح بقيود غير متوازنة:"), 0, 0)
        self.allow_unbalanced = QCheckBox()
        self.allow_unbalanced.setChecked(False)
        entries_layout.addWidget(self.allow_unbalanced, 0, 1)
        
        # الحد الأقصى لفرق التوازن
        entries_layout.addWidget(QLabel("الحد الأقصى لفرق التوازن:"), 1, 0)
        self.max_balance_diff = QDoubleSpinBox()
        self.max_balance_diff.setRange(0.0, 1000.0)
        self.max_balance_diff.setValue(0.01)
        self.max_balance_diff.setSuffix(" ر.ي")
        entries_layout.addWidget(self.max_balance_diff, 1, 1)
        
        entries_group.setLayout(entries_layout)
        
        layout.addWidget(posting_group)
        layout.addWidget(balance_group)
        layout.addWidget(entries_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab
        
    def create_inventory_rules_tab(self):
        """إنشاء تبويب قواعد المخزون"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة التحقق من المخزون
        stock_group = QGroupBox("📦 التحقق من المخزون")
        stock_layout = QVBoxLayout()
        
        # التحقق من المخزون قبل البيع
        self.check_stock_before_sale = QCheckBox("التحقق من المخزون قبل البيع")
        self.check_stock_before_sale.setChecked(True)
        stock_layout.addWidget(self.check_stock_before_sale)
        
        # السماح ببيع بالسالب
        self.allow_negative_stock = QCheckBox("السماح ببيع بالسالب (مخزون سالب)")
        self.allow_negative_stock.setChecked(False)
        stock_layout.addWidget(self.allow_negative_stock)
        
        # تحديث المخزون تلقائياً
        self.auto_update_stock = QCheckBox("تحديث المخزون تلقائياً عند البيع/الشراء")
        self.auto_update_stock.setChecked(True)
        stock_layout.addWidget(self.auto_update_stock)
        
        # تنبيه عند نفاد المخزون
        self.alert_out_of_stock = QCheckBox("تنبيه عند نفاد المخزون")
        self.alert_out_of_stock.setChecked(True)
        stock_layout.addWidget(self.alert_out_of_stock)
        
        stock_group.setLayout(stock_layout)
        
        # مجموعة طرق التقييم
        valuation_group = QGroupBox("💰 طرق تقييم المخزون")
        valuation_layout = QGridLayout()
        
        # طريقة التقييم
        valuation_layout.addWidget(QLabel("طريقة تقييم المخزون:"), 0, 0)
        self.valuation_method = QComboBox()
        self.valuation_method.addItems([
            "المتوسط المرجح", "الوارد أولاً صادر أولاً (FIFO)", 
            "الوارد أخيراً صادر أولاً (LIFO)", "التكلفة المحددة"
        ])
        valuation_layout.addWidget(self.valuation_method, 0, 1)
        
        # إعادة حساب التكلفة
        valuation_layout.addWidget(QLabel("إعادة حساب التكلفة:"), 1, 0)
        self.recalculate_cost = QComboBox()
        self.recalculate_cost.addItems(["تلقائي", "يدوي", "شهري"])
        valuation_layout.addWidget(self.recalculate_cost, 1, 1)
        
        valuation_group.setLayout(valuation_layout)
        
        # مجموعة الحدود والتنبيهات
        limits_group = QGroupBox("⚠️ الحدود والتنبيهات")
        limits_layout = QGridLayout()
        
        # الحد الأدنى للمخزون
        limits_layout.addWidget(QLabel("تفعيل تنبيه الحد الأدنى:"), 0, 0)
        self.enable_min_stock_alert = QCheckBox()
        self.enable_min_stock_alert.setChecked(True)
        limits_layout.addWidget(self.enable_min_stock_alert, 0, 1)
        
        # الحد الأقصى للمخزون
        limits_layout.addWidget(QLabel("تفعيل تنبيه الحد الأقصى:"), 1, 0)
        self.enable_max_stock_alert = QCheckBox()
        self.enable_max_stock_alert.setChecked(False)
        limits_layout.addWidget(self.enable_max_stock_alert, 1, 1)
        
        limits_group.setLayout(limits_layout)
        
        layout.addWidget(stock_group)
        layout.addWidget(valuation_group)
        layout.addWidget(limits_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab
        
    def create_sales_rules_tab(self):
        """إنشاء تبويب قواعد المبيعات"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة قواعد التسعير
        pricing_group = QGroupBox("💲 قواعد التسعير")
        pricing_layout = QVBoxLayout()
        
        # السماح ببيع بأقل من سعر التكلفة
        self.allow_below_cost = QCheckBox("السماح ببيع بأقل من سعر التكلفة")
        self.allow_below_cost.setChecked(False)
        pricing_layout.addWidget(self.allow_below_cost)
        
        # السماح بتعديل الأسعار في الفاتورة
        self.allow_price_edit = QCheckBox("السماح بتعديل الأسعار في الفاتورة")
        self.allow_price_edit.setChecked(True)
        pricing_layout.addWidget(self.allow_price_edit)
        
        # تطبيق خصم تلقائي
        self.auto_discount = QCheckBox("تطبيق خصم تلقائي حسب نوع العميل")
        self.auto_discount.setChecked(False)
        pricing_layout.addWidget(self.auto_discount)
        
        # حساب الضريبة تلقائياً
        self.auto_calculate_tax = QCheckBox("حساب الضريبة تلقائياً")
        self.auto_calculate_tax.setChecked(True)
        pricing_layout.addWidget(self.auto_calculate_tax)
        
        pricing_group.setLayout(pricing_layout)
        
        # مجموعة قواعد الخصم
        discount_group = QGroupBox("🎯 قواعد الخصم")
        discount_layout = QGridLayout()
        
        # الحد الأقصى للخصم
        discount_layout.addWidget(QLabel("الحد الأقصى للخصم (%):"), 0, 0)
        self.max_discount = QDoubleSpinBox()
        self.max_discount.setRange(0.0, 100.0)
        self.max_discount.setValue(20.0)
        self.max_discount.setSuffix("%")
        discount_layout.addWidget(self.max_discount, 0, 1)
        
        # طلب تأكيد للخصم الكبير
        discount_layout.addWidget(QLabel("طلب تأكيد للخصم أكبر من (%):"), 1, 0)
        self.confirm_discount_above = QDoubleSpinBox()
        self.confirm_discount_above.setRange(0.0, 100.0)
        self.confirm_discount_above.setValue(10.0)
        self.confirm_discount_above.setSuffix("%")
        discount_layout.addWidget(self.confirm_discount_above, 1, 1)
        
        discount_group.setLayout(discount_layout)
        
        # مجموعة قواعد الدفع
        payment_group = QGroupBox("💳 قواعد الدفع")
        payment_layout = QVBoxLayout()
        
        # السماح بالبيع الآجل
        self.allow_credit_sales = QCheckBox("السماح بالبيع الآجل")
        self.allow_credit_sales.setChecked(True)
        payment_layout.addWidget(self.allow_credit_sales)
        
        # طلب دفعة مقدمة
        self.require_advance = QCheckBox("طلب دفعة مقدمة للبيع الآجل")
        self.require_advance.setChecked(False)
        payment_layout.addWidget(self.require_advance)
        
        # السماح بالدفع الجزئي
        self.allow_partial_payment = QCheckBox("السماح بالدفع الجزئي")
        self.allow_partial_payment.setChecked(True)
        payment_layout.addWidget(self.allow_partial_payment)
        
        payment_group.setLayout(payment_layout)
        
        layout.addWidget(pricing_group)
        layout.addWidget(discount_group)
        layout.addWidget(payment_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab
        
    def create_security_rules_tab(self):
        """إنشاء تبويب قواعد الأمان"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة قواعد كلمة المرور
        password_group = QGroupBox("🔐 قواعد كلمة المرور")
        password_layout = QGridLayout()
        
        # الحد الأدنى لطول كلمة المرور
        password_layout.addWidget(QLabel("الحد الأدنى لطول كلمة المرور:"), 0, 0)
        self.min_password_length = QSpinBox()
        self.min_password_length.setRange(4, 20)
        self.min_password_length.setValue(6)
        password_layout.addWidget(self.min_password_length, 0, 1)
        
        # طلب أحرف كبيرة وصغيرة
        password_layout.addWidget(QLabel("طلب أحرف كبيرة وصغيرة:"), 1, 0)
        self.require_mixed_case = QCheckBox()
        self.require_mixed_case.setChecked(False)
        password_layout.addWidget(self.require_mixed_case, 1, 1)
        
        # طلب أرقام في كلمة المرور
        password_layout.addWidget(QLabel("طلب أرقام في كلمة المرور:"), 2, 0)
        self.require_numbers = QCheckBox()
        self.require_numbers.setChecked(True)
        password_layout.addWidget(self.require_numbers, 2, 1)
        
        password_group.setLayout(password_layout)
        
        # مجموعة قواعد الجلسة
        session_group = QGroupBox("⏰ قواعد الجلسة")
        session_layout = QGridLayout()
        
        # مدة انتهاء الجلسة (بالدقائق)
        session_layout.addWidget(QLabel("مدة انتهاء الجلسة (دقيقة):"), 0, 0)
        self.session_timeout = QSpinBox()
        self.session_timeout.setRange(5, 480)
        self.session_timeout.setValue(60)
        session_layout.addWidget(self.session_timeout, 0, 1)
        
        # الحد الأقصى لمحاولات الدخول الخاطئة
        session_layout.addWidget(QLabel("الحد الأقصى لمحاولات الدخول الخاطئة:"), 1, 0)
        self.max_login_attempts = QSpinBox()
        self.max_login_attempts.setRange(3, 10)
        self.max_login_attempts.setValue(5)
        session_layout.addWidget(self.max_login_attempts, 1, 1)
        
        # قفل الحساب بعد المحاولات الفاشلة
        session_layout.addWidget(QLabel("قفل الحساب بعد المحاولات الفاشلة:"), 2, 0)
        self.lock_account = QCheckBox()
        self.lock_account.setChecked(True)
        session_layout.addWidget(self.lock_account, 2, 1)
        
        session_group.setLayout(session_layout)
        
        # مجموعة قواعد التدقيق
        audit_group = QGroupBox("📝 قواعد التدقيق")
        audit_layout = QVBoxLayout()
        
        # تسجيل جميع العمليات
        self.log_all_operations = QCheckBox("تسجيل جميع العمليات في سجل التدقيق")
        self.log_all_operations.setChecked(True)
        audit_layout.addWidget(self.log_all_operations)
        
        # تسجيل محاولات الدخول
        self.log_login_attempts = QCheckBox("تسجيل محاولات الدخول (ناجحة وفاشلة)")
        self.log_login_attempts.setChecked(True)
        audit_layout.addWidget(self.log_login_attempts)
        
        # تسجيل التعديلات على البيانات الحساسة
        self.log_sensitive_changes = QCheckBox("تسجيل التعديلات على البيانات الحساسة")
        self.log_sensitive_changes.setChecked(True)
        audit_layout.addWidget(self.log_sensitive_changes)
        
        audit_group.setLayout(audit_layout)
        
        layout.addWidget(password_group)
        layout.addWidget(session_group)
        layout.addWidget(audit_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab
        
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر الحفظ
        save_btn = QPushButton("💾 حفظ القواعد")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_rules)
        
        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        # زر الافتراضي
        default_btn = QPushButton("🔄 القواعد الافتراضية")
        default_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        default_btn.clicked.connect(self.load_defaults)
        
        layout.addStretch()
        layout.addWidget(default_btn)
        layout.addWidget(save_btn)
        layout.addWidget(cancel_btn)
        
        return layout
        
    def load_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            # هنا يتم تحميل البيانات من قاعدة البيانات
            pass
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            
    def load_defaults(self):
        """تحميل القواعد الافتراضية"""
        # القواعد المحاسبية
        self.auto_posting.setChecked(True)
        self.auto_post_sales.setChecked(True)
        self.auto_post_purchases.setChecked(True)
        self.auto_post_vouchers.setChecked(True)
        self.check_customer_balance.setChecked(False)
        self.check_credit_limit.setChecked(True)
        self.check_cash_balance.setChecked(True)
        self.allow_unbalanced.setChecked(False)
        self.max_balance_diff.setValue(0.01)
        
        # قواعد المخزون
        self.check_stock_before_sale.setChecked(True)
        self.allow_negative_stock.setChecked(False)
        self.auto_update_stock.setChecked(True)
        self.alert_out_of_stock.setChecked(True)
        self.valuation_method.setCurrentText("المتوسط المرجح")
        self.recalculate_cost.setCurrentText("تلقائي")
        self.enable_min_stock_alert.setChecked(True)
        self.enable_max_stock_alert.setChecked(False)
        
        # قواعد المبيعات
        self.allow_below_cost.setChecked(False)
        self.allow_price_edit.setChecked(True)
        self.auto_discount.setChecked(False)
        self.auto_calculate_tax.setChecked(True)
        self.max_discount.setValue(20.0)
        self.confirm_discount_above.setValue(10.0)
        self.allow_credit_sales.setChecked(True)
        self.require_advance.setChecked(False)
        self.allow_partial_payment.setChecked(True)
        
        # قواعد الأمان
        self.min_password_length.setValue(6)
        self.require_mixed_case.setChecked(False)
        self.require_numbers.setChecked(True)
        self.session_timeout.setValue(60)
        self.max_login_attempts.setValue(5)
        self.lock_account.setChecked(True)
        self.log_all_operations.setChecked(True)
        self.log_login_attempts.setChecked(True)
        self.log_sensitive_changes.setChecked(True)
        
        QMessageBox.information(self, "تم", "تم تحميل القواعد الافتراضية بنجاح")
        
    def save_rules(self):
        """حفظ قواعد النظام"""
        try:
            # جمع جميع القواعد
            rules_data = {
                # القواعد المحاسبية
                'auto_posting': self.auto_posting.isChecked(),
                'auto_post_sales': self.auto_post_sales.isChecked(),
                'auto_post_purchases': self.auto_post_purchases.isChecked(),
                'auto_post_vouchers': self.auto_post_vouchers.isChecked(),
                'check_customer_balance': self.check_customer_balance.isChecked(),
                'check_credit_limit': self.check_credit_limit.isChecked(),
                'check_cash_balance': self.check_cash_balance.isChecked(),
                'allow_unbalanced': self.allow_unbalanced.isChecked(),
                'max_balance_diff': self.max_balance_diff.value(),
                
                # قواعد المخزون
                'check_stock_before_sale': self.check_stock_before_sale.isChecked(),
                'allow_negative_stock': self.allow_negative_stock.isChecked(),
                'auto_update_stock': self.auto_update_stock.isChecked(),
                'alert_out_of_stock': self.alert_out_of_stock.isChecked(),
                'valuation_method': self.valuation_method.currentText(),
                'recalculate_cost': self.recalculate_cost.currentText(),
                'enable_min_stock_alert': self.enable_min_stock_alert.isChecked(),
                'enable_max_stock_alert': self.enable_max_stock_alert.isChecked(),
                
                # قواعد المبيعات
                'allow_below_cost': self.allow_below_cost.isChecked(),
                'allow_price_edit': self.allow_price_edit.isChecked(),
                'auto_discount': self.auto_discount.isChecked(),
                'auto_calculate_tax': self.auto_calculate_tax.isChecked(),
                'max_discount': self.max_discount.value(),
                'confirm_discount_above': self.confirm_discount_above.value(),
                'allow_credit_sales': self.allow_credit_sales.isChecked(),
                'require_advance': self.require_advance.isChecked(),
                'allow_partial_payment': self.allow_partial_payment.isChecked(),
                
                # قواعد الأمان
                'min_password_length': self.min_password_length.value(),
                'require_mixed_case': self.require_mixed_case.isChecked(),
                'require_numbers': self.require_numbers.isChecked(),
                'session_timeout': self.session_timeout.value(),
                'max_login_attempts': self.max_login_attempts.value(),
                'lock_account': self.lock_account.isChecked(),
                'log_all_operations': self.log_all_operations.isChecked(),
                'log_login_attempts': self.log_login_attempts.isChecked(),
                'log_sensitive_changes': self.log_sensitive_changes.isChecked()
            }
            
            # حفظ في قاعدة البيانات
            # هنا يتم حفظ البيانات في قاعدة البيانات
            
            # عرض ملخص القواعد المحفوظة
            summary = f"""✅ تم حفظ قواعد النظام بنجاح!

📊 القواعد المحاسبية:
   • الترحيل التلقائي: {'مفعل' if rules_data['auto_posting'] else 'معطل'}
   • التحقق من الحد الائتماني: {'مفعل' if rules_data['check_credit_limit'] else 'معطل'}

📦 قواعد المخزون:
   • التحقق من المخزون: {'مفعل' if rules_data['check_stock_before_sale'] else 'معطل'}
   • السماح بالسالب: {'مفعل' if rules_data['allow_negative_stock'] else 'معطل'}
   • طريقة التقييم: {rules_data['valuation_method']}

🛒 قواعد المبيعات:
   • الحد الأقصى للخصم: {rules_data['max_discount']}%
   • البيع الآجل: {'مفعل' if rules_data['allow_credit_sales'] else 'معطل'}

🔒 قواعد الأمان:
   • طول كلمة المرور: {rules_data['min_password_length']} أحرف
   • مدة الجلسة: {rules_data['session_timeout']} دقيقة"""
            
            QMessageBox.information(self, "نجح الحفظ", summary)
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ القواعد:\n{str(e)}")


def main():
    """اختبار الشاشة"""
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass
    
    dialog = SystemRulesDialog(MockDBManager())
    dialog.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
