#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة التحكم بواجهات الدخول
Login Interface Control Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QCheckBox, QTimeEdit,
                             QDateEdit, QSplitter, QFrame, QDialogButtonBox)
from PyQt5.QtCore import Qt, QDate, QTime, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPixmap, QPainter
from datetime import datetime, timedelta
import json

try:
    from ..database.sqlite_manager import SQLiteManager
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class LoginSessionDialog(QDialog):
    """حوار إدارة جلسة الدخول"""
    
    def __init__(self, db_manager, session_data=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.session_data = session_data
        self.is_edit_mode = session_data is not None
        self.setup_ui()
        
        if self.is_edit_mode:
            self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔐 إدارة جلسة الدخول")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        layout = QVBoxLayout()
        
        # العنوان
        title = "تعديل جلسة" if self.is_edit_mode else "جلسة جديدة"
        title_label = QLabel(f"🔐 {title}")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 18px;
                font-weight: bold;
                color: white;
                background-color: #2c3e50;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # معلومات الجلسة
        info_group = QGroupBox("معلومات الجلسة")
        info_layout = QFormLayout()
        
        self.user_combo = QComboBox()
        self.load_users()
        info_layout.addRow("المستخدم:", self.user_combo)
        
        self.session_type_combo = QComboBox()
        self.session_type_combo.addItems([
            "جلسة عادية",
            "جلسة إدارية",
            "جلسة محدودة",
            "جلسة مؤقتة"
        ])
        info_layout.addRow("نوع الجلسة:", self.session_type_combo)
        
        self.start_time_edit = QTimeEdit()
        self.start_time_edit.setTime(QTime.currentTime())
        info_layout.addRow("وقت البداية:", self.start_time_edit)
        
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(1, 1440)  # من دقيقة إلى 24 ساعة
        self.duration_spin.setValue(480)  # 8 ساعات افتراضي
        self.duration_spin.setSuffix(" دقيقة")
        info_layout.addRow("مدة الجلسة:", self.duration_spin)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # إعدادات الأمان
        security_group = QGroupBox("إعدادات الأمان")
        security_layout = QVBoxLayout()
        
        self.auto_logout_check = QCheckBox("تسجيل خروج تلقائي عند عدم النشاط")
        self.auto_logout_check.setChecked(True)
        security_layout.addWidget(self.auto_logout_check)
        
        self.force_password_check = QCheckBox("إجبار تغيير كلمة المرور")
        security_layout.addWidget(self.force_password_check)
        
        self.restrict_ip_check = QCheckBox("تقييد عنوان IP")
        security_layout.addWidget(self.restrict_ip_check)
        
        self.single_session_check = QCheckBox("جلسة واحدة فقط")
        self.single_session_check.setChecked(True)
        security_layout.addWidget(self.single_session_check)
        
        security_group.setLayout(security_layout)
        layout.addWidget(security_group)
        
        # ملاحظات
        notes_group = QGroupBox("ملاحظات")
        notes_layout = QVBoxLayout()
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات حول الجلسة...")
        notes_layout.addWidget(self.notes_edit)
        
        notes_group.setLayout(notes_layout)
        layout.addWidget(notes_group)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("💾 حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        self.save_btn.clicked.connect(self.save_session)
        self.cancel_btn.clicked.connect(self.reject)
        
        layout.addWidget(button_box)
        self.setLayout(layout)
    
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            query = "SELECT id, username, full_name FROM users WHERE is_active = 1"
            users = self.db_manager.execute_query(query)
            
            self.user_combo.clear()
            for user in users:
                user_id, username, full_name = user
                display_name = f"{full_name} ({username})" if full_name else username
                self.user_combo.addItem(display_name, user_id)
                
        except Exception as e:
            print(f"خطأ في تحميل المستخدمين: {e}")
    
    def load_data(self):
        """تحميل بيانات الجلسة للتعديل"""
        if self.session_data:
            # تحديد المستخدم
            user_id = self.session_data.get('user_id')
            for i in range(self.user_combo.count()):
                if self.user_combo.itemData(i) == user_id:
                    self.user_combo.setCurrentIndex(i)
                    break
            
            # باقي البيانات
            self.session_type_combo.setCurrentText(self.session_data.get('session_type', ''))
            self.notes_edit.setPlainText(self.session_data.get('notes', ''))
    
    def save_session(self):
        """حفظ الجلسة"""
        try:
            # التحقق من البيانات
            if self.user_combo.currentData() is None:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم")
                return
            
            # إنشاء جدول الجلسات إذا لم يكن موجوداً
            create_query = """
            CREATE TABLE IF NOT EXISTS login_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_type TEXT NOT NULL,
                start_time TIME,
                duration_minutes INTEGER,
                auto_logout BOOLEAN DEFAULT 1,
                force_password_change BOOLEAN DEFAULT 0,
                restrict_ip BOOLEAN DEFAULT 0,
                single_session BOOLEAN DEFAULT 1,
                notes TEXT,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            """
            self.db_manager.execute_update(create_query)
            
            # حفظ البيانات
            if self.is_edit_mode:
                # تحديث
                query = """
                UPDATE login_sessions SET
                    user_id = ?, session_type = ?, start_time = ?, duration_minutes = ?,
                    auto_logout = ?, force_password_change = ?, restrict_ip = ?, 
                    single_session = ?, notes = ?
                WHERE id = ?
                """
                params = (
                    self.user_combo.currentData(),
                    self.session_type_combo.currentText(),
                    self.start_time_edit.time().toString("HH:mm"),
                    self.duration_spin.value(),
                    self.auto_logout_check.isChecked(),
                    self.force_password_check.isChecked(),
                    self.restrict_ip_check.isChecked(),
                    self.single_session_check.isChecked(),
                    self.notes_edit.toPlainText(),
                    self.session_data['id']
                )
            else:
                # إدراج جديد
                query = """
                INSERT INTO login_sessions 
                (user_id, session_type, start_time, duration_minutes, auto_logout, 
                 force_password_change, restrict_ip, single_session, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (
                    self.user_combo.currentData(),
                    self.session_type_combo.currentText(),
                    self.start_time_edit.time().toString("HH:mm"),
                    self.duration_spin.value(),
                    self.auto_logout_check.isChecked(),
                    self.force_password_check.isChecked(),
                    self.restrict_ip_check.isChecked(),
                    self.single_session_check.isChecked(),
                    self.notes_edit.toPlainText()
                )
            
            self.db_manager.execute_update(query, params)
            
            QMessageBox.information(self, "تم", "تم حفظ الجلسة بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الجلسة:\n{str(e)}")


class LoginInterfaceControlWidget(QWidget):
    """وحدة التحكم بواجهات الدخول الرئيسية"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_sessions()
        self.setup_timer()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🔐 التحكم بواجهات الدخول")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 20px;
                font-weight: bold;
                color: white;
                background-color: #2c3e50;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب الجلسات النشطة
        active_sessions_tab = self.create_active_sessions_tab()
        self.tabs.addTab(active_sessions_tab, "🟢 الجلسات النشطة")
        
        # تبويب إدارة الجلسات
        sessions_management_tab = self.create_sessions_management_tab()
        self.tabs.addTab(sessions_management_tab, "⚙️ إدارة الجلسات")
        
        # تبويب إعدادات الأمان
        security_settings_tab = self.create_security_settings_tab()
        self.tabs.addTab(security_settings_tab, "🔒 إعدادات الأمان")
        
        # تبويب سجل الدخول
        login_log_tab = self.create_login_log_tab()
        self.tabs.addTab(login_log_tab, "📋 سجل الدخول")
        
        layout.addWidget(self.tabs)
        self.setLayout(layout)
    
    def create_active_sessions_tab(self):
        """إنشاء تبويب الجلسات النشطة"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # معلومات سريعة
        info_layout = QHBoxLayout()
        
        self.total_sessions_label = QLabel("إجمالي الجلسات: 0")
        self.total_sessions_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                background-color: #3498db;
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            
            }
        """)
        info_layout.addWidget(self.total_sessions_label)
        
        self.active_users_label = QLabel("المستخدمون النشطون: 0")
        self.active_users_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                background-color: #27ae60;
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            
            }
        """)
        info_layout.addWidget(self.active_users_label)
        
        self.expired_sessions_label = QLabel("الجلسات المنتهية: 0")
        self.expired_sessions_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                background-color: #e74c3c;
                color: white;
                padding: 10px;
                border-radius: 5px;
                font-weight: bold;
            
            }
        """)
        info_layout.addWidget(self.expired_sessions_label)
        
        info_layout.addStretch()
        layout.addLayout(info_layout)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_active_sessions)
        buttons_layout.addWidget(refresh_btn)
        
        terminate_btn = QPushButton("🚫 إنهاء الجلسة")
        terminate_btn.clicked.connect(self.terminate_session)
        terminate_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        buttons_layout.addWidget(terminate_btn)
        
        terminate_all_btn = QPushButton("🚫 إنهاء جميع الجلسات")
        terminate_all_btn.clicked.connect(self.terminate_all_sessions)
        buttons_layout.addWidget(terminate_all_btn)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
        
        # جدول الجلسات النشطة
        self.active_sessions_table = QTableWidget()
        self.active_sessions_table.setColumnCount(7)
        self.active_sessions_table.setHorizontalHeaderLabels([
            "المستخدم", "نوع الجلسة", "وقت البداية", "المدة المتبقية", 
            "عنوان IP", "الحالة", "آخر نشاط"
        ])
        
        header = self.active_sessions_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.active_sessions_table)
        
        widget.setLayout(layout)
        return widget
    
    def create_sessions_management_tab(self):
        """إنشاء تبويب إدارة الجلسات"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        new_session_btn = QPushButton("➕ جلسة جديدة")
        new_session_btn.clicked.connect(self.new_session)
        new_session_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        buttons_layout.addWidget(new_session_btn)
        
        edit_session_btn = QPushButton("✏️ تعديل")
        edit_session_btn.clicked.connect(self.edit_session)
        buttons_layout.addWidget(edit_session_btn)
        
        delete_session_btn = QPushButton("🗑️ حذف")
        delete_session_btn.clicked.connect(self.delete_session)
        buttons_layout.addWidget(delete_session_btn)
        
        buttons_layout.addStretch()
        
        refresh_sessions_btn = QPushButton("🔄 تحديث")
        refresh_sessions_btn.clicked.connect(self.load_sessions)
        buttons_layout.addWidget(refresh_sessions_btn)
        
        layout.addLayout(buttons_layout)
        
        # جدول إدارة الجلسات
        self.sessions_table = QTableWidget()
        self.sessions_table.setColumnCount(6)
        self.sessions_table.setHorizontalHeaderLabels([
            "المستخدم", "نوع الجلسة", "وقت البداية", "المدة", "الحالة", "ملاحظات"
        ])
        
        header = self.sessions_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.sessions_table)
        
        widget.setLayout(layout)
        return widget
    
    def create_security_settings_tab(self):
        """إنشاء تبويب إعدادات الأمان"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات عامة
        general_group = QGroupBox("الإعدادات العامة")
        general_layout = QFormLayout()
        
        self.max_sessions_spin = QSpinBox()
        self.max_sessions_spin.setRange(1, 100)
        self.max_sessions_spin.setValue(10)
        general_layout.addRow("الحد الأقصى للجلسات المتزامنة:", self.max_sessions_spin)
        
        self.session_timeout_spin = QSpinBox()
        self.session_timeout_spin.setRange(5, 1440)
        self.session_timeout_spin.setValue(30)
        self.session_timeout_spin.setSuffix(" دقيقة")
        general_layout.addRow("انتهاء الجلسة عند عدم النشاط:", self.session_timeout_spin)
        
        self.password_expiry_spin = QSpinBox()
        self.password_expiry_spin.setRange(1, 365)
        self.password_expiry_spin.setValue(90)
        self.password_expiry_spin.setSuffix(" يوم")
        general_layout.addRow("انتهاء صلاحية كلمة المرور:", self.password_expiry_spin)
        
        general_group.setLayout(general_layout)
        layout.addWidget(general_group)
        
        # إعدادات الأمان المتقدمة
        advanced_group = QGroupBox("الإعدادات المتقدمة")
        advanced_layout = QVBoxLayout()
        
        self.force_https_check = QCheckBox("إجبار استخدام HTTPS")
        advanced_layout.addWidget(self.force_https_check)
        
        self.two_factor_check = QCheckBox("تفعيل المصادقة الثنائية")
        advanced_layout.addWidget(self.two_factor_check)
        
        self.ip_whitelist_check = QCheckBox("تفعيل قائمة IP المسموحة")
        advanced_layout.addWidget(self.ip_whitelist_check)
        
        self.audit_login_check = QCheckBox("تسجيل جميع محاولات الدخول")
        self.audit_login_check.setChecked(True)
        advanced_layout.addWidget(self.audit_login_check)
        
        advanced_group.setLayout(advanced_layout)
        layout.addWidget(advanced_group)
        
        # أزرار الحفظ
        save_layout = QHBoxLayout()
        save_layout.addStretch()
        
        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        save_settings_btn.clicked.connect(self.save_security_settings)
        save_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        save_layout.addWidget(save_settings_btn)
        
        layout.addLayout(save_layout)
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget
    
    def create_login_log_tab(self):
        """إنشاء تبويب سجل الدخول"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # فلاتر البحث
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("من تاريخ:"))
        self.from_date_edit = QDateEdit()
        self.from_date_edit.setDate(QDate.currentDate().addDays(-7))
        self.from_date_edit.setCalendarPopup(True)
        filter_layout.addWidget(self.from_date_edit)
        
        filter_layout.addWidget(QLabel("إلى تاريخ:"))
        self.to_date_edit = QDateEdit()
        self.to_date_edit.setDate(QDate.currentDate())
        self.to_date_edit.setCalendarPopup(True)
        filter_layout.addWidget(self.to_date_edit)
        
        filter_layout.addWidget(QLabel("المستخدم:"))
        self.user_filter_combo = QComboBox()
        self.user_filter_combo.addItem("جميع المستخدمين", None)
        filter_layout.addWidget(self.user_filter_combo)
        
        search_btn = QPushButton("🔍 بحث")
        search_btn.clicked.connect(self.search_login_log)
        filter_layout.addWidget(search_btn)
        
        filter_layout.addStretch()
        layout.addLayout(filter_layout)
        
        # جدول سجل الدخول
        self.login_log_table = QTableWidget()
        self.login_log_table.setColumnCount(6)
        self.login_log_table.setHorizontalHeaderLabels([
            "المستخدم", "تاريخ ووقت الدخول", "عنوان IP", "نوع العملية", "الحالة", "ملاحظات"
        ])
        
        header = self.login_log_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        layout.addWidget(self.login_log_table)
        
        widget.setLayout(layout)
        return widget
    
    def setup_timer(self):
        """إعداد مؤقت التحديث"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_active_sessions)
        self.refresh_timer.start(30000)  # تحديث كل 30 ثانية
    
    def load_sessions(self):
        """تحميل قائمة الجلسات"""
        try:
            # إنشاء الجدول إذا لم يكن موجوداً
            create_query = """
            CREATE TABLE IF NOT EXISTS login_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_type TEXT NOT NULL,
                start_time TIME,
                duration_minutes INTEGER,
                auto_logout BOOLEAN DEFAULT 1,
                force_password_change BOOLEAN DEFAULT 0,
                restrict_ip BOOLEAN DEFAULT 0,
                single_session BOOLEAN DEFAULT 1,
                notes TEXT,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
            """
            self.db_manager.execute_update(create_query)
            
            # تحميل البيانات
            query = """
            SELECT ls.id, u.username, u.full_name, ls.session_type, ls.start_time, 
                   ls.duration_minutes, ls.is_active, ls.notes
            FROM login_sessions ls
            JOIN users u ON ls.user_id = u.id
            ORDER BY ls.created_date DESC
            """
            
            sessions = self.db_manager.execute_query(query)
            
            self.sessions_table.setRowCount(len(sessions))
            
            for row, session in enumerate(sessions):
                session_id, username, full_name, session_type, start_time, duration, is_active, notes = session
                
                user_display = f"{full_name} ({username})" if full_name else username
                self.sessions_table.setItem(row, 0, QTableWidgetItem(user_display))
                self.sessions_table.setItem(row, 1, QTableWidgetItem(str(session_type)))
                self.sessions_table.setItem(row, 2, QTableWidgetItem(str(start_time)))
                self.sessions_table.setItem(row, 3, QTableWidgetItem(f"{duration} دقيقة"))
                
                status = "نشط" if is_active else "غير نشط"
                status_item = QTableWidgetItem(status)
                if is_active:
                    status_item.setBackground(QColor("#d5f4e6"))
                else:
                    status_item.setBackground(QColor("#ffebee"))
                self.sessions_table.setItem(row, 4, status_item)
                
                self.sessions_table.setItem(row, 5, QTableWidgetItem(str(notes or "")))
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الجلسات:\n{str(e)}")
    
    def refresh_active_sessions(self):
        """تحديث الجلسات النشطة"""
        # محاكاة بيانات الجلسات النشطة
        active_sessions = [
            ("admin", "جلسة إدارية", "09:00", "7:30:00", "*************", "نشط", "منذ دقيقتين"),
            ("user1", "جلسة عادية", "10:30", "6:00:00", "*************", "نشط", "منذ 5 دقائق"),
            ("user2", "جلسة محدودة", "11:00", "2:00:00", "*************", "خامل", "منذ 15 دقيقة"),
        ]
        
        self.active_sessions_table.setRowCount(len(active_sessions))
        
        for row, session in enumerate(active_sessions):
            for col, data in enumerate(session):
                item = QTableWidgetItem(str(data))
                
                # تلوين حسب الحالة
                if col == 5:  # عمود الحالة
                    if data == "نشط":
                        item.setBackground(QColor("#d5f4e6"))
                    elif data == "خامل":
                        item.setBackground(QColor("#fff3cd"))
                    else:
                        item.setBackground(QColor("#ffebee"))
                
                self.active_sessions_table.setItem(row, col, item)
        
        # تحديث الإحصائيات
        self.total_sessions_label.setText(f"إجمالي الجلسات: {len(active_sessions)}")
        active_count = sum(1 for session in active_sessions if session[5] == "نشط")
        self.active_users_label.setText(f"المستخدمون النشطون: {active_count}")
        expired_count = sum(1 for session in active_sessions if session[5] == "منتهي")
        self.expired_sessions_label.setText(f"الجلسات المنتهية: {expired_count}")
    
    def new_session(self):
        """إنشاء جلسة جديدة"""
        dialog = LoginSessionDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_sessions()
    
    def edit_session(self):
        """تعديل جلسة"""
        current_row = self.sessions_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار جلسة للتعديل")
            return
        
        # الحصول على بيانات الجلسة (محاكاة)
        session_data = {
            'id': 1,
            'user_id': 1,
            'session_type': 'جلسة عادية',
            'notes': 'ملاحظات تجريبية'
        }
        
        dialog = LoginSessionDialog(self.db_manager, session_data, self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_sessions()
    
    def delete_session(self):
        """حذف جلسة"""
        current_row = self.sessions_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار جلسة للحذف")
            return
        
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذه الجلسة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم", "تم حذف الجلسة بنجاح")
            self.load_sessions()
    
    def terminate_session(self):
        """إنهاء جلسة نشطة"""
        current_row = self.active_sessions_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار جلسة لإنهائها")
            return
        
        reply = QMessageBox.question(
            self, "تأكيد الإنهاء",
            "هل أنت متأكد من إنهاء هذه الجلسة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم", "تم إنهاء الجلسة بنجاح")
            self.refresh_active_sessions()
    
    def terminate_all_sessions(self):
        """إنهاء جميع الجلسات"""
        reply = QMessageBox.question(
            self, "تأكيد الإنهاء",
            "هل أنت متأكد من إنهاء جميع الجلسات النشطة؟\n\n"
            "تحذير: سيتم تسجيل خروج جميع المستخدمين!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تم", "تم إنهاء جميع الجلسات بنجاح")
            self.refresh_active_sessions()
    
    def save_security_settings(self):
        """حفظ إعدادات الأمان"""
        try:
            # حفظ الإعدادات في قاعدة البيانات أو ملف إعدادات
            settings = {
                'max_sessions': self.max_sessions_spin.value(),
                'session_timeout': self.session_timeout_spin.value(),
                'password_expiry': self.password_expiry_spin.value(),
                'force_https': self.force_https_check.isChecked(),
                'two_factor': self.two_factor_check.isChecked(),
                'ip_whitelist': self.ip_whitelist_check.isChecked(),
                'audit_login': self.audit_login_check.isChecked()
            }
            
            # يمكن حفظ الإعدادات في جدول منفصل أو ملف JSON
            QMessageBox.information(self, "تم", "تم حفظ إعدادات الأمان بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات:\n{str(e)}")
    
    def search_login_log(self):
        """البحث في سجل الدخول"""
        # محاكاة بيانات سجل الدخول
        log_entries = [
            ("admin", "2024-01-15 09:00:00", "*************", "تسجيل دخول", "نجح", ""),
            ("user1", "2024-01-15 10:30:00", "*************", "تسجيل دخول", "نجح", ""),
            ("user2", "2024-01-15 11:00:00", "*************", "تسجيل دخول", "فشل", "كلمة مرور خاطئة"),
            ("user2", "2024-01-15 11:05:00", "*************", "تسجيل دخول", "نجح", ""),
        ]
        
        self.login_log_table.setRowCount(len(log_entries))
        
        for row, entry in enumerate(log_entries):
            for col, data in enumerate(entry):
                item = QTableWidgetItem(str(data))
                
                # تلوين حسب الحالة
                if col == 4:  # عمود الحالة
                    if data == "نجح":
                        item.setBackground(QColor("#d5f4e6"))
                    else:
                        item.setBackground(QColor("#ffebee"))
                
                self.login_log_table.setItem(row, col, item)

def show_login_interface_control(db_manager, parent=None):
    """عرض وحدة التحكم بواجهات الدخول"""
    widget = LoginInterfaceControlWidget(db_manager, parent)
    return widget
