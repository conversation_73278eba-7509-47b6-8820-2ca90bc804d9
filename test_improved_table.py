#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الجدول المحسن في نظام السندات
Improved Table Test - Voucher System
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QPushButton, QWidget, QLabel, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class ImprovedTableTestWindow(QMainWindow):
    """نافذة اختبار الجدول المحسن"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_mock_db()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار الجدول المحسن - نظام السندات")
        self.setGeometry(200, 200, 800, 600)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QMainWindow {
                background-color: #fdf2f8;
                font-family: "Tahoma", "Arial", sans-serif;
            }
            QPushButton {
                background-color: #e91e63;
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                margin: 10px;
                min-width: 300px;
            }
            QPushButton:hover {
                background-color: #c2185b;
            }
            QPushButton:pressed {
                background-color: #ad1457;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                text-align: center;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("📊 اختبار الجدول المحسن في نظام السندات")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                color: #e91e63;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 10px;
                padding: 20px;
                margin: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        # أزرار الاختبار
        buttons_layout = QVBoxLayout()
        
        # اختبار سند القبض مع الجدول المحسن
        receipt_btn = QPushButton("📄 سند قبض - جدول محسن")
        receipt_btn.clicked.connect(self.test_receipt_improved)
        buttons_layout.addWidget(receipt_btn)
        
        # اختبار سند الصرف مع الجدول المحسن
        payment_btn = QPushButton("📄 سند صرف - جدول محسن")
        payment_btn.clicked.connect(self.test_payment_improved)
        buttons_layout.addWidget(payment_btn)
        
        # اختبار مع بيانات تجريبية
        sample_data_btn = QPushButton("📋 اختبار مع بيانات تجريبية")
        sample_data_btn.clicked.connect(self.test_with_sample_data)
        buttons_layout.addWidget(sample_data_btn)
        
        layout.addLayout(buttons_layout)
        
        # معلومات التحسينات
        info_label = QLabel("""
📊 التحسينات المطبقة على الجدول:

✅ تكبير خانات رقم الحساب (150px بدلاً من 100px)
✅ تكبير خانات اسم الحساب (250px - عرض ثابت)
✅ تكبير خانات المبالغ (130px بدلاً من 100px)
✅ زيادة ارتفاع الصفوف (35px)
✅ تحسين خط الجدول (Tahoma 11px)
✅ ألوان متناوبة للصفوف
✅ محاذاة المبالغ لليمين
✅ تحسين تنسيق الرؤوس
✅ حدود أوضح للخلايا
✅ تأثيرات hover للرؤوس
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: white;
                border: 1px solid #e91e63;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
                font-size: 12px;
                text-align: left;
            }
        """)
        layout.addWidget(info_label)
        
        central_widget.setLayout(layout)
    
    def setup_mock_db(self):
        """إعداد قاعدة بيانات وهمية للاختبار"""
        class MockDBManager:
            def __init__(self):
                self.cursor = type('obj', (object,), {'lastrowid': 1})()
            
            def execute_query(self, query, params=None):
                # محاكاة نتائج الاستعلام
                if "MAX" in query:
                    return [(0,)]
                elif "chart_of_accounts" in query:
                    # إرجاع بيانات الحسابات التجريبية
                    return [
                        ('1010001', 'صندوق المركز الرئيسي', 'أصول متداولة', 1, 3, '101000'),
                        ('1020001', 'البنك الأهلي التجاري', 'أصول متداولة', 1, 3, '102000'),
                        ('1020002', 'مصرف الراجحي', 'أصول متداولة', 1, 3, '102000'),
                        ('1030001', 'العملاء المحليين', 'أصول متداولة', 1, 3, '103000'),
                        ('2010001', 'الموردين المحليين', 'خصوم متداولة', 1, 3, '201000'),
                        ('4010001', 'مبيعات محلية', 'إيرادات', 1, 3, '401000'),
                        ('5010001', 'تكلفة البضاعة المباعة', 'مصروفات', 1, 3, '501000'),
                    ]
                return []
            
            def execute_update(self, query, params=None):
                return True
        
        self.db_manager = MockDBManager()
    
    def test_receipt_improved(self):
        """اختبار سند القبض مع الجدول المحسن"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            receipt_dialog = VoucherInterface(self.db_manager, "receipt", self)
            receipt_dialog.show()
            
            QMessageBox.information(
                self, 
                "سند القبض - جدول محسن",
                "✅ تم فتح سند القبض مع الجدول المحسن\n\n"
                "📊 التحسينات المطبقة:\n"
                "• خانة رقم الحساب أوسع (150px)\n"
                "• خانة اسم الحساب أوسع (250px)\n"
                "• خانات المبالغ أوسع (130px)\n"
                "• ارتفاع الصفوف أكبر (35px)\n"
                "• محاذاة المبالغ لليمين\n"
                "• ألوان متناوبة للصفوف\n\n"
                "🔍 جرب:\n"
                "• إضافة صفوف جديدة\n"
                "• استخدام F9 للبحث عن الحسابات\n"
                "• إدخال مبالغ مختلفة"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند القبض:\n{str(e)}")
    
    def test_payment_improved(self):
        """اختبار سند الصرف مع الجدول المحسن"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            payment_dialog = VoucherInterface(self.db_manager, "payment", self)
            payment_dialog.show()
            
            QMessageBox.information(
                self, 
                "سند الصرف - جدول محسن",
                "✅ تم فتح سند الصرف مع الجدول المحسن\n\n"
                "📊 التحسينات المطبقة:\n"
                "• خانة رقم الحساب أوسع (150px)\n"
                "• خانة اسم الحساب أوسع (250px)\n"
                "• خانات المبالغ أوسع (130px)\n"
                "• ارتفاع الصفوف أكبر (35px)\n"
                "• محاذاة المبالغ لليمين\n"
                "• ألوان متناوبة للصفوف\n\n"
                "🔍 جرب:\n"
                "• إضافة صفوف جديدة\n"
                "• استخدام F9 للبحث عن الحسابات\n"
                "• إدخال مبالغ مختلفة"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند الصرف:\n{str(e)}")
    
    def test_with_sample_data(self):
        """اختبار مع بيانات تجريبية"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            from PyQt5.QtWidgets import QTableWidgetItem
            from PyQt5.QtCore import Qt
            
            # إنشاء سند قبض
            receipt_dialog = VoucherInterface(self.db_manager, "receipt", self)
            
            # تعبئة بيانات تجريبية
            receipt_dialog.beneficiary_edit.setText("شركة الأمل للتجارة")
            receipt_dialog.beneficiary_id_edit.setText("C001")
            receipt_dialog.description_edit.setPlainText("سند قبض من العميل - دفعة على الحساب")
            
            # إضافة صفوف تجريبية للجدول
            table = receipt_dialog.details_table
            
            # الصف الأول - النقدية
            table.setItem(0, 1, QTableWidgetItem("1010001"))
            table.setItem(0, 2, QTableWidgetItem("صندوق المركز الرئيسي"))
            table.setItem(0, 3, QTableWidgetItem("مبلغ مقبوض نقداً"))
            debit_item = QTableWidgetItem("15,000.00")
            debit_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            table.setItem(0, 4, debit_item)
            table.setItem(0, 5, QTableWidgetItem("0.00"))
            
            # إضافة صف ثاني
            receipt_dialog.add_detail_row()
            table.setItem(1, 1, QTableWidgetItem("1030001"))
            table.setItem(1, 2, QTableWidgetItem("العملاء المحليين"))
            table.setItem(1, 3, QTableWidgetItem("تحصيل من العميل"))
            table.setItem(1, 4, QTableWidgetItem("0.00"))
            credit_item = QTableWidgetItem("15,000.00")
            credit_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            table.setItem(1, 5, credit_item)
            
            # حساب الإجماليات
            receipt_dialog.calculate_totals()
            
            receipt_dialog.show()
            
            QMessageBox.information(
                self, 
                "بيانات تجريبية",
                "✅ تم تعبئة السند ببيانات تجريبية\n\n"
                "📋 البيانات المعبأة:\n"
                "• المستفيد: شركة الأمل للتجارة\n"
                "• المبلغ: 15,000.00 ريال\n"
                "• الحسابات: صندوق + عملاء\n\n"
                "📊 لاحظ التحسينات:\n"
                "• وضوح أرقام الحسابات\n"
                "• وضوح أسماء الحسابات الطويلة\n"
                "• وضوح المبالغ مع المحاذاة\n"
                "• ارتفاع الصفوف المريح\n"
                "• الألوان المتناوبة"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء البيانات التجريبية:\n{str(e)}")


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont("Tahoma", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = ImprovedTableTestWindow()
    window.show()
    
    print("📊 تم تشغيل اختبار الجدول المحسن!")
    print("✅ التحسينات المطبقة:")
    print("   📏 خانة رقم الحساب: 150px (بدلاً من 100px)")
    print("   📏 خانة اسم الحساب: 250px (عرض ثابت)")
    print("   📏 خانات المبالغ: 130px (بدلاً من 100px)")
    print("   📏 ارتفاع الصفوف: 35px")
    print("   🎨 ألوان متناوبة للصفوف")
    print("   ➡️ محاذاة المبالغ لليمين")
    print("   🔤 خط محسن: Tahoma 11px")
    print("   🎯 حدود أوضح للخلايا")
    print("")
    print("💡 للاختبار:")
    print("   • افتح أي سند وجرب إدخال البيانات")
    print("   • لاحظ وضوح الخانات المكبرة")
    print("   • استخدم F9 للبحث عن الحسابات")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
