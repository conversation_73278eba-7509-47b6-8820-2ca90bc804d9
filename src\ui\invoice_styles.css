/* تصميم فاتورة المبيعات الجديد */

/* الخط الأساسي */
* {
    font-family: 'Cairo', Tahoma, sans-serif;
}

/* الحاوي الرئيسي */
.invoice-container {
    background: #eef2f7;
    margin: 20px;
    direction: rtl;
    color: #333;
}

.invoice-main {
    max-width: 920px;
    margin: auto;
    background: #fff;
    padding: 25px 30px 30px 30px;
    border-radius: 12px;
    box-shadow: 0 6px 18px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    height: 100%;
}

/* شريط الأدوات */
.toolbar {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    margin-bottom: 20px;
    flex-wrap: nowrap;
}

.toolbar-button {
    flex: 1;
    padding: 12px 0;
    background: linear-gradient(135deg, #4a90e2, #357ABD);
    border: none;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    box-shadow: 0 4px 8px rgba(53,122,189,0.4);
    transition: background 0.3s, box-shadow 0.3s;
    margin: 0 6px;
}

.toolbar-button:hover {
    background: linear-gradient(135deg, #357ABD, #2b5f94);
    box-shadow: 0 6px 14px rgba(43,95,148,0.6);
}

/* الرأس */
.header {
    flex: 0 0 15%;
    font-size: 15px;
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 14px;
}

/* صفوف الرأس: السماح باللف عند الحاجة */
.header .row {
    display: flex;
    gap: 30px;
    align-items: center;
    flex-wrap: wrap;
}

/* مجموعة label + input/select */
.field-group {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 180px;
    flex: 1 1 180px;
    white-space: nowrap;
}

.field-group label {
    min-width: 110px;
    font-weight: 700;
    text-align: right;
    color: #222;
    user-select: none;
    flex-shrink: 0;
}

.field-group input,
.field-group select {
    flex: 1 1 auto;
    padding: 8px 12px;
    border: 1.8px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.field-group input:focus,
.field-group select:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 6px rgba(74,144,226,0.6);
    outline: none;
}

/* عرض الحقول المحددة */
.invoice-date {
    width: 140px;
    min-width: 140px;
}

.currency-field {
    width: 110px;
    min-width: 110px;
}

/* جدول الأصناف */
.items-table-container {
    flex: 1 1 70%;
    overflow-y: auto;
    border: 1.5px solid #ddd;
    border-radius: 10px;
    padding: 8px;
    background: #fff;
    box-shadow: inset 0 0 12px #f0f4fa;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    word-wrap: break-word;
    font-size: 15px;
}

.items-table th, .items-table td {
    border: 1.5px solid #ddd;
    padding: 10px 8px;
    text-align: center;
    background: #fafafa;
    transition: background-color 0.3s;
}

.items-table th {
    background: #4a90e2;
    color: white;
    font-weight: 700;
}

.items-table tbody tr:hover td {
    background: #e6f0fb;
}

.items-table input {
    width: 100%;
    padding: 7px 10px;
    box-sizing: border-box;
    border: 1.2px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
    text-align: center;
    transition: border-color 0.3s;
}

.items-table input:focus {
    border-color: #4a90e2;
    outline: none;
    background: #f0f7ff;
}

/* التذييل */
.footer {
    flex: 0 0 15%;
    font-size: 14px;
    color: #444;
    margin-top: 18px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.summary {
    font-weight: 700;
    margin-bottom: 12px;
    font-size: 16px;
    text-align: left;
    direction: ltr;
    color: #1a1a1a;
}

.summary div {
    margin-bottom: 8px;
}

.footer-info {
    display: flex;
    justify-content: space-between;
    gap: 14px;
    flex-wrap: wrap;
}

.footer-info-item {
    flex: 1 1 200px;
    padding: 8px 12px;
    border: 1.5px solid #e3e7ee;
    border-radius: 8px;
    background-color: #f7f9fc;
    box-shadow: inset 0 0 5px #d9dfea;
    font-size: 13px;
    color: #555;
    user-select: none;
}

/* الألوان والحالات */
.success {
    background-color: #27ae60;
    color: white;
}

.warning {
    background-color: #f39c12;
    color: white;
}

.danger {
    background-color: #e74c3c;
    color: white;
}

.info {
    background-color: #3498db;
    color: white;
}

/* التحريك والانتقالات */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* الاستجابة للشاشات المختلفة */
@media (max-width: 800px) {
    .header .row {
        gap: 20px;
    }

    .field-group {
        min-width: 140px;
        flex: 1 1 140px;
    }

    .invoice-date {
        width: 120px;
        min-width: 120px;
    }

    .currency-field {
        width: 90px;
        min-width: 90px;
    }

    .toolbar {
        flex-direction: column;
        gap: 8px;
    }

    .footer-info {
        flex-direction: column;
    }
}
