#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مولد كلمات المرور المتقدم
Advanced Password Generator System
"""

import random
import string
import secrets
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QLineEdit, QSpinBox, QCheckBox,
                             QGroupBox, QTextEdit, QSlider, QProgressBar,
                             QComboBox, QTableWidget, QTableWidgetItem,
                             QHeaderView, QMessageBox, QApplication)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QColor

class PasswordGeneratorDialog(QDialog):
    """مولد كلمات المرور المتقدم"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.password_history = []
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔐 مولد كلمات المرور المتقدم")
        self.setGeometry(100, 100, 800, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🔐 مولد كلمات المرور المتقدم")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: white;
                background-color: #e74c3c;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # إعدادات كلمة المرور
        settings_group = QGroupBox("⚙️ إعدادات كلمة المرور")
        settings_layout = QVBoxLayout()
        
        # طول كلمة المرور
        length_layout = QHBoxLayout()
        length_layout.addWidget(QLabel("طول كلمة المرور:"))
        
        self.length_spin = QSpinBox()
        self.length_spin.setRange(4, 128)
        self.length_spin.setValue(12)
        self.length_spin.valueChanged.connect(self.update_strength)
        length_layout.addWidget(self.length_spin)
        
        self.length_slider = QSlider(Qt.Horizontal)
        self.length_slider.setRange(4, 128)
        self.length_slider.setValue(12)
        self.length_slider.valueChanged.connect(self.length_spin.setValue)
        self.length_spin.valueChanged.connect(self.length_slider.setValue)
        length_layout.addWidget(self.length_slider)
        
        settings_layout.addLayout(length_layout)
        
        # خيارات الأحرف
        chars_layout = QHBoxLayout()
        
        self.uppercase_check = QCheckBox("أحرف كبيرة (A-Z)")
        self.uppercase_check.setChecked(True)
        self.uppercase_check.toggled.connect(self.update_strength)
        chars_layout.addWidget(self.uppercase_check)
        
        self.lowercase_check = QCheckBox("أحرف صغيرة (a-z)")
        self.lowercase_check.setChecked(True)
        self.lowercase_check.toggled.connect(self.update_strength)
        chars_layout.addWidget(self.lowercase_check)
        
        settings_layout.addLayout(chars_layout)
        
        chars_layout2 = QHBoxLayout()
        
        self.numbers_check = QCheckBox("أرقام (0-9)")
        self.numbers_check.setChecked(True)
        self.numbers_check.toggled.connect(self.update_strength)
        chars_layout2.addWidget(self.numbers_check)
        
        self.symbols_check = QCheckBox("رموز (!@#$%^&*)")
        self.symbols_check.setChecked(True)
        self.symbols_check.toggled.connect(self.update_strength)
        chars_layout2.addWidget(self.symbols_check)
        
        settings_layout.addLayout(chars_layout2)
        
        # خيارات متقدمة
        advanced_layout = QHBoxLayout()
        
        self.exclude_similar_check = QCheckBox("استبعاد الأحرف المتشابهة (0, O, l, 1)")
        self.exclude_similar_check.setChecked(False)
        advanced_layout.addWidget(self.exclude_similar_check)
        
        self.exclude_ambiguous_check = QCheckBox("استبعاد الرموز الغامضة ({ } [ ] ( ) / \\ ' \" ~ , ; . < >)")
        self.exclude_ambiguous_check.setChecked(False)
        advanced_layout.addWidget(self.exclude_ambiguous_check)
        
        settings_layout.addLayout(advanced_layout)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # قوة كلمة المرور
        strength_group = QGroupBox("💪 قوة كلمة المرور")
        strength_layout = QVBoxLayout()
        
        self.strength_bar = QProgressBar()
        self.strength_bar.setRange(0, 100)
        strength_layout.addWidget(self.strength_bar)
        
        self.strength_label = QLabel("متوسطة")
        self.strength_label.setAlignment(Qt.AlignCenter)
        strength_layout.addWidget(self.strength_label)
        
        strength_group.setLayout(strength_layout)
        layout.addWidget(strength_group)
        
        # كلمة المرور المولدة
        password_group = QGroupBox("🔑 كلمة المرور المولدة")
        password_layout = QVBoxLayout()
        
        self.password_edit = QLineEdit()
        self.password_edit.setFont(QFont("Courier", 12))
        self.password_edit.setReadOnly(True)
        password_layout.addWidget(self.password_edit)
        
        # أزرار العمليات
        buttons_layout = QHBoxLayout()
        
        self.generate_btn = QPushButton("🎲 توليد كلمة مرور")
        self.generate_btn.clicked.connect(self.generate_password)
        self.generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        buttons_layout.addWidget(self.generate_btn)
        
        self.copy_btn = QPushButton("📋 نسخ")
        self.copy_btn.clicked.connect(self.copy_password)
        buttons_layout.addWidget(self.copy_btn)
        
        self.save_btn = QPushButton("💾 حفظ")
        self.save_btn.clicked.connect(self.save_password)
        buttons_layout.addWidget(self.save_btn)
        
        password_layout.addLayout(buttons_layout)
        password_group.setLayout(password_layout)
        layout.addWidget(password_group)
        
        # قوالب كلمات المرور
        templates_group = QGroupBox("📋 قوالب كلمات المرور")
        templates_layout = QVBoxLayout()
        
        templates_buttons_layout = QHBoxLayout()
        
        self.simple_btn = QPushButton("بسيطة (8 أحرف)")
        self.simple_btn.clicked.connect(lambda: self.apply_template("simple"))
        templates_buttons_layout.addWidget(self.simple_btn)
        
        self.medium_btn = QPushButton("متوسطة (12 حرف)")
        self.medium_btn.clicked.connect(lambda: self.apply_template("medium"))
        templates_buttons_layout.addWidget(self.medium_btn)
        
        self.strong_btn = QPushButton("قوية (16 حرف)")
        self.strong_btn.clicked.connect(lambda: self.apply_template("strong"))
        templates_buttons_layout.addWidget(self.strong_btn)
        
        self.ultra_btn = QPushButton("فائقة القوة (24 حرف)")
        self.ultra_btn.clicked.connect(lambda: self.apply_template("ultra"))
        templates_buttons_layout.addWidget(self.ultra_btn)
        
        templates_layout.addLayout(templates_buttons_layout)
        templates_group.setLayout(templates_layout)
        layout.addWidget(templates_group)
        
        # سجل كلمات المرور
        history_group = QGroupBox("📚 سجل كلمات المرور")
        history_layout = QVBoxLayout()
        
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(3)
        self.history_table.setHorizontalHeaderLabels(["كلمة المرور", "القوة", "التاريخ"])
        self.history_table.horizontalHeader().setStretchLastSection(True)
        self.history_table.setMaximumHeight(150)
        history_layout.addWidget(self.history_table)
        
        history_buttons_layout = QHBoxLayout()
        
        self.clear_history_btn = QPushButton("🗑️ مسح السجل")
        self.clear_history_btn.clicked.connect(self.clear_history)
        history_buttons_layout.addWidget(self.clear_history_btn)
        
        self.export_history_btn = QPushButton("📤 تصدير السجل")
        self.export_history_btn.clicked.connect(self.export_history)
        history_buttons_layout.addWidget(self.export_history_btn)
        
        history_buttons_layout.addStretch()
        history_layout.addLayout(history_buttons_layout)
        
        history_group.setLayout(history_layout)
        layout.addWidget(history_group)
        
        self.setLayout(layout)
        
        # توليد كلمة مرور أولية
        self.generate_password()
        self.update_strength()
    
    def get_character_set(self):
        """الحصول على مجموعة الأحرف المطلوبة"""
        chars = ""
        
        if self.uppercase_check.isChecked():
            chars += string.ascii_uppercase
        
        if self.lowercase_check.isChecked():
            chars += string.ascii_lowercase
        
        if self.numbers_check.isChecked():
            chars += string.digits
        
        if self.symbols_check.isChecked():
            chars += "!@#$%^&*()_+-=[]{}|;:,.<>?"
        
        # استبعاد الأحرف المتشابهة
        if self.exclude_similar_check.isChecked():
            similar_chars = "0Ol1"
            chars = ''.join(c for c in chars if c not in similar_chars)
        
        # استبعاد الرموز الغامضة
        if self.exclude_ambiguous_check.isChecked():
            ambiguous_chars = "{}[]()/'\"~,;.<>"
            chars = ''.join(c for c in chars if c not in ambiguous_chars)
        
        return chars
    
    def generate_password(self):
        """توليد كلمة مرور جديدة"""
        chars = self.get_character_set()
        
        if not chars:
            QMessageBox.warning(self, "تحذير", "يجب اختيار نوع واحد على الأقل من الأحرف")
            return
        
        length = self.length_spin.value()
        
        # استخدام secrets للأمان العالي
        password = ''.join(secrets.choice(chars) for _ in range(length))
        
        self.password_edit.setText(password)
        self.update_strength()
        self.add_to_history(password)
    
    def calculate_strength(self, password):
        """حساب قوة كلمة المرور"""
        if not password:
            return 0, "ضعيفة جداً"
        
        score = 0
        
        # طول كلمة المرور
        length = len(password)
        if length >= 8:
            score += 25
        if length >= 12:
            score += 25
        if length >= 16:
            score += 25
        
        # تنوع الأحرف
        has_upper = any(c.isupper() for c in password)
        has_lower = any(c.islower() for c in password)
        has_digit = any(c.isdigit() for c in password)
        has_symbol = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        char_types = sum([has_upper, has_lower, has_digit, has_symbol])
        score += char_types * 5
        
        # عدم التكرار
        unique_chars = len(set(password))
        if unique_chars == length:
            score += 10
        elif unique_chars >= length * 0.8:
            score += 5
        
        # تحديد مستوى القوة
        if score >= 80:
            return min(score, 100), "قوية جداً"
        elif score >= 60:
            return score, "قوية"
        elif score >= 40:
            return score, "متوسطة"
        elif score >= 20:
            return score, "ضعيفة"
        else:
            return score, "ضعيفة جداً"
    
    def update_strength(self):
        """تحديث مؤشر قوة كلمة المرور"""
        password = self.password_edit.text()
        score, level = self.calculate_strength(password)
        
        self.strength_bar.setValue(score)
        self.strength_label.setText(f"{level} ({score}%)")
        
        # تغيير لون المؤشر حسب القوة
        if score >= 80:
            color = "#27ae60"  # أخضر
        elif score >= 60:
            color = "#f39c12"  # برتقالي
        elif score >= 40:
            color = "#e67e22"  # برتقالي داكن
        else:
            color = "#e74c3c"  # أحمر
        
        self.strength_bar.setStyleSheet(f"""
            QProgressBar::chunk {{
                background-color: {color};
            }}
        """)
    
    def apply_template(self, template_type):
        """تطبيق قالب كلمة مرور"""
        templates = {
            "simple": {
                "length": 8,
                "uppercase": True,
                "lowercase": True,
                "numbers": True,
                "symbols": False
            },
            "medium": {
                "length": 12,
                "uppercase": True,
                "lowercase": True,
                "numbers": True,
                "symbols": True
            },
            "strong": {
                "length": 16,
                "uppercase": True,
                "lowercase": True,
                "numbers": True,
                "symbols": True
            },
            "ultra": {
                "length": 24,
                "uppercase": True,
                "lowercase": True,
                "numbers": True,
                "symbols": True
            }
        }
        
        template = templates.get(template_type, templates["medium"])
        
        self.length_spin.setValue(template["length"])
        self.uppercase_check.setChecked(template["uppercase"])
        self.lowercase_check.setChecked(template["lowercase"])
        self.numbers_check.setChecked(template["numbers"])
        self.symbols_check.setChecked(template["symbols"])
        
        self.generate_password()
    
    def copy_password(self):
        """نسخ كلمة المرور للحافظة"""
        password = self.password_edit.text()
        if password:
            clipboard = QApplication.clipboard()
            clipboard.setText(password)
            QMessageBox.information(self, "تم", "تم نسخ كلمة المرور للحافظة")
    
    def save_password(self):
        """حفظ كلمة المرور"""
        password = self.password_edit.text()
        if password:
            from PyQt5.QtWidgets import QInputDialog
            
            label, ok = QInputDialog.getText(self, "حفظ كلمة المرور", "تسمية كلمة المرور:")
            if ok and label:
                # يمكن إضافة حفظ في ملف أو قاعدة بيانات هنا
                QMessageBox.information(self, "تم", f"تم حفظ كلمة المرور باسم: {label}")
    
    def add_to_history(self, password):
        """إضافة كلمة المرور للسجل"""
        from datetime import datetime
        
        score, level = self.calculate_strength(password)
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        self.password_history.append({
            "password": password,
            "strength": level,
            "score": score,
            "timestamp": timestamp
        })
        
        # الاحتفاظ بآخر 20 كلمة مرور فقط
        if len(self.password_history) > 20:
            self.password_history.pop(0)
        
        self.update_history_table()
    
    def update_history_table(self):
        """تحديث جدول السجل"""
        self.history_table.setRowCount(len(self.password_history))
        
        for row, entry in enumerate(self.password_history):
            self.history_table.setItem(row, 0, QTableWidgetItem(entry["password"]))
            self.history_table.setItem(row, 1, QTableWidgetItem(f"{entry['strength']} ({entry['score']}%)"))
            self.history_table.setItem(row, 2, QTableWidgetItem(entry["timestamp"]))
    
    def clear_history(self):
        """مسح سجل كلمات المرور"""
        reply = QMessageBox.question(self, "تأكيد", "هل أنت متأكد من مسح سجل كلمات المرور؟")
        if reply == QMessageBox.Yes:
            self.password_history.clear()
            self.update_history_table()
    
    def export_history(self):
        """تصدير سجل كلمات المرور"""
        if not self.password_history:
            QMessageBox.information(self, "معلومات", "لا يوجد سجل لتصديره")
            return
        
        from PyQt5.QtWidgets import QFileDialog
        
        filename, _ = QFileDialog.getSaveFileName(self, "تصدير السجل", "password_history.txt", "Text Files (*.txt)")
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("سجل كلمات المرور\n")
                    f.write("=" * 50 + "\n\n")
                    
                    for entry in self.password_history:
                        f.write(f"كلمة المرور: {entry['password']}\n")
                        f.write(f"القوة: {entry['strength']} ({entry['score']}%)\n")
                        f.write(f"التاريخ: {entry['timestamp']}\n")
                        f.write("-" * 30 + "\n")
                
                QMessageBox.information(self, "تم", "تم تصدير السجل بنجاح")
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تصدير السجل:\n{str(e)}")

def show_password_generator(parent=None):
    """عرض مولد كلمات المرور"""
    dialog = PasswordGeneratorDialog(parent)
    dialog.exec_()
