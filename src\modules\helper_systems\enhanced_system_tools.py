#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام أدوات النظام المحسن والمتطور
Enhanced System Tools
"""

import sys
import os
import psutil
import platform
import subprocess
import shutil
import tempfile
import threading
import time
from datetime import datetime, timedelta
from collections import deque
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QLabel, QLineEdit, QPushButton, 
                             QGroupBox, QTextEdit, QTabWidget, QComboBox,
                             QApplication, QMessageBox, QListWidget,
                             QListWidgetItem, QCheckBox, QSpinBox, QSlider,
                             QProgressBar, QTableWidget, QTableWidgetItem,
                             QHeaderView, QSplitter, QTreeWidget, QTreeWidgetItem,
                             QScrollA<PERSON>, <PERSON><PERSON><PERSON><PERSON>, QFileD<PERSON>og, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,
                             <PERSON><PERSON><PERSON><PERSON><PERSON>, QStatusBar, QDoubleSpinBox, QDateTimeEdit,
                             QCalendarWidget, QDial, QLCDNumber, QFormLayout)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QDateTime, QSize
from PyQt5.QtGui import QFont, QColor, QIcon, QPalette, QPixmap, QPainter

class SystemResourceMonitor(QThread):
    """مراقب موارد النظام المتقدم"""
    
    data_updated = pyqtSignal(dict)
    alert_triggered = pyqtSignal(str, str, str)  # type, message, level
    
    def __init__(self):
        super().__init__()
        self.is_running = True
        self.monitoring_interval = 1000  # milliseconds
        self.history_size = 300  # 5 minutes at 1 second intervals
        
        # تاريخ البيانات
        self.cpu_history = deque(maxlen=self.history_size)
        self.memory_history = deque(maxlen=self.history_size)
        self.disk_history = deque(maxlen=self.history_size)
        self.network_history = deque(maxlen=self.history_size)
        self.temperature_history = deque(maxlen=self.history_size)
        
        # إعدادات التنبيهات
        self.alert_thresholds = {
            'cpu': 80,
            'memory': 85,
            'disk': 90,
            'temperature': 70
        }
        
        # متغيرات الشبكة
        self.last_network_io = None
        self.network_speeds = {'sent': 0, 'recv': 0}
        
    def run(self):
        """تشغيل مراقبة النظام"""
        while self.is_running:
            try:
                # جمع بيانات النظام
                system_data = self.collect_system_data()
                
                # إضافة للتاريخ
                self.add_to_history(system_data)
                
                # فحص التنبيهات
                self.check_alerts(system_data)
                
                # إرسال البيانات
                self.data_updated.emit(system_data)
                
                self.msleep(self.monitoring_interval)
                
            except Exception as e:
                print(f"خطأ في مراقبة النظام: {e}")
                self.msleep(5000)
    
    def collect_system_data(self):
        """جمع بيانات النظام"""
        data = {}
        
        # معلومات المعالج
        data['cpu_percent'] = psutil.cpu_percent(interval=None)
        data['cpu_count'] = psutil.cpu_count()
        data['cpu_freq'] = psutil.cpu_freq()._asdict() if psutil.cpu_freq() else {}
        
        # معلومات الذاكرة
        memory = psutil.virtual_memory()
        data['memory'] = {
            'total': memory.total,
            'available': memory.available,
            'used': memory.used,
            'percent': memory.percent,
            'free': memory.free
        }
        
        # معلومات المبادلة
        swap = psutil.swap_memory()
        data['swap'] = {
            'total': swap.total,
            'used': swap.used,
            'free': swap.free,
            'percent': swap.percent
        }
        
        # معلومات القرص
        disk_usage = psutil.disk_usage('/' if os.name != 'nt' else 'C:')
        data['disk'] = {
            'total': disk_usage.total,
            'used': disk_usage.used,
            'free': disk_usage.free,
            'percent': (disk_usage.used / disk_usage.total) * 100
        }
        
        # معلومات الشبكة
        network_io = psutil.net_io_counters()
        if self.last_network_io:
            time_diff = 1  # second
            sent_speed = (network_io.bytes_sent - self.last_network_io.bytes_sent) / time_diff
            recv_speed = (network_io.bytes_recv - self.last_network_io.bytes_recv) / time_diff
            self.network_speeds = {'sent': sent_speed, 'recv': recv_speed}
        
        data['network'] = {
            'bytes_sent': network_io.bytes_sent,
            'bytes_recv': network_io.bytes_recv,
            'packets_sent': network_io.packets_sent,
            'packets_recv': network_io.packets_recv,
            'speed_sent': self.network_speeds['sent'],
            'speed_recv': self.network_speeds['recv']
        }
        self.last_network_io = network_io
        
        # معلومات العمليات
        data['processes_count'] = len(psutil.pids())
        
        # معلومات النظام
        data['boot_time'] = psutil.boot_time()
        data['uptime'] = time.time() - psutil.boot_time()
        
        # درجة الحرارة (إن أمكن)
        try:
            temps = psutil.sensors_temperatures()
            if temps:
                avg_temp = 0
                temp_count = 0
                for name, entries in temps.items():
                    for entry in entries:
                        if entry.current:
                            avg_temp += entry.current
                            temp_count += 1
                data['temperature'] = avg_temp / temp_count if temp_count > 0 else 0
            else:
                data['temperature'] = 0
        except:
            data['temperature'] = 0
        
        # معلومات البطارية (للأجهزة المحمولة)
        try:
            battery = psutil.sensors_battery()
            if battery:
                data['battery'] = {
                    'percent': battery.percent,
                    'plugged': battery.power_plugged,
                    'time_left': battery.secsleft if battery.secsleft != psutil.POWER_TIME_UNLIMITED else None
                }
            else:
                data['battery'] = None
        except:
            data['battery'] = None
        
        data['timestamp'] = time.time()
        return data
    
    def add_to_history(self, data):
        """إضافة البيانات للتاريخ"""
        self.cpu_history.append(data['cpu_percent'])
        self.memory_history.append(data['memory']['percent'])
        self.disk_history.append(data['disk']['percent'])
        self.network_history.append({
            'sent': data['network']['speed_sent'],
            'recv': data['network']['speed_recv']
        })
        if data['temperature'] > 0:
            self.temperature_history.append(data['temperature'])
    
    def check_alerts(self, data):
        """فحص التنبيهات"""
        # تنبيه المعالج
        if data['cpu_percent'] > self.alert_thresholds['cpu']:
            self.alert_triggered.emit(
                'cpu', 
                f"استخدام المعالج مرتفع: {data['cpu_percent']:.1f}%",
                'warning'
            )
        
        # تنبيه الذاكرة
        if data['memory']['percent'] > self.alert_thresholds['memory']:
            self.alert_triggered.emit(
                'memory',
                f"استخدام الذاكرة مرتفع: {data['memory']['percent']:.1f}%",
                'warning'
            )
        
        # تنبيه القرص
        if data['disk']['percent'] > self.alert_thresholds['disk']:
            self.alert_triggered.emit(
                'disk',
                f"مساحة القرص منخفضة: {data['disk']['percent']:.1f}%",
                'critical'
            )
        
        # تنبيه درجة الحرارة
        if data['temperature'] > self.alert_thresholds['temperature']:
            self.alert_triggered.emit(
                'temperature',
                f"درجة حرارة النظام مرتفعة: {data['temperature']:.1f}°C",
                'critical'
            )
    
    def get_history_data(self, data_type):
        """الحصول على بيانات التاريخ"""
        if data_type == 'cpu':
            return list(self.cpu_history)
        elif data_type == 'memory':
            return list(self.memory_history)
        elif data_type == 'disk':
            return list(self.disk_history)
        elif data_type == 'network':
            return list(self.network_history)
        elif data_type == 'temperature':
            return list(self.temperature_history)
        return []
    
    def set_alert_threshold(self, resource, threshold):
        """تعيين حد التنبيه"""
        if resource in self.alert_thresholds:
            self.alert_thresholds[resource] = threshold
    
    def stop(self):
        """إيقاف المراقبة"""
        self.is_running = False

class ProcessManager:
    """مدير العمليات المتقدم"""
    
    @staticmethod
    def get_processes_list(sort_by='cpu', limit=50):
        """الحصول على قائمة العمليات"""
        processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 
                                       'memory_info', 'status', 'create_time', 'username']):
            try:
                proc_info = proc.info
                proc_info['memory_mb'] = proc_info['memory_info'].rss / (1024 * 1024)
                proc_info['running_time'] = time.time() - proc_info['create_time']
                processes.append(proc_info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        # ترتيب العمليات
        if sort_by == 'cpu':
            processes.sort(key=lambda x: x['cpu_percent'] or 0, reverse=True)
        elif sort_by == 'memory':
            processes.sort(key=lambda x: x['memory_percent'] or 0, reverse=True)
        elif sort_by == 'name':
            processes.sort(key=lambda x: x['name'] or '')
        
        return processes[:limit]
    
    @staticmethod
    def kill_process(pid):
        """إنهاء عملية"""
        try:
            proc = psutil.Process(pid)
            proc.terminate()
            return True, "تم إنهاء العملية بنجاح"
        except psutil.NoSuchProcess:
            return False, "العملية غير موجودة"
        except psutil.AccessDenied:
            return False, "ليس لديك صلاحية لإنهاء هذه العملية"
        except Exception as e:
            return False, f"خطأ: {str(e)}"
    
    @staticmethod
    def get_process_details(pid):
        """الحصول على تفاصيل العملية"""
        try:
            proc = psutil.Process(pid)
            return {
                'pid': proc.pid,
                'name': proc.name(),
                'status': proc.status(),
                'cpu_percent': proc.cpu_percent(),
                'memory_percent': proc.memory_percent(),
                'memory_info': proc.memory_info()._asdict(),
                'create_time': proc.create_time(),
                'username': proc.username(),
                'cmdline': proc.cmdline(),
                'cwd': proc.cwd(),
                'exe': proc.exe(),
                'connections': len(proc.connections()),
                'threads': proc.num_threads()
            }
        except Exception as e:
            return None

class SystemCleaner:
    """منظف النظام المتقدم"""
    
    @staticmethod
    def get_temp_files_size():
        """حساب حجم الملفات المؤقتة"""
        temp_dirs = [tempfile.gettempdir()]
        if os.name == 'nt':
            temp_dirs.extend([
                os.path.expandvars('%TEMP%'),
                os.path.expandvars('%TMP%'),
                os.path.expandvars('%LOCALAPPDATA%\\Temp')
            ])
        
        total_size = 0
        file_count = 0
        
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        try:
                            file_path = os.path.join(root, file)
                            total_size += os.path.getsize(file_path)
                            file_count += 1
                        except:
                            continue
        
        return total_size, file_count
    
    @staticmethod
    def clean_temp_files():
        """تنظيف الملفات المؤقتة"""
        temp_dirs = [tempfile.gettempdir()]
        if os.name == 'nt':
            temp_dirs.extend([
                os.path.expandvars('%TEMP%'),
                os.path.expandvars('%TMP%'),
                os.path.expandvars('%LOCALAPPDATA%\\Temp')
            ])
        
        deleted_files = 0
        freed_space = 0
        errors = []
        
        for temp_dir in temp_dirs:
            if os.path.exists(temp_dir):
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        try:
                            file_path = os.path.join(root, file)
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            deleted_files += 1
                            freed_space += file_size
                        except Exception as e:
                            errors.append(f"{file}: {str(e)}")
        
        return deleted_files, freed_space, errors
    
    @staticmethod
    def get_recycle_bin_size():
        """حساب حجم سلة المحذوفات"""
        if os.name == 'nt':
            try:
                import winshell
                total_size = 0
                for item in winshell.recycle_bin():
                    total_size += item.size()
                return total_size
            except:
                return 0
        return 0
    
    @staticmethod
    def empty_recycle_bin():
        """تفريغ سلة المحذوفات"""
        if os.name == 'nt':
            try:
                import winshell
                winshell.recycle_bin().empty(confirm=False, show_progress=False, sound=False)
                return True, "تم تفريغ سلة المحذوفات"
            except Exception as e:
                return False, f"خطأ: {str(e)}"
        return False, "غير مدعوم على هذا النظام"

class SystemOptimizer:
    """محسن النظام"""
    
    @staticmethod
    def optimize_memory():
        """تحسين الذاكرة"""
        try:
            # تشغيل garbage collector
            import gc
            gc.collect()
            
            # محاولة تحرير ذاكرة النظام (Windows)
            if os.name == 'nt':
                try:
                    import ctypes
                    ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1, -1)
                except:
                    pass
            
            return True, "تم تحسين الذاكرة"
        except Exception as e:
            return False, f"خطأ في تحسين الذاكرة: {str(e)}"
    
    @staticmethod
    def defragment_registry():
        """إلغاء تجزئة السجل (Windows فقط)"""
        if os.name == 'nt':
            try:
                # هذه وظيفة محاكاة - في التطبيق الحقيقي تحتاج أدوات متخصصة
                return True, "تم إلغاء تجزئة السجل"
            except Exception as e:
                return False, f"خطأ: {str(e)}"
        return False, "متاح فقط على Windows"
    
    @staticmethod
    def update_system():
        """تحديث النظام"""
        try:
            if os.name == 'nt':
                # Windows Update
                subprocess.run(['powershell', 'Get-WindowsUpdate'], check=True)
                return True, "تم فحص التحديثات"
            else:
                # Linux package update
                subprocess.run(['sudo', 'apt', 'update'], check=True)
                return True, "تم تحديث قائمة الحزم"
        except Exception as e:
            return False, f"خطأ في التحديث: {str(e)}"

class EnhancedSystemToolsMainWindow(QMainWindow):
    """النافذة الرئيسية لأدوات النظام المحسنة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.system_monitor = None
        self.current_system_data = {}
        self.alerts_log = []
        self.setup_ui()
        self.setup_connections()
        self.start_monitoring()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔧 أدوات النظام المحسنة والمتطورة")
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء القوائم
        self.create_menus()

        # إنشاء شريط الأدوات
        self.create_toolbar()

        # الودجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # عنوان النظام
        title_label = QLabel("🔧 أدوات النظام المحسنة - مراقبة وإدارة وتحسين شامل")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: white;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # لوحة المعلومات السريعة
        self.create_dashboard(main_layout)

        # التبويبات الرئيسية
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                padding: 10px 15px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #007bff;
            }
        """)

        # تبويب المراقبة المباشرة
        monitoring_tab = self.create_monitoring_tab()
        self.tabs.addTab(monitoring_tab, "📊 المراقبة المباشرة")

        # تبويب إدارة العمليات
        processes_tab = self.create_processes_tab()
        self.tabs.addTab(processes_tab, "⚙️ إدارة العمليات")

        # تبويب تنظيف النظام
        cleanup_tab = self.create_cleanup_tab()
        self.tabs.addTab(cleanup_tab, "🧹 تنظيف النظام")

        # تبويب تحسين الأداء
        optimization_tab = self.create_optimization_tab()
        self.tabs.addTab(optimization_tab, "⚡ تحسين الأداء")

        # تبويب معلومات النظام
        system_info_tab = self.create_system_info_tab()
        self.tabs.addTab(system_info_tab, "💻 معلومات النظام")

        # تبويب التنبيهات والسجلات
        alerts_tab = self.create_alerts_tab()
        self.tabs.addTab(alerts_tab, "🚨 التنبيهات والسجلات")

        main_layout.addWidget(self.tabs)

        central_widget.setLayout(main_layout)

        # شريط الحالة
        self.create_status_bar()

    def create_menus(self):
        """إنشاء القوائم"""
        menubar = self.menuBar()

        # قائمة الملف
        file_menu = menubar.addMenu("📁 ملف")

        export_action = QAction("📤 تصدير التقرير", self)
        export_action.triggered.connect(self.export_system_report)
        file_menu.addAction(export_action)

        file_menu.addSeparator()

        exit_action = QAction("❌ خروج", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة الأدوات
        tools_menu = menubar.addMenu("🔧 أدوات")

        cleanup_action = QAction("🧹 تنظيف سريع", self)
        cleanup_action.triggered.connect(self.quick_cleanup)
        tools_menu.addAction(cleanup_action)

        optimize_action = QAction("⚡ تحسين سريع", self)
        optimize_action.triggered.connect(self.quick_optimization)
        tools_menu.addAction(optimize_action)

        # قائمة العرض
        view_menu = menubar.addMenu("👁️ عرض")

        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_all_data)
        view_menu.addAction(refresh_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu("❓ مساعدة")

        about_action = QAction("ℹ️ حول", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # زر التحديث
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setToolTip("تحديث جميع البيانات")
        refresh_action.triggered.connect(self.refresh_all_data)
        toolbar.addAction(refresh_action)

        toolbar.addSeparator()

        # زر التنظيف السريع
        cleanup_action = QAction("🧹 تنظيف", self)
        cleanup_action.setToolTip("تنظيف سريع للنظام")
        cleanup_action.triggered.connect(self.quick_cleanup)
        toolbar.addAction(cleanup_action)

        # زر التحسين السريع
        optimize_action = QAction("⚡ تحسين", self)
        optimize_action.setToolTip("تحسين سريع للأداء")
        optimize_action.triggered.connect(self.quick_optimization)
        toolbar.addAction(optimize_action)

        toolbar.addSeparator()

        # زر التصدير
        export_action = QAction("📤 تصدير", self)
        export_action.setToolTip("تصدير تقرير النظام")
        export_action.triggered.connect(self.export_system_report)
        toolbar.addAction(export_action)

    def create_dashboard(self, main_layout):
        """إنشاء لوحة المعلومات السريعة"""
        dashboard_frame = QFrame()
        dashboard_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 10px;
                padding: 10px;
                margin-bottom: 15px;
            }
        """)

        dashboard_layout = QHBoxLayout()

        # مؤشر المعالج
        self.cpu_gauge = self.create_gauge("🖥️ المعالج", "#007bff")
        dashboard_layout.addWidget(self.cpu_gauge)

        # مؤشر الذاكرة
        self.memory_gauge = self.create_gauge("💾 الذاكرة", "#28a745")
        dashboard_layout.addWidget(self.memory_gauge)

        # مؤشر القرص
        self.disk_gauge = self.create_gauge("💿 القرص", "#ffc107")
        dashboard_layout.addWidget(self.disk_gauge)

        # مؤشر الشبكة
        self.network_gauge = self.create_gauge("🌐 الشبكة", "#17a2b8")
        dashboard_layout.addWidget(self.network_gauge)

        # مؤشر درجة الحرارة
        self.temp_gauge = self.create_gauge("🌡️ الحرارة", "#dc3545")
        dashboard_layout.addWidget(self.temp_gauge)

        dashboard_frame.setLayout(dashboard_layout)
        main_layout.addWidget(dashboard_frame)

    def create_gauge(self, title, color):
        """إنشاء مؤشر دائري"""
        widget = QWidget()
        layout = QVBoxLayout()

        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-weight: bold;
                color: {color};
                font-size: 12px;
                margin-bottom: 5px;
            }}
        """)

        # المؤشر الدائري
        dial = QDial()
        dial.setRange(0, 100)
        dial.setValue(0)
        dial.setNotchesVisible(True)
        dial.setMaximumSize(80, 80)
        dial.setStyleSheet(f"""
            QDial {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 40px;
            }}
        """)

        # شاشة LCD للقيمة
        lcd = QLCDNumber(3)
        lcd.setMaximumHeight(30)
        lcd.setStyleSheet(f"""
            QLCDNumber {{
                background-color: {color};
                color: white;
                border-radius: 5px;
            }}
        """)

        layout.addWidget(title_label)
        layout.addWidget(dial, 0, Qt.AlignCenter)
        layout.addWidget(lcd)

        widget.setLayout(layout)

        # حفظ مراجع للتحديث
        widget.dial = dial
        widget.lcd = lcd

        return widget

    def create_monitoring_tab(self):
        """إنشاء تبويب المراقبة المباشرة"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة الرسوم البيانية
        charts_group = QGroupBox("📈 الرسوم البيانية المباشرة")
        charts_layout = QGridLayout()

        # هنا يمكن إضافة رسوم بيانية باستخدام matplotlib أو pyqtgraph
        # مؤقتاً سنستخدم نص توضيحي
        charts_placeholder = QLabel("📊 الرسوم البيانية المباشرة\n\nسيتم عرض رسوم بيانية تفاعلية هنا")
        charts_placeholder.setAlignment(Qt.AlignCenter)
        charts_placeholder.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 2px dashed #dee2e6;
                border-radius: 10px;
                padding: 50px;
                font-size: 16px;
                color: #6c757d;
            }
        """)
        charts_layout.addWidget(charts_placeholder, 0, 0, 1, 2)

        charts_group.setLayout(charts_layout)
        layout.addWidget(charts_group)

        # مجموعة الإحصائيات التفصيلية
        stats_group = QGroupBox("📊 إحصائيات تفصيلية")
        stats_layout = QGridLayout()

        # جدول الإحصائيات
        self.stats_table = QTableWidget()
        self.stats_table.setColumnCount(2)
        self.stats_table.setHorizontalHeaderLabels(["المؤشر", "القيمة"])
        self.stats_table.horizontalHeader().setStretchLastSection(True)
        self.stats_table.setAlternatingRowColors(True)
        self.stats_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #343a40;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)

        stats_layout.addWidget(self.stats_table)
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)

        tab.setLayout(layout)
        return tab

    def create_processes_tab(self):
        """إنشاء تبويب إدارة العمليات"""
        tab = QWidget()
        layout = QVBoxLayout()

        # شريط أدوات العمليات
        processes_toolbar = QHBoxLayout()

        refresh_processes_btn = QPushButton("🔄 تحديث")
        refresh_processes_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        refresh_processes_btn.clicked.connect(self.refresh_processes)

        kill_process_btn = QPushButton("❌ إنهاء العملية")
        kill_process_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        kill_process_btn.clicked.connect(self.kill_selected_process)

        # فلتر العمليات
        filter_label = QLabel("ترتيب حسب:")
        self.process_sort_combo = QComboBox()
        self.process_sort_combo.addItems(["استخدام المعالج", "استخدام الذاكرة", "الاسم"])
        self.process_sort_combo.currentTextChanged.connect(self.refresh_processes)

        processes_toolbar.addWidget(refresh_processes_btn)
        processes_toolbar.addWidget(kill_process_btn)
        processes_toolbar.addStretch()
        processes_toolbar.addWidget(filter_label)
        processes_toolbar.addWidget(self.process_sort_combo)

        layout.addLayout(processes_toolbar)

        # جدول العمليات
        self.processes_table = QTableWidget()
        self.processes_table.setColumnCount(7)
        self.processes_table.setHorizontalHeaderLabels([
            "PID", "اسم العملية", "المستخدم", "المعالج%", "الذاكرة%", "الذاكرة MB", "الحالة"
        ])

        # تنسيق الجدول
        header = self.processes_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)

        self.processes_table.setAlternatingRowColors(True)
        self.processes_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.processes_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-size: 11px;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #343a40;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.processes_table)

        tab.setLayout(layout)
        return tab

    def create_cleanup_tab(self):
        """إنشاء تبويب تنظيف النظام"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة تحليل النظام
        analysis_group = QGroupBox("🔍 تحليل النظام")
        analysis_layout = QGridLayout()

        # معلومات الملفات المؤقتة
        temp_files_label = QLabel("الملفات المؤقتة:")
        self.temp_files_info = QLabel("جاري التحليل...")
        self.temp_files_info.setStyleSheet("color: #007bff; font-weight: bold;")

        # معلومات سلة المحذوفات
        recycle_label = QLabel("سلة المحذوفات:")
        self.recycle_info = QLabel("جاري التحليل...")
        self.recycle_info.setStyleSheet("color: #007bff; font-weight: bold;")

        # زر التحليل
        analyze_btn = QPushButton("🔍 تحليل النظام")
        analyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        analyze_btn.clicked.connect(self.analyze_system)

        analysis_layout.addWidget(temp_files_label, 0, 0)
        analysis_layout.addWidget(self.temp_files_info, 0, 1)
        analysis_layout.addWidget(recycle_label, 1, 0)
        analysis_layout.addWidget(self.recycle_info, 1, 1)
        analysis_layout.addWidget(analyze_btn, 2, 0, 1, 2)

        analysis_group.setLayout(analysis_layout)
        layout.addWidget(analysis_group)

        # مجموعة عمليات التنظيف
        cleanup_group = QGroupBox("🧹 عمليات التنظيف")
        cleanup_layout = QGridLayout()

        # تنظيف الملفات المؤقتة
        clean_temp_btn = QPushButton("🗑️ تنظيف الملفات المؤقتة")
        clean_temp_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        clean_temp_btn.clicked.connect(self.clean_temp_files)

        # تفريغ سلة المحذوفات
        empty_recycle_btn = QPushButton("🗑️ تفريغ سلة المحذوفات")
        empty_recycle_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        empty_recycle_btn.clicked.connect(self.empty_recycle_bin)

        # تنظيف شامل
        full_cleanup_btn = QPushButton("🧹 تنظيف شامل")
        full_cleanup_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 15px 30px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        full_cleanup_btn.clicked.connect(self.full_cleanup)

        cleanup_layout.addWidget(clean_temp_btn, 0, 0)
        cleanup_layout.addWidget(empty_recycle_btn, 0, 1)
        cleanup_layout.addWidget(full_cleanup_btn, 1, 0, 1, 2)

        cleanup_group.setLayout(cleanup_layout)
        layout.addWidget(cleanup_group)

        # سجل التنظيف
        log_group = QGroupBox("📋 سجل التنظيف")
        log_layout = QVBoxLayout()

        self.cleanup_log = QTextEdit()
        self.cleanup_log.setMaximumHeight(150)
        self.cleanup_log.setReadOnly(True)
        self.cleanup_log.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)

        log_layout.addWidget(self.cleanup_log)
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)

        tab.setLayout(layout)
        return tab

    def create_optimization_tab(self):
        """إنشاء تبويب تحسين الأداء"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة تحسين الذاكرة
        memory_group = QGroupBox("💾 تحسين الذاكرة")
        memory_layout = QGridLayout()

        optimize_memory_btn = QPushButton("⚡ تحسين الذاكرة")
        optimize_memory_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        optimize_memory_btn.clicked.connect(self.optimize_memory)

        memory_layout.addWidget(optimize_memory_btn)
        memory_group.setLayout(memory_layout)
        layout.addWidget(memory_group)

        # مجموعة تحسين النظام
        system_group = QGroupBox("🔧 تحسين النظام")
        system_layout = QGridLayout()

        defrag_registry_btn = QPushButton("📝 إلغاء تجزئة السجل")
        defrag_registry_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        defrag_registry_btn.clicked.connect(self.defrag_registry)

        update_system_btn = QPushButton("🔄 تحديث النظام")
        update_system_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        update_system_btn.clicked.connect(self.update_system)

        system_layout.addWidget(defrag_registry_btn, 0, 0)
        system_layout.addWidget(update_system_btn, 0, 1)

        system_group.setLayout(system_layout)
        layout.addWidget(system_group)

        # مجموعة التحسين الشامل
        full_optimization_group = QGroupBox("⚡ التحسين الشامل")
        full_optimization_layout = QVBoxLayout()

        full_optimize_btn = QPushButton("🚀 تحسين شامل للنظام")
        full_optimize_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                padding: 15px 30px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e8650e;
            }
        """)
        full_optimize_btn.clicked.connect(self.full_optimization)

        full_optimization_layout.addWidget(full_optimize_btn)
        full_optimization_group.setLayout(full_optimization_layout)
        layout.addWidget(full_optimization_group)

        # سجل التحسين
        optimization_log_group = QGroupBox("📋 سجل التحسين")
        optimization_log_layout = QVBoxLayout()

        self.optimization_log = QTextEdit()
        self.optimization_log.setMaximumHeight(200)
        self.optimization_log.setReadOnly(True)
        self.optimization_log.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)

        optimization_log_layout.addWidget(self.optimization_log)
        optimization_log_group.setLayout(optimization_log_layout)
        layout.addWidget(optimization_log_group)

        tab.setLayout(layout)
        return tab

    def create_system_info_tab(self):
        """إنشاء تبويب معلومات النظام"""
        tab = QWidget()
        layout = QVBoxLayout()

        # معلومات النظام
        self.system_info_tree = QTreeWidget()
        self.system_info_tree.setHeaderLabels(["المكون", "المعلومات"])
        self.system_info_tree.setAlternatingRowColors(True)
        self.system_info_tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-size: 12px;
            }
            QTreeWidget::item {
                padding: 5px;
            }
            QHeaderView::section {
                background-color: #343a40;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)

        layout.addWidget(self.system_info_tree)

        # زر تحديث معلومات النظام
        refresh_info_btn = QPushButton("🔄 تحديث معلومات النظام")
        refresh_info_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        refresh_info_btn.clicked.connect(self.refresh_system_info)
        layout.addWidget(refresh_info_btn)

        tab.setLayout(layout)
        return tab

    def create_alerts_tab(self):
        """إنشاء تبويب التنبيهات والسجلات"""
        tab = QWidget()
        layout = QVBoxLayout()

        # إعدادات التنبيهات
        alerts_settings_group = QGroupBox("⚙️ إعدادات التنبيهات")
        alerts_settings_layout = QGridLayout()

        # حدود التنبيهات
        cpu_threshold_label = QLabel("حد تنبيه المعالج (%):")
        self.cpu_threshold_spin = QSpinBox()
        self.cpu_threshold_spin.setRange(50, 100)
        self.cpu_threshold_spin.setValue(80)

        memory_threshold_label = QLabel("حد تنبيه الذاكرة (%):")
        self.memory_threshold_spin = QSpinBox()
        self.memory_threshold_spin.setRange(50, 100)
        self.memory_threshold_spin.setValue(85)

        disk_threshold_label = QLabel("حد تنبيه القرص (%):")
        self.disk_threshold_spin = QSpinBox()
        self.disk_threshold_spin.setRange(70, 100)
        self.disk_threshold_spin.setValue(90)

        temp_threshold_label = QLabel("حد تنبيه الحرارة (°C):")
        self.temp_threshold_spin = QSpinBox()
        self.temp_threshold_spin.setRange(50, 100)
        self.temp_threshold_spin.setValue(70)

        # زر تطبيق الإعدادات
        apply_settings_btn = QPushButton("✅ تطبيق الإعدادات")
        apply_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        apply_settings_btn.clicked.connect(self.apply_alert_settings)

        alerts_settings_layout.addWidget(cpu_threshold_label, 0, 0)
        alerts_settings_layout.addWidget(self.cpu_threshold_spin, 0, 1)
        alerts_settings_layout.addWidget(memory_threshold_label, 0, 2)
        alerts_settings_layout.addWidget(self.memory_threshold_spin, 0, 3)
        alerts_settings_layout.addWidget(disk_threshold_label, 1, 0)
        alerts_settings_layout.addWidget(self.disk_threshold_spin, 1, 1)
        alerts_settings_layout.addWidget(temp_threshold_label, 1, 2)
        alerts_settings_layout.addWidget(self.temp_threshold_spin, 1, 3)
        alerts_settings_layout.addWidget(apply_settings_btn, 2, 0, 1, 4)

        alerts_settings_group.setLayout(alerts_settings_layout)
        layout.addWidget(alerts_settings_group)

        # سجل التنبيهات
        alerts_log_group = QGroupBox("🚨 سجل التنبيهات")
        alerts_log_layout = QVBoxLayout()

        self.alerts_list = QListWidget()
        self.alerts_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QListWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)

        # أزرار إدارة السجل
        log_buttons_layout = QHBoxLayout()

        clear_log_btn = QPushButton("🗑️ مسح السجل")
        clear_log_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        clear_log_btn.clicked.connect(self.clear_alerts_log)

        export_log_btn = QPushButton("📤 تصدير السجل")
        export_log_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        export_log_btn.clicked.connect(self.export_alerts_log)

        log_buttons_layout.addWidget(clear_log_btn)
        log_buttons_layout.addWidget(export_log_btn)
        log_buttons_layout.addStretch()

        alerts_log_layout.addWidget(self.alerts_list)
        alerts_log_layout.addLayout(log_buttons_layout)

        alerts_log_group.setLayout(alerts_log_layout)
        layout.addWidget(alerts_log_group)

        tab.setLayout(layout)
        return tab

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()

        # تسمية الحالة
        self.status_label = QLabel("جاهز")
        status_bar.addWidget(self.status_label)

        # مؤشر المراقبة
        self.monitoring_indicator = QLabel("🔴 المراقبة متوقفة")
        status_bar.addPermanentWidget(self.monitoring_indicator)

        # الوقت الحالي
        self.time_label = QLabel()
        self.update_time()
        status_bar.addPermanentWidget(self.time_label)

        # مؤقت تحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)

    def setup_connections(self):
        """إعداد الاتصالات"""
        pass

    def start_monitoring(self):
        """بدء مراقبة النظام"""
        self.system_monitor = SystemResourceMonitor()
        self.system_monitor.data_updated.connect(self.update_system_data)
        self.system_monitor.alert_triggered.connect(self.handle_alert)
        self.system_monitor.start()

        self.monitoring_indicator.setText("🟢 المراقبة نشطة")
        self.status_label.setText("تم بدء مراقبة النظام")

        # تحديث معلومات النظام
        self.refresh_system_info()
        self.refresh_processes()
        self.analyze_system()

    def update_system_data(self, data):
        """تحديث بيانات النظام"""
        self.current_system_data = data

        # تحديث المؤشرات الدائرية
        self.cpu_gauge.dial.setValue(int(data['cpu_percent']))
        self.cpu_gauge.lcd.display(f"{data['cpu_percent']:.1f}")

        self.memory_gauge.dial.setValue(int(data['memory']['percent']))
        self.memory_gauge.lcd.display(f"{data['memory']['percent']:.1f}")

        self.disk_gauge.dial.setValue(int(data['disk']['percent']))
        self.disk_gauge.lcd.display(f"{data['disk']['percent']:.1f}")

        # مؤشر الشبكة (سرعة الإرسال كنسبة مئوية من 100 ميجابايت/ثانية)
        network_percent = min(100, (data['network']['speed_sent'] / (100 * 1024 * 1024)) * 100)
        self.network_gauge.dial.setValue(int(network_percent))
        self.network_gauge.lcd.display(f"{network_percent:.1f}")

        # مؤشر درجة الحرارة
        if data['temperature'] > 0:
            temp_percent = min(100, (data['temperature'] / 100) * 100)
            self.temp_gauge.dial.setValue(int(temp_percent))
            self.temp_gauge.lcd.display(f"{data['temperature']:.1f}")

        # تحديث جدول الإحصائيات
        self.update_stats_table(data)

    def update_stats_table(self, data):
        """تحديث جدول الإحصائيات"""
        stats = [
            ("استخدام المعالج", f"{data['cpu_percent']:.1f}%"),
            ("عدد أنوية المعالج", str(data['cpu_count'])),
            ("تردد المعالج", f"{data['cpu_freq'].get('current', 0):.0f} MHz" if data['cpu_freq'] else "غير متاح"),
            ("إجمالي الذاكرة", f"{data['memory']['total'] / (1024**3):.1f} GB"),
            ("الذاكرة المستخدمة", f"{data['memory']['used'] / (1024**3):.1f} GB"),
            ("الذاكرة المتاحة", f"{data['memory']['available'] / (1024**3):.1f} GB"),
            ("نسبة استخدام الذاكرة", f"{data['memory']['percent']:.1f}%"),
            ("إجمالي مساحة القرص", f"{data['disk']['total'] / (1024**3):.1f} GB"),
            ("المساحة المستخدمة", f"{data['disk']['used'] / (1024**3):.1f} GB"),
            ("المساحة المتاحة", f"{data['disk']['free'] / (1024**3):.1f} GB"),
            ("نسبة استخدام القرص", f"{data['disk']['percent']:.1f}%"),
            ("البيانات المرسلة", f"{data['network']['bytes_sent'] / (1024**2):.1f} MB"),
            ("البيانات المستقبلة", f"{data['network']['bytes_recv'] / (1024**2):.1f} MB"),
            ("سرعة الإرسال", f"{data['network']['speed_sent'] / 1024:.1f} KB/s"),
            ("سرعة الاستقبال", f"{data['network']['speed_recv'] / 1024:.1f} KB/s"),
            ("عدد العمليات", str(data['processes_count'])),
            ("وقت تشغيل النظام", f"{data['uptime'] / 3600:.1f} ساعة"),
        ]

        if data['temperature'] > 0:
            stats.append(("درجة الحرارة", f"{data['temperature']:.1f}°C"))

        if data['battery']:
            stats.extend([
                ("مستوى البطارية", f"{data['battery']['percent']:.1f}%"),
                ("حالة الشحن", "متصل" if data['battery']['plugged'] else "غير متصل"),
            ])
            if data['battery']['time_left']:
                stats.append(("الوقت المتبقي", f"{data['battery']['time_left'] / 3600:.1f} ساعة"))

        self.stats_table.setRowCount(len(stats))
        for i, (key, value) in enumerate(stats):
            self.stats_table.setItem(i, 0, QTableWidgetItem(key))
            self.stats_table.setItem(i, 1, QTableWidgetItem(value))

    def handle_alert(self, alert_type, message, level):
        """معالجة التنبيهات"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        alert_text = f"[{timestamp}] {message}"

        # إضافة للسجل
        self.alerts_log.append({
            'timestamp': timestamp,
            'type': alert_type,
            'message': message,
            'level': level
        })

        # إضافة لقائمة التنبيهات
        item = QListWidgetItem(alert_text)
        if level == 'critical':
            item.setBackground(QColor("#dc3545"))
            item.setForeground(QColor("white"))
        elif level == 'warning':
            item.setBackground(QColor("#ffc107"))
            item.setForeground(QColor("#212529"))

        self.alerts_list.insertItem(0, item)

        # الحد الأقصى للعناصر في القائمة
        if self.alerts_list.count() > 100:
            self.alerts_list.takeItem(self.alerts_list.count() - 1)

        # تحديث شريط الحالة
        self.status_label.setText(f"تنبيه: {message}")

    def refresh_processes(self):
        """تحديث قائمة العمليات"""
        try:
            sort_map = {
                "استخدام المعالج": "cpu",
                "استخدام الذاكرة": "memory",
                "الاسم": "name"
            }
            sort_by = sort_map.get(self.process_sort_combo.currentText(), "cpu")

            processes = ProcessManager.get_processes_list(sort_by=sort_by, limit=50)

            self.processes_table.setRowCount(len(processes))

            for i, proc in enumerate(processes):
                self.processes_table.setItem(i, 0, QTableWidgetItem(str(proc['pid'])))
                self.processes_table.setItem(i, 1, QTableWidgetItem(proc['name'] or ''))
                self.processes_table.setItem(i, 2, QTableWidgetItem(proc['username'] or ''))
                self.processes_table.setItem(i, 3, QTableWidgetItem(f"{proc['cpu_percent'] or 0:.1f}%"))
                self.processes_table.setItem(i, 4, QTableWidgetItem(f"{proc['memory_percent'] or 0:.1f}%"))
                self.processes_table.setItem(i, 5, QTableWidgetItem(f"{proc['memory_mb']:.1f}"))
                self.processes_table.setItem(i, 6, QTableWidgetItem(proc['status'] or ''))

            self.status_label.setText(f"تم تحديث {len(processes)} عملية")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث العمليات:\n{str(e)}")

    def kill_selected_process(self):
        """إنهاء العملية المحددة"""
        current_row = self.processes_table.currentRow()
        if current_row >= 0:
            pid_item = self.processes_table.item(current_row, 0)
            name_item = self.processes_table.item(current_row, 1)

            if pid_item and name_item:
                pid = int(pid_item.text())
                name = name_item.text()

                reply = QMessageBox.question(
                    self, "تأكيد إنهاء العملية",
                    f"هل أنت متأكد من إنهاء العملية:\n{name} (PID: {pid})?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )

                if reply == QMessageBox.Yes:
                    success, message = ProcessManager.kill_process(pid)
                    if success:
                        QMessageBox.information(self, "نجح", message)
                        self.refresh_processes()
                    else:
                        QMessageBox.warning(self, "فشل", message)
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عملية لإنهائها")

    def analyze_system(self):
        """تحليل النظام"""
        try:
            # تحليل الملفات المؤقتة
            temp_size, temp_count = SystemCleaner.get_temp_files_size()
            self.temp_files_info.setText(f"{temp_count} ملف - {temp_size / (1024**2):.1f} MB")

            # تحليل سلة المحذوفات
            recycle_size = SystemCleaner.get_recycle_bin_size()
            self.recycle_info.setText(f"{recycle_size / (1024**2):.1f} MB")

            self.status_label.setText("تم تحليل النظام")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحليل النظام:\n{str(e)}")

    def clean_temp_files(self):
        """تنظيف الملفات المؤقتة"""
        try:
            reply = QMessageBox.question(
                self, "تأكيد التنظيف",
                "هل أنت متأكد من حذف جميع الملفات المؤقتة؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                deleted_files, freed_space, errors = SystemCleaner.clean_temp_files()

                log_message = f"[{datetime.now().strftime('%H:%M:%S')}] تم حذف {deleted_files} ملف وتحرير {freed_space / (1024**2):.1f} MB"
                self.cleanup_log.append(log_message)

                if errors:
                    log_message += f"\nأخطاء: {len(errors)}"
                    self.cleanup_log.append(f"الأخطاء: {errors[:5]}")  # أول 5 أخطاء فقط

                QMessageBox.information(self, "تم التنظيف",
                    f"تم حذف {deleted_files} ملف\nتم تحرير {freed_space / (1024**2):.1f} MB")

                self.analyze_system()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تنظيف الملفات المؤقتة:\n{str(e)}")

    def empty_recycle_bin(self):
        """تفريغ سلة المحذوفات"""
        try:
            reply = QMessageBox.question(
                self, "تأكيد التفريغ",
                "هل أنت متأكد من تفريغ سلة المحذوفات؟\nلا يمكن التراجع عن هذا الإجراء.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                success, message = SystemCleaner.empty_recycle_bin()

                log_message = f"[{datetime.now().strftime('%H:%M:%S')}] {message}"
                self.cleanup_log.append(log_message)

                if success:
                    QMessageBox.information(self, "تم التفريغ", message)
                else:
                    QMessageBox.warning(self, "فشل", message)

                self.analyze_system()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تفريغ سلة المحذوفات:\n{str(e)}")

    def full_cleanup(self):
        """تنظيف شامل"""
        try:
            reply = QMessageBox.question(
                self, "تأكيد التنظيف الشامل",
                "هل أنت متأكد من التنظيف الشامل؟\nسيتم:\n- حذف الملفات المؤقتة\n- تفريغ سلة المحذوفات",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                log_message = f"[{datetime.now().strftime('%H:%M:%S')}] بدء التنظيف الشامل"
                self.cleanup_log.append(log_message)

                # تنظيف الملفات المؤقتة
                deleted_files, freed_space, errors = SystemCleaner.clean_temp_files()
                log_message = f"الملفات المؤقتة: {deleted_files} ملف - {freed_space / (1024**2):.1f} MB"
                self.cleanup_log.append(log_message)

                # تفريغ سلة المحذوفات
                success, message = SystemCleaner.empty_recycle_bin()
                self.cleanup_log.append(f"سلة المحذوفات: {message}")

                total_message = f"تم التنظيف الشامل:\n- حذف {deleted_files} ملف مؤقت\n- تحرير {freed_space / (1024**2):.1f} MB\n- {message}"
                QMessageBox.information(self, "تم التنظيف الشامل", total_message)

                self.analyze_system()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في التنظيف الشامل:\n{str(e)}")

    def optimize_memory(self):
        """تحسين الذاكرة"""
        try:
            success, message = SystemOptimizer.optimize_memory()

            log_message = f"[{datetime.now().strftime('%H:%M:%S')}] {message}"
            self.optimization_log.append(log_message)

            if success:
                QMessageBox.information(self, "تم التحسين", message)
            else:
                QMessageBox.warning(self, "فشل", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحسين الذاكرة:\n{str(e)}")

    def defrag_registry(self):
        """إلغاء تجزئة السجل"""
        try:
            success, message = SystemOptimizer.defragment_registry()

            log_message = f"[{datetime.now().strftime('%H:%M:%S')}] {message}"
            self.optimization_log.append(log_message)

            if success:
                QMessageBox.information(self, "تم التحسين", message)
            else:
                QMessageBox.warning(self, "فشل", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إلغاء تجزئة السجل:\n{str(e)}")

    def update_system(self):
        """تحديث النظام"""
        try:
            success, message = SystemOptimizer.update_system()

            log_message = f"[{datetime.now().strftime('%H:%M:%S')}] {message}"
            self.optimization_log.append(log_message)

            if success:
                QMessageBox.information(self, "تم التحديث", message)
            else:
                QMessageBox.warning(self, "فشل", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث النظام:\n{str(e)}")

    def full_optimization(self):
        """تحسين شامل"""
        try:
            reply = QMessageBox.question(
                self, "تأكيد التحسين الشامل",
                "هل أنت متأكد من التحسين الشامل؟\nسيتم:\n- تحسين الذاكرة\n- إلغاء تجزئة السجل\n- فحص التحديثات",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                log_message = f"[{datetime.now().strftime('%H:%M:%S')}] بدء التحسين الشامل"
                self.optimization_log.append(log_message)

                # تحسين الذاكرة
                success1, message1 = SystemOptimizer.optimize_memory()
                self.optimization_log.append(f"الذاكرة: {message1}")

                # إلغاء تجزئة السجل
                success2, message2 = SystemOptimizer.defragment_registry()
                self.optimization_log.append(f"السجل: {message2}")

                # تحديث النظام
                success3, message3 = SystemOptimizer.update_system()
                self.optimization_log.append(f"التحديث: {message3}")

                total_message = f"تم التحسين الشامل:\n- {message1}\n- {message2}\n- {message3}"
                QMessageBox.information(self, "تم التحسين الشامل", total_message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في التحسين الشامل:\n{str(e)}")

    def refresh_system_info(self):
        """تحديث معلومات النظام"""
        try:
            self.system_info_tree.clear()

            # معلومات النظام الأساسية
            system_item = QTreeWidgetItem(["💻 النظام", ""])
            system_item.addChild(QTreeWidgetItem(["نظام التشغيل", platform.system()]))
            system_item.addChild(QTreeWidgetItem(["إصدار النظام", platform.release()]))
            system_item.addChild(QTreeWidgetItem(["معمارية النظام", platform.architecture()[0]]))
            system_item.addChild(QTreeWidgetItem(["اسم الجهاز", platform.node()]))
            system_item.addChild(QTreeWidgetItem(["المعالج", platform.processor()]))
            self.system_info_tree.addTopLevelItem(system_item)

            # معلومات المعالج
            cpu_item = QTreeWidgetItem(["🖥️ المعالج", ""])
            cpu_item.addChild(QTreeWidgetItem(["عدد الأنوية", str(psutil.cpu_count(logical=False))]))
            cpu_item.addChild(QTreeWidgetItem(["عدد الخيوط", str(psutil.cpu_count(logical=True))]))

            cpu_freq = psutil.cpu_freq()
            if cpu_freq:
                cpu_item.addChild(QTreeWidgetItem(["التردد الحالي", f"{cpu_freq.current:.0f} MHz"]))
                cpu_item.addChild(QTreeWidgetItem(["الحد الأدنى للتردد", f"{cpu_freq.min:.0f} MHz"]))
                cpu_item.addChild(QTreeWidgetItem(["الحد الأقصى للتردد", f"{cpu_freq.max:.0f} MHz"]))

            self.system_info_tree.addTopLevelItem(cpu_item)

            # معلومات الذاكرة
            memory = psutil.virtual_memory()
            memory_item = QTreeWidgetItem(["💾 الذاكرة", ""])
            memory_item.addChild(QTreeWidgetItem(["الإجمالي", f"{memory.total / (1024**3):.2f} GB"]))
            memory_item.addChild(QTreeWidgetItem(["المتاح", f"{memory.available / (1024**3):.2f} GB"]))
            memory_item.addChild(QTreeWidgetItem(["المستخدم", f"{memory.used / (1024**3):.2f} GB"]))
            memory_item.addChild(QTreeWidgetItem(["النسبة المئوية", f"{memory.percent:.1f}%"]))
            self.system_info_tree.addTopLevelItem(memory_item)

            # معلومات الأقراص
            disks_item = QTreeWidgetItem(["💿 الأقراص", ""])
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_item = QTreeWidgetItem([partition.device, ""])
                    disk_item.addChild(QTreeWidgetItem(["نقطة التحميل", partition.mountpoint]))
                    disk_item.addChild(QTreeWidgetItem(["نظام الملفات", partition.fstype]))
                    disk_item.addChild(QTreeWidgetItem(["الحجم الإجمالي", f"{usage.total / (1024**3):.2f} GB"]))
                    disk_item.addChild(QTreeWidgetItem(["المستخدم", f"{usage.used / (1024**3):.2f} GB"]))
                    disk_item.addChild(QTreeWidgetItem(["المتاح", f"{usage.free / (1024**3):.2f} GB"]))
                    disk_item.addChild(QTreeWidgetItem(["النسبة المئوية", f"{(usage.used/usage.total)*100:.1f}%"]))
                    disks_item.addChild(disk_item)
                except PermissionError:
                    continue
            self.system_info_tree.addTopLevelItem(disks_item)

            # معلومات الشبكة
            network_item = QTreeWidgetItem(["🌐 الشبكة", ""])
            net_io = psutil.net_io_counters()
            network_item.addChild(QTreeWidgetItem(["البيانات المرسلة", f"{net_io.bytes_sent / (1024**2):.2f} MB"]))
            network_item.addChild(QTreeWidgetItem(["البيانات المستقبلة", f"{net_io.bytes_recv / (1024**2):.2f} MB"]))
            network_item.addChild(QTreeWidgetItem(["الحزم المرسلة", str(net_io.packets_sent)]))
            network_item.addChild(QTreeWidgetItem(["الحزم المستقبلة", str(net_io.packets_recv)]))

            # واجهات الشبكة
            for interface, addresses in psutil.net_if_addrs().items():
                interface_item = QTreeWidgetItem([interface, ""])
                for addr in addresses:
                    if addr.family.name == 'AF_INET':
                        interface_item.addChild(QTreeWidgetItem(["IPv4", addr.address]))
                    elif addr.family.name == 'AF_INET6':
                        interface_item.addChild(QTreeWidgetItem(["IPv6", addr.address]))
                network_item.addChild(interface_item)

            self.system_info_tree.addTopLevelItem(network_item)

            # توسيع جميع العناصر
            self.system_info_tree.expandAll()

            self.status_label.setText("تم تحديث معلومات النظام")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث معلومات النظام:\n{str(e)}")

    def apply_alert_settings(self):
        """تطبيق إعدادات التنبيهات"""
        if self.system_monitor:
            self.system_monitor.set_alert_threshold('cpu', self.cpu_threshold_spin.value())
            self.system_monitor.set_alert_threshold('memory', self.memory_threshold_spin.value())
            self.system_monitor.set_alert_threshold('disk', self.disk_threshold_spin.value())
            self.system_monitor.set_alert_threshold('temperature', self.temp_threshold_spin.value())

            QMessageBox.information(self, "تم التطبيق", "تم تطبيق إعدادات التنبيهات بنجاح")
            self.status_label.setText("تم تطبيق إعدادات التنبيهات")

    def clear_alerts_log(self):
        """مسح سجل التنبيهات"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح جميع التنبيهات؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.alerts_list.clear()
            self.alerts_log.clear()
            self.status_label.setText("تم مسح سجل التنبيهات")

    def export_alerts_log(self):
        """تصدير سجل التنبيهات"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير سجل التنبيهات",
                f"alerts_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "Text Files (*.txt);;All Files (*)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("سجل تنبيهات أدوات النظام\n")
                    f.write("=" * 50 + "\n\n")

                    for alert in self.alerts_log:
                        f.write(f"[{alert['timestamp']}] {alert['type'].upper()}: {alert['message']}\n")

                QMessageBox.information(self, "تم التصدير", f"تم تصدير السجل إلى:\n{file_path}")
                self.status_label.setText("تم تصدير سجل التنبيهات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير السجل:\n{str(e)}")

    def quick_cleanup(self):
        """تنظيف سريع"""
        self.full_cleanup()

    def quick_optimization(self):
        """تحسين سريع"""
        self.optimize_memory()

    def export_system_report(self):
        """تصدير تقرير النظام"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير تقرير النظام",
                f"system_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "Text Files (*.txt);;All Files (*)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("تقرير أدوات النظام المحسنة\n")
                    f.write("=" * 50 + "\n")
                    f.write(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                    # معلومات النظام
                    f.write("معلومات النظام:\n")
                    f.write("-" * 20 + "\n")
                    f.write(f"نظام التشغيل: {platform.system()} {platform.release()}\n")
                    f.write(f"المعالج: {platform.processor()}\n")
                    f.write(f"عدد الأنوية: {psutil.cpu_count()}\n")

                    memory = psutil.virtual_memory()
                    f.write(f"إجمالي الذاكرة: {memory.total / (1024**3):.2f} GB\n")

                    # البيانات الحالية
                    if self.current_system_data:
                        data = self.current_system_data
                        f.write(f"\nالحالة الحالية:\n")
                        f.write("-" * 20 + "\n")
                        f.write(f"استخدام المعالج: {data['cpu_percent']:.1f}%\n")
                        f.write(f"استخدام الذاكرة: {data['memory']['percent']:.1f}%\n")
                        f.write(f"استخدام القرص: {data['disk']['percent']:.1f}%\n")
                        f.write(f"عدد العمليات: {data['processes_count']}\n")
                        f.write(f"وقت التشغيل: {data['uptime'] / 3600:.1f} ساعة\n")

                    # سجل التنبيهات
                    if self.alerts_log:
                        f.write(f"\nسجل التنبيهات ({len(self.alerts_log)} تنبيه):\n")
                        f.write("-" * 20 + "\n")
                        for alert in self.alerts_log[-10:]:  # آخر 10 تنبيهات
                            f.write(f"[{alert['timestamp']}] {alert['message']}\n")

                QMessageBox.information(self, "تم التصدير", f"تم تصدير التقرير إلى:\n{file_path}")
                self.status_label.setText("تم تصدير تقرير النظام")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        self.refresh_system_info()
        self.refresh_processes()
        self.analyze_system()
        self.status_label.setText("تم تحديث جميع البيانات")

    def update_time(self):
        """تحديث الوقت"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = """
🔧 أدوات النظام المحسنة والمتطورة

الإصدار: 2.0
المطور: نظام إدارة الحسابات المتقدم

الميزات:
• مراقبة مباشرة لموارد النظام
• إدارة متقدمة للعمليات
• تنظيف وتحسين شامل للنظام
• تنبيهات ذكية ومخصصة
• تقارير مفصلة وقابلة للتصدير
• واجهة عربية احترافية

© 2024 جميع الحقوق محفوظة
        """
        QMessageBox.about(self, "حول أدوات النظام", about_text)

    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        if self.system_monitor:
            self.system_monitor.stop()
            self.system_monitor.wait()
        event.accept()


def main():
    """تشغيل أدوات النظام المحسنة"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # تحقق من توفر psutil
    try:
        import psutil
    except ImportError:
        QMessageBox.critical(None, "خطأ",
            "مكتبة psutil غير مثبتة.\nيرجى تثبيتها باستخدام:\npip install psutil")
        sys.exit(1)

    window = EnhancedSystemToolsMainWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
