#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعدادات النظام
System Configuration
"""

import os
import json
from typing import Dict, Any

class Config:
    """فئة إدارة إعدادات النظام"""
    
    def __init__(self):
        self.config_file = "config.json"
        self.default_config = {
            "database": {
                "host": "localhost",
                "port": 3306,
                "user": "root",
                "password": "",
                "database": "accounting_system"
            },
            "ui": {
                "language": "ar",  # ar, en
                "theme": "light",  # light, dark
                "font_size": 10
            },
            "company": {
                "name": "شركة المحاسبة",
                "name_en": "Accounting Company",
                "address": "",
                "phone": "",
                "email": "",
                "tax_number": "",
                "logo_path": ""
            },
            "accounting": {
                "currency": "SAR",
                "currency_symbol": "ر.س",
                "decimal_places": 2,
                "tax_rate": 15.0,
                "fiscal_year_start": "01-01",
                "backup_interval": 24  # hours
            }
        }
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """تحميل الإعدادات من الملف"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # دمج الإعدادات الافتراضية مع المحملة
                return self._merge_configs(self.default_config, config)
            except Exception as e:
                print(f"خطأ في تحميل الإعدادات: {e}")
                return self.default_config.copy()
        else:
            # إنشاء ملف الإعدادات الافتراضي
            self.save_config(self.default_config)
            return self.default_config.copy()
    
    def save_config(self, config: Dict[str, Any] = None):
        """حفظ الإعدادات في الملف"""
        if config is None:
            config = self.config
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
    
    def get(self, key: str, default=None):
        """الحصول على قيمة إعداد"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """تعيين قيمة إعداد"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        
        # حفظ الإعدادات
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
    
    def _merge_configs(self, default: Dict, loaded: Dict) -> Dict:
        """دمج الإعدادات الافتراضية مع المحملة"""
        result = default.copy()
        
        for key, value in loaded.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result

# إنشاء مثيل عام للإعدادات
config = Config()