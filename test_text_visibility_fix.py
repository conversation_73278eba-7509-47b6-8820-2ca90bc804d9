#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة ظهور النص في خانة المبلغ والخط الأزرق
Text Visibility Fix Test for Amount Field and Blue Line Issue
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QPushButton, QWidget, QLabel, QMessageBox, QTextEdit
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class TextVisibilityFixTestWindow(QMainWindow):
    """نافذة اختبار إصلاح مشكلة ظهور النص"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_mock_db()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار إصلاح مشكلة ظهور النص في خانة المبلغ والخط الأزرق")
        self.setGeometry(200, 200, 1000, 700)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QMainWindow {
                background-color: #fdf2f8;
                font-family: "Tahoma", "Arial", sans-serif;
            }
            QPushButton {
                background-color: #e91e63;
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                margin: 10px;
                min-width: 300px;
            }
            QPushButton:hover {
                background-color: #c2185b;
            }
            QPushButton:pressed {
                background-color: #ad1457;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 14px;
                color: #2c3e50;
                padding: 10px;
            }
            QTextEdit {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 12px;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🔧 اختبار إصلاح مشكلة ظهور النص والخط الأزرق")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 22px;
                color: #e91e63;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 10px;
                padding: 20px;
                margin: 20px;
                text-align: center;
            }
        """)
        layout.addWidget(title_label)
        
        # أزرار الاختبار
        buttons_layout = QVBoxLayout()
        
        # اختبار خانة المبلغ
        amount_test_btn = QPushButton("💰 اختبار خانة المبلغ - ظهور النص أثناء الكتابة")
        amount_test_btn.clicked.connect(self.test_amount_field)
        buttons_layout.addWidget(amount_test_btn)
        
        # اختبار حقل رقم الصندوق
        cashbox_test_btn = QPushButton("🏦 اختبار حقل رقم الصندوق - إزالة الخط الأزرق")
        cashbox_test_btn.clicked.connect(self.test_cashbox_field)
        buttons_layout.addWidget(cashbox_test_btn)
        
        # اختبار شامل
        comprehensive_btn = QPushButton("🔍 اختبار شامل - جميع الحقول")
        comprehensive_btn.clicked.connect(self.test_comprehensive)
        buttons_layout.addWidget(comprehensive_btn)
        
        layout.addLayout(buttons_layout)
        
        # معلومات الإصلاحات
        info = QTextEdit()
        info.setReadOnly(True)
        info.setMaximumHeight(350)
        info.setHtml("""
        <h3 style="color: #e91e63;">🔧 الإصلاحات المطبقة:</h3>
        
        <h4 style="color: #27ae60;">✅ إصلاح مشكلة خانة المبلغ:</h4>
        <ul>
            <li><strong>لون النص الثابت:</strong> #2c3e50 !important</li>
            <li><strong>خلفية واضحة:</strong> أبيض أثناء الكتابة</li>
            <li><strong>تنسيق Focus:</strong> خلفية زرقاء فاتحة مع نص أسود</li>
            <li><strong>خط محسن:</strong> Tahoma 11px Bold للمبالغ</li>
            <li><strong>محاذاة يمين:</strong> للأرقام العربية</li>
        </ul>
        
        <h4 style="color: #3498db;">🏦 إصلاح مشكلة حقل رقم الصندوق:</h4>
        <ul>
            <li><strong>إزالة الخط الأزرق:</strong> outline: none</li>
            <li><strong>حدود واضحة:</strong> رمادي عادي، أزرق عند التركيز</li>
            <li><strong>لون نص ثابت:</strong> #2c3e50 !important</li>
            <li><strong>تأثير Hover:</strong> حدود زرقاء فاتحة</li>
        </ul>
        
        <h4 style="color: #9b59b6;">📊 تحسينات الجدول:</h4>
        <ul>
            <li><strong>نص واضح:</strong> في جميع حالات التركيز</li>
            <li><strong>خلفية محسنة:</strong> أثناء التحديد والتركيز</li>
            <li><strong>ألوان متناوبة:</strong> للصفوف</li>
            <li><strong>تأثيرات Hover:</strong> لسهولة التنقل</li>
        </ul>
        
        <h4 style="color: #e74c3c;">🎯 المشاكل المحلولة:</h4>
        <table border="1" style="border-collapse: collapse; width: 100%; font-size: 11px;">
            <tr style="background-color: #34495e; color: white;">
                <th>المشكلة</th>
                <th>السبب</th>
                <th>الحل</th>
            </tr>
            <tr>
                <td>النص لا يظهر أثناء الكتابة في المبلغ</td>
                <td>تضارب في ألوان CSS</td>
                <td>color: #2c3e50 !important</td>
            </tr>
            <tr>
                <td>خط أزرق في حقل رقم الصندوق</td>
                <td>outline افتراضي من المتصفح</td>
                <td>outline: none + تنسيق مخصص</td>
            </tr>
            <tr>
                <td>عدم وضوح النص عند التركيز</td>
                <td>ألوان متضاربة</td>
                <td>تنسيق :focus محدد</td>
            </tr>
        </table>
        
        <h4 style="color: #f39c12;">🧪 خطوات الاختبار:</h4>
        <ol>
            <li><strong>اختبار خانة المبلغ:</strong>
                <ul>
                    <li>انقر في خانة المبلغ</li>
                    <li>ابدأ الكتابة فوراً</li>
                    <li>تحقق من ظهور النص أثناء الكتابة</li>
                    <li>انتقل لخانة أخرى وعد</li>
                </ul>
            </li>
            <li><strong>اختبار حقل رقم الصندوق:</strong>
                <ul>
                    <li>انقر في حقل رقم الصندوق</li>
                    <li>تحقق من عدم وجود خط أزرق</li>
                    <li>اكتب رقم صندوق</li>
                    <li>تحقق من وضوح النص</li>
                </ul>
            </li>
            <li><strong>اختبار التنقل:</strong>
                <ul>
                    <li>استخدم Tab للتنقل</li>
                    <li>تحقق من تأثيرات Focus</li>
                    <li>جرب الكتابة في كل حقل</li>
                </ul>
            </li>
        </ol>
        
        <h4 style="color: #17a2b8;">💡 نصائح للاختبار:</h4>
        <ul>
            <li>✅ <strong>اكتب مبالغ مختلفة:</strong> 1000, 15000.50, 999999</li>
            <li>✅ <strong>جرب أرقام صناديق:</strong> 1010001, 1020001</li>
            <li>✅ <strong>استخدم اختصارات:</strong> Tab, Enter, F9</li>
            <li>✅ <strong>تحقق من الألوان:</strong> أثناء الكتابة والتركيز</li>
            <li>✅ <strong>اختبر على شاشات مختلفة:</strong> للتأكد من الوضوح</li>
        </ul>
        
        <h4 style="color: #6f42c1;">🎨 التنسيقات المطبقة:</h4>
        <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 10px;">
/* خانة المبلغ */
QTableWidget::item:focus {
    background-color: #ebf3fd;
    color: #2c3e50 !important;
    border: 2px solid #3498db;
    outline: none;
}

/* حقل رقم الصندوق */
QLineEdit:focus {
    border: 2px solid #3498db;
    background-color: #ebf3fd;
    color: #2c3e50 !important;
    outline: none;
}
        </pre>
        """)
        layout.addWidget(info)
        
        central_widget.setLayout(layout)
    
    def setup_mock_db(self):
        """إعداد قاعدة بيانات وهمية للاختبار"""
        class MockDBManager:
            def __init__(self):
                self.cursor = type('obj', (object,), {'lastrowid': 1})()
            
            def execute_query(self, query, params=None):
                if "MAX" in query:
                    return [(0,)]
                elif "chart_of_accounts" in query:
                    return [
                        ('1010001', 'صندوق المركز الرئيسي', 'أصول متداولة', 1, 3, '101000'),
                        ('1020001', 'البنك الأهلي التجاري', 'أصول متداولة', 1, 3, '102000'),
                        ('1030001', 'العملاء المحليين', 'أصول متداولة', 1, 3, '103000'),
                        ('4010001', 'مبيعات محلية', 'إيرادات', 1, 3, '401000'),
                        ('5010001', 'مصروفات عمومية', 'مصروفات', 1, 3, '501000'),
                    ]
                return []
            
            def execute_update(self, query, params=None):
                return True
        
        self.db_manager = MockDBManager()
    
    def test_amount_field(self):
        """اختبار خانة المبلغ"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            dialog = VoucherInterface(self.db_manager, "receipt", self)
            
            # إضافة صف للاختبار
            dialog.add_detail_row()
            
            # التركيز على خانة المبلغ
            dialog.details_table.setCurrentCell(0, 4)
            dialog.details_table.setFocus()
            
            dialog.show()
            
            QMessageBox.information(
                self, 
                "اختبار خانة المبلغ",
                "✅ تم فتح السند لاختبار خانة المبلغ\n\n"
                "🔍 خطوات الاختبار:\n"
                "1. انقر في خانة المبلغ (العمود الخامس)\n"
                "2. ابدأ الكتابة فوراً: 15000.50\n"
                "3. تحقق من ظهور النص أثناء الكتابة\n"
                "4. اضغط Tab للانتقال لخانة أخرى\n"
                "5. عد لخانة المبلغ مرة أخرى\n\n"
                "✅ المتوقع:\n"
                "• النص يظهر فوراً أثناء الكتابة\n"
                "• لون النص أسود واضح\n"
                "• خلفية زرقاء فاتحة عند التركيز\n"
                "• محاذاة يمين للأرقام"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في اختبار خانة المبلغ:\n{str(e)}")
    
    def test_cashbox_field(self):
        """اختبار حقل رقم الصندوق"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            dialog = VoucherInterface(self.db_manager, "payment", self)
            
            # التركيز على حقل رقم الصندوق
            dialog.cashbox_code_edit.setFocus()
            
            dialog.show()
            
            QMessageBox.information(
                self, 
                "اختبار حقل رقم الصندوق",
                "✅ تم فتح السند لاختبار حقل رقم الصندوق\n\n"
                "🔍 خطوات الاختبار:\n"
                "1. انقر في حقل 'رقم الصندوق'\n"
                "2. تحقق من عدم وجود خط أزرق\n"
                "3. اكتب: 1010001\n"
                "4. اضغط F9 للبحث\n"
                "5. جرب التنقل بـ Tab\n\n"
                "✅ المتوقع:\n"
                "• لا يوجد خط أزرق حول الحقل\n"
                "• حدود زرقاء عادية عند التركيز\n"
                "• نص أسود واضح\n"
                "• تأثير hover عند المرور بالماوس"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في اختبار حقل الصندوق:\n{str(e)}")
    
    def test_comprehensive(self):
        """اختبار شامل لجميع الحقول"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            from PyQt5.QtWidgets import QTableWidgetItem
            from PyQt5.QtCore import Qt
            
            dialog = VoucherInterface(self.db_manager, "receipt", self)
            
            # تعبئة بيانات تجريبية
            dialog.beneficiary_edit.setText("شركة الاختبار")
            dialog.cashbox_code_edit.setText("1010001")
            dialog.cashbox_name_edit.setText("صندوق المركز الرئيسي")
            
            # إضافة بيانات للجدول
            table = dialog.details_table
            table.setItem(0, 1, QTableWidgetItem("1030001"))
            table.setItem(0, 2, QTableWidgetItem("العملاء المحليين"))
            table.setItem(0, 3, QTableWidgetItem("تحصيل فاتورة"))
            
            # التركيز على خانة المبلغ
            amount_item = QTableWidgetItem("25000.50")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            table.setItem(0, 4, amount_item)
            table.setCurrentCell(0, 4)
            
            dialog.show()
            
            QMessageBox.information(
                self, 
                "اختبار شامل",
                "✅ تم فتح السند للاختبار الشامل\n\n"
                "🔍 اختبر جميع الحقول:\n"
                "• حقل رقم الصندوق (بدون خط أزرق)\n"
                "• حقل اسم الصندوق (للقراءة فقط)\n"
                "• خانة المبلغ (نص واضح أثناء الكتابة)\n"
                "• جميع حقول الجدول\n\n"
                "⌨️ جرب الاختصارات:\n"
                "• F9 في حقل الصندوق للبحث\n"
                "• Tab للتنقل بين الحقول\n"
                "• Enter في الجدول\n\n"
                "✅ تحقق من:\n"
                "• وضوح النص في جميع الحالات\n"
                "• عدم وجود خطوط زرقاء غريبة\n"
                "• تأثيرات التركيز الصحيحة\n"
                "• الألوان المناسبة"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الاختبار الشامل:\n{str(e)}")


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont("Tahoma", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = TextVisibilityFixTestWindow()
    window.show()
    
    print("🔧 تم تشغيل اختبار إصلاح مشكلة ظهور النص!")
    print("✅ الإصلاحات المطبقة:")
    print("   💰 خانة المبلغ: نص واضح أثناء الكتابة")
    print("   🏦 حقل رقم الصندوق: إزالة الخط الأزرق")
    print("   📊 الجدول: تحسين ألوان التركيز")
    print("   🎨 التنسيق: ألوان متسقة وواضحة")
    print("")
    print("🧪 اختبر:")
    print("   • الكتابة في خانة المبلغ")
    print("   • التنقل بين الحقول")
    print("   • استخدام F9 للبحث")
    print("   • تأثيرات التركيز والتحديد")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
