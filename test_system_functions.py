#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وظائف النظام المحاسبي
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_system_settings_functions():
    """اختبار وظائف إعدادات النظام"""
    print("⚙️ اختبار وظائف إعدادات النظام...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system_restructured import MainSystemWindow
        
        main_window = MainSystemWindow()
        
        # اختبار وجود الوظائف
        functions_to_test = [
            'open_auto_numbering',
            'open_backup_system', 
            'create_backup_interface',
            'create_backup',
            'restore_backup',
            'schedule_backup',
            'view_backups',
            'open_database_management',
            'create_database_management_interface',
            'check_database',
            'optimize_database',
            'show_database_stats',
            'export_data',
            'open_technical_settings',
            'open_company_settings',
            'open_print_settings'
        ]
        
        missing_functions = []
        existing_functions = []
        
        for func_name in functions_to_test:
            if hasattr(main_window, func_name):
                existing_functions.append(func_name)
                print(f"   ✅ {func_name}")
            else:
                missing_functions.append(func_name)
                print(f"   ❌ {func_name}")
        
        print(f"\n📊 النتائج:")
        print(f"   ✅ الوظائف الموجودة: {len(existing_functions)}")
        print(f"   ❌ الوظائف المفقودة: {len(missing_functions)}")
        
        if missing_functions:
            print(f"\n❌ الوظائف المفقودة:")
            for func in missing_functions:
                print(f"      • {func}")
        
        return len(missing_functions) == 0
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار وظائف إعدادات النظام: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_numbering_function():
    """اختبار وظيفة الترقيم التلقائي"""
    print("\n🔢 اختبار وظيفة الترقيم التلقائي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system_restructured import MainSystemWindow
        from modules.auto_numbering_module import AutoNumberingWidget
        
        main_window = MainSystemWindow()
        
        # اختبار إنشاء وحدة الترقيم التلقائي
        numbering_widget = AutoNumberingWidget(main_window.db_manager)
        print("   ✅ تم إنشاء وحدة الترقيم التلقائي")
        
        # اختبار إنتاج رقم تلقائي
        test_number = AutoNumberingWidget.get_next_number(
            main_window.db_manager, 
            "فاتورة مبيعات"
        )
        print(f"   ✅ تم إنتاج رقم تلقائي: {test_number}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار الترقيم التلقائي: {e}")
        return False

def test_backup_functions():
    """اختبار وظائف النسخ الاحتياطي"""
    print("\n💾 اختبار وظائف النسخ الاحتياطي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system_restructured import MainSystemWindow
        
        main_window = MainSystemWindow()
        
        # اختبار إنشاء واجهة النسخ الاحتياطي
        main_window.create_backup_interface()
        print("   ✅ تم إنشاء واجهة النسخ الاحتياطي")
        
        # اختبار إنشاء نسخة احتياطية
        main_window.create_backup()
        print("   ✅ تم إنشاء نسخة احتياطية")
        
        # اختبار عرض النسخ الاحتياطية
        main_window.view_backups()
        print("   ✅ تم عرض النسخ الاحتياطية")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار النسخ الاحتياطي: {e}")
        return False

def test_database_functions():
    """اختبار وظائف إدارة قاعدة البيانات"""
    print("\n🗄️ اختبار وظائف إدارة قاعدة البيانات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system_restructured import MainSystemWindow
        
        main_window = MainSystemWindow()
        
        # اختبار إنشاء واجهة إدارة قاعدة البيانات
        main_window.create_database_management_interface()
        print("   ✅ تم إنشاء واجهة إدارة قاعدة البيانات")
        
        # اختبار فحص قاعدة البيانات
        main_window.check_database()
        print("   ✅ تم فحص قاعدة البيانات")
        
        # اختبار عرض إحصائيات قاعدة البيانات
        main_window.show_database_stats()
        print("   ✅ تم عرض إحصائيات قاعدة البيانات")
        
        # اختبار تحسين قاعدة البيانات
        main_window.optimize_database()
        print("   ✅ تم تحسين قاعدة البيانات")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إدارة قاعدة البيانات: {e}")
        return False

def test_navigation():
    """اختبار التنقل بين الوظائف"""
    print("\n🧭 اختبار التنقل بين الوظائف...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QTreeWidgetItem
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system_restructured import MainSystemWindow
        
        main_window = MainSystemWindow()
        
        # محاكاة النقر على عناصر مختلفة
        test_items = [
            "الترقيم التلقائي للمستندات",
            "النسخ الاحتياطي", 
            "إدارة قاعدة البيانات"
        ]
        
        for item_text in test_items:
            # إنشاء عنصر وهمي للاختبار
            item = QTreeWidgetItem([f"📄 {item_text}"])
            
            # محاكاة النقر
            main_window.handle_tree_click(item, 0)
            print(f"   ✅ تم اختبار التنقل إلى: {item_text}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التنقل: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار وظائف النظام المحاسبي")
    print("=" * 70)
    
    tests = [
        ("وظائف إعدادات النظام", test_system_settings_functions),
        ("وظيفة الترقيم التلقائي", test_auto_numbering_function),
        ("وظائف النسخ الاحتياطي", test_backup_functions),
        ("وظائف إدارة قاعدة البيانات", test_database_functions),
        ("التنقل بين الوظائف", test_navigation),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم تفعيل وظائف إعدادات النظام بنجاح!")
        print("✅ جميع الوظائف تعمل بدون مشاكل")
        
        print("\n🔧 الوظائف المفعلة:")
        print("   🔢 الترقيم التلقائي للمستندات - مفعل ✅")
        print("   💾 النسخ الاحتياطي - مفعل ✅")
        print("   🗄️ إدارة قاعدة البيانات - مفعل ✅")
        print("   ⚙️ الإعدادات التقنية - قريباً 🚧")
        print("   🏢 إعدادات الشركة - قريباً 🚧")
        print("   🖨️ إعدادات الطباعة - قريباً 🚧")
        
        print("\n🚀 للاستخدام:")
        print("   1. شغل النظام: python src/main_system_restructured.py")
        print("   2. انقر على 'إعدادات النظام'")
        print("   3. اختر الوظيفة المطلوبة")
        
    else:
        print("\n⚠️ بعض الوظائف تحتاج إلى إصلاح")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
