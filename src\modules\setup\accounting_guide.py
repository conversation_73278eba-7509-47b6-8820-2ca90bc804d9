#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة إعداد الدليل المحاسبي
Accounting Guide Setup Screen
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QMessageBox, 
                             QFrame, QSplitter, QTextEdit, QTabWidget, QWidget,
                             QTreeWidget, QTreeWidgetItem, QListWidget, QListWidgetItem,
                             QProgressBar, QSlider, QDateEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QColor, QIcon

class AccountingGuideDialog(QDialog):
    """شاشة إعداد الدليل المحاسبي"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إعداد الدليل المحاسبي - تصميم وإدارة شجرة الحسابات")
        self.setGeometry(50, 50, 1600, 1000)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان الشاشة
        title_label = QLabel("📚 إعداد الدليل المحاسبي - شجرة الحسابات المتكاملة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تقسيم الشاشة
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - شجرة الحسابات
        left_panel = self.create_accounts_tree_panel()
        splitter.addWidget(left_panel)
        
        # الجانب الأيمن - إدارة الحسابات
        right_panel = self.create_management_panel()
        splitter.addWidget(right_panel)
        
        # تحديد نسب التقسيم
        splitter.setSizes([700, 900])
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(splitter)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_accounts_tree_panel(self):
        """إنشاء لوحة شجرة الحسابات"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # عنوان القسم
        section_title = QLabel("📊 شجرة الحسابات")
        section_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #34495e;
                padding: 10px;
                background-color: #d5dbdb;
                border-radius: 5px;
                margin-bottom: 15px;
            }
        """)
        section_title.setAlignment(Qt.AlignCenter)
        
        # شجرة الحسابات
        self.accounts_tree = QTreeWidget()
        self.accounts_tree.setHeaderLabels(["اسم الحساب", "رقم الحساب", "النوع", "الرصيد", "الحالة"])
        self.accounts_tree.setRootIsDecorated(True)
        self.accounts_tree.setAnimated(True)
        
        self.accounts_tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                font-size: 14px;
                outline: none;
            }
            QTreeWidget::item {
                padding: 10px;
                border-bottom: 1px solid #ecf0f1;
                min-height: 35px;
            }
            QTreeWidget::item:hover {
                background-color: #e8f4fd;
            }
            QTreeWidget::item:selected {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
        """)
        
        # ربط الأحداث
        self.accounts_tree.itemClicked.connect(self.on_account_selected)
        self.accounts_tree.itemDoubleClicked.connect(self.on_account_double_clicked)
        
        # أزرار إدارة الشجرة
        tree_buttons = QHBoxLayout()
        
        expand_all_btn = QPushButton("📂 توسيع الكل")
        expand_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        expand_all_btn.clicked.connect(self.accounts_tree.expandAll)
        
        collapse_all_btn = QPushButton("📁 طي الكل")
        collapse_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        collapse_all_btn.clicked.connect(self.accounts_tree.collapseAll)
        
        refresh_tree_btn = QPushButton("🔄 تحديث الشجرة")
        refresh_tree_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        refresh_tree_btn.clicked.connect(self.refresh_accounts_tree)
        
        print_tree_btn = QPushButton("🖨️ طباعة الدليل")
        print_tree_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        print_tree_btn.clicked.connect(self.print_accounting_guide)
        
        tree_buttons.addWidget(expand_all_btn)
        tree_buttons.addWidget(collapse_all_btn)
        tree_buttons.addWidget(refresh_tree_btn)
        tree_buttons.addWidget(print_tree_btn)
        
        # معلومات الشجرة
        self.tree_info = QLabel("📊 إحصائيات الدليل: 0 حساب رئيسي، 0 حساب فرعي")
        self.tree_info.setStyleSheet("""
            QLabel {
                background-color: #e8f4fd;
                border: 1px solid #bee5eb;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                color: #0c5460;
            }
        """)
        
        layout.addWidget(section_title)
        layout.addWidget(self.accounts_tree)
        layout.addLayout(tree_buttons)
        layout.addWidget(self.tree_info)
        
        panel.setLayout(layout)
        return panel
        
    def create_management_panel(self):
        """إنشاء لوحة الإدارة"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # تبويبات الإدارة
        tabs = QTabWidget()
        
        # تبويب الحسابات الرئيسية
        main_accounts_tab = self.create_main_accounts_tab()
        tabs.addTab(main_accounts_tab, "🏛️ الحسابات الرئيسية")
        
        # تبويب الحسابات الفرعية
        sub_accounts_tab = self.create_sub_accounts_tab()
        tabs.addTab(sub_accounts_tab, "📋 الحسابات الفرعية")
        
        # تبويب أنواع الحسابات
        account_types_tab = self.create_account_types_tab()
        tabs.addTab(account_types_tab, "🏷️ أنواع الحسابات")
        
        # تبويب إعدادات الدليل
        settings_tab = self.create_settings_tab()
        tabs.addTab(settings_tab, "⚙️ إعدادات الدليل")
        
        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        tabs.addTab(reports_tab, "📊 التقارير والتحليل")
        
        layout.addWidget(tabs)
        panel.setLayout(layout)
        return panel
        
    def create_main_accounts_tab(self):
        """إنشاء تبويب الحسابات الرئيسية"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # نموذج إضافة حساب رئيسي
        account_form = QGroupBox("➕ إضافة حساب رئيسي جديد")
        form_layout = QGridLayout()
        
        # اسم الحساب
        form_layout.addWidget(QLabel("اسم الحساب:"), 0, 0)
        self.main_account_name = QLineEdit()
        self.main_account_name.setPlaceholderText("مثال: الأصول المتداولة")
        form_layout.addWidget(self.main_account_name, 0, 1)
        
        # اسم الحساب بالإنجليزية
        form_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 0, 2)
        self.main_account_name_en = QLineEdit()
        self.main_account_name_en.setPlaceholderText("Current Assets")
        form_layout.addWidget(self.main_account_name_en, 0, 3)
        
        # رقم الحساب
        form_layout.addWidget(QLabel("رقم الحساب:"), 1, 0)
        self.main_account_number = QLineEdit()
        self.main_account_number.setPlaceholderText("1000")
        self.main_account_number.setMaxLength(10)
        form_layout.addWidget(self.main_account_number, 1, 1)
        
        # نوع الحساب
        form_layout.addWidget(QLabel("نوع الحساب:"), 1, 2)
        self.main_account_type = QComboBox()
        self.main_account_type.addItems([
            "الأصول", "الخصوم", "حقوق الملكية", "الإيرادات", "المصروفات"
        ])
        form_layout.addWidget(self.main_account_type, 1, 3)
        
        # طبيعة الحساب
        form_layout.addWidget(QLabel("طبيعة الحساب:"), 2, 0)
        self.account_nature = QComboBox()
        self.account_nature.addItems(["مدين", "دائن"])
        form_layout.addWidget(self.account_nature, 2, 1)
        
        # مستوى الحساب
        form_layout.addWidget(QLabel("مستوى الحساب:"), 2, 2)
        self.account_level = QSpinBox()
        self.account_level.setRange(1, 10)
        self.account_level.setValue(1)
        form_layout.addWidget(self.account_level, 2, 3)
        
        # الوصف
        form_layout.addWidget(QLabel("وصف الحساب:"), 3, 0)
        self.main_account_description = QTextEdit()
        self.main_account_description.setMaximumHeight(60)
        self.main_account_description.setPlaceholderText("وصف مختصر لطبيعة الحساب...")
        form_layout.addWidget(self.main_account_description, 3, 1, 1, 3)
        
        # حساب نشط
        self.main_account_active = QCheckBox("حساب نشط")
        self.main_account_active.setChecked(True)
        form_layout.addWidget(self.main_account_active, 4, 0, 1, 2)
        
        # يقبل القيد المباشر
        self.accepts_direct_entry = QCheckBox("يقبل القيد المباشر")
        form_layout.addWidget(self.accepts_direct_entry, 4, 2, 1, 2)
        
        account_form.setLayout(form_layout)
        
        # أزرار إدارة الحسابات الرئيسية
        main_account_buttons = QHBoxLayout()
        
        add_main_account_btn = QPushButton("🏛️ إضافة حساب رئيسي")
        add_main_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_main_account_btn.clicked.connect(self.add_main_account)
        
        edit_main_account_btn = QPushButton("✏️ تعديل")
        edit_main_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_main_account_btn.clicked.connect(self.edit_main_account)
        
        delete_main_account_btn = QPushButton("🗑️ حذف")
        delete_main_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_main_account_btn.clicked.connect(self.delete_main_account)
        
        main_account_buttons.addWidget(add_main_account_btn)
        main_account_buttons.addWidget(edit_main_account_btn)
        main_account_buttons.addWidget(delete_main_account_btn)
        main_account_buttons.addStretch()
        
        # جدول الحسابات الرئيسية
        self.main_accounts_table = QTableWidget()
        self.main_accounts_table.setColumnCount(7)
        self.main_accounts_table.setHorizontalHeaderLabels([
            "اسم الحساب", "رقم الحساب", "النوع", "الطبيعة", "المستوى", "الحسابات الفرعية", "الحالة"
        ])
        
        # تنسيق الجدول
        self.main_accounts_table.horizontalHeader().setStretchLastSection(True)
        self.main_accounts_table.setAlternatingRowColors(True)
        self.main_accounts_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.main_accounts_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        layout.addWidget(account_form)
        layout.addLayout(main_account_buttons)
        layout.addWidget(QLabel("📋 الحسابات الرئيسية المعرفة:"))
        layout.addWidget(self.main_accounts_table)
        
        tab.setLayout(layout)
        return tab

    def create_sub_accounts_tab(self):
        """إنشاء تبويب الحسابات الفرعية"""
        tab = QWidget()
        layout = QVBoxLayout()

        # نموذج إضافة حساب فرعي
        sub_account_form = QGroupBox("➕ إضافة حساب فرعي جديد")
        form_layout = QGridLayout()

        # اسم الحساب الفرعي
        form_layout.addWidget(QLabel("اسم الحساب الفرعي:"), 0, 0)
        self.sub_account_name = QLineEdit()
        self.sub_account_name.setPlaceholderText("مثال: النقدية في الصندوق")
        form_layout.addWidget(self.sub_account_name, 0, 1)

        # الحساب الرئيسي التابع له
        form_layout.addWidget(QLabel("الحساب الرئيسي:"), 0, 2)
        self.sub_account_parent = QComboBox()
        form_layout.addWidget(self.sub_account_parent, 0, 3)

        # رقم الحساب الفرعي
        form_layout.addWidget(QLabel("رقم الحساب:"), 1, 0)
        self.sub_account_number = QLineEdit()
        self.sub_account_number.setPlaceholderText("1001")
        self.sub_account_number.setMaxLength(10)
        form_layout.addWidget(self.sub_account_number, 1, 1)

        # العملة
        form_layout.addWidget(QLabel("العملة:"), 1, 2)
        self.sub_account_currency = QComboBox()
        form_layout.addWidget(self.sub_account_currency, 1, 3)

        # الرصيد الافتتاحي
        form_layout.addWidget(QLabel("الرصيد الافتتاحي:"), 2, 0)
        self.opening_balance = QDoubleSpinBox()
        self.opening_balance.setRange(-*********, *********)
        self.opening_balance.setDecimals(2)
        form_layout.addWidget(self.opening_balance, 2, 1)

        # تاريخ الرصيد الافتتاحي
        form_layout.addWidget(QLabel("تاريخ الرصيد:"), 2, 2)
        self.opening_balance_date = QDateEdit()
        self.opening_balance_date.setDate(QDate.currentDate())
        self.opening_balance_date.setCalendarPopup(True)
        form_layout.addWidget(self.opening_balance_date, 2, 3)

        # الوصف
        form_layout.addWidget(QLabel("وصف الحساب:"), 3, 0)
        self.sub_account_description = QTextEdit()
        self.sub_account_description.setMaximumHeight(60)
        self.sub_account_description.setPlaceholderText("وصف تفصيلي للحساب الفرعي...")
        form_layout.addWidget(self.sub_account_description, 3, 1, 1, 3)

        # حساب فرعي نشط
        self.sub_account_active = QCheckBox("حساب فرعي نشط")
        self.sub_account_active.setChecked(True)
        form_layout.addWidget(self.sub_account_active, 4, 0, 1, 2)

        # يظهر في التقارير
        self.show_in_reports = QCheckBox("يظهر في التقارير")
        self.show_in_reports.setChecked(True)
        form_layout.addWidget(self.show_in_reports, 4, 2, 1, 2)

        sub_account_form.setLayout(form_layout)

        # أزرار إدارة الحسابات الفرعية
        sub_account_buttons = QHBoxLayout()

        add_sub_account_btn = QPushButton("📋 إضافة حساب فرعي")
        add_sub_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_sub_account_btn.clicked.connect(self.add_sub_account)

        edit_sub_account_btn = QPushButton("✏️ تعديل")
        edit_sub_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_sub_account_btn.clicked.connect(self.edit_sub_account)

        delete_sub_account_btn = QPushButton("🗑️ حذف")
        delete_sub_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_sub_account_btn.clicked.connect(self.delete_sub_account)

        sub_account_buttons.addWidget(add_sub_account_btn)
        sub_account_buttons.addWidget(edit_sub_account_btn)
        sub_account_buttons.addWidget(delete_sub_account_btn)
        sub_account_buttons.addStretch()

        # جدول الحسابات الفرعية
        self.sub_accounts_table = QTableWidget()
        self.sub_accounts_table.setColumnCount(8)
        self.sub_accounts_table.setHorizontalHeaderLabels([
            "اسم الحساب", "رقم الحساب", "الحساب الرئيسي", "العملة", "الرصيد الافتتاحي", "الرصيد الحالي", "آخر حركة", "الحالة"
        ])

        # تنسيق الجدول
        self.sub_accounts_table.horizontalHeader().setStretchLastSection(True)
        self.sub_accounts_table.setAlternatingRowColors(True)
        self.sub_accounts_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.sub_accounts_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)

        layout.addWidget(sub_account_form)
        layout.addLayout(sub_account_buttons)
        layout.addWidget(QLabel("📋 الحسابات الفرعية المعرفة:"))
        layout.addWidget(self.sub_accounts_table)

        tab.setLayout(layout)
        return tab

    def create_account_types_tab(self):
        """إنشاء تبويب أنواع الحسابات"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة أنواع الحسابات الرئيسية
        main_types_group = QGroupBox("🏛️ أنواع الحسابات الرئيسية")
        main_types_layout = QGridLayout()

        # قائمة أنواع الحسابات
        account_types = [
            ("الأصول", "Assets", "مدين", "#2ecc71"),
            ("الخصوم", "Liabilities", "دائن", "#e74c3c"),
            ("حقوق الملكية", "Equity", "دائن", "#3498db"),
            ("الإيرادات", "Revenues", "دائن", "#f39c12"),
            ("المصروفات", "Expenses", "مدين", "#9b59b6")
        ]

        self.account_type_checkboxes = {}
        for i, (ar_name, en_name, nature, color) in enumerate(account_types):
            checkbox = QCheckBox(f"{ar_name} ({en_name})")
            checkbox.setChecked(True)
            checkbox.setStyleSheet(f"""
                QCheckBox {{
                    font-weight: bold;
                    color: {color};
                }}
            """)
            self.account_type_checkboxes[ar_name] = checkbox
            main_types_layout.addWidget(checkbox, i // 2, i % 2)

        main_types_group.setLayout(main_types_layout)

        # مجموعة إعدادات الترقيم
        numbering_group = QGroupBox("🔢 إعدادات ترقيم الحسابات")
        numbering_layout = QGridLayout()

        # نظام الترقيم
        numbering_layout.addWidget(QLabel("نظام الترقيم:"), 0, 0)
        self.numbering_system = QComboBox()
        self.numbering_system.addItems([
            "تسلسلي (1, 2, 3...)",
            "عشري (10, 20, 30...)",
            "مئوي (100, 200, 300...)",
            "ألفي (1000, 2000, 3000...)",
            "مخصص"
        ])
        self.numbering_system.setCurrentText("ألفي (1000, 2000, 3000...)")
        numbering_layout.addWidget(self.numbering_system, 0, 1)

        # طول رقم الحساب
        numbering_layout.addWidget(QLabel("طول رقم الحساب:"), 0, 2)
        self.account_number_length = QSpinBox()
        self.account_number_length.setRange(4, 15)
        self.account_number_length.setValue(4)
        numbering_layout.addWidget(self.account_number_length, 0, 3)

        # فاصل المستويات
        numbering_layout.addWidget(QLabel("فاصل المستويات:"), 1, 0)
        self.level_separator = QComboBox()
        self.level_separator.addItems(["بدون فاصل", "نقطة (.)", "شرطة (-)", "شرطة مائلة (/)"])
        numbering_layout.addWidget(self.level_separator, 1, 1)

        # ترقيم تلقائي
        self.auto_numbering = QCheckBox("ترقيم تلقائي للحسابات الجديدة")
        self.auto_numbering.setChecked(True)
        numbering_layout.addWidget(self.auto_numbering, 1, 2, 1, 2)

        numbering_group.setLayout(numbering_layout)

        # مجموعة قوالب الحسابات
        templates_group = QGroupBox("📋 قوالب الحسابات الجاهزة")
        templates_layout = QVBoxLayout()

        # قائمة القوالب
        self.templates_list = QListWidget()
        templates = [
            "🏢 دليل محاسبي للشركات التجارية",
            "🏭 دليل محاسبي للشركات الصناعية",
            "🏪 دليل محاسبي للمحلات التجارية",
            "🏥 دليل محاسبي للمؤسسات الطبية",
            "🎓 دليل محاسبي للمؤسسات التعليمية",
            "🏗️ دليل محاسبي لشركات المقاولات",
            "🚗 دليل محاسبي لشركات النقل",
            "🍽️ دليل محاسبي للمطاعم والفنادق"
        ]

        for template in templates:
            self.templates_list.addItem(template)

        # أزرار القوالب
        template_buttons = QHBoxLayout()

        apply_template_btn = QPushButton("✅ تطبيق القالب")
        apply_template_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        apply_template_btn.clicked.connect(self.apply_template)

        preview_template_btn = QPushButton("👁️ معاينة القالب")
        preview_template_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        preview_template_btn.clicked.connect(self.preview_template)

        template_buttons.addWidget(apply_template_btn)
        template_buttons.addWidget(preview_template_btn)
        template_buttons.addStretch()

        templates_layout.addWidget(self.templates_list)
        templates_layout.addLayout(template_buttons)
        templates_group.setLayout(templates_layout)

        layout.addWidget(main_types_group)
        layout.addWidget(numbering_group)
        layout.addWidget(templates_group)
        layout.addStretch()

        tab.setLayout(layout)
        return tab

    def create_settings_tab(self):
        """إنشاء تبويب إعدادات الدليل"""
        from .accounting_guide_functions import create_settings_tab_function
        return create_settings_tab_function(self)

    def create_reports_tab(self):
        """إنشاء تبويب التقارير والتحليل"""
        from .accounting_guide_functions import create_reports_tab_function
        return create_reports_tab_function(self)

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        from .accounting_guide_functions import create_control_buttons_function
        return create_control_buttons_function(self)

    def load_data(self):
        """تحميل البيانات المحفوظة"""
        from .accounting_guide_data import load_data_function
        load_data_function(self)

    def load_currencies(self):
        """تحميل قائمة العملات"""
        from .accounting_guide_data import load_currencies_function
        load_currencies_function(self)

    def load_sample_accounts_data(self):
        """تحميل البيانات النموذجية للحسابات"""
        from .accounting_guide_data import load_sample_accounts_data_function
        load_sample_accounts_data_function(self)

    def update_parent_accounts(self):
        """تحديث قائمة الحسابات الرئيسية"""
        from .accounting_guide_data import update_parent_accounts_function
        update_parent_accounts_function(self)

    def update_sub_accounts_count(self):
        """تحديث عدد الحسابات الفرعية لكل حساب رئيسي"""
        from .accounting_guide_data import update_sub_accounts_count_function
        update_sub_accounts_count_function(self)

    def build_accounts_tree(self):
        """بناء شجرة الحسابات"""
        from .accounting_guide_data import build_accounts_tree_function
        build_accounts_tree_function(self)

    def update_tree_statistics(self):
        """تحديث إحصائيات الشجرة"""
        from .accounting_guide_data import update_tree_statistics_function
        update_tree_statistics_function(self)

    def on_account_selected(self, item, column):
        """معالجة اختيار حساب من الشجرة"""
        from .accounting_guide_data import on_account_selected_function
        on_account_selected_function(self, item, column)

    def on_account_double_clicked(self, item, column):
        """معالجة النقر المزدوج على حساب في الشجرة"""
        from .accounting_guide_data import on_account_double_clicked_function
        on_account_double_clicked_function(self, item, column)

    def refresh_accounts_tree(self):
        """تحديث شجرة الحسابات"""
        from .accounting_guide_data import refresh_accounts_tree_function
        refresh_accounts_tree_function(self)

    def print_accounting_guide(self):
        """طباعة الدليل المحاسبي"""
        from .accounting_guide_data import print_accounting_guide_function
        print_accounting_guide_function(self)

    def generate_chart_of_accounts(self):
        """إنتاج دليل الحسابات الكامل"""
        from .accounting_guide_data import generate_chart_of_accounts_function
        generate_chart_of_accounts_function(self)

    def generate_trial_balance(self):
        """إنتاج ميزان المراجعة"""
        from .accounting_guide_data import generate_trial_balance_function
        generate_trial_balance_function(self)

    def generate_account_balances(self):
        """إنتاج تقرير أرصدة الحسابات"""
        from .accounting_guide_data import generate_account_balances_function
        generate_account_balances_function(self)

    def show_help(self):
        """عرض المساعدة"""
        from .accounting_guide_data import show_help_function
        show_help_function(self)

    # وظائف إدارة الحسابات (ستكون في ملف منفصل)
    def add_main_account(self):
        """إضافة حساب رئيسي جديد"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة الحسابات الرئيسية قيد التطوير")

    def edit_main_account(self):
        """تعديل حساب رئيسي"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل الحسابات الرئيسية قيد التطوير")

    def delete_main_account(self):
        """حذف حساب رئيسي"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حذف الحسابات الرئيسية قيد التطوير")

    def add_sub_account(self):
        """إضافة حساب فرعي جديد"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة الحسابات الفرعية قيد التطوير")

    def edit_sub_account(self):
        """تعديل حساب فرعي"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل الحسابات الفرعية قيد التطوير")

    def delete_sub_account(self):
        """حذف حساب فرعي"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حذف الحسابات الفرعية قيد التطوير")

    def apply_template(self):
        """تطبيق قالب الحسابات"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تطبيق القوالب قيد التطوير")

    def preview_template(self):
        """معاينة قالب الحسابات"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة معاينة القوالب قيد التطوير")

    def generate_account_activity(self):
        """إنتاج تقرير نشاط الحسابات"""
        QMessageBox.information(self, "قيد التطوير", "تقرير نشاط الحسابات قيد التطوير")

    def generate_unused_accounts(self):
        """إنتاج تقرير الحسابات غير المستخدمة"""
        QMessageBox.information(self, "قيد التطوير", "تقرير الحسابات غير المستخدمة قيد التطوير")

    def generate_guide_statistics(self):
        """إنتاج إحصائيات الدليل"""
        QMessageBox.information(self, "قيد التطوير", "إحصائيات الدليل قيد التطوير")

    def export_report(self):
        """تصدير التقرير"""
        QMessageBox.information(self, "قيد التطوير", "تصدير التقارير قيد التطوير")

    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "قيد التطوير", "طباعة التقارير قيد التطوير")

    def clear_report(self):
        """مسح التقرير"""
        self.reports_display.clear()

    def validate_accounting_guide(self):
        """التحقق من صحة الدليل المحاسبي"""
        QMessageBox.information(self, "قيد التطوير", "التحقق من الدليل قيد التطوير")

    def save_accounting_guide(self):
        """حفظ الدليل المحاسبي"""
        QMessageBox.information(self, "قيد التطوير", "حفظ الدليل المحاسبي قيد التطوير")


def main():
    """اختبار الشاشة"""
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass

    dialog = AccountingGuideDialog(MockDBManager())
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
