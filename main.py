#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المحاسبة الشامل
Comprehensive Accounting System

الملف الرئيسي لتشغيل النظام
Main file to run the system
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.ui.login_window import LoginWindow
from src.database.sqlite_manager import SQLiteManager
from src.utils.config import Config

class AccountingSystem:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("نظام المحاسبة الشامل")
        self.app.setApplicationVersion("1.0.0")
        
        # تعيين الخط الافتراضي للنظام
        font = QFont("Arial", 10)
        self.app.setFont(font)
        
        # تهيئة قاعدة البيانات
        self.db_manager = SQLiteManager()
        
        # إنشاء شاشة البداية
        self.create_splash_screen()
        
    def create_splash_screen(self):
        """إنشاء شاشة البداية"""
        # إنشاء صورة شاشة البداية
        splash_pix = QPixmap(400, 300)
        splash_pix.fill(Qt.white)
        
        self.splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
        self.splash.setMask(splash_pix.mask())
        self.splash.show()
        
        # عرض رسالة التحميل
        self.splash.showMessage("جاري تحميل النظام...\nLoading System...", 
                               Qt.AlignCenter | Qt.AlignBottom, Qt.black)
        
        # تأخير لعرض شاشة البداية
        QTimer.singleShot(3000, self.show_login)
        
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        self.splash.close()
        self.login_window = LoginWindow()
        self.login_window.show()
        
    def run(self):
        """تشغيل التطبيق"""
        return self.app.exec_()

if __name__ == "__main__":
    # تعيين ترميز UTF-8
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8')
    
    # إنشاء وتشغيل النظام
    system = AccountingSystem()
    sys.exit(system.run())