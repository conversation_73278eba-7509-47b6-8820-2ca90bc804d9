#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التصميم الجديد للسند وفقاً للمواصفات المطلوبة
New Voucher Design Test According to Required Specifications
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QPushButton, QWidget, QLabel, QMessageBox, QTextEdit
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class NewVoucherDesignTestWindow(QMainWindow):
    """نافذة اختبار التصميم الجديد للسند"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_mock_db()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار التصميم الجديد للسند - وفقاً للمواصفات المطلوبة")
        self.setGeometry(200, 200, 1000, 700)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QMainWindow {
                background-color: #fdf2f8;
                font-family: "Tahoma", "Arial", sans-serif;
            }
            QPushButton {
                background-color: #e91e63;
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                margin: 10px;
                min-width: 300px;
            }
            QPushButton:hover {
                background-color: #c2185b;
            }
            QPushButton:pressed {
                background-color: #ad1457;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 14px;
                color: #2c3e50;
                padding: 10px;
            }
            QTextEdit {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 12px;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("📄 اختبار التصميم الجديد للسند - وفقاً للمواصفات المطلوبة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                color: #e91e63;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 10px;
                padding: 15px;
                margin: 15px;
                text-align: center;
            }
        """)
        layout.addWidget(title_label)
        
        # أزرار الاختبار
        buttons_layout = QVBoxLayout()
        
        # اختبار سند قبض بالتصميم الجديد
        receipt_btn = QPushButton("📄 سند قبض - التصميم الجديد")
        receipt_btn.clicked.connect(self.test_new_receipt_design)
        buttons_layout.addWidget(receipt_btn)
        
        # اختبار سند صرف بالتصميم الجديد
        payment_btn = QPushButton("📄 سند صرف - التصميم الجديد")
        payment_btn.clicked.connect(self.test_new_payment_design)
        buttons_layout.addWidget(payment_btn)
        
        # اختبار مع بيانات كاملة
        full_data_btn = QPushButton("📋 اختبار مع بيانات كاملة")
        full_data_btn.clicked.connect(self.test_with_full_data)
        buttons_layout.addWidget(full_data_btn)
        
        layout.addLayout(buttons_layout)
        
        # معلومات التصميم الجديد
        info = QTextEdit()
        info.setReadOnly(True)
        info.setMaximumHeight(350)
        info.setHtml("""
        <h3 style="color: #e91e63;">📄 التصميم الجديد للسند:</h3>
        
        <h4 style="color: #27ae60;">📌 رأس السند (الجزء الأيسر):</h4>
        <ul>
            <li><strong>نوع السند:</strong> 01: عام، 02: مبيعات، 03: مشتريات، 04: مرتجعات</li>
            <li><strong>نوع العملية:</strong> نقداً / بنكي</li>
            <li><strong>العملة:</strong> R.Y: ريال يمني، USD، SAR، EUR</li>
            <li><strong>عدد المرفقات:</strong> عدد المستندات المرفقة</li>
            <li><strong>التاريخ:</strong> تاريخ إنشاء السند (28/07/2025)</li>
            <li><strong>مركز رئيسي/فرعي:</strong> المركز الرئيسي، الفرع الأول، الثاني</li>
            <li><strong>رقم السند:</strong> رقم تسلسلي فريد (9905)</li>
            <li><strong>R.Y المبلغ المحلي:</strong> إجمالي المبلغ بالريال اليمني</li>
        </ul>
        
        <h4 style="color: #3498db;">🏦 بيانات الصندوق/البنك (الجزء الأيمن):</h4>
        <ul>
            <li><strong>رقم الفرع:</strong> رقم الفرع المعني</li>
            <li><strong>رقم الصندوق:</strong> صندوق المحل الرئيسي</li>
            <li><strong>اسم المستلم:</strong> اسم الشخص المستلم للمبلغ</li>
            <li><strong>رقم المرجع / اسم المرجع:</strong> معلومات مرجعية</li>
            <li><strong>رقم الموظف / الرقم:</strong> الموظف المعني بالسند</li>
            <li><strong>بيان الصندوق/البنك:</strong> شرح تفصيلي للسند</li>
            <li><strong>🧮 آلة حاسبة:</strong> لإجراء عمليات حسابية</li>
            <li><strong>🗑️ إزالة البيانات:</strong> مسح النموذج</li>
        </ul>
        
        <h4 style="color: #9b59b6;">📊 جدول التفاصيل المحدث:</h4>
        <table border="1" style="border-collapse: collapse; width: 100%; font-size: 10px;">
            <tr style="background-color: #34495e; color: white;">
                <th>رقم الحساب</th>
                <th>الحساب التحليلي</th>
                <th>الاسم</th>
                <th>البيان</th>
                <th>العملة</th>
                <th>سعر التحويل</th>
                <th>نسبة</th>
                <th>المبلغ</th>
                <th>المبلغ الأجنبي</th>
                <th>رقم المركز</th>
                <th>رقم المندوب/المرجع</th>
            </tr>
            <tr>
                <td>120px</td>
                <td>120px</td>
                <td>200px</td>
                <td>مرن</td>
                <td>80px</td>
                <td>100px</td>
                <td>80px</td>
                <td>120px</td>
                <td>120px</td>
                <td>100px</td>
                <td>120px</td>
            </tr>
        </table>
        
        <h4 style="color: #e74c3c;">👤 بيانات المستخدم ومُدخل السند:</h4>
        <ul>
            <li><strong>مدخل السجل:</strong> اسم المستخدم الذي أدخل السند</li>
            <li><strong>معدل السجل:</strong> المستخدم الذي أجرى آخر تعديل</li>
            <li><strong>تاريخ الإدخال:</strong> التاريخ والوقت (28/07/2025 16:13:45)</li>
            <li><strong>تاريخ آخر تعديل:</strong> توقيت آخر تعديل</li>
            <li><strong>الجهاز المُدخل:</strong> اسم الجهاز (GLST002)</li>
            <li><strong>الجهاز المعدل:</strong> جهاز التعديل</li>
            <li><strong>مرات الطباعة:</strong> عدد مرات الطباعة (0)</li>
            <li><strong>مرات التعديل:</strong> عدد مرات التعديل</li>
            <li><strong>الإصدار:</strong> V8.1.18-05-2025</li>
            <li><strong>المساعدة:</strong> للمساعدة اضغط على F9</li>
        </ul>
        
        <h4 style="color: #f39c12;">🎯 الفوائد المحققة:</h4>
        <ul>
            <li>✅ <strong>تصميم احترافي:</strong> يتماشى مع أنظمة ERP الحديثة</li>
            <li>✅ <strong>معلومات شاملة:</strong> جميع البيانات المطلوبة</li>
            <li>✅ <strong>تتبع دقيق:</strong> للمستخدمين والتعديلات</li>
            <li>✅ <strong>مرونة في العملات:</strong> دعم عملات متعددة</li>
            <li>✅ <strong>تحليل مفصل:</strong> للحسابات والمراكز</li>
            <li>✅ <strong>سهولة الاستخدام:</strong> واجهة منظمة وواضحة</li>
        </ul>
        
        <h4 style="color: #17a2b8;">🔧 الميزات التقنية:</h4>
        <ul>
            <li><strong>تخطيط أفقي:</strong> لرأس السند وبيانات الصندوق</li>
            <li><strong>جدول محسن:</strong> 11 عمود بدلاً من 7</li>
            <li><strong>قسم بيانات المستخدم:</strong> تتبع كامل للعمليات</li>
            <li><strong>أزرار وظيفية:</strong> آلة حاسبة ومسح البيانات</li>
            <li><strong>تنسيق محسن:</strong> ألوان وخطوط واضحة</li>
        </ul>
        """)
        layout.addWidget(info)
        
        central_widget.setLayout(layout)
    
    def setup_mock_db(self):
        """إعداد قاعدة بيانات وهمية للاختبار"""
        class MockDBManager:
            def __init__(self):
                self.cursor = type('obj', (object,), {'lastrowid': 1})()
            
            def execute_query(self, query, params=None):
                if "MAX" in query:
                    return [(0,)]
                elif "chart_of_accounts" in query:
                    return [
                        ('111001', 'صندوق المحل الرئيسي', 'أصول متداولة', 1, 3, '111000'),
                        ('112001', 'البنك الأهلي اليمني', 'أصول متداولة', 1, 3, '112000'),
                        ('211001', 'الدائنين المحليين', 'خصوم متداولة', 1, 3, '211000'),
                        ('411001', 'مبيعات محلية', 'إيرادات', 1, 3, '411000'),
                        ('511001', 'مصروفات عمومية', 'مصروفات', 1, 3, '511000'),
                    ]
                return []
            
            def execute_update(self, query, params=None):
                return True
        
        self.db_manager = MockDBManager()
    
    def test_new_receipt_design(self):
        """اختبار سند القبض بالتصميم الجديد"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            dialog = VoucherInterface(self.db_manager, "receipt", self)
            dialog.show()
            
            QMessageBox.information(
                self, 
                "سند القبض - التصميم الجديد",
                "✅ تم فتح سند القبض بالتصميم الجديد\n\n"
                "📋 الميزات الجديدة:\n"
                "• رأس السند: نوع السند، العملية، العملة، المرفقات\n"
                "• بيانات الصندوق: رقم الفرع، المستلم، المرجع، الموظف\n"
                "• جدول محسن: 11 عمود شامل\n"
                "• بيانات المستخدم: تتبع كامل للعمليات\n\n"
                "🔍 اختبر:\n"
                "• تعبئة جميع الحقول الجديدة\n"
                "• استخدام الجدول المحسن\n"
                "• الآلة الحاسبة وإزالة البيانات\n"
                "• معلومات المستخدم في الأسفل"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند القبض:\n{str(e)}")
    
    def test_new_payment_design(self):
        """اختبار سند الصرف بالتصميم الجديد"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            dialog = VoucherInterface(self.db_manager, "payment", self)
            dialog.show()
            
            QMessageBox.information(
                self, 
                "سند الصرف - التصميم الجديد",
                "✅ تم فتح سند الصرف بالتصميم الجديد\n\n"
                "📋 الميزات الجديدة:\n"
                "• رأس السند: جميع البيانات الأساسية\n"
                "• بيانات الصندوق: معلومات شاملة\n"
                "• جدول تفصيلي: عملات وأسعار تحويل\n"
                "• تتبع المستخدمين: كامل ودقيق\n\n"
                "🔍 اختبر:\n"
                "• العملات المختلفة وأسعار التحويل\n"
                "• الحسابات التحليلية\n"
                "• مراكز التكلفة والمندوبين\n"
                "• النسب والمبالغ الأجنبية"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند الصرف:\n{str(e)}")
    
    def test_with_full_data(self):
        """اختبار مع بيانات كاملة"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            from PyQt5.QtWidgets import QTableWidgetItem
            from PyQt5.QtCore import Qt
            
            dialog = VoucherInterface(self.db_manager, "receipt", self)
            
            # تعبئة رأس السند
            dialog.voucher_type_combo.setCurrentText("01: عام")
            dialog.operation_type_combo.setCurrentText("نقداً")
            dialog.currency_combo.setCurrentText("R.Y: ريال يمني")
            dialog.attachments_count_edit.setText("3")
            dialog.center_combo.setCurrentText("المركز الرئيسي")
            dialog.voucher_number_edit.setText("9905")
            
            # تعبئة بيانات الصندوق
            dialog.branch_number_edit.setText("001")
            dialog.cashbox_code_edit.setText("111001")
            dialog.recipient_name_edit.setText("أحمد محمد علي")
            dialog.reference_number_edit.setText("REF-2025-001")
            dialog.reference_name_edit.setText("فاتورة مبيعات")
            dialog.employee_number_edit.setText("EMP-001")
            dialog.number_edit.setText("12345")
            dialog.cashbox_description_edit.setPlainText("تحصيل مبلغ من العميل مقابل فاتورة مبيعات رقم 2025-001")
            
            # إضافة بيانات للجدول
            table = dialog.details_table
            if table.rowCount() == 0:
                dialog.add_detail_row()
            
            table.setItem(0, 0, QTableWidgetItem("111001"))  # رقم الحساب
            table.setItem(0, 1, QTableWidgetItem("111001-01"))  # الحساب التحليلي
            table.setItem(0, 2, QTableWidgetItem("صندوق المحل الرئيسي"))  # الاسم
            table.setItem(0, 3, QTableWidgetItem("تحصيل نقدي من العميل"))  # البيان
            table.setItem(0, 4, QTableWidgetItem("R.Y"))  # العملة
            table.setItem(0, 5, QTableWidgetItem("1.0000"))  # سعر التحويل
            table.setItem(0, 6, QTableWidgetItem("0.00"))  # نسبة
            
            amount_item = QTableWidgetItem("50000.00")  # المبلغ
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            table.setItem(0, 7, amount_item)
            
            foreign_amount_item = QTableWidgetItem("50000.00")  # المبلغ الأجنبي
            foreign_amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            table.setItem(0, 8, foreign_amount_item)
            
            table.setItem(0, 9, QTableWidgetItem("001"))  # رقم المركز
            table.setItem(0, 10, QTableWidgetItem("REP-001"))  # رقم المندوب
            
            dialog.show()
            
            QMessageBox.information(
                self, 
                "اختبار مع بيانات كاملة",
                "✅ تم تعبئة السند ببيانات كاملة\n\n"
                "📋 البيانات المعبأة:\n"
                "• رأس السند: نوع عام، عملية نقدية، ريال يمني\n"
                "• الصندوق: المحل الرئيسي، المستلم أحمد محمد\n"
                "• الجدول: حساب تحليلي، عملة، مبلغ 50,000\n"
                "• المستخدم: جميع بيانات التتبع\n\n"
                "🔍 لاحظ:\n"
                "• التصميم الاحترافي الجديد\n"
                "• شمولية البيانات\n"
                "• سهولة الاستخدام\n"
                "• التتبع الدقيق للعمليات"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء البيانات:\n{str(e)}")


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont("Tahoma", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = NewVoucherDesignTestWindow()
    window.show()
    
    print("📄 تم تشغيل اختبار التصميم الجديد للسند!")
    print("✅ الميزات الجديدة:")
    print("   📌 رأس السند: نوع، عملية، عملة، مرفقات، تاريخ، مركز، رقم، مبلغ")
    print("   🏦 بيانات الصندوق: فرع، صندوق، مستلم، مرجع، موظف، بيان")
    print("   📊 جدول محسن: 11 عمود شامل للتفاصيل")
    print("   👤 بيانات المستخدم: تتبع كامل للعمليات والتعديلات")
    print("   🔧 أدوات إضافية: آلة حاسبة، مسح البيانات")
    print("")
    print("🎯 التحسينات:")
    print("   • تصميم احترافي متقدم")
    print("   • معلومات شاملة ومفصلة")
    print("   • تتبع دقيق للمستخدمين")
    print("   • دعم العملات المتعددة")
    print("   • تحليل مالي متقدم")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
