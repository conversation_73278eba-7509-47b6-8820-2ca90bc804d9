#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف أنواع السلامة المهنية
Occupational Safety Types Functions
"""

from PyQt5.QtWidgets import QMessageBox, QTableWidgetItem, QInputDialog, QFileDialog, QHBoxLayout, QPushButton
from PyQt5.QtCore import Qt
from datetime import datetime

def create_reports_tab_function(self):
    """إنشاء تبويب التقارير"""
    from PyQt5.QtWidgets import QWidget, QVBoxLayout, QGroupBox, QGridLayout, QTextEdit
    
    tab = QWidget()
    layout = QVBoxLayout()
    
    # مجموعة تقارير السلامة المهنية
    reports_group = QGroupBox("📊 تقارير السلامة المهنية")
    reports_layout = QGridLayout()
    
    # أزرار التقارير
    safety_types_report_btn = QPushButton("🦺 تقرير أنواع السلامة")
    safety_types_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    safety_types_report_btn.clicked.connect(self.generate_safety_types_report)
    
    incidents_report_btn = QPushButton("⚠️ تقرير الحوادث")
    incidents_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #e74c3c;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #c0392b;
        }
    """)
    incidents_report_btn.clicked.connect(self.generate_incidents_report)
    
    training_report_btn = QPushButton("📚 تقرير التدريب")
    training_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    training_report_btn.clicked.connect(self.generate_training_report)
    
    compliance_report_btn = QPushButton("📋 تقرير الامتثال")
    compliance_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #9b59b6;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #8e44ad;
        }
    """)
    compliance_report_btn.clicked.connect(self.generate_compliance_report)
    
    reports_layout.addWidget(safety_types_report_btn, 0, 0)
    reports_layout.addWidget(incidents_report_btn, 0, 1)
    reports_layout.addWidget(training_report_btn, 1, 0)
    reports_layout.addWidget(compliance_report_btn, 1, 1)
    
    reports_group.setLayout(reports_layout)
    
    # منطقة عرض التقارير
    reports_display_group = QGroupBox("📄 عرض التقارير")
    reports_display_layout = QVBoxLayout()
    
    self.reports_display = QTextEdit()
    self.reports_display.setPlaceholderText("ستظهر التقارير هنا...")
    self.reports_display.setStyleSheet("""
        QTextEdit {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    """)
    
    # أزرار التحكم في التقارير
    report_buttons = QHBoxLayout()
    
    export_report_btn = QPushButton("📤 تصدير التقرير")
    export_report_btn.clicked.connect(self.export_report)
    
    print_report_btn = QPushButton("🖨️ طباعة التقرير")
    print_report_btn.clicked.connect(self.print_report)
    
    clear_report_btn = QPushButton("🗑️ مسح التقرير")
    clear_report_btn.clicked.connect(self.clear_report)
    
    report_buttons.addWidget(export_report_btn)
    report_buttons.addWidget(print_report_btn)
    report_buttons.addWidget(clear_report_btn)
    report_buttons.addStretch()
    
    reports_display_layout.addWidget(self.reports_display)
    reports_display_layout.addLayout(report_buttons)
    reports_display_group.setLayout(reports_display_layout)
    
    layout.addWidget(reports_group)
    layout.addWidget(reports_display_group)
    
    tab.setLayout(layout)
    return tab

def create_control_buttons_function(self):
    """إنشاء أزرار التحكم"""
    layout = QHBoxLayout()
    
    # زر الحفظ
    save_btn = QPushButton("💾 حفظ التغييرات")
    save_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    save_btn.clicked.connect(self.save_changes)
    
    # زر الإلغاء
    cancel_btn = QPushButton("❌ إلغاء")
    cancel_btn.setStyleSheet("""
        QPushButton {
            background-color: #e74c3c;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #c0392b;
        }
    """)
    cancel_btn.clicked.connect(self.reject)
    
    # زر المساعدة
    help_btn = QPushButton("❓ المساعدة")
    help_btn.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    help_btn.clicked.connect(self.show_help)
    
    layout.addWidget(help_btn)
    layout.addStretch()
    layout.addWidget(save_btn)
    layout.addWidget(cancel_btn)
    
    return layout

def load_sample_data_function(self):
    """تحميل البيانات النموذجية"""
    # بيانات أنواع السلامة النموذجية
    self.safety_types_data = [
        {
            "code": "FIRE001",
            "name": "السلامة من الحرائق",
            "classification": "سلامة فيزيائية",
            "risk_level": "عالي",
            "description": "إجراءات الوقاية من الحرائق ومكافحتها في بيئة العمل",
            "compliance_requirements": "معايير الدفاع المدني اليمني",
            "status": "نشط"
        },
        {
            "code": "CHEM001",
            "name": "السلامة الكيميائية",
            "classification": "سلامة كيميائية",
            "risk_level": "حرج",
            "description": "التعامل الآمن مع المواد الكيميائية والخطرة",
            "compliance_requirements": "معايير منظمة الصحة العالمية",
            "status": "نشط"
        },
        {
            "code": "ELEC001",
            "name": "السلامة الكهربائية",
            "classification": "سلامة كهربائية",
            "risk_level": "عالي",
            "description": "الوقاية من مخاطر الكهرباء والصعق الكهربائي",
            "compliance_requirements": "معايير الكهرباء الوطنية",
            "status": "نشط"
        },
        {
            "code": "MECH001",
            "name": "السلامة الميكانيكية",
            "classification": "سلامة ميكانيكية",
            "risk_level": "متوسط",
            "description": "الوقاية من مخاطر الآلات والمعدات الميكانيكية",
            "compliance_requirements": "معايير السلامة الصناعية",
            "status": "نشط"
        },
        {
            "code": "PSYC001",
            "name": "السلامة النفسية",
            "classification": "سلامة نفسية",
            "risk_level": "متوسط",
            "description": "الحفاظ على الصحة النفسية والعقلية للموظفين",
            "compliance_requirements": "معايير الصحة المهنية",
            "status": "نشط"
        },
        {
            "code": "ENV001",
            "name": "السلامة البيئية",
            "classification": "سلامة بيئية",
            "risk_level": "متوسط",
            "description": "حماية البيئة من التلوث والمخاطر البيئية",
            "compliance_requirements": "قانون حماية البيئة اليمني",
            "status": "نشط"
        }
    ]
    
    # بيانات إجراءات السلامة النموذجية
    self.safety_measures_data = [
        {
            "code": "MEAS001",
            "name": "تركيب أجهزة إنذار الحريق",
            "safety_type": "السلامة من الحرائق",
            "priority": "عالية",
            "cost": 50000,
            "duration": "أسبوعين",
            "responsible": "قسم الصيانة",
            "status": "مكتمل"
        },
        {
            "code": "MEAS002",
            "name": "توفير معدات الحماية الشخصية",
            "safety_type": "السلامة الكيميائية",
            "priority": "حرجة",
            "cost": 75000,
            "duration": "شهر",
            "responsible": "قسم الموارد البشرية",
            "status": "قيد التنفيذ"
        },
        {
            "code": "MEAS003",
            "name": "فحص الأنظمة الكهربائية",
            "safety_type": "السلامة الكهربائية",
            "priority": "عالية",
            "cost": 30000,
            "duration": "أسبوع",
            "responsible": "قسم الكهرباء",
            "status": "مخطط"
        }
    ]
    
    # بيانات الحوادث النموذجية
    self.incidents_data = [
        {
            "number": "INC001",
            "date": "2024-01-15",
            "type": "حريق صغير",
            "location": "المخزن الرئيسي",
            "risk_level": "متوسط",
            "injuries": "لا يوجد",
            "damages": "أضرار طفيفة",
            "status": "مغلق"
        },
        {
            "number": "INC002",
            "date": "2024-02-03",
            "type": "انسكاب مواد كيميائية",
            "location": "المختبر",
            "risk_level": "عالي",
            "injuries": "إصابة طفيفة",
            "damages": "تلوث محدود",
            "status": "قيد التحقيق"
        }
    ]
    
    # بيانات برامج التدريب النموذجية
    self.training_programs_data = [
        {
            "code": "TRAIN001",
            "name": "برنامج مكافحة الحرائق",
            "safety_type": "السلامة من الحرائق",
            "duration": "3 أيام",
            "trainees": 25,
            "cost": 15000,
            "trainer": "خبير السلامة",
            "status": "مجدول"
        },
        {
            "code": "TRAIN002",
            "name": "التعامل مع المواد الخطرة",
            "safety_type": "السلامة الكيميائية",
            "duration": "5 أيام",
            "trainees": 15,
            "cost": 25000,
            "trainer": "مختص كيميائي",
            "status": "مكتمل"
        }
    ]
    
    # تحديث الجداول
    self.update_safety_types_table()
    self.update_safety_measures_table()
    self.update_incidents_table()
    self.update_training_programs_table()

def update_safety_types_table_function(self):
    """تحديث جدول أنواع السلامة"""
    self.safety_types_table.setRowCount(len(self.safety_types_data))
    
    for row, safety_type in enumerate(self.safety_types_data):
        self.safety_types_table.setItem(row, 0, QTableWidgetItem(safety_type["code"]))
        self.safety_types_table.setItem(row, 1, QTableWidgetItem(safety_type["name"]))
        self.safety_types_table.setItem(row, 2, QTableWidgetItem(safety_type["classification"]))
        self.safety_types_table.setItem(row, 3, QTableWidgetItem(safety_type["risk_level"]))
        self.safety_types_table.setItem(row, 4, QTableWidgetItem(safety_type["description"]))
        self.safety_types_table.setItem(row, 5, QTableWidgetItem(safety_type["compliance_requirements"]))
        self.safety_types_table.setItem(row, 6, QTableWidgetItem(safety_type["status"]))

def update_safety_measures_table_function(self):
    """تحديث جدول إجراءات السلامة"""
    self.safety_measures_table.setRowCount(len(self.safety_measures_data))
    
    for row, measure in enumerate(self.safety_measures_data):
        self.safety_measures_table.setItem(row, 0, QTableWidgetItem(measure["code"]))
        self.safety_measures_table.setItem(row, 1, QTableWidgetItem(measure["name"]))
        self.safety_measures_table.setItem(row, 2, QTableWidgetItem(measure["safety_type"]))
        self.safety_measures_table.setItem(row, 3, QTableWidgetItem(measure["priority"]))
        self.safety_measures_table.setItem(row, 4, QTableWidgetItem(f"{measure['cost']:,} ريال"))
        self.safety_measures_table.setItem(row, 5, QTableWidgetItem(measure["duration"]))
        self.safety_measures_table.setItem(row, 6, QTableWidgetItem(measure["responsible"]))
        self.safety_measures_table.setItem(row, 7, QTableWidgetItem(measure["status"]))

def update_incidents_table_function(self):
    """تحديث جدول الحوادث"""
    self.incidents_table.setRowCount(len(self.incidents_data))
    
    for row, incident in enumerate(self.incidents_data):
        self.incidents_table.setItem(row, 0, QTableWidgetItem(incident["number"]))
        self.incidents_table.setItem(row, 1, QTableWidgetItem(incident["date"]))
        self.incidents_table.setItem(row, 2, QTableWidgetItem(incident["type"]))
        self.incidents_table.setItem(row, 3, QTableWidgetItem(incident["location"]))
        self.incidents_table.setItem(row, 4, QTableWidgetItem(incident["risk_level"]))
        self.incidents_table.setItem(row, 5, QTableWidgetItem(incident["injuries"]))
        self.incidents_table.setItem(row, 6, QTableWidgetItem(incident["damages"]))
        self.incidents_table.setItem(row, 7, QTableWidgetItem(incident["status"]))

def update_training_programs_table_function(self):
    """تحديث جدول برامج التدريب"""
    self.training_programs_table.setRowCount(len(self.training_programs_data))
    
    for row, program in enumerate(self.training_programs_data):
        self.training_programs_table.setItem(row, 0, QTableWidgetItem(program["code"]))
        self.training_programs_table.setItem(row, 1, QTableWidgetItem(program["name"]))
        self.training_programs_table.setItem(row, 2, QTableWidgetItem(program["safety_type"]))
        self.training_programs_table.setItem(row, 3, QTableWidgetItem(program["duration"]))
        self.training_programs_table.setItem(row, 4, QTableWidgetItem(str(program["trainees"])))
        self.training_programs_table.setItem(row, 5, QTableWidgetItem(f"{program['cost']:,} ريال"))
        self.training_programs_table.setItem(row, 6, QTableWidgetItem(program["trainer"]))
        self.training_programs_table.setItem(row, 7, QTableWidgetItem(program["status"]))
