# 🌳 دليل شجرة النظام الشامل - يمن سوفت

## 📋 **نظرة عامة**

تم تطوير واجهة نظام إدارة الأعمال المتكامل مع **شجرة النظام** الشاملة التي تحتوي على جميع الأقسام والشاشات المطلوبة في نظام ERP متكامل.

---

## 🎯 **المميزات الرئيسية**

### 🌳 **شجرة النظام الهرمية:**
- ✅ **عرض هرمي منسدل** باستخدام أيقونات [+] / [-]
- ✅ **أيقونات مميزة** للمجلدات 📁 والشاشات 📄
- ✅ **إمكانية الوصول السريع** لأي شاشة بالنقر عليها
- ✅ **عرض ثابت** على جانب الشاشة لا يختفي
- ✅ **قابل للتخصيص** بناءً على صلاحيات المستخدم

### 🎨 **التصميم المحسن:**
- ✅ **واجهة احترافية** بألوان متناسقة
- ✅ **ترقيم تلقائي** للشاشات (01، 02، 03...)
- ✅ **تلميحات مفيدة** عند التمرير فوق العناصر
- ✅ **تحريك سلس** للتوسع والانهيار
- ✅ **تمييز بصري** للعناصر المحددة والمرور عليها

---

## 📁 **الأقسام الرئيسية**

### 1️⃣ **📁 التهيئة** (15 شاشة)
**الوظيفة:** إعداد النظام الأساسي والبيانات الأولية

| الترتيب | الشاشة | الوظيفة |
|---------|--------|---------|
| 01 | المؤشرات العامة | تحديد خصائص النظام العامة |
| 02 | إعداد قواعد النظام | تفعيل أو تعطيل خصائص النظام |
| 03 | تهيئة العملات | تعريف العملات وأسعار الصرف |
| 04 | الأقاليم الدولية / الدول | إعداد التقسيم الجغرافي الدولي |
| 05 | بيانات المحافظات / المناطق / المدن | الهيكل الإداري الجغرافي |
| 06 | مجموعات المحافظات | تجميع إداري للمناطق |
| 07 | شجرة الفروع | عرض هيكلي لفروع المؤسسة |
| 08 | تعريف الفرع | إنشاء أو تعديل بيانات كل فرع |
| 09 | إعداد الدليل المحاسبي | إنشاء هيكل الحسابات |
| 10 | ترميز الحسابات | إعطاء كل حساب رقم/كود |
| 11 | ربط الحسابات بإدخال المشاريع | ربط الحسابات مع المشاريع |
| 12 | أنواع الهيكل الإداري | تعريف أشكال الهيكل الوظيفي |
| 13 | أنواع السلامة المهنية | إعداد معلومات السلامة |
| 14 | مجموعة الموظفين | تنظيم الموظفين في مجموعات |
| 15 | تعريف أصناف العملاء | تصنيف العملاء حسب النوع |

### 2️⃣ **📁 المدخلات** (7 شاشات)
**الوظيفة:** ربط الحسابات والإعدادات المحاسبية

| الترتيب | الشاشة | الوظيفة |
|---------|--------|---------|
| 01 | الدليل المحاسبي العام للوحدات | ربط كل وحدة بالحسابات |
| 02 | الإدارة المحاسبية الافتراضية | تحديد الإدارة الافتراضية |
| 03 | الربط المحاسبي | إعداد قواعد الربط التلقائي |
| 04 | ربط الحسابات بالحسابات العامة والتدفقات | ترحيل العمليات المالية |
| 05 | ربط الحسابات بالأنشطة أو المراكز | ربط المراكز والأنشطة |
| 06 | إعدادات ترحيل القيود الآلية | تحديد طريقة إنشاء القيود |
| 07 | ربط الحسابات مع الموردين والعملاء والأصناف | ضمان دقة القيود |

### 3️⃣ **📁 إدارة النظام** (10 شاشات)
**الوظيفة:** إدارة المستخدمين والأمان والصيانة

| الترتيب | الشاشة | الوظيفة |
|---------|--------|---------|
| 01 | إدارة المستخدمين | إنشاء وتعديل المستخدمين |
| 02 | صلاحيات المستخدمين | تحديد الصلاحيات |
| 03 | خصوصية المستخدم | تخصيص إعدادات خاصة |
| 04 | الإغلاقات والتوثيقات | قفل الفترات المالية |
| 05 | النسخ الاحتياطي | إنشاء نسخة احتياطية |
| 06 | إدارة قاعدة البيانات | صيانة وإدارة القاعدة |
| 07 | التحكم بواجهات الدخول | إعدادات الأمان |
| 08 | تتبع النشاطات (سجل الأحداث) | تسجيل نشاطات المستخدمين |
| 09 | تهيئة إعدادات النظام التقنية | إعدادات تقنية عامة |
| 10 | مزامنة البيانات بين الفروع | مزامنة البيانات |

### 4️⃣ **📁 أنظمة الحسابات** (16 شاشة)
**الوظيفة:** إدارة العمليات المحاسبية والمالية

| الترتيب | الشاشة | الوظيفة |
|---------|--------|---------|
| 01 | قيود اليومية العامة | إدخال القيود المالية يدوياً |
| 02 | قيود يومية آلية | مراجعة القيود الآلية |
| 03 | دفتر الأستاذ العام | عرض الحركات المالية |
| 04 | ميزان المراجعة | ميزان أرصدة الحسابات |
| 05 | ميزان المراجعة التفصيلي | ميزان الحسابات الفرعية |
| 06 | شجرة الحسابات (الدليل المحاسبي) | عرض هيكل الحسابات |
| 07 | بطاقة حساب | تفاصيل حساب معين |
| 08 | المراكز المحاسبية / مراكز التكلفة | تتبع التكاليف |
| 09 | التحليل المالي | تحليل الأداء المالي |
| 10 | ترحيل القيود | نقل القيود للحسابات النهائية |
| 11 | إقفال الحسابات | إقفال الفترات المالية |
| 12 | تجهيز القوائم المالية | إعداد القوائم المالية |
| 13 | التسويات المحاسبية | تسجيل التسويات |
| 14 | أوامر الدفع / القبض العامة | معالجة مالية عامة |
| 15 | التحويلات البنكية | تسجيل التحويلات |
| 16 | ضبط إعدادات القيود | إعداد خيارات القيود |

### 5️⃣ **📁 أنظمة المخازن** (16 شاشة)
**الوظيفة:** إدارة المخزون والأصناف

| الترتيب | الشاشة | الوظيفة |
|---------|--------|---------|
| 01 | بطاقة صنف | إنشاء أو تعديل بيانات صنف |
| 02 | أنواع الأصناف | تصنيف الأصناف |
| 03 | المجموعات الصنفية | تنظيم الأصناف في مجموعات |
| 04 | الوحدات الصنفية | تعريف وحدات البيع والشراء |
| 05 | تعريف المخازن | إنشاء المخازن |
| 06 | إدخال مخزني | تسجيل كميات جديدة |
| 07 | إخراج مخزني | تسجيل إخراجات |
| 08 | تحويل مخزني بين المخازن | نقل بين المخازن |
| 09 | تسويات مخزنية | معالجة الفروقات |
| 10 | جرد المخزون | تنفيذ عملية جرد |
| 11 | تقرير أرصدة الأصناف | عرض كميات الأصناف |
| 12 | تقرير حركات الأصناف | عمليات الإدخال والإخراج |
| 13 | تقرير التالف والمرتجع | الأصناف التالفة |
| 14 | مستويات المخزون (الحد الأدنى/الأقصى) | تحديد مستويات إعادة الطلب |
| 15 | التكلفة والربحية | عرض كلفة المخزون |
| 16 | إعدادات المخزون | تهيئة خصائص المخزون |

### 6️⃣ **📁 أنظمة الموردين** (13 شاشة)
**الوظيفة:** إدارة الموردين والمشتريات

### 7️⃣ **📁 نظام العملاء** (12 شاشة)
**الوظيفة:** إدارة العملاء والمبيعات

### 8️⃣ **📁 نظام نقاط البيع** (8 شاشات)
**الوظيفة:** إدارة نقاط البيع والمبيعات السريعة

### 9️⃣ **📁 نظام إدارة المعلومات** (8 شاشات)
**الوظيفة:** التقارير والاستعلامات

### 🔟 **📁 الأنظمة المساعدة** (8 شاشات)
**الوظيفة:** أدوات مساعدة وصيانة

---

## 🎮 **كيفية الاستخدام**

### 🖱️ **التنقل في الشجرة:**
1. **انقر على المجلد** (📁) لتوسيعه أو طيه
2. **انقر على الشاشة** (📄) لفتحها
3. **مرر الماوس** فوق العنصر لرؤية التلميح
4. **استخدم شريط التمرير** للتنقل في القائمة الطويلة

### ⌨️ **الاختصارات:**
- **مفاتيح الأسهم** للتنقل
- **Enter** لفتح العنصر المحدد
- **Space** لتوسيع/طي المجلد

---

## ✅ **الحالة الحالية**

### 🟢 **مكتمل:**
- ✅ شجرة النظام الشاملة
- ✅ فواتير البيع (مع هامش الربح)
- ✅ الواجهة الرئيسية
- ✅ نظام التنقل

### 🟡 **قيد التطوير:**
- 🚧 باقي الشاشات (تظهر رسالة "قيد التطوير")
- 🚧 نظام الصلاحيات
- 🚧 قاعدة البيانات المتقدمة

---

## 🎯 **النتيجة النهائية**

**🏆 نظام إدارة أعمال متكامل مع شجرة نظام شاملة تحتوي على:**

📊 **113 شاشة** موزعة على 10 أقسام رئيسية
🌳 **واجهة هرمية** سهلة الاستخدام والتنقل
🎨 **تصميم احترافي** مع ألوان وأيقونات مميزة
⚡ **أداء سريع** واستجابة فورية
🔧 **قابلية التوسع** لإضافة شاشات جديدة

**🎉 جاهز للاستخدام كنظام ERP متكامل!**
