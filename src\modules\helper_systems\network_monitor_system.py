#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب الشبكة المتقدم
Advanced Network Monitor System
"""

import socket
import subprocess
import platform
import time
import threading
from datetime import datetime
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QLineEdit, QTextEdit, QGroupBox,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QProgressBar, QComboBox, QSpinBox, QTabWidget,
                             QWidget, QMessageBox, QCheckBox)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor

class PingThread(QThread):
    """خيط لاختبار الاتصال"""
    ping_result = pyqtSignal(str, bool, float)
    
    def __init__(self, host, count=4):
        super().__init__()
        self.host = host
        self.count = count
        self.running = True
    
    def run(self):
        """تشغيل اختبار الاتصال"""
        for i in range(self.count):
            if not self.running:
                break
            
            try:
                start_time = time.time()
                
                # تحديد نوع النظام
                param = "-n" if platform.system().lower() == "windows" else "-c"
                
                # تشغيل أمر ping
                result = subprocess.run(
                    ["ping", param, "1", self.host],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                
                success = result.returncode == 0
                self.ping_result.emit(self.host, success, response_time)
                
            except Exception as e:
                self.ping_result.emit(self.host, False, 0)
            
            if i < self.count - 1:
                time.sleep(1)
    
    def stop(self):
        """إيقاف الاختبار"""
        self.running = False

class PortScanThread(QThread):
    """خيط لفحص المنافذ"""
    port_result = pyqtSignal(str, int, bool)
    scan_progress = pyqtSignal(int)
    
    def __init__(self, host, start_port, end_port):
        super().__init__()
        self.host = host
        self.start_port = start_port
        self.end_port = end_port
        self.running = True
    
    def run(self):
        """تشغيل فحص المنافذ"""
        total_ports = self.end_port - self.start_port + 1
        
        for i, port in enumerate(range(self.start_port, self.end_port + 1)):
            if not self.running:
                break
            
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((self.host, port))
                sock.close()
                
                is_open = result == 0
                self.port_result.emit(self.host, port, is_open)
                
                progress = int((i + 1) / total_ports * 100)
                self.scan_progress.emit(progress)
                
            except Exception as e:
                self.port_result.emit(self.host, port, False)
    
    def stop(self):
        """إيقاف الفحص"""
        self.running = False

class NetworkMonitorDialog(QDialog):
    """مراقب الشبكة المتقدم"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.ping_threads = []
        self.scan_thread = None
        self.setup_ui()
        self.setup_timers()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🌐 مراقب الشبكة المتقدم")
        self.setGeometry(100, 100, 1000, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🌐 مراقب الشبكة المتقدم")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: white;
                background-color: #3498db;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # التبويبات
        self.tabs = QTabWidget()
        
        # تبويب اختبار الاتصال
        ping_tab = self.create_ping_tab()
        self.tabs.addTab(ping_tab, "🏓 اختبار الاتصال")
        
        # تبويب فحص المنافذ
        port_scan_tab = self.create_port_scan_tab()
        self.tabs.addTab(port_scan_tab, "🔍 فحص المنافذ")
        
        # تبويب معلومات الشبكة
        network_info_tab = self.create_network_info_tab()
        self.tabs.addTab(network_info_tab, "ℹ️ معلومات الشبكة")
        
        # تبويب مراقبة الاتصال
        monitoring_tab = self.create_monitoring_tab()
        self.tabs.addTab(monitoring_tab, "📊 مراقبة الاتصال")
        
        layout.addWidget(self.tabs)
        self.setLayout(layout)
    
    def create_ping_tab(self):
        """إنشاء تبويب اختبار الاتصال"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات الاختبار
        settings_group = QGroupBox("⚙️ إعدادات اختبار الاتصال")
        settings_layout = QHBoxLayout()
        
        settings_layout.addWidget(QLabel("العنوان/IP:"))
        self.ping_host_edit = QLineEdit()
        self.ping_host_edit.setText("google.com")
        settings_layout.addWidget(self.ping_host_edit)
        
        settings_layout.addWidget(QLabel("عدد المحاولات:"))
        self.ping_count_spin = QSpinBox()
        self.ping_count_spin.setRange(1, 100)
        self.ping_count_spin.setValue(4)
        settings_layout.addWidget(self.ping_count_spin)
        
        self.ping_btn = QPushButton("🏓 بدء الاختبار")
        self.ping_btn.clicked.connect(self.start_ping)
        self.ping_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        settings_layout.addWidget(self.ping_btn)
        
        self.stop_ping_btn = QPushButton("⏹️ إيقاف")
        self.stop_ping_btn.clicked.connect(self.stop_ping)
        self.stop_ping_btn.setEnabled(False)
        settings_layout.addWidget(self.stop_ping_btn)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # نتائج الاختبار
        results_group = QGroupBox("📊 نتائج اختبار الاتصال")
        results_layout = QVBoxLayout()
        
        self.ping_results_text = QTextEdit()
        self.ping_results_text.setFont(QFont("Courier", 10))
        self.ping_results_text.setMaximumHeight(300)
        results_layout.addWidget(self.ping_results_text)
        
        results_group.setLayout(results_layout)
        layout.addWidget(results_group)
        
        # إحصائيات سريعة
        stats_group = QGroupBox("📈 إحصائيات سريعة")
        stats_layout = QHBoxLayout()
        
        self.ping_success_label = QLabel("نجح: 0")
        stats_layout.addWidget(self.ping_success_label)
        
        self.ping_failed_label = QLabel("فشل: 0")
        stats_layout.addWidget(self.ping_failed_label)
        
        self.ping_avg_time_label = QLabel("متوسط الوقت: 0 ms")
        stats_layout.addWidget(self.ping_avg_time_label)
        
        stats_layout.addStretch()
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_port_scan_tab(self):
        """إنشاء تبويب فحص المنافذ"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات الفحص
        settings_group = QGroupBox("⚙️ إعدادات فحص المنافذ")
        settings_layout = QHBoxLayout()
        
        settings_layout.addWidget(QLabel("العنوان/IP:"))
        self.scan_host_edit = QLineEdit()
        self.scan_host_edit.setText("127.0.0.1")
        settings_layout.addWidget(self.scan_host_edit)
        
        settings_layout.addWidget(QLabel("من منفذ:"))
        self.start_port_spin = QSpinBox()
        self.start_port_spin.setRange(1, 65535)
        self.start_port_spin.setValue(1)
        settings_layout.addWidget(self.start_port_spin)
        
        settings_layout.addWidget(QLabel("إلى منفذ:"))
        self.end_port_spin = QSpinBox()
        self.end_port_spin.setRange(1, 65535)
        self.end_port_spin.setValue(1000)
        settings_layout.addWidget(self.end_port_spin)
        
        self.scan_btn = QPushButton("🔍 بدء الفحص")
        self.scan_btn.clicked.connect(self.start_port_scan)
        self.scan_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        settings_layout.addWidget(self.scan_btn)
        
        self.stop_scan_btn = QPushButton("⏹️ إيقاف")
        self.stop_scan_btn.clicked.connect(self.stop_port_scan)
        self.stop_scan_btn.setEnabled(False)
        settings_layout.addWidget(self.stop_scan_btn)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # شريط التقدم
        self.scan_progress = QProgressBar()
        layout.addWidget(self.scan_progress)
        
        # نتائج الفحص
        results_group = QGroupBox("📊 نتائج فحص المنافذ")
        results_layout = QVBoxLayout()
        
        self.port_results_table = QTableWidget()
        self.port_results_table.setColumnCount(3)
        self.port_results_table.setHorizontalHeaderLabels(["المنفذ", "الحالة", "الخدمة"])
        self.port_results_table.horizontalHeader().setStretchLastSection(True)
        results_layout.addWidget(self.port_results_table)
        
        results_group.setLayout(results_layout)
        layout.addWidget(results_group)
        
        widget.setLayout(layout)
        return widget
    
    def create_network_info_tab(self):
        """إنشاء تبويب معلومات الشبكة"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # معلومات الشبكة
        info_group = QGroupBox("ℹ️ معلومات الشبكة المحلية")
        info_layout = QVBoxLayout()
        
        self.network_info_text = QTextEdit()
        self.network_info_text.setFont(QFont("Courier", 10))
        info_layout.addWidget(self.network_info_text)
        
        refresh_btn = QPushButton("🔄 تحديث المعلومات")
        refresh_btn.clicked.connect(self.refresh_network_info)
        info_layout.addWidget(refresh_btn)
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        widget.setLayout(layout)
        
        # تحميل المعلومات الأولية
        self.refresh_network_info()
        
        return widget
    
    def create_monitoring_tab(self):
        """إنشاء تبويب مراقبة الاتصال"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # إعدادات المراقبة
        settings_group = QGroupBox("⚙️ إعدادات المراقبة")
        settings_layout = QHBoxLayout()
        
        settings_layout.addWidget(QLabel("العنوان للمراقبة:"))
        self.monitor_host_edit = QLineEdit()
        self.monitor_host_edit.setText("*******")
        settings_layout.addWidget(self.monitor_host_edit)
        
        settings_layout.addWidget(QLabel("فترة الاختبار (ثانية):"))
        self.monitor_interval_spin = QSpinBox()
        self.monitor_interval_spin.setRange(1, 300)
        self.monitor_interval_spin.setValue(5)
        settings_layout.addWidget(self.monitor_interval_spin)
        
        self.monitor_btn = QPushButton("📊 بدء المراقبة")
        self.monitor_btn.clicked.connect(self.toggle_monitoring)
        settings_layout.addWidget(self.monitor_btn)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # جدول المراقبة
        monitor_group = QGroupBox("📈 سجل المراقبة")
        monitor_layout = QVBoxLayout()
        
        self.monitor_table = QTableWidget()
        self.monitor_table.setColumnCount(4)
        self.monitor_table.setHorizontalHeaderLabels(["الوقت", "العنوان", "الحالة", "وقت الاستجابة"])
        self.monitor_table.horizontalHeader().setStretchLastSection(True)
        monitor_layout.addWidget(self.monitor_table)
        
        # أزرار إضافية
        monitor_buttons_layout = QHBoxLayout()
        
        clear_monitor_btn = QPushButton("🗑️ مسح السجل")
        clear_monitor_btn.clicked.connect(self.clear_monitor_log)
        monitor_buttons_layout.addWidget(clear_monitor_btn)
        
        export_monitor_btn = QPushButton("📤 تصدير السجل")
        export_monitor_btn.clicked.connect(self.export_monitor_log)
        monitor_buttons_layout.addWidget(export_monitor_btn)
        
        monitor_buttons_layout.addStretch()
        monitor_layout.addLayout(monitor_buttons_layout)
        
        monitor_group.setLayout(monitor_layout)
        layout.addWidget(monitor_group)
        
        widget.setLayout(layout)
        return widget
    
    def setup_timers(self):
        """إعداد المؤقتات"""
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.monitor_ping)
        self.monitoring = False
        
        # متغيرات الإحصائيات
        self.ping_stats = {"success": 0, "failed": 0, "times": []}
    
    def start_ping(self):
        """بدء اختبار الاتصال"""
        host = self.ping_host_edit.text().strip()
        if not host:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال عنوان للاختبار")
            return
        
        count = self.ping_count_spin.value()
        
        self.ping_btn.setEnabled(False)
        self.stop_ping_btn.setEnabled(True)
        self.ping_results_text.clear()
        
        # إعادة تعيين الإحصائيات
        self.ping_stats = {"success": 0, "failed": 0, "times": []}
        
        # إنشاء خيط الاختبار
        self.ping_thread = PingThread(host, count)
        self.ping_thread.ping_result.connect(self.on_ping_result)
        self.ping_thread.finished.connect(self.on_ping_finished)
        self.ping_thread.start()
        
        self.ping_results_text.append(f"بدء اختبار الاتصال مع {host}...")
    
    def stop_ping(self):
        """إيقاف اختبار الاتصال"""
        if hasattr(self, 'ping_thread') and self.ping_thread.isRunning():
            self.ping_thread.stop()
            self.ping_thread.wait()
        
        self.ping_btn.setEnabled(True)
        self.stop_ping_btn.setEnabled(False)
    
    def on_ping_result(self, host, success, response_time):
        """معالجة نتيجة اختبار الاتصال"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        if success:
            self.ping_results_text.append(f"[{timestamp}] ✅ {host}: {response_time:.2f} ms")
            self.ping_stats["success"] += 1
            self.ping_stats["times"].append(response_time)
        else:
            self.ping_results_text.append(f"[{timestamp}] ❌ {host}: انتهت مهلة الاتصال")
            self.ping_stats["failed"] += 1
        
        self.update_ping_stats()
    
    def on_ping_finished(self):
        """انتهاء اختبار الاتصال"""
        self.ping_btn.setEnabled(True)
        self.stop_ping_btn.setEnabled(False)
        self.ping_results_text.append("\nانتهى اختبار الاتصال.")
    
    def update_ping_stats(self):
        """تحديث إحصائيات الاختبار"""
        success = self.ping_stats["success"]
        failed = self.ping_stats["failed"]
        times = self.ping_stats["times"]
        
        self.ping_success_label.setText(f"نجح: {success}")
        self.ping_failed_label.setText(f"فشل: {failed}")
        
        if times:
            avg_time = sum(times) / len(times)
            self.ping_avg_time_label.setText(f"متوسط الوقت: {avg_time:.2f} ms")
        else:
            self.ping_avg_time_label.setText("متوسط الوقت: 0 ms")
    
    def start_port_scan(self):
        """بدء فحص المنافذ"""
        host = self.scan_host_edit.text().strip()
        if not host:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال عنوان للفحص")
            return
        
        start_port = self.start_port_spin.value()
        end_port = self.end_port_spin.value()
        
        if start_port > end_port:
            QMessageBox.warning(self, "تحذير", "منفذ البداية يجب أن يكون أقل من منفذ النهاية")
            return
        
        self.scan_btn.setEnabled(False)
        self.stop_scan_btn.setEnabled(True)
        self.port_results_table.setRowCount(0)
        self.scan_progress.setValue(0)
        
        # إنشاء خيط الفحص
        self.scan_thread = PortScanThread(host, start_port, end_port)
        self.scan_thread.port_result.connect(self.on_port_result)
        self.scan_thread.scan_progress.connect(self.scan_progress.setValue)
        self.scan_thread.finished.connect(self.on_scan_finished)
        self.scan_thread.start()
    
    def stop_port_scan(self):
        """إيقاف فحص المنافذ"""
        if self.scan_thread and self.scan_thread.isRunning():
            self.scan_thread.stop()
            self.scan_thread.wait()
        
        self.scan_btn.setEnabled(True)
        self.stop_scan_btn.setEnabled(False)
    
    def on_port_result(self, host, port, is_open):
        """معالجة نتيجة فحص المنفذ"""
        if is_open:
            row = self.port_results_table.rowCount()
            self.port_results_table.insertRow(row)
            
            self.port_results_table.setItem(row, 0, QTableWidgetItem(str(port)))
            self.port_results_table.setItem(row, 1, QTableWidgetItem("مفتوح"))
            
            # محاولة تحديد الخدمة
            service = self.get_service_name(port)
            self.port_results_table.setItem(row, 2, QTableWidgetItem(service))
    
    def on_scan_finished(self):
        """انتهاء فحص المنافذ"""
        self.scan_btn.setEnabled(True)
        self.stop_scan_btn.setEnabled(False)
        self.scan_progress.setValue(100)
    
    def get_service_name(self, port):
        """الحصول على اسم الخدمة للمنفذ"""
        common_ports = {
            21: "FTP",
            22: "SSH",
            23: "Telnet",
            25: "SMTP",
            53: "DNS",
            80: "HTTP",
            110: "POP3",
            143: "IMAP",
            443: "HTTPS",
            993: "IMAPS",
            995: "POP3S"
        }
        
        return common_ports.get(port, "غير معروف")
    
    def refresh_network_info(self):
        """تحديث معلومات الشبكة"""
        try:
            info = []
            
            # معلومات الجهاز
            info.append("🖥️ معلومات الجهاز:")
            info.append(f"   اسم الجهاز: {socket.gethostname()}")
            info.append(f"   نظام التشغيل: {platform.system()} {platform.release()}")
            info.append("")
            
            # معلومات الشبكة
            info.append("🌐 معلومات الشبكة:")
            
            # الحصول على IP المحلي
            try:
                s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
                s.close()
                info.append(f"   IP المحلي: {local_ip}")
            except:
                info.append("   IP المحلي: غير متاح")
            
            # DNS
            try:
                dns_servers = []
                if platform.system().lower() == "windows":
                    result = subprocess.run(["nslookup", "google.com"], 
                                          capture_output=True, text=True)
                    # استخراج DNS من النتيجة
                    info.append("   خوادم DNS: متاحة")
                else:
                    info.append("   خوادم DNS: متاحة")
            except:
                info.append("   خوادم DNS: غير متاح")
            
            info.append("")
            info.append("🔗 اختبار الاتصال:")
            
            # اختبار الاتصال مع مواقع مشهورة
            test_sites = ["google.com", "facebook.com", "youtube.com"]
            for site in test_sites:
                try:
                    start_time = time.time()
                    socket.gethostbyname(site)
                    end_time = time.time()
                    response_time = (end_time - start_time) * 1000
                    info.append(f"   ✅ {site}: {response_time:.2f} ms")
                except:
                    info.append(f"   ❌ {site}: غير متاح")
            
            self.network_info_text.setText("\n".join(info))
            
        except Exception as e:
            self.network_info_text.setText(f"خطأ في جلب معلومات الشبكة:\n{str(e)}")
    
    def toggle_monitoring(self):
        """تبديل حالة المراقبة"""
        if not self.monitoring:
            host = self.monitor_host_edit.text().strip()
            if not host:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال عنوان للمراقبة")
                return
            
            interval = self.monitor_interval_spin.value() * 1000  # تحويل لميلي ثانية
            self.monitor_timer.start(interval)
            self.monitoring = True
            self.monitor_btn.setText("⏹️ إيقاف المراقبة")
            
        else:
            self.monitor_timer.stop()
            self.monitoring = False
            self.monitor_btn.setText("📊 بدء المراقبة")
    
    def monitor_ping(self):
        """مراقبة الاتصال"""
        host = self.monitor_host_edit.text().strip()
        
        try:
            start_time = time.time()
            socket.gethostbyname(host)
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            self.add_monitor_entry(host, True, response_time)
            
        except:
            self.add_monitor_entry(host, False, 0)
    
    def add_monitor_entry(self, host, success, response_time):
        """إضافة إدخال لسجل المراقبة"""
        row = self.monitor_table.rowCount()
        self.monitor_table.insertRow(row)
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.monitor_table.setItem(row, 0, QTableWidgetItem(timestamp))
        self.monitor_table.setItem(row, 1, QTableWidgetItem(host))
        
        if success:
            self.monitor_table.setItem(row, 2, QTableWidgetItem("✅ متصل"))
            self.monitor_table.setItem(row, 3, QTableWidgetItem(f"{response_time:.2f} ms"))
        else:
            self.monitor_table.setItem(row, 2, QTableWidgetItem("❌ غير متصل"))
            self.monitor_table.setItem(row, 3, QTableWidgetItem("انتهت المهلة"))
        
        # التمرير للأسفل
        self.monitor_table.scrollToBottom()
        
        # الاحتفاظ بآخر 100 إدخال فقط
        if self.monitor_table.rowCount() > 100:
            self.monitor_table.removeRow(0)
    
    def clear_monitor_log(self):
        """مسح سجل المراقبة"""
        self.monitor_table.setRowCount(0)
    
    def export_monitor_log(self):
        """تصدير سجل المراقبة"""
        if self.monitor_table.rowCount() == 0:
            QMessageBox.information(self, "معلومات", "لا يوجد سجل لتصديره")
            return
        
        from PyQt5.QtWidgets import QFileDialog
        
        filename, _ = QFileDialog.getSaveFileName(self, "تصدير سجل المراقبة", 
                                                "network_monitor.txt", "Text Files (*.txt)")
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("سجل مراقبة الشبكة\n")
                    f.write("=" * 50 + "\n\n")
                    
                    for row in range(self.monitor_table.rowCount()):
                        timestamp = self.monitor_table.item(row, 0).text()
                        host = self.monitor_table.item(row, 1).text()
                        status = self.monitor_table.item(row, 2).text()
                        response_time = self.monitor_table.item(row, 3).text()
                        
                        f.write(f"الوقت: {timestamp}\n")
                        f.write(f"العنوان: {host}\n")
                        f.write(f"الحالة: {status}\n")
                        f.write(f"وقت الاستجابة: {response_time}\n")
                        f.write("-" * 30 + "\n")
                
                QMessageBox.information(self, "تم", "تم تصدير السجل بنجاح")
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تصدير السجل:\n{str(e)}")

def show_network_monitor(parent=None):
    """عرض مراقب الشبكة"""
    dialog = NetworkMonitorDialog(parent)
    dialog.exec_()
