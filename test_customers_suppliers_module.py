#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوحدة العملاء والموردين
Comprehensive test for customers and suppliers module
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_customers_suppliers_module():
    """اختبار وحدة العملاء والموردين"""
    print("🧪 اختبار وحدة العملاء والموردين...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.customers_suppliers_module import CustomersSupplierModule
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار إنشاء وحدة العملاء والموردين
        customers_widget = CustomersSupplierModule(db_manager)
        print("✅ تم إنشاء وحدة العملاء والموردين")
        
        # اختبار تحميل البيانات
        customers_widget.load_customers_suppliers()
        print("✅ تم تحميل البيانات من قاعدة البيانات")
        
        # اختبار عدد السجلات المحملة
        row_count = customers_widget.table.rowCount()
        print(f"📊 عدد السجلات المحملة: {row_count}")
        
        # اختبار البحث والفلترة
        customers_widget.search_edit.setText("test")
        customers_widget.filter_table()
        print("✅ تم اختبار وظيفة البحث")
        
        # اختبار مسح البحث
        customers_widget.search_edit.clear()
        customers_widget.filter_table()
        print("✅ تم اختبار مسح البحث")
        
        # تنظيف
        customers_widget.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وحدة العملاء والموردين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_customer_supplier_dialog():
    """اختبار حوار العميل/المورد"""
    print("\n🧪 اختبار حوار العميل/المورد...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.customers_suppliers_module import CustomerSupplierDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار حوار إضافة عميل/مورد جديد
        dialog = CustomerSupplierDialog(db_manager)
        print("✅ تم إنشاء حوار إضافة عميل/مورد")
        
        # اختبار ملء البيانات
        dialog.code_edit.setText("CUST001")
        dialog.name_ar_edit.setText("عميل تجريبي")
        dialog.name_en_edit.setText("Test Customer")
        dialog.type_combo.setCurrentIndex(0)  # عميل
        dialog.phone_edit.setText("123456789")
        dialog.email_edit.setText("<EMAIL>")
        print("✅ تم ملء البيانات الأساسية")
        
        # اختبار التحقق من صحة البيانات
        if dialog.validate_data():
            print("✅ البيانات صحيحة")
        else:
            print("❌ البيانات غير صحيحة")
        
        # اختبار جمع البيانات
        data = dialog.collect_data()
        if data and data.get('code') and data.get('name_ar'):
            print("✅ تم جمع البيانات بنجاح")
        else:
            print("❌ فشل في جمع البيانات")
        
        # تنظيف
        dialog.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حوار العميل/المورد: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_customers_suppliers_database():
    """اختبار عمليات قاعدة البيانات للعملاء والموردين"""
    print("\n🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار استعلام العملاء والموردين
        query = """
            SELECT code, name_ar, name_en, entity_type, phone, email, 
                   address_ar, tax_number, credit_limit, payment_terms, is_active
            FROM customers_suppliers 
            ORDER BY name_ar
        """
        
        customers_suppliers = db_manager.execute_query(query)
        print(f"✅ تم استعلام {len(customers_suppliers)} عميل/مورد من قاعدة البيانات")
        
        # اختبار استعلام العملاء فقط
        customers_query = """
            SELECT COUNT(*) as count FROM customers_suppliers 
            WHERE entity_type = 'customer' OR entity_type = 'both'
        """
        customers_result = db_manager.execute_query(customers_query)
        customers_count = customers_result[0]['count'] if customers_result else 0
        print(f"👥 عدد العملاء: {customers_count}")
        
        # اختبار استعلام الموردين فقط
        suppliers_query = """
            SELECT COUNT(*) as count FROM customers_suppliers 
            WHERE entity_type = 'supplier' OR entity_type = 'both'
        """
        suppliers_result = db_manager.execute_query(suppliers_query)
        suppliers_count = suppliers_result[0]['count'] if suppliers_result else 0
        print(f"🏭 عدد الموردين: {suppliers_count}")
        
        # اختبار استعلام الحالة النشطة
        active_query = """
            SELECT COUNT(*) as count FROM customers_suppliers 
            WHERE is_active = 1
        """
        active_result = db_manager.execute_query(active_query)
        active_count = active_result[0]['count'] if active_result else 0
        print(f"✅ العملاء/الموردين النشطين: {active_count}")
        
        # اختبار البحث
        search_query = """
            SELECT COUNT(*) as count FROM customers_suppliers 
            WHERE name_ar LIKE ? OR name_en LIKE ? OR code LIKE ?
        """
        search_result = db_manager.execute_query(search_query, ('%شركة%', '%company%', '%001%'))
        search_count = search_result[0]['count'] if search_result else 0
        print(f"🔍 نتائج البحث التجريبي: {search_count}")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_structure():
    """اختبار هيكل قاعدة البيانات"""
    print("\n🧪 اختبار هيكل قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار وجود جدول customers_suppliers
        table_query = "SELECT name FROM sqlite_master WHERE type='table' AND name='customers_suppliers'"
        table_result = db_manager.execute_query(table_query)
        
        if table_result:
            print("✅ جدول customers_suppliers موجود")
        else:
            print("❌ جدول customers_suppliers مفقود")
            return False
        
        # اختبار أعمدة الجدول
        columns_query = "PRAGMA table_info(customers_suppliers)"
        columns = db_manager.execute_query(columns_query)
        column_names = [col['name'] for col in columns]
        
        required_columns = [
            'id', 'code', 'name_ar', 'name_en', 'entity_type', 
            'phone', 'email', 'address_ar', 'tax_number', 
            'credit_limit', 'payment_terms', 'is_active'
        ]
        
        print("\n📋 أعمدة جدول customers_suppliers:")
        for col in required_columns:
            if col in column_names:
                print(f"   ✅ {col}")
            else:
                print(f"   ❌ {col} مفقود")
        
        # عرض جميع الأعمدة الموجودة
        print(f"\n📊 إجمالي الأعمدة الموجودة: {len(column_names)}")
        print(f"الأعمدة: {', '.join(column_names)}")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار هيكل قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار شامل لوحدة العملاء والموردين")
    print("   Comprehensive test for customers and suppliers module")
    print("=" * 60)
    
    # اختبار هيكل قاعدة البيانات
    structure_test = test_database_structure()
    
    # اختبار وحدة العملاء والموردين
    module_test = test_customers_suppliers_module()
    
    # اختبار حوار العميل/المورد
    dialog_test = test_customer_supplier_dialog()
    
    # اختبار عمليات قاعدة البيانات
    database_test = test_customers_suppliers_database()
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار وحدة العملاء والموردين:")
    print(f"   🗄️ هيكل قاعدة البيانات: {'✅ نجح' if structure_test else '❌ فشل'}")
    print(f"   🏗️ اختبار الوحدة: {'✅ نجح' if module_test else '❌ فشل'}")
    print(f"   💬 اختبار الحوار: {'✅ نجح' if dialog_test else '❌ فشل'}")
    print(f"   🗄️ اختبار قاعدة البيانات: {'✅ نجح' if database_test else '❌ فشل'}")
    
    if structure_test and module_test and dialog_test and database_test:
        print("\n🎉 جميع اختبارات وحدة العملاء والموردين نجحت!")
        print("✅ وحدة العملاء والموردين جاهزة للاستخدام")
        
        print("\n📋 الوظائف المتاحة:")
        print("   ➕ إضافة عملاء وموردين جدد")
        print("   ✏️ تعديل بيانات العملاء والموردين")
        print("   🗑️ حذف العملاء والموردين")
        print("   🔍 البحث والتصفية")
        print("   📊 عرض الإحصائيات")
        print("   📋 إدارة معلومات الاتصال والمالية")
    else:
        print("\n⚠️ بعض اختبارات وحدة العملاء والموردين فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)
    
    return 0 if (structure_test and module_test and dialog_test and database_test) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
