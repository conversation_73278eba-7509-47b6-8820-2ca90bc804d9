#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص هيكل قاعدة البيانات
Check Database Structure
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.database.sqlite_manager import SQLiteManager

def check_customers_suppliers_table():
    """فحص جدول العملاء والموردين"""
    print("🔍 فحص هيكل جدول العملاء والموردين...")
    
    try:
        db = SQLiteManager()
        
        # فحص هيكل الجدول
        result = db.execute_query("PRAGMA table_info(customers_suppliers)")
        
        print("\n📋 أعمدة جدول customers_suppliers:")
        print("-" * 50)
        for row in result:
            print(f"  {row['name']} - {row['type']} - {'NOT NULL' if row['notnull'] else 'NULL'}")
        
        # فحص البيانات الموجودة
        count_result = db.execute_query("SELECT COUNT(*) as count FROM customers_suppliers")
        count = count_result[0]['count'] if count_result else 0
        
        print(f"\n📊 عدد السجلات الموجودة: {count}")
        
        if count > 0:
            # عرض عينة من البيانات
            sample = db.execute_query("SELECT * FROM customers_suppliers LIMIT 3")
            print("\n📄 عينة من البيانات:")
            for i, record in enumerate(sample, 1):
                print(f"  {i}. {record.get('code', 'N/A')} - {record.get('name_ar', 'N/A')}")
        
        db.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    check_customers_suppliers_table()