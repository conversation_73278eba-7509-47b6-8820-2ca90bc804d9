#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة بيانات العملاء - نظام Onyx Pro ERP
Customer Data Interface - Onyx Pro ERP System
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QLabel, QLineEdit, QComboBox, QPushButton, QTabWidget,
                             QGroupBox, QCheckBox, QSpinBox, QDateEdit, QTextEdit,
                             QToolBar, QAction, QFrame, QSplitter, QFormLayout,
                             QMessageBox, QApplication)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette, QPixmap, QPainter

try:
    from ...database.sqlite_manager import SQLiteManager
    from ...utils.text_styles import get_title_style, get_button_style
except ImportError:
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))
    from database.sqlite_manager import SQLiteManager
    from utils.text_styles import get_title_style, get_button_style

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class CustomerDataInterface(QWidget):
    """واجهة بيانات العملاء - نظام Onyx Pro ERP"""
    
    # إشارات
    customer_saved = pyqtSignal(dict)
    customer_deleted = pyqtSignal(int)
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.current_customer_id = None
        self.is_editing_mode = False
        self.is_adding_mode = False
        self.setup_ui()
        self.setup_database()
        self.load_combo_data()
        self.set_fields_readonly(True)  # جعل الحقول للقراءة فقط في البداية
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("بيانات العملاء - Onyx Pro ERP")
        self.setGeometry(100, 100, 1200, 800)
        
        # تطبيق الخلفية الوردية الفاتحة
        self.setStyleSheet("""
            QWidget {
                background-color: #fdf2f8;
                font-family: "Tahoma", "Arial", sans-serif;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #e91e63;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                color: #e91e63;
                font-size: 14px;
            }
        """)
        
        layout = QVBoxLayout()
        
        # شريط الأدوات العلوي
        self.create_toolbar()
        layout.addWidget(self.toolbar)
        
        # عنوان النظام
        self.create_header()
        layout.addWidget(self.header_frame)
        
        # البيانات الرئيسية
        self.create_main_data_section()
        layout.addWidget(self.main_data_group)
        
        # التبويبات
        self.create_tabs()
        layout.addWidget(self.tabs)
        
        # بيانات التتبع
        self.create_tracking_section()
        layout.addWidget(self.tracking_group)
        
        
        
        # نمط عام للليبلات لضمان وضوح النص
        self.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50 !important;
                background-color: transparent;
                padding: 5px;
                min-width: 100px;
                border: none;
            }
            
            QLabel[class="form-label"] {
                font-size: 12px;
                padding: 8px;
                min-width: 150px;
            }
            
            QLabel[class="grid-label"] {
                font-size: 11px;
                min-width: 140px;
                max-width: 200px;
            }
            
            QLabel[class="value-label"] {
                font-size: 11px;
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                min-width: 100px;
            }
            
            QCheckBox {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
            
            QLineEdit {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                color: #2c3e50;
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
            }
            
            QLineEdit:read-only {
                background-color: #f5f5f5;
                color: #666;
            }
            
            QTextEdit {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                color: #2c3e50;
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
            }
            
            QTextEdit:read-only {
                background-color: #f5f5f5;
                color: #666;
            }
            
            QComboBox {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                color: #2c3e50;
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
            }
            
            QComboBox:disabled {
                background-color: #f5f5f5;
                color: #666;
            }
            
            QSpinBox, QDateEdit {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                color: #2c3e50;
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
            }
            
            QSpinBox:read-only, QDateEdit:read-only {
                background-color: #f5f5f5;
                color: #666;
            }
        """)

        self.setLayout(layout)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        self.toolbar = QToolBar()
        self.toolbar.setStyleSheet("""
            QToolBar {
                background-color: #e91e63;
                border: none;
                spacing: 5px;
                padding: 5px;
            }
            QToolBar QToolButton {
                background-color: white;
                border: 1px solid #c2185b;
                border-radius: 4px;
                padding: 8px;
                margin: 2px;
                color: #e91e63;
                font-weight: bold;
            }
            QToolBar QToolButton:hover {
                background-color: #f8bbd9;
            }
            QToolBar QToolButton:pressed {
                background-color: #e91e63;
                color: white;
            }
        """)
        
        # أزرار شريط الأدوات
        actions = [
            ("💾", "حفظ", self.save_customer),
            ("➕", "جديد", self.new_customer),
            ("✏️", "تعديل", self.edit_customer),
            ("❌", "إلغاء", self.cancel_edit),
            ("🗑️", "حذف", self.delete_customer),
            ("⏮️", "الأول", self.first_record),
            ("⏪", "السابق", self.previous_record),
            ("⏩", "التالي", self.next_record),
            ("⏭️", "الأخير", self.last_record),
            ("🖨️", "طباعة", self.print_customer),
            ("🔍", "بحث", self.search_customer),
            ("👁️", "معاينة", self.preview_customer),
            ("🚪", "خروج", self.close_interface)
        ]
        
        for icon, text, slot in actions:
            action = QAction(f"{icon} {text}", self)
            action.triggered.connect(slot)
            self.toolbar.addAction(action)
            if text in ["تعديل", "التالي", "طباعة"]:
                self.toolbar.addSeparator()
    
    def create_header(self):
        """إنشاء رأس الصفحة"""
        self.header_frame = QFrame()
        self.header_frame.setStyleSheet("""
            QFrame {
                background-color: #e91e63;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        
        header_layout = QHBoxLayout()
        
        # عنوان الواجهة
        title_label = QLabel("🧾 بيانات العملاء")
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                color: white;
                font-size: 24px;
                font-weight: bold;
                padding: 15px;
            
            }
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # اسم النظام
        system_label = QLabel("Onyx Pro ERP")
        system_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                color: white;
                font-size: 18px;
                font-weight: bold;
                padding: 15px;
            
            }
        """)
        header_layout.addWidget(system_label)
        
        self.header_frame.setLayout(header_layout)
    
    def create_main_data_section(self):
        """إنشاء قسم البيانات الرئيسية"""
        self.main_data_group = QGroupBox("📋 البيانات الرئيسية")
        layout = QGridLayout()
        
        # نمط موحد للليبلات
        label_style = """
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                background-color: transparent;
                padding: 5px;
                min-width: 120px;
                max-width: 200px;
            }
        """

        # الصف الأول
        label1 = QLabel("رقم العميل الرئيسي:")
        label1.setStyleSheet(label_style)
        layout.addWidget(label1, 0, 0)
        self.main_customer_id_edit = QLineEdit()
        layout.addWidget(self.main_customer_id_edit, 0, 1)

        label2 = QLabel("أنواع العملاء:")
        label2.setStyleSheet(label_style)
        layout.addWidget(label2, 0, 2)
        self.customer_types_combo = QComboBox()
        layout.addWidget(self.customer_types_combo, 0, 3)

        label3 = QLabel("رقم الحساب:")
        label3.setStyleSheet(label_style)
        layout.addWidget(label3, 0, 4)
        self.account_number_edit = QLineEdit()
        layout.addWidget(self.account_number_edit, 0, 5)

        # الصف الثاني
        label4 = QLabel("الاسم الأخير:")
        label4.setStyleSheet(label_style)
        layout.addWidget(label4, 1, 0)
        self.last_name_edit = QLineEdit()
        layout.addWidget(self.last_name_edit, 1, 1)

        label5 = QLabel("التصنيف:")
        label5.setStyleSheet(label_style)
        layout.addWidget(label5, 1, 2)
        self.classification_combo = QComboBox()
        layout.addWidget(self.classification_combo, 1, 3)

        label6 = QLabel("المجموعة:")
        label6.setStyleSheet(label_style)
        layout.addWidget(label6, 1, 4)
        self.group_combo = QComboBox()
        layout.addWidget(self.group_combo, 1, 5)

        # الصف الثالث
        label7 = QLabel("رقم العميل:")
        label7.setStyleSheet(label_style)
        layout.addWidget(label7, 2, 0)
        self.customer_id_edit = QLineEdit()
        layout.addWidget(self.customer_id_edit, 2, 1)

        label8 = QLabel("اسم العميل:")
        label8.setStyleSheet(label_style)
        layout.addWidget(label8, 2, 2)
        self.customer_name_edit = QLineEdit()
        self.customer_name_edit.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(self.customer_name_edit, 2, 3)

        label9 = QLabel("رقم المستخدم:")
        label9.setStyleSheet(label_style)
        layout.addWidget(label9, 2, 4)
        self.user_id_edit = QLineEdit()
        layout.addWidget(self.user_id_edit, 2, 5)

        # الصف الرابع
        label10 = QLabel("كلمة السر:")
        label10.setStyleSheet(label_style)
        layout.addWidget(label10, 3, 2)
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        layout.addWidget(self.password_edit, 3, 3)
        
        self.main_data_group.setLayout(layout)
    
    def create_tabs(self):
        """إنشاء التبويبات"""
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #e91e63;
                background-color: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background-color: #f8bbd9;
                border: 1px solid #e91e63;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #e91e63;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #ec407a;
                color: white;
            }
        """)
        
        # تبويب البيانات الرئيسية
        self.main_tab = self.create_main_tab()
        self.tabs.addTab(self.main_tab, "📋 البيانات الرئيسية")
        
        # تبويب بيانات أخرى
        self.other_data_tab = self.create_other_data_tab()
        self.tabs.addTab(self.other_data_tab, "📑 بيانات أخرى")
        
        # تبويب العنوان الوظيفي
        self.job_address_tab = self.create_job_address_tab()
        self.tabs.addTab(self.job_address_tab, "🏢 العنوان الوظيفي")
        
        # تبويب البيانات الشخصية
        self.personal_data_tab = self.create_personal_data_tab()
        self.tabs.addTab(self.personal_data_tab, "👤 البيانات الشخصية")
        
        # تبويب التفويض والمعرفات
        self.authorization_tab = self.create_authorization_tab()
        self.tabs.addTab(self.authorization_tab, "🔐 التفويض والمعرفات")
        
        # تبويب بيانات إضافية
        self.additional_data_tab = self.create_additional_data_tab()
        self.tabs.addTab(self.additional_data_tab, "📄 بيانات إضافية")
        
        # تبويب حقول إضافية
        self.extra_fields_tab = self.create_extra_fields_tab()
        self.tabs.addTab(self.extra_fields_tab, "🗂️ حقول إضافية")

        # ربط إشارة تغيير التبويب
        self.tabs.currentChanged.connect(self.on_tab_changed)
    
    def create_main_tab(self):
        """إنشاء تبويب البيانات الرئيسية"""
        widget = QWidget()
        layout = QFormLayout()

        # نمط موحد للليبلات في التبويبات
        form_label_style = """
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                background-color: transparent;
                padding: 8px;
                min-width: 150px;
                text-align: right;
            }
        """

        # حقول أساسية
        self.company_name_edit = QLineEdit()
        company_label = QLabel("اسم الشركة:")
        company_label.setStyleSheet(form_label_style)
        layout.addRow(company_label, self.company_name_edit)

        self.contact_person_edit = QLineEdit()
        contact_label = QLabel("الشخص المسؤول:")
        contact_label.setStyleSheet(form_label_style)
        layout.addRow(contact_label, self.contact_person_edit)

        self.phone_edit = QLineEdit()
        phone_label = QLabel("رقم الهاتف:")
        phone_label.setStyleSheet(form_label_style)
        layout.addRow(phone_label, self.phone_edit)

        self.mobile_edit = QLineEdit()
        mobile_label = QLabel("رقم الجوال:")
        mobile_label.setStyleSheet(form_label_style)
        layout.addRow(mobile_label, self.mobile_edit)

        self.email_edit = QLineEdit()
        email_label = QLabel("البريد الإلكتروني:")
        email_label.setStyleSheet(form_label_style)
        layout.addRow(email_label, self.email_edit)

        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        address_label = QLabel("العنوان:")
        address_label.setStyleSheet(form_label_style)
        layout.addRow(address_label, self.address_edit)

        widget.setLayout(layout)
        return widget
    
    def create_other_data_tab(self):
        """إنشاء تبويب بيانات أخرى"""
        widget = QWidget()
        layout = QGridLayout()
        
        # نمط موحد للليبلات في الشبكة
        grid_label_style = """
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 11px;
                font-weight: bold;
                color: #2c3e50;
                background-color: transparent;
                padding: 5px;
                min-width: 140px;
                max-width: 200px;
            }
        """

        # الفرع والمحصل
        branch_label = QLabel("الفرع المرتبط بالعميل:")
        branch_label.setStyleSheet(grid_label_style)
        layout.addWidget(branch_label, 0, 0)
        self.branch_combo = QComboBox()
        layout.addWidget(self.branch_combo, 0, 1)

        collector_label = QLabel("رقم المحصل:")
        collector_label.setStyleSheet(grid_label_style)
        layout.addWidget(collector_label, 0, 2)
        self.collector_id_edit = QLineEdit()
        layout.addWidget(self.collector_id_edit, 0, 3)

        # فترات الائتمان
        credit_label = QLabel("فترة الائتمان (يوم):")
        credit_label.setStyleSheet(grid_label_style)
        layout.addWidget(credit_label, 1, 0)
        self.credit_period_spin = QSpinBox()
        self.credit_period_spin.setRange(0, 365)
        layout.addWidget(self.credit_period_spin, 1, 1)

        deferred_credit_label = QLabel("فترة الائتمان للفاتورة المؤجلة (يوم):")
        deferred_credit_label.setStyleSheet(grid_label_style)
        layout.addWidget(deferred_credit_label, 1, 2)
        self.deferred_credit_period_spin = QSpinBox()
        self.deferred_credit_period_spin.setRange(0, 365)
        layout.addWidget(self.deferred_credit_period_spin, 1, 3)

        # خط السير والترتيب
        route_label = QLabel("خط السير:")
        route_label.setStyleSheet(grid_label_style)
        layout.addWidget(route_label, 2, 0)
        self.route_edit = QLineEdit()
        layout.addWidget(self.route_edit, 2, 1)

        route_order_label = QLabel("ترتيب خط السير:")
        route_order_label.setStyleSheet(grid_label_style)
        layout.addWidget(route_order_label, 2, 2)
        self.route_order_spin = QSpinBox()
        self.route_order_spin.setRange(1, 999)
        layout.addWidget(self.route_order_spin, 2, 3)

        # المسوق والموظف
        marketer_label = QLabel("رقم المسوق:")
        marketer_label.setStyleSheet(grid_label_style)
        layout.addWidget(marketer_label, 3, 0)
        self.marketer_id_edit = QLineEdit()
        layout.addWidget(self.marketer_id_edit, 3, 1)

        employee_label = QLabel("رقم الموظف:")
        employee_label.setStyleSheet(grid_label_style)
        layout.addWidget(employee_label, 3, 2)
        self.employee_id_edit = QLineEdit()
        layout.addWidget(self.employee_id_edit, 3, 3)

        # الدرجة والعضوية
        grade_label = QLabel("رقم الدرجة:")
        grade_label.setStyleSheet(grid_label_style)
        layout.addWidget(grade_label, 4, 0)
        self.grade_id_edit = QLineEdit()
        layout.addWidget(self.grade_id_edit, 4, 1)

        membership_label = QLabel("رقم العضوية:")
        membership_label.setStyleSheet(grid_label_style)
        layout.addWidget(membership_label, 4, 2)
        self.membership_id_edit = QLineEdit()
        layout.addWidget(self.membership_id_edit, 4, 3)

        # مركز التكلفة والمورد
        cost_center_label = QLabel("مركز التكلفة:")
        cost_center_label.setStyleSheet(grid_label_style)
        layout.addWidget(cost_center_label, 5, 0)
        self.cost_center_combo = QComboBox()
        layout.addWidget(self.cost_center_combo, 5, 1)

        supplier_label = QLabel("مرتبط برقم مورد:")
        supplier_label.setStyleSheet(grid_label_style)
        layout.addWidget(supplier_label, 5, 2)
        self.linked_supplier_edit = QLineEdit()
        layout.addWidget(self.linked_supplier_edit, 5, 3)
        
        # إعدادات العملة والتسعير
        currency_group = QGroupBox("💵 إعدادات العملة والتسعير")
        currency_layout = QGridLayout()

        currency_label = QLabel("العملة:")
        currency_label.setStyleSheet(grid_label_style)
        currency_layout.addWidget(currency_label, 0, 0)
        self.currency_combo = QComboBox()
        currency_layout.addWidget(self.currency_combo, 0, 1)

        currency_name_label = QLabel("اسم العملة:")
        currency_name_label.setStyleSheet(grid_label_style)
        currency_layout.addWidget(currency_name_label, 0, 2)
        self.currency_name_edit = QLineEdit()
        currency_layout.addWidget(self.currency_name_edit, 0, 3)

        deferred_price_label = QLabel("مستوى التسعيرة للبيع الآجل:")
        deferred_price_label.setStyleSheet(grid_label_style)
        currency_layout.addWidget(deferred_price_label, 1, 0)
        self.deferred_price_level_combo = QComboBox()
        currency_layout.addWidget(self.deferred_price_level_combo, 1, 1)

        cash_price_label = QLabel("مستوى التسعيرة للبيع النقدي:")
        cash_price_label.setStyleSheet(grid_label_style)
        currency_layout.addWidget(cash_price_label, 1, 2)
        self.cash_price_level_combo = QComboBox()
        currency_layout.addWidget(self.cash_price_level_combo, 1, 3)

        campaign_label = QLabel("الحملة الافتراضية:")
        campaign_label.setStyleSheet(grid_label_style)
        currency_layout.addWidget(campaign_label, 2, 0)
        self.default_campaign_edit = QLineEdit()
        currency_layout.addWidget(self.default_campaign_edit, 2, 1)

        # مربعات الاختيار مع نمط واضح
        checkbox_style = """
            QCheckBox {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
        """

        self.stop_customer_check = QCheckBox("توقيف العميل")
        self.stop_customer_check.setStyleSheet(checkbox_style)
        currency_layout.addWidget(self.stop_customer_check, 2, 2)

        self.stop_sales_check = QCheckBox("توقيف المبيعات")
        self.stop_sales_check.setStyleSheet(checkbox_style)
        currency_layout.addWidget(self.stop_sales_check, 2, 3)
        
        currency_group.setLayout(currency_layout)
        
        layout.addWidget(currency_group, 6, 0, 1, 4)
        
        widget.setLayout(layout)
        return widget

    def create_job_address_tab(self):
        """إنشاء تبويب العنوان الوظيفي"""
        widget = QWidget()
        layout = QFormLayout()

        self.job_title_edit = QLineEdit()
        layout.addRow("المسمى الوظيفي:", self.job_title_edit)

        self.department_edit = QLineEdit()
        layout.addRow("القسم:", self.department_edit)

        self.work_address_edit = QTextEdit()
        self.work_address_edit.setMaximumHeight(80)
        layout.addRow("عنوان العمل:", self.work_address_edit)

        self.work_phone_edit = QLineEdit()
        layout.addRow("هاتف العمل:", self.work_phone_edit)

        self.work_fax_edit = QLineEdit()
        layout.addRow("فاكس العمل:", self.work_fax_edit)

        self.work_email_edit = QLineEdit()
        layout.addRow("بريد العمل:", self.work_email_edit)

        widget.setLayout(layout)
        return widget

    def create_personal_data_tab(self):
        """إنشاء تبويب البيانات الشخصية"""
        widget = QWidget()
        layout = QFormLayout()

        self.birth_date_edit = QDateEdit()
        self.birth_date_edit.setDate(QDate.currentDate())
        self.birth_date_edit.setCalendarPopup(True)
        layout.addRow("تاريخ الميلاد:", self.birth_date_edit)

        self.nationality_edit = QLineEdit()
        layout.addRow("الجنسية:", self.nationality_edit)

        self.id_number_edit = QLineEdit()
        layout.addRow("رقم الهوية:", self.id_number_edit)

        self.passport_number_edit = QLineEdit()
        layout.addRow("رقم الجواز:", self.passport_number_edit)

        self.marital_status_combo = QComboBox()
        self.marital_status_combo.addItems(["أعزب", "متزوج", "مطلق", "أرمل"])
        layout.addRow("الحالة الاجتماعية:", self.marital_status_combo)

        self.personal_notes_edit = QTextEdit()
        self.personal_notes_edit.setMaximumHeight(100)
        layout.addRow("ملاحظات شخصية:", self.personal_notes_edit)

        widget.setLayout(layout)
        return widget

    def create_authorization_tab(self):
        """إنشاء تبويب التفويض والمعرفات"""
        widget = QWidget()
        layout = QFormLayout()

        self.authorization_level_combo = QComboBox()
        self.authorization_level_combo.addItems(["مستوى 1", "مستوى 2", "مستوى 3", "مستوى 4"])
        layout.addRow("مستوى التفويض:", self.authorization_level_combo)

        self.credit_limit_edit = QLineEdit()
        layout.addRow("حد الائتمان:", self.credit_limit_edit)

        self.discount_limit_edit = QLineEdit()
        layout.addRow("حد الخصم المسموح:", self.discount_limit_edit)

        # مربعات اختيار للصلاحيات
        permissions_group = QGroupBox("الصلاحيات")
        permissions_layout = QGridLayout()

        self.is_agent_check = QCheckBox("وكيل")
        permissions_layout.addWidget(self.is_agent_check, 0, 0)

        self.is_representative_check = QCheckBox("مندوب")
        permissions_layout.addWidget(self.is_representative_check, 0, 1)

        self.is_vip_check = QCheckBox("عميل مميز")
        permissions_layout.addWidget(self.is_vip_check, 1, 0)

        self.is_blacklisted_check = QCheckBox("قائمة سوداء")
        permissions_layout.addWidget(self.is_blacklisted_check, 1, 1)

        permissions_group.setLayout(permissions_layout)
        layout.addRow(permissions_group)

        widget.setLayout(layout)
        return widget

    def create_additional_data_tab(self):
        """إنشاء تبويب بيانات إضافية"""
        widget = QWidget()
        layout = QFormLayout()

        self.tax_number_edit = QLineEdit()
        layout.addRow("الرقم الضريبي:", self.tax_number_edit)

        self.commercial_register_edit = QLineEdit()
        layout.addRow("السجل التجاري:", self.commercial_register_edit)

        self.bank_name_edit = QLineEdit()
        layout.addRow("اسم البنك:", self.bank_name_edit)

        self.bank_account_edit = QLineEdit()
        layout.addRow("رقم الحساب البنكي:", self.bank_account_edit)

        self.iban_edit = QLineEdit()
        layout.addRow("رقم IBAN:", self.iban_edit)

        self.website_edit = QLineEdit()
        layout.addRow("الموقع الإلكتروني:", self.website_edit)

        self.additional_notes_edit = QTextEdit()
        self.additional_notes_edit.setMaximumHeight(100)
        layout.addRow("ملاحظات إضافية:", self.additional_notes_edit)

        widget.setLayout(layout)
        return widget

    def create_extra_fields_tab(self):
        """إنشاء تبويب حقول إضافية"""
        widget = QWidget()
        layout = QFormLayout()

        # حقول إضافية قابلة للتخصيص
        for i in range(1, 11):
            field_edit = QLineEdit()
            setattr(self, f'extra_field_{i}_edit', field_edit)
            layout.addRow(f"حقل إضافي {i}:", field_edit)

        widget.setLayout(layout)
        return widget

    def create_tracking_section(self):
        """إنشاء قسم بيانات التتبع"""
        self.tracking_group = QGroupBox("🔽 بيانات التتبع")
        layout = QGridLayout()

        # نمط للليبلات في قسم التتبع
        tracking_label_style = """
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 11px;
                font-weight: bold;
                color: #34495e;
                background-color: transparent;
                padding: 5px;
                min-width: 120px;
            }
        """

        # نمط لقيم التتبع
        tracking_value_style = """
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 11px;
                color: #2c3e50;
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                padding: 5px;
                min-width: 100px;
            }
        """

        # معلومات التتبع
        edit_count_label = QLabel("مرات التعديل:")
        edit_count_label.setStyleSheet(tracking_label_style)
        layout.addWidget(edit_count_label, 0, 0)
        self.edit_count_label = QLabel("0")
        self.edit_count_label.setStyleSheet(tracking_value_style)
        layout.addWidget(self.edit_count_label, 0, 1)

        print_count_label = QLabel("مرات الطباعة:")
        print_count_label.setStyleSheet(tracking_label_style)
        layout.addWidget(print_count_label, 0, 2)
        self.print_count_label = QLabel("0")
        self.print_count_label.setStyleSheet(tracking_value_style)
        layout.addWidget(self.print_count_label, 0, 3)

        input_device_label = QLabel("الجهاز المدخل:")
        input_device_label.setStyleSheet(tracking_label_style)
        layout.addWidget(input_device_label, 1, 0)
        self.input_device_label = QLabel("غير محدد")
        self.input_device_label.setStyleSheet(tracking_value_style)
        layout.addWidget(self.input_device_label, 1, 1)

        modified_device_label = QLabel("الجهاز المعدل:")
        modified_device_label.setStyleSheet(tracking_label_style)
        layout.addWidget(modified_device_label, 1, 2)
        self.modified_device_label = QLabel("غير محدد")
        self.modified_device_label.setStyleSheet(tracking_value_style)
        layout.addWidget(self.modified_device_label, 1, 3)

        input_date_label = QLabel("تاريخ الإدخال:")
        input_date_label.setStyleSheet(tracking_label_style)
        layout.addWidget(input_date_label, 2, 0)
        self.input_date_label = QLabel("غير محدد")
        self.input_date_label.setStyleSheet(tracking_value_style)
        layout.addWidget(self.input_date_label, 2, 1)

        last_modified_label = QLabel("تاريخ آخر تعديل:")
        last_modified_label.setStyleSheet(tracking_label_style)
        layout.addWidget(last_modified_label, 2, 2)
        self.last_modified_label = QLabel("غير محدد")
        self.last_modified_label.setStyleSheet(tracking_value_style)
        layout.addWidget(self.last_modified_label, 2, 3)

        record_creator_label = QLabel("مدخل السجل:")
        record_creator_label.setStyleSheet(tracking_label_style)
        layout.addWidget(record_creator_label, 3, 0)
        self.record_creator_label = QLabel("غير محدد")
        self.record_creator_label.setStyleSheet(tracking_value_style)
        layout.addWidget(self.record_creator_label, 3, 1)

        record_modifier_label = QLabel("معدل السجل:")
        record_modifier_label.setStyleSheet(tracking_label_style)
        layout.addWidget(record_modifier_label, 3, 2)
        self.record_modifier_label = QLabel("غير محدد")
        self.record_modifier_label.setStyleSheet(tracking_value_style)
        layout.addWidget(self.record_modifier_label, 3, 3)

        self.tracking_group.setLayout(layout)

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            # إنشاء جدول العملاء
            create_table_query = """
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                main_customer_id TEXT,
                customer_type TEXT,
                account_number TEXT,
                last_name TEXT,
                classification TEXT,
                customer_group TEXT,
                customer_id TEXT UNIQUE,
                customer_name TEXT NOT NULL,
                user_id TEXT,
                password TEXT,
                company_name TEXT,
                contact_person TEXT,
                phone TEXT,
                mobile TEXT,
                email TEXT,
                address TEXT,
                branch_id TEXT,
                collector_id TEXT,
                credit_period INTEGER DEFAULT 0,
                deferred_credit_period INTEGER DEFAULT 0,
                route TEXT,
                route_order INTEGER DEFAULT 1,
                marketer_id TEXT,
                employee_id TEXT,
                grade_id TEXT,
                membership_id TEXT,
                cost_center TEXT,
                linked_supplier TEXT,
                currency TEXT,
                currency_name TEXT,
                deferred_price_level TEXT,
                cash_price_level TEXT,
                default_campaign TEXT,
                stop_customer BOOLEAN DEFAULT 0,
                stop_sales BOOLEAN DEFAULT 0,
                job_title TEXT,
                department TEXT,
                work_address TEXT,
                work_phone TEXT,
                work_fax TEXT,
                work_email TEXT,
                birth_date DATE,
                nationality TEXT,
                id_number TEXT,
                passport_number TEXT,
                marital_status TEXT,
                personal_notes TEXT,
                authorization_level TEXT,
                credit_limit DECIMAL(15,2) DEFAULT 0,
                discount_limit DECIMAL(5,2) DEFAULT 0,
                is_agent BOOLEAN DEFAULT 0,
                is_representative BOOLEAN DEFAULT 0,
                is_vip BOOLEAN DEFAULT 0,
                is_blacklisted BOOLEAN DEFAULT 0,
                tax_number TEXT,
                commercial_register TEXT,
                bank_name TEXT,
                bank_account TEXT,
                iban TEXT,
                website TEXT,
                additional_notes TEXT,
                extra_field_1 TEXT,
                extra_field_2 TEXT,
                extra_field_3 TEXT,
                extra_field_4 TEXT,
                extra_field_5 TEXT,
                extra_field_6 TEXT,
                extra_field_7 TEXT,
                extra_field_8 TEXT,
                extra_field_9 TEXT,
                extra_field_10 TEXT,
                edit_count INTEGER DEFAULT 0,
                print_count INTEGER DEFAULT 0,
                input_device TEXT,
                modified_device TEXT,
                input_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_modified DATETIME DEFAULT CURRENT_TIMESTAMP,
                record_creator TEXT,
                record_modifier TEXT,
                is_active BOOLEAN DEFAULT 1
            )
            """
            self.db_manager.execute_update(create_table_query)

        except Exception as e:
            QMessageBox.critical(self, "خطأ في قاعدة البيانات", f"فشل في إعداد قاعدة البيانات:\n{str(e)}")

    def load_combo_data(self):
        """تحميل بيانات القوائم المنسدلة"""
        try:
            # أنواع العملاء
            customer_types = ["عميل عادي", "عميل مميز", "عميل تجاري", "عميل حكومي"]
            self.customer_types_combo.addItems(customer_types)

            # التصنيفات
            classifications = ["تصنيف أ", "تصنيف ب", "تصنيف ج"]
            self.classification_combo.addItems(classifications)

            # المجموعات
            groups = ["مجموعة 1", "مجموعة 2", "مجموعة 3"]
            self.group_combo.addItems(groups)

            # الفروع
            branches = ["الفرع الرئيسي", "فرع الشمال", "فرع الجنوب", "فرع الشرق", "فرع الغرب"]
            self.branch_combo.addItems(branches)

            # مراكز التكلفة
            cost_centers = ["مركز 1", "مركز 2", "مركز 3"]
            self.cost_center_combo.addItems(cost_centers)

            # العملات
            currencies = ["ريال سعودي", "دولار أمريكي", "يورو", "جنيه إسترليني"]
            self.currency_combo.addItems(currencies)

            # مستويات التسعير
            price_levels = ["مستوى 1", "مستوى 2", "مستوى 3", "مستوى 4"]
            self.deferred_price_level_combo.addItems(price_levels)
            self.cash_price_level_combo.addItems(price_levels)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات القوائم:\n{str(e)}")

    def apply_label_styles_to_all_tabs(self):
        """تطبيق أنماط الليبلات على جميع التبويبات"""
        form_label_style = """
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                background-color: transparent;
                padding: 8px;
                min-width: 150px;
            }
        """

        # تطبيق النمط على جميع الليبلات في التبويبات
        for tab_index in range(self.tabs.count()):
            tab_widget = self.tabs.widget(tab_index)
            if tab_widget:
                for label in tab_widget.findChildren(QLabel):
                    # تجاهل ليبلات القيم (التي تحتوي على بيانات)
                    if not hasattr(label, 'buddy') or label.buddy() is None:
                        continue
                    label.setStyleSheet(form_label_style)

    # وظائف شريط الأدوات
    def save_customer(self):
        """حفظ بيانات العميل"""
        if not self.is_editing_mode and not self.is_adding_mode:
            QMessageBox.warning(self, "تحذير", "يجب تفعيل وضع التعديل أو الإضافة أولاً")
            return

        try:
            # التحقق من البيانات المطلوبة
            if not self.customer_name_edit.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم العميل")
                return

            # جمع البيانات
            customer_data = self.collect_customer_data()

            if self.is_adding_mode:
                # إضافة عميل جديد
                self.insert_customer(customer_data)
                QMessageBox.information(self, "نجح", "تم إضافة العميل الجديد بنجاح")
            elif self.is_editing_mode:
                # تحديث عميل موجود
                self.update_customer(customer_data)
                QMessageBox.information(self, "نجح", "تم تحديث بيانات العميل بنجاح")

            # إنهاء وضع التعديل/الإضافة
            self.finish_editing()
            self.customer_saved.emit(customer_data)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ بيانات العميل:\n{str(e)}")

    def new_customer(self):
        """إضافة عميل جديد"""
        # مسح النموذج
        self.clear_form()

        # تفعيل وضع الإضافة
        self.is_adding_mode = True
        self.is_editing_mode = False
        self.current_customer_id = None

        # تفعيل الحقول للكتابة
        self.set_fields_readonly(False)

        # التركيز على حقل اسم العميل
        self.customer_name_edit.setFocus()

        QMessageBox.information(self, "عميل جديد", "تم تفعيل وضع إضافة عميل جديد\nيمكنك الآن إدخال البيانات")

    def edit_customer(self):
        """تعديل بيانات العميل"""
        if not self.current_customer_id:
            QMessageBox.warning(self, "تحذير", "لا يوجد عميل محدد للتعديل")
            return

        # تفعيل وضع التعديل
        self.is_editing_mode = True
        self.is_adding_mode = False

        # تفعيل الحقول للكتابة
        self.set_fields_readonly(False)

        QMessageBox.information(self, "وضع التعديل", "تم تفعيل وضع التعديل\nيمكنك الآن تعديل البيانات")

    def cancel_edit(self):
        """إلغاء التعديل أو الإضافة"""
        if self.is_adding_mode:
            reply = QMessageBox.question(
                self, "إلغاء الإضافة",
                "هل أنت متأكد من إلغاء إضافة العميل الجديد؟\nسيتم فقدان جميع البيانات المدخلة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
        elif self.is_editing_mode:
            reply = QMessageBox.question(
                self, "إلغاء التعديل",
                "هل أنت متأكد من إلغاء التعديل؟\nسيتم فقدان جميع التغييرات.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
        else:
            return

        if reply == QMessageBox.Yes:
            if self.is_adding_mode:
                # مسح النموذج والعودة للحالة الأولى
                self.clear_form()
            elif self.is_editing_mode and self.current_customer_id:
                # إعادة تحميل البيانات الأصلية
                self.reload_current_customer()

            # إنهاء وضع التعديل/الإضافة
            self.finish_editing()

    def finish_editing(self):
        """إنهاء وضع التعديل أو الإضافة"""
        self.is_editing_mode = False
        self.is_adding_mode = False

        # جعل الحقول للقراءة فقط
        self.set_fields_readonly(True)

        # تحديث حالة الأزرار
        self.update_toolbar_state()

    def delete_customer(self):
        """حذف العميل"""
        if not self.current_customer_id:
            QMessageBox.warning(self, "تحذير", "لا يوجد عميل محدد للحذف")
            return

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف العميل '{self.customer_name_edit.text()}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                query = "UPDATE customers SET is_active = 0 WHERE id = ?"
                self.db_manager.execute_update(query, (self.current_customer_id,))

                QMessageBox.information(self, "تم الحذف", "تم حذف العميل بنجاح")
                self.customer_deleted.emit(self.current_customer_id)
                self.clear_form()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف العميل:\n{str(e)}")

    def first_record(self):
        """الانتقال للسجل الأول"""
        if self.is_editing_mode or self.is_adding_mode:
            QMessageBox.warning(self, "تحذير", "يجب حفظ أو إلغاء التغييرات الحالية أولاً")
            return

        try:
            query = "SELECT * FROM customers WHERE is_active = 1 ORDER BY id LIMIT 1"
            result = self.db_manager.execute_query(query)

            if result:
                self.load_customer_data(result[0])
                self.finish_editing()  # التأكد من وضع العرض
            else:
                QMessageBox.information(self, "معلومات", "لا توجد سجلات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل السجل:\n{str(e)}")

    def previous_record(self):
        """الانتقال للسجل السابق"""
        if self.is_editing_mode or self.is_adding_mode:
            QMessageBox.warning(self, "تحذير", "يجب حفظ أو إلغاء التغييرات الحالية أولاً")
            return

        if not self.current_customer_id:
            return

        try:
            query = "SELECT * FROM customers WHERE is_active = 1 AND id < ? ORDER BY id DESC LIMIT 1"
            result = self.db_manager.execute_query(query, (self.current_customer_id,))

            if result:
                self.load_customer_data(result[0])
                self.finish_editing()  # التأكد من وضع العرض
            else:
                QMessageBox.information(self, "معلومات", "هذا هو السجل الأول")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل السجل:\n{str(e)}")

    def next_record(self):
        """الانتقال للسجل التالي"""
        if self.is_editing_mode or self.is_adding_mode:
            QMessageBox.warning(self, "تحذير", "يجب حفظ أو إلغاء التغييرات الحالية أولاً")
            return

        if not self.current_customer_id:
            return

        try:
            query = "SELECT * FROM customers WHERE is_active = 1 AND id > ? ORDER BY id LIMIT 1"
            result = self.db_manager.execute_query(query, (self.current_customer_id,))

            if result:
                self.load_customer_data(result[0])
                self.finish_editing()  # التأكد من وضع العرض
            else:
                QMessageBox.information(self, "معلومات", "هذا هو السجل الأخير")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل السجل:\n{str(e)}")

    def last_record(self):
        """الانتقال للسجل الأخير"""
        if self.is_editing_mode or self.is_adding_mode:
            QMessageBox.warning(self, "تحذير", "يجب حفظ أو إلغاء التغييرات الحالية أولاً")
            return

        try:
            query = "SELECT * FROM customers WHERE is_active = 1 ORDER BY id DESC LIMIT 1"
            result = self.db_manager.execute_query(query)

            if result:
                self.load_customer_data(result[0])
                self.finish_editing()  # التأكد من وضع العرض
            else:
                QMessageBox.information(self, "معلومات", "لا توجد سجلات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل السجل:\n{str(e)}")

    def print_customer(self):
        """طباعة بيانات العميل"""
        if not self.current_customer_id:
            QMessageBox.warning(self, "تحذير", "لا يوجد عميل محدد للطباعة")
            return

        # تحديث عداد الطباعة
        try:
            current_count = int(self.print_count_label.text())
            new_count = current_count + 1

            query = "UPDATE customers SET print_count = ? WHERE id = ?"
            self.db_manager.execute_update(query, (new_count, self.current_customer_id))

            self.print_count_label.setText(str(new_count))

            QMessageBox.information(self, "طباعة", "تم إرسال البيانات للطباعة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الطباعة:\n{str(e)}")

    def search_customer(self):
        """البحث عن عميل"""
        from PyQt5.QtWidgets import QInputDialog

        search_text, ok = QInputDialog.getText(
            self, "البحث عن عميل",
            "أدخل اسم العميل أو رقم العميل:"
        )

        if ok and search_text.strip():
            try:
                query = """
                SELECT * FROM customers
                WHERE is_active = 1 AND (
                    customer_name LIKE ? OR
                    customer_id LIKE ? OR
                    phone LIKE ? OR
                    mobile LIKE ?
                )
                LIMIT 1
                """
                search_pattern = f"%{search_text.strip()}%"
                result = self.db_manager.execute_query(
                    query, (search_pattern, search_pattern, search_pattern, search_pattern)
                )

                if result:
                    self.load_customer_data(result[0])
                    QMessageBox.information(self, "نتيجة البحث", "تم العثور على العميل")
                else:
                    QMessageBox.information(self, "نتيجة البحث", "لم يتم العثور على العميل")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في البحث:\n{str(e)}")

    def preview_customer(self):
        """معاينة بيانات العميل"""
        if not self.current_customer_id:
            QMessageBox.warning(self, "تحذير", "لا يوجد عميل محدد للمعاينة")
            return

        # إنشاء نافذة معاينة
        preview_text = self.generate_customer_preview()

        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton

        preview_dialog = QDialog(self)
        preview_dialog.setWindowTitle("معاينة بيانات العميل")
        preview_dialog.setGeometry(200, 200, 600, 500)

        layout = QVBoxLayout()

        text_edit = QTextEdit()
        text_edit.setPlainText(preview_text)
        text_edit.setReadOnly(True)
        layout.addWidget(text_edit)

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(preview_dialog.accept)
        layout.addWidget(close_btn)

        preview_dialog.setLayout(layout)
        preview_dialog.exec_()

    def close_interface(self):
        """إغلاق الواجهة"""
        self.close()

    # وظائف مساعدة
    def collect_customer_data(self):
        """جمع بيانات العميل من النموذج"""
        return {
            'main_customer_id': self.main_customer_id_edit.text(),
            'customer_type': self.customer_types_combo.currentText(),
            'account_number': self.account_number_edit.text(),
            'last_name': self.last_name_edit.text(),
            'classification': self.classification_combo.currentText(),
            'customer_group': self.group_combo.currentText(),
            'customer_id': self.customer_id_edit.text(),
            'customer_name': self.customer_name_edit.text(),
            'user_id': self.user_id_edit.text(),
            'password': self.password_edit.text(),
            'company_name': self.company_name_edit.text(),
            'contact_person': self.contact_person_edit.text(),
            'phone': self.phone_edit.text(),
            'mobile': self.mobile_edit.text(),
            'email': self.email_edit.text(),
            'address': self.address_edit.toPlainText(),
            'branch_id': self.branch_combo.currentText(),
            'collector_id': self.collector_id_edit.text(),
            'credit_period': self.credit_period_spin.value(),
            'deferred_credit_period': self.deferred_credit_period_spin.value(),
            'route': self.route_edit.text(),
            'route_order': self.route_order_spin.value(),
            'marketer_id': self.marketer_id_edit.text(),
            'employee_id': self.employee_id_edit.text(),
            'grade_id': self.grade_id_edit.text(),
            'membership_id': self.membership_id_edit.text(),
            'cost_center': self.cost_center_combo.currentText(),
            'linked_supplier': self.linked_supplier_edit.text(),
            'currency': self.currency_combo.currentText(),
            'currency_name': self.currency_name_edit.text(),
            'deferred_price_level': self.deferred_price_level_combo.currentText(),
            'cash_price_level': self.cash_price_level_combo.currentText(),
            'default_campaign': self.default_campaign_edit.text(),
            'stop_customer': self.stop_customer_check.isChecked(),
            'stop_sales': self.stop_sales_check.isChecked(),
            'job_title': self.job_title_edit.text(),
            'department': self.department_edit.text(),
            'work_address': self.work_address_edit.toPlainText(),
            'work_phone': self.work_phone_edit.text(),
            'work_fax': self.work_fax_edit.text(),
            'work_email': self.work_email_edit.text(),
            'birth_date': self.birth_date_edit.date().toString('yyyy-MM-dd'),
            'nationality': self.nationality_edit.text(),
            'id_number': self.id_number_edit.text(),
            'passport_number': self.passport_number_edit.text(),
            'marital_status': self.marital_status_combo.currentText(),
            'personal_notes': self.personal_notes_edit.toPlainText(),
            'authorization_level': self.authorization_level_combo.currentText(),
            'credit_limit': float(self.credit_limit_edit.text() or 0),
            'discount_limit': float(self.discount_limit_edit.text() or 0),
            'is_agent': self.is_agent_check.isChecked(),
            'is_representative': self.is_representative_check.isChecked(),
            'is_vip': self.is_vip_check.isChecked(),
            'is_blacklisted': self.is_blacklisted_check.isChecked(),
            'tax_number': self.tax_number_edit.text(),
            'commercial_register': self.commercial_register_edit.text(),
            'bank_name': self.bank_name_edit.text(),
            'bank_account': self.bank_account_edit.text(),
            'iban': self.iban_edit.text(),
            'website': self.website_edit.text(),
            'additional_notes': self.additional_notes_edit.toPlainText(),
        }

    def insert_customer(self, data):
        """إدراج عميل جديد"""
        query = """
        INSERT INTO customers (
            main_customer_id, customer_type, account_number, last_name, classification,
            customer_group, customer_id, customer_name, user_id, password,
            company_name, contact_person, phone, mobile, email, address,
            branch_id, collector_id, credit_period, deferred_credit_period,
            route, route_order, marketer_id, employee_id, grade_id, membership_id,
            cost_center, linked_supplier, currency, currency_name,
            deferred_price_level, cash_price_level, default_campaign,
            stop_customer, stop_sales, job_title, department, work_address,
            work_phone, work_fax, work_email, birth_date, nationality,
            id_number, passport_number, marital_status, personal_notes,
            authorization_level, credit_limit, discount_limit, is_agent,
            is_representative, is_vip, is_blacklisted, tax_number,
            commercial_register, bank_name, bank_account, iban, website,
            additional_notes, input_device, record_creator
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )
        """

        values = tuple(data.values()) + ("النظام", "المستخدم الحالي")
        result = self.db_manager.execute_update(query, values)

        if result:
            # الحصول على ID العميل الجديد
            self.current_customer_id = self.db_manager.cursor.lastrowid
            self.update_tracking_info()

    def update_customer(self, data):
        """تحديث بيانات العميل"""
        # تحديث عداد التعديل
        current_count = int(self.edit_count_label.text())
        new_count = current_count + 1

        query = """
        UPDATE customers SET
            main_customer_id=?, customer_type=?, account_number=?, last_name=?,
            classification=?, customer_group=?, customer_id=?, customer_name=?,
            user_id=?, password=?, company_name=?, contact_person=?, phone=?,
            mobile=?, email=?, address=?, branch_id=?, collector_id=?,
            credit_period=?, deferred_credit_period=?, route=?, route_order=?,
            marketer_id=?, employee_id=?, grade_id=?, membership_id=?,
            cost_center=?, linked_supplier=?, currency=?, currency_name=?,
            deferred_price_level=?, cash_price_level=?, default_campaign=?,
            stop_customer=?, stop_sales=?, job_title=?, department=?,
            work_address=?, work_phone=?, work_fax=?, work_email=?,
            birth_date=?, nationality=?, id_number=?, passport_number=?,
            marital_status=?, personal_notes=?, authorization_level=?,
            credit_limit=?, discount_limit=?, is_agent=?, is_representative=?,
            is_vip=?, is_blacklisted=?, tax_number=?, commercial_register=?,
            bank_name=?, bank_account=?, iban=?, website=?, additional_notes=?,
            edit_count=?, last_modified=CURRENT_TIMESTAMP, modified_device=?,
            record_modifier=?
        WHERE id=?
        """

        values = tuple(data.values()) + (new_count, "النظام", "المستخدم الحالي", self.current_customer_id)
        self.db_manager.execute_update(query, values)

        # تحديث واجهة التتبع
        self.edit_count_label.setText(str(new_count))
        self.last_modified_label.setText("الآن")

    def load_customer_data(self, customer_record):
        """تحميل بيانات العميل في النموذج"""
        if not customer_record:
            return

        self.current_customer_id = customer_record[0]

        # إنهاء أي وضع تعديل حالي
        self.finish_editing()

        # تحميل البيانات الرئيسية
        self.main_customer_id_edit.setText(str(customer_record[1] or ""))
        self.customer_types_combo.setCurrentText(str(customer_record[2] or ""))
        self.account_number_edit.setText(str(customer_record[3] or ""))
        self.last_name_edit.setText(str(customer_record[4] or ""))
        self.classification_combo.setCurrentText(str(customer_record[5] or ""))
        self.group_combo.setCurrentText(str(customer_record[6] or ""))
        self.customer_id_edit.setText(str(customer_record[7] or ""))
        self.customer_name_edit.setText(str(customer_record[8] or ""))
        self.user_id_edit.setText(str(customer_record[9] or ""))
        self.password_edit.setText(str(customer_record[10] or ""))

        # تحميل باقي البيانات إذا كانت متوفرة
        if len(customer_record) > 11:
            self.company_name_edit.setText(str(customer_record[11] or ""))
            self.contact_person_edit.setText(str(customer_record[12] or ""))
            self.phone_edit.setText(str(customer_record[13] or ""))
            self.mobile_edit.setText(str(customer_record[14] or ""))
            self.email_edit.setText(str(customer_record[15] or ""))
            self.address_edit.setPlainText(str(customer_record[16] or ""))

        # تحديث معلومات التتبع
        self.update_tracking_info(customer_record)

        # التأكد من أن الحقول في وضع القراءة فقط
        self.set_fields_readonly(True)

    def update_tracking_info(self, customer_record=None):
        """تحديث معلومات التتبع"""
        if customer_record:
            self.edit_count_label.setText(str(customer_record[-15] or 0))
            self.print_count_label.setText(str(customer_record[-14] or 0))
            self.input_device_label.setText(str(customer_record[-13] or "غير محدد"))
            self.modified_device_label.setText(str(customer_record[-12] or "غير محدد"))
            self.input_date_label.setText(str(customer_record[-11] or "غير محدد"))
            self.last_modified_label.setText(str(customer_record[-10] or "غير محدد"))
            self.record_creator_label.setText(str(customer_record[-9] or "غير محدد"))
            self.record_modifier_label.setText(str(customer_record[-8] or "غير محدد"))

    def clear_form(self):
        """مسح النموذج"""
        self.current_customer_id = None

        # مسح جميع الحقول
        for widget in self.findChildren(QLineEdit):
            widget.clear()

        for widget in self.findChildren(QTextEdit):
            widget.clear()

        for widget in self.findChildren(QComboBox):
            widget.setCurrentIndex(0)

        for widget in self.findChildren(QCheckBox):
            widget.setChecked(False)

        for widget in self.findChildren(QSpinBox):
            widget.setValue(0)

        # إعادة تعيين التواريخ
        self.birth_date_edit.setDate(QDate.currentDate())

        # مسح معلومات التتبع
        for label in [self.edit_count_label, self.print_count_label,
                     self.input_device_label, self.modified_device_label,
                     self.input_date_label, self.last_modified_label,
                     self.record_creator_label, self.record_modifier_label]:
            label.setText("غير محدد")

        self.edit_count_label.setText("0")
        self.print_count_label.setText("0")

    def set_fields_readonly(self, readonly):
        """تعيين حالة القراءة فقط للحقول"""
        # حقول النص
        for widget in self.findChildren(QLineEdit):
            widget.setReadOnly(readonly)
            if readonly:
                widget.setStyleSheet("QLineEdit { background-color: #f5f5f5; color: #666; }")
            else:
                widget.setStyleSheet("QLineEdit { background-color: white; color: black; }")

        # مناطق النص
        for widget in self.findChildren(QTextEdit):
            widget.setReadOnly(readonly)
            if readonly:
                widget.setStyleSheet("QTextEdit { background-color: #f5f5f5; color: #666; }")
            else:
                widget.setStyleSheet("QTextEdit { background-color: white; color: black; }")

        # القوائم المنسدلة
        for widget in self.findChildren(QComboBox):
            widget.setEnabled(not readonly)
            if readonly:
                widget.setStyleSheet("QComboBox { background-color: #f5f5f5; color: #666; }")
            else:
                widget.setStyleSheet("QComboBox { background-color: white; color: black; }")

        # مربعات الاختيار
        for widget in self.findChildren(QCheckBox):
            widget.setEnabled(not readonly)

        # صناديق الأرقام
        for widget in self.findChildren(QSpinBox):
            widget.setReadOnly(readonly)
            widget.setEnabled(not readonly)

        # حقول التاريخ
        for widget in self.findChildren(QDateEdit):
            widget.setReadOnly(readonly)
            widget.setEnabled(not readonly)

    def enable_editing(self, enabled):
        """تفعيل/إلغاء تفعيل وضع التعديل - دالة للتوافق مع الكود القديم"""
        self.set_fields_readonly(not enabled)

    def update_toolbar_state(self):
        """تحديث حالة أزرار شريط الأدوات"""
        # يمكن إضافة منطق لتفعيل/إلغاء تفعيل أزرار معينة حسب الحالة
        pass

    def reload_current_customer(self):
        """إعادة تحميل بيانات العميل الحالي"""
        if self.current_customer_id:
            try:
                query = "SELECT * FROM customers WHERE id = ? AND is_active = 1"
                result = self.db_manager.execute_query(query, (self.current_customer_id,))

                if result:
                    self.load_customer_data(result[0])
                else:
                    QMessageBox.warning(self, "تحذير", "لم يتم العثور على بيانات العميل")

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في إعادة تحميل البيانات:\n{str(e)}")

    def set_tab_fields_readonly(self, tab_index, readonly):
        """تعيين حالة القراءة فقط لحقول تبويب معين"""
        current_tab = None

        if tab_index == 0:  # البيانات الرئيسية
            current_tab = self.main_tab
        elif tab_index == 1:  # بيانات أخرى
            current_tab = self.other_data_tab
        elif tab_index == 2:  # العنوان الوظيفي
            current_tab = self.job_address_tab
        elif tab_index == 3:  # البيانات الشخصية
            current_tab = self.personal_data_tab
        elif tab_index == 4:  # التفويض والمعرفات
            current_tab = self.authorization_tab
        elif tab_index == 5:  # بيانات إضافية
            current_tab = self.additional_data_tab
        elif tab_index == 6:  # حقول إضافية
            current_tab = self.extra_fields_tab

        if current_tab:
            # تطبيق حالة القراءة فقط على حقول التبويب المحدد
            for widget in current_tab.findChildren(QLineEdit):
                widget.setReadOnly(readonly)
                if readonly:
                    widget.setStyleSheet("QLineEdit { background-color: #f5f5f5; color: #666; }")
                else:
                    widget.setStyleSheet("QLineEdit { background-color: white; color: black; }")

            for widget in current_tab.findChildren(QTextEdit):
                widget.setReadOnly(readonly)
                if readonly:
                    widget.setStyleSheet("QTextEdit { background-color: #f5f5f5; color: #666; }")
                else:
                    widget.setStyleSheet("QTextEdit { background-color: white; color: black; }")

            for widget in current_tab.findChildren(QComboBox):
                widget.setEnabled(not readonly)

            for widget in current_tab.findChildren(QCheckBox):
                widget.setEnabled(not readonly)

            for widget in current_tab.findChildren(QSpinBox):
                widget.setReadOnly(readonly)
                widget.setEnabled(not readonly)

            for widget in current_tab.findChildren(QDateEdit):
                widget.setReadOnly(readonly)
                widget.setEnabled(not readonly)

    def on_tab_changed(self, index):
        """معالج تغيير التبويب"""
        # إذا كان في وضع التعديل أو الإضافة، تفعيل حقول التبويب الحالي فقط
        if self.is_editing_mode or self.is_adding_mode:
            # جعل جميع التبويبات للقراءة فقط أولاً
            for i in range(self.tabs.count()):
                self.set_tab_fields_readonly(i, True)

            # تفعيل التبويب الحالي للكتابة
            self.set_tab_fields_readonly(index, False)

            # عرض رسالة توضيحية
            tab_names = [
                "البيانات الرئيسية",
                "بيانات أخرى",
                "العنوان الوظيفي",
                "البيانات الشخصية",
                "التفويض والمعرفات",
                "بيانات إضافية",
                "حقول إضافية"
            ]

            if index < len(tab_names):
                current_tab_name = tab_names[index]
                mode_text = "الإضافة" if self.is_adding_mode else "التعديل"

                # عرض رسالة في شريط الحالة أو كتلميح
                if hasattr(self.parent(), 'status_bar'):
                    self.parent().status_bar.showMessage(
                        f"وضع {mode_text} - يمكنك تعديل حقول تبويب: {current_tab_name}"
                    )
        else:
            # في وضع العرض، جميع الحقول للقراءة فقط
            self.set_fields_readonly(True)

    def generate_customer_preview(self):
        """إنتاج معاينة بيانات العميل"""
        preview = f"""
=== بيانات العميل - Onyx Pro ERP ===

البيانات الأساسية:
- رقم العميل: {self.customer_id_edit.text()}
- اسم العميل: {self.customer_name_edit.text()}
- نوع العميل: {self.customer_types_combo.currentText()}
- التصنيف: {self.classification_combo.currentText()}

معلومات الاتصال:
- الهاتف: {self.phone_edit.text()}
- الجوال: {self.mobile_edit.text()}
- البريد الإلكتروني: {self.email_edit.text()}
- العنوان: {self.address_edit.toPlainText()}

الإعدادات المالية:
- حد الائتمان: {self.credit_limit_edit.text()}
- فترة الائتمان: {self.credit_period_spin.value()} يوم
- العملة: {self.currency_combo.currentText()}

الحالة:
- العميل موقوف: {'نعم' if self.stop_customer_check.isChecked() else 'لا'}
- المبيعات موقوفة: {'نعم' if self.stop_sales_check.isChecked() else 'لا'}
- عميل مميز: {'نعم' if self.is_vip_check.isChecked() else 'لا'}

معلومات التتبع:
- مرات التعديل: {self.edit_count_label.text()}
- مرات الطباعة: {self.print_count_label.text()}
- تاريخ الإدخال: {self.input_date_label.text()}
- آخر تعديل: {self.last_modified_label.text()}
        """
        return preview.strip()


# دالة اختبار الواجهة
def main():
    """دالة اختبار الواجهة"""
    import sys
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        def __init__(self):
            self.cursor = type('obj', (object,), {'lastrowid': 1})()

        def execute_query(self, query, params=None):
            return []

        def execute_update(self, query, params=None):
            return True

    db_manager = MockDBManager()

    # إنشاء الواجهة
    window = CustomerDataInterface(db_manager)
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
