#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة الحسابات
Accounts Management Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel, 
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QDialogButtonBox, QHeaderView, QFrame, QGroupBox,
                             QGridLayout, QTextEdit, QSpinBox, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon
def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Aria<PERSON>", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class AccountsManagementWidget(QWidget):
    """ويدجت إدارة الحسابات"""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.setup_ui()
        self.load_accounts()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # العنوان
        title = QLabel("📋 دليل الحسابات")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        layout.addWidget(title)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # منطقة البحث
        search_frame = self.create_search_frame()
        layout.addWidget(search_frame)
        
        # جدول الحسابات
        self.accounts_table = self.create_accounts_table()
        layout.addWidget(self.accounts_table)
        
        # شريط الحالة
        status_frame = self.create_status_frame()
        layout.addWidget(status_frame)
        
        self.setLayout(layout)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        
        toolbar_layout = QHBoxLayout()
        
        # أزرار الأدوات
        self.add_btn = QPushButton("➕ إضافة حساب")
        self.edit_btn = QPushButton("✏️ تعديل")
        self.delete_btn = QPushButton("🗑️ حذف")
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.export_btn = QPushButton("📤 تصدير")
        self.print_btn = QPushButton("🖨️ طباعة")

        # أزرار السندات
        self.receipt_voucher_btn = QPushButton("📄 سند قبض")
        self.payment_voucher_btn = QPushButton("📄 سند صرف")
        
        buttons = [self.add_btn, self.edit_btn, self.delete_btn,
                  self.refresh_btn, self.export_btn, self.print_btn]

        # أزرار السندات مع نمط مختلف
        voucher_buttons = [self.receipt_voucher_btn, self.payment_voucher_btn]

        for btn in buttons:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    font-weight: bold;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QPushButton:pressed {
                    background-color: #21618c;
                }
            """)
            toolbar_layout.addWidget(btn)

        # إضافة فاصل
        toolbar_layout.addWidget(QLabel("|"))

        # أزرار السندات مع نمط مميز
        for btn in voucher_buttons:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    font-weight: bold;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                }
            """)
            toolbar_layout.addWidget(btn)

        # ربط الإشارات
        self.add_btn.clicked.connect(self.add_account)
        self.edit_btn.clicked.connect(self.edit_account)
        self.delete_btn.clicked.connect(self.delete_account)
        self.refresh_btn.clicked.connect(self.load_accounts)
        self.export_btn.clicked.connect(self.export_accounts)
        self.print_btn.clicked.connect(self.print_accounts)

        # ربط إشارات السندات
        self.receipt_voucher_btn.clicked.connect(self.open_receipt_voucher)
        self.payment_voucher_btn.clicked.connect(self.open_payment_voucher)
        
        toolbar_layout.addStretch()
        toolbar_frame.setLayout(toolbar_layout)
        
        return toolbar_frame
    
    def create_search_frame(self):
        """إنشاء منطقة البحث"""
        search_frame = QGroupBox("🔍 البحث والتصفية")
        search_frame.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 5px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        search_layout = QGridLayout()
        
        # حقول البحث
        search_layout.addWidget(QLabel("رقم الحساب:"), 0, 0)
        self.search_code = QLineEdit()
        self.search_code.setPlaceholderText("ابحث برقم الحساب...")
        search_layout.addWidget(self.search_code, 0, 1)
        
        search_layout.addWidget(QLabel("اسم الحساب:"), 0, 2)
        self.search_name = QLineEdit()
        self.search_name.setPlaceholderText("ابحث باسم الحساب...")
        search_layout.addWidget(self.search_name, 0, 3)
        
        search_layout.addWidget(QLabel("نوع الحساب:"), 1, 0)
        self.search_type = QComboBox()
        self.search_type.addItems(["الكل", "أصول", "خصوم", "حقوق ملكية", "إيرادات", "مصروفات"])
        search_layout.addWidget(self.search_type, 1, 1)
        
        # زر البحث
        search_btn = QPushButton("🔍 بحث")
        search_btn.clicked.connect(self.search_accounts)
        search_layout.addWidget(search_btn, 1, 2)
        
        # زر مسح البحث
        clear_btn = QPushButton("🗑️ مسح")
        clear_btn.clicked.connect(self.clear_search)
        search_layout.addWidget(clear_btn, 1, 3)
        
        # تنسيق أزرار البحث
        for btn in [search_btn, clear_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #229954;
                }
            """)
        
        # ربط إشارات البحث التلقائي
        self.search_code.textChanged.connect(self.auto_search)
        self.search_name.textChanged.connect(self.auto_search)
        self.search_type.currentTextChanged.connect(self.auto_search)
        
        search_frame.setLayout(search_layout)
        return search_frame
    
    def create_accounts_table(self):
        """إنشاء جدول الحسابات"""
        table = QTableWidget()
        
        # تعيين الأعمدة
        headers = ["رقم الحساب", "اسم الحساب (عربي)", "اسم الحساب (إنجليزي)", 
                  "نوع الحساب", "الحساب الأب", "مستوى", "نشط", "ملاحظات"]
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        
        # تنسيق الجدول
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # إعدادات الجدول
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSortingEnabled(True)
        
        # تعديل عرض الأعمدة
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # رقم الحساب
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم الحساب عربي
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # اسم الحساب إنجليزي
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # نوع الحساب
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # الحساب الأب
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # مستوى
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # نشط
        header.setSectionResizeMode(7, QHeaderView.Stretch)  # ملاحظات
        
        table.setColumnWidth(0, 120)  # رقم الحساب
        table.setColumnWidth(5, 80)   # مستوى
        table.setColumnWidth(6, 80)   # نشط
        
        # ربط النقر المزدوج
        table.doubleClicked.connect(self.edit_account)
        
        return table
    
    def create_status_frame(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border-top: 1px solid #bdc3c7;
                padding: 10px;
            }
        """)
        
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("جاري تحميل البيانات...")
        self.status_label.setStyleSheet("color: #7f8c8d; font-weight: bold;")
        
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        
        status_frame.setLayout(status_layout)
        return status_frame
    
    def load_accounts(self):
        """تحميل الحسابات من قاعدة البيانات"""
        try:
            # استعلام الحسابات
            query = """
                SELECT account_code, account_name_ar, account_name_en,
                       account_type, parent_id, level, is_active, notes
                FROM chart_of_accounts
                ORDER BY account_code
            """
            
            accounts = self.db_manager.execute_query(query)
            
            # مسح الجدول
            self.accounts_table.setRowCount(0)
            
            # إضافة البيانات
            for i, account in enumerate(accounts):
                self.accounts_table.insertRow(i)
                
                # رقم الحساب
                self.accounts_table.setItem(i, 0, QTableWidgetItem(str(account['account_code'])))
                
                # اسم الحساب عربي
                self.accounts_table.setItem(i, 1, QTableWidgetItem(account['account_name_ar'] or ''))
                
                # اسم الحساب إنجليزي
                self.accounts_table.setItem(i, 2, QTableWidgetItem(account['account_name_en'] or ''))
                
                # نوع الحساب
                account_type_ar = self.get_account_type_arabic(account['account_type'])
                self.accounts_table.setItem(i, 3, QTableWidgetItem(account_type_ar))
                
                # الحساب الأب
                parent = account['parent_id'] or ''
                self.accounts_table.setItem(i, 4, QTableWidgetItem(str(parent)))
                
                # المستوى
                self.accounts_table.setItem(i, 5, QTableWidgetItem(str(account['level'] or 0)))
                
                # نشط
                is_active = "نعم" if account['is_active'] else "لا"
                self.accounts_table.setItem(i, 6, QTableWidgetItem(is_active))
                
                # ملاحظات
                self.accounts_table.setItem(i, 7, QTableWidgetItem(account['notes'] or ''))
            
            # تحديث شريط الحالة
            count = len(accounts)
            self.status_label.setText(f"تم تحميل {count} حساب")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الحسابات:\n{str(e)}")
            self.status_label.setText("خطأ في تحميل البيانات")
    
    def get_account_type_arabic(self, account_type):
        """تحويل نوع الحساب إلى العربية"""
        types_map = {
            'asset': 'أصول',
            'liability': 'خصوم',
            'equity': 'حقوق ملكية',
            'revenue': 'إيرادات',
            'expense': 'مصروفات'
        }
        return types_map.get(account_type, account_type)
    
    def auto_search(self):
        """البحث التلقائي"""
        self.search_accounts()
    
    def search_accounts(self):
        """البحث في الحسابات"""
        code_filter = self.search_code.text().strip()
        name_filter = self.search_name.text().strip()
        type_filter = self.search_type.currentText()
        
        # إخفاء جميع الصفوف أولاً
        for row in range(self.accounts_table.rowCount()):
            self.accounts_table.setRowHidden(row, True)
        
        # إظهار الصفوف المطابقة
        visible_count = 0
        for row in range(self.accounts_table.rowCount()):
            show_row = True
            
            # فلترة رقم الحساب
            if code_filter:
                account_code = self.accounts_table.item(row, 0).text()
                if code_filter not in account_code:
                    show_row = False
            
            # فلترة اسم الحساب
            if name_filter and show_row:
                account_name = self.accounts_table.item(row, 1).text()
                if name_filter.lower() not in account_name.lower():
                    show_row = False
            
            # فلترة نوع الحساب
            if type_filter != "الكل" and show_row:
                account_type = self.accounts_table.item(row, 3).text()
                if type_filter != account_type:
                    show_row = False
            
            if show_row:
                self.accounts_table.setRowHidden(row, False)
                visible_count += 1
        
        # تحديث شريط الحالة
        self.status_label.setText(f"عرض {visible_count} من أصل {self.accounts_table.rowCount()} حساب")
    
    def clear_search(self):
        """مسح البحث"""
        self.search_code.clear()
        self.search_name.clear()
        self.search_type.setCurrentIndex(0)
        
        # إظهار جميع الصفوف
        for row in range(self.accounts_table.rowCount()):
            self.accounts_table.setRowHidden(row, False)
        
        self.status_label.setText(f"عرض جميع الحسابات ({self.accounts_table.rowCount()})")
    
    def add_account(self):
        """إضافة حساب جديد"""
        dialog = AccountDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_accounts()
    
    def edit_account(self):
        """تعديل حساب"""
        current_row = self.accounts_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار حساب للتعديل")
            return
        
        account_code = self.accounts_table.item(current_row, 0).text()
        dialog = AccountDialog(self.db_manager, account_code=account_code, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_accounts()
    
    def delete_account(self):
        """حذف حساب"""
        current_row = self.accounts_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار حساب للحذف")
            return
        
        account_code = self.accounts_table.item(current_row, 0).text()
        account_name = self.accounts_table.item(current_row, 1).text()
        
        reply = QMessageBox.question(
            self, 'تأكيد الحذف',
            f'هل تريد حذف الحساب:\n{account_code} - {account_name}؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # التحقق من وجود حركات على الحساب
                movements_query = "SELECT COUNT(*) as count FROM journal_entry_details WHERE account_code = ?"
                result = self.db_manager.execute_query(movements_query, (account_code,))
                
                if result and result[0]['count'] > 0:
                    QMessageBox.warning(self, "تحذير", 
                                      f"لا يمكن حذف الحساب لوجود {result[0]['count']} حركة عليه")
                    return
                
                # حذف الحساب
                delete_query = "DELETE FROM chart_of_accounts WHERE account_code = ?"
                self.db_manager.execute_update(delete_query, (account_code,))
                
                QMessageBox.information(self, "نجح", "تم حذف الحساب بنجاح")
                self.load_accounts()
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف الحساب:\n{str(e)}")
    
    def export_accounts(self):
        """تصدير الحسابات"""
        QMessageBox.information(self, "تصدير", "وظيفة التصدير قيد التطوير")
    
    def print_accounts(self):
        """طباعة الحسابات"""
        QMessageBox.information(self, "طباعة", "وظيفة الطباعة قيد التطوير")


class AccountDialog(QDialog):
    """حوار إضافة/تعديل حساب"""
    
    def __init__(self, db_manager, account_code=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.account_code = account_code
        self.is_edit_mode = account_code is not None
        
        self.setup_ui()
        if self.is_edit_mode:
            self.load_account_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل حساب" if self.is_edit_mode else "إضافة حساب جديد"
        self.setWindowTitle(title)
        self.setFixedSize(500, 600)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # النموذج
        form_layout = QFormLayout()
        
        # رقم الحساب
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("مثال: 1110")
        if self.is_edit_mode:
            self.code_edit.setReadOnly(True)
        form_layout.addRow("رقم الحساب:", self.code_edit)
        
        # اسم الحساب عربي
        self.name_ar_edit = QLineEdit()
        self.name_ar_edit.setPlaceholderText("مثال: النقدية")
        form_layout.addRow("اسم الحساب (عربي):", self.name_ar_edit)
        
        # اسم الحساب إنجليزي
        self.name_en_edit = QLineEdit()
        self.name_en_edit.setPlaceholderText("مثال: Cash")
        form_layout.addRow("اسم الحساب (إنجليزي):", self.name_en_edit)
        
        # نوع الحساب
        self.type_combo = QComboBox()
        account_types = [
            ("أصول", "asset"),
            ("خصوم", "liability"),
            ("حقوق ملكية", "equity"),
            ("إيرادات", "revenue"),
            ("مصروفات", "expense")
        ]
        for text, data in account_types:
            self.type_combo.addItem(text, data)
        form_layout.addRow("نوع الحساب:", self.type_combo)
        
        # الحساب الأب
        self.parent_combo = QComboBox()
        self.parent_combo.setEditable(True)
        self.load_parent_accounts()
        form_layout.addRow("الحساب الأب:", self.parent_combo)
        
        # المستوى
        self.level_spin = QSpinBox()
        self.level_spin.setRange(1, 10)
        self.level_spin.setValue(1)
        form_layout.addRow("المستوى:", self.level_spin)
        
        # نشط
        self.active_check = QCheckBox("الحساب نشط")
        self.active_check.setChecked(True)
        form_layout.addRow("", self.active_check)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        form_layout.addRow("ملاحظات:", self.notes_edit)
        
        layout.addLayout(form_layout)
        
        # الأزرار
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept_dialog)
        button_box.rejected.connect(self.reject)
        
        # تنسيق الأزرار
        button_box.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                font-weight: bold;
                border-radius: 5px;
                margin: 5px;
            }
            QPushButton[text="OK"] {
                background-color: #27ae60;
                color: white;
                border: none;
            }
            QPushButton[text="OK"]:hover {
                background-color: #229954;
            }
            QPushButton[text="Cancel"] {
                background-color: #e74c3c;
                color: white;
                border: none;
            }
            QPushButton[text="Cancel"]:hover {
                background-color: #c0392b;
            }
        """)
        
        layout.addWidget(button_box)
        self.setLayout(layout)
    
    def load_parent_accounts(self):
        """تحميل الحسابات الأب"""
        try:
            query = "SELECT account_code, account_name_ar FROM chart_of_accounts ORDER BY account_code"
            accounts = self.db_manager.execute_query(query)
            
            self.parent_combo.addItem("", "")  # خيار فارغ
            for account in accounts:
                display_text = f"{account['account_code']} - {account['account_name_ar']}"
                self.parent_combo.addItem(display_text, account['account_code'])
                
        except Exception as e:
            print(f"خطأ في تحميل الحسابات الأب: {e}")
    
    def load_account_data(self):
        """تحميل بيانات الحساب للتعديل"""
        try:
            query = """
                SELECT account_code, account_name_ar, account_name_en,
                       account_type, parent_id, level, is_active, notes
                FROM chart_of_accounts
                WHERE account_code = ?
            """
            
            result = self.db_manager.execute_query(query, (self.account_code,))
            if result:
                account = result[0]
                
                self.code_edit.setText(str(account['account_code']))
                self.name_ar_edit.setText(account['account_name_ar'] or '')
                self.name_en_edit.setText(account['account_name_en'] or '')
                
                # تعيين نوع الحساب
                type_map = {
                    'asset': 0, 'liability': 1, 'equity': 2, 
                    'revenue': 3, 'expense': 4
                }
                self.type_combo.setCurrentIndex(type_map.get(account['account_type'], 0))
                
                # تعيين الحساب الأب
                if account['parent_id']:
                    for i in range(self.parent_combo.count()):
                        if self.parent_combo.itemData(i) == account['parent_id']:
                            self.parent_combo.setCurrentIndex(i)
                            break
                
                self.level_spin.setValue(account['level'] or 1)
                self.active_check.setChecked(account['is_active'])
                self.notes_edit.setPlainText(account['notes'] or '')
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الحساب:\n{str(e)}")
    
    def accept_dialog(self):
        """قبول الحوار وحفظ البيانات"""
        # التحقق من صحة البيانات
        if not self.code_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الحساب")
            return
        
        if not self.name_ar_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الحساب بالعربية")
            return
        
        try:
            # جمع البيانات
            account_data = {
                'account_code': self.code_edit.text().strip(),
                'account_name_ar': self.name_ar_edit.text().strip(),
                'account_name_en': self.name_en_edit.text().strip(),
                'account_type': ['asset', 'liability', 'equity', 'revenue', 'expense'][self.type_combo.currentIndex()],
                'parent_id': self.parent_combo.currentData() or None,
                'level': self.level_spin.value(),
                'is_active': self.active_check.isChecked(),
                'notes': self.notes_edit.toPlainText().strip() or None
            }
            
            if self.is_edit_mode:
                # تحديث الحساب
                query = """
                    UPDATE chart_of_accounts
                    SET account_name_ar = ?, account_name_en = ?, account_type = ?,
                        parent_id = ?, level = ?, is_active = ?, notes = ?
                    WHERE account_code = ?
                """
                params = (
                    account_data['account_name_ar'], account_data['account_name_en'],
                    account_data['account_type'], account_data['parent_id'],
                    account_data['level'], account_data['is_active'],
                    account_data['notes'], account_data['account_code']
                )
            else:
                # إضافة حساب جديد
                # التحقق من عدم تكرار رقم الحساب
                check_query = "SELECT COUNT(*) as count FROM chart_of_accounts WHERE account_code = ?"
                result = self.db_manager.execute_query(check_query, (account_data['account_code'],))
                
                if result and result[0]['count'] > 0:
                    QMessageBox.warning(self, "تحذير", "رقم الحساب موجود مسبقاً")
                    return
                
                query = """
                    INSERT INTO chart_of_accounts
                    (account_code, account_name_ar, account_name_en, account_type,
                     parent_id, level, is_active, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (
                    account_data['account_code'], account_data['account_name_ar'],
                    account_data['account_name_en'], account_data['account_type'],
                    account_data['parent_id'], account_data['level'],
                    account_data['is_active'], account_data['notes']
                )
            
            # تنفيذ الاستعلام
            self.db_manager.execute_update(query, params)
            
            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} الحساب بنجاح")
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الحساب:\n{str(e)}")

    def open_receipt_voucher(self):
        """فتح سند قبض"""
        try:
            from .vouchers.receipt_voucher import ReceiptVoucherDialog
            dialog = ReceiptVoucherDialog(self.db_manager, self)
            dialog.exec_()
        except ImportError:
            # إنشاء سند قبض مؤقت
            self.create_voucher_dialog("سند قبض", "receipt")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند القبض:\n{str(e)}")

    def open_payment_voucher(self):
        """فتح سند صرف"""
        try:
            from .vouchers.payment_voucher import PaymentVoucherDialog
            dialog = PaymentVoucherDialog(self.db_manager, self)
            dialog.exec_()
        except ImportError:
            # إنشاء سند صرف مؤقت
            self.create_voucher_dialog("سند صرف", "payment")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند الصرف:\n{str(e)}")

    def create_voucher_dialog(self, title, voucher_type):
        """إنشاء حوار سند مؤقت"""
        QMessageBox.information(
            self,
            title,
            f"سيتم فتح {title} قريباً...\nالوحدة قيد التطوير."
        )