#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيم الوحدات الداخلية حسب البنية الجديدة
Internal Modules Organization According to New Structure
"""

# خريطة تنظيم الوحدات حسب البنية الجديدة
# مع الحفاظ على الواجهة الأصلية
MODULES_ORGANIZATION = {
    # 🔹 القسم: تهيئة النظام
    "system_setup": {
        "general_setup": {
            "المؤشرات العامة": "general_variables",
            "إعداد قواعد النظام": "system_rules_setup",
            "تهيئة العملات": "currencies_setup",
            "الأقاليم الدولية / الدول": "regions_countries",
            "بيانات المحافظات / المناطق / المدن": "provinces_regions_cities",
            "مجموعات المحافظات": "provinces_groups",
            "شجرة الفروع": "branches_tree",
            "تعريف الفرع": "branch_definition",
            "إعداد الدليل المحاسبي": "chart_of_accounts_setup",
            "ترميز الحسابات": "accounts_coding",
            "ربط الحسابات بإدخال المشاريع": "accounts_projects_link",
            "أنواع الهيكل الإداري": "organizational_structure_types",
            "أنواع السلامة المهنية": "safety_types",
            "مجموعة الموظفين": "employees_groups",
            "تعريف أصناف العملاء": "customer_types_definition"
        },
        "inputs": {
            "الدليل المحاسبي العام للوحدات": "general_chart_of_accounts",
            "الإدارة المحاسبية الافتراضية": "default_accounting_management",
            "الربط المحاسبي": "accounting_links",
            "ربط الحسابات بالحسابات العامة والتدفقات النقدية": "accounts_general_cashflow_link",
            "ربط الحسابات بالأنشطة أو المراكز": "accounts_activities_centers_link",
            "إعدادات ترحيل القيود الآلية": "automatic_entries_transfer_settings",
            "ربط الحسابات مع الموردين والعملاء والأصناف": "accounts_suppliers_customers_items_link"
        }
    },
    
    # 🔹 القسم: إدارة النظام
    "system_management": {
        "user_management": {
            "إدارة المستخدمين": "users_management",
            "صلاحيات المستخدمين": "users_permissions",
            "خصوصية المستخدم": "user_privacy"
        },
        "system_control": {
            "الإغلاقات والتوثيقات": "closures_documentation",
            "النسخ الاحتياطي": "backup_system",
            "إدارة قاعدة البيانات": "database_management",
            "التحكم بواجهات الدخول": "login_interfaces_control",
            "تتبع النشاطات (سجل الأحداث)": "activities_tracking",
            "الترقيم التلقائي للمستندات": "automatic_document_numbering",
            "تهيئة إعدادات النظام التقنية": "technical_system_settings",
            "مزامنة البيانات بين الفروع": "branches_data_sync"
        }
    },
    
    # 🔹 القسم: أنظمة الحسابات
    "accounting_systems": {
        "setup": {
            "متغيرات الحسابات": "accounting_variables",
            "الترميزات المحاسبية": "accounting_codes",
            "مجموعة الصناديق": "cash_boxes_group"
        },
        "inputs": {
            "بيانات الصناديق": "cash_boxes_data",
            "البنوك": "banks_data",
            "الأرصدة الافتتاحية": "opening_balances"
        },
        "operations": {
            "قيود اليومية العامة": "general_journal_entries",
            "قيود يومية آلية": "automatic_journal_entries",
            "دفتر الأستاذ العام": "general_ledger",
            "ميزان المراجعة": "trial_balance",
            "ميزان المراجعة التفصيلي": "detailed_trial_balance",
            "شجرة الحسابات (الدليل المحاسبي)": "chart_of_accounts_tree",
            "بطاقة حساب": "account_card",
            "المراكز المحاسبية / مراكز التكلفة": "accounting_cost_centers",
            "التحليل المالي": "financial_analysis",
            "ترحيل القيود": "entries_posting",
            "إقفال الحسابات": "accounts_closing",
            "تجهيز القوائم المالية": "financial_statements_preparation",
            "التسويات المحاسبية": "accounting_adjustments",
            "أوامر الدفع / القبض العامة": "general_payment_receipt_orders",
            "التحويلات البنكية": "bank_transfers",
            "ضبط إعدادات القيود": "entries_settings_adjustment"
        },
        "reports": {
            "تقارير كشف حساب": "account_statement_reports",
            "حركة الصناديق والبنوك": "cash_bank_movement_reports",
            "اليومية": "journal_reports",
            "ميزان المراجعة": "trial_balance_reports",
            "قائمة الدخل": "income_statement",
            "المركز المالي": "balance_sheet",
            "التدفقات النقدية": "cash_flows_statement",
            "الشيكات المستحقة": "due_checks_reports",
            "الأرصدة الافتتاحية": "opening_balances_reports"
        }
    },
    
    # 🔹 القسم: إدارة العملاء والمبيعات
    "customers_sales": {
        "setup": {
            "متغيرات النظام": "system_variables",
            "أنواع العملاء": "customer_types",
            "المسوقين": "marketers_types",
            "الفواتير": "invoice_types",
            "المردودات": "return_types"
        },
        "inputs": {
            "بيانات عملاء (تعريف عميل جديد)": "customers_data",
            "تصنيفات العملاء / أنواع العملاء": "customer_classifications",
            "مجموعات العملاء": "customer_groups",
            "ربط العملاء بالحسابات المحاسبية": "customers_accounts_link",
            "رصيد افتتاحي للعميل": "customer_opening_balance",
            "خطوط السير": "routes_data",
            "تسعيرة الأصناف": "items_pricing"
        },
        "operations": {
            "فواتير المبيعات": "sales_invoices",
            "فواتير المردود": "return_invoices",
            "عروض الأسعار": "quotations",
            "طلبات العملاء": "customer_orders",
            "إشعارات العملاء": "customer_notifications",
            "مبيعات الكوبونات": "coupon_sales",
            "متابعة العقود": "contracts_follow_up",
            "السندات": "vouchers"
        },
        "reports": {
            "حركة العملاء": "customers_movement",
            "كشف حساب عميل": "customer_account_statement",
            "تقرير أعمار الديون": "aging_report",
            "حدود الائتمان والمتابعة": "credit_limits_follow_up",
            "تقارير العملاء": "customers_reports",
            "ملاحظات العملاء / المرفقات": "customer_notes_attachments",
            "متابعة التحصيل": "collection_follow_up"
        }
    },
    
    # 🔹 القسم: إدارة الموردين والمشتريات
    "suppliers_purchases": {
        "setup": {
            "متغيرات النظام": "system_variables",
            "أنواع الموردين": "supplier_types",
            "أعباء المشتريات": "purchase_expenses",
            "المردودات": "return_types",
            "العقود": "contract_types"
        },
        "inputs": {
            "بطاقة مورد (تعريف مورد جديد)": "suppliers_data",
            "تصنيفات الموردين": "supplier_classifications",
            "مجموعات الموردين": "supplier_groups",
            "ربط الموردين بالحسابات المحاسبية": "suppliers_accounts_link",
            "رصيد افتتاحي للمورد": "supplier_opening_balance",
            "مندوبي المشتريات": "purchase_representatives"
        },
        "operations": {
            "فواتير المشتريات (محلية / خارجية)": "purchase_invoices",
            "أوامر وطلبات الشراء": "purchase_orders_requests",
            "سندات الصرف": "payment_vouchers",
            "بيانات الشحن": "shipping_data",
            "العقود": "contracts"
        },
        "reports": {
            "حركة الموردين": "suppliers_movement",
            "كشف حساب مورد": "supplier_account_statement",
            "تقرير أعمار الديون للموردين": "suppliers_aging_report",
            "حدود الائتمان والتعاقدات": "credit_limits_contracts",
            "متابعة المشتريات": "purchases_follow_up",
            "تقارير الموردين": "suppliers_reports",
            "ملاحظات الموردين / مرفقات": "supplier_notes_attachments",
            "إعدادات إشعارات المدفوعات": "payment_notifications_settings"
        }
    },
    
    # 🔹 القسم: إدارة المخازن
    "inventory_management": {
        "setup": {
            "أنواع الأصناف": "item_types",
            "المجموعات الصنفية": "item_groups",
            "الوحدات الصنفية": "item_units",
            "تعريف المخازن": "warehouses_definition",
            "إعدادات المخزون": "inventory_settings"
        },
        "inputs": {
            "بطاقة صنف": "item_card",
            "مستويات المخزون (الحد الأدنى/الأقصى)": "inventory_levels"
        },
        "operations": {
            "إدخال مخزني": "inventory_input",
            "إخراج مخزني": "inventory_output",
            "تحويل مخزني بين المخازن": "inventory_transfer",
            "تسويات مخزنية": "inventory_adjustments",
            "جرد المخزون": "inventory_count"
        },
        "reports": {
            "تقرير أرصدة الأصناف": "items_balances_report",
            "تقرير حركات الأصناف": "items_movement_report",
            "تقرير التالف والمرتجع": "damaged_returned_report",
            "التكلفة والربحية": "cost_profitability"
        }
    },
    
    # 🔹 القسم: نظام إدارة المعلومات
    "information_management": {
        "management_reports": {
            "التقارير المالية الشاملة": "comprehensive_financial_reports",
            "تقارير المبيعات والمشتريات": "sales_purchases_reports",
            "تقارير المخزون والحركة": "inventory_movement_reports",
            "تقارير العملاء والموردين": "customers_suppliers_reports",
            "الاستعلامات المتقدمة": "advanced_queries",
            "تحليل الأداء المالي": "financial_performance_analysis",
            "مؤشرات الأداء الرئيسية": "key_performance_indicators",
            "التقارير المخصصة": "custom_reports"
        }
    },
    
    # 🔹 القسم: نقاط البيع
    "pos_system": {
        "operations": {
            "شاشة نقاط البيع الرئيسية": "main_pos_screen",
            "إعدادات نقاط البيع": "pos_settings",
            "إدارة الكاشيرات": "cashiers_management",
            "تقارير المبيعات اليومية": "daily_sales_reports",
            "إدارة الخصومات والعروض": "discounts_offers_management",
            "ربط أجهزة الباركود": "barcode_devices_link",
            "إعدادات الطابعات": "printers_settings",
            "إدارة الورديات": "shifts_management"
        }
    },
    
    # 🔹 القسم: الأنظمة المساعدة
    "helper_systems": {
        "utilities": {
            "الآلة الحاسبة المتقدمة": "advanced_calculator",
            "التقويم والمواعيد": "calendar_appointments",
            "محرر النصوص": "text_editor",
            "البحث المتقدم": "advanced_search",
            "مولد التقارير": "reports_generator",
            "أدوات النظام": "system_tools",
            "محول العملات": "currency_converter",
            "مراقب الأداء": "performance_monitor",
            "مدير الملفات": "file_manager",
            "مولد كلمات المرور": "password_generator",
            "مدير الحافظة": "clipboard_manager",
            "متصفح الويب": "web_browser"
        }
    }
}

def get_module_by_name(module_name):
    """الحصول على معرف الوحدة من اسمها"""
    for department, sections in MODULES_ORGANIZATION.items():
        for section, modules in sections.items():
            if module_name in modules:
                return {
                    'department': department,
                    'section': section,
                    'module_id': modules[module_name],
                    'module_name': module_name
                }
    return None

def get_modules_by_department(department):
    """الحصول على جميع وحدات قسم معين"""
    if department in MODULES_ORGANIZATION:
        return MODULES_ORGANIZATION[department]
    return {}

def get_all_modules():
    """الحصول على جميع الوحدات"""
    all_modules = {}
    for department, sections in MODULES_ORGANIZATION.items():
        all_modules[department] = {}
        for section, modules in sections.items():
            all_modules[department][section] = modules
    return all_modules
