#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المحاسبي المحدث
Restructured Accounting System Test
"""

import sys
import os
from datetime import datetime

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_restructured_system():
    """اختبار النظام المحدث"""
    print("🏗️ اختبار النظام المحاسبي المحدث...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # إنشاء التطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system_restructured import MainSystemWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainSystemWindow()
        print("   ✅ تم إنشاء النافذة الرئيسية المحدثة بنجاح")
        
        # اختبار وجود الشجرة
        if hasattr(main_window, 'tree_widget'):
            print("   ✅ شجرة النظام موجودة")
            
            # فحص عدد العناصر الرئيسية
            root_count = main_window.tree_widget.topLevelItemCount()
            print(f"   📊 عدد الأقسام الرئيسية: {root_count}")
            
            # فحص الأقسام الرئيسية
            expected_sections = [
                "📦 إدارة المخازن",
                "🧾 إدارة العملاء", 
                "💼 إدارة الموردين",
                "🛒 إدارة المشتريات",
                "🛍️ إدارة المبيعات",
                "📊 المحاسبة العامة",
                "👥 إدارة المستخدمين والصلاحيات",
                "⚙️ إعدادات النظام"
            ]
            
            found_sections = []
            for i in range(root_count):
                item = main_window.tree_widget.topLevelItem(i)
                section_name = item.text(0)
                found_sections.append(section_name)
                print(f"      • {section_name}")
            
            # التحقق من وجود جميع الأقسام المطلوبة
            missing_sections = []
            for expected in expected_sections:
                if not any(expected in found for found in found_sections):
                    missing_sections.append(expected)
            
            if missing_sections:
                print(f"   ⚠️ أقسام مفقودة: {missing_sections}")
            else:
                print("   ✅ جميع الأقسام الرئيسية موجودة")
        
        # اختبار وجود المنطقة المركزية
        if hasattr(main_window, 'stacked_widget'):
            print("   ✅ المنطقة المركزية موجودة")
        
        # اختبار الوظائف
        functions_to_test = [
            'open_customers_data',
            'open_suppliers_data', 
            'open_products_data',
            'open_warehouses_data',
            'open_sales_invoices',
            'open_purchase_invoices',
            'open_journal_entries',
            'open_chart_of_accounts',
            'open_auto_numbering',
            'open_user_management'
        ]
        
        for func_name in functions_to_test:
            if hasattr(main_window, func_name):
                print(f"   ✅ دالة {func_name} موجودة")
            else:
                print(f"   ❌ دالة {func_name} غير موجودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار النظام المحدث: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tree_structure():
    """اختبار هيكل الشجرة"""
    print("\n🌳 اختبار هيكل الشجرة...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system_restructured import MainSystemWindow
        
        main_window = MainSystemWindow()
        tree_data = main_window.get_restructured_tree_data()
        
        print(f"   📊 عدد الأقسام الرئيسية: {len(tree_data)}")
        
        for section in tree_data:
            section_title = section['title']
            print(f"\n   📁 {section_title}")
            
            if isinstance(section['children'], list):
                for child in section['children']:
                    if isinstance(child, dict):
                        # قسم فرعي
                        print(f"      📂 {child['title']}")
                        for subitem in child['children']:
                            print(f"         📄 {subitem}")
                    else:
                        # عنصر مباشر
                        print(f"      📄 {child}")
        
        # التحقق من التقسيم الثلاثي (مدخلات، عمليات، تقارير)
        sections_with_three_parts = 0
        for section in tree_data:
            if isinstance(section['children'], list) and len(section['children']) >= 3:
                has_inputs = any('المدخلات' in str(child) for child in section['children'] if isinstance(child, dict))
                has_processes = any('العمليات' in str(child) for child in section['children'] if isinstance(child, dict))
                has_reports = any('التقارير' in str(child) for child in section['children'] if isinstance(child, dict))
                
                if has_inputs and has_processes and has_reports:
                    sections_with_three_parts += 1
                    print(f"   ✅ {section['title']} - يحتوي على التقسيم الثلاثي")
        
        print(f"\n   📊 الأقسام التي تحتوي على التقسيم الثلاثي: {sections_with_three_parts}")
        
        return sections_with_three_parts >= 6  # معظم الأقسام يجب أن تحتوي على التقسيم الثلاثي
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار هيكل الشجرة: {e}")
        return False

def test_navigation():
    """اختبار التنقل"""
    print("\n🧭 اختبار التنقل...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system_restructured import MainSystemWindow
        
        main_window = MainSystemWindow()
        
        # اختبار دالة معالجة النقر
        if hasattr(main_window, 'handle_tree_click'):
            print("   ✅ دالة معالجة النقر موجودة")
        
        # اختبار دالة عرض قريباً
        if hasattr(main_window, 'show_coming_soon'):
            print("   ✅ دالة عرض 'قريباً' موجودة")
        
        # اختبار الاتصالات
        if hasattr(main_window, 'setup_connections'):
            print("   ✅ دالة إعداد الاتصالات موجودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التنقل: {e}")
        return False

def test_ui_components():
    """اختبار مكونات الواجهة"""
    print("\n🎨 اختبار مكونات الواجهة...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system_restructured import MainSystemWindow
        
        main_window = MainSystemWindow()
        
        # اختبار الصفحة الرئيسية
        if hasattr(main_window, 'create_home_widget'):
            print("   ✅ دالة إنشاء الصفحة الرئيسية موجودة")
        
        # اختبار ويدجت الإحصائيات
        if hasattr(main_window, 'create_stat_widget'):
            print("   ✅ دالة إنشاء ويدجت الإحصائيات موجودة")
        
        # اختبار الأنماط
        if hasattr(main_window, 'apply_styles'):
            print("   ✅ دالة تطبيق الأنماط موجودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار مكونات الواجهة: {e}")
        return False

def compare_with_old_system():
    """مقارنة مع النظام القديم"""
    print("\n🔄 مقارنة مع النظام القديم...")
    
    try:
        # اختبار النظام القديم
        old_system_works = False
        try:
            from main_system import MainSystemWindow as OldMainSystemWindow
            old_system_works = True
            print("   ✅ النظام القديم متاح للمقارنة")
        except:
            print("   ⚠️ النظام القديم غير متاح للمقارنة")
        
        # اختبار النظام الجديد
        new_system_works = False
        try:
            from main_system_restructured import MainSystemWindow as NewMainSystemWindow
            new_system_works = True
            print("   ✅ النظام الجديد يعمل بنجاح")
        except:
            print("   ❌ النظام الجديد لا يعمل")
        
        if old_system_works and new_system_works:
            print("   🎯 كلا النظامين متاحان - يمكن الاختيار بينهما")
        elif new_system_works:
            print("   🆕 النظام الجديد جاهز للاستخدام")
        
        return new_system_works
        
    except Exception as e:
        print(f"   ❌ خطأ في المقارنة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🏗️ اختبار النظام المحاسبي المحدث")
    print("=" * 70)
    
    tests = [
        ("النظام المحدث", test_restructured_system),
        ("هيكل الشجرة", test_tree_structure),
        ("التنقل", test_navigation),
        ("مكونات الواجهة", test_ui_components),
        ("المقارنة مع النظام القديم", compare_with_old_system),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم إعادة هيكلة النظام بنجاح!")
        print("✅ جميع الوظائف تعمل بدون أخطاء")
        
        print("\n🏗️ الهيكل الجديد:")
        print("   📦 إدارة المخازن - مقسمة إلى (مدخلات، عمليات، تقارير)")
        print("   🧾 إدارة العملاء - مقسمة إلى (مدخلات، عمليات، تقارير)")
        print("   💼 إدارة الموردين - مقسمة إلى (مدخلات، عمليات، تقارير)")
        print("   🛒 إدارة المشتريات - مقسمة إلى (مدخلات، عمليات، تقارير)")
        print("   🛍️ إدارة المبيعات - مقسمة إلى (مدخلات، عمليات، تقارير)")
        print("   📊 المحاسبة العامة - مقسمة إلى (مدخلات، عمليات، تقارير)")
        print("   👥 إدارة المستخدمين - مقسمة إلى (مدخلات، عمليات، تقارير)")
        print("   ⚙️ إعدادات النظام - الوظائف التقنية")
        
        print("\n🚀 كيفية الاستخدام:")
        print("   1. شغل النظام الجديد: python src/main_system_restructured.py")
        print("   2. أو شغل النظام القديم: python src/main_system.py")
        print("   3. استكشف الهيكل الجديد المنظم")
        
    else:
        print("\n⚠️ بعض وظائف النظام المحدث تحتاج إلى إصلاح")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
