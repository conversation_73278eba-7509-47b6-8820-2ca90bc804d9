#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الواجهة الرئيسية لنظام أونكس ERP
Onyx ERP Main Window
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QPushButton, QFrame, QScrollArea, QTreeWidget,
                             QTreeWidgetItem, QToolBar, QAction, QMenuBar, QMenu,
                             QSplitter, QStackedWidget, QMessageBox, QStatusBar,
                             QSpacerItem, QSizePolicy, QGroupBox, QGridLayout, QDialog,
                             QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTimer, QSize, QDate
from PyQt5.QtGui import QFont, QIcon, QPalette, QPixmap, QPainter, QColor, Q<PERSON>rush, QLinearGradient

from ..database.sqlite_manager import SQLiteManager
from ..modules.accounts_module import AccountsManagementWidget
from ..modules.journal_entries_module import JournalEntriesWidget
from ..modules.reports_module import ReportsWidget

class OnyxMainWindow(QMainWindow):
    """الواجهة الرئيسية لنظام أونكس ERP"""
    
    logout_requested = pyqtSignal()  # إشارة طلب تسجيل الخروج
    
    def __init__(self, session_data: dict, db_manager: SQLiteManager):
        super().__init__()
        
        try:
            self.session_data = session_data
            self.db_manager = db_manager
            self.current_widget = None
            self.user_logged_out = False  # علامة لتتبع تسجيل الخروج
            
            self.setup_ui()
            self.setup_connections()
            self.load_user_preferences()
            
        except Exception as e:
            print(f"خطأ في إنشاء النافذة الرئيسية: {e}")
            import traceback
            traceback.print_exc()
            raise e
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(f"Onyx ERP - {self.session_data.get('company', 'نظام تخطيط موارد المؤسسات')}")
        self.setMinimumSize(1200, 800)
        self.showMaximized()
        
        # تطبيق التنسيق العام
        self.setStyleSheet(self.get_main_stylesheet())
        
        # إنشاء المكونات الرئيسية
        self.create_menu_bar()
        self.create_toolbar()
        self.create_central_widget()
        self.create_status_bar()
        
        # تعيين الأيقونة
        self.setWindowIcon(self.create_app_icon())
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: #2c3e50;
                color: white;
                font-size: 12px;
                padding: 5px;
            }
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                margin: 2px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #34495e;
            }
            QMenu {
                background-color: white;
                color: #2c3e50;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
            }
            QMenu::item {
                padding: 8px 20px;
            }
            QMenu::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        # قائمة المحاسبة
        accounting_menu = menubar.addMenu("المحاسبة")
        accounting_menu.addAction("دليل الحسابات", self.show_accounts_module)
        accounting_menu.addAction("القيود اليومية", self.show_journal_entries)
        accounting_menu.addSeparator()
        accounting_menu.addAction("ميزان المراجعة", lambda: self.show_reports_module("trial_balance"))
        
        # قائمة العملاء والموردين
        customers_suppliers_menu = menubar.addMenu("العملاء والموردين")
        customers_suppliers_menu.addAction("إدارة العملاء والموردين", self.show_customers_suppliers_module)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu("التقارير")
        reports_menu.addAction("التقارير المالية", self.show_reports_module)
        reports_menu.addAction("التقارير المتقدمة", self.show_advanced_reports_module)
        
        # قائمة النظام
        system_menu = menubar.addMenu("النظام")
        system_menu.addAction("إعدادات النظام", self.show_system_settings)
        system_menu.addAction("إدارة المستخدمين", self.show_user_management)
        system_menu.addSeparator()
        system_menu.addAction("تسجيل الخروج", self.logout)
        system_menu.addAction("خروج", self.close)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("المساعدة")
        help_menu.addAction("دليل المستخدم", self.show_help)
        help_menu.addAction("حول النظام", self.show_about)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QToolBar()
        toolbar.setMovable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        toolbar.setIconSize(QSize(32, 32))
        toolbar.setStyleSheet("""
            QToolBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ecf0f1, stop:1 #bdc3c7);
                border-bottom: 2px solid #95a5a6;
                spacing: 10px;
                padding: 5px;
            }
            QToolButton {
                background-color: transparent;
                border: 2px solid transparent;
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
                color: #2c3e50;
                font-size: 11px;
                font-weight: bold;
            }
            QToolButton:hover {
                background-color: #d5dbdb;
                border-color: #95a5a6;
            }
            QToolButton:pressed {
                background-color: #bdc3c7;
            }
        """)
        
        # إضافة الأدوات
        toolbar.addAction(self.create_icon("🏠"), "الرئيسية", self.show_dashboard)
        toolbar.addSeparator()
        toolbar.addAction(self.create_icon("🏦"), "دليل الحسابات", self.show_accounts_module)
        toolbar.addAction(self.create_icon("📝"), "القيود اليومية", self.show_journal_entries)
        toolbar.addAction(self.create_icon("👥"), "العملاء والموردين", self.show_customers_suppliers_module)
        toolbar.addAction(self.create_icon("📊"), "التقارير", self.show_reports_module)
        toolbar.addSeparator()
        toolbar.addAction(self.create_icon("🔔"), "التنبيهات", self.show_notifications)
        toolbar.addAction(self.create_icon("❓"), "المساعدة", self.show_help)
        toolbar.addAction(self.create_icon("⚙️"), "الإعدادات", self.show_settings)
        
        # إضافة معلومات المستخدم في نهاية الشريط
        spacer = QWidget()
        spacer.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        toolbar.addWidget(spacer)
        
        # معلومات المستخدم والتاريخ
        user_info = QLabel(f"المستخدم: {self.session_data.get('full_name', 'غير محدد')} | التاريخ: {QDate.currentDate().toString('yyyy-MM-dd')} | المركز الرئيسي: {self.session_data.get('branch', '1')}")
        user_info.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                padding: 5px 10px;
                background-color: rgba(255, 255, 255, 0.8);
                border-radius: 4px;
                margin: 5px;
            }
        """)
        toolbar.addWidget(user_info)
        
        self.addToolBar(toolbar)
    
    def create_central_widget(self):
        """إنشاء الويدجت المركزي"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إنشاء المقسم الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        
        # الشريط الجانبي
        self.create_sidebar()
        splitter.addWidget(self.sidebar_frame)
        
        # المنطقة الرئيسية
        self.create_main_area()
        splitter.addWidget(self.main_area)
        
        # تعيين النسب
        splitter.setSizes([250, 950])
        splitter.setCollapsible(0, False)
        
        main_layout.addWidget(splitter)
        central_widget.setLayout(main_layout)
    
    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        self.sidebar_frame = QFrame()
        self.sidebar_frame.setFixedWidth(250)
        self.sidebar_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-right: 2px solid #2c3e50;
            }
        """)
        
        sidebar_layout = QVBoxLayout()
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)
        
        # شعار الشركة
        self.create_company_logo()
        sidebar_layout.addWidget(self.company_logo_frame)
        
        # قائمة الوظائف
        self.create_function_tree()
        sidebar_layout.addWidget(self.function_tree)
        
        self.sidebar_frame.setLayout(sidebar_layout)
    
    def create_company_logo(self):
        """إنشاء منطقة شعار الشركة"""
        self.company_logo_frame = QFrame()
        self.company_logo_frame.setFixedHeight(120)
        self.company_logo_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                border-bottom: 2px solid #1a252f;
            }
        """)
        
        logo_layout = QVBoxLayout()
        logo_layout.setContentsMargins(15, 15, 15, 15)
        
        # اسم الشركة
        company_name = QLabel(self.session_data.get('company', 'صقالات أبو سيف للنقل'))
        company_name.setAlignment(Qt.AlignCenter)
        company_name.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        company_name.setWordWrap(True)
        
        # شعار النظام
        system_logo = QLabel("Onyx ERP")
        system_logo.setAlignment(Qt.AlignCenter)
        system_logo.setStyleSheet("""
            QLabel {
                color: #3498db;
                font-size: 18px;
                font-weight: bold;
                background-color: transparent;
                margin-top: 10px;
            }
        """)
        
        logo_layout.addWidget(company_name)
        logo_layout.addWidget(system_logo)
        
        self.company_logo_frame.setLayout(logo_layout)
    
    def create_function_tree(self):
        """إنشاء شجرة الوظائف"""
        self.function_tree = QTreeWidget()
        self.function_tree.setHeaderLabel("📋 قائمة الوظائف")
        self.function_tree.setMaximumWidth(350)
        self.function_tree.setMinimumWidth(300)

        # إعداد الخط
        font = QFont("Arial", 10)
        self.function_tree.setFont(font)

        # تعيين الاتجاه من اليمين إلى الشمال
        self.function_tree.setLayoutDirection(Qt.RightToLeft)

        self.function_tree.setStyleSheet("""
            QTreeWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 11px;
                color: #495057;
                selection-background-color: #007bff;
                selection-color: white;
                outline: none;
            }
            QTreeWidget::item {
                padding: 6px 8px;
                border-bottom: 1px solid #e9ecef;
                min-height: 22px;
            }
            QTreeWidget::item:hover {
                background-color: #e3f2fd;
                color: #1976d2;
                border-radius: 3px;
            }
            QTreeWidget::item:selected {
                background-color: #2196f3;
                color: white;
                font-weight: bold;
                border-radius: 3px;
            }
            QTreeWidget::item:selected:hover {
                background-color: #1976d2;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                border-image: none;
                image: url(none);
                background: transparent;
            }
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                border-image: none;
                image: url(none);
                background: transparent;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed:hover,
            QTreeWidget::branch:closed:has-children:has-siblings:hover {
                background-color: #e3f2fd;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4a90e2, stop:1 #357abd);
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: bold;
                font-size: 12px;
                text-align: center;
            }
        """)
        
        # إضافة العناصر الرئيسية
        self.add_tree_items()
        
        # ربط الإشارات
        self.function_tree.itemClicked.connect(self.on_tree_item_clicked)
        self.function_tree.itemExpanded.connect(self.on_tree_item_expanded)
        self.function_tree.itemCollapsed.connect(self.on_tree_item_collapsed)
    
    def add_tree_items(self):
        """إضافة عناصر الشجرة"""

        # 1. تهيئة النظام
        system_setup = QTreeWidgetItem(["+ 🏢 تهيئة النظام"])

        # التهيئة العامة
        general_setup = QTreeWidgetItem(["+ 📑 التهيئة العامة"])
        general_setup.addChild(QTreeWidgetItem(["🔧 المتغيرات العامة"]))
        general_setup.addChild(QTreeWidgetItem(["📅 إعداد فترات النظام"]))
        general_setup.addChild(QTreeWidgetItem(["💱 تهيئة العملات"]))
        general_setup.addChild(QTreeWidgetItem(["🌍 بيانات المناطق والدول"]))
        general_setup.addChild(QTreeWidgetItem(["🏪 مجموعة الفروع"]))
        general_setup.addChild(QTreeWidgetItem(["🏢 بيانات الشركات"]))
        general_setup.addChild(QTreeWidgetItem(["📊 تهيئة الدليل المحاسبي"]))
        general_setup.addChild(QTreeWidgetItem(["🔢 الترميزات العامة"]))
        general_setup.addChild(QTreeWidgetItem(["👥 أنواع الهيكل الإداري"]))
        general_setup.addChild(QTreeWidgetItem(["📝 تهيئة الحقول الإضافية"]))
        system_setup.addChild(general_setup)

        # المدخلات
        system_inputs = QTreeWidgetItem(["+ 📥 المدخلات"])
        system_inputs.addChild(QTreeWidgetItem(["📋 الدليل المحاسبي"]))
        system_inputs.addChild(QTreeWidgetItem(["🎯 مراكز التكلفة"]))
        system_inputs.addChild(QTreeWidgetItem(["🏗️ الهيكل الإداري"]))
        system_inputs.addChild(QTreeWidgetItem(["🔄 الحسابات الوسيطة"]))
        system_inputs.addChild(QTreeWidgetItem(["👨‍💼 بيانات الموظفين"]))
        system_inputs.addChild(QTreeWidgetItem(["🔗 ربط الموظفين بالمهن"]))
        system_inputs.addChild(QTreeWidgetItem(["💰 الحسابات المدينة والدائنة"]))
        system_inputs.addChild(QTreeWidgetItem(["🔗 الربط بالحسابات العامة والتدفقات"]))
        system_setup.addChild(system_inputs)

        self.function_tree.addTopLevelItem(system_setup)

        # 2. إدارة النظام
        system_management = QTreeWidgetItem(["+ 🔧 إدارة النظام"])

        # إدارة المستخدمين
        user_management = QTreeWidgetItem(["+ 📥 إدارة المستخدمين"])
        user_management.addChild(QTreeWidgetItem(["👥 مجموعة المستخدمين"]))
        user_management.addChild(QTreeWidgetItem(["👤 بيانات المستخدمين"]))
        user_management.addChild(QTreeWidgetItem(["👁️ عرض المستخدمين"]))
        user_management.addChild(QTreeWidgetItem(["🔍 الرقابة"]))
        system_management.addChild(user_management)

        # صلاحيات المستخدمين
        user_permissions = QTreeWidgetItem(["+ ⚙️ صلاحيات المستخدمين"])
        user_permissions.addChild(QTreeWidgetItem(["🖥️ صلاحيات الشاشات"]))
        user_permissions.addChild(QTreeWidgetItem(["📑 صلاحيات التبويبات"]))
        user_permissions.addChild(QTreeWidgetItem(["📝 صلاحيات المدخلات"]))
        user_permissions.addChild(QTreeWidgetItem(["⚡ صلاحيات العمليات"]))
        user_permissions.addChild(QTreeWidgetItem(["🔔 تنبيهات النظام"]))
        user_permissions.addChild(QTreeWidgetItem(["📱 اعتماد أجهزة الموبايل"]))
        user_permissions.addChild(QTreeWidgetItem(["📊 تقارير الصلاحيات"]))
        system_management.addChild(user_permissions)

        # خصوصية المستخدمين
        user_privacy = QTreeWidgetItem(["+ 🔐 خصوصية المستخدمين"])
        user_privacy.addChild(QTreeWidgetItem(["⭐ الشاشات المفضلة"]))
        user_privacy.addChild(QTreeWidgetItem(["🔑 تغيير كلمة المرور"]))
        user_privacy.addChild(QTreeWidgetItem(["📄 الترويسة حسب المستخدم"]))
        system_management.addChild(user_privacy)

        # الإقفالات
        closures = QTreeWidgetItem(["+ 📛 الإقفالات"])
        closures.addChild(QTreeWidgetItem(["⏸️ التوقيف الشهري / السنوي"]))
        closures.addChild(QTreeWidgetItem(["🔐 الإقفال"]))
        closures.addChild(QTreeWidgetItem(["🔓 إلغاء الإقفالات"]))
        system_management.addChild(closures)

        # النسخ الاحتياطية
        backups = QTreeWidgetItem(["+ 💾 النسخ الاحتياطية"])
        backups.addChild(QTreeWidgetItem(["⚙️ إعدادات"]))
        backups.addChild(QTreeWidgetItem(["💾 نسخ / استرجاع"]))
        system_management.addChild(backups)

        # إدارة قاعدة البيانات
        db_management = QTreeWidgetItem(["+ 🗃️ إدارة قاعدة البيانات"])
        db_management.addChild(QTreeWidgetItem(["🌐 ترميزات"]))
        db_management.addChild(QTreeWidgetItem(["📁 أرشفة"]))
        db_management.addChild(QTreeWidgetItem(["🏢 الوحدات المحاسبية"]))
        db_management.addChild(QTreeWidgetItem(["🔄 تحديث قاعدة البيانات"]))
        db_management.addChild(QTreeWidgetItem(["📊 تقييم المخزون"]))
        system_management.addChild(db_management)

        self.function_tree.addTopLevelItem(system_management)

        # 3. أنظمة الحسابات
        accounting_systems = QTreeWidgetItem(["+ 💰 أنظمة الحسابات"])

        # التهيئة
        acc_setup = QTreeWidgetItem(["+ 🛠 التهيئة"])
        acc_setup.addChild(QTreeWidgetItem(["🔧 متغيرات"]))
        acc_setup.addChild(QTreeWidgetItem(["🔢 الترميزات"]))
        acc_setup.addChild(QTreeWidgetItem(["💰 مجموعة الصناديق"]))
        accounting_systems.addChild(acc_setup)

        # المدخلات
        acc_inputs = QTreeWidgetItem(["+ 📥 المدخلات"])
        acc_inputs.addChild(QTreeWidgetItem(["💰 بيانات الصناديق"]))
        acc_inputs.addChild(QTreeWidgetItem(["🏦 البنوك"]))
        acc_inputs.addChild(QTreeWidgetItem(["📊 الأرصدة الافتتاحية"]))
        accounting_systems.addChild(acc_inputs)

        # العمليات
        acc_operations = QTreeWidgetItem(["+ ⚙️ العمليات"])
        acc_operations.addChild(QTreeWidgetItem(["💳 إشعارات دائن / مدين"]))
        acc_operations.addChild(QTreeWidgetItem(["📥 سند قبض / صرف"]))
        acc_operations.addChild(QTreeWidgetItem(["📝 قيود يومية"]))
        acc_operations.addChild(QTreeWidgetItem(["💱 صرف عملة"]))
        acc_operations.addChild(QTreeWidgetItem(["🏦 تسوية بنوك"]))
        acc_operations.addChild(QTreeWidgetItem(["📋 طلبات السندات"]))
        acc_operations.addChild(QTreeWidgetItem(["💰 جرد النقدية"]))
        accounting_systems.addChild(acc_operations)

        # التقارير
        acc_reports = QTreeWidgetItem(["+ 📊 التقارير"])
        acc_reports.addChild(QTreeWidgetItem(["📋 تقارير كشف حساب"]))
        acc_reports.addChild(QTreeWidgetItem(["💰 حركة الصناديق والبنوك"]))
        acc_reports.addChild(QTreeWidgetItem(["📝 اليومية"]))
        acc_reports.addChild(QTreeWidgetItem(["⚖️ ميزان المراجعة"]))
        acc_reports.addChild(QTreeWidgetItem(["📈 قائمة الدخل"]))
        acc_reports.addChild(QTreeWidgetItem(["📊 المركز المالي"]))
        acc_reports.addChild(QTreeWidgetItem(["💰 التدفقات النقدية"]))
        acc_reports.addChild(QTreeWidgetItem(["💳 الشيكات المستحقة"]))
        acc_reports.addChild(QTreeWidgetItem(["📊 الأرصدة الافتتاحية"]))
        accounting_systems.addChild(acc_reports)

        self.function_tree.addTopLevelItem(accounting_systems)

        # 4. إدارة العملاء والمبيعات
        customers_sales = QTreeWidgetItem(["+ 👥 إدارة العملاء والمبيعات"])

        # التهيئة
        cust_setup = QTreeWidgetItem(["+ 🛠 التهيئة"])
        cust_setup.addChild(QTreeWidgetItem(["🔧 متغيرات النظام"]))
        cust_setup.addChild(QTreeWidgetItem(["👥 أنواع العملاء"]))
        cust_setup.addChild(QTreeWidgetItem(["👨‍💼 أنواع المسوقين"]))
        cust_setup.addChild(QTreeWidgetItem(["🧾 أنواع الفواتير"]))
        cust_setup.addChild(QTreeWidgetItem(["↩️ أنواع المردودات"]))
        customers_sales.addChild(cust_setup)

        # المدخلات
        cust_inputs = QTreeWidgetItem(["+ 📥 المدخلات"])
        cust_inputs.addChild(QTreeWidgetItem(["👥 بيانات العملاء"]))
        cust_inputs.addChild(QTreeWidgetItem(["👨‍💼 بيانات المسوقين"]))
        cust_inputs.addChild(QTreeWidgetItem(["🚛 بيانات السائقين"]))
        cust_inputs.addChild(QTreeWidgetItem(["📊 الأرصدة الافتتاحية"]))
        cust_inputs.addChild(QTreeWidgetItem(["🛣️ خطوط السير"]))
        cust_inputs.addChild(QTreeWidgetItem(["💰 تسعيرة الأصناف"]))
        customers_sales.addChild(cust_inputs)

        # العمليات
        cust_operations = QTreeWidgetItem(["+ ⚙️ العمليات"])
        cust_operations.addChild(QTreeWidgetItem(["🧾 فاتورة مبيعات"]))
        cust_operations.addChild(QTreeWidgetItem(["↩️ فاتورة مردود مبيعات"]))
        cust_operations.addChild(QTreeWidgetItem(["💰 عروض الأسعار"]))
        cust_operations.addChild(QTreeWidgetItem(["📋 طلبات العملاء"]))
        cust_operations.addChild(QTreeWidgetItem(["📢 إشعارات العملاء"]))
        cust_operations.addChild(QTreeWidgetItem(["🎫 مبيعات الكوبونات"]))
        cust_operations.addChild(QTreeWidgetItem(["📊 متابعة العقود"]))
        cust_operations.addChild(QTreeWidgetItem(["📥 سند قبض"]))
        customers_sales.addChild(cust_operations)

        # التقارير
        cust_reports = QTreeWidgetItem(["+ 📊 التقارير"])
        cust_reports.addChild(QTreeWidgetItem(["👥 تقارير العملاء"]))
        cust_reports.addChild(QTreeWidgetItem(["🧾 تقارير الفواتير"]))
        cust_reports.addChild(QTreeWidgetItem(["💰 تقارير العروض"]))
        cust_reports.addChild(QTreeWidgetItem(["💰 تقارير التحصيل"]))
        cust_reports.addChild(QTreeWidgetItem(["⏰ تقارير الأعمار"]))
        cust_reports.addChild(QTreeWidgetItem(["🗺️ تقارير المناطق"]))
        cust_reports.addChild(QTreeWidgetItem(["👨‍💼 مندوبي المبيعات"]))
        cust_reports.addChild(QTreeWidgetItem(["📊 رسوم بيانية"]))
        customers_sales.addChild(cust_reports)

        self.function_tree.addTopLevelItem(customers_sales)

        # 5. إدارة الموردين والمشتريات
        suppliers_purchases = QTreeWidgetItem(["+ 🏢 إدارة الموردين والمشتريات"])

        # التهيئة
        supp_setup = QTreeWidgetItem(["+ 🛠 التهيئة"])
        supp_setup.addChild(QTreeWidgetItem(["🔧 متغيرات النظام"]))
        supp_setup.addChild(QTreeWidgetItem(["🏢 أنواع الموردين"]))
        supp_setup.addChild(QTreeWidgetItem(["💰 أعباء المشتريات"]))
        supp_setup.addChild(QTreeWidgetItem(["↩️ المردودات"]))
        supp_setup.addChild(QTreeWidgetItem(["📄 العقود"]))
        suppliers_purchases.addChild(supp_setup)

        # المدخلات
        supp_inputs = QTreeWidgetItem(["+ 📥 المدخلات"])
        supp_inputs.addChild(QTreeWidgetItem(["🏢 بيانات الموردين"]))
        supp_inputs.addChild(QTreeWidgetItem(["👨‍💼 مندوبي المشتريات"]))
        suppliers_purchases.addChild(supp_inputs)

        # العمليات
        supp_operations = QTreeWidgetItem(["+ ⚙️ العمليات"])
        supp_operations.addChild(QTreeWidgetItem(["🧾 فواتير المشتريات (محلية / خارجية)"]))
        supp_operations.addChild(QTreeWidgetItem(["↩️ فاتورة مردود مشتريات"]))
        supp_operations.addChild(QTreeWidgetItem(["📋 أوامر وطلبات الشراء"]))
        supp_operations.addChild(QTreeWidgetItem(["📤 سندات الصرف"]))
        supp_operations.addChild(QTreeWidgetItem(["🚛 بيانات الشحن"]))
        supp_operations.addChild(QTreeWidgetItem(["📄 العقود"]))
        suppliers_purchases.addChild(supp_operations)

        # التقارير
        supp_reports = QTreeWidgetItem(["+ 📊 التقارير"])
        supp_reports.addChild(QTreeWidgetItem(["🏢 تقارير الموردين"]))
        supp_reports.addChild(QTreeWidgetItem(["🧾 تقارير الفواتير"]))
        supp_reports.addChild(QTreeWidgetItem(["💰 تقارير الأعباء"]))
        supp_reports.addChild(QTreeWidgetItem(["💰 تقارير العروض"]))
        supp_reports.addChild(QTreeWidgetItem(["📄 تقارير العقود"]))
        supp_reports.addChild(QTreeWidgetItem(["⏰ تقارير الأعمار"]))
        supp_reports.addChild(QTreeWidgetItem(["📋 كشف الحساب"]))
        suppliers_purchases.addChild(supp_reports)

        self.function_tree.addTopLevelItem(suppliers_purchases)

        # 6. إدارة المخازن
        warehouse_management = QTreeWidgetItem(["+ 📦 إدارة المخازن"])

        # التهيئة
        warehouse_setup = QTreeWidgetItem(["+ 🛠 التهيئة"])
        warehouse_setup.addChild(QTreeWidgetItem(["🔧 متغيرات المخزون"]))
        warehouse_setup.addChild(QTreeWidgetItem(["📏 وحدات القياس"]))
        warehouse_setup.addChild(QTreeWidgetItem(["📦 أنواع الأصناف"]))
        warehouse_setup.addChild(QTreeWidgetItem(["💰 مستويات التسعيرة"]))
        warehouse_setup.addChild(QTreeWidgetItem(["📥 أنواع التوريد"]))
        warehouse_setup.addChild(QTreeWidgetItem(["📤 أنواع الصرف"]))
        warehouse_setup.addChild(QTreeWidgetItem(["🔄 أنواع التحويل"]))
        warehouse_setup.addChild(QTreeWidgetItem(["🔢 ترميز بيانات الأصناف"]))
        warehouse_setup.addChild(QTreeWidgetItem(["📊 معايير التقييم"]))
        warehouse_management.addChild(warehouse_setup)

        # المدخلات
        warehouse_inputs = QTreeWidgetItem(["+ 📥 المدخلات"])
        warehouse_inputs.addChild(QTreeWidgetItem(["📂 بيانات المجموعات الرئيسية"]))
        warehouse_inputs.addChild(QTreeWidgetItem(["📁 المجموعات الفرعية"]))
        warehouse_inputs.addChild(QTreeWidgetItem(["🏪 مجموعة المخازن"]))
        warehouse_inputs.addChild(QTreeWidgetItem(["🏢 بيانات المخازن"]))
        warehouse_inputs.addChild(QTreeWidgetItem(["📦 بيانات الأصناف"]))
        warehouse_inputs.addChild(QTreeWidgetItem(["💰 تقييم المخزون"]))
        warehouse_inputs.addChild(QTreeWidgetItem(["✏️ التعديل الجماعي للأصناف"]))
        warehouse_inputs.addChild(QTreeWidgetItem(["📊 المخزون الافتتاحي"]))
        warehouse_inputs.addChild(QTreeWidgetItem(["🔗 ربط حساب المخزون بالأستاذ العام"]))
        warehouse_inputs.addChild(QTreeWidgetItem(["⭐ الأصناف المفضلة"]))
        warehouse_inputs.addChild(QTreeWidgetItem(["📝 طلب تعديل تسعيرة"]))
        warehouse_inputs.addChild(QTreeWidgetItem(["⚖️ الميازين"]))
        warehouse_management.addChild(warehouse_inputs)

        # العمليات
        warehouse_operations = QTreeWidgetItem(["+ ⚙️ العمليات"])
        warehouse_operations.addChild(QTreeWidgetItem(["🔒 حجز كميات الأصناف"]))
        warehouse_operations.addChild(QTreeWidgetItem(["📋 طلب صرف / تحويل مواد"]))
        warehouse_operations.addChild(QTreeWidgetItem(["🔄 تحويل مخزني"]))
        warehouse_operations.addChild(QTreeWidgetItem(["📥 استلام مخزني"]))
        warehouse_operations.addChild(QTreeWidgetItem(["📋 أمر توريد مخزني"]))
        warehouse_operations.addChild(QTreeWidgetItem(["📤 أمر صرف مخزني"]))
        warehouse_operations.addChild(QTreeWidgetItem(["⚖️ تسوية مخزون"]))
        warehouse_operations.addChild(QTreeWidgetItem(["🗑️ طلب صرف توالف"]))
        warehouse_operations.addChild(QTreeWidgetItem(["📋 إذن صرف أمانات"]))
        warehouse_operations.addChild(QTreeWidgetItem(["👨‍💼 عهد الموظفين المخزنية"]))
        warehouse_operations.addChild(QTreeWidgetItem(["🧹 تصفية العهد المخزنية"]))
        warehouse_management.addChild(warehouse_operations)

        # التقارير
        warehouse_reports = QTreeWidgetItem(["+ 📊 التقارير"])
        warehouse_reports.addChild(QTreeWidgetItem(["📊 تقارير أرصدة المخزون"]))
        warehouse_reports.addChild(QTreeWidgetItem(["📂 تقارير بيانات المجموعات"]))
        warehouse_reports.addChild(QTreeWidgetItem(["📦 تقارير بيانات الأصناف"]))
        warehouse_reports.addChild(QTreeWidgetItem(["🏷️ طباعة باركود الأصناف"]))
        warehouse_reports.addChild(QTreeWidgetItem(["💰 تقارير تسعيرة الأصناف"]))
        warehouse_reports.addChild(QTreeWidgetItem(["📥 تقارير إذن توريد"]))
        warehouse_reports.addChild(QTreeWidgetItem(["📤 تقارير طلبات الصرف"]))
        warehouse_reports.addChild(QTreeWidgetItem(["🔄 تقارير التحويلات والاستلامات المخزنية"]))
        warehouse_reports.addChild(QTreeWidgetItem(["⚖️ تقارير التسويات المخزنية"]))
        warehouse_reports.addChild(QTreeWidgetItem(["💰 تقارير مديونية الموردين"]))
        warehouse_reports.addChild(QTreeWidgetItem(["👨‍💼 تقارير العهد المخزنية"]))
        warehouse_reports.addChild(QTreeWidgetItem(["📈 تقارير حركة الأصناف"]))
        warehouse_reports.addChild(QTreeWidgetItem(["📄 تقارير العقود والأعمار وكشف الحساب"]))
        warehouse_management.addChild(warehouse_reports)

        self.function_tree.addTopLevelItem(warehouse_management)

        # 7. نظام إدارة المعلومات
        information_management = QTreeWidgetItem(["+ 📊 نظام إدارة المعلومات"])

        # التقارير الإدارية
        admin_reports = QTreeWidgetItem(["+ 📊 التقارير الإدارية"])
        admin_reports.addChild(QTreeWidgetItem(["🖥️ تقارير النظام"]))
        admin_reports.addChild(QTreeWidgetItem(["🔍 الرقابة"]))
        admin_reports.addChild(QTreeWidgetItem(["📁 أرشفة الوثائق"]))
        admin_reports.addChild(QTreeWidgetItem(["📈 حركة الأستاذ العام"]))
        admin_reports.addChild(QTreeWidgetItem(["📊 القوائم الختامية"]))
        admin_reports.addChild(QTreeWidgetItem(["📈 الإحصاءات المالية"]))
        information_management.addChild(admin_reports)

        # تقارير المخزون
        inventory_reports = QTreeWidgetItem(["+ 📊 تقارير المخزون"])
        inventory_reports.addChild(QTreeWidgetItem(["📈 حركة الأصناف"]))
        inventory_reports.addChild(QTreeWidgetItem(["🔄 معدل الدوران"]))
        inventory_reports.addChild(QTreeWidgetItem(["⏰ أعمار المخزون"]))
        information_management.addChild(inventory_reports)

        # تقارير المشتريات
        purchase_reports = QTreeWidgetItem(["+ 📊 تقارير المشتريات"])
        purchase_reports.addChild(QTreeWidgetItem(["📊 الأرصدة الشهرية"]))
        purchase_reports.addChild(QTreeWidgetItem(["🤖 طلب شراء آلي"]))
        information_management.addChild(purchase_reports)

        # تقارير المبيعات
        sales_reports = QTreeWidgetItem(["+ 📊 تقارير المبيعات"])
        sales_reports.addChild(QTreeWidgetItem(["💰 صافي المبيعات"]))
        sales_reports.addChild(QTreeWidgetItem(["📈 هامش الربح"]))
        sales_reports.addChild(QTreeWidgetItem(["📊 الأرصدة الشهرية"]))
        sales_reports.addChild(QTreeWidgetItem(["📦 الأصناف المطلوبة"]))
        information_management.addChild(sales_reports)

        self.function_tree.addTopLevelItem(information_management)

        # 8. الأنظمة المساعدة
        auxiliary_systems = QTreeWidgetItem(["+ 🔧 الأنظمة المساعدة"])
        auxiliary_systems.addChild(QTreeWidgetItem(["📁 أرشفة"]))
        auxiliary_systems.addChild(QTreeWidgetItem(["🔗 التكامل مع أنظمة خارجية"]))
        auxiliary_systems.addChild(QTreeWidgetItem(["⚙️ إعدادات متقدمة"]))
        auxiliary_systems.addChild(QTreeWidgetItem(["🔍 البحث السريع"]))
        auxiliary_systems.addChild(QTreeWidgetItem(["🔔 الإشعارات"]))
        auxiliary_systems.addChild(QTreeWidgetItem(["📤 تصدير PDF / Excel"]))
        auxiliary_systems.addChild(QTreeWidgetItem(["🔐 صلاحيات تفصيلية"]))
        auxiliary_systems.addChild(QTreeWidgetItem(["📊 لوحة المعلومات"]))

        self.function_tree.addTopLevelItem(auxiliary_systems)

        # جعل جميع العناصر الرئيسية مطوية بشكل افتراضي
        for i in range(self.function_tree.topLevelItemCount()):
            item = self.function_tree.topLevelItem(i)
            self.function_tree.collapseItem(item)










    
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        self.main_area = QFrame()
        self.main_area.setStyleSheet("""
            QFrame {
                background-color: white;
                border: none;
            }
        """)
        
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء الستاك ويدجت للصفحات المختلفة
        self.stacked_widget = QStackedWidget()
        
        # إضافة الصفحة الرئيسية
        self.create_dashboard_page()
        self.stacked_widget.addWidget(self.dashboard_page)
        
        main_layout.addWidget(self.stacked_widget)
        self.main_area.setLayout(main_layout)
    
    def create_dashboard_page(self):
        """إنشاء صفحة لوحة التحكم الرئيسية"""
        self.dashboard_page = QWidget()
        
        # إنشاء خلفية متدرجة
        self.dashboard_page.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff, stop:0.3 #f8f9fa, 
                    stop:0.7 #e3f2fd, stop:1 #bbdefb);
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(30)
        
        # العنوان الرئيسي
        welcome_label = QLabel(f"مرحباً بك في نظام Onyx ERP")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #2c3e50;
                background-color: transparent;
                margin: 20px;
            }
        """)
        layout.addWidget(welcome_label)
        
        # معلومات الجلسة
        session_info = self.create_session_info_widget()
        layout.addWidget(session_info)
        
        # الاختصارات السريعة
        shortcuts = self.create_shortcuts_widget()
        layout.addWidget(shortcuts)
        
        # شعار النظام
        logo_area = self.create_logo_area()
        layout.addWidget(logo_area)
        
        # إضافة مساحة مرنة
        layout.addStretch()
        
        # النص السفلي
        footer_label = QLabel("Enterprise Resource Planning Solutions")
        footer_label.setAlignment(Qt.AlignCenter)
        footer_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #7f8c8d;
                background-color: transparent;
                margin: 10px;
            }
        """)
        layout.addWidget(footer_label)
        
        trust_label = QLabel("Absolute Trust")
        trust_label.setAlignment(Qt.AlignCenter)
        trust_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-style: italic;
                color: #95a5a6;
                background-color: transparent;
            }
        """)
        layout.addWidget(trust_label)
        
        self.dashboard_page.setLayout(layout)
    
    def create_session_info_widget(self):
        """إنشاء ويدجت معلومات الجلسة"""
        info_group = QGroupBox("معلومات الجلسة الحالية")
        info_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                background-color: rgba(255, 255, 255, 0.9);
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding-top: 15px;
                margin: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        info_layout = QGridLayout()
        info_layout.setSpacing(15)
        
        # معلومات الجلسة
        session_data = [
            ("👤 المستخدم:", self.session_data.get('full_name', 'غير محدد')),
            ("🏢 الشركة:", self.session_data.get('company', 'غير محدد')),
            ("🏪 الفرع:", self.session_data.get('branch', 'غير محدد')),
            ("📅 السنة المالية:", self.session_data.get('year', 'غير محدد')),
            ("🌐 اللغة:", "عربي" if self.session_data.get('language', 'ar') == 'ar' else "English"),
            ("📊 الوحدة المحاسبية:", self.session_data.get('accounting_unit', 'غير محدد')),
            ("🎯 المركز الرئيسي:", self.session_data.get('main_center', 'غير محدد')),
            ("🕐 وقت الدخول:", QDate.currentDate().toString('yyyy-MM-dd'))
        ]
        
        for i, (label, value) in enumerate(session_data):
            row = i // 2
            col = (i % 2) * 2
            
            label_widget = QLabel(label)
            label_widget.setStyleSheet("""
                QLabel {
                    font-weight: bold;
                    color: #34495e;
                    background-color: transparent;
                }
            """)
            
            value_widget = QLabel(str(value))
            value_widget.setStyleSheet("""
                QLabel {
                    color: #2c3e50;
                    background-color: transparent;
                    padding: 5px;
                    border: 1px solid #ecf0f1;
                    border-radius: 4px;
                    background-color: #f8f9fa;
                }
            """)
            
            info_layout.addWidget(label_widget, row, col)
            info_layout.addWidget(value_widget, row, col + 1)
        
        info_group.setLayout(info_layout)
        return info_group
    
    def create_shortcuts_widget(self):
        """إنشاء ويدجت الاختصارات السريعة"""
        shortcuts_group = QGroupBox("الاختصارات السريعة")
        shortcuts_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                background-color: rgba(255, 255, 255, 0.9);
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                padding-top: 15px;
                margin: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        shortcuts_layout = QGridLayout()
        shortcuts_layout.setSpacing(15)
        
        # أزرار الاختصارات
        shortcuts = [
            ("📋 دليل الحسابات", "accounts", "#3498db"),
            ("📝 قيد يومي جديد", "journal", "#2ecc71"),
            ("🧾 فاتورة بيع", "sales", "#e74c3c"),
            ("📦 إدارة المخزون", "inventory", "#f39c12"),
            ("📊 التقارير", "reports", "#9b59b6"),
            ("👥 إدارة العملاء", "customers", "#1abc9c"),
            ("⚙️ إعدادات النظام", "settings", "#34495e"),
            ("🔍 البحث والاستعلام", "search", "#e67e22")
        ]
        
        for i, (text, action, color) in enumerate(shortcuts):
            row = i // 4
            col = i % 4
            
            button = QPushButton(text)
            button.setFixedSize(180, 60)
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 12px;
                    font-weight: bold;
                    padding: 10px;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                    transform: translateY(-2px);
                }}
                QPushButton:pressed {{
                    background-color: {self.darken_color(color, 0.3)};
                }}
            """)
            
            # ربط الإشارة
            button.clicked.connect(lambda checked, a=action: self.handle_shortcut(a))
            
            shortcuts_layout.addWidget(button, row, col)
        
        shortcuts_group.setLayout(shortcuts_layout)
        return shortcuts_group
    
    def create_logo_area(self):
        """إنشاء منطقة الشعار"""
        logo_frame = QFrame()
        logo_frame.setFixedHeight(150)
        logo_frame.setStyleSheet("""
            QFrame {
                background-color: transparent;
            }
        """)
        
        logo_layout = QHBoxLayout()
        logo_layout.setContentsMargins(0, 0, 0, 0)
        
        # شعار Onyx ERP
        onyx_logo = QLabel("Onyx ERP")
        onyx_logo.setAlignment(Qt.AlignCenter)
        onyx_logo.setStyleSheet("""
            QLabel {
                font-size: 48px;
                font-weight: bold;
                color: #2c3e50;
                background-color: transparent;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            }
        """)
        
        logo_layout.addWidget(onyx_logo)
        logo_frame.setLayout(logo_layout)
        
        return logo_frame
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = QStatusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #ecf0f1;
                color: #2c3e50;
                border-top: 1px solid #bdc3c7;
                font-size: 11px;
            }
        """)
        
        # معلومات الحالة
        status_text = f"متصل | المستخدم: {self.session_data.get('full_name', 'غير محدد')} | الشركة: {self.session_data.get('company', 'غير محدد')}"
        status_bar.showMessage(status_text)
        
        self.setStatusBar(status_bar)
    
    def get_main_stylesheet(self):
        """الحصول على تنسيق CSS الرئيسي"""
        return """
            QMainWindow {
                background-color: #ecf0f1;
            }
            QWidget {
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 12px;
            }
        """
    
    def create_app_icon(self):
        """إنشاء أيقونة التطبيق"""
        pixmap = QPixmap(32, 32)
        pixmap.fill(QColor(44, 62, 80))
        
        painter = QPainter(pixmap)
        painter.setPen(QColor(255, 255, 255))
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "O")
        painter.end()
        
        return QIcon(pixmap)
    
    def create_icon(self, emoji):
        """إنشاء أيقونة من الإيموجي"""
        pixmap = QPixmap(32, 32)
        pixmap.fill(QColor(0, 0, 0, 0))
        
        painter = QPainter(pixmap)
        painter.setFont(QFont("Segoe UI Emoji", 20))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, emoji)
        painter.end()
        
        return QIcon(pixmap)
    
    def darken_color(self, color, factor=0.2):
        """تغميق اللون"""
        color = QColor(color)
        h, s, l, a = color.getHsl()
        l = max(0, int(l * (1 - factor)))
        color.setHsl(h, s, l, a)
        return color.name()
    
    def setup_connections(self):
        """ربط الإشارات بالوظائف"""
        pass
    
    def load_user_preferences(self):
        """تحميل تفضيلات المستخدم"""
        # يمكن إضافة تحميل الإعدادات المخصصة هنا
        pass
    
    def on_tree_item_clicked(self, item, column):
        """معالجة النقر على عنصر في الشجرة"""
        item_text = item.text(0)

        # إزالة علامات + أو - من النص للمقارنة وإزالة الرموز التعبيرية
        import re
        clean_text = item_text.replace("+ ", "").replace("- ", "")
        # إزالة الرموز التعبيرية
        clean_text = re.sub(r'[^\w\s\u0600-\u06FF]', '', clean_text).strip()

        print(f"تم النقر على: '{item_text}' -> النص المنظف: '{clean_text}'")  # للتشخيص

        # تحديد الوظيفة المطلوبة حسب النص
        if "المتغيرات العامة" in clean_text:
            self.show_general_variables()
        elif "إعداد فترات النظام" in clean_text:
            self.show_system_periods()
        elif "تهيئة العملات" in clean_text:
            self.show_currencies_setup()
        elif "بيانات المناطق والدول" in clean_text:
            self.show_regions_countries()
        elif "مجموعة الفروع" in clean_text:
            self.show_branch_groups()
        elif "بيانات الشركات" in clean_text:
            self.show_companies_data()
        elif "تهيئة الدليل المحاسبي" in clean_text:
            self.show_accounting_guide_setup()
        elif "الترميزات العامة" in clean_text:
            self.show_general_codes()
        elif "أنواع الهيكل الإداري" in clean_text:
            self.show_admin_structure_types()
        elif "تهيئة الحقول الإضافية" in clean_text:
            self.show_additional_fields()
        elif "الدليل المحاسبي" in clean_text:
            self.show_accounting_guide()
        elif "الهيكل التنظيمي" in clean_text:
            self.show_organizational_structure()
        elif "التقسيمات الإدارية" in clean_text:
            self.show_administrative_divisions()
        elif "تعريف الفروع" in clean_text:
            self.show_branches_definition()
        elif "شجرة الفروع" in clean_text:
            self.show_branches_tree()
        elif "إعداد المستخدمين" in clean_text:
            self.show_users_setup()
        # التعامل مع الأقسام الرئيسية
        elif "تهيئة النظام" in clean_text:
            self.show_system_setup_module()
        elif "إدارة النظام" in clean_text:
            self.show_system_management_module()
        elif "أنظمة الحسابات" in clean_text:
            self.show_accounting_systems_module()
        elif "إدارة العملاء والمبيعات" in clean_text:
            self.show_customers_sales_module()
        elif "إدارة الموردين والمشتريات" in clean_text:
            self.show_suppliers_purchases_module()
        elif "نظام إدارة المعلومات" in clean_text:
            self.show_information_management_module()
        else:
            # للعناصر الفرعية الأخرى، عرض رسالة تطوير
            QMessageBox.information(self, "تم النقر",
                f"تم النقر على: '{item_text}'\n"
                f"النص المنظف: '{clean_text}'\n\n"
                f"هذه الوظيفة قيد التطوير أو لم يتم ربطها بعد.")

    def show_general_variables(self):
        """عرض نافذة المتغيرات العامة"""
        try:
            from src.modules.setup.general_variables import GeneralVariablesDialog
            dialog = GeneralVariablesDialog(self.db_manager, self)
            dialog.exec_()
        except ImportError as e:
            QMessageBox.warning(self, "خطأ", f"لا يمكن تحميل نافذة المتغيرات العامة:\n{str(e)}")

    def show_system_periods(self):
        """عرض نافذة إعداد فترات النظام"""
        self.show_coming_soon("إعداد فترات النظام")

    def show_currencies_setup(self):
        """عرض نافذة تهيئة العملات"""
        self.show_coming_soon("تهيئة العملات")

    def show_regions_countries(self):
        """عرض نافذة بيانات المناطق والدول"""
        self.show_coming_soon("بيانات المناطق والدول")

    def show_branch_groups(self):
        """عرض نافذة مجموعة الفروع"""
        self.show_coming_soon("مجموعة الفروع")

    def show_companies_data(self):
        """عرض نافذة بيانات الشركات"""
        self.show_coming_soon("بيانات الشركات")

    def show_accounting_guide_setup(self):
        """عرض نافذة تهيئة الدليل المحاسبي"""
        self.show_coming_soon("تهيئة الدليل المحاسبي")

    def show_general_codes(self):
        """عرض نافذة الترميزات العامة"""
        self.show_coming_soon("الترميزات العامة")

    def show_admin_structure_types(self):
        """عرض نافذة أنواع الهيكل الإداري"""
        self.show_coming_soon("أنواع الهيكل الإداري")

    def show_additional_fields(self):
        """عرض نافذة تهيئة الحقول الإضافية"""
        self.show_coming_soon("تهيئة الحقول الإضافية")

    def show_organizational_structure(self):
        """عرض نافذة الهيكل التنظيمي"""
        try:
            from src.modules.setup.administrative_structure import AdministrativeStructureDialog
            dialog = AdministrativeStructureDialog(self.db_manager, self)
            dialog.exec_()
        except ImportError:
            self.show_coming_soon("الهيكل التنظيمي")

    def show_administrative_divisions(self):
        """عرض نافذة التقسيمات الإدارية"""
        try:
            from src.modules.setup.administrative_divisions import AdministrativeDivisionsDialog
            dialog = AdministrativeDivisionsDialog(self.db_manager, self)
            dialog.exec_()
        except ImportError:
            self.show_coming_soon("التقسيمات الإدارية")

    def show_branches_definition(self):
        """عرض نافذة تعريف الفروع"""
        try:
            from src.modules.setup.branch_definition import BranchDefinitionDialog
            dialog = BranchDefinitionDialog(self.db_manager, self)
            dialog.exec_()
        except ImportError:
            self.show_coming_soon("تعريف الفروع")

    def show_branches_tree(self):
        """عرض نافذة شجرة الفروع"""
        try:
            from src.modules.setup.branch_tree import BranchTreeDialog
            dialog = BranchTreeDialog(self.db_manager, self)
            dialog.exec_()
        except ImportError:
            self.show_coming_soon("شجرة الفروع")

    def show_accounting_guide(self):
        """عرض نافذة دليل الحسابات"""
        try:
            from src.modules.setup.accounting_guide import AccountingGuideDialog
            dialog = AccountingGuideDialog(self.db_manager, self)
            dialog.exec_()
        except ImportError:
            self.show_coming_soon("دليل الحسابات")

    def show_users_setup(self):
        """عرض نافذة إعداد المستخدمين"""
        try:
            from src.modules.users.users_management import UsersManagementDialog
            dialog = UsersManagementDialog(self.db_manager, self)
            dialog.exec_()
        except ImportError:
            self.show_coming_soon("إعداد المستخدمين")

    def show_coming_soon(self, feature_name):
        """عرض رسالة قيد التطوير"""
        QMessageBox.information(
            self, "قيد التطوير",
            f"🚧 الوظيفة '{feature_name}' قيد التطوير\n\n"
            "سيتم إضافتها في التحديثات القادمة"
        )

    def create_module_with_tabs(self, module_name, tabs_config):
        """إنشاء وحدة مع تبويبات"""
        module_widget = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)

        # عنوان الوحدة
        title_label = QLabel(module_name)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # إنشاء التبويبات
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #bdc3c7;
            }
        """)

        # إضافة التبويبات حسب التكوين
        for tab_name, tab_content in tabs_config.items():
            tab = QWidget()
            tab_layout = QVBoxLayout()

            # إضافة محتوى التبويب
            if isinstance(tab_content, list):
                for item in tab_content:
                    btn = QPushButton(item)
                    btn.setStyleSheet("""
                        QPushButton {
                            text-align: right;
                            padding: 8px 16px;
                            margin: 2px;
                            background-color: #f8f9fa;
                            border: 1px solid #dee2e6;
                            border-radius: 4px;
                        }
                        QPushButton:hover {
                            background-color: #e9ecef;
                        }
                        QPushButton:pressed {
                            background-color: #dee2e6;
                        }
                    """)
                    btn.clicked.connect(lambda checked, text=item: self.handle_module_action(text))
                    tab_layout.addWidget(btn)

            tab_layout.addStretch()
            tab.setLayout(tab_layout)
            tab_widget.addTab(tab, tab_name)

        layout.addWidget(tab_widget)
        module_widget.setLayout(layout)

        return module_widget

    def handle_module_action(self, action_text):
        """معالجة إجراءات الوحدات"""
        QMessageBox.information(self, "إجراء", f"تم النقر على: {action_text}\n\nهذه الوظيفة قيد التطوير.")

    def show_system_setup_module(self):
        """عرض وحدة تهيئة النظام"""
        tabs_config = {
            "التهيئة العامة": [
                "🔧 المتغيرات العامة", "📅 اعداد فترات النظام", "💱 تهيئة العملات",
                "🌍 الأقاليم الدولية", "🏳️ بيانات الدول", "🏛️ بيانات المحافظات",
                "🏢 بيانات المديريات", "📍 بيانات المناطق", "🏪 مجموعة الفروع",
                "🏢 بيانات الشركات", "📊 تهيئة الدليل المحاسبي", "🔢 الترميزات العامة",
                "👥 أنواع الهيكل الإداري", "📝 تهيئة الحقول الاضافية"
            ],
            "المدخلات": [
                "📋 الدليل المحاسبي", "🎯 مراكز التكلفة", "🏗️ الهيكل الإداري",
                "🔄 الحسابات الوسيطة", "👨‍💼 بيانات الموظفين", "🔗 ربط الموظفين بالمهن",
                "💰 الحسابات المدينة والدائنة الأخرى", "🔗 ربط الحسابات بالحسابات العامة والتدفقات النقدية"
            ]
        }

        module_widget = self.create_module_with_tabs("🏢 تهيئة النظام", tabs_config)

        # إضافة الوحدة إلى الستاك ويدجت
        self.stacked_widget.addWidget(module_widget)
        self.stacked_widget.setCurrentWidget(module_widget)

    def show_system_management_module(self):
        """عرض وحدة إدارة النظام"""
        tabs_config = {
            "إدارة المستخدمين": [
                "👥 مجموعة المستخدمين", "👤 بيانات المستخدمين", "👁️ عرض المستخدمين", "🔍 الرقابة"
            ],
            "صلاحيات المستخدمين": [
                "🖥️ صلاحيات الشاشات", "📑 صلاحيات التبيوبات", "📝 صلاحيات المدخلات",
                "⚡ صلاحيات العمليات", "🔔 تنبيهات النظام", "📱 اعتماد أجهزة تطبيقات الموبايل", "📊 تقارير الصلاحيات"
            ],
            "خصوصية المستخدمين": [
                "⭐ الشاشات المفضله", "🔗 الشاشات المرتبطة بالمستخدم", "🔑 تغيير كلمة السر", "📄 الترويسة حسب المستخدم"
            ],
            "الإقفالات": [
                "⏸️ التوقيف الشهري/الفتري", "🔐 الاقفال الشهري/الفتري", "🔒 الاقفال السنوي", "🔓 إلغاء الاقفالات"
            ],
            "النسخ الاحتياطية": [
                "⚙️ اعدادات النسخ الاحتياطي", "💾 النسخ الاحتياطي", "🔄 استرجاع النسخ الاحتياطي"
            ],
            "إدارة قاعدة البيانات": [
                "🌐 ترميزات اللغات", "📁 الارشفة", "🏢 الوحدات المحاسبية", "🔧 متغيرات قاعدة البيانات",
                "💿 المساحات التخزينية", "🔄 تحديث قاعدة البيانات", "🔄 تحديث قاعدة البيانات كل السنوات", "📊 إعادة تقييم المخزون"
            ]
        }

        module_widget = self.create_module_with_tabs("🔧 إدارة النظام", tabs_config)

        # إضافة الوحدة إلى الستاك ويدجت
        self.stacked_widget.addWidget(module_widget)
        self.stacked_widget.setCurrentWidget(module_widget)

    def show_accounting_systems_module(self):
        """عرض وحدة أنظمة الحسابات"""
        tabs_config = {
            "التهيئة": [
                "🔧 متغيرات", "🔢 الترميزات", "💰 مجموعة الصناديق"
            ],
            "المدخلات": [
                "💰 بيانات الصناديق", "🏦 بيانات البنوك", "📊 الارصده الافتتاحية"
            ],
            "العمليات": [
                "💳 اشعارات دائنه", "💸 اشعارات مدينه", "📥 سند قبض", "📤 سند صرف",
                "📝 قيود يومية", "💱 صرف عملة", "🏦 تسوية بنوك", "📋 طلب سند قبض",
                "📋 طلب سند صرف", "💰 جرد النقدية"
            ],
            "التقارير": [
                "📋 تقارير كشف حساب", "💰 تقارير حركة الصناديق", "🏦 تقارير حركة البنوك",
                "📝 تقارير اليومية العامة", "⚖️ تقارير ميزان المراجعه", "📈 تقارير قائمة الدخل",
                "📊 تقارير المركز المالي", "📊 تقارير الأرصدة الافتتاحية", "📥 تقارير سندات القبض",
                "📤 تقارير سندات الصرف", "📝 تقارير القيود اليومية", "💱 تقارير صرف عملة",
                "📋 تقارير طلبات الصرف والقبض", "💳 تقارير الشيكات المستحقة", "💰 تقارير قائمة التدفقات النقدية"
            ]
        }

        module_widget = self.create_module_with_tabs("💰 أنظمة الحسابات", tabs_config)

        # إضافة الوحدة إلى الستاك ويدجت
        self.stacked_widget.addWidget(module_widget)
        self.stacked_widget.setCurrentWidget(module_widget)

    def show_customers_sales_module(self):
        """عرض وحدة إدارة العملاء والمبيعات"""
        tabs_config = {
            "التهيئة": [
                "🔧 متغيرات نظام العملاء", "👥 أنواع العملاء", "👨‍💼 أنواع المحصلين والمسوقين",
                "💰 أنواع عروض الأسعار", "📋 أنواع طلبات العملاء", "🧾 أنواع فواتير المبيعات",
                "↩️ أنواع مردود المبيعات", "❓ أسباب مردود المبيعات"
            ],
            "المدخلات": [
                "👥 بيانات العملاء", "👨‍💼 بيانات المسوقين والمحصلين", "🚛 بيانات السائقين",
                "🛣️ بيانات خطوط السير", "📊 الأرصدة الافتتاحية للعملاء", "📋 طلب فتح حساب",
                "📦 الأصناف المطلوبة", "💰 تسعيرة الاصناف"
            ],
            "العمليات": [
                "🧾 فاتورة مبيعات", "↩️ فاتورة مردود مبيعات", "📥 سندات القبض", "💰 عروض الأسعار",
                "📋 طلبات العملاء", "📢 طلبات اشعار عملاء", "📊 متابعة فواتير المبيعات عقود المبيعات",
                "🎫 مبيعات الكوبونات", "⏰ فواتير المبيعات المستحقة للسداد"
            ],
            "التقارير": [
                "📋 تقارير كشف حساب العملاء", "🧾 تقارير فواتير المبيعات", "↩️ تقارير فواتير مردود مبيعات",
                "📥 تقارير سندات القبض", "📦 تقارير الأصناف المطلوبة", "📋 تقارير طلبات العملاء",
                "💰 تقارير عروض الأسعار", "📈 تقارير المبيعات اليومية", "🗺️ تقارير بيانات المناطق",
                "👨‍💼 تقارير مندوبي المبيعات", "📥 تقارير سندات القبض", "📄 تقارير عقود المبيعات",
                "📊 رسوم بيانية", "💰 تقارير التحصيل", "📊 تقارير الأرصدة الافتتاحية", "⏰ تقارير اعمار الديون"
            ]
        }

        module_widget = self.create_module_with_tabs("👥 إدارة العملاء والمبيعات", tabs_config)

        # إضافة الوحدة إلى الستاك ويدجت
        self.stacked_widget.addWidget(module_widget)
        self.stacked_widget.setCurrentWidget(module_widget)

    def show_suppliers_purchases_module(self):
        """عرض وحدة إدارة الموردين والمشتريات"""
        tabs_config = {
            "التهيئة": [
                "🔧 متغيرات نظام الموردين", "🏢 أنواع الموردين", "📋 أنواع طلبات الشراء",
                "🧾 أنواع فواتير المشتريات", "↩️ أنواع مردود المشتريات", "❓ أسباب مردود المشتريات"
            ],
            "المدخلات": [
                "🏢 بيانات الموردين", "📊 الأرصدة الافتتاحية للموردين", "📋 طلب فتح حساب"
            ],
            "العمليات": [
                "🧾 فاتورة مشتريات", "↩️ فاتورة مردود مشتريات", "📤 سندات الصرف", "📋 طلبات الشراء",
                "📢 طلبات اشعار موردين", "📊 متابعة فواتير المشتريات عقود المشتريات", "⏰ فواتير المشتريات المستحقة للسداد"
            ],
            "التقارير": [
                "📋 تقارير كشف حساب الموردين", "🧾 تقارير فواتير المشتريات", "↩️ تقارير فواتير مردود مشتريات",
                "📤 تقارير سندات الصرف", "📋 تقارير طلبات الشراء", "📈 تقارير المشتريات اليومية",
                "📄 تقارير عقود المشتريات", "📊 رسوم بيانية", "💰 تقارير السداد",
                "📊 تقارير الأرصدة الافتتاحية", "⏰ تقارير اعمار الديون"
            ]
        }

        module_widget = self.create_module_with_tabs("🏢 إدارة الموردين والمشتريات", tabs_config)

        # إضافة الوحدة إلى الستاك ويدجت
        self.stacked_widget.addWidget(module_widget)
        self.stacked_widget.setCurrentWidget(module_widget)

    def show_information_management_module(self):
        """عرض وحدة نظام إدارة المعلومات"""
        tabs_config = {
            "التقارير": [
                "📊 تقارير الأصناف", "📦 تقارير المخزون", "🔄 تقارير حركة المخزون", "📋 تقارير الجرد",
                "📊 تقارير الأرصدة الافتتاحية للمخزون", "📈 تقارير تحليل المبيعات", "📊 تقارير تحليل المشتريات",
                "💰 تقارير الربحية", "📊 تقارير مقارنة الأداء", "📈 تقارير الاتجاهات", "🎯 تقارير مراكز التكلفة",
                "👥 تقارير الموظفين", "📊 تقارير إدارية", "📈 لوحة المعلومات التنفيذية", "📊 تقارير مخصصة"
            ]
        }

        module_widget = self.create_module_with_tabs("📊 نظام إدارة المعلومات", tabs_config)

        # إضافة الوحدة إلى الستاك ويدجت
        self.stacked_widget.addWidget(module_widget)
        self.stacked_widget.setCurrentWidget(module_widget)

    def on_tree_item_expanded(self, item):
        """معالجة توسيع عنصر الشجرة"""
        text = item.text(0)
        if text.startswith("+ "):
            # تغيير علامة + إلى -
            new_text = text.replace("+ ", "- ")
            item.setText(0, new_text)

    def on_tree_item_collapsed(self, item):
        """معالجة انكماش عنصر الشجرة"""
        text = item.text(0)
        if text.startswith("- "):
            # تغيير علامة - إلى +
            new_text = text.replace("- ", "+ ")
            item.setText(0, new_text)
    
    def handle_shortcut(self, action):
        """معالجة الاختصارات السريعة"""
        if action == "accounts":
            self.show_accounts_management()
        elif action == "journal":
            self.show_new_journal_entry()
        elif action == "sales":
            QMessageBox.information(self, "فاتورة بيع", "وحدة فواتير البيع قيد التطوير")
        elif action == "inventory":
            QMessageBox.information(self, "إدارة المخزون", "وحدة إدارة المخزون قيد التطوير")
        elif action == "reports":
            self.show_reports_module()
        elif action == "customers":
            QMessageBox.information(self, "إدارة العملاء", "وحدة إدارة العملاء قيد التطوير")
        elif action == "settings":
            self.show_settings()
        elif action == "search":
            QMessageBox.information(self, "البحث والاستعلام", "وحدة البحث والاستعلام قيد التطوير")
        else:
            QMessageBox.information(self, "اختصار سريع", "هذه الوحدة قيد التطوير")
    
    def show_dashboard(self):
        """عرض لوحة التحكم الرئيسية"""
        try:
            # إنشاء لوحة تحكم بسيطة
            dashboard_widget = QWidget()
            layout = QVBoxLayout()

            # العنوان
            title = QLabel("🏢 لوحة التحكم الرئيسية - نظام Onyx ERP")
            title.setStyleSheet("""
                QLabel {
                    font-size: 24px;
                    font-weight: bold;
                    color: #2c3e50;
                    padding: 20px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #3498db, stop:1 #2980b9);
                    color: white;
                    border-radius: 10px;
                    margin-bottom: 20px;
                }
            """)
            title.setAlignment(Qt.AlignCenter)
            layout.addWidget(title)

            # معلومات الشركة
            company_info = QLabel(f"""
                <div style='text-align: center; font-size: 16px; color: #34495e;'>
                    <h2>🚛 {self.session_data.get('company', 'صقالات أبو سيف للنقل')}</h2>
                    <p><strong>المستخدم:</strong> {self.session_data.get('username', 'admin')}</p>
                    <p><strong>الدور:</strong> {self.session_data.get('role', 'مدير النظام')}</p>
                    <p><strong>تاريخ اليوم:</strong> {QDate.currentDate().toString('yyyy-MM-dd')}</p>
                </div>
            """)
            layout.addWidget(company_info)

            # إحصائيات سريعة
            stats_layout = QGridLayout()

            # الحصول على الإحصائيات الحقيقية
            customers_count, products_count, invoices_count, total_sales = self.get_dashboard_stats()

            # بطاقة العملاء
            customers_card = self.create_stats_card("👥", "العملاء", str(customers_count), "#3498db")
            stats_layout.addWidget(customers_card, 0, 0)

            # بطاقة المنتجات
            products_card = self.create_stats_card("📦", "المنتجات", str(products_count), "#27ae60")
            stats_layout.addWidget(products_card, 0, 1)

            # بطاقة الفواتير
            invoices_card = self.create_stats_card("🧾", "الفواتير", str(invoices_count), "#e74c3c")
            stats_layout.addWidget(invoices_card, 0, 2)

            # بطاقة المبيعات
            sales_card = self.create_stats_card("💰", "المبيعات", f"{total_sales:,.0f} ريال", "#f39c12")
            stats_layout.addWidget(sales_card, 0, 3)

            layout.addLayout(stats_layout)

            # رسالة ترحيب
            welcome_msg = QLabel("""
                <div style='text-align: center; font-size: 14px; color: #7f8c8d; margin-top: 30px;'>
                    <h3>مرحباً بك في نظام Onyx ERP</h3>
                    <p>نظام إدارة موارد المؤسسات المتكامل</p>
                    <p>يمكنك البدء باستخدام القائمة الجانبية لاختيار الوحدة المطلوبة</p>
                </div>
            """)
            layout.addWidget(welcome_msg)

            layout.addStretch()
            dashboard_widget.setLayout(layout)

            # إضافة لوحة التحكم إلى المكدس
            self.stacked_widget.addWidget(dashboard_widget)
            self.stacked_widget.setCurrentWidget(dashboard_widget)

            # تحديث عنوان النافذة
            self.setWindowTitle(f"Onyx ERP - لوحة التحكم - {self.session_data.get('company', '')}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح لوحة التحكم:\n{str(e)}")
    
    def show_reports(self):
        """عرض التقارير"""
        QMessageBox.information(self, "التقارير", "وحدة التقارير قيد التطوير")
    
    def show_notifications(self):
        """عرض التنبيهات"""
        QMessageBox.information(self, "التنبيهات", "لا توجد تنبيهات جديدة")
    
    def show_help(self):
        """عرض المساعدة"""
        help_text = """
        مرحباً بك في نظام Onyx ERP
        
        الوحدات المتاحة:
        • إدارة النظام
        • إدارة الحسابات
        • إدارة المبيعات
        • إدارة المشتريات
        • إدارة المخزون
        • نظام إدارة المعلومات
        • الإجراءات المساعدة
        
        للمساعدة الفنية، يرجى الاتصال بفريق الدعم.
        """
        QMessageBox.information(self, "المساعدة", help_text)
    
    def show_settings(self):
        """عرض الإعدادات"""
        QMessageBox.information(self, "الإعدادات", "وحدة الإعدادات قيد التطوير")
    
    def show_system_settings(self):
        """عرض إعدادات النظام"""
        QMessageBox.information(self, "إعدادات النظام", "وحدة إعدادات النظام قيد التطوير")
    
    def show_user_management(self):
        """عرض إدارة المستخدمين"""
        QMessageBox.information(self, "إدارة المستخدمين", "وحدة إدارة المستخدمين قيد التطوير")
    
    def show_about(self):
        """عرض معلومات حول النظام"""
        about_text = f"""
        نظام Onyx ERP
        إصدار 1.0.0
        
        نظام تخطيط موارد المؤسسات
        Enterprise Resource Planning Solutions
        
        المستخدم الحالي: {self.session_data.get('full_name', 'غير محدد')}
        الشركة: {self.session_data.get('company', 'غير محدد')}
        
        Absolute Trust
        """
        QMessageBox.about(self, "حول النظام", about_text)
    
    def show_accounts_management(self):
        """عرض وحدة إدارة الحسابات"""
        try:
            accounts_widget = AccountsManagementWidget(self.db_manager)
            
            # إضافة الويدجت إلى الستاك
            self.stacked_widget.addWidget(accounts_widget)
            self.stacked_widget.setCurrentWidget(accounts_widget)
            
            # تحديث عنوان النافذة
            self.setWindowTitle(f"Onyx ERP - دليل الحسابات - {self.session_data.get('company', '')}")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح وحدة إدارة الحسابات:\n{str(e)}")
    
    def show_journal_entries(self):
        """عرض وحدة القيود اليومية"""
        try:
            journal_widget = JournalEntriesWidget(self.db_manager)

            # إضافة الوحدة إلى الستاك
            self.stacked_widget.addWidget(journal_widget)
            self.stacked_widget.setCurrentWidget(journal_widget)
            self.current_widget = journal_widget

            # تحديث عنوان النافذة
            self.setWindowTitle(f"Onyx ERP - القيود اليومية - {self.session_data.get('company', '')}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح وحدة القيود اليومية:\n{str(e)}")
    
    def show_new_journal_entry(self):
        """عرض حوار قيد يومي جديد"""
        try:
            from ..modules.journal_entries_module import JournalEntryDialog
            
            dialog = JournalEntryDialog(self.db_manager, parent=self)
            if dialog.exec_() == QDialog.Accepted:
                QMessageBox.information(self, "نجح", "تم إضافة القيد بنجاح")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح حوار القيد الجديد:\n{str(e)}")
    
    def show_accounts_module(self):
        """عرض وحدة إدارة الحسابات"""
        try:
            from ..modules.accounts_module import AccountsManagementWidget

            # إنشاء الوحدة
            accounts_widget = AccountsManagementWidget(self.db_manager)

            # إضافة الوحدة إلى الستاك
            self.stacked_widget.addWidget(accounts_widget)
            self.stacked_widget.setCurrentWidget(accounts_widget)
            self.current_widget = accounts_widget

            # تحديث عنوان النافذة
            self.setWindowTitle(f"Onyx ERP - دليل الحسابات - {self.session_data.get('company', '')}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح وحدة الحسابات:\n{str(e)}")
    
    def show_reports_module(self):
        """عرض وحدة التقارير"""
        try:
            reports_widget = ReportsWidget(self.db_manager)

            # إضافة الوحدة إلى الستاك
            self.stacked_widget.addWidget(reports_widget)
            self.stacked_widget.setCurrentWidget(reports_widget)
            self.current_widget = reports_widget

            # تحديث عنوان النافذة
            self.setWindowTitle(f"Onyx ERP - التقارير المالية - {self.session_data.get('company', '')}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح وحدة التقارير:\n{str(e)}")
    
    def show_customers_suppliers_module(self):
        """عرض وحدة العملاء والموردين"""
        try:
            from ..modules.customers_suppliers_module import CustomersSupplierModule

            # إنشاء الوحدة
            customers_suppliers_widget = CustomersSupplierModule(self.db_manager)

            # إضافة الوحدة إلى الستاك
            self.stacked_widget.addWidget(customers_suppliers_widget)
            self.stacked_widget.setCurrentWidget(customers_suppliers_widget)
            self.current_widget = customers_suppliers_widget

            # تحديث عنوان النافذة
            self.setWindowTitle(f"Onyx ERP - العملاء والموردين - {self.session_data.get('company', '')}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح وحدة العملاء والموردين:\n{str(e)}")
    
    def show_advanced_reports_module(self):
        """عرض وحدة التقارير المتقدمة"""
        try:
            from ..modules.advanced_reports_module import AdvancedReportsModule

            # إنشاء الوحدة
            advanced_reports_widget = AdvancedReportsModule(self.db_manager)

            # إضافة الوحدة إلى الستاك
            self.stacked_widget.addWidget(advanced_reports_widget)
            self.stacked_widget.setCurrentWidget(advanced_reports_widget)
            self.current_widget = advanced_reports_widget

            # تحديث عنوان النافذة
            self.setWindowTitle(f"Onyx ERP - التقارير المتقدمة - {self.session_data.get('company', '')}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح وحدة التقارير المتقدمة:\n{str(e)}")

    def show_system_management(self):
        """عرض وحدة إدارة النظام"""
        try:
            from ..modules.system_management_module import SystemManagementModule

            # إنشاء وحدة إدارة النظام
            system_management_widget = SystemManagementModule(self.db_manager)

            # إضافة الوحدة إلى المكدس
            self.stacked_widget.addWidget(system_management_widget)
            self.stacked_widget.setCurrentWidget(system_management_widget)

            # تحديث عنوان النافذة
            self.setWindowTitle(f"Onyx ERP - إدارة النظام - {self.session_data.get('company', '')}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح وحدة إدارة النظام:\n{str(e)}")

    def show_customers_management(self):
        """عرض وحدة إدارة العملاء"""
        try:
            from ..modules.customers_module import CustomersWidget

            # إنشاء وحدة إدارة العملاء
            customers_widget = CustomersWidget(self.db_manager)

            # إضافة الوحدة إلى المكدس
            self.stacked_widget.addWidget(customers_widget)
            self.stacked_widget.setCurrentWidget(customers_widget)

            # تحديث عنوان النافذة
            self.setWindowTitle(f"Onyx ERP - إدارة العملاء - {self.session_data.get('company', '')}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح وحدة إدارة العملاء:\n{str(e)}")

    def show_products_management(self):
        """عرض وحدة إدارة المنتجات"""
        try:
            from ..modules.products_module import ProductsWidget

            # إنشاء وحدة إدارة المنتجات
            products_widget = ProductsWidget(self.db_manager)

            # إضافة الوحدة إلى المكدس
            self.stacked_widget.addWidget(products_widget)
            self.stacked_widget.setCurrentWidget(products_widget)

            # تحديث عنوان النافذة
            self.setWindowTitle(f"Onyx ERP - إدارة المنتجات والخدمات - {self.session_data.get('company', '')}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح وحدة إدارة المنتجات:\n{str(e)}")

    def show_sales_invoices(self):
        """عرض وحدة فواتير المبيعات"""
        try:
            from ..modules.sales_invoices_new import SalesInvoicesWidget

            # إنشاء وحدة فواتير المبيعات
            sales_invoices_widget = SalesInvoicesWidget(self.db_manager)

            # إضافة الوحدة إلى المكدس
            self.stacked_widget.addWidget(sales_invoices_widget)
            self.stacked_widget.setCurrentWidget(sales_invoices_widget)

            # تحديث عنوان النافذة
            self.setWindowTitle(f"Onyx ERP - فواتير المبيعات - {self.session_data.get('company', '')}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح وحدة فواتير المبيعات:\n{str(e)}")

    def show_payments_management(self):
        """عرض وحدة إدارة المدفوعات"""
        try:
            from ..modules.payments_module import PaymentsWidget

            # إنشاء وحدة إدارة المدفوعات
            payments_widget = PaymentsWidget(self.db_manager)

            # إضافة الوحدة إلى المكدس
            self.stacked_widget.addWidget(payments_widget)
            self.stacked_widget.setCurrentWidget(payments_widget)

            # تحديث عنوان النافذة
            self.setWindowTitle(f"Onyx ERP - إدارة المدفوعات - {self.session_data.get('company', '')}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح وحدة إدارة المدفوعات:\n{str(e)}")

    def show_purchases_invoices(self):
        """عرض وحدة فواتير المشتريات"""
        QMessageBox.information(self, "فواتير المشتريات", "وحدة فواتير المشتريات قيد التطوير")

    def get_dashboard_stats(self):
        """الحصول على إحصائيات لوحة التحكم"""
        try:
            # عدد العملاء
            customers_query = "SELECT COUNT(*) as count FROM customers WHERE is_active = 1"
            customers_result = self.db_manager.execute_query(customers_query)
            customers_count = customers_result[0]['count'] if customers_result else 0

            # عدد المنتجات
            products_query = "SELECT COUNT(*) as count FROM products WHERE is_active = 1"
            products_result = self.db_manager.execute_query(products_query)
            products_count = products_result[0]['count'] if products_result else 0

            # عدد الفواتير
            invoices_query = "SELECT COUNT(*) as count FROM sales_invoices"
            invoices_result = self.db_manager.execute_query(invoices_query)
            invoices_count = invoices_result[0]['count'] if invoices_result else 0

            # إجمالي المبيعات
            sales_query = "SELECT COALESCE(SUM(total_amount), 0) as total FROM sales_invoices WHERE status != 'cancelled'"
            sales_result = self.db_manager.execute_query(sales_query)
            total_sales = sales_result[0]['total'] if sales_result else 0

            return customers_count, products_count, invoices_count, total_sales

        except Exception as e:
            print(f"خطأ في الحصول على الإحصائيات: {e}")
            return 0, 0, 0, 0

    def create_stats_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائيات"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
            }}
            QLabel {{
                color: {color};
                font-weight: bold;
            }}
        """)

        layout = QVBoxLayout()

        # الأيقونة والقيمة
        header_layout = QHBoxLayout()
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"font-size: 24px; color: {color};")

        value_label = QLabel(value)
        value_label.setStyleSheet(f"font-size: 20px; font-weight: bold; color: {color};")
        value_label.setAlignment(Qt.AlignRight)

        header_layout.addWidget(icon_label)
        header_layout.addStretch()
        header_layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet(f"font-size: 12px; color: {color}; font-weight: normal;")
        title_label.setAlignment(Qt.AlignCenter)

        layout.addLayout(header_layout)
        layout.addWidget(title_label)

        card.setLayout(layout)
        return card

    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(self, 'تأكيد تسجيل الخروج', 
                                   'هل تريد تسجيل الخروج والعودة لشاشة تسجيل الدخول؟',
                                   QMessageBox.Yes | QMessageBox.No, 
                                   QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            # تعيين علامة أن المستخدم سجل خروج بإرادته
            self.user_logged_out = True
            # إخفاء النافذة أولاً لتجنب رسالة closeEvent
            self.hide()
            self.logout_requested.emit()
    
    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        # تجنب الاستدعاء المتكرر
        if hasattr(self, '_closing') and self._closing:
            event.accept()
            return
        
        # إذا كان المستخدم سجل خروج بإرادته، لا نسأل مرة أخرى
        if self.user_logged_out:
            if self.db_manager:
                self.db_manager.disconnect()
            event.accept()
            return
        
        # إذا كانت النافذة مخفية، فهذا يعني أن المستخدم سجل خروج بالفعل
        if not self.isVisible():
            if self.db_manager:
                self.db_manager.disconnect()
            event.accept()
            return
        
        # إذا لم يسجل خروج، نسأل عن الخروج من النظام
        self._closing = True
        try:
            reply = QMessageBox.question(self, 'تأكيد الخروج', 
                                       'هل تريد الخروج من نظام Onyx ERP؟',
                                       QMessageBox.Yes | QMessageBox.No, 
                                       QMessageBox.No)
            
            if reply == QMessageBox.Yes:
                if self.db_manager:
                    self.db_manager.disconnect()
                event.accept()
            else:
                event.ignore()
        finally:
            self._closing = False