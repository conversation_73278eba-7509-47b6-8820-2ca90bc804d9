#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة تسجيل الدخول لنظام أونكس ERP
Onyx ERP Login Window
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, 
                             QLabel, QLineEdit, QPushButton, QComboBox, 
                             QMessageBox, QFrame, QSpacerItem, QSizePolicy,
                             QGridLayout, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPalette, QIcon

from ..database.sqlite_manager import SQLiteManager
from ..utils.translations import translator
from .onyx_main_window import OnyxMainWindow

class OnyxLoginWindow(QWidget):
    """واجهة تسجيل الدخول لنظام أونكس ERP"""
    
    login_successful = pyqtSignal(dict)  # إشارة نجاح تسجيل الدخول
    
    def __init__(self):
        super().__init__()
        self.db_manager = SQLiteManager()
        self.main_window = None
        self.setup_ui()
        self.setup_connections()
        self.load_default_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("Onyx ERP - تسجيل الدخول")
        self.setFixedSize(500, 650)
        self.setStyleSheet(self.get_stylesheet())
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # إنشاء المكونات
        self.create_header()
        self.create_form()
        self.create_buttons()
        self.create_footer()
        
        # إضافة المكونات للتخطيط الرئيسي
        main_layout.addWidget(self.header_frame)
        main_layout.addWidget(self.form_frame)
        main_layout.addWidget(self.buttons_frame)
        main_layout.addWidget(self.footer_frame)
        
        self.setLayout(main_layout)
        
        # تعيين التركيز الافتراضي
        self.password_edit.setFocus()
    
    def create_header(self):
        """إنشاء رأس الصفحة مع الشعار"""
        self.header_frame = QFrame()
        self.header_frame.setFixedHeight(100)
        self.header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                border: none;
            }
        """)
        
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(20, 20, 20, 20)
        
        # شعار النظام
        logo_label = QLabel("Onyx ERP")
        logo_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 32px;
                font-weight: bold;
                font-family: 'Arial Black';
            }
        """)
        
        # نص فرعي
        subtitle_label = QLabel("نظام تخطيط موارد المؤسسات")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #bdc3c7;
                font-size: 14px;
                font-weight: normal;
            }
        """)
        
        # تخطيط الشعار
        logo_layout = QVBoxLayout()
        logo_layout.addWidget(logo_label)
        logo_layout.addWidget(subtitle_label)
        logo_layout.setSpacing(5)
        
        header_layout.addLayout(logo_layout)
        header_layout.addStretch()
        
        self.header_frame.setLayout(header_layout)
    
    def create_form(self):
        """إنشاء نموذج تسجيل الدخول"""
        self.form_frame = QFrame()
        self.form_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: none;
            }
        """)
        
        form_layout = QVBoxLayout()
        form_layout.setContentsMargins(40, 30, 40, 30)
        form_layout.setSpacing(20)
        
        # عنوان النموذج
        title_label = QLabel("تسجيل الدخول إلى النظام")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        form_layout.addWidget(title_label)
        
        # إنشاء الحقول
        fields_layout = QGridLayout()
        fields_layout.setSpacing(15)
        fields_layout.setColumnStretch(1, 1)
        
        # السنة المالية
        fields_layout.addWidget(QLabel("السنة المالية:"), 0, 0)
        self.year_combo = QComboBox()
        self.year_combo.addItems(["2024", "2025", "2026"])
        self.year_combo.setCurrentText("2025")
        fields_layout.addWidget(self.year_combo, 0, 1)
        
        # اللغة
        fields_layout.addWidget(QLabel("اللغة:"), 1, 0)
        self.language_combo = QComboBox()
        self.language_combo.addItem("عربي", "ar")
        self.language_combo.addItem("English", "en")
        fields_layout.addWidget(self.language_combo, 1, 1)
        
        # الوحدة المحاسبية
        fields_layout.addWidget(QLabel("الوحدة المحاسبية:"), 2, 0)
        self.accounting_unit_combo = QComboBox()
        self.accounting_unit_combo.addItems(["1", "2", "3"])
        fields_layout.addWidget(self.accounting_unit_combo, 2, 1)
        
        # المستخدم
        fields_layout.addWidget(QLabel("المستخدم:"), 3, 0)
        self.user_combo = QComboBox()
        self.user_combo.addItems(["1 - admin", "2 - محاسب", "3 - مستخدم"])
        fields_layout.addWidget(self.user_combo, 3, 1)
        
        # كلمة المرور
        fields_layout.addWidget(QLabel("كلمة المرور:"), 4, 0)
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        fields_layout.addWidget(self.password_edit, 4, 1)
        
        # الشركة
        fields_layout.addWidget(QLabel("الشركة:"), 5, 0)
        self.company_combo = QComboBox()
        self.company_combo.addItems([
            "صقالات ابو سيف للنقل",
            "شركة المحاسبة المتقدمة",
            "مؤسسة التجارة الحديثة"
        ])
        fields_layout.addWidget(self.company_combo, 5, 1)
        
        # رقم الفرع
        fields_layout.addWidget(QLabel("رقم الفرع:"), 6, 0)
        self.branch_combo = QComboBox()
        self.branch_combo.addItems(["1 - الفرع الرئيسي", "2 - فرع الشمال", "3 - فرع الجنوب"])
        fields_layout.addWidget(self.branch_combo, 6, 1)
        
        # المركز الرئيسي
        fields_layout.addWidget(QLabel("المركز الرئيسي:"), 7, 0)
        self.main_center_combo = QComboBox()
        self.main_center_combo.addItems(["1 - المركز الرئيسي", "2 - مركز فرعي"])
        fields_layout.addWidget(self.main_center_combo, 7, 1)
        
        form_layout.addLayout(fields_layout)
        self.form_frame.setLayout(form_layout)
    
    def create_buttons(self):
        """إنشاء الأزرار"""
        self.buttons_frame = QFrame()
        self.buttons_frame.setFixedHeight(80)
        self.buttons_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
            }
        """)
        
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(40, 20, 40, 20)
        
        # زر موافقة
        self.ok_button = QPushButton("موافقة")
        self.ok_button.setFixedSize(120, 40)
        self.ok_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        
        # زر خروج
        self.exit_button = QPushButton("خروج")
        self.exit_button.setFixedSize(120, 40)
        self.exit_button.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.ok_button)
        buttons_layout.addSpacing(20)
        buttons_layout.addWidget(self.exit_button)
        buttons_layout.addStretch()
        
        self.buttons_frame.setLayout(buttons_layout)
    
    def create_footer(self):
        """إنشاء تذييل الصفحة"""
        self.footer_frame = QFrame()
        self.footer_frame.setFixedHeight(80)
        self.footer_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #34495e, stop:1 #2c3e50);
                border: none;
            }
        """)
        
        footer_layout = QVBoxLayout()
        footer_layout.setContentsMargins(20, 15, 20, 15)
        footer_layout.setSpacing(5)
        
        # النص الرئيسي
        main_text = QLabel("Enterprise Resource Planning Solutions")
        main_text.setAlignment(Qt.AlignCenter)
        main_text.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        
        # النص الفرعي
        sub_text = QLabel("Absolute Trust")
        sub_text.setAlignment(Qt.AlignCenter)
        sub_text.setStyleSheet("""
            QLabel {
                color: #bdc3c7;
                font-size: 12px;
                font-style: italic;
            }
        """)
        
        footer_layout.addWidget(main_text)
        footer_layout.addWidget(sub_text)
        
        self.footer_frame.setLayout(footer_layout)
    
    def get_stylesheet(self):
        """الحصول على تنسيق CSS للنافذة"""
        return """
            QWidget {
                font-family: 'Segoe UI', Arial, sans-serif;
                font-size: 12px;
            }
            
            QLabel {
                color: #2c3e50;
                font-weight: 500;
            }
            
            QComboBox {
                padding: 8px 12px;
                border: 2px solid #e9ecef;
                border-radius: 5px;
                background-color: white;
                min-height: 20px;
            }
            
            QComboBox:focus {
                border-color: #007bff;
                outline: none;
            }
            
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
                margin-right: 5px;
            }
            
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #e9ecef;
                border-radius: 5px;
                background-color: white;
                min-height: 20px;
            }
            
            QLineEdit:focus {
                border-color: #007bff;
                outline: none;
            }
            
            QLineEdit::placeholder {
                color: #6c757d;
            }
        """
    
    def setup_connections(self):
        """ربط الإشارات بالوظائف"""
        self.ok_button.clicked.connect(self.handle_login)
        self.exit_button.clicked.connect(self.close)
        self.password_edit.returnPressed.connect(self.handle_login)
        self.language_combo.currentTextChanged.connect(self.change_language)
    
    def load_default_data(self):
        """تحميل البيانات الافتراضية"""
        # تعيين القيم الافتراضية
        self.year_combo.setCurrentText("2025")
        self.language_combo.setCurrentIndex(0)  # عربي
        self.accounting_unit_combo.setCurrentText("1")
        self.user_combo.setCurrentText("1 - admin")
        self.company_combo.setCurrentText("صقالات ابو سيف للنقل")
        self.branch_combo.setCurrentText("1 - الفرع الرئيسي")
        self.main_center_combo.setCurrentText("1 - المركز الرئيسي")
    
    def change_language(self, text):
        """تغيير اللغة"""
        if "English" in text:
            self.update_ui_to_english()
        else:
            self.update_ui_to_arabic()
    
    def update_ui_to_english(self):
        """تحديث الواجهة للإنجليزية"""
        self.setLayoutDirection(Qt.LeftToRight)
        # يمكن إضافة المزيد من التحديثات هنا
    
    def update_ui_to_arabic(self):
        """تحديث الواجهة للعربية"""
        self.setLayoutDirection(Qt.RightToLeft)
        # يمكن إضافة المزيد من التحديثات هنا
    
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        # الحصول على البيانات المدخلة
        password = self.password_edit.text()
        user_selection = self.user_combo.currentText()
        year = self.year_combo.currentText()
        language = self.language_combo.currentData() or "ar"
        accounting_unit = self.accounting_unit_combo.currentText()
        company = self.company_combo.currentText()
        branch = self.branch_combo.currentText()
        main_center = self.main_center_combo.currentText()
        
        # التحقق من كلمة المرور
        if not password:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كلمة المرور")
            self.password_edit.setFocus()
            return
        
        # استخراج اسم المستخدم من الاختيار
        username = "admin"  # افتراضي
        if " - " in user_selection:
            username = user_selection.split(" - ")[1]
        
        # التحقق من صحة البيانات
        user = self.db_manager.authenticate_user(username, password)
        
        if user:
            # إنشاء بيانات الجلسة
            session_data = {
                **user,
                'year': year,
                'language': language,
                'accounting_unit': accounting_unit,
                'company': company,
                'branch': branch,
                'main_center': main_center
            }
            
            # عرض رسالة نجاح
            welcome_msg = f"""
مرحباً {user['full_name']}

تم تسجيل الدخول بنجاح إلى نظام Onyx ERP

تفاصيل الجلسة:
• السنة المالية: {year}
• الشركة: {company}
• الفرع: {branch}
• المركز الرئيسي: {main_center}
• الوحدة المحاسبية: {accounting_unit}
            """
            
            QMessageBox.information(self, "تم تسجيل الدخول بنجاح", welcome_msg)
            
            # تعيين علامة نجاح تسجيل الدخول
            self._login_successful = True

            # فتح النافذة الرئيسية
            self.open_main_window(session_data)
            
        else:
            QMessageBox.critical(self, "خطأ في تسجيل الدخول", 
                               "كلمة المرور غير صحيحة\n\nكلمة المرور الافتراضية: admin123")
            self.password_edit.clear()
            self.password_edit.setFocus()
    
    def open_main_window(self, session_data):
        """فتح النافذة الرئيسية"""
        try:
            # التأكد من عدم وجود نافذة رئيسية مفتوحة بالفعل
            if self.main_window:
                return
            
            # إنشاء النافذة الرئيسية
            self.main_window = OnyxMainWindow(session_data, self.db_manager)
            
            # ربط إشارة تسجيل الخروج
            self.main_window.logout_requested.connect(self.show_login_again)
            
            # إظهار النافذة الرئيسية وإخفاء نافذة تسجيل الدخول
            self.main_window.show()
            self.hide()
            
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            print(f"خطأ في فتح النافذة الرئيسية: {e}")
            print(f"تفاصيل الخطأ: {error_details}")
            
            QMessageBox.critical(self, "خطأ", 
                               f"فشل في فتح النافذة الرئيسية:\n{str(e)}\n\nيرجى المحاولة مرة أخرى")
            
            # إعادة تعيين النافذة الرئيسية
            self.main_window = None
    
    def show_login_again(self):
        """إظهار نافذة تسجيل الدخول مرة أخرى"""
        # تجنب الاستدعاء المتكرر
        if hasattr(self, '_showing_login') and self._showing_login:
            return
        
        self._showing_login = True
        
        try:
            if self.main_window:
                # تأكد من تعيين علامة تسجيل الخروج قبل الإغلاق
                self.main_window.user_logged_out = True
                
                # قطع الاتصال بالإشارات لتجنب الحلقات اللا نهائية
                try:
                    self.main_window.logout_requested.disconnect()
                except:
                    pass
                
                # إخفاء النافذة الرئيسية وإتلافها
                self.main_window.hide()
                self.main_window.close()
                self.main_window.deleteLater()
                self.main_window = None
            
            # إعادة تعيين علامة نجاح تسجيل الدخول
            if hasattr(self, '_login_successful'):
                delattr(self, '_login_successful')

            # مسح كلمة المرور وإظهار نافذة تسجيل الدخول
            self.password_edit.clear()
            self.password_edit.setFocus()
            
            # التأكد من أن النافذة ليست مرئية قبل إظهارها
            if not self.isVisible():
                self.show()
                self.raise_()
                self.activateWindow()
                
        finally:
            self._showing_login = False
    
    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        # تجنب الاستدعاء المتكرر
        if hasattr(self, '_closing') and self._closing:
            event.accept()
            return

        # إذا كانت النافذة الرئيسية مفتوحة ومرئية، لا نتدخل
        if self.main_window and self.main_window.isVisible():
            event.accept()
            return

        # إذا كان المستخدم سجل خروج من النافذة الرئيسية، لا نسأل مرة أخرى
        if self.main_window and hasattr(self.main_window, 'user_logged_out') and self.main_window.user_logged_out:
            # تنظيف النافذة الرئيسية
            self.cleanup_main_window()
            if self.db_manager:
                self.db_manager.disconnect()
            event.accept()
            return

        # إذا لم تعد النافذة الرئيسية موجودة (تم إتلافها)، اقبل الإغلاق مباشرة
        if not self.main_window:
            if self.db_manager:
                self.db_manager.disconnect()
            event.accept()
            return

        # إذا كانت نافذة تسجيل الدخول مرئية فقط ولم يتم تسجيل دخول بعد، نسأل عن الخروج
        if self.isVisible() and not (self.main_window and self.main_window.isVisible()) and not hasattr(self, '_login_successful'):
            self._closing = True
            try:
                reply = QMessageBox.question(self, 'تأكيد الخروج',
                                           'هل تريد الخروج من نظام Onyx ERP؟',
                                           QMessageBox.Yes | QMessageBox.No,
                                           QMessageBox.No)

                if reply == QMessageBox.Yes:
                    self.cleanup_main_window()
                    if self.db_manager:
                        self.db_manager.disconnect()
                    event.accept()
                else:
                    event.ignore()
            finally:
                self._closing = False
        else:
            # في جميع الحالات الأخرى، اقبل الإغلاق مباشرة
            self.cleanup_main_window()
            if self.db_manager:
                self.db_manager.disconnect()
            event.accept()
    
    def cleanup_main_window(self):
        """تنظيف النافذة الرئيسية"""
        if self.main_window:
            try:
                self.main_window.logout_requested.disconnect()
            except:
                pass
            
            if not self.main_window.isHidden():
                self.main_window.hide()
            
            self.main_window.close()
            self.main_window.deleteLater()
            self.main_window = None