#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص وجود أزرار الفواتير في وحدة العملاء
Check Invoice Buttons in Customers Module
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        from PyQt5.QtWidgets import QApplication
        from src.modules.customers_suppliers_module import CustomersSupplierModule
        from src.database.sqlite_manager import SQLiteManager

        print("🔍 فحص أزرار الفواتير في وحدة العملاء...")
        
        # إنشاء تطبيق مؤقت
        app = QApplication([])
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # إنشاء وحدة العملاء
        module = CustomersSupplierModule(db_manager)
        
        print("\n📋 الأزرار الموجودة:")
        
        # فحص جميع الأزرار
        buttons_to_check = [
            ('add_btn', 'إضافة'),
            ('edit_btn', 'تعديل'), 
            ('delete_btn', 'حذف'),
            ('refresh_btn', 'تحديث'),
            ('receipt_voucher_btn', 'سند قبض'),
            ('payment_voucher_btn', 'سند صرف'),
            ('sales_invoice_btn', 'فاتورة مبيعات'),
            ('sales_return_btn', 'مردود مبيعات')
        ]
        
        found_buttons = 0
        total_buttons = len(buttons_to_check)
        
        for button_attr, button_desc in buttons_to_check:
            if hasattr(module, button_attr):
                button = getattr(module, button_attr)
                print(f"✅ {button_desc}: {button.text()}")
                found_buttons += 1
            else:
                print(f"❌ {button_desc}: غير موجود")
        
        print(f"\n📊 النتيجة: {found_buttons}/{total_buttons} زر موجود")
        
        if found_buttons == total_buttons:
            print("🎉 جميع الأزرار موجودة!")
        else:
            print("⚠️ بعض الأزرار مفقودة")
            
        # فحص الدوال
        print("\n🔧 فحص الدوال:")
        functions_to_check = [
            ('open_sales_invoice', 'فتح فاتورة مبيعات'),
            ('open_sales_return', 'فتح مردود مبيعات')
        ]
        
        for func_attr, func_desc in functions_to_check:
            if hasattr(module, func_attr):
                print(f"✅ {func_desc}: موجودة")
            else:
                print(f"❌ {func_desc}: غير موجودة")
        
        return found_buttons == total_buttons
        
    except Exception as e:
        print(f"❌ خطأ في الفحص: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
