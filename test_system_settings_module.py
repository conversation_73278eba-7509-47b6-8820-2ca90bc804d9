#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوحدة إعدادات النظام
Comprehensive test for system settings module
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_system_settings_module():
    """اختبار وحدة إعدادات النظام"""
    print("🧪 اختبار وحدة إعدادات النظام...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.system_settings_module import SystemSettingsWidget, SystemSettingsDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار إنشاء وحدة إعدادات النظام
        settings_widget = SystemSettingsWidget(db_manager)
        print("✅ تم إنشاء وحدة إعدادات النظام")
        
        # اختبار وجود التبويبات
        tab_count = settings_widget.tab_widget.count()
        print(f"✅ عدد التبويبات: {tab_count}")
        
        # اختبار تحميل الإعدادات
        settings_widget.load_settings()
        print("✅ تم تحميل الإعدادات من قاعدة البيانات")
        
        # اختبار البحث والفلترة
        settings_widget.search_edit.setText("company")
        settings_widget.filter_settings()
        print("✅ تم اختبار وظيفة البحث")
        
        # اختبار مسح البحث
        settings_widget.search_edit.clear()
        settings_widget.filter_settings()
        print("✅ تم اختبار مسح البحث")
        
        # تنظيف
        settings_widget.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وحدة إعدادات النظام: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_settings_dialog():
    """اختبار حوار تعديل الإعدادات"""
    print("\n🧪 اختبار حوار تعديل الإعدادات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.system_settings_module import SystemSettingsDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # الحصول على إعداد للاختبار
        query = "SELECT * FROM system_settings LIMIT 1"
        result = db_manager.execute_query(query)
        
        if result:
            setting_data = result[0]
            
            # اختبار حوار تعديل الإعداد
            dialog = SystemSettingsDialog(db_manager, setting_data)
            print("✅ تم إنشاء حوار تعديل الإعداد")
            
            # اختبار تحميل البيانات
            dialog.load_data()
            print("✅ تم تحميل بيانات الإعداد في الحوار")
            
            print(f"   🔑 مفتاح الإعداد: {setting_data.get('setting_key')}")
            print(f"   📝 الوصف: {setting_data.get('description')}")
            print(f"   💾 القيمة: {setting_data.get('setting_value')}")
            print(f"   🏷️ النوع: {setting_data.get('setting_type')}")
            
            # تنظيف
            dialog.close()
        else:
            print("⚠️ لا توجد إعدادات في قاعدة البيانات للاختبار")
        
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حوار تعديل الإعدادات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_settings_database():
    """اختبار عمليات قاعدة البيانات للإعدادات"""
    print("\n🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار استعلام الإعدادات
        query = """
            SELECT id, setting_key, setting_value, setting_type, description, category, is_editable
            FROM system_settings 
            ORDER BY category, setting_key
        """
        
        settings = db_manager.execute_query(query)
        print(f"✅ تم استعلام {len(settings)} إعداد من قاعدة البيانات")
        
        # اختبار تجميع الإعدادات حسب الفئة
        categories = {}
        for setting in settings:
            category = setting.get('category', 'general')
            if category not in categories:
                categories[category] = 0
            categories[category] += 1
        
        print(f"📊 توزيع الإعدادات حسب الفئة:")
        category_names = {
            'company': 'الشركة',
            'accounting': 'المحاسبة',
            'system': 'النظام',
            'security': 'الأمان',
            'ui': 'الواجهة'
        }
        
        for category, count in categories.items():
            category_name = category_names.get(category, category)
            print(f"   {category_name}: {count}")
        
        # اختبار الإعدادات القابلة للتعديل
        editable_query = """
            SELECT COUNT(*) as count FROM system_settings WHERE is_editable = 1
        """
        editable_result = db_manager.execute_query(editable_query)
        editable_count = editable_result[0]['count'] if editable_result else 0
        print(f"✏️ الإعدادات القابلة للتعديل: {editable_count}")
        
        # اختبار البحث في الإعدادات
        search_query = """
            SELECT COUNT(*) as count FROM system_settings 
            WHERE setting_key LIKE ? OR description LIKE ?
        """
        search_result = db_manager.execute_query(search_query, ('%company%', '%شركة%'))
        search_count = search_result[0]['count'] if search_result else 0
        print(f"🔍 نتائج البحث عن 'company/شركة': {search_count}")
        
        # اختبار أنواع البيانات
        types_query = """
            SELECT setting_type, COUNT(*) as count FROM system_settings 
            GROUP BY setting_type
            ORDER BY count DESC
        """
        types_result = db_manager.execute_query(types_query)
        print(f"📋 توزيع أنواع البيانات:")
        type_names = {
            'string': 'نص',
            'integer': 'رقم صحيح',
            'boolean': 'منطقي',
            'float': 'رقم عشري'
        }
        
        for type_data in types_result:
            type_name = type_names.get(type_data['setting_type'], type_data['setting_type'])
            print(f"   {type_name}: {type_data['count']}")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_structure():
    """اختبار هيكل قاعدة البيانات للإعدادات"""
    print("\n🧪 اختبار هيكل قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار وجود جدول system_settings
        table_query = "SELECT name FROM sqlite_master WHERE type='table' AND name='system_settings'"
        table_result = db_manager.execute_query(table_query)
        
        if table_result:
            print("✅ جدول system_settings موجود")
        else:
            print("❌ جدول system_settings مفقود")
            return False
        
        # اختبار أعمدة الجدول
        columns_query = "PRAGMA table_info(system_settings)"
        columns = db_manager.execute_query(columns_query)
        column_names = [col['name'] for col in columns]
        
        required_columns = [
            'id', 'setting_key', 'setting_value', 'setting_type', 'description',
            'category', 'is_editable', 'created_at', 'updated_at'
        ]
        
        print("\n📋 أعمدة جدول system_settings:")
        for col in required_columns:
            if col in column_names:
                print(f"   ✅ {col}")
            else:
                print(f"   ❌ {col} مفقود")
        
        # عرض جميع الأعمدة الموجودة
        print(f"\n📊 إجمالي الأعمدة الموجودة: {len(column_names)}")
        print(f"الأعمدة: {', '.join(column_names)}")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار هيكل قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار شامل لوحدة إعدادات النظام")
    print("   Comprehensive test for system settings module")
    print("=" * 60)
    
    # اختبار هيكل قاعدة البيانات
    structure_test = test_database_structure()
    
    # اختبار وحدة إعدادات النظام
    module_test = test_system_settings_module()
    
    # اختبار حوار تعديل الإعدادات
    dialog_test = test_system_settings_dialog()
    
    # اختبار عمليات قاعدة البيانات
    database_test = test_system_settings_database()
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار وحدة إعدادات النظام:")
    print(f"   🗄️ هيكل قاعدة البيانات: {'✅ نجح' if structure_test else '❌ فشل'}")
    print(f"   🏗️ اختبار الوحدة: {'✅ نجح' if module_test else '❌ فشل'}")
    print(f"   💬 اختبار الحوار: {'✅ نجح' if dialog_test else '❌ فشل'}")
    print(f"   🗄️ اختبار قاعدة البيانات: {'✅ نجح' if database_test else '❌ فشل'}")
    
    if structure_test and module_test and dialog_test and database_test:
        print("\n🎉 جميع اختبارات وحدة إعدادات النظام نجحت!")
        print("✅ وحدة إعدادات النظام جاهزة للاستخدام")
        
        print("\n📋 الوظائف المتاحة:")
        print("   ⚙️ إدارة إعدادات الشركة")
        print("   💰 إدارة إعدادات المحاسبة")
        print("   🔧 إدارة إعدادات النظام")
        print("   🔒 إدارة إعدادات الأمان")
        print("   🎨 إدارة إعدادات الواجهة")
        print("   🔍 البحث والفلترة في الإعدادات")
        print("   📊 عرض الإعدادات مجمعة حسب الفئة")
        print("   ✏️ تعديل الإعدادات القابلة للتعديل")
        print("   🔄 استعادة الإعدادات الافتراضية")
    else:
        print("\n⚠️ بعض اختبارات وحدة إعدادات النظام فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)
    
    return 0 if (structure_test and module_test and dialog_test and database_test) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
