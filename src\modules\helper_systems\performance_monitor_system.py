#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام مراقب الأداء
Performance Monitor System
"""

import sys
import time
from datetime import datetime, timedelta
from collections import deque
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QPushButton, QGroupBox, 
                             QTextEdit, QTabWidget, QWidget, QComboBox,
                             QApplication, QMessageBox, QListWidget,
                             QListWidgetItem, QCheckBox, QSpinBox,
                             QProgressBar, QTableWidget, QTableWidgetItem,
                             QHeaderView, QSplitter, QFrame, QScrollArea)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QIcon, QPainter, QPen
import pyqtgraph as pg

class PerformanceDataCollector(QThread):
    """جامع بيانات الأداء"""
    data_updated = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.is_running = True
        self.data_history = {
            'cpu': deque(maxlen=100),
            'memory': deque(maxlen=100),
            'disk': deque(maxlen=100),
            'network_in': deque(maxlen=100),
            'network_out': deque(maxlen=100),
            'timestamps': deque(maxlen=100)
        }
        
    def run(self):
        """تشغيل جمع البيانات"""
        while self.is_running:
            try:
                import psutil
                
                # جمع بيانات الأداء
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/' if sys.platform != 'win32' else 'C:')
                network = psutil.net_io_counters()
                
                # حساب سرعة الشبكة
                current_time = time.time()
                
                performance_data = {
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_used': memory.used / (1024**3),  # GB
                    'memory_total': memory.total / (1024**3),  # GB
                    'disk_percent': (disk.used / disk.total) * 100,
                    'disk_used': disk.used / (1024**3),  # GB
                    'disk_total': disk.total / (1024**3),  # GB
                    'network_bytes_sent': network.bytes_sent,
                    'network_bytes_recv': network.bytes_recv,
                    'processes_count': len(psutil.pids()),
                    'timestamp': current_time
                }
                
                # إضافة للتاريخ
                self.data_history['cpu'].append(cpu_percent)
                self.data_history['memory'].append(memory.percent)
                self.data_history['disk'].append((disk.used / disk.total) * 100)
                self.data_history['timestamps'].append(current_time)
                
                # حساب سرعة الشبكة
                if len(self.data_history['network_in']) > 0:
                    prev_recv = self.data_history['network_in'][-1] if self.data_history['network_in'] else 0
                    prev_sent = self.data_history['network_out'][-1] if self.data_history['network_out'] else 0
                    
                    recv_speed = max(0, (network.bytes_recv - prev_recv) / 1024)  # KB/s
                    sent_speed = max(0, (network.bytes_sent - prev_sent) / 1024)  # KB/s
                else:
                    recv_speed = sent_speed = 0
                
                self.data_history['network_in'].append(network.bytes_recv)
                self.data_history['network_out'].append(network.bytes_sent)
                
                performance_data['network_speed_in'] = recv_speed
                performance_data['network_speed_out'] = sent_speed
                performance_data['history'] = dict(self.data_history)
                
                self.data_updated.emit(performance_data)
                
            except Exception as e:
                print(f"خطأ في جمع بيانات الأداء: {e}")
                self.msleep(5000)
                
    def stop(self):
        """إيقاف جمع البيانات"""
        self.is_running = False

class PerformanceMonitorDialog(QDialog):
    """حوار مراقب الأداء"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_collector = None
        self.performance_data = {}
        self.alerts_enabled = True
        self.alert_thresholds = {
            'cpu': 80,
            'memory': 85,
            'disk': 90
        }
        self.setup_ui()
        self.start_monitoring()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📈 مراقب الأداء المتقدم")
        self.setGeometry(200, 200, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("📈 مراقب الأداء المتقدم - مراقبة شاملة لأداء النظام")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات النظام
        self.tabs = QTabWidget()
        
        # تبويب المراقبة المباشرة
        live_monitor_tab = self.create_live_monitor_tab()
        self.tabs.addTab(live_monitor_tab, "📊 المراقبة المباشرة")
        
        # تبويب الرسوم البيانية
        charts_tab = self.create_charts_tab()
        self.tabs.addTab(charts_tab, "📈 الرسوم البيانية")
        
        # تبويب التنبيهات
        alerts_tab = self.create_alerts_tab()
        self.tabs.addTab(alerts_tab, "🚨 التنبيهات")
        
        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        self.tabs.addTab(reports_tab, "📋 التقارير")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_live_monitor_tab(self):
        """إنشاء تبويب المراقبة المباشرة"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة المؤشرات الرئيسية
        main_indicators_group = QGroupBox("📊 المؤشرات الرئيسية")
        main_indicators_layout = QGridLayout()
        
        # مؤشر المعالج
        cpu_frame = QFrame()
        cpu_frame.setFrameStyle(QFrame.Box)
        cpu_frame.setStyleSheet("""
            QFrame {
                background-color: #e3f2fd;
                border: 2px solid #2196f3;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        cpu_layout = QVBoxLayout()
        
        cpu_title = QLabel("🖥️ المعالج")
        cpu_title.setAlignment(Qt.AlignCenter)
        cpu_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #1976d2;")
        
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #2196f3;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #2196f3;
                border-radius: 3px;
            }
        """)
        
        self.cpu_label = QLabel("0%")
        self.cpu_label.setAlignment(Qt.AlignCenter)
        self.cpu_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #1976d2;")
        
        cpu_layout.addWidget(cpu_title)
        cpu_layout.addWidget(self.cpu_progress)
        cpu_layout.addWidget(self.cpu_label)
        cpu_frame.setLayout(cpu_layout)
        
        # مؤشر الذاكرة
        memory_frame = QFrame()
        memory_frame.setFrameStyle(QFrame.Box)
        memory_frame.setStyleSheet("""
            QFrame {
                background-color: #f3e5f5;
                border: 2px solid #9c27b0;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        memory_layout = QVBoxLayout()
        
        memory_title = QLabel("💾 الذاكرة")
        memory_title.setAlignment(Qt.AlignCenter)
        memory_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #7b1fa2;")
        
        self.memory_progress = QProgressBar()
        self.memory_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #9c27b0;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #9c27b0;
                border-radius: 3px;
            }
        """)
        
        self.memory_label = QLabel("0%")
        self.memory_label.setAlignment(Qt.AlignCenter)
        self.memory_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #7b1fa2;")
        
        memory_layout.addWidget(memory_title)
        memory_layout.addWidget(self.memory_progress)
        memory_layout.addWidget(self.memory_label)
        memory_frame.setLayout(memory_layout)
        
        # مؤشر القرص الصلب
        disk_frame = QFrame()
        disk_frame.setFrameStyle(QFrame.Box)
        disk_frame.setStyleSheet("""
            QFrame {
                background-color: #fff3e0;
                border: 2px solid #ff9800;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        disk_layout = QVBoxLayout()
        
        disk_title = QLabel("💿 القرص الصلب")
        disk_title.setAlignment(Qt.AlignCenter)
        disk_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #f57c00;")
        
        self.disk_progress = QProgressBar()
        self.disk_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #ff9800;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #ff9800;
                border-radius: 3px;
            }
        """)
        
        self.disk_label = QLabel("0%")
        self.disk_label.setAlignment(Qt.AlignCenter)
        self.disk_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #f57c00;")
        
        disk_layout.addWidget(disk_title)
        disk_layout.addWidget(self.disk_progress)
        disk_layout.addWidget(self.disk_label)
        disk_frame.setLayout(disk_layout)
        
        # مؤشر الشبكة
        network_frame = QFrame()
        network_frame.setFrameStyle(QFrame.Box)
        network_frame.setStyleSheet("""
            QFrame {
                background-color: #e8f5e8;
                border: 2px solid #4caf50;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        network_layout = QVBoxLayout()
        
        network_title = QLabel("🌐 الشبكة")
        network_title.setAlignment(Qt.AlignCenter)
        network_title.setStyleSheet("font-weight: bold; font-size: 14px; color: #388e3c;")
        
        self.network_in_label = QLabel("📥 0 KB/s")
        self.network_in_label.setAlignment(Qt.AlignCenter)
        self.network_in_label.setStyleSheet("font-weight: bold; color: #388e3c;")
        
        self.network_out_label = QLabel("📤 0 KB/s")
        self.network_out_label.setAlignment(Qt.AlignCenter)
        self.network_out_label.setStyleSheet("font-weight: bold; color: #388e3c;")
        
        network_layout.addWidget(network_title)
        network_layout.addWidget(self.network_in_label)
        network_layout.addWidget(self.network_out_label)
        network_frame.setLayout(network_layout)
        
        main_indicators_layout.addWidget(cpu_frame, 0, 0)
        main_indicators_layout.addWidget(memory_frame, 0, 1)
        main_indicators_layout.addWidget(disk_frame, 1, 0)
        main_indicators_layout.addWidget(network_frame, 1, 1)
        
        main_indicators_group.setLayout(main_indicators_layout)
        
        # مجموعة المعلومات التفصيلية
        details_group = QGroupBox("📋 معلومات تفصيلية")
        details_layout = QVBoxLayout()
        
        self.details_table = QTableWidget()
        self.details_table.setColumnCount(2)
        self.details_table.setHorizontalHeaderLabels(["المؤشر", "القيمة"])
        self.details_table.horizontalHeader().setStretchLastSection(True)
        self.details_table.setAlternatingRowColors(True)
        self.details_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)
        
        details_layout.addWidget(self.details_table)
        details_group.setLayout(details_layout)
        
        layout.addWidget(main_indicators_group)
        layout.addWidget(details_group)
        
        tab.setLayout(layout)
        return tab
        
    def create_charts_tab(self):
        """إنشاء تبويب الرسوم البيانية"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # شريط أدوات الرسوم البيانية
        charts_toolbar = QHBoxLayout()
        
        # اختيار نوع الرسم البياني
        charts_toolbar.addWidget(QLabel("نوع الرسم البياني:"))
        self.chart_type = QComboBox()
        self.chart_type.addItems(["المعالج", "الذاكرة", "القرص الصلب", "الشبكة"])
        self.chart_type.currentTextChanged.connect(self.update_chart)
        charts_toolbar.addWidget(self.chart_type)
        
        # فترة العرض
        charts_toolbar.addWidget(QLabel("فترة العرض:"))
        self.time_range = QComboBox()
        self.time_range.addItems(["آخر دقيقة", "آخر 5 دقائق", "آخر 15 دقيقة", "آخر ساعة"])
        self.time_range.currentTextChanged.connect(self.update_chart)
        charts_toolbar.addWidget(self.time_range)
        
        charts_toolbar.addStretch()
        
        # منطقة الرسم البياني
        self.chart_widget = pg.PlotWidget()
        self.chart_widget.setBackground('w')
        self.chart_widget.setLabel('left', 'النسبة المئوية (%)')
        self.chart_widget.setLabel('bottom', 'الوقت')
        self.chart_widget.showGrid(x=True, y=True)
        
        layout.addLayout(charts_toolbar)
        layout.addWidget(self.chart_widget)
        
        tab.setLayout(layout)
        return tab

    def create_alerts_tab(self):
        """إنشاء تبويب التنبيهات"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة إعدادات التنبيهات
        alerts_settings_group = QGroupBox("🚨 إعدادات التنبيهات")
        alerts_settings_layout = QGridLayout()

        # تفعيل التنبيهات
        self.alerts_enabled_check = QCheckBox("تفعيل التنبيهات")
        self.alerts_enabled_check.setChecked(self.alerts_enabled)
        self.alerts_enabled_check.toggled.connect(self.toggle_alerts)
        alerts_settings_layout.addWidget(self.alerts_enabled_check, 0, 0, 1, 2)

        # حدود التنبيهات
        alerts_settings_layout.addWidget(QLabel("حد تنبيه المعالج (%):"), 1, 0)
        self.cpu_threshold = QSpinBox()
        self.cpu_threshold.setRange(50, 100)
        self.cpu_threshold.setValue(self.alert_thresholds['cpu'])
        self.cpu_threshold.valueChanged.connect(self.update_thresholds)
        alerts_settings_layout.addWidget(self.cpu_threshold, 1, 1)

        alerts_settings_layout.addWidget(QLabel("حد تنبيه الذاكرة (%):"), 1, 2)
        self.memory_threshold = QSpinBox()
        self.memory_threshold.setRange(50, 100)
        self.memory_threshold.setValue(self.alert_thresholds['memory'])
        self.memory_threshold.valueChanged.connect(self.update_thresholds)
        alerts_settings_layout.addWidget(self.memory_threshold, 1, 3)

        alerts_settings_layout.addWidget(QLabel("حد تنبيه القرص (%):"), 2, 0)
        self.disk_threshold = QSpinBox()
        self.disk_threshold.setRange(50, 100)
        self.disk_threshold.setValue(self.alert_thresholds['disk'])
        self.disk_threshold.valueChanged.connect(self.update_thresholds)
        alerts_settings_layout.addWidget(self.disk_threshold, 2, 1)

        alerts_settings_group.setLayout(alerts_settings_layout)

        # مجموعة سجل التنبيهات
        alerts_log_group = QGroupBox("📋 سجل التنبيهات")
        alerts_log_layout = QVBoxLayout()

        # شريط أدوات السجل
        log_toolbar = QHBoxLayout()

        clear_log_btn = QPushButton("🗑️ مسح السجل")
        clear_log_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        clear_log_btn.clicked.connect(self.clear_alerts_log)

        export_log_btn = QPushButton("📤 تصدير السجل")
        export_log_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        export_log_btn.clicked.connect(self.export_alerts_log)

        log_toolbar.addWidget(clear_log_btn)
        log_toolbar.addWidget(export_log_btn)
        log_toolbar.addStretch()

        # جدول سجل التنبيهات
        self.alerts_table = QTableWidget()
        self.alerts_table.setColumnCount(4)
        self.alerts_table.setHorizontalHeaderLabels([
            "الوقت", "النوع", "القيمة", "الرسالة"
        ])
        self.alerts_table.horizontalHeader().setStretchLastSection(True)
        self.alerts_table.setAlternatingRowColors(True)
        self.alerts_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 11px;
            }
            QTableWidget::item:selected {
                background-color: #dc3545;
                color: white;
            }
            QHeaderView::section {
                background-color: #dc3545;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)

        alerts_log_layout.addLayout(log_toolbar)
        alerts_log_layout.addWidget(self.alerts_table)
        alerts_log_group.setLayout(alerts_log_layout)

        layout.addWidget(alerts_settings_group)
        layout.addWidget(alerts_log_group)

        tab.setLayout(layout)
        return tab

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة إنشاء التقارير
        reports_group = QGroupBox("📋 إنشاء تقارير الأداء")
        reports_layout = QGridLayout()

        # نوع التقرير
        reports_layout.addWidget(QLabel("نوع التقرير:"), 0, 0)
        self.report_type = QComboBox()
        self.report_type.addItems([
            "تقرير شامل", "تقرير المعالج", "تقرير الذاكرة",
            "تقرير القرص", "تقرير الشبكة", "تقرير التنبيهات"
        ])
        reports_layout.addWidget(self.report_type, 0, 1)

        # فترة التقرير
        reports_layout.addWidget(QLabel("فترة التقرير:"), 0, 2)
        self.report_period = QComboBox()
        self.report_period.addItems([
            "آخر ساعة", "آخر 6 ساعات", "آخر 24 ساعة",
            "آخر أسبوع", "آخر شهر"
        ])
        reports_layout.addWidget(self.report_period, 0, 3)

        # أزرار التقارير
        generate_report_btn = QPushButton("📊 إنشاء تقرير")
        generate_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        generate_report_btn.clicked.connect(self.generate_report)
        reports_layout.addWidget(generate_report_btn, 1, 0, 1, 2)

        export_report_btn = QPushButton("📤 تصدير تقرير")
        export_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        export_report_btn.clicked.connect(self.export_report)
        reports_layout.addWidget(export_report_btn, 1, 2, 1, 2)

        reports_group.setLayout(reports_layout)

        # منطقة عرض التقرير
        report_display_group = QGroupBox("📄 عرض التقرير")
        report_display_layout = QVBoxLayout()

        self.report_display = QTextEdit()
        self.report_display.setReadOnly(True)
        self.report_display.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 15px;
                font-family: 'Courier New';
                font-size: 12px;
            }
        """)
        self.report_display.setPlainText("انقر على 'إنشاء تقرير' لعرض تقرير الأداء...")

        report_display_layout.addWidget(self.report_display)
        report_display_group.setLayout(report_display_layout)

        layout.addWidget(reports_group)
        layout.addWidget(report_display_group)

        tab.setLayout(layout)
        return tab

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        # زر إيقاف/تشغيل المراقبة
        self.monitor_toggle_btn = QPushButton("⏸️ إيقاف المراقبة")
        self.monitor_toggle_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        self.monitor_toggle_btn.clicked.connect(self.toggle_monitoring)

        # زر المساعدة
        help_btn = QPushButton("❓ المساعدة")
        help_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        help_btn.clicked.connect(self.show_help)

        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_btn.clicked.connect(self.close_monitor)

        layout.addWidget(self.monitor_toggle_btn)
        layout.addWidget(help_btn)
        layout.addStretch()
        layout.addWidget(close_btn)

        return layout

    # الوظائف الأساسية
    def start_monitoring(self):
        """بدء مراقبة الأداء"""
        self.data_collector = PerformanceDataCollector()
        self.data_collector.data_updated.connect(self.update_performance_data)
        self.data_collector.start()

    def update_performance_data(self, data):
        """تحديث بيانات الأداء"""
        self.performance_data = data

        # تحديث المؤشرات الرئيسية
        self.cpu_progress.setValue(int(data['cpu_percent']))
        self.cpu_label.setText(f"{data['cpu_percent']:.1f}%")

        self.memory_progress.setValue(int(data['memory_percent']))
        self.memory_label.setText(f"{data['memory_percent']:.1f}%")

        self.disk_progress.setValue(int(data['disk_percent']))
        self.disk_label.setText(f"{data['disk_percent']:.1f}%")

        self.network_in_label.setText(f"📥 {data['network_speed_in']:.1f} KB/s")
        self.network_out_label.setText(f"📤 {data['network_speed_out']:.1f} KB/s")

        # تحديث الجدول التفصيلي
        self.update_details_table(data)

        # تحديث الرسم البياني
        self.update_chart()

        # فحص التنبيهات
        self.check_alerts(data)

    def update_details_table(self, data):
        """تحديث جدول التفاصيل"""
        details = [
            ("استخدام المعالج", f"{data['cpu_percent']:.1f}%"),
            ("استخدام الذاكرة", f"{data['memory_percent']:.1f}%"),
            ("الذاكرة المستخدمة", f"{data['memory_used']:.2f} GB"),
            ("إجمالي الذاكرة", f"{data['memory_total']:.2f} GB"),
            ("استخدام القرص", f"{data['disk_percent']:.1f}%"),
            ("مساحة القرص المستخدمة", f"{data['disk_used']:.2f} GB"),
            ("إجمالي مساحة القرص", f"{data['disk_total']:.2f} GB"),
            ("سرعة التحميل", f"{data['network_speed_in']:.1f} KB/s"),
            ("سرعة الرفع", f"{data['network_speed_out']:.1f} KB/s"),
            ("عدد العمليات", str(data['processes_count'])),
            ("وقت التحديث", datetime.fromtimestamp(data['timestamp']).strftime("%H:%M:%S"))
        ]

        self.details_table.setRowCount(len(details))
        for row, (key, value) in enumerate(details):
            self.details_table.setItem(row, 0, QTableWidgetItem(key))
            self.details_table.setItem(row, 1, QTableWidgetItem(value))

    def update_chart(self):
        """تحديث الرسم البياني"""
        if not hasattr(self, 'performance_data') or 'history' not in self.performance_data:
            return

        chart_type = self.chart_type.currentText()
        history = self.performance_data['history']

        if not history['timestamps']:
            return

        # تحديد البيانات حسب نوع الرسم البياني
        if chart_type == "المعالج":
            y_data = list(history['cpu'])
            color = 'b'
            title = 'استخدام المعالج (%)'
        elif chart_type == "الذاكرة":
            y_data = list(history['memory'])
            color = 'r'
            title = 'استخدام الذاكرة (%)'
        elif chart_type == "القرص الصلب":
            y_data = list(history['disk'])
            color = 'g'
            title = 'استخدام القرص الصلب (%)'
        else:  # الشبكة
            y_data = [0] * len(history['timestamps'])  # مؤقت
            color = 'orange'
            title = 'نشاط الشبكة'

        # تحديد نطاق الوقت
        time_range = self.time_range.currentText()
        if time_range == "آخر دقيقة":
            data_points = 60
        elif time_range == "آخر 5 دقائق":
            data_points = 300
        elif time_range == "آخر 15 دقيقة":
            data_points = 900
        else:  # آخر ساعة
            data_points = 3600

        # قطع البيانات حسب النطاق المطلوب
        if len(y_data) > data_points:
            y_data = y_data[-data_points:]

        x_data = list(range(len(y_data)))

        # رسم البيانات
        self.chart_widget.clear()
        self.chart_widget.plot(x_data, y_data, pen=color, name=title)
        self.chart_widget.setTitle(title)

    def check_alerts(self, data):
        """فحص التنبيهات"""
        if not self.alerts_enabled:
            return

        current_time = datetime.now().strftime("%H:%M:%S")

        # فحص تنبيه المعالج
        if data['cpu_percent'] > self.alert_thresholds['cpu']:
            self.add_alert(current_time, "المعالج", f"{data['cpu_percent']:.1f}%",
                          f"استخدام المعالج مرتفع: {data['cpu_percent']:.1f}%")

        # فحص تنبيه الذاكرة
        if data['memory_percent'] > self.alert_thresholds['memory']:
            self.add_alert(current_time, "الذاكرة", f"{data['memory_percent']:.1f}%",
                          f"استخدام الذاكرة مرتفع: {data['memory_percent']:.1f}%")

        # فحص تنبيه القرص
        if data['disk_percent'] > self.alert_thresholds['disk']:
            self.add_alert(current_time, "القرص", f"{data['disk_percent']:.1f}%",
                          f"استخدام القرص مرتفع: {data['disk_percent']:.1f}%")

    def add_alert(self, time, alert_type, value, message):
        """إضافة تنبيه للسجل"""
        row = self.alerts_table.rowCount()
        self.alerts_table.insertRow(row)

        self.alerts_table.setItem(row, 0, QTableWidgetItem(time))
        self.alerts_table.setItem(row, 1, QTableWidgetItem(alert_type))
        self.alerts_table.setItem(row, 2, QTableWidgetItem(value))
        self.alerts_table.setItem(row, 3, QTableWidgetItem(message))

        # تمرير للأسفل لإظهار آخر تنبيه
        self.alerts_table.scrollToBottom()

    def toggle_alerts(self, enabled):
        """تبديل حالة التنبيهات"""
        self.alerts_enabled = enabled

    def update_thresholds(self):
        """تحديث حدود التنبيهات"""
        self.alert_thresholds['cpu'] = self.cpu_threshold.value()
        self.alert_thresholds['memory'] = self.memory_threshold.value()
        self.alert_thresholds['disk'] = self.disk_threshold.value()

    def clear_alerts_log(self):
        """مسح سجل التنبيهات"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح جميع التنبيهات؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.alerts_table.setRowCount(0)
            QMessageBox.information(self, "تم", "تم مسح سجل التنبيهات")

    def export_alerts_log(self):
        """تصدير سجل التنبيهات"""
        if self.alerts_table.rowCount() == 0:
            QMessageBox.information(self, "تنبيه", "لا يوجد تنبيهات للتصدير")
            return

        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير سجل التنبيهات",
                f"alerts_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv);;All Files (*)"
            )

            if file_path:
                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['الوقت', 'النوع', 'القيمة', 'الرسالة']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()

                    for row in range(self.alerts_table.rowCount()):
                        writer.writerow({
                            'الوقت': self.alerts_table.item(row, 0).text(),
                            'النوع': self.alerts_table.item(row, 1).text(),
                            'القيمة': self.alerts_table.item(row, 2).text(),
                            'الرسالة': self.alerts_table.item(row, 3).text()
                        })

                QMessageBox.information(self, "نجح", f"تم تصدير السجل إلى:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير السجل:\n{str(e)}")

    def generate_report(self):
        """إنشاء تقرير الأداء"""
        if not hasattr(self, 'performance_data'):
            QMessageBox.warning(self, "تحذير", "لا توجد بيانات أداء متاحة")
            return

        report_type = self.report_type.currentText()
        report_period = self.report_period.currentText()

        report_content = f"""
📈 تقرير مراقب الأداء
{'='*50}

📋 معلومات التقرير:
   نوع التقرير: {report_type}
   فترة التقرير: {report_period}
   تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 الحالة الحالية:
   🖥️ استخدام المعالج: {self.performance_data.get('cpu_percent', 0):.1f}%
   💾 استخدام الذاكرة: {self.performance_data.get('memory_percent', 0):.1f}%
   💿 استخدام القرص: {self.performance_data.get('disk_percent', 0):.1f}%
   🌐 سرعة التحميل: {self.performance_data.get('network_speed_in', 0):.1f} KB/s
   🌐 سرعة الرفع: {self.performance_data.get('network_speed_out', 0):.1f} KB/s

📈 الإحصائيات:
   عدد العمليات الجارية: {self.performance_data.get('processes_count', 0)}
   إجمالي الذاكرة: {self.performance_data.get('memory_total', 0):.2f} GB
   إجمالي مساحة القرص: {self.performance_data.get('disk_total', 0):.2f} GB

🚨 التنبيهات:
   حد تنبيه المعالج: {self.alert_thresholds['cpu']}%
   حد تنبيه الذاكرة: {self.alert_thresholds['memory']}%
   حد تنبيه القرص: {self.alert_thresholds['disk']}%
   عدد التنبيهات: {self.alerts_table.rowCount()}

💡 التوصيات:
   • راقب استخدام الموارد بانتظام
   • قم بتنظيف الملفات المؤقتة عند امتلاء القرص
   • أغلق البرامج غير المستخدمة لتوفير الذاكرة
   • راجع العمليات المستهلكة للمعالج

📝 ملاحظات:
   هذا التقرير يعكس الحالة الحالية للنظام.
   للحصول على تحليل أكثر دقة، راقب النظام لفترة أطول.
        """

        self.report_display.setPlainText(report_content.strip())

    def export_report(self):
        """تصدير التقرير"""
        if not self.report_display.toPlainText() or "انقر على" in self.report_display.toPlainText():
            QMessageBox.warning(self, "تحذير", "يرجى إنشاء تقرير أولاً")
            return

        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير تقرير الأداء",
                f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "Text Files (*.txt);;All Files (*)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.report_display.toPlainText())

                QMessageBox.information(self, "نجح", f"تم تصدير التقرير إلى:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")

    def toggle_monitoring(self):
        """تبديل حالة المراقبة"""
        if self.data_collector and self.data_collector.is_running:
            # إيقاف المراقبة
            self.data_collector.stop()
            self.monitor_toggle_btn.setText("▶️ تشغيل المراقبة")
            self.monitor_toggle_btn.setStyleSheet("""
                QPushButton {
                    background-color: #28a745;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #218838;
                }
            """)
        else:
            # تشغيل المراقبة
            self.start_monitoring()
            self.monitor_toggle_btn.setText("⏸️ إيقاف المراقبة")
            self.monitor_toggle_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ffc107;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #e0a800;
                }
            """)

    def close_monitor(self):
        """إغلاق مراقب الأداء"""
        if self.data_collector:
            self.data_collector.stop()
            self.data_collector.wait()
        self.reject()

    def show_help(self):
        """عرض المساعدة"""
        help_text = """
📈 مساعدة مراقب الأداء المتقدم

📋 الميزات المتاحة:

📊 المراقبة المباشرة:
• مراقبة استخدام المعالج في الوقت الفعلي
• مراقبة استخدام الذاكرة والقرص الصلب
• مراقبة نشاط الشبكة (التحميل والرفع)
• عرض معلومات تفصيلية شاملة
• تحديث تلقائي كل ثانية

📈 الرسوم البيانية:
• رسوم بيانية تفاعلية للأداء
• اختيار نوع البيانات (معالج، ذاكرة، قرص، شبكة)
• فترات عرض مختلفة (دقيقة إلى ساعة)
• تتبع التغييرات عبر الزمن

🚨 نظام التنبيهات:
• تنبيهات تلقائية عند تجاوز الحدود
• حدود قابلة للتخصيص لكل مورد
• سجل شامل لجميع التنبيهات
• تصدير سجل التنبيهات

📋 التقارير:
• إنشاء تقارير شاملة للأداء
• أنواع تقارير متعددة
• فترات زمنية مختلفة
• تصدير التقارير للحفظ

⚙️ المؤشرات المراقبة:
• المعالج: نسبة الاستخدام المئوية
• الذاكرة: الاستخدام والمساحة المتاحة
• القرص الصلب: المساحة المستخدمة والمتاحة
• الشبكة: سرعة التحميل والرفع
• العمليات: عدد العمليات الجارية

🎨 الواجهة:
• مؤشرات ملونة وواضحة
• أشرطة تقدم تفاعلية
• جداول تفصيلية منظمة
• رسوم بيانية احترافية

💡 نصائح الاستخدام:
• راقب الأداء بانتظام
• اضبط حدود التنبيهات حسب احتياجاتك
• استخدم التقارير لتحليل الأداء
• صدر السجلات للاحتفاظ بالبيانات

⚠️ التنبيهات الافتراضية:
• المعالج: 80%
• الذاكرة: 85%
• القرص الصلب: 90%

🔧 التحكم:
• إيقاف/تشغيل المراقبة
• مسح السجلات
• تصدير البيانات
• تخصيص الإعدادات

📊 الرسوم البيانية:
• خط زمني للأداء
• ألوان مميزة لكل مورد
• تكبير وتصغير تفاعلي
• حفظ الرسوم البيانية

🎯 الاستخدامات:
• مراقبة أداء الخادم
• تشخيص مشاكل الأداء
• تحسين استخدام الموارد
• التخطيط لترقية النظام
        """

        QMessageBox.information(self, "المساعدة", help_text)


def main():
    """اختبار مراقب الأداء"""
    import sys

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    dialog = PerformanceMonitorDialog()
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
