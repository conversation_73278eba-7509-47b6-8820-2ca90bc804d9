#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة شجرة الفروع
Branch Tree Setup Screen
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QMessageBox, 
                             QFrame, QSplitter, QTextEdit, QTabWidget, QWidget,
                             QTreeWidget, QTreeWidgetItem, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QIcon

class BranchTreeDialog(QDialog):
    """شاشة شجرة الفروع"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("شجرة الفروع - الهيكل التنظيمي للفروع والمكاتب")
        self.setGeometry(50, 50, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان الشاشة
        title_label = QLabel("🌳 شجرة الفروع والمكاتب")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تقسيم الشاشة
        splitter = QSplitter(Qt.Horizontal)
        
        # الجانب الأيسر - الشجرة الهرمية
        left_panel = self.create_tree_panel()
        splitter.addWidget(left_panel)
        
        # الجانب الأيمن - نماذج الإدارة
        right_panel = self.create_management_panel()
        splitter.addWidget(right_panel)
        
        # تحديد نسب التقسيم
        splitter.setSizes([600, 800])
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(splitter)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_tree_panel(self):
        """إنشاء لوحة الشجرة الهرمية"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # عنوان القسم
        section_title = QLabel("🌳 الهيكل التنظيمي للفروع")
        section_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                padding: 10px;
                background-color: #d5dbdb;
                border-radius: 5px;
                margin-bottom: 15px;
            }
        """)
        section_title.setAlignment(Qt.AlignCenter)
        
        # شجرة الفروع
        self.branch_tree = QTreeWidget()
        self.branch_tree.setHeaderLabels(["اسم الفرع/المكتب", "النوع", "الرمز", "المدير", "الحالة"])
        self.branch_tree.setRootIsDecorated(True)
        self.branch_tree.setAnimated(True)
        
        self.branch_tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                font-size: 13px;
                outline: none;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
                min-height: 30px;
            }
            QTreeWidget::item:hover {
                background-color: #e8f4fd;
            }
            QTreeWidget::item:selected {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
            QTreeWidget::branch:has-siblings:!adjoins-item {
                border-image: url(vline.png) 0;
            }
            QTreeWidget::branch:has-siblings:adjoins-item {
                border-image: url(branch-more.png) 0;
            }
            QTreeWidget::branch:!has-children:!has-siblings:adjoins-item {
                border-image: url(branch-end.png) 0;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                border-image: none;
                image: url(branch-closed.png);
            }
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                border-image: none;
                image: url(branch-open.png);
            }
        """)
        
        # ربط الأحداث
        self.branch_tree.itemClicked.connect(self.on_tree_item_clicked)
        self.branch_tree.itemDoubleClicked.connect(self.on_tree_item_double_clicked)
        
        # أزرار إدارة الشجرة
        tree_buttons = QHBoxLayout()
        
        expand_all_btn = QPushButton("📂 توسيع الكل")
        expand_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        expand_all_btn.clicked.connect(self.branch_tree.expandAll)
        
        collapse_all_btn = QPushButton("📁 طي الكل")
        collapse_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        collapse_all_btn.clicked.connect(self.branch_tree.collapseAll)
        
        refresh_tree_btn = QPushButton("🔄 تحديث الشجرة")
        refresh_tree_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        refresh_tree_btn.clicked.connect(self.refresh_tree)
        
        print_tree_btn = QPushButton("🖨️ طباعة الشجرة")
        print_tree_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        print_tree_btn.clicked.connect(self.print_tree)
        
        tree_buttons.addWidget(expand_all_btn)
        tree_buttons.addWidget(collapse_all_btn)
        tree_buttons.addWidget(refresh_tree_btn)
        tree_buttons.addWidget(print_tree_btn)
        
        # معلومات الشجرة
        self.tree_info = QLabel("📊 إحصائيات الشجرة: 0 فرع، 0 مكتب")
        self.tree_info.setStyleSheet("""
            QLabel {
                background-color: #e8f4fd;
                border: 1px solid #bee5eb;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                color: #0c5460;
            }
        """)
        
        layout.addWidget(section_title)
        layout.addWidget(self.branch_tree)
        layout.addLayout(tree_buttons)
        layout.addWidget(self.tree_info)
        
        panel.setLayout(layout)
        return panel
        
    def create_management_panel(self):
        """إنشاء لوحة الإدارة"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout()
        
        # تبويبات الإدارة
        tabs = QTabWidget()
        
        # تبويب الفروع الرئيسية
        main_branches_tab = self.create_main_branches_tab()
        tabs.addTab(main_branches_tab, "🏢 الفروع الرئيسية")
        
        # تبويب الفروع الفرعية
        sub_branches_tab = self.create_sub_branches_tab()
        tabs.addTab(sub_branches_tab, "🏪 الفروع الفرعية")
        
        # تبويب المكاتب
        offices_tab = self.create_offices_tab()
        tabs.addTab(offices_tab, "🏬 المكاتب")
        
        # تبويب الإعدادات
        settings_tab = self.create_settings_tab()
        tabs.addTab(settings_tab, "⚙️ إعدادات الشجرة")
        
        layout.addWidget(tabs)
        panel.setLayout(layout)
        return panel
        
    def create_main_branches_tab(self):
        """إنشاء تبويب الفروع الرئيسية"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # نموذج إضافة فرع رئيسي
        branch_form = QGroupBox("➕ إضافة فرع رئيسي جديد")
        form_layout = QGridLayout()
        
        # اسم الفرع
        form_layout.addWidget(QLabel("اسم الفرع:"), 0, 0)
        self.main_branch_name = QLineEdit()
        self.main_branch_name.setPlaceholderText("مثال: فرع صنعاء الرئيسي")
        form_layout.addWidget(self.main_branch_name, 0, 1)
        
        # اسم الفرع بالإنجليزية
        form_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 0, 2)
        self.main_branch_name_en = QLineEdit()
        self.main_branch_name_en.setPlaceholderText("Sana'a Main Branch")
        form_layout.addWidget(self.main_branch_name_en, 0, 3)
        
        # رمز الفرع
        form_layout.addWidget(QLabel("رمز الفرع:"), 1, 0)
        self.main_branch_code = QLineEdit()
        self.main_branch_code.setPlaceholderText("SAN-001")
        self.main_branch_code.setMaxLength(10)
        form_layout.addWidget(self.main_branch_code, 1, 1)
        
        # المحافظة
        form_layout.addWidget(QLabel("المحافظة:"), 1, 2)
        self.main_branch_governorate = QComboBox()
        form_layout.addWidget(self.main_branch_governorate, 1, 3)
        
        # المدينة
        form_layout.addWidget(QLabel("المدينة:"), 2, 0)
        self.main_branch_city = QComboBox()
        form_layout.addWidget(self.main_branch_city, 2, 1)
        
        # العنوان
        form_layout.addWidget(QLabel("العنوان:"), 2, 2)
        self.main_branch_address = QLineEdit()
        self.main_branch_address.setPlaceholderText("العنوان التفصيلي للفرع")
        form_layout.addWidget(self.main_branch_address, 2, 3)
        
        # مدير الفرع
        form_layout.addWidget(QLabel("مدير الفرع:"), 3, 0)
        self.main_branch_manager = QComboBox()
        self.main_branch_manager.setEditable(True)
        form_layout.addWidget(self.main_branch_manager, 3, 1)
        
        # رقم الهاتف
        form_layout.addWidget(QLabel("رقم الهاتف:"), 3, 2)
        self.main_branch_phone = QLineEdit()
        self.main_branch_phone.setPlaceholderText("+967-1-123456")
        form_layout.addWidget(self.main_branch_phone, 3, 3)
        
        # فرع نشط
        self.main_branch_active = QCheckBox("فرع نشط")
        self.main_branch_active.setChecked(True)
        form_layout.addWidget(self.main_branch_active, 4, 0, 1, 2)
        
        # فرع رئيسي
        self.main_branch_is_main = QCheckBox("فرع رئيسي (مقر)")
        form_layout.addWidget(self.main_branch_is_main, 4, 2, 1, 2)
        
        branch_form.setLayout(form_layout)
        
        # أزرار إدارة الفروع الرئيسية
        main_branch_buttons = QHBoxLayout()
        
        add_main_branch_btn = QPushButton("🏢 إضافة فرع رئيسي")
        add_main_branch_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_main_branch_btn.clicked.connect(self.add_main_branch)
        
        edit_main_branch_btn = QPushButton("✏️ تعديل")
        edit_main_branch_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_main_branch_btn.clicked.connect(self.edit_main_branch)
        
        delete_main_branch_btn = QPushButton("🗑️ حذف")
        delete_main_branch_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_main_branch_btn.clicked.connect(self.delete_main_branch)
        
        main_branch_buttons.addWidget(add_main_branch_btn)
        main_branch_buttons.addWidget(edit_main_branch_btn)
        main_branch_buttons.addWidget(delete_main_branch_btn)
        main_branch_buttons.addStretch()
        
        # جدول الفروع الرئيسية
        self.main_branches_table = QTableWidget()
        self.main_branches_table.setColumnCount(8)
        self.main_branches_table.setHorizontalHeaderLabels([
            "اسم الفرع", "الرمز", "المحافظة", "المدينة", "المدير", "الهاتف", "الفروع الفرعية", "الحالة"
        ])
        
        # تنسيق الجدول
        self.main_branches_table.horizontalHeader().setStretchLastSection(True)
        self.main_branches_table.setAlternatingRowColors(True)
        self.main_branches_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.main_branches_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        layout.addWidget(branch_form)
        layout.addLayout(main_branch_buttons)
        layout.addWidget(QLabel("📋 الفروع الرئيسية المعرفة:"))
        layout.addWidget(self.main_branches_table)
        
        tab.setLayout(layout)
        return tab

    def create_sub_branches_tab(self):
        """إنشاء تبويب الفروع الفرعية"""
        tab = QWidget()
        layout = QVBoxLayout()

        # نموذج إضافة فرع فرعي
        sub_branch_form = QGroupBox("➕ إضافة فرع فرعي جديد")
        form_layout = QGridLayout()

        # اسم الفرع الفرعي
        form_layout.addWidget(QLabel("اسم الفرع الفرعي:"), 0, 0)
        self.sub_branch_name = QLineEdit()
        self.sub_branch_name.setPlaceholderText("مثال: فرع الثورة")
        form_layout.addWidget(self.sub_branch_name, 0, 1)

        # الفرع الرئيسي التابع له
        form_layout.addWidget(QLabel("الفرع الرئيسي:"), 0, 2)
        self.sub_branch_parent = QComboBox()
        form_layout.addWidget(self.sub_branch_parent, 0, 3)

        # رمز الفرع الفرعي
        form_layout.addWidget(QLabel("رمز الفرع:"), 1, 0)
        self.sub_branch_code = QLineEdit()
        self.sub_branch_code.setPlaceholderText("SAN-002")
        self.sub_branch_code.setMaxLength(10)
        form_layout.addWidget(self.sub_branch_code, 1, 1)

        # المنطقة
        form_layout.addWidget(QLabel("المنطقة:"), 1, 2)
        self.sub_branch_district = QComboBox()
        form_layout.addWidget(self.sub_branch_district, 1, 3)

        # العنوان
        form_layout.addWidget(QLabel("العنوان:"), 2, 0)
        self.sub_branch_address = QLineEdit()
        self.sub_branch_address.setPlaceholderText("العنوان التفصيلي للفرع الفرعي")
        form_layout.addWidget(self.sub_branch_address, 2, 1, 1, 3)

        # مدير الفرع الفرعي
        form_layout.addWidget(QLabel("مدير الفرع:"), 3, 0)
        self.sub_branch_manager = QComboBox()
        self.sub_branch_manager.setEditable(True)
        form_layout.addWidget(self.sub_branch_manager, 3, 1)

        # رقم الهاتف
        form_layout.addWidget(QLabel("رقم الهاتف:"), 3, 2)
        self.sub_branch_phone = QLineEdit()
        self.sub_branch_phone.setPlaceholderText("+967-1-654321")
        form_layout.addWidget(self.sub_branch_phone, 3, 3)

        # فرع فرعي نشط
        self.sub_branch_active = QCheckBox("فرع فرعي نشط")
        self.sub_branch_active.setChecked(True)
        form_layout.addWidget(self.sub_branch_active, 4, 0, 1, 2)

        sub_branch_form.setLayout(form_layout)

        # أزرار إدارة الفروع الفرعية
        sub_branch_buttons = QHBoxLayout()

        add_sub_branch_btn = QPushButton("🏪 إضافة فرع فرعي")
        add_sub_branch_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_sub_branch_btn.clicked.connect(self.add_sub_branch)

        edit_sub_branch_btn = QPushButton("✏️ تعديل")
        edit_sub_branch_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_sub_branch_btn.clicked.connect(self.edit_sub_branch)

        delete_sub_branch_btn = QPushButton("🗑️ حذف")
        delete_sub_branch_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_sub_branch_btn.clicked.connect(self.delete_sub_branch)

        sub_branch_buttons.addWidget(add_sub_branch_btn)
        sub_branch_buttons.addWidget(edit_sub_branch_btn)
        sub_branch_buttons.addWidget(delete_sub_branch_btn)
        sub_branch_buttons.addStretch()

        # جدول الفروع الفرعية
        self.sub_branches_table = QTableWidget()
        self.sub_branches_table.setColumnCount(7)
        self.sub_branches_table.setHorizontalHeaderLabels([
            "اسم الفرع الفرعي", "الرمز", "الفرع الرئيسي", "المنطقة", "المدير", "الهاتف", "الحالة"
        ])

        # تنسيق الجدول
        self.sub_branches_table.horizontalHeader().setStretchLastSection(True)
        self.sub_branches_table.setAlternatingRowColors(True)
        self.sub_branches_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.sub_branches_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)

        layout.addWidget(sub_branch_form)
        layout.addLayout(sub_branch_buttons)
        layout.addWidget(QLabel("📋 الفروع الفرعية المعرفة:"))
        layout.addWidget(self.sub_branches_table)

        tab.setLayout(layout)
        return tab

    def create_offices_tab(self):
        """إنشاء تبويب المكاتب"""
        tab = QWidget()
        layout = QVBoxLayout()

        # نموذج إضافة مكتب
        office_form = QGroupBox("➕ إضافة مكتب جديد")
        form_layout = QGridLayout()

        # اسم المكتب
        form_layout.addWidget(QLabel("اسم المكتب:"), 0, 0)
        self.office_name = QLineEdit()
        self.office_name.setPlaceholderText("مثال: مكتب خدمة العملاء")
        form_layout.addWidget(self.office_name, 0, 1)

        # الفرع التابع له
        form_layout.addWidget(QLabel("الفرع التابع له:"), 0, 2)
        self.office_branch = QComboBox()
        form_layout.addWidget(self.office_branch, 0, 3)

        # رمز المكتب
        form_layout.addWidget(QLabel("رمز المكتب:"), 1, 0)
        self.office_code = QLineEdit()
        self.office_code.setPlaceholderText("SAN-001-CS")
        self.office_code.setMaxLength(15)
        form_layout.addWidget(self.office_code, 1, 1)

        # نوع المكتب
        form_layout.addWidget(QLabel("نوع المكتب:"), 1, 2)
        self.office_type = QComboBox()
        self.office_type.addItems([
            "خدمة العملاء", "المبيعات", "المحاسبة", "الموارد البشرية",
            "تقنية المعلومات", "التسويق", "المشتريات", "المخازن", "الأمن", "الصيانة"
        ])
        form_layout.addWidget(self.office_type, 1, 3)

        # مسؤول المكتب
        form_layout.addWidget(QLabel("مسؤول المكتب:"), 2, 0)
        self.office_supervisor = QComboBox()
        self.office_supervisor.setEditable(True)
        form_layout.addWidget(self.office_supervisor, 2, 1)

        # رقم الهاتف الداخلي
        form_layout.addWidget(QLabel("الهاتف الداخلي:"), 2, 2)
        self.office_extension = QLineEdit()
        self.office_extension.setPlaceholderText("101")
        form_layout.addWidget(self.office_extension, 2, 3)

        # الوصف
        form_layout.addWidget(QLabel("الوصف:"), 3, 0)
        self.office_description = QTextEdit()
        self.office_description.setMaximumHeight(60)
        self.office_description.setPlaceholderText("وصف مختصر لوظائف المكتب...")
        form_layout.addWidget(self.office_description, 3, 1, 1, 3)

        # مكتب نشط
        self.office_active = QCheckBox("مكتب نشط")
        self.office_active.setChecked(True)
        form_layout.addWidget(self.office_active, 4, 0, 1, 2)

        office_form.setLayout(form_layout)

        # أزرار إدارة المكاتب
        office_buttons = QHBoxLayout()

        add_office_btn = QPushButton("🏬 إضافة مكتب")
        add_office_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_office_btn.clicked.connect(self.add_office)

        edit_office_btn = QPushButton("✏️ تعديل")
        edit_office_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_office_btn.clicked.connect(self.edit_office)

        delete_office_btn = QPushButton("🗑️ حذف")
        delete_office_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_office_btn.clicked.connect(self.delete_office)

        office_buttons.addWidget(add_office_btn)
        office_buttons.addWidget(edit_office_btn)
        office_buttons.addWidget(delete_office_btn)
        office_buttons.addStretch()

        # جدول المكاتب
        self.offices_table = QTableWidget()
        self.offices_table.setColumnCount(7)
        self.offices_table.setHorizontalHeaderLabels([
            "اسم المكتب", "الرمز", "النوع", "الفرع", "المسؤول", "الهاتف الداخلي", "الحالة"
        ])

        # تنسيق الجدول
        self.offices_table.horizontalHeader().setStretchLastSection(True)
        self.offices_table.setAlternatingRowColors(True)
        self.offices_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.offices_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)

        layout.addWidget(office_form)
        layout.addLayout(office_buttons)
        layout.addWidget(QLabel("📋 المكاتب المعرفة:"))
        layout.addWidget(self.offices_table)

        tab.setLayout(layout)
        return tab

    def create_settings_tab(self):
        """إنشاء تبويب إعدادات الشجرة"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة إعدادات العرض
        display_group = QGroupBox("🖥️ إعدادات عرض الشجرة")
        display_layout = QGridLayout()

        # عرض الرموز
        self.show_codes = QCheckBox("عرض رموز الفروع والمكاتب")
        self.show_codes.setChecked(True)
        display_layout.addWidget(self.show_codes, 0, 0, 1, 2)

        # عرض المديرين
        self.show_managers = QCheckBox("عرض أسماء المديرين والمسؤولين")
        self.show_managers.setChecked(True)
        display_layout.addWidget(self.show_managers, 1, 0, 1, 2)

        # عرض الحالة
        self.show_status = QCheckBox("عرض حالة الفروع والمكاتب")
        self.show_status.setChecked(True)
        display_layout.addWidget(self.show_status, 2, 0, 1, 2)

        # ألوان الشجرة
        display_layout.addWidget(QLabel("لون الفروع الرئيسية:"), 3, 0)
        self.main_branch_color = QComboBox()
        self.main_branch_color.addItems(["أزرق", "أخضر", "أحمر", "بنفسجي", "برتقالي"])
        display_layout.addWidget(self.main_branch_color, 3, 1)

        display_layout.addWidget(QLabel("لون الفروع الفرعية:"), 4, 0)
        self.sub_branch_color = QComboBox()
        self.sub_branch_color.addItems(["أخضر", "أزرق", "أصفر", "وردي", "رمادي"])
        display_layout.addWidget(self.sub_branch_color, 4, 1)

        display_layout.addWidget(QLabel("لون المكاتب:"), 5, 0)
        self.office_color = QComboBox()
        self.office_color.addItems(["رمادي", "بني", "أصفر", "وردي", "أزرق فاتح"])
        display_layout.addWidget(self.office_color, 5, 1)

        display_group.setLayout(display_layout)

        # مجموعة إعدادات التقارير
        reports_group = QGroupBox("📊 إعدادات التقارير")
        reports_layout = QVBoxLayout()

        # أزرار التقارير
        reports_buttons = QHBoxLayout()

        org_chart_btn = QPushButton("📋 الهيكل التنظيمي")
        org_chart_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        org_chart_btn.clicked.connect(self.generate_org_chart)

        branches_report_btn = QPushButton("📈 تقرير الفروع")
        branches_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        branches_report_btn.clicked.connect(self.generate_branches_report)

        contacts_report_btn = QPushButton("📞 دليل الاتصالات")
        contacts_report_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        contacts_report_btn.clicked.connect(self.generate_contacts_report)

        reports_buttons.addWidget(org_chart_btn)
        reports_buttons.addWidget(branches_report_btn)
        reports_buttons.addWidget(contacts_report_btn)
        reports_buttons.addStretch()

        reports_layout.addLayout(reports_buttons)
        reports_group.setLayout(reports_layout)

        layout.addWidget(display_group)
        layout.addWidget(reports_group)
        layout.addStretch()

        tab.setLayout(layout)
        return tab

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        from .branch_tree_functions import create_control_buttons_function
        return create_control_buttons_function(self)

    def load_data(self):
        """تحميل البيانات المحفوظة"""
        from .branch_tree_functions import load_data_function
        load_data_function(self)

    def load_locations(self):
        """تحميل قائمة المحافظات والمدن"""
        from .branch_tree_functions import load_locations_function
        load_locations_function(self)

    def load_managers(self):
        """تحميل قائمة المديرين والموظفين"""
        from .branch_tree_functions import load_managers_function
        load_managers_function(self)

    def load_sample_data(self):
        """تحميل البيانات النموذجية"""
        from .branch_tree_functions import load_sample_data_function
        load_sample_data_function(self)

    def update_parent_branches(self):
        """تحديث قائمة الفروع الرئيسية"""
        from .branch_tree_functions import update_parent_branches_function
        update_parent_branches_function(self)

    def update_branch_offices(self):
        """تحديث قائمة الفروع للمكاتب"""
        from .branch_tree_functions import update_branch_offices_function
        update_branch_offices_function(self)

    def update_sub_branch_counts(self):
        """تحديث عدد الفروع الفرعية لكل فرع رئيسي"""
        from .branch_tree_functions import update_sub_branch_counts_function
        update_sub_branch_counts_function(self)

    def build_branch_tree(self):
        """بناء الشجرة الهرمية للفروع"""
        from .branch_tree_functions import build_branch_tree_function
        build_branch_tree_function(self)

    def update_tree_statistics(self):
        """تحديث إحصائيات الشجرة"""
        from .branch_tree_functions import update_tree_statistics_function
        update_tree_statistics_function(self)

    def refresh_tree(self):
        """تحديث الشجرة الهرمية"""
        from .branch_tree_functions import refresh_tree_function
        refresh_tree_function(self)

    def on_tree_item_clicked(self, item, column):
        """معالجة النقر على عنصر في الشجرة"""
        from .branch_tree_functions import on_tree_item_clicked_function
        on_tree_item_clicked_function(self, item, column)

    def on_tree_item_double_clicked(self, item, column):
        """معالجة النقر المزدوج على عنصر في الشجرة"""
        from .branch_tree_functions import on_tree_item_double_clicked_function
        on_tree_item_double_clicked_function(self, item, column)

    def print_tree(self):
        """طباعة الشجرة"""
        from .branch_tree_functions import print_tree_function
        print_tree_function(self)

    def generate_org_chart(self):
        """إنتاج الهيكل التنظيمي"""
        from .branch_tree_functions import generate_org_chart_function
        generate_org_chart_function(self)

    def generate_branches_report(self):
        """إنتاج تقرير الفروع"""
        from .branch_tree_functions import generate_branches_report_function
        generate_branches_report_function(self)

    def generate_contacts_report(self):
        """إنتاج دليل الاتصالات"""
        from .branch_tree_functions import generate_contacts_report_function
        generate_contacts_report_function(self)

    def show_help(self):
        """عرض المساعدة"""
        from .branch_tree_functions import show_help_function
        show_help_function(self)

    def save_all_changes(self):
        """حفظ جميع التغييرات"""
        from .branch_tree_functions import save_all_changes_function
        save_all_changes_function(self)

    # وظائف إدارة الفروع الرئيسية (ستكون في ملف منفصل)
    def add_main_branch(self):
        """إضافة فرع رئيسي جديد"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة الفروع الرئيسية قيد التطوير")

    def edit_main_branch(self):
        """تعديل فرع رئيسي"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل الفروع الرئيسية قيد التطوير")

    def delete_main_branch(self):
        """حذف فرع رئيسي"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حذف الفروع الرئيسية قيد التطوير")

    def add_sub_branch(self):
        """إضافة فرع فرعي جديد"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة الفروع الفرعية قيد التطوير")

    def edit_sub_branch(self):
        """تعديل فرع فرعي"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل الفروع الفرعية قيد التطوير")

    def delete_sub_branch(self):
        """حذف فرع فرعي"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حذف الفروع الفرعية قيد التطوير")

    def add_office(self):
        """إضافة مكتب جديد"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة المكاتب قيد التطوير")

    def edit_office(self):
        """تعديل مكتب"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل المكاتب قيد التطوير")

    def delete_office(self):
        """حذف مكتب"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حذف المكاتب قيد التطوير")


def main():
    """اختبار الشاشة"""
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass

    dialog = BranchTreeDialog(MockDBManager())
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
