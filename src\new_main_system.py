#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النظام الرئيسي المحدث - البنية الجديدة
Updated Main System - New Structure
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QTreeWidget, QTreeWidgetItem,
                             QSplitter, QLabel, QFrame, QPushButton, QScrollArea,
                             QGridLayout, QGroupBox, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.system_structure import SYSTEM_STRUCTURE
from src.database.sqlite_manager import SQLiteManager

class ModuleButton(QPushButton):
    """زر الوحدة مع معلومات إضافية"""
    
    module_clicked = pyqtSignal(str, str, str)  # department, tab, module
    
    def __init__(self, module_info, department_key, tab_key, parent=None):
        super().__init__(parent)
        self.module_info = module_info
        self.department_key = department_key
        self.tab_key = tab_key
        
        self.setText(module_info["name"])
        self.setMinimumHeight(40)
        self.setStyleSheet("""
            QPushButton {
                text-align: left;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: #f8f9fa;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #3498db;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """)
        
        self.clicked.connect(self.on_clicked)
    
    def on_clicked(self):
        self.module_clicked.emit(
            self.department_key, 
            self.tab_key, 
            self.module_info["module"]
        )

class DepartmentTabWidget(QTabWidget):
    """تبويبات القسم"""
    
    def __init__(self, department_data, department_key, parent=None):
        super().__init__(parent)
        self.department_data = department_data
        self.department_key = department_key
        self.parent_window = parent
        
        self.setup_tabs()
    
    def setup_tabs(self):
        """إعداد التبويبات"""
        for tab_key, tab_data in self.department_data["tabs"].items():
            # إنشاء محتوى التبويب
            tab_widget = QScrollArea()
            tab_widget.setWidgetResizable(True)
            tab_widget.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
            tab_widget.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
            
            # إنشاء widget المحتوى
            content_widget = QWidget()
            layout = QGridLayout()
            layout.setSpacing(10)
            layout.setContentsMargins(15, 15, 15, 15)
            
            # إضافة الوحدات
            row = 0
            col = 0
            max_cols = 3  # عدد الأعمدة في الشبكة
            
            for module_info in tab_data["modules"]:
                module_btn = ModuleButton(
                    module_info, 
                    self.department_key, 
                    tab_key, 
                    self
                )
                module_btn.module_clicked.connect(self.on_module_clicked)
                
                layout.addWidget(module_btn, row, col)
                
                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1
            
            # إضافة مساحة فارغة في النهاية
            layout.setRowStretch(row + 1, 1)
            layout.setColumnStretch(max_cols, 1)
            
            content_widget.setLayout(layout)
            tab_widget.setWidget(content_widget)
            
            # إضافة التبويب
            tab_icon = tab_data.get("icon", "📄")
            self.addTab(tab_widget, f"{tab_icon} {tab_data['name']}")
    
    def on_module_clicked(self, department, tab, module):
        """عند النقر على وحدة"""
        if hasattr(self.parent_window, 'load_module'):
            self.parent_window.load_module(department, tab, module)

class NewMainSystem(QMainWindow):
    """النظام الرئيسي الجديد"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = SQLiteManager()
        self.current_module = None
        
        self.setup_ui()
        self.setup_connections()
        self.load_welcome_screen()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("نظام المحاسبة المتكامل - البنية الجديدة")
        self.setGeometry(100, 100, 1400, 900)
        
        # Widget الرئيسي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # شريط العنوان
        self.create_header(main_layout)
        
        # المحتوى الرئيسي
        self.create_main_content(main_layout)
        
        # شريط الحالة
        self.statusBar().showMessage("مرحباً بك في نظام المحاسبة المتكامل")
    
    def create_header(self, parent_layout):
        """إنشاء شريط العنوان"""
        header_frame = QFrame()
        header_frame.setFixedHeight(80)
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-bottom: 2px solid #2c3e50;
            }
        """)
        
        header_layout = QHBoxLayout()
        header_frame.setLayout(header_layout)
        
        # عنوان النظام
        title_label = QLabel("🏢 نظام المحاسبة المتكامل")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                padding: 10px;
            }
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # معلومات المستخدم
        user_info = QLabel("👤 المستخدم: admin | 📅 2025")
        user_info.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 12px;
                padding: 10px;
            }
        """)
        header_layout.addWidget(user_info)
        
        parent_layout.addWidget(header_frame)
    
    def create_main_content(self, parent_layout):
        """إنشاء المحتوى الرئيسي"""
        # إنشاء التبويبات الرئيسية للأقسام
        self.departments_tabs = QTabWidget()
        self.departments_tabs.setTabPosition(QTabWidget.North)
        self.departments_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #ddd;
                padding: 8px 16px;
                margin-right: 2px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #3498db;
            }
            QTabBar::tab:hover {
                background-color: #e9ecef;
            }
        """)
        
        # إضافة الأقسام
        for dept_key, dept_data in SYSTEM_STRUCTURE.items():
            dept_widget = DepartmentTabWidget(dept_data, dept_key, self)
            dept_icon = dept_data.get("icon", "📁")
            self.departments_tabs.addTab(
                dept_widget, 
                f"{dept_icon} {dept_data['name']}"
            )
        
        parent_layout.addWidget(self.departments_tabs)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        pass
    
    def load_welcome_screen(self):
        """تحميل شاشة الترحيب"""
        self.statusBar().showMessage("النظام جاهز - اختر قسماً من الأعلى للبدء")
    
    def load_module(self, department, tab, module):
        """تحميل وحدة معينة"""
        try:
            dept_name = SYSTEM_STRUCTURE[department]["name"]
            tab_name = SYSTEM_STRUCTURE[department]["tabs"][tab]["name"]
            
            # البحث عن الوحدة
            module_name = None
            for mod in SYSTEM_STRUCTURE[department]["tabs"][tab]["modules"]:
                if mod["module"] == module:
                    module_name = mod["name"]
                    break
            
            if module_name:
                # محاولة تحميل الوحدة الفعلية
                success = self.try_load_actual_module(department, tab, module)
                
                if not success:
                    # عرض رسالة معلومات
                    QMessageBox.information(
                        self,
                        "تحميل الوحدة",
                        f"📍 القسم: {dept_name}\n"
                        f"📑 التبويب: {tab_name}\n"
                        f"🔧 الوحدة: {module_name}\n\n"
                        f"🚧 هذه الوحدة قيد التطوير\n"
                        f"📝 المعرف: {module}"
                    )
                
                # تحديث شريط الحالة
                self.statusBar().showMessage(
                    f"تم تحميل: {dept_name} → {tab_name} → {module_name}"
                )
            
        except Exception as e:
            QMessageBox.critical(
                self, 
                "خطأ", 
                f"فشل في تحميل الوحدة:\n{str(e)}"
            )
    
    def try_load_actual_module(self, department, tab, module):
        """محاولة تحميل الوحدة الفعلية"""
        try:
            # خريطة الوحدات الموجودة
            existing_modules = {
                "customers_data": "src.modules.customers_suppliers_module",
                "chart_of_accounts": "src.modules.accounts_module",
                "sales_invoices": "src.modules.invoices.enhanced_invoice_system",
                "journal_entries": "src.modules.journal_entries_module",
                "inventory_valuation": "src.modules.inventory_module",
                "receipt_voucher": "src.modules.payments_module",
                "payment_voucher": "src.modules.payments_module",
            }
            
            if module in existing_modules:
                # هنا يمكن تحميل الوحدة الفعلية
                # لكن سنكتفي بإرجاع True للإشارة إلى وجودها
                return True
            
            return False
            
        except Exception as e:
            print(f"خطأ في تحميل الوحدة {module}: {e}")
            return False

def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    
    # إعداد الخط
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    # إنشاء النظام الرئيسي
    main_window = NewMainSystem()
    main_window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
