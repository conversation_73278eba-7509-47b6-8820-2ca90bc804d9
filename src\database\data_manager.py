#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير البيانات المتكامل
Integrated Data Manager
"""

import sqlite3
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import hashlib

class DataManager:
    """مدير البيانات المتكامل"""
    
    def __init__(self, db_path="accounting_system.db"):
        self.db_path = db_path
        self.connection = None
        self.ensure_database_exists()
    
    def ensure_database_exists(self):
        """التأكد من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة"""
        if not os.path.exists(self.db_path):
            print(f"📁 إنشاء قاعدة بيانات جديدة: {self.db_path}")
            self.setup_database()
        else:
            print(f"✅ قاعدة البيانات موجودة: {self.db_path}")
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            from database.production_database_setup import ProductionDatabaseSetup
            
            db_setup = ProductionDatabaseSetup(self.db_path)
            if db_setup.connect():
                db_setup.create_complete_schema()
                db_setup.insert_default_data()
                db_setup.close()
                print("✅ تم إعداد قاعدة البيانات بنجاح")
            else:
                print("❌ فشل في إعداد قاعدة البيانات")
                
        except ImportError:
            print("⚠️ لم يتم العثور على إعداد قاعدة البيانات، سيتم إنشاء قاعدة بيانات بسيطة")
            self.create_simple_database()
    
    def create_simple_database(self):
        """إنشاء قاعدة بيانات بسيطة"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # جدول المستخدمين البسيط
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100) NOT NULL,
                    role VARCHAR(20) DEFAULT 'user',
                    status VARCHAR(20) DEFAULT 'active',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول العملاء البسيط
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_code VARCHAR(20) UNIQUE NOT NULL,
                    customer_name VARCHAR(100) NOT NULL,
                    phone VARCHAR(20),
                    email VARCHAR(100),
                    address TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول المنتجات البسيط
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_code VARCHAR(50) UNIQUE NOT NULL,
                    product_name VARCHAR(100) NOT NULL,
                    selling_price DECIMAL(15,2) DEFAULT 0,
                    current_stock INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إدراج مستخدم افتراضي
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            cursor.execute("""
                INSERT OR IGNORE INTO users (username, email, password_hash, full_name, role)
                VALUES (?, ?, ?, ?, ?)
            """, ('admin', '<EMAIL>', password_hash, 'مدير النظام', 'admin'))
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة بيانات بسيطة")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات البسيطة: {e}")
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
            return True
        except Exception as e:
            print(f"❌ خطأ في الاتصال: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query: str, params: Tuple = None) -> Dict:
        """تنفيذ استعلام"""
        try:
            if not self.connection:
                if not self.connect():
                    return {'success': False, 'error': 'فشل الاتصال بقاعدة البيانات'}
            
            cursor = self.connection.cursor()
            
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            query_type = query.strip().upper().split()[0]
            
            if query_type == 'SELECT':
                data = [dict(row) for row in cursor.fetchall()]
                return {'success': True, 'data': data}
            else:
                self.connection.commit()
                return {'success': True, 'affected_rows': cursor.rowcount}
                
        except Exception as e:
            if self.connection:
                self.connection.rollback()
            return {'success': False, 'error': str(e)}
    
    def fetch_all(self, query: str, params: Tuple = None) -> List[Dict]:
        """جلب جميع النتائج"""
        result = self.execute_query(query, params)
        return result.get('data', []) if result['success'] else []
    
    def fetch_one(self, query: str, params: Tuple = None) -> Optional[Dict]:
        """جلب نتيجة واحدة"""
        result = self.execute_query(query, params)
        data = result.get('data', []) if result['success'] else []
        return data[0] if data else None
    
    def insert(self, table: str, data: Dict) -> Optional[int]:
        """إدراج بيانات"""
        try:
            columns = list(data.keys())
            placeholders = ['?' for _ in columns]
            values = list(data.values())
            
            query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
            result = self.execute_query(query, tuple(values))
            
            if result['success']:
                # الحصول على ID المُدرج
                id_result = self.fetch_one("SELECT last_insert_rowid() as id")
                return id_result['id'] if id_result else None
            
            return None
            
        except Exception as e:
            print(f"❌ خطأ في الإدراج: {e}")
            return None
    
    def update(self, table: str, data: Dict, where_clause: str, where_params: Tuple = None) -> int:
        """تحديث البيانات"""
        try:
            set_clauses = [f"{column} = ?" for column in data.keys()]
            values = list(data.values())
            
            if where_params:
                values.extend(where_params)
            
            query = f"UPDATE {table} SET {', '.join(set_clauses)} WHERE {where_clause}"
            result = self.execute_query(query, tuple(values))
            
            return result.get('affected_rows', 0) if result['success'] else 0
            
        except Exception as e:
            print(f"❌ خطأ في التحديث: {e}")
            return 0
    
    def delete(self, table: str, where_clause: str, where_params: Tuple = None) -> int:
        """حذف البيانات"""
        try:
            query = f"DELETE FROM {table} WHERE {where_clause}"
            result = self.execute_query(query, where_params)
            
            return result.get('affected_rows', 0) if result['success'] else 0
            
        except Exception as e:
            print(f"❌ خطأ في الحذف: {e}")
            return 0
    
    # وظائف خاصة بالمستخدمين
    def get_users(self) -> List[Dict]:
        """الحصول على قائمة المستخدمين"""
        return self.fetch_all("SELECT * FROM users ORDER BY created_at DESC")
    
    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """الحصول على مستخدم بالاسم"""
        return self.fetch_one("SELECT * FROM users WHERE username = ?", (username,))
    
    def create_user(self, user_data: Dict) -> Optional[int]:
        """إنشاء مستخدم جديد"""
        # تشفير كلمة المرور
        if 'password' in user_data:
            user_data['password_hash'] = hashlib.sha256(user_data['password'].encode()).hexdigest()
            del user_data['password']
        
        return self.insert('users', user_data)
    
    def verify_user_password(self, username: str, password: str) -> bool:
        """التحقق من كلمة مرور المستخدم"""
        user = self.get_user_by_username(username)
        if user:
            password_hash = hashlib.sha256(password.encode()).hexdigest()
            return user['password_hash'] == password_hash
        return False
    
    # وظائف خاصة بالعملاء
    def get_customers(self) -> List[Dict]:
        """الحصول على قائمة العملاء"""
        return self.fetch_all("SELECT * FROM customers ORDER BY customer_name")
    
    def get_customer_by_code(self, customer_code: str) -> Optional[Dict]:
        """الحصول على عميل بالكود"""
        return self.fetch_one("SELECT * FROM customers WHERE customer_code = ?", (customer_code,))
    
    def create_customer(self, customer_data: Dict) -> Optional[int]:
        """إنشاء عميل جديد"""
        return self.insert('customers', customer_data)
    
    def update_customer(self, customer_id: int, customer_data: Dict) -> int:
        """تحديث بيانات العميل"""
        return self.update('customers', customer_data, 'id = ?', (customer_id,))

    def update_user(self, user_id: int, user_data: Dict) -> int:
        """تحديث بيانات المستخدم"""
        # تشفير كلمة المرور إذا تم تمريرها
        if 'password' in user_data:
            user_data['password_hash'] = hashlib.sha256(user_data['password'].encode()).hexdigest()
            del user_data['password']

        return self.update('users', user_data, 'id = ?', (user_id,))

    def delete_user(self, user_id: int) -> int:
        """حذف مستخدم"""
        return self.delete('users', 'id = ?', (user_id,))
    
    def delete_customer(self, customer_id: int) -> int:
        """حذف عميل"""
        return self.delete('customers', 'id = ?', (customer_id,))
    
    # وظائف خاصة بالمنتجات
    def get_products(self) -> List[Dict]:
        """الحصول على قائمة المنتجات"""
        return self.fetch_all("SELECT * FROM products ORDER BY product_name")
    
    def get_product_by_code(self, product_code: str) -> Optional[Dict]:
        """الحصول على منتج بالكود"""
        return self.fetch_one("SELECT * FROM products WHERE product_code = ?", (product_code,))
    
    def create_product(self, product_data: Dict) -> Optional[int]:
        """إنشاء منتج جديد"""
        return self.insert('products', product_data)
    
    def update_product(self, product_id: int, product_data: Dict) -> int:
        """تحديث بيانات المنتج"""
        return self.update('products', product_data, 'id = ?', (product_id,))
    
    def delete_product(self, product_id: int) -> int:
        """حذف منتج"""
        return self.delete('products', 'id = ?', (product_id,))
    
    def search_products(self, search_term: str) -> List[Dict]:
        """البحث في المنتجات"""
        return self.fetch_all("""
            SELECT * FROM products 
            WHERE product_name LIKE ? OR product_code LIKE ?
            ORDER BY product_name
        """, (f'%{search_term}%', f'%{search_term}%'))
    
    # وظائف الإحصائيات
    def get_dashboard_stats(self) -> Dict:
        """الحصول على إحصائيات لوحة التحكم"""
        stats = {}
        
        # عدد المستخدمين
        users_count = self.fetch_one("SELECT COUNT(*) as count FROM users")
        stats['users_count'] = users_count['count'] if users_count else 0
        
        # عدد العملاء
        customers_count = self.fetch_one("SELECT COUNT(*) as count FROM customers")
        stats['customers_count'] = customers_count['count'] if customers_count else 0
        
        # عدد المنتجات
        products_count = self.fetch_one("SELECT COUNT(*) as count FROM products")
        stats['products_count'] = products_count['count'] if products_count else 0
        
        # إجمالي قيمة المخزون
        inventory_value = self.fetch_one("""
            SELECT SUM(selling_price * current_stock) as total_value 
            FROM products WHERE current_stock > 0
        """)
        stats['inventory_value'] = inventory_value['total_value'] if inventory_value and inventory_value['total_value'] else 0
        
        return stats
    
    def get_low_stock_products(self, limit: int = 10) -> List[Dict]:
        """الحصول على المنتجات منخفضة المخزون"""
        return self.fetch_all("""
            SELECT * FROM products 
            WHERE current_stock <= 5 
            ORDER BY current_stock ASC 
            LIMIT ?
        """, (limit,))
    
    # وظائف النسخ الاحتياطي
    def create_backup(self, backup_path: str = None) -> bool:
        """إنشاء نسخة احتياطية"""
        try:
            if not backup_path:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = f"backup_{timestamp}.db"
            
            # نسخ قاعدة البيانات
            import shutil
            shutil.copy2(self.db_path, backup_path)
            
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return False
    
    def get_database_info(self) -> Dict:
        """الحصول على معلومات قاعدة البيانات"""
        info = {}
        
        try:
            # حجم قاعدة البيانات
            if os.path.exists(self.db_path):
                info['size'] = os.path.getsize(self.db_path)
            else:
                info['size'] = 0
            
            # عدد الجداول
            tables = self.fetch_all("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
            """)
            info['tables_count'] = len(tables)
            info['tables'] = [table['name'] for table in tables]
            
            # إصدار SQLite
            version = self.fetch_one("SELECT sqlite_version() as version")
            info['sqlite_version'] = version['version'] if version else 'غير معروف'
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات قاعدة البيانات: {e}")
        
        return info
    
    def __enter__(self):
        """دخول السياق"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """خروج السياق"""
        self.disconnect()

# مثيل عام لمدير البيانات
data_manager = DataManager()

def get_data_manager() -> DataManager:
    """الحصول على مثيل مدير البيانات"""
    return data_manager
