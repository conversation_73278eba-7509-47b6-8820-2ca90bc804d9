#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشاكل إدارة النظام
System Management Issues Fix Test
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_database_management():
    """اختبار إدارة قاعدة البيانات"""
    print("🗄️ اختبار إدارة قاعدة البيانات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from database.database_admin_gui import DatabaseAdminMainWindow
        
        # اختبار إنشاء النافذة
        db_admin = DatabaseAdminMainWindow()
        print("   ✅ تم إنشاء نافذة إدارة قاعدة البيانات بنجاح")
        
        # اختبار الوظائف الأساسية
        if hasattr(db_admin, 'setup_ui'):
            print("   ✅ دالة إعداد الواجهة موجودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إدارة قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_security_settings():
    """اختبار إعدادات الأمان"""
    print("\n🔒 اختبار إعدادات الأمان...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.security_settings_module import SecuritySettingsWidget, PasswordPolicyDialog
        from database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إنشاء ويدجت الأمان
        security_widget = SecuritySettingsWidget(db_manager)
        print("   ✅ تم إنشاء ويدجت إعدادات الأمان بنجاح")
        
        # اختبار حوار سياسة كلمات المرور
        password_dialog = PasswordPolicyDialog(db_manager)
        print("   ✅ تم إنشاء حوار سياسة كلمات المرور بنجاح")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إعدادات الأمان: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_system_settings():
    """اختبار إعدادات النظام"""
    print("\n⚙️ اختبار إعدادات النظام...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.system_settings_module import SystemSettingsWidget, SystemSettingsDialog
        from database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إنشاء ويدجت إعدادات النظام
        settings_widget = SystemSettingsWidget(db_manager)
        print("   ✅ تم إنشاء ويدجت إعدادات النظام بنجاح")
        
        # اختبار حوار إعدادات النظام
        settings_dialog = SystemSettingsDialog(db_manager)
        print("   ✅ تم إنشاء حوار إعدادات النظام بنجاح")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إعدادات النظام: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backup_management():
    """اختبار إدارة النسخ الاحتياطي"""
    print("\n💾 اختبار إدارة النسخ الاحتياطي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.backup_module import BackupWidget, BackupScheduleDialog
        from database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إنشاء ويدجت النسخ الاحتياطي
        backup_widget = BackupWidget(db_manager)
        print("   ✅ تم إنشاء ويدجت النسخ الاحتياطي بنجاح")
        
        # اختبار حوار جدولة النسخ
        schedule_dialog = BackupScheduleDialog(db_manager)
        print("   ✅ تم إنشاء حوار جدولة النسخ الاحتياطي بنجاح")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إدارة النسخ الاحتياطي: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_management():
    """اختبار إدارة المستخدمين"""
    print("\n👥 اختبار إدارة المستخدمين...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.system_management_module import SystemManagementModule, UserManagementWidget
        from database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إنشاء وحدة إدارة النظام
        system_module = SystemManagementModule(db_manager)
        print("   ✅ تم إنشاء وحدة إدارة النظام بنجاح")
        
        # اختبار إنشاء ويدجت إدارة المستخدمين
        user_widget = UserManagementWidget(db_manager)
        print("   ✅ تم إنشاء ويدجت إدارة المستخدمين بنجاح")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إدارة المستخدمين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_system_integration():
    """اختبار تكامل النظام الرئيسي"""
    print("\n🔗 اختبار تكامل النظام الرئيسي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        import main_system
        from main_system import MainSystemWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainSystemWindow()
        print("   ✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار وجود دوال إدارة النظام المُصلحة
        system_functions = [
            'open_user_management',
            'open_user_permissions', 
            'open_backup_management',
            'open_database_management',
            'open_technical_system_settings'
        ]
        
        all_functions_exist = True
        for func_name in system_functions:
            if hasattr(main_window, func_name):
                print(f"   ✅ دالة {func_name} موجودة")
            else:
                print(f"   ❌ دالة {func_name} غير موجودة")
                all_functions_exist = False
        
        return all_functions_exist
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التكامل: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار إصلاح مشاكل إدارة النظام")
    print("=" * 70)
    
    tests = [
        ("إدارة قاعدة البيانات", test_database_management),
        ("إعدادات الأمان", test_security_settings),
        ("إعدادات النظام", test_system_settings),
        ("إدارة النسخ الاحتياطي", test_backup_management),
        ("إدارة المستخدمين", test_user_management),
        ("تكامل النظام الرئيسي", test_main_system_integration),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم إصلاح جميع مشاكل إدارة النظام بنجاح!")
        print("✅ جميع الوحدات تعمل بدون أخطاء")
        
        print("\n🔧 الوظائف المُصلحة:")
        print("   🗄️ إدارة قاعدة البيانات - مُصلحة ✅")
        print("   🔒 إعدادات الأمان - مُصلحة ✅")
        print("   ⚙️ إعدادات النظام - مُصلحة ✅")
        print("   💾 إدارة النسخ الاحتياطي - مُصلحة ✅")
        print("   👥 إدارة المستخدمين - تعمل ✅")
        
        print("\n🚀 كيفية الاستخدام:")
        print("   1. شغل النظام: python src/main_system.py")
        print("   2. انقر على 'إدارة النظام' في الشجرة")
        print("   3. اختر الوظيفة المطلوبة - ستعمل بدون أخطاء")
        
    else:
        print("\n⚠️ لا تزال هناك مشاكل تحتاج إلى إصلاح")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
