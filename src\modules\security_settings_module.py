#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إعدادات الأمان
Security Settings Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QDoubleSpinBox, QCheckBox,
                             QDateEdit, QSplitter, QFrame, QDialogButtonBox,
                             QScrollArea, QProgressBar, QSlider)
from PyQt5.QtCore import Qt, QDate, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor
import hashlib
from datetime import datetime, timedelta

try:
    from ..database.sqlite_manager import SQLiteManager
except ImportError:
    # في حالة التشغيل المباشر
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class PasswordPolicyDialog(QDialog):
    """حوار سياسة كلمات المرور"""
    
    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.setup_connections()
        self.load_current_policy()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("سياسة كلمات المرور")
        self.setFixedSize(500, 600)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # العنوان
        title = QLabel("🔐 إعدادات سياسة كلمات المرور")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        main_layout.addWidget(title)
        
        # النموذج
        form_layout = QFormLayout()
        
        # الحد الأدنى لطول كلمة المرور
        self.min_length_spin = QSpinBox()
        self.min_length_spin.setRange(4, 50)
        self.min_length_spin.setValue(8)
        form_layout.addRow("الحد الأدنى للطول:", self.min_length_spin)
        
        # يجب أن تحتوي على أحرف كبيرة
        self.require_uppercase = QCheckBox("يجب أن تحتوي على أحرف كبيرة")
        form_layout.addRow("", self.require_uppercase)
        
        # يجب أن تحتوي على أحرف صغيرة
        self.require_lowercase = QCheckBox("يجب أن تحتوي على أحرف صغيرة")
        form_layout.addRow("", self.require_lowercase)
        
        # يجب أن تحتوي على أرقام
        self.require_numbers = QCheckBox("يجب أن تحتوي على أرقام")
        form_layout.addRow("", self.require_numbers)
        
        # يجب أن تحتوي على رموز خاصة
        self.require_symbols = QCheckBox("يجب أن تحتوي على رموز خاصة")
        form_layout.addRow("", self.require_symbols)
        
        # منع استخدام كلمات المرور الشائعة
        self.prevent_common = QCheckBox("منع كلمات المرور الشائعة")
        self.prevent_common.setChecked(True)
        form_layout.addRow("", self.prevent_common)
        
        # منع استخدام معلومات المستخدم
        self.prevent_user_info = QCheckBox("منع استخدام معلومات المستخدم")
        self.prevent_user_info.setChecked(True)
        form_layout.addRow("", self.prevent_user_info)
        
        # مدة انتهاء كلمة المرور (بالأيام)
        self.password_expiry_spin = QSpinBox()
        self.password_expiry_spin.setRange(0, 365)
        self.password_expiry_spin.setValue(90)
        self.password_expiry_spin.setSuffix(" يوم")
        self.password_expiry_spin.setSpecialValueText("لا تنتهي")
        form_layout.addRow("انتهاء كلمة المرور:", self.password_expiry_spin)
        
        # عدد المحاولات المسموحة
        self.max_attempts_spin = QSpinBox()
        self.max_attempts_spin.setRange(1, 10)
        self.max_attempts_spin.setValue(3)
        form_layout.addRow("المحاولات المسموحة:", self.max_attempts_spin)
        
        # مدة القفل بعد المحاولات الفاشلة (بالدقائق)
        self.lockout_duration_spin = QSpinBox()
        self.lockout_duration_spin.setRange(1, 1440)
        self.lockout_duration_spin.setValue(15)
        self.lockout_duration_spin.setSuffix(" دقيقة")
        form_layout.addRow("مدة القفل:", self.lockout_duration_spin)
        
        main_layout.addLayout(form_layout)
        
        # مؤشر قوة كلمة المرور التجريبية
        strength_group = QGroupBox("اختبار قوة كلمة المرور")
        strength_layout = QVBoxLayout()
        
        self.test_password_edit = QLineEdit()
        self.test_password_edit.setPlaceholderText("أدخل كلمة مرور للاختبار...")
        self.test_password_edit.setEchoMode(QLineEdit.Password)
        strength_layout.addWidget(self.test_password_edit)
        
        self.strength_bar = QProgressBar()
        self.strength_bar.setRange(0, 100)
        strength_layout.addWidget(self.strength_bar)
        
        self.strength_label = QLabel("قوة كلمة المرور: غير محددة")
        strength_layout.addWidget(self.strength_label)
        
        strength_group.setLayout(strength_layout)
        main_layout.addWidget(strength_group)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        main_layout.addWidget(button_box)
        
        self.setLayout(main_layout)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLineEdit, QSpinBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QSpinBox:focus {
                border-color: #2196F3;
            }
            QPushButton {
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-weight: bold;
                color: #333;
            
            
                padding: 5px;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QCheckBox {
                font-weight: normal;
                color: #555;
            }
        """)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_policy)
        self.cancel_btn.clicked.connect(self.reject)
        self.test_password_edit.textChanged.connect(self.test_password_strength)
    
    def load_current_policy(self):
        """تحميل السياسة الحالية"""
        try:
            # تحميل الإعدادات من قاعدة البيانات
            settings_query = """
                SELECT setting_key, setting_value FROM system_settings 
                WHERE setting_key IN (
                    'password_min_length', 'password_require_uppercase', 'password_require_lowercase',
                    'password_require_numbers', 'password_require_symbols', 'password_prevent_common',
                    'password_prevent_user_info', 'password_expiry_days', 'max_login_attempts',
                    'lockout_duration_minutes'
                )
            """
            
            settings = self.db_manager.execute_query(settings_query)
            settings_dict = {s['setting_key']: s['setting_value'] for s in settings}
            
            # تطبيق الإعدادات على الواجهة
            self.min_length_spin.setValue(int(settings_dict.get('password_min_length', '8')))
            self.require_uppercase.setChecked(settings_dict.get('password_require_uppercase', '0') == '1')
            self.require_lowercase.setChecked(settings_dict.get('password_require_lowercase', '0') == '1')
            self.require_numbers.setChecked(settings_dict.get('password_require_numbers', '0') == '1')
            self.require_symbols.setChecked(settings_dict.get('password_require_symbols', '0') == '1')
            self.prevent_common.setChecked(settings_dict.get('password_prevent_common', '1') == '1')
            self.prevent_user_info.setChecked(settings_dict.get('password_prevent_user_info', '1') == '1')
            self.password_expiry_spin.setValue(int(settings_dict.get('password_expiry_days', '90')))
            self.max_attempts_spin.setValue(int(settings_dict.get('max_login_attempts', '3')))
            self.lockout_duration_spin.setValue(int(settings_dict.get('lockout_duration_minutes', '15')))
            
        except Exception as e:
            print(f"خطأ في تحميل سياسة كلمات المرور: {e}")
    
    def save_policy(self):
        """حفظ السياسة"""
        try:
            # إعدادات السياسة
            policy_settings = [
                ('password_min_length', str(self.min_length_spin.value())),
                ('password_require_uppercase', '1' if self.require_uppercase.isChecked() else '0'),
                ('password_require_lowercase', '1' if self.require_lowercase.isChecked() else '0'),
                ('password_require_numbers', '1' if self.require_numbers.isChecked() else '0'),
                ('password_require_symbols', '1' if self.require_symbols.isChecked() else '0'),
                ('password_prevent_common', '1' if self.prevent_common.isChecked() else '0'),
                ('password_prevent_user_info', '1' if self.prevent_user_info.isChecked() else '0'),
                ('password_expiry_days', str(self.password_expiry_spin.value())),
                ('max_login_attempts', str(self.max_attempts_spin.value())),
                ('lockout_duration_minutes', str(self.lockout_duration_spin.value()))
            ]
            
            # حفظ أو تحديث الإعدادات
            for key, value in policy_settings:
                query = """
                    INSERT OR REPLACE INTO system_settings 
                    (setting_key, setting_value, setting_type, description, category, is_editable)
                    VALUES (?, ?, 'string', ?, 'security', 1)
                """
                description = self.get_setting_description(key)
                self.db_manager.execute_update(query, (key, value, description))
            
            QMessageBox.information(self, "نجح", "تم حفظ سياسة كلمات المرور بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ السياسة:\n{str(e)}")
    
    def get_setting_description(self, key):
        """الحصول على وصف الإعداد"""
        descriptions = {
            'password_min_length': 'الحد الأدنى لطول كلمة المرور',
            'password_require_uppercase': 'يجب أن تحتوي على أحرف كبيرة',
            'password_require_lowercase': 'يجب أن تحتوي على أحرف صغيرة',
            'password_require_numbers': 'يجب أن تحتوي على أرقام',
            'password_require_symbols': 'يجب أن تحتوي على رموز خاصة',
            'password_prevent_common': 'منع كلمات المرور الشائعة',
            'password_prevent_user_info': 'منع استخدام معلومات المستخدم',
            'password_expiry_days': 'مدة انتهاء كلمة المرور بالأيام',
            'max_login_attempts': 'عدد المحاولات المسموحة لتسجيل الدخول',
            'lockout_duration_minutes': 'مدة القفل بعد المحاولات الفاشلة بالدقائق'
        }
        return descriptions.get(key, key)
    
    def test_password_strength(self):
        """اختبار قوة كلمة المرور"""
        password = self.test_password_edit.text()
        
        if not password:
            self.strength_bar.setValue(0)
            self.strength_label.setText("قوة كلمة المرور: غير محددة")
            self.strength_bar.setStyleSheet("")
            return
        
        score = 0
        feedback = []
        
        # طول كلمة المرور
        if len(password) >= self.min_length_spin.value():
            score += 20
        else:
            feedback.append(f"يجب أن تكون {self.min_length_spin.value()} أحرف على الأقل")
        
        # أحرف كبيرة
        if self.require_uppercase.isChecked():
            if any(c.isupper() for c in password):
                score += 15
            else:
                feedback.append("يجب أن تحتوي على أحرف كبيرة")
        
        # أحرف صغيرة
        if self.require_lowercase.isChecked():
            if any(c.islower() for c in password):
                score += 15
            else:
                feedback.append("يجب أن تحتوي على أحرف صغيرة")
        
        # أرقام
        if self.require_numbers.isChecked():
            if any(c.isdigit() for c in password):
                score += 15
            else:
                feedback.append("يجب أن تحتوي على أرقام")
        
        # رموز خاصة
        if self.require_symbols.isChecked():
            if any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
                score += 15
            else:
                feedback.append("يجب أن تحتوي على رموز خاصة")
        
        # تنوع الأحرف
        if len(set(password)) > len(password) * 0.7:
            score += 10
        
        # طول إضافي
        if len(password) > 12:
            score += 10
        
        # تحديث شريط التقدم
        self.strength_bar.setValue(min(score, 100))
        
        # تحديد مستوى القوة
        if score < 30:
            strength = "ضعيفة جداً"
            color = "#e74c3c"
        elif score < 50:
            strength = "ضعيفة"
            color = "#f39c12"
        elif score < 70:
            strength = "متوسطة"
            color = "#f1c40f"
        elif score < 90:
            strength = "قوية"
            color = "#27ae60"
        else:
            strength = "قوية جداً"
            color = "#2ecc71"
        
        self.strength_label.setText(f"قوة كلمة المرور: {strength}")
        self.strength_bar.setStyleSheet(f"""
            QProgressBar::chunk {{
                background-color: {color};
            }}
        """)
        
        # عرض التغذية الراجعة
        if feedback:
            self.strength_label.setToolTip("المتطلبات المفقودة:\n" + "\n".join(feedback))
        else:
            self.strength_label.setToolTip("كلمة المرور تلبي جميع المتطلبات")


class SecurityAuditDialog(QDialog):
    """حوار مراجعة الأمان"""

    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.setup_connections()
        self.load_audit_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("مراجعة الأمان")
        self.setFixedSize(700, 500)
        self.setModal(True)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # العنوان
        title = QLabel("🔍 مراجعة أمان النظام")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        main_layout.addWidget(title)

        # التبويبات
        self.tab_widget = QTabWidget()

        # تبويب المستخدمين
        self.users_tab = self.create_users_audit_tab()
        self.tab_widget.addTab(self.users_tab, "👥 المستخدمين")

        # تبويب كلمات المرور
        self.passwords_tab = self.create_passwords_audit_tab()
        self.tab_widget.addTab(self.passwords_tab, "🔐 كلمات المرور")

        # تبويب الجلسات
        self.sessions_tab = self.create_sessions_audit_tab()
        self.tab_widget.addTab(self.sessions_tab, "🔗 الجلسات")

        main_layout.addWidget(self.tab_widget)

        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.refresh_btn = button_box.addButton("🔄 تحديث", QDialogButtonBox.ActionRole)
        self.export_btn = button_box.addButton("📤 تصدير", QDialogButtonBox.ActionRole)
        self.close_btn = button_box.addButton("إغلاق", QDialogButtonBox.RejectRole)

        main_layout.addWidget(button_box)

        self.setLayout(main_layout)

        # تطبيق التنسيق
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """)

    def create_users_audit_tab(self):
        """إنشاء تبويب مراجعة المستخدمين"""
        widget = QWidget()
        layout = QVBoxLayout()

        # جدول المستخدمين
        self.users_table = QTableWidget()
        columns = ["اسم المستخدم", "الاسم الكامل", "الدور", "الحالة", "آخر دخول", "عدد المحاولات الفاشلة"]
        self.users_table.setColumnCount(len(columns))
        self.users_table.setHorizontalHeaderLabels(columns)

        # تنسيق الجدول
        self.users_table.setAlternatingRowColors(True)
        self.users_table.setSelectionBehavior(QTableWidget.SelectRows)

        layout.addWidget(self.users_table)
        widget.setLayout(layout)

        return widget

    def create_passwords_audit_tab(self):
        """إنشاء تبويب مراجعة كلمات المرور"""
        widget = QWidget()
        layout = QVBoxLayout()

        # جدول كلمات المرور
        self.passwords_table = QTableWidget()
        columns = ["اسم المستخدم", "تاريخ آخر تغيير", "الأيام المتبقية", "قوة كلمة المرور", "الحالة"]
        self.passwords_table.setColumnCount(len(columns))
        self.passwords_table.setHorizontalHeaderLabels(columns)

        # تنسيق الجدول
        self.passwords_table.setAlternatingRowColors(True)
        self.passwords_table.setSelectionBehavior(QTableWidget.SelectRows)

        layout.addWidget(self.passwords_table)
        widget.setLayout(layout)

        return widget

    def create_sessions_audit_tab(self):
        """إنشاء تبويب مراجعة الجلسات"""
        widget = QWidget()
        layout = QVBoxLayout()

        # جدول الجلسات
        self.sessions_table = QTableWidget()
        columns = ["اسم المستخدم", "وقت تسجيل الدخول", "عنوان IP", "المتصفح", "الحالة"]
        self.sessions_table.setColumnCount(len(columns))
        self.sessions_table.setHorizontalHeaderLabels(columns)

        # تنسيق الجدول
        self.sessions_table.setAlternatingRowColors(True)
        self.sessions_table.setSelectionBehavior(QTableWidget.SelectRows)

        layout.addWidget(self.sessions_table)
        widget.setLayout(layout)

        return widget

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.refresh_btn.clicked.connect(self.load_audit_data)
        self.export_btn.clicked.connect(self.export_audit_report)
        self.close_btn.clicked.connect(self.reject)

    def load_audit_data(self):
        """تحميل بيانات المراجعة"""
        self.load_users_audit()
        self.load_passwords_audit()
        self.load_sessions_audit()

    def load_users_audit(self):
        """تحميل مراجعة المستخدمين"""
        try:
            query = """
                SELECT username, full_name, role, is_active,
                       last_login, failed_attempts
                FROM users
                ORDER BY last_login DESC
            """

            users = self.db_manager.execute_query(query)
            self.users_table.setRowCount(len(users))

            for row, user in enumerate(users):
                self.users_table.setItem(row, 0, QTableWidgetItem(user.get('username', '')))
                self.users_table.setItem(row, 1, QTableWidgetItem(user.get('full_name', '')))

                # الدور
                role = user.get('role', '')
                role_text = {'admin': 'مدير النظام', 'accountant': 'محاسب', 'user': 'مستخدم عادي'}.get(role, role)
                self.users_table.setItem(row, 2, QTableWidgetItem(role_text))

                # الحالة
                status_text = "نشط" if user.get('is_active', True) else "غير نشط"
                status_item = QTableWidgetItem(status_text)
                if user.get('is_active', True):
                    status_item.setBackground(QColor("#d4edda"))
                else:
                    status_item.setBackground(QColor("#f8d7da"))
                self.users_table.setItem(row, 3, status_item)

                # آخر دخول
                last_login = user.get('last_login', 'لم يسجل دخول')
                self.users_table.setItem(row, 4, QTableWidgetItem(str(last_login)))

                # عدد المحاولات الفاشلة
                failed_attempts = user.get('failed_attempts', 0)
                attempts_item = QTableWidgetItem(str(failed_attempts))
                if failed_attempts > 0:
                    attempts_item.setBackground(QColor("#fff3cd"))
                self.users_table.setItem(row, 5, attempts_item)

        except Exception as e:
            print(f"خطأ في تحميل مراجعة المستخدمين: {e}")

    def load_passwords_audit(self):
        """تحميل مراجعة كلمات المرور"""
        try:
            # بيانات وهمية للعرض (يمكن تطويرها لاحقاً)
            users_query = "SELECT username, updated_at FROM users ORDER BY username"
            users = self.db_manager.execute_query(users_query)

            self.passwords_table.setRowCount(len(users))

            for row, user in enumerate(users):
                self.passwords_table.setItem(row, 0, QTableWidgetItem(user.get('username', '')))

                # تاريخ آخر تغيير (افتراضي)
                last_change = user.get('updated_at', 'غير محدد')
                self.passwords_table.setItem(row, 1, QTableWidgetItem(str(last_change)))

                # الأيام المتبقية (افتراضي 90 يوم)
                remaining_days = "90"
                self.passwords_table.setItem(row, 2, QTableWidgetItem(remaining_days))

                # قوة كلمة المرور (افتراضي)
                strength = "متوسطة"
                strength_item = QTableWidgetItem(strength)
                strength_item.setBackground(QColor("#fff3cd"))
                self.passwords_table.setItem(row, 3, strength_item)

                # الحالة
                status = "صالحة"
                status_item = QTableWidgetItem(status)
                status_item.setBackground(QColor("#d4edda"))
                self.passwords_table.setItem(row, 4, status_item)

        except Exception as e:
            print(f"خطأ في تحميل مراجعة كلمات المرور: {e}")

    def load_sessions_audit(self):
        """تحميل مراجعة الجلسات"""
        try:
            # بيانات وهمية للعرض (يمكن تطويرها لاحقاً)
            sessions_data = [
                ("admin", "2025-01-15 10:30:00", "192.168.1.100", "Chrome", "نشطة"),
                ("user1", "2025-01-15 09:15:00", "192.168.1.101", "Firefox", "منتهية"),
                ("accountant", "2025-01-15 08:45:00", "192.168.1.102", "Edge", "نشطة")
            ]

            self.sessions_table.setRowCount(len(sessions_data))

            for row, session in enumerate(sessions_data):
                for col, value in enumerate(session):
                    item = QTableWidgetItem(str(value))
                    if col == 4:  # عمود الحالة
                        if value == "نشطة":
                            item.setBackground(QColor("#d4edda"))
                        else:
                            item.setBackground(QColor("#f8d7da"))
                    self.sessions_table.setItem(row, col, item)

        except Exception as e:
            print(f"خطأ في تحميل مراجعة الجلسات: {e}")

    def export_audit_report(self):
        """تصدير تقرير المراجعة"""
        QMessageBox.information(self, "تصدير التقرير", "وظيفة تصدير تقرير المراجعة قيد التطوير")


class SecuritySettingsWidget(QWidget):
    """وحدة إعدادات الأمان"""

    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.setup_connections()
        self.load_security_status()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # العنوان
        title = QLabel("🔒 إعدادات الأمان")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        main_layout.addWidget(title)

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        self.password_policy_btn = QPushButton("🔐 سياسة كلمات المرور")
        self.security_audit_btn = QPushButton("🔍 مراجعة الأمان")
        self.backup_security_btn = QPushButton("💾 نسخ احتياطي للأمان")
        self.refresh_btn = QPushButton("🔄 تحديث")

        # تنسيق الأزرار
        buttons = [self.password_policy_btn, self.security_audit_btn,
                  self.backup_security_btn, self.refresh_btn]

        for btn in buttons:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 150px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                }
            """)

        # ألوان خاصة لبعض الأزرار
        self.security_audit_btn.setStyleSheet(self.security_audit_btn.styleSheet().replace("#e74c3c", "#3498db"))
        self.backup_security_btn.setStyleSheet(self.backup_security_btn.styleSheet().replace("#e74c3c", "#27ae60"))
        self.refresh_btn.setStyleSheet(self.refresh_btn.styleSheet().replace("#e74c3c", "#95a5a6"))

        toolbar_layout.addWidget(self.password_policy_btn)
        toolbar_layout.addWidget(self.security_audit_btn)
        toolbar_layout.addWidget(self.backup_security_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.refresh_btn)

        main_layout.addLayout(toolbar_layout)

        # لوحة حالة الأمان
        self.create_security_dashboard()
        main_layout.addWidget(self.security_dashboard)

        self.setLayout(main_layout)
        self.setStyleSheet(self.get_module_stylesheet())

    def create_security_dashboard(self):
        """إنشاء لوحة حالة الأمان"""
        self.security_dashboard = QGroupBox("📊 لوحة حالة الأمان")
        layout = QGridLayout()

        # بطاقات الحالة
        self.create_status_cards(layout)

        # مخطط الأمان
        self.create_security_chart(layout)

        self.security_dashboard.setLayout(layout)

        # تنسيق لوحة التحكم
        self.security_dashboard.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 10px 0 10px;
                color: #e74c3c;
            }
        """)

    def create_status_cards(self, layout):
        """إنشاء بطاقات الحالة"""
        # بطاقة المستخدمين النشطين
        self.active_users_card = self.create_status_card(
            "👥", "المستخدمين النشطين", "0", "#27ae60"
        )
        layout.addWidget(self.active_users_card, 0, 0)

        # بطاقة المحاولات الفاشلة
        self.failed_attempts_card = self.create_status_card(
            "⚠️", "المحاولات الفاشلة", "0", "#f39c12"
        )
        layout.addWidget(self.failed_attempts_card, 0, 1)

        # بطاقة كلمات المرور المنتهية
        self.expired_passwords_card = self.create_status_card(
            "🔐", "كلمات المرور المنتهية", "0", "#e74c3c"
        )
        layout.addWidget(self.expired_passwords_card, 0, 2)

        # بطاقة الجلسات النشطة
        self.active_sessions_card = self.create_status_card(
            "🔗", "الجلسات النشطة", "0", "#3498db"
        )
        layout.addWidget(self.active_sessions_card, 0, 3)

    def create_status_card(self, icon, title, value, color):
        """إنشاء بطاقة حالة"""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 10px;
                padding: 15px;
            }}
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                {
                color: {color
            
                padding: 5px;
            };
                font-weight: bold;
            }}
        """)

        layout = QVBoxLayout()

        # الأيقونة والقيمة
        header_layout = QHBoxLayout()
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"font-size: 24px; color: {color};")

        value_label = QLabel(value)
        value_label.setStyleSheet(f"font-size: 28px; font-weight: bold; color: {color};")
        value_label.setAlignment(Qt.AlignRight)

        header_layout.addWidget(icon_label)
        header_layout.addStretch()
        header_layout.addWidget(value_label)

        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet(f"font-size: 12px; color: {color}; font-weight: normal;")
        title_label.setAlignment(Qt.AlignCenter)

        layout.addLayout(header_layout)
        layout.addWidget(title_label)

        card.setLayout(layout)

        # حفظ مرجع لتحديث القيمة لاحقاً
        setattr(card, 'value_label', value_label)

        return card

    def create_security_chart(self, layout):
        """إنشاء مخطط الأمان"""
        chart_frame = QFrame()
        chart_frame.setFrameStyle(QFrame.StyledPanel)
        chart_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #95a5a6;
                border-radius: 10px;
                padding: 15px;
            }
        """)

        chart_layout = QVBoxLayout()

        # عنوان المخطط
        chart_title = QLabel("📈 مؤشر الأمان العام")
        chart_title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            
            
                padding: 5px;
            }
        """)
        chart_title.setAlignment(Qt.AlignCenter)
        chart_layout.addWidget(chart_title)

        # شريط التقدم للأمان العام
        self.security_score_bar = QProgressBar()
        self.security_score_bar.setRange(0, 100)
        self.security_score_bar.setValue(75)  # قيمة افتراضية
        self.security_score_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                height: 25px;
            }
            QProgressBar::chunk {
                background-color: #27ae60;
                border-radius: 3px;
            }
        """)
        chart_layout.addWidget(self.security_score_bar)

        # تسمية النتيجة
        self.security_score_label = QLabel("نتيجة الأمان: جيد (75%)")
        self.security_score_label.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 14px;
                color: #27ae60;
                font-weight: bold;
                margin-top: 5px;
            
            
                padding: 5px;
            }
        """)
        self.security_score_label.setAlignment(Qt.AlignCenter)
        chart_layout.addWidget(self.security_score_label)

        chart_frame.setLayout(chart_layout)
        layout.addWidget(chart_frame, 1, 0, 1, 4)

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.password_policy_btn.clicked.connect(self.show_password_policy)
        self.security_audit_btn.clicked.connect(self.show_security_audit)
        self.backup_security_btn.clicked.connect(self.backup_security_settings)
        self.refresh_btn.clicked.connect(self.load_security_status)

    def load_security_status(self):
        """تحميل حالة الأمان"""
        try:
            # تحميل عدد المستخدمين النشطين
            active_users_query = "SELECT COUNT(*) as count FROM users WHERE is_active = 1"
            active_users_result = self.db_manager.execute_query(active_users_query)
            active_users_count = active_users_result[0]['count'] if active_users_result else 0

            # تحديث بطاقة المستخدمين النشطين
            self.active_users_card.value_label.setText(str(active_users_count))

            # تحميل المحاولات الفاشلة (افتراضي)
            failed_attempts_count = 0  # يمكن تطويرها لاحقاً
            self.failed_attempts_card.value_label.setText(str(failed_attempts_count))

            # تحميل كلمات المرور المنتهية (افتراضي)
            expired_passwords_count = 0  # يمكن تطويرها لاحقاً
            self.expired_passwords_card.value_label.setText(str(expired_passwords_count))

            # تحميل الجلسات النشطة (افتراضي)
            active_sessions_count = 1  # افتراضي للجلسة الحالية
            self.active_sessions_card.value_label.setText(str(active_sessions_count))

            # حساب نتيجة الأمان
            self.calculate_security_score()

        except Exception as e:
            print(f"خطأ في تحميل حالة الأمان: {e}")

    def calculate_security_score(self):
        """حساب نتيجة الأمان"""
        score = 0

        # فحص سياسة كلمات المرور
        try:
            policy_query = """
                SELECT setting_key, setting_value FROM system_settings
                WHERE setting_key LIKE 'password_%' OR setting_key LIKE 'max_login_%'
            """
            policy_settings = self.db_manager.execute_query(policy_query)

            if policy_settings:
                score += 30  # نقاط لوجود سياسة كلمات المرور

            # فحص قوة السياسة
            for setting in policy_settings:
                key = setting['setting_key']
                value = setting['setting_value']

                if key == 'password_min_length' and int(value) >= 8:
                    score += 10
                elif key == 'password_require_uppercase' and value == '1':
                    score += 5
                elif key == 'password_require_numbers' and value == '1':
                    score += 5
                elif key == 'max_login_attempts' and int(value) <= 5:
                    score += 10

        except:
            pass

        # فحص المستخدمين النشطين
        active_users = int(self.active_users_card.value_label.text())
        if active_users > 0:
            score += 20

        # فحص عدم وجود محاولات فاشلة
        failed_attempts = int(self.failed_attempts_card.value_label.text())
        if failed_attempts == 0:
            score += 20

        # تحديث شريط النتيجة
        score = min(score, 100)
        self.security_score_bar.setValue(score)

        # تحديد مستوى الأمان
        if score < 40:
            level = "ضعيف"
            color = "#e74c3c"
        elif score < 70:
            level = "متوسط"
            color = "#f39c12"
        else:
            level = "جيد"
            color = "#27ae60"

        self.security_score_label.setText(f"نتيجة الأمان: {level} ({score}%)")
        self.security_score_label.setStyleSheet(f"""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                {
                font-size: 14px;
                color: {color
            
                padding: 5px;
            };
                font-weight: bold;
                margin-top: 5px;
            }}
        """)

        # تحديث لون شريط التقدم
        self.security_score_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
                height: 25px;
            }}
            QProgressBar::chunk {{
                background-color: {color};
                border-radius: 3px;
            }}
        """)

    def show_password_policy(self):
        """عرض حوار سياسة كلمات المرور"""
        dialog = PasswordPolicyDialog(self.db_manager, self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_security_status()

    def show_security_audit(self):
        """عرض حوار مراجعة الأمان"""
        dialog = SecurityAuditDialog(self.db_manager, self)
        dialog.exec_()

    def backup_security_settings(self):
        """نسخ احتياطي لإعدادات الأمان"""
        QMessageBox.information(self, "النسخ الاحتياطي", "وظيفة النسخ الاحتياطي لإعدادات الأمان قيد التطوير")

    def get_module_stylesheet(self):
        """تنسيق الوحدة"""
        return """
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """
