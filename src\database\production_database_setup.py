#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد قاعدة البيانات للإنتاج
Production Database Setup
"""

import sqlite3
import os
import hashlib
from datetime import datetime, timedelta
import json

class ProductionDatabaseSetup:
    """إعداد قاعدة البيانات للإنتاج"""
    
    def __init__(self, db_path="accounting_system.db"):
        self.db_path = db_path
        self.connection = None
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row
            print(f"✅ تم الاتصال بقاعدة البيانات: {self.db_path}")
            return True
        except Exception as e:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def create_complete_schema(self):
        """إنشاء المخطط الكامل لقاعدة البيانات"""
        try:
            cursor = self.connection.cursor()
            
            # تفعيل المفاتيح الخارجية
            cursor.execute("PRAGMA foreign_keys = ON")
            
            # إنشاء الجداول الأساسية
            self.create_users_table(cursor)
            self.create_customers_table(cursor)
            self.create_suppliers_table(cursor)
            self.create_product_categories_table(cursor)
            self.create_products_table(cursor)
            self.create_sales_invoices_table(cursor)
            self.create_sales_invoice_items_table(cursor)
            self.create_purchase_invoices_table(cursor)
            self.create_purchase_invoice_items_table(cursor)
            self.create_payments_table(cursor)
            self.create_inventory_movements_table(cursor)
            self.create_accounts_table(cursor)
            self.create_journal_entries_table(cursor)
            self.create_system_settings_table(cursor)
            self.create_activity_log_table(cursor)
            self.create_user_sessions_table(cursor)
            
            # إنشاء الفهارس
            self.create_indexes(cursor)
            
            # إنشاء المشاهد (Views)
            self.create_views(cursor)
            
            # إنشاء المحفزات (Triggers)
            self.create_triggers(cursor)
            
            self.connection.commit()
            print("✅ تم إنشاء المخطط الكامل لقاعدة البيانات")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء المخطط: {e}")
            self.connection.rollback()
            return False
    
    def create_users_table(self, cursor):
        """إنشاء جدول المستخدمين"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'manager', 'user', 'viewer')),
                status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
                phone VARCHAR(20),
                address TEXT,
                profile_image TEXT,
                last_login DATETIME,
                login_attempts INTEGER DEFAULT 0,
                locked_until DATETIME,
                two_factor_enabled BOOLEAN DEFAULT FALSE,
                two_factor_secret VARCHAR(32),
                preferences TEXT, -- JSON
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        """)
    
    def create_customers_table(self, cursor):
        """إنشاء جدول العملاء"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_code VARCHAR(20) UNIQUE NOT NULL,
                customer_name VARCHAR(100) NOT NULL,
                customer_type VARCHAR(20) DEFAULT 'individual' CHECK (customer_type IN ('individual', 'company')),
                company_name VARCHAR(100),
                tax_number VARCHAR(50),
                phone VARCHAR(20),
                mobile VARCHAR(20),
                email VARCHAR(100),
                website VARCHAR(100),
                address TEXT,
                city VARCHAR(50),
                country VARCHAR(50) DEFAULT 'اليمن',
                postal_code VARCHAR(20),
                credit_limit DECIMAL(15,2) DEFAULT 0,
                current_balance DECIMAL(15,2) DEFAULT 0,
                payment_terms INTEGER DEFAULT 30, -- أيام
                discount_percentage DECIMAL(5,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
                notes TEXT,
                tags TEXT, -- JSON array
                birth_date DATE,
                gender VARCHAR(10) CHECK (gender IN ('male', 'female')),
                preferred_language VARCHAR(10) DEFAULT 'ar',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        """)
    
    def create_suppliers_table(self, cursor):
        """إنشاء جدول الموردين"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_code VARCHAR(20) UNIQUE NOT NULL,
                supplier_name VARCHAR(100) NOT NULL,
                company_name VARCHAR(100),
                tax_number VARCHAR(50),
                phone VARCHAR(20),
                mobile VARCHAR(20),
                email VARCHAR(100),
                website VARCHAR(100),
                address TEXT,
                city VARCHAR(50),
                country VARCHAR(50) DEFAULT 'اليمن',
                postal_code VARCHAR(20),
                credit_limit DECIMAL(15,2) DEFAULT 0,
                current_balance DECIMAL(15,2) DEFAULT 0,
                payment_terms INTEGER DEFAULT 30,
                status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
                notes TEXT,
                bank_account VARCHAR(50),
                bank_name VARCHAR(100),
                contact_person VARCHAR(100),
                contact_phone VARCHAR(20),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        """)
    
    def create_product_categories_table(self, cursor):
        """إنشاء جدول فئات المنتجات"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS product_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category_name VARCHAR(100) NOT NULL,
                category_code VARCHAR(20) UNIQUE,
                parent_id INTEGER,
                description TEXT,
                image_url TEXT,
                status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
                sort_order INTEGER DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES product_categories(id)
            )
        """)
    
    def create_products_table(self, cursor):
        """إنشاء جدول المنتجات"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_code VARCHAR(50) UNIQUE NOT NULL,
                barcode VARCHAR(50) UNIQUE,
                product_name VARCHAR(100) NOT NULL,
                description TEXT,
                category_id INTEGER,
                brand VARCHAR(50),
                model VARCHAR(50),
                unit VARCHAR(20) DEFAULT 'قطعة',
                cost_price DECIMAL(15,2) DEFAULT 0,
                selling_price DECIMAL(15,2) DEFAULT 0,
                wholesale_price DECIMAL(15,2) DEFAULT 0,
                min_stock_level INTEGER DEFAULT 0,
                max_stock_level INTEGER DEFAULT 0,
                current_stock INTEGER DEFAULT 0,
                reserved_stock INTEGER DEFAULT 0,
                reorder_point INTEGER DEFAULT 0,
                reorder_quantity INTEGER DEFAULT 0,
                weight DECIMAL(10,3),
                dimensions VARCHAR(50),
                color VARCHAR(30),
                size VARCHAR(30),
                status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'discontinued')),
                is_service BOOLEAN DEFAULT FALSE,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                discount_allowed BOOLEAN DEFAULT TRUE,
                track_inventory BOOLEAN DEFAULT TRUE,
                expiry_date DATE,
                manufacture_date DATE,
                warranty_period INTEGER, -- بالأشهر
                notes TEXT,
                image_url TEXT,
                location VARCHAR(50),
                supplier_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (category_id) REFERENCES product_categories(id),
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        """)
    
    def create_sales_invoices_table(self, cursor):
        """إنشاء جدول فواتير المبيعات"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sales_invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number VARCHAR(50) UNIQUE NOT NULL,
                customer_id INTEGER NOT NULL,
                invoice_date DATE NOT NULL,
                due_date DATE,
                payment_terms INTEGER DEFAULT 30,
                subtotal DECIMAL(15,2) DEFAULT 0,
                discount_amount DECIMAL(15,2) DEFAULT 0,
                discount_percentage DECIMAL(5,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                total_amount DECIMAL(15,2) DEFAULT 0,
                paid_amount DECIMAL(15,2) DEFAULT 0,
                remaining_amount DECIMAL(15,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'pending', 'approved', 'cancelled')),
                payment_status VARCHAR(20) DEFAULT 'unpaid' CHECK (payment_status IN ('unpaid', 'partial', 'paid', 'overdue')),
                currency VARCHAR(10) DEFAULT 'YER',
                exchange_rate DECIMAL(10,4) DEFAULT 1,
                notes TEXT,
                terms_conditions TEXT,
                reference_number VARCHAR(50),
                delivery_date DATE,
                delivery_address TEXT,
                printed_count INTEGER DEFAULT 0,
                last_printed_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                approved_by INTEGER,
                approved_at DATETIME,
                FOREIGN KEY (customer_id) REFERENCES customers(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (approved_by) REFERENCES users(id)
            )
        """)
    
    def create_sales_invoice_items_table(self, cursor):
        """إنشاء جدول تفاصيل فواتير المبيعات"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sales_invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity DECIMAL(10,3) NOT NULL,
                unit_price DECIMAL(15,2) NOT NULL,
                discount_percentage DECIMAL(5,2) DEFAULT 0,
                discount_amount DECIMAL(15,2) DEFAULT 0,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) NOT NULL,
                notes TEXT,
                line_number INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES sales_invoices(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
        """)
    
    def create_purchase_invoices_table(self, cursor):
        """إنشاء جدول فواتير المشتريات"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS purchase_invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number VARCHAR(50) UNIQUE NOT NULL,
                supplier_id INTEGER NOT NULL,
                invoice_date DATE NOT NULL,
                due_date DATE,
                payment_terms INTEGER DEFAULT 30,
                subtotal DECIMAL(15,2) DEFAULT 0,
                discount_amount DECIMAL(15,2) DEFAULT 0,
                discount_percentage DECIMAL(5,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                total_amount DECIMAL(15,2) DEFAULT 0,
                paid_amount DECIMAL(15,2) DEFAULT 0,
                remaining_amount DECIMAL(15,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'pending', 'approved', 'cancelled')),
                payment_status VARCHAR(20) DEFAULT 'unpaid' CHECK (payment_status IN ('unpaid', 'partial', 'paid', 'overdue')),
                currency VARCHAR(10) DEFAULT 'YER',
                exchange_rate DECIMAL(10,4) DEFAULT 1,
                notes TEXT,
                reference_number VARCHAR(50),
                delivery_date DATE,
                received_date DATE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                approved_by INTEGER,
                approved_at DATETIME,
                FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (approved_by) REFERENCES users(id)
            )
        """)
    
    def create_purchase_invoice_items_table(self, cursor):
        """إنشاء جدول تفاصيل فواتير المشتريات"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS purchase_invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity DECIMAL(10,3) NOT NULL,
                unit_price DECIMAL(15,2) NOT NULL,
                discount_percentage DECIMAL(5,2) DEFAULT 0,
                discount_amount DECIMAL(15,2) DEFAULT 0,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) NOT NULL,
                notes TEXT,
                line_number INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES purchase_invoices(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
        """)
    
    def create_payments_table(self, cursor):
        """إنشاء جدول المدفوعات"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                payment_number VARCHAR(50) UNIQUE NOT NULL,
                payment_type VARCHAR(20) NOT NULL CHECK (payment_type IN ('receipt', 'payment')),
                reference_type VARCHAR(20) NOT NULL CHECK (reference_type IN ('sales_invoice', 'purchase_invoice', 'customer', 'supplier')),
                reference_id INTEGER NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                payment_method VARCHAR(20) DEFAULT 'cash' CHECK (payment_method IN ('cash', 'bank', 'check', 'card', 'transfer')),
                payment_date DATE NOT NULL,
                currency VARCHAR(10) DEFAULT 'YER',
                exchange_rate DECIMAL(10,4) DEFAULT 1,
                notes TEXT,
                check_number VARCHAR(50),
                check_date DATE,
                bank_name VARCHAR(100),
                account_number VARCHAR(50),
                transaction_reference VARCHAR(100),
                status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'cancelled', 'bounced')),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        """)
    
    def create_inventory_movements_table(self, cursor):
        """إنشاء جدول حركات المخزون"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS inventory_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                movement_type VARCHAR(20) NOT NULL CHECK (movement_type IN ('in', 'out', 'adjustment', 'transfer')),
                quantity DECIMAL(10,3) NOT NULL,
                unit_cost DECIMAL(15,2),
                total_cost DECIMAL(15,2),
                reference_type VARCHAR(20) CHECK (reference_type IN ('sales_invoice', 'purchase_invoice', 'adjustment', 'transfer')),
                reference_id INTEGER,
                movement_date DATE NOT NULL,
                notes TEXT,
                location_from VARCHAR(50),
                location_to VARCHAR(50),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (product_id) REFERENCES products(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        """)
    
    def create_accounts_table(self, cursor):
        """إنشاء جدول الحسابات"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_code VARCHAR(20) UNIQUE NOT NULL,
                account_name VARCHAR(100) NOT NULL,
                account_type VARCHAR(20) NOT NULL CHECK (account_type IN ('asset', 'liability', 'equity', 'revenue', 'expense')),
                parent_id INTEGER,
                level INTEGER DEFAULT 1,
                is_active BOOLEAN DEFAULT TRUE,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES accounts(id)
            )
        """)
    
    def create_journal_entries_table(self, cursor):
        """إنشاء جدول القيود المحاسبية"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS journal_entries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entry_number VARCHAR(50) UNIQUE NOT NULL,
                entry_date DATE NOT NULL,
                description TEXT NOT NULL,
                reference_type VARCHAR(20),
                reference_id INTEGER,
                total_debit DECIMAL(15,2) DEFAULT 0,
                total_credit DECIMAL(15,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'posted', 'cancelled')),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                posted_at DATETIME,
                posted_by INTEGER,
                FOREIGN KEY (created_by) REFERENCES users(id),
                FOREIGN KEY (posted_by) REFERENCES users(id)
            )
        """)
    
    def create_system_settings_table(self, cursor):
        """إنشاء جدول إعدادات النظام"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type VARCHAR(20) DEFAULT 'string' CHECK (setting_type IN ('string', 'integer', 'decimal', 'boolean', 'json')),
                description TEXT,
                category VARCHAR(50),
                is_public BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_by INTEGER,
                FOREIGN KEY (updated_by) REFERENCES users(id)
            )
        """)
    
    def create_activity_log_table(self, cursor):
        """إنشاء جدول سجل النشاطات"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action VARCHAR(50) NOT NULL,
                table_name VARCHAR(50),
                record_id INTEGER,
                old_values TEXT, -- JSON
                new_values TEXT, -- JSON
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        """)
    
    def create_user_sessions_table(self, cursor):
        """إنشاء جدول جلسات المستخدمين"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                session_token VARCHAR(255) UNIQUE NOT NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        """)
    
    def create_indexes(self, cursor):
        """إنشاء الفهارس لتحسين الأداء"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)",
            "CREATE INDEX IF NOT EXISTS idx_customers_code ON customers(customer_code)",
            "CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name)",
            "CREATE INDEX IF NOT EXISTS idx_customers_status ON customers(status)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_code ON suppliers(supplier_code)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(supplier_name)",
            "CREATE INDEX IF NOT EXISTS idx_products_code ON products(product_code)",
            "CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)",
            "CREATE INDEX IF NOT EXISTS idx_products_name ON products(product_name)",
            "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id)",
            "CREATE INDEX IF NOT EXISTS idx_sales_invoices_number ON sales_invoices(invoice_number)",
            "CREATE INDEX IF NOT EXISTS idx_sales_invoices_customer ON sales_invoices(customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_sales_invoices_date ON sales_invoices(invoice_date)",
            "CREATE INDEX IF NOT EXISTS idx_sales_invoices_status ON sales_invoices(status)",
            "CREATE INDEX IF NOT EXISTS idx_purchase_invoices_number ON purchase_invoices(invoice_number)",
            "CREATE INDEX IF NOT EXISTS idx_purchase_invoices_supplier ON purchase_invoices(supplier_id)",
            "CREATE INDEX IF NOT EXISTS idx_purchase_invoices_date ON purchase_invoices(invoice_date)",
            "CREATE INDEX IF NOT EXISTS idx_payments_reference ON payments(reference_type, reference_id)",
            "CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(payment_date)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_movements_product ON inventory_movements(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_movements_date ON inventory_movements(movement_date)",
            "CREATE INDEX IF NOT EXISTS idx_activity_log_user ON activity_log(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_activity_log_date ON activity_log(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)",
            "CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON user_sessions(user_id)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def create_views(self, cursor):
        """إنشاء المشاهد (Views)"""
        # مشهد تفاصيل فواتير المبيعات مع أسماء المنتجات
        cursor.execute("""
            CREATE VIEW IF NOT EXISTS sales_invoice_details AS
            SELECT 
                si.id,
                si.invoice_number,
                si.invoice_date,
                c.customer_name,
                c.phone as customer_phone,
                si.total_amount,
                si.paid_amount,
                si.remaining_amount,
                si.status,
                si.payment_status,
                GROUP_CONCAT(p.product_name, ', ') as products
            FROM sales_invoices si
            JOIN customers c ON si.customer_id = c.id
            LEFT JOIN sales_invoice_items sii ON si.id = sii.invoice_id
            LEFT JOIN products p ON sii.product_id = p.id
            GROUP BY si.id
        """)
        
        # مشهد أرصدة العملاء
        cursor.execute("""
            CREATE VIEW IF NOT EXISTS customer_balances AS
            SELECT 
                c.id,
                c.customer_code,
                c.customer_name,
                c.phone,
                c.email,
                COALESCE(SUM(CASE WHEN si.status = 'approved' THEN si.remaining_amount ELSE 0 END), 0) as outstanding_balance,
                COUNT(si.id) as total_invoices,
                MAX(si.invoice_date) as last_invoice_date
            FROM customers c
            LEFT JOIN sales_invoices si ON c.id = si.customer_id
            WHERE c.status = 'active'
            GROUP BY c.id
        """)
        
        # مشهد مستويات المخزون
        cursor.execute("""
            CREATE VIEW IF NOT EXISTS inventory_levels AS
            SELECT 
                p.id,
                p.product_code,
                p.product_name,
                p.current_stock,
                p.min_stock_level,
                p.max_stock_level,
                p.reorder_point,
                CASE 
                    WHEN p.current_stock <= p.reorder_point THEN 'low'
                    WHEN p.current_stock >= p.max_stock_level THEN 'high'
                    ELSE 'normal'
                END as stock_status,
                p.cost_price * p.current_stock as inventory_value
            FROM products p
            WHERE p.track_inventory = TRUE AND p.status = 'active'
        """)
    
    def create_triggers(self, cursor):
        """إنشاء المحفزات (Triggers)"""
        # محفز تحديث تاريخ التعديل
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_users_timestamp 
            AFTER UPDATE ON users
            BEGIN
                UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_customers_timestamp 
            AFTER UPDATE ON customers
            BEGIN
                UPDATE customers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        """)
        
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_products_timestamp 
            AFTER UPDATE ON products
            BEGIN
                UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        """)
        
        # محفز تحديث المخزون عند إضافة عنصر فاتورة مبيعات
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_inventory_on_sale 
            AFTER INSERT ON sales_invoice_items
            WHEN (SELECT status FROM sales_invoices WHERE id = NEW.invoice_id) = 'approved'
            BEGIN
                UPDATE products 
                SET current_stock = current_stock - NEW.quantity
                WHERE id = NEW.product_id AND track_inventory = TRUE;
                
                INSERT INTO inventory_movements (
                    product_id, movement_type, quantity, reference_type, reference_id, movement_date, created_by
                ) VALUES (
                    NEW.product_id, 'out', NEW.quantity, 'sales_invoice', NEW.invoice_id, 
                    (SELECT invoice_date FROM sales_invoices WHERE id = NEW.invoice_id),
                    (SELECT created_by FROM sales_invoices WHERE id = NEW.invoice_id)
                );
            END
        """)
        
        # محفز تحديث المخزون عند إضافة عنصر فاتورة مشتريات
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_inventory_on_purchase 
            AFTER INSERT ON purchase_invoice_items
            WHEN (SELECT status FROM purchase_invoices WHERE id = NEW.invoice_id) = 'approved'
            BEGIN
                UPDATE products 
                SET current_stock = current_stock + NEW.quantity
                WHERE id = NEW.product_id AND track_inventory = TRUE;
                
                INSERT INTO inventory_movements (
                    product_id, movement_type, quantity, reference_type, reference_id, movement_date, created_by
                ) VALUES (
                    NEW.product_id, 'in', NEW.quantity, 'purchase_invoice', NEW.invoice_id,
                    (SELECT invoice_date FROM purchase_invoices WHERE id = NEW.invoice_id),
                    (SELECT created_by FROM purchase_invoices WHERE id = NEW.invoice_id)
                );
            END
        """)
    
    def insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        try:
            cursor = self.connection.cursor()
            
            # إدراج المستخدم الافتراضي
            self.insert_default_admin(cursor)
            
            # إدراج الإعدادات الافتراضية
            self.insert_default_settings(cursor)
            
            # إدراج فئات المنتجات الافتراضية
            self.insert_default_categories(cursor)
            
            # إدراج الحسابات الافتراضية
            self.insert_default_accounts(cursor)
            
            # إدراج بيانات تجريبية
            self.insert_sample_data(cursor)
            
            self.connection.commit()
            print("✅ تم إدراج البيانات الافتراضية")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إدراج البيانات الافتراضية: {e}")
            self.connection.rollback()
            return False
    
    def insert_default_admin(self, cursor):
        """إدراج المستخدم الافتراضي"""
        # فحص وجود المستخدم
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        if cursor.fetchone():
            return
        
        # تشفير كلمة المرور
        password_hash = hashlib.sha256("admin123".encode()).hexdigest()
        
        cursor.execute("""
            INSERT INTO users (username, email, password_hash, full_name, role, status)
            VALUES (?, ?, ?, ?, ?, ?)
        """, ('admin', '<EMAIL>', password_hash, 'مدير النظام', 'admin', 'active'))
    
    def insert_default_settings(self, cursor):
        """إدراج الإعدادات الافتراضية"""
        settings = [
            ('company_name', 'شركة المحاسبة المتقدمة', 'string', 'اسم الشركة', 'company', True),
            ('company_address', 'صنعاء - اليمن', 'string', 'عنوان الشركة', 'company', True),
            ('company_phone', '+967-1-123456', 'string', 'هاتف الشركة', 'company', True),
            ('company_email', '<EMAIL>', 'string', 'بريد الشركة', 'company', True),
            ('tax_rate', '0', 'decimal', 'معدل الضريبة الافتراضي', 'tax', True),
            ('currency', 'YER', 'string', 'العملة الافتراضية', 'general', True),
            ('currency_symbol', 'ر.ي', 'string', 'رمز العملة', 'general', True),
            ('date_format', 'Y-m-d', 'string', 'تنسيق التاريخ', 'general', False),
            ('decimal_places', '2', 'integer', 'عدد الخانات العشرية', 'general', False),
            ('invoice_prefix', 'INV', 'string', 'بادئة رقم الفاتورة', 'invoicing', False),
            ('next_invoice_number', '1', 'integer', 'رقم الفاتورة التالي', 'invoicing', False),
            ('backup_enabled', 'true', 'boolean', 'تفعيل النسخ الاحتياطي', 'system', False),
            ('backup_interval', '24', 'integer', 'فترة النسخ الاحتياطي بالساعات', 'system', False),
            ('session_timeout', '60', 'integer', 'انتهاء الجلسة بالدقائق', 'security', False),
            ('max_login_attempts', '5', 'integer', 'الحد الأقصى لمحاولات تسجيل الدخول', 'security', False)
        ]
        
        for setting in settings:
            cursor.execute("""
                INSERT OR IGNORE INTO system_settings 
                (setting_key, setting_value, setting_type, description, category, is_public)
                VALUES (?, ?, ?, ?, ?, ?)
            """, setting)
    
    def insert_default_categories(self, cursor):
        """إدراج فئات المنتجات الافتراضية"""
        categories = [
            ('CAT001', 'إلكترونيات', None, 'الأجهزة الإلكترونية'),
            ('CAT002', 'ملابس', None, 'الملابس والأزياء'),
            ('CAT003', 'أغذية', None, 'المواد الغذائية'),
            ('CAT004', 'كتب', None, 'الكتب والمطبوعات'),
            ('CAT005', 'أدوات منزلية', None, 'الأدوات المنزلية'),
        ]
        
        for cat_code, cat_name, parent_id, description in categories:
            cursor.execute("""
                INSERT OR IGNORE INTO product_categories 
                (category_code, category_name, parent_id, description)
                VALUES (?, ?, ?, ?)
            """, (cat_code, cat_name, parent_id, description))
    
    def insert_default_accounts(self, cursor):
        """إدراج الحسابات الافتراضية"""
        accounts = [
            ('1000', 'الأصول', 'asset', None, 1),
            ('1100', 'الأصول المتداولة', 'asset', 1, 2),
            ('1110', 'النقدية', 'asset', 2, 3),
            ('1120', 'العملاء', 'asset', 2, 3),
            ('1130', 'المخزون', 'asset', 2, 3),
            ('2000', 'الخصوم', 'liability', None, 1),
            ('2100', 'الخصوم المتداولة', 'liability', 5, 2),
            ('2110', 'الموردون', 'liability', 6, 3),
            ('3000', 'حقوق الملكية', 'equity', None, 1),
            ('3100', 'رأس المال', 'equity', 8, 2),
            ('4000', 'الإيرادات', 'revenue', None, 1),
            ('4100', 'إيرادات المبيعات', 'revenue', 10, 2),
            ('5000', 'المصروفات', 'expense', None, 1),
            ('5100', 'تكلفة البضاعة المباعة', 'expense', 12, 2),
        ]
        
        for acc_code, acc_name, acc_type, parent_id, level in accounts:
            cursor.execute("""
                INSERT OR IGNORE INTO accounts 
                (account_code, account_name, account_type, parent_id, level)
                VALUES (?, ?, ?, ?, ?)
            """, (acc_code, acc_name, acc_type, parent_id, level))
    
    def insert_sample_data(self, cursor):
        """إدراج بيانات تجريبية"""
        # عملاء تجريبيون
        customers = [
            ('CUST001', 'أحمد محمد علي', 'individual', None, '*********', '<EMAIL>', 'صنعاء'),
            ('CUST002', 'شركة التقنية المتقدمة', 'company', 'شركة التقنية المتقدمة', '*********', '<EMAIL>', 'عدن'),
            ('CUST003', 'فاطمة حسن', 'individual', None, '*********', '<EMAIL>', 'تعز'),
        ]
        
        for cust_code, cust_name, cust_type, company, phone, email, city in customers:
            cursor.execute("""
                INSERT OR IGNORE INTO customers 
                (customer_code, customer_name, customer_type, company_name, phone, email, city, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, 1)
            """, (cust_code, cust_name, cust_type, company, phone, email, city))
        
        # موردون تجريبيون
        suppliers = [
            ('SUPP001', 'شركة الإمدادات الذهبية', 'شركة الإمدادات الذهبية', '*********', '<EMAIL>', 'صنعاء'),
            ('SUPP002', 'مؤسسة التجارة الحديثة', 'مؤسسة التجارة الحديثة', '*********', '<EMAIL>', 'الحديدة'),
        ]
        
        for supp_code, supp_name, company, phone, email, city in suppliers:
            cursor.execute("""
                INSERT OR IGNORE INTO suppliers 
                (supplier_code, supplier_name, company_name, phone, email, city, created_by)
                VALUES (?, ?, ?, ?, ?, ?, 1)
            """, (supp_code, supp_name, company, phone, email, city))
        
        # منتجات تجريبية
        products = [
            ('PROD001', '1234567890123', 'لابتوب ديل', 'لابتوب ديل انسبايرون 15', 1, 'Dell', 'Inspiron 15', 'قطعة', 800000, 1000000, 950000, 5, 50, 25),
            ('PROD002', '2345678901234', 'ماوس لاسلكي', 'ماوس لاسلكي لوجيتك', 1, 'Logitech', 'M705', 'قطعة', 15000, 25000, 22000, 10, 100, 45),
            ('PROD003', '3456789012345', 'كيبورد ميكانيكي', 'كيبورد ميكانيكي للألعاب', 1, 'Razer', 'BlackWidow', 'قطعة', 45000, 75000, 65000, 5, 30, 12),
        ]
        
        for prod_code, barcode, prod_name, description, cat_id, brand, model, unit, cost, selling, wholesale, min_stock, max_stock, current_stock in products:
            cursor.execute("""
                INSERT OR IGNORE INTO products 
                (product_code, barcode, product_name, description, category_id, brand, model, unit, 
                 cost_price, selling_price, wholesale_price, min_stock_level, max_stock_level, current_stock, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
            """, (prod_code, barcode, prod_name, description, cat_id, brand, model, unit, cost, selling, wholesale, min_stock, max_stock, current_stock))
    
    def close(self):
        """إغلاق الاتصال"""
        if self.connection:
            self.connection.close()
            print("✅ تم إغلاق الاتصال بقاعدة البيانات")

def setup_production_database():
    """إعداد قاعدة البيانات للإنتاج"""
    print("🚀 بدء إعداد قاعدة البيانات للإنتاج...")
    
    db_setup = ProductionDatabaseSetup()
    
    if not db_setup.connect():
        return False
    
    if not db_setup.create_complete_schema():
        return False
    
    if not db_setup.insert_default_data():
        return False
    
    db_setup.close()
    
    print("🎉 تم إعداد قاعدة البيانات للإنتاج بنجاح!")
    return True

if __name__ == "__main__":
    setup_production_database()
