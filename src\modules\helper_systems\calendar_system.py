#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقويم والمواعيد
Calendar and Appointments System
"""

import sys
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QPushButton, QGroupBox, 
                             QTextEdit, QTabWidget, QWidget, QComboBox,
                             QApplication, QMessageBox, QCalendarWidget,
                             QListWidget, QListWidgetItem, QTimeEdit,
                             QDateEdit, QCheckBox, QSpinBox)
from PyQt5.QtCore import Qt, QDate, QTime, QTimer
from PyQt5.QtGui import QFont, QColor

class CalendarDialog(QDialog):
    """حوار التقويم والمواعيد"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.appointments = []
        self.events = []
        self.reminders = []
        self.setup_ui()
        self.load_sample_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📅 التقويم والمواعيد")
        self.setGeometry(200, 200, 900, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("📅 التقويم والمواعيد - نظام إدارة المواعيد والأحداث")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات التقويم
        self.tabs = QTabWidget()
        
        # تبويب التقويم الرئيسي
        calendar_tab = self.create_calendar_tab()
        self.tabs.addTab(calendar_tab, "📅 التقويم")
        
        # تبويب المواعيد
        appointments_tab = self.create_appointments_tab()
        self.tabs.addTab(appointments_tab, "⏰ المواعيد")
        
        # تبويب الأحداث
        events_tab = self.create_events_tab()
        self.tabs.addTab(events_tab, "🎉 الأحداث")
        
        # تبويب التذكيرات
        reminders_tab = self.create_reminders_tab()
        self.tabs.addTab(reminders_tab, "🔔 التذكيرات")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_calendar_tab(self):
        """إنشاء تبويب التقويم الرئيسي"""
        tab = QWidget()
        layout = QHBoxLayout()
        
        # التقويم الرئيسي
        calendar_group = QGroupBox("📅 التقويم الميلادي")
        calendar_layout = QVBoxLayout()
        
        self.calendar = QCalendarWidget()
        self.calendar.setStyleSheet("""
            QCalendarWidget {
                background-color: white;
                border: 2px solid #3498db;
                border-radius: 8px;
            }
            QCalendarWidget QToolButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 5px;
            }
            QCalendarWidget QMenu {
                background-color: white;
                border: 1px solid #bdc3c7;
            }
            QCalendarWidget QSpinBox {
                background-color: white;
                border: 1px solid #bdc3c7;
                padding: 2px;
            }
        """)
        
        # ربط إشارة تغيير التاريخ
        self.calendar.selectionChanged.connect(self.on_date_selected)
        
        calendar_layout.addWidget(self.calendar)
        calendar_group.setLayout(calendar_layout)
        
        # معلومات اليوم المحدد
        info_group = QGroupBox("📋 معلومات اليوم المحدد")
        info_layout = QVBoxLayout()
        
        # التاريخ المحدد
        self.selected_date_label = QLabel()
        self.selected_date_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        
        # التقويم الهجري
        self.hijri_date_label = QLabel()
        self.hijri_date_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #27ae60;
                padding: 8px;
                background-color: #d5f4e6;
                border-radius: 5px;
                margin: 5px;
            }
        """)
        
        # مواعيد اليوم
        today_appointments_label = QLabel("📋 مواعيد اليوم:")
        today_appointments_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #34495e;
                margin: 10px 5px 5px 5px;
            }
        """)
        
        self.today_appointments_list = QListWidget()
        self.today_appointments_list.setStyleSheet("""
            QListWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                margin: 2px;
                background-color: white;
                border-radius: 3px;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        info_layout.addWidget(self.selected_date_label)
        info_layout.addWidget(self.hijri_date_label)
        info_layout.addWidget(today_appointments_label)
        info_layout.addWidget(self.today_appointments_list)
        
        info_group.setLayout(info_layout)
        
        layout.addWidget(calendar_group, 2)
        layout.addWidget(info_group, 1)
        
        tab.setLayout(layout)
        return tab
        
    def create_appointments_tab(self):
        """إنشاء تبويب المواعيد"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إضافة موعد جديد
        add_appointment_group = QGroupBox("➕ إضافة موعد جديد")
        add_layout = QGridLayout()
        
        # عنوان الموعد
        add_layout.addWidget(QLabel("العنوان:"), 0, 0)
        self.appointment_title = QLineEdit()
        self.appointment_title.setPlaceholderText("عنوان الموعد")
        add_layout.addWidget(self.appointment_title, 0, 1)
        
        # التاريخ
        add_layout.addWidget(QLabel("التاريخ:"), 0, 2)
        self.appointment_date = QDateEdit()
        self.appointment_date.setDate(QDate.currentDate())
        self.appointment_date.setCalendarPopup(True)
        add_layout.addWidget(self.appointment_date, 0, 3)
        
        # الوقت
        add_layout.addWidget(QLabel("الوقت:"), 1, 0)
        self.appointment_time = QTimeEdit()
        self.appointment_time.setTime(QTime.currentTime())
        add_layout.addWidget(self.appointment_time, 1, 1)
        
        # المدة
        add_layout.addWidget(QLabel("المدة (دقيقة):"), 1, 2)
        self.appointment_duration = QSpinBox()
        self.appointment_duration.setRange(15, 480)
        self.appointment_duration.setValue(60)
        self.appointment_duration.setSuffix(" دقيقة")
        add_layout.addWidget(self.appointment_duration, 1, 3)
        
        # الوصف
        add_layout.addWidget(QLabel("الوصف:"), 2, 0)
        self.appointment_description = QTextEdit()
        self.appointment_description.setMaximumHeight(80)
        self.appointment_description.setPlaceholderText("وصف الموعد...")
        add_layout.addWidget(self.appointment_description, 2, 1, 1, 3)
        
        # زر الإضافة
        add_appointment_btn = QPushButton("➕ إضافة الموعد")
        add_appointment_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_appointment_btn.clicked.connect(self.add_appointment)
        add_layout.addWidget(add_appointment_btn, 3, 0, 1, 4)
        
        add_appointment_group.setLayout(add_layout)
        
        # مجموعة قائمة المواعيد
        appointments_list_group = QGroupBox("📋 قائمة المواعيد")
        appointments_list_layout = QVBoxLayout()
        
        self.appointments_list = QListWidget()
        self.appointments_list.setStyleSheet("""
            QListWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 10px;
                margin: 3px;
                background-color: white;
                border-radius: 5px;
                border-left: 4px solid #3498db;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        # أزرار إدارة المواعيد
        appointments_buttons = QHBoxLayout()
        
        edit_appointment_btn = QPushButton("✏️ تعديل")
        edit_appointment_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_appointment_btn.clicked.connect(self.edit_appointment)
        
        delete_appointment_btn = QPushButton("🗑️ حذف")
        delete_appointment_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_appointment_btn.clicked.connect(self.delete_appointment)
        
        appointments_buttons.addWidget(edit_appointment_btn)
        appointments_buttons.addWidget(delete_appointment_btn)
        appointments_buttons.addStretch()
        
        appointments_list_layout.addWidget(self.appointments_list)
        appointments_list_layout.addLayout(appointments_buttons)
        appointments_list_group.setLayout(appointments_list_layout)
        
        layout.addWidget(add_appointment_group)
        layout.addWidget(appointments_list_group)
        
        tab.setLayout(layout)
        return tab

    def create_events_tab(self):
        """إنشاء تبويب الأحداث"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة الأحداث
        events_group = QGroupBox("🎉 إدارة الأحداث")
        events_layout = QVBoxLayout()

        # قائمة الأحداث
        self.events_list = QListWidget()
        self.events_list.setStyleSheet("""
            QListWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 12px;
                margin: 3px;
                background-color: white;
                border-radius: 5px;
                border-left: 4px solid #e74c3c;
            }
            QListWidget::item:selected {
                background-color: #e74c3c;
                color: white;
            }
        """)

        events_layout.addWidget(self.events_list)
        events_group.setLayout(events_layout)

        layout.addWidget(events_group)

        tab.setLayout(layout)
        return tab

    def create_reminders_tab(self):
        """إنشاء تبويب التذكيرات"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة التذكيرات
        reminders_group = QGroupBox("🔔 إدارة التذكيرات")
        reminders_layout = QVBoxLayout()

        # قائمة التذكيرات
        self.reminders_list = QListWidget()
        self.reminders_list.setStyleSheet("""
            QListWidget {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 12px;
                margin: 3px;
                background-color: white;
                border-radius: 5px;
                border-left: 4px solid #f39c12;
            }
            QListWidget::item:selected {
                background-color: #f39c12;
                color: white;
            }
        """)

        reminders_layout.addWidget(self.reminders_list)
        reminders_group.setLayout(reminders_layout)

        layout.addWidget(reminders_group)

        tab.setLayout(layout)
        return tab

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        # زر المساعدة
        help_btn = QPushButton("❓ المساعدة")
        help_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        help_btn.clicked.connect(self.show_help)

        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        close_btn.clicked.connect(self.reject)

        layout.addWidget(help_btn)
        layout.addStretch()
        layout.addWidget(close_btn)

        return layout

    # ربط الوظائف من الملف المنفصل
    def load_sample_data(self):
        """تحميل البيانات النموذجية"""
        from .calendar_functions import load_sample_data_function
        load_sample_data_function(self)

    def on_date_selected(self):
        """عند تحديد تاريخ في التقويم"""
        from .calendar_functions import on_date_selected_function
        on_date_selected_function(self)

    def update_selected_date(self):
        """تحديث معلومات التاريخ المحدد"""
        from .calendar_functions import update_selected_date_function
        update_selected_date_function(self)

    def convert_to_hijri(self, gregorian_date):
        """تحويل التاريخ الميلادي إلى هجري"""
        from .calendar_functions import convert_to_hijri_function
        return convert_to_hijri_function(self, gregorian_date)

    def update_today_appointments(self, selected_date):
        """تحديث مواعيد اليوم المحدد"""
        from .calendar_functions import update_today_appointments_function
        update_today_appointments_function(self, selected_date)

    def update_appointments_list(self):
        """تحديث قائمة المواعيد"""
        from .calendar_functions import update_appointments_list_function
        update_appointments_list_function(self)

    def update_events_list(self):
        """تحديث قائمة الأحداث"""
        from .calendar_functions import update_events_list_function
        update_events_list_function(self)

    def update_reminders_list(self):
        """تحديث قائمة التذكيرات"""
        from .calendar_functions import update_reminders_list_function
        update_reminders_list_function(self)

    def add_appointment(self):
        """إضافة موعد جديد"""
        from .calendar_functions import add_appointment_function
        add_appointment_function(self)

    def edit_appointment(self):
        """تعديل موعد"""
        from .calendar_functions import edit_appointment_function
        edit_appointment_function(self)

    def delete_appointment(self):
        """حذف موعد"""
        from .calendar_functions import delete_appointment_function
        delete_appointment_function(self)

    def show_help(self):
        """عرض المساعدة"""
        from .calendar_functions import show_help_function
        show_help_function(self)


def main():
    """اختبار نظام التقويم"""
    import sys

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    dialog = CalendarDialog()
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
