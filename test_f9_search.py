#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة البحث بـ F9 في نظام السندات
F9 Search Feature Test - Voucher System
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QPushButton, QWidget, QLabel, QMessageBox, QTextEdit
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class F9SearchTestWindow(QMainWindow):
    """نافذة اختبار ميزة البحث بـ F9"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_mock_db()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار ميزة البحث بـ F9 - نظام السندات")
        self.setGeometry(200, 200, 900, 700)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
                font-family: "Tahoma", "Arial", sans-serif;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                margin: 10px;
                min-width: 250px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 14px;
                color: #2c3e50;
                padding: 10px;
            }
            QTextEdit {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 12px;
                background-color: white;
                border: 2px solid #3498db;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🔍 اختبار ميزة البحث بـ F9 في نظام السندات")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 22px;
                color: #3498db;
                background-color: white;
                border: 2px solid #3498db;
                border-radius: 10px;
                padding: 20px;
                margin: 20px;
                text-align: center;
            }
        """)
        layout.addWidget(title_label)
        
        # أزرار الاختبار
        buttons_layout = QVBoxLayout()
        
        # اختبار نافذة البحث مباشرة
        search_dialog_btn = QPushButton("🔍 اختبار نافذة البحث مباشرة")
        search_dialog_btn.clicked.connect(self.test_search_dialog)
        buttons_layout.addWidget(search_dialog_btn)
        
        # اختبار البحث في سند القبض
        receipt_search_btn = QPushButton("📄 اختبار البحث في سند القبض")
        receipt_search_btn.clicked.connect(self.test_receipt_with_search)
        buttons_layout.addWidget(receipt_search_btn)
        
        # اختبار البحث في سند الصرف
        payment_search_btn = QPushButton("📄 اختبار البحث في سند الصرف")
        payment_search_btn.clicked.connect(self.test_payment_with_search)
        buttons_layout.addWidget(payment_search_btn)
        
        layout.addLayout(buttons_layout)
        
        # تعليمات الاستخدام
        instructions = QTextEdit()
        instructions.setReadOnly(True)
        instructions.setMaximumHeight(300)
        instructions.setHtml("""
        <h3 style="color: #3498db;">📋 تعليمات اختبار ميزة البحث بـ F9:</h3>
        
        <h4 style="color: #e74c3c;">⌨️ كيفية استخدام البحث:</h4>
        <ol>
            <li><strong>افتح أي سند</strong> (قبض أو صرف)</li>
            <li><strong>انقر في عمود "رقم الحساب"</strong> في جدول التفاصيل</li>
            <li><strong>اضغط F9</strong> من لوحة المفاتيح</li>
            <li><strong>ستظهر نافذة البحث</strong> مع العنوان "يحتوي على"</li>
        </ol>
        
        <h4 style="color: #27ae60;">🔍 ميزات نافذة البحث:</h4>
        <ul>
            <li>🔤 <strong>حقل البحث:</strong> اكتب جزء من رقم الحساب أو اسمه</li>
            <li>📋 <strong>نوع البحث:</strong> اختر البحث في (الكل، رقم الحساب، اسم الحساب، نوع الحساب)</li>
            <li>⚡ <strong>البحث التلقائي:</strong> النتائج تتحدث أثناء الكتابة</li>
            <li>📊 <strong>جدول النتائج:</strong> يعرض رقم الحساب، الاسم، النوع، والحالة</li>
            <li>📈 <strong>عداد النتائج:</strong> يظهر عدد الحسابات المطابقة</li>
        </ul>
        
        <h4 style="color: #9b59b6;">⌨️ اختصارات لوحة المفاتيح:</h4>
        <ul>
            <li><strong>F9:</strong> فتح نافذة البحث (في عمود رقم الحساب)</li>
            <li><strong>Enter:</strong> اختيار الحساب المحدد</li>
            <li><strong>Esc:</strong> إلغاء وإغلاق نافذة البحث</li>
            <li><strong>النقر المزدوج:</strong> اختيار الحساب مباشرة</li>
            <li><strong>الأسهم:</strong> التنقل في قائمة النتائج</li>
        </ul>
        
        <h4 style="color: #f39c12;">✅ سلوك النظام بعد الاختيار:</h4>
        <ul>
            <li>🔢 <strong>رقم الحساب:</strong> يتم نسخه تلقائياً للخلية</li>
            <li>📝 <strong>اسم الحساب:</strong> يتم تعبئته في العمود المجاور</li>
            <li>📋 <strong>البيان:</strong> يتم تعبئته تلقائياً إذا كان فارغاً</li>
            <li>➡️ <strong>الانتقال:</strong> المؤشر ينتقل للعمود التالي (مدين/دائن)</li>
            <li>📊 <strong>شريط الحالة:</strong> يعرض معلومات الحساب المختار</li>
        </ul>
        
        <h4 style="color: #e67e22;">🎯 بيانات الاختبار المتاحة:</h4>
        <ul>
            <li>1010001 - صندوق المركز الرئيسي</li>
            <li>1020001 - البنك الأهلي</li>
            <li>1020002 - بنك الراجحي</li>
            <li>1030001 - العملاء المحليين</li>
            <li>1030002 - العملاء الأجانب</li>
            <li>2010001 - الموردين المحليين</li>
            <li>2010002 - الموردين الأجانب</li>
            <li>4010001 - مبيعات محلية</li>
            <li>4010002 - مبيعات تصدير</li>
            <li>5010001 - تكلفة البضاعة المباعة</li>
        </ul>
        """)
        layout.addWidget(instructions)
        
        central_widget.setLayout(layout)
    
    def setup_mock_db(self):
        """إعداد قاعدة بيانات وهمية للاختبار"""
        class MockDBManager:
            def __init__(self):
                self.cursor = type('obj', (object,), {'lastrowid': 1})()
            
            def execute_query(self, query, params=None):
                # محاكاة نتائج الاستعلام
                if "MAX" in query:
                    return [(0,)]
                elif "chart_of_accounts" in query:
                    # إرجاع بيانات الحسابات التجريبية
                    return [
                        ('1010001', 'صندوق المركز الرئيسي', 'أصول متداولة', 1, 3, '101000'),
                        ('1020001', 'البنك الأهلي', 'أصول متداولة', 1, 3, '102000'),
                        ('1020002', 'بنك الراجحي', 'أصول متداولة', 1, 3, '102000'),
                        ('1030001', 'العملاء المحليين', 'أصول متداولة', 1, 3, '103000'),
                        ('1030002', 'العملاء الأجانب', 'أصول متداولة', 1, 3, '103000'),
                        ('2010001', 'الموردين المحليين', 'خصوم متداولة', 1, 3, '201000'),
                        ('2010002', 'الموردين الأجانب', 'خصوم متداولة', 1, 3, '201000'),
                        ('4010001', 'مبيعات محلية', 'إيرادات', 1, 3, '401000'),
                        ('4010002', 'مبيعات تصدير', 'إيرادات', 1, 3, '401000'),
                        ('5010001', 'تكلفة البضاعة المباعة', 'مصروفات', 1, 3, '501000'),
                    ]
                return []
            
            def execute_update(self, query, params=None):
                return True
        
        self.db_manager = MockDBManager()
    
    def test_search_dialog(self):
        """اختبار نافذة البحث مباشرة"""
        try:
            from modules.vouchers.account_search_dialog import AccountSearchDialog
            search_dialog = AccountSearchDialog(self.db_manager, self)
            
            def on_account_selected(account_data):
                QMessageBox.information(
                    self, 
                    "تم اختيار الحساب",
                    f"تم اختيار الحساب:\n\n"
                    f"رقم الحساب: {account_data['code']}\n"
                    f"اسم الحساب: {account_data['name']}\n"
                    f"نوع الحساب: {account_data['type']}\n"
                    f"الحالة: {'نشط' if account_data['is_active'] else 'غير نشط'}"
                )
            
            search_dialog.account_selected.connect(on_account_selected)
            search_dialog.exec_()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة البحث:\n{str(e)}")
    
    def test_receipt_with_search(self):
        """اختبار البحث في سند القبض"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            receipt_dialog = VoucherInterface(self.db_manager, "receipt", self)
            receipt_dialog.show()
            
            QMessageBox.information(
                self, 
                "اختبار سند القبض",
                "تم فتح سند القبض مع ميزة البحث بـ F9\n\n"
                "للاختبار:\n"
                "1. انقر في عمود 'رقم الحساب' في الجدول\n"
                "2. اضغط F9 لفتح نافذة البحث\n"
                "3. جرب البحث عن الحسابات المختلفة\n"
                "4. اختر حساباً وشاهد التعبئة التلقائية"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند القبض:\n{str(e)}")
    
    def test_payment_with_search(self):
        """اختبار البحث في سند الصرف"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            payment_dialog = VoucherInterface(self.db_manager, "payment", self)
            payment_dialog.show()
            
            QMessageBox.information(
                self, 
                "اختبار سند الصرف",
                "تم فتح سند الصرف مع ميزة البحث بـ F9\n\n"
                "للاختبار:\n"
                "1. انقر في عمود 'رقم الحساب' في الجدول\n"
                "2. اضغط F9 لفتح نافذة البحث\n"
                "3. جرب البحث عن الحسابات المختلفة\n"
                "4. اختر حساباً وشاهد التعبئة التلقائية"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند الصرف:\n{str(e)}")


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont("Tahoma", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = F9SearchTestWindow()
    window.show()
    
    print("🔍 تم تشغيل اختبار ميزة البحث بـ F9!")
    print("📋 الميزات المتاحة للاختبار:")
    print("   ✅ نافذة البحث المستقلة")
    print("   ✅ البحث في سند القبض")
    print("   ✅ البحث في سند الصرف")
    print("   ✅ البحث التلقائي أثناء الكتابة")
    print("   ✅ اختصارات لوحة المفاتيح")
    print("   ✅ التعبئة التلقائية للحسابات")
    print("")
    print("💡 للاختبار:")
    print("   • اضغط F9 في عمود رقم الحساب")
    print("   • جرب البحث بالرقم أو الاسم")
    print("   • استخدم النقر المزدوج للاختيار")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
