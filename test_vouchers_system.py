#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام السندات - Onyx Pro ERP
Vouchers System Test - Onyx Pro ERP
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QPushButton, QWidget, QLabel, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class VouchersTestWindow(QMainWindow):
    """نافذة اختبار نظام السندات"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_mock_db()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار نظام السندات - Onyx Pro ERP")
        self.setGeometry(200, 200, 800, 600)
        
        # تطبيق الخلفية الوردية
        self.setStyleSheet("""
            QMainWindow {
                background-color: #fdf2f8;
                font-family: "Tahoma", "Arial", sans-serif;
            }
            QPushButton {
                background-color: #e91e63;
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                margin: 10px;
                min-width: 200px;
            }
            QPushButton:hover {
                background-color: #c2185b;
            }
            QPushButton:pressed {
                background-color: #ad1457;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                text-align: center;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🧾 اختبار نظام السندات - Onyx Pro ERP")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                color: #e91e63;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 10px;
                padding: 20px;
                margin: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        # أزرار الاختبار
        buttons_layout = QVBoxLayout()
        
        # اختبار سند القبض
        receipt_btn = QPushButton("📄 اختبار سند القبض")
        receipt_btn.clicked.connect(self.test_receipt_voucher)
        buttons_layout.addWidget(receipt_btn)
        
        # اختبار سند الصرف
        payment_btn = QPushButton("📄 اختبار سند الصرف")
        payment_btn.clicked.connect(self.test_payment_voucher)
        buttons_layout.addWidget(payment_btn)
        
        # اختبار من إدارة الحسابات
        accounts_btn = QPushButton("🏦 اختبار من إدارة الحسابات")
        accounts_btn.clicked.connect(self.test_from_accounts)
        buttons_layout.addWidget(accounts_btn)
        
        # اختبار من إدارة العملاء
        customers_btn = QPushButton("👥 اختبار من إدارة العملاء")
        customers_btn.clicked.connect(self.test_from_customers)
        buttons_layout.addWidget(customers_btn)
        
        # اختبار من إدارة الموردين
        suppliers_btn = QPushButton("🏭 اختبار من إدارة الموردين")
        suppliers_btn.clicked.connect(self.test_from_suppliers)
        buttons_layout.addWidget(suppliers_btn)
        
        # اختبار الواجهة الموحدة
        unified_btn = QPushButton("🎯 اختبار الواجهة الموحدة")
        unified_btn.clicked.connect(self.test_unified_interface)
        buttons_layout.addWidget(unified_btn)

        # اختبار ميزة البحث بـ F9
        f9_search_btn = QPushButton("🔍 اختبار البحث بـ F9")
        f9_search_btn.clicked.connect(self.test_f9_search)
        buttons_layout.addWidget(f9_search_btn)
        
        layout.addLayout(buttons_layout)
        
        # معلومات النظام
        info_label = QLabel("""
📋 معلومات النظام:
✅ سند القبض - مع تخصيص للعملاء
✅ سند الصرف - مع تخصيص للموردين  
✅ واجهة موحدة مع شريط أدوات كامل
✅ تكامل مع إدارة الحسابات والعملاء والموردين
✅ تصميم ERP احترافي بالخلفية الوردية
✅ دعم كامل للعربية مع RTL
🔍 البحث عن الحسابات بـ F9
⌨️ اختصارات لوحة المفاتيح متقدمة
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: white;
                border: 1px solid #e91e63;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
                font-size: 12px;
                text-align: left;
            }
        """)
        layout.addWidget(info_label)
        
        central_widget.setLayout(layout)
    
    def setup_mock_db(self):
        """إعداد قاعدة بيانات وهمية للاختبار"""
        class MockDBManager:
            def __init__(self):
                self.cursor = type('obj', (object,), {'lastrowid': 1})()
            
            def execute_query(self, query, params=None):
                # محاكاة نتائج الاستعلام
                if "MAX" in query:
                    return [(0,)]
                return []
            
            def execute_update(self, query, params=None):
                return True
        
        self.db_manager = MockDBManager()
    
    def test_receipt_voucher(self):
        """اختبار سند القبض"""
        try:
            from modules.vouchers.receipt_voucher import ReceiptVoucherDialog
            dialog = ReceiptVoucherDialog(self.db_manager, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.information(self, "اختبار سند القبض", f"تم اختبار سند القبض\nالحالة: {str(e)}")
    
    def test_payment_voucher(self):
        """اختبار سند الصرف"""
        try:
            from modules.vouchers.payment_voucher import PaymentVoucherDialog
            dialog = PaymentVoucherDialog(self.db_manager, self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.information(self, "اختبار سند الصرف", f"تم اختبار سند الصرف\nالحالة: {str(e)}")
    
    def test_from_accounts(self):
        """اختبار من إدارة الحسابات"""
        try:
            from modules.accounts_module import AccountsModule
            accounts_widget = AccountsModule(self.db_manager, self)
            accounts_widget.show()
            QMessageBox.information(self, "إدارة الحسابات", "تم فتح إدارة الحسابات\nابحث عن أزرار 'سند قبض' و 'سند صرف'")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إدارة الحسابات:\n{str(e)}")
    
    def test_from_customers(self):
        """اختبار من إدارة العملاء"""
        try:
            from modules.customers.customer_management import CustomerManagement
            customers_widget = CustomerManagement(self.db_manager, self)
            customers_widget.show()
            QMessageBox.information(self, "إدارة العملاء", "تم فتح إدارة العملاء\nابحث عن زر 'سند قبض'")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إدارة العملاء:\n{str(e)}")
    
    def test_from_suppliers(self):
        """اختبار من إدارة الموردين"""
        try:
            from modules.customers_suppliers_module import CustomersSupplierModule
            suppliers_widget = CustomersSupplierModule(self.db_manager, self)
            suppliers_widget.show()
            QMessageBox.information(self, "إدارة الموردين", "تم فتح إدارة العملاء والموردين\nابحث عن أزرار 'سند قبض' و 'سند صرف'")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إدارة الموردين:\n{str(e)}")
    
    def test_unified_interface(self):
        """اختبار الواجهة الموحدة"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            
            # اختبار سند قبض
            receipt_dialog = VoucherInterface(self.db_manager, "receipt", self)
            receipt_dialog.show()
            
            QMessageBox.information(self, "الواجهة الموحدة", 
                "تم فتح الواجهة الموحدة لسند القبض\n\n" +
                "الميزات المتاحة:\n" +
                "✅ شريط أدوات كامل\n" +
                "✅ جدول تفاصيل السند\n" +
                "✅ حساب الإجماليات التلقائي\n" +
                "✅ التنقل بين السندات\n" +
                "✅ البحث والطباعة والمعاينة"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الواجهة الموحدة:\n{str(e)}")

    def test_f9_search(self):
        """اختبار ميزة البحث بـ F9"""
        try:
            from modules.vouchers.account_search_dialog import AccountSearchDialog

            # إنشاء نافذة البحث
            search_dialog = AccountSearchDialog(self.db_manager, self)

            def on_account_selected(account_data):
                QMessageBox.information(
                    self,
                    "تم اختيار الحساب",
                    f"✅ تم اختيار الحساب بنجاح!\n\n"
                    f"📋 بيانات الحساب:\n"
                    f"🔢 رقم الحساب: {account_data['code']}\n"
                    f"📝 اسم الحساب: {account_data['name']}\n"
                    f"📊 نوع الحساب: {account_data['type']}\n"
                    f"✅ الحالة: {'نشط' if account_data['is_active'] else 'غير نشط'}\n\n"
                    f"🎯 هذا مثال على كيفية عمل البحث بـ F9 في السندات"
                )

            search_dialog.account_selected.connect(on_account_selected)
            search_dialog.show()

            QMessageBox.information(
                self,
                "اختبار البحث بـ F9",
                "🔍 تم فتح نافذة البحث عن الحسابات\n\n"
                "📋 الميزات المتاحة:\n"
                "✅ البحث في رقم الحساب أو اسمه\n"
                "✅ فلترة حسب نوع الحساب\n"
                "✅ البحث التلقائي أثناء الكتابة\n"
                "✅ عرض عدد النتائج\n"
                "✅ اختصارات لوحة المفاتيح\n\n"
                "💡 للاختبار:\n"
                "• اكتب في حقل البحث (مثل: صندوق، بنك)\n"
                "• جرب تغيير نوع البحث\n"
                "• انقر نقراً مزدوجاً على أي حساب\n"
                "• اضغط F9 للتركيز على البحث\n"
                "• اضغط Enter للاختيار أو Esc للإلغاء"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة البحث:\n{str(e)}")


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont("Tahoma", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = VouchersTestWindow()
    window.show()
    
    print("🎉 تم تشغيل اختبار نظام السندات بنجاح!")
    print("📋 الميزات المتاحة:")
    print("   ✅ سند القبض مع تخصيص للعملاء")
    print("   ✅ سند الصرف مع تخصيص للموردين")
    print("   ✅ تكامل مع إدارة الحسابات")
    print("   ✅ تكامل مع إدارة العملاء")
    print("   ✅ تكامل مع إدارة الموردين")
    print("   ✅ واجهة موحدة احترافية")
    print("   ✅ شريط أدوات كامل")
    print("   ✅ تصميم ERP متقدم")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
