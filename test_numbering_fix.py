#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة الترقيم التلقائي
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_numbering_data_loading():
    """اختبار تحميل بيانات الترقيم"""
    print("🔢 اختبار تحميل بيانات الترقيم التلقائي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.auto_numbering_module import AutoNumberingWidget
        from database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager("accounting_system.db")
        
        # إنشاء وحدة الترقيم التلقائي
        numbering_widget = AutoNumberingWidget(db_manager)
        print("   ✅ تم إنشاء وحدة الترقيم التلقائي")
        
        # تحميل البيانات
        numbering_widget.load_data()
        print("   ✅ تم استدعاء دالة تحميل البيانات")
        
        # فحص عدد الصفوف في الجدول
        row_count = numbering_widget.sequences_table.rowCount()
        print(f"   📊 عدد التسلسلات في الجدول: {row_count}")
        
        if row_count > 0:
            print("   ✅ البيانات تظهر في الجدول")
            
            # عرض بعض البيانات
            for row in range(min(3, row_count)):
                doc_type = numbering_widget.sequences_table.item(row, 0)
                prefix = numbering_widget.sequences_table.item(row, 1)
                current_num = numbering_widget.sequences_table.item(row, 2)
                
                if doc_type and prefix and current_num:
                    print(f"      {row+1}. {doc_type.text()}: {prefix.text()}-{current_num.text()}")
                else:
                    print(f"      {row+1}. بيانات غير مكتملة")
        else:
            print("   ❌ لا توجد بيانات في الجدول")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار تحميل البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_add_sequence():
    """اختبار إضافة تسلسل جديد"""
    print("\n➕ اختبار إضافة تسلسل جديد...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.auto_numbering_module import AutoNumberingWidget
        from database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager("accounting_system.db")
        
        # إنشاء وحدة الترقيم التلقائي
        numbering_widget = AutoNumberingWidget(db_manager)
        
        # عدد التسلسلات قبل الإضافة
        numbering_widget.load_data()
        count_before = numbering_widget.sequences_table.rowCount()
        print(f"   📊 عدد التسلسلات قبل الإضافة: {count_before}")
        
        # إضافة تسلسل اختباري مباشرة في قاعدة البيانات
        try:
            from datetime import datetime
            
            test_query = """
                INSERT OR REPLACE INTO numbering_sequences 
                (document_type, prefix, current_number, number_length, include_date, 
                 date_format, separator, reset_yearly, reset_monthly, is_active, 
                 created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            test_params = (
                "اختبار المستند",
                "TEST",
                1,
                6,
                True,
                "YYYY",
                "-",
                True,
                False,
                True,
                datetime.now().isoformat(),
                datetime.now().isoformat()
            )
            
            db_manager.execute_update(test_query, test_params)
            print("   ✅ تم إضافة تسلسل اختباري في قاعدة البيانات")
            
            # تحديث البيانات
            numbering_widget.load_data()
            count_after = numbering_widget.sequences_table.rowCount()
            print(f"   📊 عدد التسلسلات بعد الإضافة: {count_after}")
            
            if count_after > count_before:
                print("   ✅ تم تحديث الجدول بنجاح بعد الإضافة")
                return True
            else:
                print("   ❌ لم يتم تحديث الجدول بعد الإضافة")
                return False
                
        except Exception as e:
            print(f"   ❌ خطأ في إضافة التسلسل: {e}")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إضافة التسلسل: {e}")
        return False

def test_refresh_function():
    """اختبار دالة التحديث"""
    print("\n🔄 اختبار دالة التحديث...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.auto_numbering_module import AutoNumberingWidget
        from database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager("accounting_system.db")
        
        # إنشاء وحدة الترقيم التلقائي
        numbering_widget = AutoNumberingWidget(db_manager)
        
        # اختبار دالة التحديث
        if hasattr(numbering_widget, 'refresh_data'):
            print("   ✅ دالة refresh_data موجودة")
            
            # استدعاء دالة التحديث
            numbering_widget.refresh_data()
            print("   ✅ تم استدعاء دالة التحديث بنجاح")
            
            return True
        else:
            print("   ❌ دالة refresh_data غير موجودة")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار دالة التحديث: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🗄️ اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار الاستعلام
        sequences = db_manager.execute_query("""
            SELECT COUNT(*) as count FROM numbering_sequences
        """)
        
        if sequences:
            count = sequences[0][0] if sequences[0] else 0
            print(f"   ✅ الاتصال بقاعدة البيانات ناجح")
            print(f"   📊 عدد التسلسلات في قاعدة البيانات: {count}")
            return True
        else:
            print("   ❌ فشل في الاستعلام")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🔧 اختبار إصلاح مشكلة الترقيم التلقائي")
    print("=" * 70)
    
    tests = [
        ("الاتصال بقاعدة البيانات", test_database_connection),
        ("تحميل بيانات الترقيم", test_numbering_data_loading),
        ("إضافة تسلسل جديد", test_add_sequence),
        ("دالة التحديث", test_refresh_function),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم إصلاح مشكلة الترقيم التلقائي بنجاح!")
        print("✅ البيانات تظهر بعد الحفظ")
        print("✅ التحديث يعمل بشكل صحيح")
        print("✅ الجدول يتحدث فوراً")
        
        print("\n🔧 الإصلاحات المطبقة:")
        print("   ✅ إصلاح تحديث البيانات بعد الحفظ")
        print("   ✅ إصلاح تحديث الجدول بعد الحذف")
        print("   ✅ إصلاح عرض البيانات في الجدول")
        print("   ✅ إضافة زر تحديث محسن")
        print("   ✅ إضافة رسائل تأكيد")
        
        print("\n🚀 للاستخدام:")
        print("   1. شغل النظام: python src/main_system_restructured.py")
        print("   2. انقر على 'إعدادات النظام'")
        print("   3. اختر 'الترقيم التلقائي للمستندات'")
        print("   4. أضف أو عدل التسلسلات - ستظهر فوراً!")
        
    else:
        print("\n⚠️ بعض جوانب الإصلاح تحتاج إلى مراجعة")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
