#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لإصلاح مشكلة ظهور النص
Simple Text Visibility Fix Test
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QPushButton, QWidget, QLabel, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SimpleTextFixTestWindow(QMainWindow):
    """نافذة اختبار بسيطة لإصلاح النص"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_mock_db()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار بسيط - إصلاح مشكلة ظهور النص")
        self.setGeometry(300, 300, 600, 400)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🔧 اختبار إصلاح مشكلة ظهور النص")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                color: #e91e63;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 10px;
                padding: 15px;
                margin: 15px;
                text-align: center;
            }
        """)
        layout.addWidget(title_label)
        
        # زر الاختبار
        test_btn = QPushButton("🧪 اختبار السند - النص يجب أن يظهر أثناء الكتابة")
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        test_btn.clicked.connect(self.test_voucher)
        layout.addWidget(test_btn)
        
        # التعليمات
        instructions = QLabel("""
📋 تعليمات الاختبار:

1️⃣ انقر على زر الاختبار أعلاه
2️⃣ ستفتح نافذة السند
3️⃣ انقر في خانة "المبلغ" (العمود الخامس)
4️⃣ ابدأ الكتابة فوراً: 15000
5️⃣ يجب أن ترى النص يظهر أثناء الكتابة

✅ المتوقع:
• النص يظهر بوضوح أثناء الكتابة
• لون النص أسود
• خلفية بيضاء
• لا توجد مشاكل في الرؤية

❌ إذا لم يظهر النص:
• تحقق من إعدادات الشاشة
• جرب تغيير حجم النافذة
• أعد تشغيل البرنامج
        """)
        instructions.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
                font-size: 12px;
                line-height: 1.5;
            }
        """)
        layout.addWidget(instructions)
        
        central_widget.setLayout(layout)
    
    def setup_mock_db(self):
        """إعداد قاعدة بيانات وهمية"""
        class MockDBManager:
            def __init__(self):
                self.cursor = type('obj', (object,), {'lastrowid': 1})()
            
            def execute_query(self, query, params=None):
                if "MAX" in query:
                    return [(0,)]
                elif "chart_of_accounts" in query:
                    return [
                        ('1010001', 'صندوق المركز الرئيسي', 'أصول متداولة', 1, 3, '101000'),
                        ('1030001', 'العملاء المحليين', 'أصول متداولة', 1, 3, '103000'),
                        ('4010001', 'مبيعات محلية', 'إيرادات', 1, 3, '401000'),
                    ]
                return []
            
            def execute_update(self, query, params=None):
                return True
        
        self.db_manager = MockDBManager()
    
    def test_voucher(self):
        """اختبار السند"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            from PyQt5.QtWidgets import QTableWidgetItem
            
            # إنشاء السند
            dialog = VoucherInterface(self.db_manager, "receipt", self)
            
            # تعبئة بيانات أساسية
            dialog.beneficiary_edit.setText("شركة الاختبار")
            dialog.cashbox_code_edit.setText("1010001")
            dialog.cashbox_name_edit.setText("صندوق المركز الرئيسي")
            
            # إضافة صف للاختبار
            if dialog.details_table.rowCount() == 0:
                dialog.add_detail_row()
            
            # تعبئة بيانات الصف
            dialog.details_table.setItem(0, 1, QTableWidgetItem("1030001"))
            dialog.details_table.setItem(0, 2, QTableWidgetItem("العملاء المحليين"))
            dialog.details_table.setItem(0, 3, QTableWidgetItem("تحصيل فاتورة"))
            
            # التركيز على خانة المبلغ
            dialog.details_table.setCurrentCell(0, 4)
            dialog.details_table.setFocus()
            
            # عرض السند
            dialog.show()
            
            # رسالة التوجيه
            QMessageBox.information(
                self, 
                "اختبار النص",
                "✅ تم فتح السند للاختبار\n\n"
                "🔍 الآن:\n"
                "1. انقر في خانة 'المبلغ' (العمود الخامس)\n"
                "2. ابدأ الكتابة فوراً: 15000\n"
                "3. يجب أن ترى النص يظهر أثناء الكتابة\n\n"
                "✅ إذا ظهر النص بوضوح = تم الإصلاح ✓\n"
                "❌ إذا لم يظهر النص = المشكلة ما زالت موجودة\n\n"
                "💡 جرب أيضاً:\n"
                "• كتابة أرقام مختلفة\n"
                "• التنقل بين الخانات\n"
                "• استخدام Tab و Enter"
            )
            
        except Exception as e:
            QMessageBox.critical(
                self, 
                "خطأ", 
                f"فشل في فتح السند:\n{str(e)}\n\n"
                "تأكد من وجود ملفات النظام في المجلد الصحيح"
            )


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont("Tahoma", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = SimpleTextFixTestWindow()
    window.show()
    
    print("🔧 تم تشغيل اختبار إصلاح النص!")
    print("✅ الإصلاحات المطبقة:")
    print("   • إزالة التنسيقات المتضاربة")
    print("   • تبسيط CSS للجدول")
    print("   • إضافة معالج لضمان ظهور النص")
    print("   • تنسيق واضح للمبالغ")
    print("")
    print("🧪 اختبر:")
    print("   • الكتابة في خانة المبلغ")
    print("   • ظهور النص أثناء الكتابة")
    print("   • وضوح الألوان والخطوط")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
