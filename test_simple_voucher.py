#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط للسند الجديد
Simple Voucher Test
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QPushButton, QWidget, QLabel, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class SimpleVoucherTestWindow(QMainWindow):
    """نافذة اختبار مبسطة للسند"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_mock_db()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار مبسط للسند الجديد")
        self.setGeometry(300, 300, 600, 400)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🔧 اختبار مبسط للسند الجديد")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                color: #e91e63;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 10px;
                padding: 15px;
                margin: 15px;
                text-align: center;
            }
        """)
        layout.addWidget(title_label)
        
        # زر الاختبار
        test_btn = QPushButton("🧪 اختبار السند الجديد")
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        test_btn.clicked.connect(self.test_voucher)
        layout.addWidget(test_btn)
        
        # معلومات الحالة
        self.status_label = QLabel("جاهز للاختبار...")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin: 10px;
                font-size: 12px;
            }
        """)
        layout.addWidget(self.status_label)
        
        central_widget.setLayout(layout)
    
    def setup_mock_db(self):
        """إعداد قاعدة بيانات وهمية"""
        class MockDBManager:
            def __init__(self):
                self.cursor = type('obj', (object,), {'lastrowid': 1})()
            
            def execute_query(self, query, params=None):
                if "MAX" in query:
                    return [(0,)]
                elif "chart_of_accounts" in query:
                    return [
                        ('111001', 'صندوق المحل الرئيسي', 'أصول متداولة', 1, 3, '111000'),
                        ('112001', 'البنك الأهلي اليمني', 'أصول متداولة', 1, 3, '112000'),
                    ]
                return []
            
            def execute_update(self, query, params=None):
                return True
        
        self.db_manager = MockDBManager()
    
    def test_voucher(self):
        """اختبار السند"""
        try:
            self.status_label.setText("🔄 جاري تحميل السند...")
            QApplication.processEvents()
            
            from modules.vouchers.voucher_interface import VoucherInterface
            
            # إنشاء السند
            dialog = VoucherInterface(self.db_manager, "receipt", self)
            
            self.status_label.setText("✅ تم تحميل السند بنجاح!")
            
            # عرض السند
            dialog.show()
            
            QMessageBox.information(
                self, 
                "نجح الاختبار",
                "✅ تم فتح السند الجديد بنجاح!\n\n"
                "📋 الميزات الجديدة:\n"
                "• رأس السند: نوع، عملية، عملة، مرفقات\n"
                "• بيانات الصندوق: فرع، مستلم، مرجع، موظف\n"
                "• جدول محسن: 11 عمود شامل\n"
                "• بيانات المستخدم: تتبع كامل\n\n"
                "🎯 يمكنك الآن استخدام السند الجديد!"
            )
            
        except ImportError as e:
            self.status_label.setText(f"❌ خطأ في الاستيراد: {str(e)}")
            QMessageBox.critical(
                self, 
                "خطأ في الاستيراد", 
                f"فشل في استيراد وحدة السند:\n{str(e)}\n\n"
                "تأكد من وجود ملفات النظام في المجلد الصحيح"
            )
        except Exception as e:
            self.status_label.setText(f"❌ خطأ: {str(e)}")
            QMessageBox.critical(
                self, 
                "خطأ", 
                f"فشل في فتح السند:\n{str(e)}\n\n"
                "تفاصيل الخطأ:\n"
                f"النوع: {type(e).__name__}\n"
                f"الرسالة: {str(e)}"
            )


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont("Tahoma", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = SimpleVoucherTestWindow()
    window.show()
    
    print("🔧 تم تشغيل اختبار السند المبسط!")
    print("✅ انقر على زر الاختبار لفتح السند الجديد")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
