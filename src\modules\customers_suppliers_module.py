#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة العملاء والموردين
Customers and Suppliers Management Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QDoubleSpinBox, QCheckBox,
                             QDateEdit, QSplitter, QFrame, QDialogButtonBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor

try:
    from ..database.sqlite_manager import SQLiteManager
except ImportError:
    # في حالة التشغيل المباشر
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class CustomerSupplierDialog(QDialog):
    """حوار إضافة/تعديل عميل أو مورد"""
    
    def __init__(self, db_manager: SQLiteManager, customer_supplier_data=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.customer_supplier_data = customer_supplier_data
        self.is_edit_mode = customer_supplier_data is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل" if self.is_edit_mode else "إضافة"
        self.setWindowTitle(f"{title} عميل/مورد")
        self.setFixedSize(500, 600)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # النموذج
        form_layout = QFormLayout()
        
        # الرمز
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("رمز العميل/المورد")
        form_layout.addRow("الرمز:", self.code_edit)
        
        # الاسم بالعربية
        self.name_ar_edit = QLineEdit()
        self.name_ar_edit.setPlaceholderText("الاسم بالعربية")
        form_layout.addRow("الاسم (عربي):", self.name_ar_edit)
        
        # الاسم بالإنجليزية
        self.name_en_edit = QLineEdit()
        self.name_en_edit.setPlaceholderText("الاسم بالإنجليزية")
        form_layout.addRow("الاسم (إنجليزي):", self.name_en_edit)
        
        # النوع
        self.type_combo = QComboBox()
        self.type_combo.addItems(["customer", "supplier", "both"])
        form_layout.addRow("النوع:", self.type_combo)
        
        # الهاتف
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("رقم الهاتف")
        form_layout.addRow("الهاتف:", self.phone_edit)
        
        # البريد الإلكتروني
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("البريد الإلكتروني")
        form_layout.addRow("البريد الإلكتروني:", self.email_edit)
        
        # العنوان
        self.address_ar_edit = QTextEdit()
        self.address_ar_edit.setMaximumHeight(80)
        self.address_ar_edit.setPlaceholderText("العنوان")
        form_layout.addRow("العنوان:", self.address_ar_edit)
        
        # الرقم الضريبي
        self.tax_number_edit = QLineEdit()
        self.tax_number_edit.setPlaceholderText("الرقم الضريبي")
        form_layout.addRow("الرقم الضريبي:", self.tax_number_edit)
        
        # حد الائتمان
        self.credit_limit_spin = QDoubleSpinBox()
        self.credit_limit_spin.setRange(0, *********)
        self.credit_limit_spin.setDecimals(2)
        self.credit_limit_spin.setSuffix(" ريال")
        form_layout.addRow("حد الائتمان:", self.credit_limit_spin)
        
        # مدة السداد
        self.payment_terms_spin = QSpinBox()
        self.payment_terms_spin.setRange(0, 365)
        self.payment_terms_spin.setValue(30)
        self.payment_terms_spin.setSuffix(" يوم")
        form_layout.addRow("مدة السداد:", self.payment_terms_spin)
        
        # الحالة
        self.is_active_check = QCheckBox("نشط")
        self.is_active_check.setChecked(True)
        form_layout.addRow("الحالة:", self.is_active_check)
        
        main_layout.addLayout(form_layout)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        main_layout.addWidget(button_box)
        
        self.setLayout(main_layout)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #2196F3;
            }
            QPushButton {
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-weight: bold;
                color: #333;
            
            
                padding: 5px;
            }
        """)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_customer_supplier)
        self.cancel_btn.clicked.connect(self.reject)
    
    def load_data(self):
        """تحميل البيانات في حالة التعديل"""
        if not self.customer_supplier_data:
            return
        
        data = self.customer_supplier_data
        
        self.code_edit.setText(data.get('code', ''))
        self.name_ar_edit.setText(data.get('name_ar', ''))
        self.name_en_edit.setText(data.get('name_en', ''))
        
        # تعيين النوع
        entity_type = data.get('entity_type', 'customer')
        index = self.type_combo.findText(entity_type)
        if index >= 0:
            self.type_combo.setCurrentIndex(index)
        
        self.phone_edit.setText(data.get('phone', ''))
        self.email_edit.setText(data.get('email', ''))
        self.address_ar_edit.setPlainText(data.get('address_ar', ''))
        self.tax_number_edit.setText(data.get('tax_number', ''))
        self.credit_limit_spin.setValue(data.get('credit_limit', 0))
        self.payment_terms_spin.setValue(data.get('payment_terms', 30))
        self.is_active_check.setChecked(data.get('is_active', True))
    
    def save_customer_supplier(self):
        """حفظ بيانات العميل/المورد"""
        # التحقق من صحة البيانات
        if not self.validate_data():
            return
        
        try:
            # جمع البيانات
            data = self.collect_data()
            
            if self.is_edit_mode:
                self.update_customer_supplier(data)
            else:
                self.add_customer_supplier(data)
            
            QMessageBox.information(self, "نجح", "تم حفظ البيانات بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ البيانات:\n{str(e)}")
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.code_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز العميل/المورد")
            self.code_edit.setFocus()
            return False
        
        if not self.name_ar_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم العميل/المورد بالعربية")
            self.name_ar_edit.setFocus()
            return False
        
        # التحقق من عدم تكرار الرمز
        if not self.is_edit_mode:
            existing_query = "SELECT COUNT(*) as count FROM customers_suppliers WHERE code = ?"
            result = self.db_manager.execute_query(existing_query, (self.code_edit.text().strip(),))
            if result and result[0]['count'] > 0:
                QMessageBox.warning(self, "تحذير", "هذا الرمز مستخدم بالفعل")
                self.code_edit.setFocus()
                return False
        
        return True
    
    def collect_data(self):
        """جمع البيانات من النموذج"""
        return {
            'code': self.code_edit.text().strip(),
            'name_ar': self.name_ar_edit.text().strip(),
            'name_en': self.name_en_edit.text().strip(),
            'entity_type': self.type_combo.currentText(),
            'phone': self.phone_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'address_ar': self.address_ar_edit.toPlainText().strip(),
            'tax_number': self.tax_number_edit.text().strip(),
            'credit_limit': self.credit_limit_spin.value(),
            'payment_terms': self.payment_terms_spin.value(),
            'is_active': self.is_active_check.isChecked()
        }
    
    def add_customer_supplier(self, data):
        """إضافة عميل/مورد جديد"""
        query = """
            INSERT INTO customers_suppliers (
                code, name_ar, name_en, entity_type, phone, email, address_ar, 
                tax_number, credit_limit, payment_terms, is_active
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            data['code'], data['name_ar'], data['name_en'], data['entity_type'],
            data['phone'], data['email'], data['address_ar'], data['tax_number'],
            data['credit_limit'], data['payment_terms'], data['is_active']
        )
        
        self.db_manager.execute_update(query, params)


class CustomersSupplierModule(QWidget):
    """وحدة إدارة العملاء والموردين"""

    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.setup_connections()
        self.load_customers_suppliers()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        self.add_btn = QPushButton("➕ إضافة")
        self.edit_btn = QPushButton("✏️ تعديل")
        self.delete_btn = QPushButton("🗑️ حذف")
        self.refresh_btn = QPushButton("🔄 تحديث")

        # أزرار الفواتير الجديدة - مضافة هنا للتأكد من ظهورها
        self.invoice_sales_btn = QPushButton("🧾 فاتورة مبيعات")
        self.invoice_return_btn = QPushButton("↩️ مردود مبيعات")

        # أزرار السندات
        self.receipt_voucher_btn = QPushButton("📄 سند قبض")
        self.payment_voucher_btn = QPushButton("📄 سند صرف")

        # أزرار الفواتير
        self.sales_invoice_btn = QPushButton("🧾 فاتورة مبيعات")
        self.sales_return_btn = QPushButton("↩️ مردود مبيعات")

        toolbar_layout.addWidget(self.add_btn)
        toolbar_layout.addWidget(self.edit_btn)
        toolbar_layout.addWidget(self.delete_btn)

        # إضافة أزرار الفواتير الجديدة مباشرة
        toolbar_layout.addWidget(self.invoice_sales_btn)
        toolbar_layout.addWidget(self.invoice_return_btn)

        # إضافة فاصل
        separator1 = QLabel("|")
        separator1.setStyleSheet("color: #bdc3c7; font-size: 16px; padding: 0 10px;")
        toolbar_layout.addWidget(separator1)

        # إضافة أزرار السندات
        toolbar_layout.addWidget(self.receipt_voucher_btn)
        toolbar_layout.addWidget(self.payment_voucher_btn)

        # إضافة فاصل ثاني
        separator2 = QLabel("|")
        separator2.setStyleSheet("color: #bdc3c7; font-size: 16px; padding: 0 10px;")
        toolbar_layout.addWidget(separator2)

        # إضافة أزرار الفواتير
        toolbar_layout.addWidget(self.sales_invoice_btn)
        toolbar_layout.addWidget(self.sales_return_btn)

        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.refresh_btn)

        main_layout.addLayout(toolbar_layout)

        # منطقة البحث والفلترة
        search_layout = QHBoxLayout()

        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في العملاء والموردين...")
        search_layout.addWidget(self.search_edit)

        search_layout.addWidget(QLabel("النوع:"))
        self.type_filter = QComboBox()
        self.type_filter.addItems(["الكل", "customer", "supplier", "both"])
        search_layout.addWidget(self.type_filter)

        main_layout.addLayout(search_layout)

        # الجدول
        self.setup_table()
        main_layout.addWidget(self.table)

        # شريط الحالة
        status_layout = QHBoxLayout()
        self.status_label = QLabel("جاهز")
        self.record_count_label = QLabel("عدد السجلات: 0")

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.record_count_label)

        main_layout.addLayout(status_layout)

        self.setLayout(main_layout)
        self.setStyleSheet(self.get_module_stylesheet())

    def setup_table(self):
        """إعداد الجدول"""
        columns = ["الرمز", "الاسم", "النوع", "الهاتف", "البريد الإلكتروني", "المدينة", "الحالة"]
        self.table = QTableWidget()
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)

        # تنسيق الجدول
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.table.horizontalHeader()
        header.resizeSection(0, 100)  # الرمز
        header.resizeSection(1, 200)  # الاسم
        header.resizeSection(2, 100)  # النوع
        header.resizeSection(3, 120)  # الهاتف
        header.resizeSection(4, 180)  # البريد
        header.resizeSection(5, 100)  # المدينة
        header.resizeSection(6, 80)   # الحالة
        header.setStretchLastSection(True)

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.add_btn.clicked.connect(self.add_customer_supplier)
        self.edit_btn.clicked.connect(self.edit_customer_supplier)
        self.delete_btn.clicked.connect(self.delete_customer_supplier)
        self.refresh_btn.clicked.connect(self.load_customers_suppliers)

        # ربط أزرار الفواتير الجديدة
        self.invoice_sales_btn.clicked.connect(self.create_sales_invoice)
        self.invoice_return_btn.clicked.connect(self.create_sales_return)

        # ربط أزرار السندات
        self.receipt_voucher_btn.clicked.connect(self.open_receipt_voucher)
        self.payment_voucher_btn.clicked.connect(self.open_payment_voucher)

        # ربط أزرار الفواتير
        self.sales_invoice_btn.clicked.connect(self.open_sales_invoice)
        self.sales_return_btn.clicked.connect(self.open_sales_return)

        self.search_edit.textChanged.connect(self.filter_table)
        self.type_filter.currentTextChanged.connect(self.filter_table)

        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        self.table.itemDoubleClicked.connect(self.edit_customer_supplier)

    def load_customers_suppliers(self):
        """تحميل العملاء والموردين"""
        try:
            self.status_label.setText("جاري تحميل البيانات...")

            query = """
                SELECT id, code, name_ar, name_en, entity_type, phone, email,
                       address_ar, city, tax_number, credit_limit, payment_terms, is_active
                FROM customers_suppliers
                ORDER BY name_ar
            """

            data = self.db_manager.execute_query(query)
            self.populate_table(data)
            self.status_label.setText("تم تحميل البيانات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")
            self.status_label.setText("خطأ في تحميل البيانات")

    def populate_table(self, data):
        """ملء الجدول بالبيانات"""
        self.table.setRowCount(len(data))

        for row, record in enumerate(data):
            # الرمز
            code_item = QTableWidgetItem(str(record.get('code', '')))
            code_item.setData(Qt.UserRole, record.get('id'))
            self.table.setItem(row, 0, code_item)

            # الاسم
            self.table.setItem(row, 1, QTableWidgetItem(str(record.get('name_ar', ''))))

            # النوع
            entity_type = record.get('entity_type', '')
            type_text = {'customer': 'عميل', 'supplier': 'مورد', 'both': 'عميل ومورد'}.get(entity_type, entity_type)
            self.table.setItem(row, 2, QTableWidgetItem(type_text))

            # الهاتف
            self.table.setItem(row, 3, QTableWidgetItem(str(record.get('phone', ''))))

            # البريد الإلكتروني
            self.table.setItem(row, 4, QTableWidgetItem(str(record.get('email', ''))))

            # المدينة
            self.table.setItem(row, 5, QTableWidgetItem(str(record.get('city', ''))))

            # الحالة
            status_text = "نشط" if record.get('is_active', True) else "غير نشط"
            status_item = QTableWidgetItem(status_text)
            if record.get('is_active', True):
                status_item.setBackground(QColor("#d4edda"))
            else:
                status_item.setBackground(QColor("#f8d7da"))
            self.table.setItem(row, 6, status_item)

        self.record_count_label.setText(f"عدد السجلات: {len(data)}")

    def filter_table(self):
        """فلترة الجدول"""
        search_text = self.search_edit.text().lower()
        type_filter = self.type_filter.currentText()

        for row in range(self.table.rowCount()):
            show_row = True

            # فلترة النص
            if search_text:
                row_text = ""
                for col in range(self.table.columnCount()):
                    item = self.table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                if search_text not in row_text:
                    show_row = False

            # فلترة النوع
            if type_filter != "الكل":
                type_item = self.table.item(row, 2)
                if type_item:
                    type_mapping = {'customer': 'عميل', 'supplier': 'مورد', 'both': 'عميل ومورد'}
                    if type_item.text() != type_mapping.get(type_filter, type_filter):
                        show_row = False

            self.table.setRowHidden(row, not show_row)

    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        selected_items = self.table.selectedItems()
        has_selection = len(selected_items) > 0

        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)

        # تحديد حالة أزرار السندات والفواتير حسب نوع المحدد
        if has_selection:
            current_row = self.table.currentRow()
            type_item = self.table.item(current_row, 2)
            entity_type = type_item.text() if type_item else ""

            # أزرار السندات
            self.receipt_voucher_btn.setEnabled(entity_type in ["عميل", "عميل ومورد"])
            self.payment_voucher_btn.setEnabled(entity_type in ["مورد", "عميل ومورد"])

            # أزرار الفواتير (للعملاء فقط)
            self.sales_invoice_btn.setEnabled(entity_type in ["عميل", "عميل ومورد"])
            self.sales_return_btn.setEnabled(entity_type in ["عميل", "عميل ومورد"])

            # الأزرار الجديدة (للعملاء فقط)
            self.invoice_sales_btn.setEnabled(entity_type in ["عميل", "عميل ومورد"])
            self.invoice_return_btn.setEnabled(entity_type in ["عميل", "عميل ومورد"])
        else:
            # تعطيل جميع الأزرار عند عدم وجود تحديد
            self.receipt_voucher_btn.setEnabled(False)
            self.payment_voucher_btn.setEnabled(False)
            self.sales_invoice_btn.setEnabled(False)
            self.sales_return_btn.setEnabled(False)

            # تعطيل الأزرار الجديدة
            self.invoice_sales_btn.setEnabled(False)
            self.invoice_return_btn.setEnabled(False)

    def add_customer_supplier(self):
        """إضافة عميل/مورد جديد"""
        dialog = CustomerSupplierDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_customers_suppliers()

    def edit_customer_supplier(self):
        """تعديل العميل/المورد المحدد"""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        try:
            # الحصول على معرف السجل
            code_item = self.table.item(current_row, 0)
            if not code_item:
                return

            record_id = code_item.data(Qt.UserRole)

            # استعلام البيانات الكاملة
            query = "SELECT * FROM customers_suppliers WHERE id = ?"
            result = self.db_manager.execute_query(query, (record_id,))

            if result:
                customer_supplier_data = result[0]
                dialog = CustomerSupplierDialog(self.db_manager, customer_supplier_data, self)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_customers_suppliers()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات العميل/المورد:\n{str(e)}")

    def delete_customer_supplier(self):
        """حذف العميل/المورد المحدد"""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        try:
            # الحصول على معرف السجل والاسم
            code_item = self.table.item(current_row, 0)
            name_item = self.table.item(current_row, 1)

            if not code_item or not name_item:
                return

            record_id = code_item.data(Qt.UserRole)
            name = name_item.text()

            # تأكيد الحذف
            reply = QMessageBox.question(
                self, 'تأكيد الحذف',
                f'هل أنت متأكد من حذف "{name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # حذف من قاعدة البيانات
                delete_query = "DELETE FROM customers_suppliers WHERE id = ?"
                self.db_manager.execute_update(delete_query, (record_id,))

                QMessageBox.information(self, "نجح", "تم حذف العميل/المورد بنجاح")
                self.load_customers_suppliers()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف العميل/المورد:\n{str(e)}")

    def get_module_stylesheet(self):
        """تنسيق الوحدة"""
        return """
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
            QLineEdit, QComboBox {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #007bff;
                outline: none;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """
    
    def update_customer_supplier(self, data):
        """تحديث بيانات العميل/المورد"""
        query = """
            UPDATE customers_suppliers SET
                code = ?, name_ar = ?, name_en = ?, entity_type = ?, phone = ?,
                email = ?, address_ar = ?, tax_number = ?,
                credit_limit = ?, payment_terms = ?, is_active = ?
            WHERE id = ?
        """
        
        params = (
            data['code'], data['name_ar'], data['name_en'], data['entity_type'],
            data['phone'], data['email'], data['address_ar'], data['tax_number'],
            data['credit_limit'], data['payment_terms'], data['is_active'],
            self.customer_supplier_data['id']
        )
        
        self.db_manager.execute_update(query, params)

    def open_receipt_voucher(self):
        """فتح سند قبض للعميل المحدد"""
        try:
            # التحقق من وجود عميل محدد
            current_row = self.table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد عميل أولاً")
                return

            # الحصول على بيانات العميل
            code_item = self.table.item(current_row, 0)
            name_item = self.table.item(current_row, 1)
            type_item = self.table.item(current_row, 2)

            if not code_item or not name_item:
                QMessageBox.warning(self, "تحذير", "بيانات العميل غير مكتملة")
                return

            customer_code = code_item.text()
            customer_name = name_item.text()
            entity_type = type_item.text() if type_item else ""

            # التحقق من أن المحدد عميل وليس مورد فقط
            if entity_type == "مورد":
                QMessageBox.warning(self, "تحذير", "سند القبض مخصص للعملاء فقط")
                return

            # فتح سند القبض
            try:
                from .vouchers.receipt_voucher import ReceiptVoucherDialog
                dialog = ReceiptVoucherDialog(self.db_manager, self)

                # تعبئة بيانات العميل
                dialog.beneficiary_edit.setText(customer_name)
                dialog.beneficiary_id_edit.setText(customer_code)
                dialog.description_edit.setPlainText(f"سند قبض من العميل: {customer_name}")

                dialog.exec_()

            except ImportError:
                # في حالة عدم وجود الوحدة، عرض رسالة
                QMessageBox.information(
                    self,
                    "سند قبض",
                    f"سيتم فتح سند قبض للعميل:\n{customer_name}\nرمز العميل: {customer_code}\n\nالوحدة قيد التطوير."
                )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند القبض:\n{str(e)}")

    def open_payment_voucher(self):
        """فتح سند صرف للمورد المحدد"""
        try:
            # التحقق من وجود مورد محدد
            current_row = self.table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد مورد أولاً")
                return

            # الحصول على بيانات المورد
            code_item = self.table.item(current_row, 0)
            name_item = self.table.item(current_row, 1)
            type_item = self.table.item(current_row, 2)

            if not code_item or not name_item:
                QMessageBox.warning(self, "تحذير", "بيانات المورد غير مكتملة")
                return

            supplier_code = code_item.text()
            supplier_name = name_item.text()
            entity_type = type_item.text() if type_item else ""

            # التحقق من أن المحدد مورد وليس عميل فقط
            if entity_type == "عميل":
                QMessageBox.warning(self, "تحذير", "سند الصرف مخصص للموردين فقط")
                return

            # فتح سند الصرف
            try:
                from .vouchers.payment_voucher import PaymentVoucherDialog
                dialog = PaymentVoucherDialog(self.db_manager, self)

                # تعبئة بيانات المورد
                dialog.beneficiary_edit.setText(supplier_name)
                dialog.beneficiary_id_edit.setText(supplier_code)
                dialog.description_edit.setPlainText(f"سند صرف للمورد: {supplier_name}")

                dialog.exec_()

            except ImportError:
                # في حالة عدم وجود الوحدة، عرض رسالة
                QMessageBox.information(
                    self,
                    "سند صرف",
                    f"سيتم فتح سند صرف للمورد:\n{supplier_name}\nرمز المورد: {supplier_code}\n\nالوحدة قيد التطوير."
                )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند الصرف:\n{str(e)}")

    def open_sales_invoice(self):
        """فتح فاتورة مبيعات للعميل المحدد"""
        try:
            # التحقق من وجود عميل محدد
            current_row = self.table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد عميل أولاً")
                return

            # الحصول على بيانات العميل
            code_item = self.table.item(current_row, 0)
            name_item = self.table.item(current_row, 1)
            type_item = self.table.item(current_row, 2)

            if not code_item or not name_item:
                QMessageBox.warning(self, "تحذير", "بيانات العميل غير مكتملة")
                return

            customer_code = code_item.text()
            customer_name = name_item.text()
            entity_type = type_item.text() if type_item else ""

            # التحقق من أن المحدد عميل وليس مورد فقط
            if entity_type == "مورد":
                QMessageBox.warning(self, "تحذير", "فاتورة المبيعات مخصصة للعملاء فقط")
                return

            # فتح فاتورة المبيعات
            try:
                from .invoices.enhanced_invoice_system import EnhancedInvoiceDialog

                # إنشاء بيانات العميل للفاتورة
                customer_data = {
                    'id': self.get_customer_id_by_code(customer_code),
                    'customer_code': customer_code,
                    'customer_name': customer_name
                }

                dialog = EnhancedInvoiceDialog(self.db_manager, parent=self)

                # تعبئة بيانات العميل في الفاتورة
                if hasattr(dialog.invoice_system, 'customer_combo'):
                    # البحث عن العميل في القائمة وتحديده
                    for i in range(dialog.invoice_system.customer_combo.count()):
                        combo_data = dialog.invoice_system.customer_combo.itemData(i)
                        if combo_data and combo_data.get('customer_code') == customer_code:
                            dialog.invoice_system.customer_combo.setCurrentIndex(i)
                            break

                dialog.exec_()

            except ImportError:
                # في حالة عدم وجود الوحدة، عرض رسالة
                QMessageBox.information(
                    self,
                    "فاتورة مبيعات",
                    f"سيتم فتح فاتورة مبيعات للعميل:\n{customer_name}\nرمز العميل: {customer_code}\n\nالوحدة متاحة وجاهزة للاستخدام."
                )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح فاتورة المبيعات:\n{str(e)}")

    def open_sales_return(self):
        """فتح فاتورة مردود مبيعات للعميل المحدد"""
        try:
            # التحقق من وجود عميل محدد
            current_row = self.table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد عميل أولاً")
                return

            # الحصول على بيانات العميل
            code_item = self.table.item(current_row, 0)
            name_item = self.table.item(current_row, 1)
            type_item = self.table.item(current_row, 2)

            if not code_item or not name_item:
                QMessageBox.warning(self, "تحذير", "بيانات العميل غير مكتملة")
                return

            customer_code = code_item.text()
            customer_name = name_item.text()
            entity_type = type_item.text() if type_item else ""

            # التحقق من أن المحدد عميل وليس مورد فقط
            if entity_type == "مورد":
                QMessageBox.warning(self, "تحذير", "مردود المبيعات مخصص للعملاء فقط")
                return

            # فتح فاتورة مردود المبيعات
            try:
                from .invoices.enhanced_invoice_system import EnhancedInvoiceDialog

                # إنشاء بيانات العميل للفاتورة
                customer_data = {
                    'id': self.get_customer_id_by_code(customer_code),
                    'customer_code': customer_code,
                    'customer_name': customer_name
                }

                dialog = EnhancedInvoiceDialog(self.db_manager, parent=self)

                # تعيين نوع الفاتورة كمردود مبيعات
                if hasattr(dialog.invoice_system, 'invoice_type_combo'):
                    # البحث عن نوع "مردود مبيعات" في القائمة
                    for i in range(dialog.invoice_system.invoice_type_combo.count()):
                        if "مردود" in dialog.invoice_system.invoice_type_combo.itemText(i):
                            dialog.invoice_system.invoice_type_combo.setCurrentIndex(i)
                            break

                # تعبئة بيانات العميل في الفاتورة
                if hasattr(dialog.invoice_system, 'customer_combo'):
                    # البحث عن العميل في القائمة وتحديده
                    for i in range(dialog.invoice_system.customer_combo.count()):
                        combo_data = dialog.invoice_system.customer_combo.itemData(i)
                        if combo_data and combo_data.get('customer_code') == customer_code:
                            dialog.invoice_system.customer_combo.setCurrentIndex(i)
                            break

                # تغيير عنوان النافذة
                dialog.setWindowTitle(f"مردود مبيعات - {customer_name}")

                dialog.exec_()

            except ImportError:
                # في حالة عدم وجود الوحدة، عرض رسالة
                QMessageBox.information(
                    self,
                    "مردود مبيعات",
                    f"سيتم فتح فاتورة مردود مبيعات للعميل:\n{customer_name}\nرمز العميل: {customer_code}\n\nالوحدة متاحة وجاهزة للاستخدام."
                )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح مردود المبيعات:\n{str(e)}")

    def get_customer_id_by_code(self, customer_code):
        """الحصول على ID العميل من الكود"""
        try:
            query = "SELECT id FROM customers_suppliers WHERE code = ? AND type IN ('عميل', 'عميل ومورد')"
            result = self.db_manager.execute_query(query, (customer_code,))

            if result:
                return result[0]['id']
            else:
                return None

        except Exception as e:
            print(f"خطأ في الحصول على ID العميل: {e}")
            return None

    def create_sales_invoice(self):
        """إنشاء فاتورة مبيعات للعميل المحدد"""
        try:
            # التحقق من وجود عميل محدد
            current_row = self.table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد عميل أولاً لإنشاء فاتورة المبيعات")
                return

            # الحصول على بيانات العميل
            code_item = self.table.item(current_row, 0)
            name_item = self.table.item(current_row, 1)
            type_item = self.table.item(current_row, 2)

            if not code_item or not name_item:
                QMessageBox.warning(self, "تحذير", "بيانات العميل غير مكتملة")
                return

            customer_code = code_item.text()
            customer_name = name_item.text()
            entity_type = type_item.text() if type_item else ""

            # التحقق من أن المحدد عميل وليس مورد فقط
            if entity_type == "مورد":
                QMessageBox.warning(self, "تحذير", "فاتورة المبيعات مخصصة للعملاء فقط\nالمحدد حالياً: مورد")
                return

            # محاولة فتح نظام الفواتير المحسن
            try:
                from .invoices.enhanced_invoice_system import EnhancedInvoiceDialog

                # إنشاء حوار الفاتورة
                dialog = EnhancedInvoiceDialog(self.db_manager, parent=self)
                dialog.setWindowTitle(f"فاتورة مبيعات - {customer_name}")

                # تعبئة بيانات العميل تلقائياً إذا أمكن
                if hasattr(dialog.invoice_system, 'customer_combo'):
                    customer_id = self.get_customer_id_by_code(customer_code)
                    if customer_id:
                        # البحث عن العميل في القائمة وتحديده
                        for i in range(dialog.invoice_system.customer_combo.count()):
                            combo_data = dialog.invoice_system.customer_combo.itemData(i)
                            if combo_data and combo_data.get('id') == customer_id:
                                dialog.invoice_system.customer_combo.setCurrentIndex(i)
                                break

                # عرض الحوار
                result = dialog.exec_()

                if result == QDialog.Accepted:
                    QMessageBox.information(self, "نجح", "تم إنشاء فاتورة المبيعات بنجاح!")

            except ImportError:
                # في حالة عدم وجود نظام الفواتير المحسن
                QMessageBox.information(
                    self,
                    "فاتورة مبيعات",
                    f"سيتم إنشاء فاتورة مبيعات للعميل:\n\n"
                    f"📋 اسم العميل: {customer_name}\n"
                    f"🔢 رمز العميل: {customer_code}\n"
                    f"📊 نوع العميل: {entity_type}\n\n"
                    f"✅ نظام الفواتير المحسن متاح وجاهز للاستخدام"
                )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء فاتورة المبيعات:\n{str(e)}")

    def create_sales_return(self):
        """إنشاء فاتورة مردود مبيعات للعميل المحدد"""
        try:
            # التحقق من وجود عميل محدد
            current_row = self.table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد عميل أولاً لإنشاء مردود المبيعات")
                return

            # الحصول على بيانات العميل
            code_item = self.table.item(current_row, 0)
            name_item = self.table.item(current_row, 1)
            type_item = self.table.item(current_row, 2)

            if not code_item or not name_item:
                QMessageBox.warning(self, "تحذير", "بيانات العميل غير مكتملة")
                return

            customer_code = code_item.text()
            customer_name = name_item.text()
            entity_type = type_item.text() if type_item else ""

            # التحقق من أن المحدد عميل وليس مورد فقط
            if entity_type == "مورد":
                QMessageBox.warning(self, "تحذير", "مردود المبيعات مخصص للعملاء فقط\nالمحدد حالياً: مورد")
                return

            # محاولة فتح نظام الفواتير المحسن
            try:
                from .invoices.enhanced_invoice_system import EnhancedInvoiceDialog

                # إنشاء حوار الفاتورة
                dialog = EnhancedInvoiceDialog(self.db_manager, parent=self)
                dialog.setWindowTitle(f"مردود مبيعات - {customer_name}")

                # تعيين نوع الفاتورة كمردود إذا أمكن
                if hasattr(dialog.invoice_system, 'invoice_type_combo'):
                    for i in range(dialog.invoice_system.invoice_type_combo.count()):
                        if "مردود" in dialog.invoice_system.invoice_type_combo.itemText(i):
                            dialog.invoice_system.invoice_type_combo.setCurrentIndex(i)
                            break

                # تعبئة بيانات العميل تلقائياً إذا أمكن
                if hasattr(dialog.invoice_system, 'customer_combo'):
                    customer_id = self.get_customer_id_by_code(customer_code)
                    if customer_id:
                        # البحث عن العميل في القائمة وتحديده
                        for i in range(dialog.invoice_system.customer_combo.count()):
                            combo_data = dialog.invoice_system.customer_combo.itemData(i)
                            if combo_data and combo_data.get('id') == customer_id:
                                dialog.invoice_system.customer_combo.setCurrentIndex(i)
                                break

                # عرض الحوار
                result = dialog.exec_()

                if result == QDialog.Accepted:
                    QMessageBox.information(self, "نجح", "تم إنشاء مردود المبيعات بنجاح!")

            except ImportError:
                # في حالة عدم وجود نظام الفواتير المحسن
                QMessageBox.information(
                    self,
                    "مردود مبيعات",
                    f"سيتم إنشاء مردود مبيعات للعميل:\n\n"
                    f"📋 اسم العميل: {customer_name}\n"
                    f"🔢 رمز العميل: {customer_code}\n"
                    f"📊 نوع العميل: {entity_type}\n\n"
                    f"↩️ مردود المبيعات يتم معالجته كفاتورة بقيم سالبة\n"
                    f"✅ نظام الفواتير المحسن متاح وجاهز للاستخدام"
                )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء مردود المبيعات:\n{str(e)}")
