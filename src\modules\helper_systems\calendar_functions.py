#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف نظام التقويم والمواعيد
Calendar System Functions
"""

from datetime import datetime, date
from PyQt5.QtWidgets import QMessageBox, QListWidgetItem
from PyQt5.QtCore import Qt, QDate

def load_sample_data_function(self):
    """تحميل البيانات النموذجية"""
    # مواعيد نموذجية
    self.appointments = [
        {
            "title": "اجتماع فريق العمل",
            "date": "2024-01-15",
            "time": "09:00",
            "duration": 60,
            "description": "اجتماع أسبوعي لمراجعة التقدم"
        },
        {
            "title": "موعد طبي",
            "date": "2024-01-16",
            "time": "14:30",
            "duration": 30,
            "description": "فحص دوري"
        },
        {
            "title": "عرض تقديمي",
            "date": "2024-01-17",
            "time": "11:00",
            "duration": 90,
            "description": "عرض المشروع الجديد"
        },
        {
            "title": "اجتماع مع العميل",
            "date": "2024-01-18",
            "time": "16:00",
            "duration": 45,
            "description": "مناقشة متطلبات المشروع الجديد"
        },
        {
            "title": "ورشة تدريبية",
            "date": "2024-01-19",
            "time": "10:00",
            "duration": 120,
            "description": "ورشة تدريبية حول التقنيات الجديدة"
        }
    ]
    
    # أحداث نموذجية
    self.events = [
        {
            "title": "عيد الفطر المبارك",
            "date": "2024-04-10",
            "type": "عطلة رسمية",
            "description": "عطلة عيد الفطر المبارك"
        },
        {
            "title": "يوم الاستقلال",
            "date": "2024-05-22",
            "type": "عطلة وطنية",
            "description": "ذكرى استقلال الجمهورية اليمنية"
        },
        {
            "title": "يوم الثورة",
            "date": "2024-09-26",
            "type": "عطلة وطنية",
            "description": "ذكرى ثورة 26 سبتمبر"
        },
        {
            "title": "عيد الأضحى المبارك",
            "date": "2024-06-16",
            "type": "عطلة رسمية",
            "description": "عطلة عيد الأضحى المبارك"
        }
    ]
    
    # تذكيرات نموذجية
    self.reminders = [
        {
            "title": "تجديد الرخصة",
            "date": "2024-02-01",
            "priority": "عالية",
            "description": "تجديد رخصة القيادة"
        },
        {
            "title": "دفع الفواتير",
            "date": "2024-01-25",
            "priority": "متوسطة",
            "description": "دفع فواتير الكهرباء والماء"
        },
        {
            "title": "موعد طبيب الأسنان",
            "date": "2024-02-05",
            "priority": "عالية",
            "description": "فحص دوري للأسنان"
        },
        {
            "title": "تجديد التأمين",
            "date": "2024-03-15",
            "priority": "متوسطة",
            "description": "تجديد تأمين السيارة"
        }
    ]
    
    self.update_appointments_list()
    self.update_events_list()
    self.update_reminders_list()
    self.update_selected_date()

def on_date_selected_function(self):
    """عند تحديد تاريخ في التقويم"""
    self.update_selected_date()

def update_selected_date_function(self):
    """تحديث معلومات التاريخ المحدد"""
    selected_date = self.calendar.selectedDate()
    
    # التاريخ الميلادي
    gregorian_date = selected_date.toString("dddd، dd MMMM yyyy")
    self.selected_date_label.setText(f"📅 {gregorian_date}")
    
    # التاريخ الهجري (تقريبي)
    hijri_date = self.convert_to_hijri(selected_date)
    self.hijri_date_label.setText(f"🌙 {hijri_date}")
    
    # مواعيد اليوم
    self.update_today_appointments(selected_date)

def convert_to_hijri_function(self, gregorian_date):
    """تحويل التاريخ الميلادي إلى هجري (تقريبي)"""
    # تحويل تقريبي - يمكن تحسينه باستخدام مكتبة متخصصة
    gregorian_year = gregorian_date.year()
    gregorian_month = gregorian_date.month()
    gregorian_day = gregorian_date.day()
    
    # حساب تقريبي للسنة الهجرية
    hijri_year = int((gregorian_year - 622) * 1.030684)
    
    # أسماء الشهور الهجرية
    hijri_months = [
        "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الثانية",
        "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
    ]
    
    # تقدير تقريبي للشهر الهجري
    hijri_month_index = (gregorian_month - 1) % 12
    hijri_month = hijri_months[hijri_month_index]
    
    return f"{gregorian_day} {hijri_month} {hijri_year} هـ"

def update_today_appointments_function(self, selected_date):
    """تحديث مواعيد اليوم المحدد"""
    self.today_appointments_list.clear()
    
    date_str = selected_date.toString("yyyy-MM-dd")
    
    appointments_found = False
    for appointment in self.appointments:
        if appointment["date"] == date_str:
            appointments_found = True
            item_text = f"⏰ {appointment['time']} - {appointment['title']}"
            if appointment.get('duration'):
                item_text += f" ({appointment['duration']} دقيقة)"
            
            item = QListWidgetItem(item_text)
            item.setToolTip(appointment.get('description', ''))
            self.today_appointments_list.addItem(item)
    
    if not appointments_found:
        item = QListWidgetItem("📭 لا توجد مواعيد في هذا اليوم")
        item.setFlags(item.flags() & ~Qt.ItemIsSelectable)
        self.today_appointments_list.addItem(item)

def update_appointments_list_function(self):
    """تحديث قائمة المواعيد"""
    self.appointments_list.clear()
    
    # ترتيب المواعيد حسب التاريخ والوقت
    sorted_appointments = sorted(self.appointments, key=lambda x: (x['date'], x['time']))
    
    for appointment in sorted_appointments:
        item_text = f"📅 {appointment['date']} ⏰ {appointment['time']} - {appointment['title']}"
        if appointment.get('duration'):
            item_text += f" ({appointment['duration']} دقيقة)"
        if appointment.get('description'):
            item_text += f"\n📝 {appointment['description']}"
        
        item = QListWidgetItem(item_text)
        self.appointments_list.addItem(item)

def update_events_list_function(self):
    """تحديث قائمة الأحداث"""
    self.events_list.clear()
    
    # ترتيب الأحداث حسب التاريخ
    sorted_events = sorted(self.events, key=lambda x: x['date'])
    
    for event in sorted_events:
        item_text = f"🎉 {event['title']} - {event['date']}"
        if event.get('type'):
            item_text += f" ({event['type']})"
        if event.get('description'):
            item_text += f"\n📝 {event['description']}"
        
        item = QListWidgetItem(item_text)
        self.events_list.addItem(item)

def update_reminders_list_function(self):
    """تحديث قائمة التذكيرات"""
    self.reminders_list.clear()
    
    # ترتيب التذكيرات حسب الأولوية والتاريخ
    priority_order = {"عالية": 1, "متوسطة": 2, "منخفضة": 3}
    sorted_reminders = sorted(self.reminders, key=lambda x: (priority_order.get(x['priority'], 4), x['date']))
    
    for reminder in sorted_reminders:
        priority_icon = "🔴" if reminder['priority'] == "عالية" else "🟡" if reminder['priority'] == "متوسطة" else "🟢"
        item_text = f"{priority_icon} {reminder['title']} - {reminder['date']}"
        if reminder.get('description'):
            item_text += f"\n📝 {reminder['description']}"
        
        item = QListWidgetItem(item_text)
        self.reminders_list.addItem(item)

def add_appointment_function(self):
    """إضافة موعد جديد"""
    title = self.appointment_title.text().strip()
    if not title:
        QMessageBox.warning(self, "تحذير", "يرجى إدخال عنوان الموعد")
        return
    
    # جمع بيانات الموعد
    appointment = {
        "title": title,
        "date": self.appointment_date.date().toString("yyyy-MM-dd"),
        "time": self.appointment_time.time().toString("HH:mm"),
        "duration": self.appointment_duration.value(),
        "description": self.appointment_description.toPlainText().strip()
    }
    
    # إضافة الموعد
    self.appointments.append(appointment)
    
    # تحديث القوائم
    self.update_appointments_list()
    self.update_selected_date()
    
    # مسح النموذج
    self.appointment_title.clear()
    self.appointment_description.clear()
    self.appointment_time.setTime(self.appointment_time.time().addSecs(3600))  # إضافة ساعة للموعد التالي
    
    QMessageBox.information(self, "نجح", "تم إضافة الموعد بنجاح")

def edit_appointment_function(self):
    """تعديل موعد محدد"""
    current_row = self.appointments_list.currentRow()
    if current_row < 0:
        QMessageBox.warning(self, "تحذير", "يرجى تحديد موعد للتعديل")
        return
    
    QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل المواعيد قيد التطوير")

def delete_appointment_function(self):
    """حذف موعد محدد"""
    current_row = self.appointments_list.currentRow()
    if current_row < 0:
        QMessageBox.warning(self, "تحذير", "يرجى تحديد موعد للحذف")
        return
    
    reply = QMessageBox.question(self, "تأكيد الحذف", "هل أنت متأكد من حذف هذا الموعد؟",
                                QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
    
    if reply == QMessageBox.Yes:
        # حذف الموعد
        del self.appointments[current_row]
        
        # تحديث القوائم
        self.update_appointments_list()
        self.update_selected_date()
        
        QMessageBox.information(self, "نجح", "تم حذف الموعد بنجاح")

def show_help_function(self):
    """عرض المساعدة"""
    help_text = """
📅 مساعدة نظام التقويم والمواعيد

📋 الميزات المتاحة:

📅 التقويم الرئيسي:
• عرض التقويم الميلادي التفاعلي
• عرض التاريخ الهجري التقريبي
• عرض مواعيد اليوم المحدد
• التنقل بين الأشهر والسنوات

⏰ إدارة المواعيد:
• إضافة مواعيد جديدة مع التفاصيل
• تحديد التاريخ والوقت والمدة
• إضافة وصف للموعد
• عرض قائمة جميع المواعيد
• تعديل وحذف المواعيد

🎉 إدارة الأحداث:
• عرض الأحداث والعطل الرسمية
• تصنيف الأحداث حسب النوع
• إضافة أحداث مخصصة
• تذكيرات للأحداث المهمة

🔔 إدارة التذكيرات:
• إنشاء تذكيرات مع أولويات
• تصنيف التذكيرات حسب الأهمية
• عرض التذكيرات مرتبة حسب الأولوية
• تذكيرات للمهام المهمة

💡 نصائح الاستخدام:
• انقر على التاريخ في التقويم لعرض مواعيد اليوم
• استخدم الألوان للتمييز بين أنواع المواعيد
• احفظ تفاصيل مهمة في وصف الموعد
• راجع التذكيرات بانتظام

🎨 الميزات التصميمية:
• واجهة عربية احترافية
• تقويم تفاعلي سهل الاستخدام
• ألوان مميزة لكل نوع من البيانات
• تخطيط منظم ومرتب

📱 سهولة الاستخدام:
• واجهة بديهية وسهلة
• تبويبات منظمة للوظائف
• رسائل تأكيد واضحة
• مساعدة شاملة متاحة

🔧 الوظائف المتقدمة:
• تحويل التاريخ الهجري (تقريبي)
• ترتيب المواعيد حسب التاريخ
• تصنيف التذكيرات حسب الأولوية
• عرض تفاصيل شاملة لكل عنصر
"""
    
    QMessageBox.information(self, "المساعدة", help_text)
