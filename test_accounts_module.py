#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوحدة إدارة الحسابات
Comprehensive test for accounts management module
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_accounts_module_functionality():
    """اختبار وظائف وحدة الحسابات"""
    print("🧪 اختبار وظائف وحدة إدارة الحسابات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.accounts_module import AccountsManagementWidget, AccountDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار إنشاء وحدة الحسابات
        accounts_widget = AccountsManagementWidget(db_manager)
        print("✅ تم إنشاء وحدة إدارة الحسابات")
        
        # اختبار تحميل الحسابات
        accounts_widget.load_accounts()
        print("✅ تم تحميل الحسابات من قاعدة البيانات")
        
        # اختبار عدد الحسابات المحملة
        row_count = accounts_widget.accounts_table.rowCount()
        print(f"📊 عدد الحسابات المحملة: {row_count}")
        
        # اختبار إنشاء حوار إضافة حساب
        add_dialog = AccountDialog(db_manager)
        print("✅ تم إنشاء حوار إضافة حساب جديد")
        
        # اختبار إنشاء حوار تعديل حساب
        if row_count > 0:
            # الحصول على أول حساب للاختبار
            first_account_code = accounts_widget.accounts_table.item(0, 0).text()
            edit_dialog = AccountDialog(db_manager, first_account_code)
            print(f"✅ تم إنشاء حوار تعديل الحساب: {first_account_code}")
            edit_dialog.close()
        
        # اختبار البحث
        accounts_widget.search_code.setText("1000")
        accounts_widget.search_accounts()
        print("✅ تم اختبار وظيفة البحث")

        # اختبار الفلترة حسب النوع
        accounts_widget.search_type.setCurrentIndex(1)  # أصول
        accounts_widget.search_accounts()
        print("✅ تم اختبار وظيفة الفلترة")

        # إعادة تعيين الفلترة
        accounts_widget.search_type.setCurrentIndex(0)  # الكل
        accounts_widget.search_code.clear()
        accounts_widget.search_accounts()
        print("✅ تم إعادة تعيين الفلترة")
        
        # تنظيف
        add_dialog.close()
        accounts_widget.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وحدة الحسابات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_account_dialog_functionality():
    """اختبار وظائف حوار الحساب"""
    print("\n🧪 اختبار وظائف حوار الحساب...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.accounts_module import AccountDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار حوار إضافة حساب جديد
        dialog = AccountDialog(db_manager)
        
        # اختبار تحميل الحسابات الأب
        dialog.load_parent_accounts()
        parent_count = dialog.parent_combo.count()
        print(f"✅ تم تحميل {parent_count} حساب أب")
        
        # اختبار ملء البيانات
        dialog.code_edit.setText("TEST001")
        dialog.name_ar_edit.setText("حساب اختبار")
        dialog.name_en_edit.setText("Test Account")
        dialog.type_combo.setCurrentIndex(0)  # أصول
        dialog.level_spin.setValue(2)
        dialog.active_check.setChecked(True)
        dialog.notes_edit.setPlainText("هذا حساب للاختبار")
        
        print("✅ تم ملء بيانات الحساب الاختباري")
        
        # اختبار التحقق من صحة البيانات
        account_data = {
            'account_code': dialog.code_edit.text().strip(),
            'account_name_ar': dialog.name_ar_edit.text().strip(),
            'account_name_en': dialog.name_en_edit.text().strip(),
            'account_type': ['asset', 'liability', 'equity', 'revenue', 'expense'][dialog.type_combo.currentIndex()],
            'parent_id': dialog.parent_combo.currentData() or None,
            'level': dialog.level_spin.value(),
            'is_active': dialog.active_check.isChecked(),
            'notes': dialog.notes_edit.toPlainText().strip() or None
        }
        
        # التحقق من البيانات
        if account_data['account_code'] and account_data['account_name_ar']:
            print("✅ بيانات الحساب صحيحة")
        else:
            print("❌ بيانات الحساب غير مكتملة")
        
        # تنظيف
        dialog.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حوار الحساب: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_accounts_database_operations():
    """اختبار عمليات قاعدة البيانات للحسابات"""
    print("\n🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار استعلام الحسابات
        query = """
            SELECT account_code, account_name_ar, account_name_en, 
                   account_type, parent_id, level, is_active, notes
            FROM chart_of_accounts 
            ORDER BY account_code
        """
        
        accounts = db_manager.execute_query(query)
        print(f"✅ تم استعلام {len(accounts)} حساب من قاعدة البيانات")
        
        # اختبار استعلام أنواع الحسابات
        types_query = "SELECT DISTINCT account_type FROM chart_of_accounts"
        types = db_manager.execute_query(types_query)
        print(f"✅ تم العثور على {len(types)} نوع حساب مختلف")
        
        # عرض أنواع الحسابات
        for account_type in types:
            type_count_query = "SELECT COUNT(*) as count FROM chart_of_accounts WHERE account_type = ?"
            count_result = db_manager.execute_query(type_count_query, (account_type['account_type'],))
            count = count_result[0]['count'] if count_result else 0
            print(f"   📋 {account_type['account_type']}: {count} حساب")
        
        # اختبار البحث في الحسابات
        search_query = """
            SELECT COUNT(*) as count FROM chart_of_accounts 
            WHERE account_code LIKE ? OR account_name_ar LIKE ?
        """
        search_result = db_manager.execute_query(search_query, ('%1000%', '%نقدية%'))
        search_count = search_result[0]['count'] if search_result else 0
        print(f"✅ تم العثور على {search_count} حساب يحتوي على '1000' أو 'نقدية'")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار شامل لوحدة إدارة الحسابات")
    print("   Comprehensive test for accounts management module")
    print("=" * 60)
    
    # اختبار وظائف الوحدة
    module_test = test_accounts_module_functionality()
    
    # اختبار حوار الحساب
    dialog_test = test_account_dialog_functionality()
    
    # اختبار عمليات قاعدة البيانات
    database_test = test_accounts_database_operations()
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار وحدة الحسابات:")
    print(f"   🏗️ اختبار الوحدة: {'✅ نجح' if module_test else '❌ فشل'}")
    print(f"   💬 اختبار الحوار: {'✅ نجح' if dialog_test else '❌ فشل'}")
    print(f"   🗄️ اختبار قاعدة البيانات: {'✅ نجح' if database_test else '❌ فشل'}")
    
    if module_test and dialog_test and database_test:
        print("\n🎉 جميع اختبارات وحدة الحسابات نجحت!")
        print("✅ وحدة إدارة الحسابات جاهزة للاستخدام")
    else:
        print("\n⚠️ بعض اختبارات وحدة الحسابات فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)
    
    return 0 if (module_test and dialog_test and database_test) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
