<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
<meta charset="UTF-8" />
<title>فاتورة مبيعات - نظام Onyx ERP</title>
<style>
  @import url('https://fonts.googleapis.com/css2?family=Cairo&display=swap');

  body {
    font-family: 'Cairo', Tahoma, sans-serif;
    background: #eef2f7;
    margin: 20px;
    direction: rtl;
    height: 95vh;
    color: #333;
  }
  .invoice {
    max-width: 920px;
    margin: auto;
    background: #fff;
    padding: 25px 30px 30px 30px;
    border-radius: 12px;
    box-shadow: 0 6px 18px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  /* Toolbar */
  .toolbar {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    margin-bottom: 20px;
    flex-wrap: nowrap;
  }
  .toolbar button {
    flex: 1;
    padding: 12px 0;
    background: linear-gradient(135deg, #4a90e2, #357ABD);
    border: none;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    box-shadow: 0 4px 8px rgba(53,122,189,0.4);
    transition: background 0.3s, box-shadow 0.3s;
    margin: 0 6px;
  }
  .toolbar button:first-child {
    margin-right: 0;
  }
  .toolbar button:last-child {
    margin-left: 0;
  }
  .toolbar button:hover {
    background: linear-gradient(135deg, #357ABD, #2b5f94);
    box-shadow: 0 6px 14px rgba(43,95,148,0.6);
  }

  /* الرأس */
  .header {
    flex: 0 0 15%;
    font-size: 15px;
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 14px;
  }
  /* صفوف الرأس: السماح باللف عند الحاجة */
  .header .row {
    display: flex;
    gap: 30px;
    align-items: center;
    flex-wrap: wrap;
  }
  /* مجموعة label + input/select */
  .field-group {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 180px;
    flex: 1 1 180px;
    white-space: nowrap;
  }
  .field-group label {
    min-width: 110px;
    font-weight: 700;
    text-align: right;
    color: #222;
    user-select: none;
    flex-shrink: 0;
  }
  .field-group input,
  .field-group select {
    flex: 1 1 auto;
    padding: 8px 12px;
    border: 1.8px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
    transition: border-color 0.3s, box-shadow 0.3s;
  }
  .field-group input:focus,
  .field-group select:focus {
    border-color: #4a90e2;
    box-shadow: 0 0 6px rgba(74,144,226,0.6);
    outline: none;
  }
  /* عرض الحقول المحددة */
  #invoiceDate {
    width: 140px;
    min-width: 140px;
  }
  #currency {
    width: 110px;
    min-width: 110px;
  }

  /* جدول الأصناف */
  .items-table-container {
    flex: 1 1 70%;
    overflow-y: auto;
    border: 1.5px solid #ddd;
    border-radius: 10px;
    padding: 8px;
    background: #fff;
    box-shadow: inset 0 0 12px #f0f4fa;
  }
  table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    word-wrap: break-word;
    font-size: 15px;
  }
  table th, table td {
    border: 1.5px solid #ddd;
    padding: 10px 8px;
    text-align: center;
    background: #fafafa;
    transition: background-color 0.3s;
  }
  table th {
    background: #4a90e2;
    color: white;
    font-weight: 700;
  }
  table tbody tr:hover td {
    background: #e6f0fb;
  }
  table input {
    width: 100%;
    padding: 7px 10px;
    box-sizing: border-box;
    border: 1.2px solid #ccc;
    border-radius: 5px;
    font-size: 14px;
    text-align: center;
    transition: border-color 0.3s;
  }
  table input:focus {
    border-color: #4a90e2;
    outline: none;
    background: #f0f7ff;
  }

  /* التذييل */
  .footer {
    flex: 0 0 15%;
    font-size: 14px;
    color: #444;
    margin-top: 18px;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .summary {
    font-weight: 700;
    margin-bottom: 12px;
    font-size: 16px;
    text-align: left;
    direction: ltr;
    color: #1a1a1a;
  }
  .summary div {
    margin-bottom: 8px;
  }
  .footer-info {
    display: flex;
    justify-content: space-between;
    gap: 14px;
    flex-wrap: wrap;
  }
  .footer-info div {
    flex: 1 1 200px;
    padding: 8px 12px;
    border: 1.5px solid #e3e7ee;
    border-radius: 8px;
    background-color: #f7f9fc;
    box-shadow: inset 0 0 5px #d9dfea;
    font-size: 13px;
    color: #555;
    user-select: none;
  }

  /* استجابة الشاشة */
  @media (max-width: 800px) {
    .header .row {
      gap: 20px;
    }
    .field-group {
      min-width: 140px;
      flex: 1 1 140px;
    }
    #invoiceDate {
      width: 120px;
      min-width: 120px;
    }
    #currency {
      width: 90px;
      min-width: 90px;
    }
  }
</style>
</head>
<body>
  <div class="invoice">

    <!-- Toolbar -->
    <div class="toolbar">
      <button onclick="addInvoice()">إضافة</button>
      <button onclick="addNewInvoice()">إضافة جديدة</button>
      <button onclick="editInvoice()">تعديل</button>
      <button onclick="saveInvoice()">حفظ</button>
      <button onclick="printInvoice()">طباعة</button>
      <button onclick="previousInvoice()">السابق</button>
      <button onclick="nextInvoice()">التالي</button>
    </div>

    <!-- الرأس -->
    <div class="header">
      <div class="row">
        <div class="field-group">
          <label for="paymentMethod">طريقة الدفع:</label>
          <select id="paymentMethod">
            <option>نقدي</option>
            <option>آجل</option>
            <option>شيك</option>
            <option>تحويل إلى حساب</option>
          </select>
        </div>

        <div class="field-group">
          <label for="invoiceNumber">رقم الفاتورة:</label>
          <input type="text" id="invoiceNumber" value="تلقائي" readonly />
        </div>

        <div class="field-group" style="min-width: auto; flex: none; display: flex; align-items: center; gap: 6px;">
          <label for="invoiceDate">تاريخ الفاتورة:</label>
          <input type="date" id="invoiceDate" />
        </div>
      </div>

      <div class="row" style="margin-top:10px;">
        <div class="field-group">
          <label for="customerNumber">رقم العميل:</label>
          <input type="text" id="customerNumber" onchange="loadCustomerName()" />
        </div>

        <div class="field-group">
          <label for="customerName">اسم العميل:</label>
          <input type="text" id="customerName" readonly />
        </div>

        <div class="field-group" style="min-width: auto; flex: none; display: flex; align-items: center; gap: 6px;">
          <label for="currency">العملة:</label>
          <select id="currency">
            <option>ريال يمني</option>
            <option>ريال سعودي</option>
            <option>دولار أمريكي</option>
          </select>
        </div>
      </div>
    </div>

    <!-- جدول الأصناف -->
    <div class="items-table-container">
      <table id="itemsTable">
        <thead>
          <tr>
            <th>رقم الصنف</th>
            <th>اسم الصنف</th>
            <th>الوحدة</th>
            <th>الكمية</th>
            <th>السعر</th>
            <th>الإجمالي</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><input type="text" onchange="calculateRowTotal(this)" /></td>
            <td><input type="text" /></td>
            <td><input type="text" /></td>
            <td><input type="number" min="0" onchange="calculateRowTotal(this)" /></td>
            <td><input type="number" min="0" onchange="calculateRowTotal(this)" /></td>
            <td><input type="number" readonly /></td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- التذييل -->
    <div class="footer">
      <div class="summary">
        <div>الإجمالي: <span id="totalAmount">0.00</span></div>
        <div>الخصم: <span id="discount">0.00</span></div>
        <div>الضريبة: <span id="tax">0.00</span></div>
        <div>الإجمالي النهائي: <span id="finalTotal">0.00</span></div>
      </div>

      <div class="footer-info">
        <div>اسم المستخدم: admin</div>
        <div>التاريخ والوقت: <span id="currentDateTime"></span></div>
        <div>تاريخ التعديل: __________</div>
        <div>عدد التعديلات / عدد الطباعة: 0 / 0</div>
      </div>
    </div>

  </div>

  <script>
    // تعيين التاريخ الحالي
    document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('currentDateTime').textContent = new Date().toLocaleString('ar-EG');

    // وظائف الأزرار
    function addInvoice() { alert('إضافة فاتورة'); }
    function addNewInvoice() { alert('إضافة فاتورة جديدة'); }
    function editInvoice() { alert('تعديل الفاتورة'); }
    function saveInvoice() { alert('حفظ الفاتورة'); }
    function printInvoice() { window.print(); }
    function previousInvoice() { alert('الفاتورة السابقة'); }
    function nextInvoice() { alert('الفاتورة التالية'); }

    // تحميل اسم العميل
    function loadCustomerName() {
      const customerNumber = document.getElementById('customerNumber').value;
      if (customerNumber) {
        document.getElementById('customerName').value = 'عميل رقم ' + customerNumber;
      }
    }

    // حساب إجمالي الصف
    function calculateRowTotal(element) {
      const row = element.closest('tr');
      const quantity = parseFloat(row.cells[3].querySelector('input').value) || 0;
      const price = parseFloat(row.cells[4].querySelector('input').value) || 0;
      const total = quantity * price;
      row.cells[5].querySelector('input').value = total.toFixed(2);
      calculateGrandTotal();
    }

    // حساب الإجمالي العام
    function calculateGrandTotal() {
      let total = 0;
      const rows = document.querySelectorAll('#itemsTable tbody tr');
      rows.forEach(row => {
        const rowTotal = parseFloat(row.cells[5].querySelector('input').value) || 0;
        total += rowTotal;
      });
      
      document.getElementById('totalAmount').textContent = total.toFixed(2);
      document.getElementById('finalTotal').textContent = total.toFixed(2);
    }
  </script>
</body>
</html>
