#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مباشر لوحدة العملاء مع الأزرار الجديدة
Direct Test for Customers Module with New Buttons
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
        from src.modules.customers_suppliers_module import CustomersSupplierModule
        from src.database.sqlite_manager import SQLiteManager

        print("🚀 تشغيل وحدة العملاء مباشرة...")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # إنشاء النافذة الرئيسية
        main_window = QMainWindow()
        main_window.setWindowTitle("اختبار وحدة العملاء والموردين - مع أزرار الفواتير")
        main_window.setGeometry(100, 100, 1200, 800)
        
        # إنشاء وحدة العملاء
        customers_module = CustomersSupplierModule(db_manager)
        
        # التحقق من وجود الأزرار الجديدة
        print("\n🔍 فحص الأزرار الجديدة:")
        if hasattr(customers_module, 'sales_invoice_btn'):
            print(f"✅ زر فاتورة المبيعات: {customers_module.sales_invoice_btn.text()}")
        else:
            print("❌ زر فاتورة المبيعات غير موجود")
            
        if hasattr(customers_module, 'sales_return_btn'):
            print(f"✅ زر مردود المبيعات: {customers_module.sales_return_btn.text()}")
        else:
            print("❌ زر مردود المبيعات غير موجود")
        
        # إعداد النافذة
        main_window.setCentralWidget(customers_module)
        
        # عرض النافذة
        main_window.show()
        
        print("\n🎯 النافذة مفتوحة الآن!")
        print("📋 يمكنك الآن رؤية الأزرار الجديدة:")
        print("   🧾 فاتورة مبيعات")
        print("   ↩️ مردود مبيعات")
        print("\n💡 لاختبار الأزرار:")
        print("   1. حدد عميل من القائمة")
        print("   2. اضغط على أحد الأزرار الجديدة")
        print("   3. ستظهر رسالة تأكيد أو نافذة الفاتورة")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل وحدة العملاء: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
