#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow
from src.modules.customers_suppliers_module import CustomersSupplierModule
from src.database.sqlite_manager import SQLiteManager

def main():
    app = QApplication(sys.argv)
    
    # إنشاء قاعدة البيانات
    db_manager = SQLiteManager()
    
    # إنشاء النافذة الرئيسية
    window = QMainWindow()
    window.setWindowTitle("وحدة العملاء والموردين - مع أزرار الفواتير الجديدة")
    window.setGeometry(100, 100, 1400, 900)
    
    # إنشاء وحدة العملاء
    customers_module = CustomersSupplierModule(db_manager)
    window.setCentralWidget(customers_module)
    
    # عرض النافذة
    window.show()
    
    print("تم فتح وحدة العملاء والموردين")
    print("ابحث عن الأزرار الجديدة:")
    print("🧾 فاتورة مبيعات")
    print("↩️ مردود مبيعات")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
