#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أزرار الفواتير في وحدة العملاء والموردين
Test Invoice Buttons in Customers and Suppliers Module
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_customers_module_import():
    """اختبار استيراد وحدة العملاء والموردين"""
    print("🧪 اختبار استيراد وحدة العملاء والموردين...")
    
    try:
        from src.modules.customers_suppliers_module import CustomersSupplierModule
        from src.database.sqlite_manager import SQLiteManager
        print("✅ تم استيراد وحدة العملاء والموردين بنجاح")
        return True, CustomersSupplierModule, SQLiteManager
    except ImportError as e:
        print(f"❌ فشل في استيراد وحدة العملاء والموردين: {e}")
        return False, None, None

def test_invoice_buttons_exist():
    """اختبار وجود أزرار الفواتير"""
    print("\n🧪 اختبار وجود أزرار الفواتير...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.modules.customers_suppliers_module import CustomersSupplierModule
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء تطبيق مؤقت
        app = QApplication([])
        db_manager = SQLiteManager()
        
        # إنشاء وحدة العملاء والموردين
        customers_module = CustomersSupplierModule(db_manager)
        
        # التحقق من وجود الأزرار
        buttons_to_check = [
            ('sales_invoice_btn', 'زر فاتورة المبيعات'),
            ('sales_return_btn', 'زر مردود المبيعات'),
            ('receipt_voucher_btn', 'زر سند القبض'),
            ('payment_voucher_btn', 'زر سند الصرف')
        ]
        
        all_buttons_exist = True
        for button_attr, button_name in buttons_to_check:
            if hasattr(customers_module, button_attr):
                button = getattr(customers_module, button_attr)
                print(f"✅ {button_name}: موجود - النص: '{button.text()}'")
            else:
                print(f"❌ {button_name}: غير موجود")
                all_buttons_exist = False
        
        return all_buttons_exist
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأزرار: {e}")
        return False

def test_invoice_functions_exist():
    """اختبار وجود دوال الفواتير"""
    print("\n🧪 اختبار وجود دوال الفواتير...")
    
    try:
        from src.modules.customers_suppliers_module import CustomersSupplierModule
        
        # التحقق من وجود الدوال
        functions_to_check = [
            ('open_sales_invoice', 'دالة فتح فاتورة المبيعات'),
            ('open_sales_return', 'دالة فتح مردود المبيعات'),
            ('get_customer_id_by_code', 'دالة الحصول على ID العميل'),
            ('open_receipt_voucher', 'دالة فتح سند القبض'),
            ('open_payment_voucher', 'دالة فتح سند الصرف')
        ]
        
        all_functions_exist = True
        for func_attr, func_name in functions_to_check:
            if hasattr(CustomersSupplierModule, func_attr):
                print(f"✅ {func_name}: موجودة")
            else:
                print(f"❌ {func_name}: غير موجودة")
                all_functions_exist = False
        
        return all_functions_exist
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الدوال: {e}")
        return False

def test_customer_id_function():
    """اختبار دالة الحصول على ID العميل"""
    print("\n🧪 اختبار دالة الحصول على ID العميل...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.modules.customers_suppliers_module import CustomersSupplierModule
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء تطبيق مؤقت
        app = QApplication([])
        db_manager = SQLiteManager()
        
        # إنشاء وحدة العملاء والموردين
        customers_module = CustomersSupplierModule(db_manager)
        
        # اختبار الدالة مع كود غير موجود
        customer_id = customers_module.get_customer_id_by_code("TEST_CODE")
        
        if customer_id is None:
            print("✅ دالة الحصول على ID العميل تعمل بشكل صحيح (ترجع None للكود غير الموجود)")
            return True
        else:
            print(f"⚠️ دالة الحصول على ID العميل ترجع: {customer_id}")
            return True  # قد يكون هناك عميل بهذا الكود
        
    except Exception as e:
        print(f"❌ خطأ في اختبار دالة ID العميل: {e}")
        return False

def test_database_customers():
    """اختبار وجود عملاء في قاعدة البيانات"""
    print("\n🧪 اختبار وجود عملاء في قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager()
        
        # البحث عن العملاء
        query = "SELECT code, name_ar, type FROM customers_suppliers WHERE type IN ('عميل', 'عميل ومورد') LIMIT 5"
        customers = db_manager.execute_query(query)
        
        if customers:
            print(f"✅ تم العثور على {len(customers)} عميل:")
            for customer in customers:
                print(f"   📋 {customer['code']} - {customer['name_ar']} ({customer['type']})")
            return True
        else:
            print("⚠️ لا توجد عملاء في قاعدة البيانات")
            return True  # ليس خطأ، فقط لا توجد بيانات
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 70)
    print("🧪 اختبار أزرار الفواتير في وحدة العملاء والموردين")
    print("=" * 70)
    
    tests = [
        ("استيراد الوحدة", test_customers_module_import),
        ("وجود الأزرار", test_invoice_buttons_exist),
        ("وجود الدوال", test_invoice_functions_exist),
        ("دالة ID العميل", test_customer_id_function),
        ("العملاء في قاعدة البيانات", test_database_customers),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if isinstance(result, tuple):
                # للاختبار الأول الذي يرجع tuple
                if result[0]:
                    passed_tests += 1
            elif result:
                passed_tests += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 نتائج الاختبار: {passed_tests}/{total_tests} اختبار نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        
        print("\n✨ الميزات المضافة:")
        print("   🧾 زر فاتورة المبيعات - لإنشاء فاتورة مبيعات للعميل المحدد")
        print("   ↩️ زر مردود المبيعات - لإنشاء فاتورة مردود للعميل المحدد")
        print("   🎯 تفعيل/تعطيل الأزرار حسب نوع المحدد (عميل/مورد)")
        print("   🔗 ربط مع نظام الفواتير المحسن")
        
        print("\n🚀 كيفية الاستخدام:")
        print("   1. شغل النظام: python src/main_system.py")
        print("   2. اذهب لوحدة 'العملاء والموردين'")
        print("   3. حدد عميل من القائمة")
        print("   4. اضغط 'فاتورة مبيعات' أو 'مردود مبيعات'")
        
    else:
        print("⚠️ بعض الاختبارات فشلت - يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
