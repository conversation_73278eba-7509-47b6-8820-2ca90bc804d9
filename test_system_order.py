#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ترتيب أقسام النظام المحاسبي
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_system_order():
    """اختبار ترتيب أقسام النظام"""
    print("📋 اختبار ترتيب أقسام النظام المحاسبي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system_restructured import MainSystemWindow
        
        main_window = MainSystemWindow()
        tree_data = main_window.get_restructured_tree_data()
        
        # الترتيب المطلوب
        expected_order = [
            "إعدادات النظام",
            "إدارة المستخدمين",
            "إدارة المخازن", 
            "إدارة المبيعات",
            "إدارة المشتريات",
            "إدارة الحسابات",  # المحاسبة العامة
            "إدارة العملاء"
        ]
        
        print(f"\n📊 عدد الأقسام: {len(tree_data)}")
        print("🔢 الترتيب الحالي:")
        
        actual_order = []
        for i, section in enumerate(tree_data, 1):
            section_title = section['title']
            actual_order.append(section_title)
            print(f"   {i}. {section_title}")
        
        print(f"\n🎯 الترتيب المطلوب:")
        for i, expected in enumerate(expected_order, 1):
            print(f"   {i}. {expected}")
        
        # التحقق من الترتيب
        print(f"\n✅ التحقق من الترتيب:")
        
        order_correct = True
        for i, expected in enumerate(expected_order):
            if i < len(actual_order):
                actual = actual_order[i]
                if expected in actual:
                    print(f"   ✅ {i+1}. {expected} - موجود في الموضع الصحيح")
                else:
                    print(f"   ❌ {i+1}. {expected} - غير موجود في الموضع الصحيح")
                    print(f"       الموجود: {actual}")
                    order_correct = False
            else:
                print(f"   ❌ {i+1}. {expected} - مفقود")
                order_correct = False
        
        return order_correct
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار ترتيب النظام: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_section_details():
    """اختبار تفاصيل الأقسام"""
    print("\n🔍 اختبار تفاصيل الأقسام...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system_restructured import MainSystemWindow
        
        main_window = MainSystemWindow()
        tree_data = main_window.get_restructured_tree_data()
        
        for section in tree_data:
            section_title = section['title']
            print(f"\n📁 {section_title}:")
            
            if isinstance(section['children'], list):
                for child in section['children']:
                    if isinstance(child, dict):
                        # قسم فرعي مع تقسيم ثلاثي
                        print(f"   📂 {child['title']}:")
                        for item in child['children']:
                            print(f"      📄 {item}")
                    else:
                        # عنصر مباشر
                        print(f"   📄 {child}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار تفاصيل الأقسام: {e}")
        return False

def test_sales_invoice_location():
    """اختبار موقع فواتير البيع"""
    print("\n🛍️ اختبار موقع فواتير البيع...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system_restructured import MainSystemWindow
        
        main_window = MainSystemWindow()
        tree_data = main_window.get_restructured_tree_data()
        
        # البحث عن قسم إدارة المبيعات
        sales_section = None
        for section in tree_data:
            if 'إدارة المبيعات' in section['title']:
                sales_section = section
                break
        
        if not sales_section:
            print("   ❌ لم يتم العثور على قسم إدارة المبيعات")
            return False
        
        # البحث عن فواتير البيع في العمليات
        invoice_in_operations = False
        invoice_in_inputs = False
        
        for subsection in sales_section['children']:
            if isinstance(subsection, dict):
                if 'العمليات' in subsection['title']:
                    for item in subsection['children']:
                        if 'فواتير البيع' in item or 'إنشاء فواتير البيع' in item:
                            invoice_in_operations = True
                            print(f"   ✅ فواتير البيع موجودة في العمليات: {item}")
                
                elif 'المدخلات' in subsection['title']:
                    for item in subsection['children']:
                        if 'فواتير البيع' in item:
                            invoice_in_inputs = True
                            print(f"   ⚠️ فواتير البيع موجودة في المدخلات: {item}")
        
        if invoice_in_operations and not invoice_in_inputs:
            print("   ✅ فواتير البيع في الموضع الصحيح (العمليات)")
            return True
        else:
            print("   ❌ فواتير البيع ليست في الموضع الصحيح")
            return False
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار موقع فواتير البيع: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🏗️ اختبار ترتيب النظام المحاسبي المحدث")
    print("=" * 70)
    
    tests = [
        ("ترتيب الأقسام", test_system_order),
        ("تفاصيل الأقسام", test_section_details),
        ("موقع فواتير البيع", test_sales_invoice_location),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم ترتيب النظام بنجاح!")
        print("✅ جميع الأقسام في الترتيب الصحيح")
        
        print("\n📋 الترتيب النهائي:")
        print("   1️⃣ إعدادات النظام")
        print("   2️⃣ إدارة المستخدمين والصلاحيات")
        print("   3️⃣ إدارة المخازن")
        print("   4️⃣ إدارة المبيعات")
        print("   5️⃣ إدارة المشتريات")
        print("   6️⃣ إدارة الحسابات (المحاسبة العامة)")
        print("   7️⃣ إدارة العملاء")
        
        print("\n🎯 التحسينات:")
        print("   ✅ فواتير البيع في قسم العمليات")
        print("   ✅ ترتيب منطقي للأقسام")
        print("   ✅ تقسيم ثلاثي واضح")
        
        print("\n🚀 للاستخدام:")
        print("   python src/main_system_restructured.py")
        
    else:
        print("\n⚠️ بعض جوانب الترتيب تحتاج إلى تعديل")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
