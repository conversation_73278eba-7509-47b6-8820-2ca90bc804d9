#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف إضافية لشاشة بيانات المحافظات / المناطق / المدن
Additional Functions for Administrative Divisions
"""

from PyQt5.QtWidgets import QMessageBox, QTableWidgetItem

def add_district_function(self):
    """إضافة منطقة جديدة"""
    try:
        if not self.dist_name.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المنطقة")
            return
            
        # إضافة المنطقة للجدول
        row = self.districts_table.rowCount()
        self.districts_table.insertRow(row)
        
        self.districts_table.setItem(row, 0, QTableWidgetItem(self.dist_name.text()))
        self.districts_table.setItem(row, 1, QTableWidgetItem(self.dist_name_en.text()))
        self.districts_table.setItem(row, 2, QTableWidgetItem(self.dist_code.text().upper()))
        self.districts_table.setItem(row, 3, QTableWidgetItem(self.dist_governorate.currentText()))
        self.districts_table.setItem(row, 4, QTableWidgetItem(self.dist_type.currentText()))
        
        status = "نشطة" if self.dist_active.isChecked() else "غير نشطة"
        self.districts_table.setItem(row, 5, QTableWidgetItem(status))
        
        # تنظيف النموذج
        self.clear_district_form()
        
        # تحديث القوائم والشجرة
        self.update_district_combos()
        self.build_admin_tree()
        
        QMessageBox.information(self, "نجح", "تم إضافة المنطقة بنجاح")
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إضافة المنطقة:\n{str(e)}")

def edit_district_function(self):
    """تعديل منطقة محددة"""
    current_row = self.districts_table.currentRow()
    if current_row < 0:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار منطقة للتعديل")
        return
        
    # ملء النموذج ببيانات المنطقة المحددة
    self.dist_name.setText(self.districts_table.item(current_row, 0).text())
    self.dist_name_en.setText(self.districts_table.item(current_row, 1).text())
    self.dist_code.setText(self.districts_table.item(current_row, 2).text())
    self.dist_governorate.setCurrentText(self.districts_table.item(current_row, 3).text())
    self.dist_type.setCurrentText(self.districts_table.item(current_row, 4).text())
    
    status = self.districts_table.item(current_row, 5).text()
    self.dist_active.setChecked(status == "نشطة")

def delete_district_function(self):
    """حذف منطقة محددة"""
    current_row = self.districts_table.currentRow()
    if current_row < 0:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار منطقة للحذف")
        return
        
    dist_name = self.districts_table.item(current_row, 0).text()
    
    # التحقق من وجود مدن تابعة
    city_count = 0
    for row in range(self.cities_table.rowCount()):
        if self.cities_table.item(row, 3).text() == dist_name:
            city_count += 1
            
    if city_count > 0:
        QMessageBox.warning(self, "تحذير", 
                           f"لا يمكن حذف المنطقة '{dist_name}'\n"
                           f"تحتوي على {city_count} مدينة\n"
                           f"يرجى حذف المدن أولاً أو نقلها لمنطقة أخرى")
        return
        
    reply = QMessageBox.question(self, "تأكيد الحذف", 
                               f"هل تريد حذف المنطقة '{dist_name}'؟",
                               QMessageBox.Yes | QMessageBox.No)
    
    if reply == QMessageBox.Yes:
        self.districts_table.removeRow(current_row)
        self.update_district_combos()
        self.build_admin_tree()
        QMessageBox.information(self, "تم", "تم حذف المنطقة بنجاح")

def clear_district_form_function(self):
    """تنظيف نموذج المنطقة"""
    self.dist_name.clear()
    self.dist_name_en.clear()
    self.dist_code.clear()
    self.dist_active.setChecked(True)

def add_city_function(self):
    """إضافة مدينة جديدة"""
    try:
        if not self.city_name.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المدينة")
            return
            
        # إضافة المدينة للجدول
        row = self.cities_table.rowCount()
        self.cities_table.insertRow(row)
        
        self.cities_table.setItem(row, 0, QTableWidgetItem(self.city_name.text()))
        self.cities_table.setItem(row, 1, QTableWidgetItem(self.city_name_en.text()))
        self.cities_table.setItem(row, 2, QTableWidgetItem(self.city_code.text().upper()))
        self.cities_table.setItem(row, 3, QTableWidgetItem(self.city_district.currentText()))
        self.cities_table.setItem(row, 4, QTableWidgetItem(self.city_type.currentText()))
        self.cities_table.setItem(row, 5, QTableWidgetItem(f"{self.city_population.value():,}"))
        
        status = "نشطة"
        if self.city_main.isChecked():
            status += " (رئيسية)"
        elif not self.city_active.isChecked():
            status = "غير نشطة"
        self.cities_table.setItem(row, 6, QTableWidgetItem(status))
        
        # تنظيف النموذج
        self.clear_city_form()
        
        # تحديث القوائم والشجرة
        self.update_city_combos()
        self.build_admin_tree()
        
        QMessageBox.information(self, "نجح", "تم إضافة المدينة بنجاح")
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إضافة المدينة:\n{str(e)}")

def edit_city_function(self):
    """تعديل مدينة محددة"""
    current_row = self.cities_table.currentRow()
    if current_row < 0:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار مدينة للتعديل")
        return
        
    # ملء النموذج ببيانات المدينة المحددة
    self.city_name.setText(self.cities_table.item(current_row, 0).text())
    self.city_name_en.setText(self.cities_table.item(current_row, 1).text())
    self.city_code.setText(self.cities_table.item(current_row, 2).text())
    self.city_district.setCurrentText(self.cities_table.item(current_row, 3).text())
    self.city_type.setCurrentText(self.cities_table.item(current_row, 4).text())
    
    population_text = self.cities_table.item(current_row, 5).text().replace(',', '')
    self.city_population.setValue(int(population_text))
    
    status = self.cities_table.item(current_row, 6).text()
    self.city_active.setChecked("نشطة" in status)
    self.city_main.setChecked("رئيسية" in status)

def delete_city_function(self):
    """حذف مدينة محددة"""
    current_row = self.cities_table.currentRow()
    if current_row < 0:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار مدينة للحذف")
        return
        
    city_name = self.cities_table.item(current_row, 0).text()
    
    # التحقق من وجود قرى تابعة
    village_count = 0
    for row in range(self.villages_table.rowCount()):
        if self.villages_table.item(row, 3).text() == city_name:
            village_count += 1
            
    if village_count > 0:
        QMessageBox.warning(self, "تحذير", 
                           f"لا يمكن حذف المدينة '{city_name}'\n"
                           f"تحتوي على {village_count} قرية\n"
                           f"يرجى حذف القرى أولاً أو نقلها لمدينة أخرى")
        return
        
    reply = QMessageBox.question(self, "تأكيد الحذف", 
                               f"هل تريد حذف المدينة '{city_name}'؟",
                               QMessageBox.Yes | QMessageBox.No)
    
    if reply == QMessageBox.Yes:
        self.cities_table.removeRow(current_row)
        self.update_city_combos()
        self.build_admin_tree()
        QMessageBox.information(self, "تم", "تم حذف المدينة بنجاح")

def clear_city_form_function(self):
    """تنظيف نموذج المدينة"""
    self.city_name.clear()
    self.city_name_en.clear()
    self.city_code.clear()
    self.city_population.setValue(0)
    self.city_active.setChecked(True)
    self.city_main.setChecked(False)

def add_village_function(self):
    """إضافة قرية جديدة"""
    try:
        if not self.village_name.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم القرية")
            return
            
        # إضافة القرية للجدول
        row = self.villages_table.rowCount()
        self.villages_table.insertRow(row)
        
        self.villages_table.setItem(row, 0, QTableWidgetItem(self.village_name.text()))
        self.villages_table.setItem(row, 1, QTableWidgetItem(self.village_name_en.text()))
        self.villages_table.setItem(row, 2, QTableWidgetItem(self.village_code.text().upper()))
        self.villages_table.setItem(row, 3, QTableWidgetItem(self.village_city.currentText()))
        self.villages_table.setItem(row, 4, QTableWidgetItem(self.village_type.currentText()))
        
        status = "نشطة" if self.village_active.isChecked() else "غير نشطة"
        self.villages_table.setItem(row, 5, QTableWidgetItem(status))
        
        # تنظيف النموذج
        self.clear_village_form()
        
        # تحديث الشجرة
        self.build_admin_tree()
        
        QMessageBox.information(self, "نجح", "تم إضافة القرية بنجاح")
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إضافة القرية:\n{str(e)}")

def edit_village_function(self):
    """تعديل قرية محددة"""
    current_row = self.villages_table.currentRow()
    if current_row < 0:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار قرية للتعديل")
        return
        
    # ملء النموذج ببيانات القرية المحددة
    self.village_name.setText(self.villages_table.item(current_row, 0).text())
    self.village_name_en.setText(self.villages_table.item(current_row, 1).text())
    self.village_code.setText(self.villages_table.item(current_row, 2).text())
    self.village_city.setCurrentText(self.villages_table.item(current_row, 3).text())
    self.village_type.setCurrentText(self.villages_table.item(current_row, 4).text())
    
    status = self.villages_table.item(current_row, 5).text()
    self.village_active.setChecked(status == "نشطة")

def delete_village_function(self):
    """حذف قرية محددة"""
    current_row = self.villages_table.currentRow()
    if current_row < 0:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار قرية للحذف")
        return
        
    village_name = self.villages_table.item(current_row, 0).text()
    
    reply = QMessageBox.question(self, "تأكيد الحذف", 
                               f"هل تريد حذف القرية '{village_name}'؟",
                               QMessageBox.Yes | QMessageBox.No)
    
    if reply == QMessageBox.Yes:
        self.villages_table.removeRow(current_row)
        self.build_admin_tree()
        QMessageBox.information(self, "تم", "تم حذف القرية بنجاح")

def clear_village_form_function(self):
    """تنظيف نموذج القرية"""
    self.village_name.clear()
    self.village_name_en.clear()
    self.village_code.clear()
    self.village_active.setChecked(True)

def save_all_changes_function(self):
    """حفظ جميع التغييرات"""
    try:
        # جمع جميع البيانات
        data = {
            'governorates': [],
            'districts': [],
            'cities': [],
            'villages': []
        }
        
        # جمع بيانات المحافظات
        for row in range(self.governorates_table.rowCount()):
            gov = {
                'name': self.governorates_table.item(row, 0).text(),
                'name_en': self.governorates_table.item(row, 1).text(),
                'code': self.governorates_table.item(row, 2).text(),
                'country': self.governorates_table.item(row, 3).text(),
                'capital': self.governorates_table.item(row, 4).text(),
                'area': self.governorates_table.item(row, 5).text(),
                'status': self.governorates_table.item(row, 6).text()
            }
            data['governorates'].append(gov)
        
        # جمع بيانات المناطق
        for row in range(self.districts_table.rowCount()):
            dist = {
                'name': self.districts_table.item(row, 0).text(),
                'name_en': self.districts_table.item(row, 1).text(),
                'code': self.districts_table.item(row, 2).text(),
                'governorate': self.districts_table.item(row, 3).text(),
                'type': self.districts_table.item(row, 4).text(),
                'status': self.districts_table.item(row, 5).text()
            }
            data['districts'].append(dist)
        
        # جمع بيانات المدن
        for row in range(self.cities_table.rowCount()):
            city = {
                'name': self.cities_table.item(row, 0).text(),
                'name_en': self.cities_table.item(row, 1).text(),
                'code': self.cities_table.item(row, 2).text(),
                'district': self.cities_table.item(row, 3).text(),
                'type': self.cities_table.item(row, 4).text(),
                'population': self.cities_table.item(row, 5).text(),
                'status': self.cities_table.item(row, 6).text()
            }
            data['cities'].append(city)
        
        # جمع بيانات القرى
        for row in range(self.villages_table.rowCount()):
            village = {
                'name': self.villages_table.item(row, 0).text(),
                'name_en': self.villages_table.item(row, 1).text(),
                'code': self.villages_table.item(row, 2).text(),
                'city': self.villages_table.item(row, 3).text(),
                'type': self.villages_table.item(row, 4).text(),
                'status': self.villages_table.item(row, 5).text()
            }
            data['villages'].append(village)
        
        # حفظ في قاعدة البيانات
        # هنا يتم حفظ البيانات في قاعدة البيانات
        
        summary = f"""✅ تم حفظ الهيكل الإداري الجغرافي بنجاح!

🏛️ المحافظات: {len(data['governorates'])}
🏘️ المناطق: {len(data['districts'])}
🏙️ المدن: {len(data['cities'])}
🏡 القرى: {len(data['villages'])}

📊 إجمالي التقسيمات الإدارية: {len(data['governorates']) + len(data['districts']) + len(data['cities']) + len(data['villages'])}"""
        
        QMessageBox.information(self, "نجح الحفظ", summary)
        self.accept()
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في حفظ البيانات:\n{str(e)}")
