#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة التقارير
Reports Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel, 
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QDialogButtonBox, QHeaderView, QFrame, QGroupBox,
                             QGridLayout, QTextEdit, QDateEdit, QTabWidget,
                             QSplitter, QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QIcon
from decimal import Decimal
def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class ReportsWidget(QWidget):
    """ويدجت التقارير"""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # العنوان
        title = QLabel("📊 التقارير المالية")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        layout.addWidget(title)
        
        # إنشاء التبويبات
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 20px;
                margin: 2px;
                border-radius: 5px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #d5dbdb;
            }
        """)
        
        # إضافة التبويبات
        self.create_trial_balance_tab()
        self.create_account_statement_tab()
        self.create_journal_report_tab()
        self.create_financial_reports_tab()
        
        layout.addWidget(self.tabs)
        self.setLayout(layout)
    
    def create_trial_balance_tab(self):
        """إنشاء تبويب ميزان المراجعة"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar = self.create_report_toolbar("trial_balance")
        layout.addWidget(toolbar)
        
        # فلاتر التقرير
        filters = self.create_trial_balance_filters()
        layout.addWidget(filters)
        
        # جدول ميزان المراجعة
        self.trial_balance_table = self.create_trial_balance_table()
        layout.addWidget(self.trial_balance_table)
        
        # شريط الإجماليات
        totals_frame = self.create_totals_frame()
        layout.addWidget(totals_frame)
        
        tab.setLayout(layout)
        self.tabs.addTab(tab, "ميزان المراجعة")
    
    def create_account_statement_tab(self):
        """إنشاء تبويب كشف حساب"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar = self.create_report_toolbar("account_statement")
        layout.addWidget(toolbar)
        
        # فلاتر التقرير
        filters = self.create_account_statement_filters()
        layout.addWidget(filters)
        
        # جدول كشف الحساب
        self.account_statement_table = self.create_account_statement_table()
        layout.addWidget(self.account_statement_table)
        
        tab.setLayout(layout)
        self.tabs.addTab(tab, "كشف حساب")
    
    def create_journal_report_tab(self):
        """إنشاء تبويب تقرير القيود"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar = self.create_report_toolbar("journal_report")
        layout.addWidget(toolbar)
        
        # فلاتر التقرير
        filters = self.create_journal_report_filters()
        layout.addWidget(filters)
        
        # جدول تقرير القيود
        self.journal_report_table = self.create_journal_report_table()
        layout.addWidget(self.journal_report_table)
        
        tab.setLayout(layout)
        self.tabs.addTab(tab, "تقرير القيود")
    
    def create_financial_reports_tab(self):
        """إنشاء تبويب التقارير المالية"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # شريط الأدوات
        toolbar = self.create_report_toolbar("financial_reports")
        layout.addWidget(toolbar)
        
        # قائمة التقارير المالية
        reports_list = self.create_financial_reports_list()
        layout.addWidget(reports_list)
        
        tab.setLayout(layout)
        self.tabs.addTab(tab, "التقارير المالية")
    
    def create_report_toolbar(self, report_type):
        """إنشاء شريط أدوات التقرير"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        
        toolbar_layout = QHBoxLayout()
        
        # أزرار الأدوات
        generate_btn = QPushButton("📊 إنشاء التقرير")
        export_btn = QPushButton("📤 تصدير")
        print_btn = QPushButton("🖨️ طباعة")
        refresh_btn = QPushButton("🔄 تحديث")
        
        buttons = [generate_btn, export_btn, print_btn, refresh_btn]
        
        for btn in buttons:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    font-weight: bold;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QPushButton:pressed {
                    background-color: #21618c;
                }
            """)
            toolbar_layout.addWidget(btn)
        
        # ربط الإشارات
        generate_btn.clicked.connect(lambda: self.generate_report(report_type))
        export_btn.clicked.connect(lambda: self.export_report(report_type))
        print_btn.clicked.connect(lambda: self.print_report(report_type))
        refresh_btn.clicked.connect(lambda: self.refresh_report(report_type))
        
        toolbar_layout.addStretch()
        toolbar_frame.setLayout(toolbar_layout)
        
        return toolbar_frame
    
    def create_trial_balance_filters(self):
        """إنشاء فلاتر ميزان المراجعة"""
        filters_frame = QGroupBox("🔍 فلاتر التقرير")
        filters_frame.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 5px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        filters_layout = QGridLayout()
        
        # تاريخ من
        filters_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.tb_date_from = QDateEdit()
        self.tb_date_from.setDate(QDate.currentDate().addDays(-365))
        self.tb_date_from.setCalendarPopup(True)
        filters_layout.addWidget(self.tb_date_from, 0, 1)
        
        # تاريخ إلى
        filters_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.tb_date_to = QDateEdit()
        self.tb_date_to.setDate(QDate.currentDate())
        self.tb_date_to.setCalendarPopup(True)
        filters_layout.addWidget(self.tb_date_to, 0, 3)
        
        # نوع الحسابات
        filters_layout.addWidget(QLabel("نوع الحسابات:"), 1, 0)
        self.tb_account_type = QComboBox()
        self.tb_account_type.addItems(["الكل", "أصول", "خصوم", "حقوق ملكية", "إيرادات", "مصروفات"])
        filters_layout.addWidget(self.tb_account_type, 1, 1)
        
        # إظهار الأرصدة الصفرية
        self.tb_show_zero = QComboBox()
        self.tb_show_zero.addItems(["إخفاء الأرصدة الصفرية", "إظهار الأرصدة الصفرية"])
        filters_layout.addWidget(self.tb_show_zero, 1, 2, 1, 2)
        
        filters_frame.setLayout(filters_layout)
        return filters_frame
    
    def create_account_statement_filters(self):
        """إنشاء فلاتر كشف الحساب"""
        filters_frame = QGroupBox("🔍 فلاتر التقرير")
        filters_frame.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 5px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        filters_layout = QGridLayout()
        
        # رقم الحساب
        filters_layout.addWidget(QLabel("رقم الحساب:"), 0, 0)
        self.as_account_code = QComboBox()
        self.as_account_code.setEditable(True)
        self.load_accounts_to_combo(self.as_account_code)
        filters_layout.addWidget(self.as_account_code, 0, 1)
        
        # تاريخ من
        filters_layout.addWidget(QLabel("من تاريخ:"), 0, 2)
        self.as_date_from = QDateEdit()
        self.as_date_from.setDate(QDate.currentDate().addDays(-365))
        self.as_date_from.setCalendarPopup(True)
        filters_layout.addWidget(self.as_date_from, 0, 3)
        
        # تاريخ إلى
        filters_layout.addWidget(QLabel("إلى تاريخ:"), 1, 0)
        self.as_date_to = QDateEdit()
        self.as_date_to.setDate(QDate.currentDate())
        self.as_date_to.setCalendarPopup(True)
        filters_layout.addWidget(self.as_date_to, 1, 1)
        
        filters_frame.setLayout(filters_layout)
        return filters_frame
    
    def create_journal_report_filters(self):
        """إنشاء فلاتر تقرير القيود"""
        filters_frame = QGroupBox("🔍 فلاتر التقرير")
        filters_frame.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 5px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        filters_layout = QGridLayout()
        
        # تاريخ من
        filters_layout.addWidget(QLabel("من تاريخ:"), 0, 0)
        self.jr_date_from = QDateEdit()
        self.jr_date_from.setDate(QDate.currentDate().addDays(-30))
        self.jr_date_from.setCalendarPopup(True)
        filters_layout.addWidget(self.jr_date_from, 0, 1)
        
        # تاريخ إلى
        filters_layout.addWidget(QLabel("إلى تاريخ:"), 0, 2)
        self.jr_date_to = QDateEdit()
        self.jr_date_to.setDate(QDate.currentDate())
        self.jr_date_to.setCalendarPopup(True)
        filters_layout.addWidget(self.jr_date_to, 0, 3)
        
        # حالة القيود
        filters_layout.addWidget(QLabel("حالة القيود:"), 1, 0)
        self.jr_status = QComboBox()
        self.jr_status.addItems(["الكل", "مرحل", "غير مرحل"])
        filters_layout.addWidget(self.jr_status, 1, 1)
        
        filters_frame.setLayout(filters_layout)
        return filters_frame
    
    def create_trial_balance_table(self):
        """إنشاء جدول ميزان المراجعة"""
        table = QTableWidget()
        
        # تعيين الأعمدة
        headers = ["رقم الحساب", "اسم الحساب", "رصيد سابق مدين", "رصيد سابق دائن", 
                  "حركة مدين", "حركة دائن", "رصيد ختامي مدين", "رصيد ختامي دائن"]
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        
        # تنسيق الجدول
        self.style_report_table(table)
        
        # تعديل عرض الأعمدة
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # رقم الحساب
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم الحساب
        for i in range(2, 8):  # الأعمدة المالية
            header.setSectionResizeMode(i, QHeaderView.Fixed)
            table.setColumnWidth(i, 120)
        
        table.setColumnWidth(0, 120)  # رقم الحساب
        
        return table
    
    def create_account_statement_table(self):
        """إنشاء جدول كشف الحساب"""
        table = QTableWidget()
        
        # تعيين الأعمدة
        headers = ["التاريخ", "رقم القيد", "البيان", "مدين", "دائن", "الرصيد"]
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        
        # تنسيق الجدول
        self.style_report_table(table)
        
        # تعديل عرض الأعمدة
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # التاريخ
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # رقم القيد
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # البيان
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # مدين
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # دائن
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # الرصيد
        
        table.setColumnWidth(0, 120)  # التاريخ
        table.setColumnWidth(1, 100)  # رقم القيد
        table.setColumnWidth(3, 120)  # مدين
        table.setColumnWidth(4, 120)  # دائن
        table.setColumnWidth(5, 120)  # الرصيد
        
        return table
    
    def create_journal_report_table(self):
        """إنشاء جدول تقرير القيود"""
        table = QTableWidget()
        
        # تعيين الأعمدة
        headers = ["رقم القيد", "التاريخ", "البيان", "رقم الحساب", "اسم الحساب", "مدين", "دائن", "الحالة"]
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        
        # تنسيق الجدول
        self.style_report_table(table)
        
        # تعديل عرض الأعمدة
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # رقم القيد
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # البيان
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # رقم الحساب
        header.setSectionResizeMode(4, QHeaderView.Stretch)  # اسم الحساب
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # مدين
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # دائن
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # الحالة
        
        table.setColumnWidth(0, 100)  # رقم القيد
        table.setColumnWidth(1, 120)  # التاريخ
        table.setColumnWidth(3, 120)  # رقم الحساب
        table.setColumnWidth(5, 120)  # مدين
        table.setColumnWidth(6, 120)  # دائن
        table.setColumnWidth(7, 100)  # الحالة
        
        return table
    
    def create_financial_reports_list(self):
        """إنشاء قائمة التقارير المالية"""
        reports_frame = QFrame()
        layout = QVBoxLayout()
        
        # قائمة التقارير
        reports_list = [
            ("📊 الميزانية العمومية", "balance_sheet"),
            ("📈 قائمة الدخل", "income_statement"),
            ("💰 قائمة التدفقات النقدية", "cash_flow"),
            ("📋 قائمة التغيرات في حقوق الملكية", "equity_changes"),
            ("📊 تحليل الأداء المالي", "financial_analysis"),
            ("📈 مؤشرات الأداء الرئيسية", "kpi_dashboard")
        ]
        
        for report_name, report_code in reports_list:
            btn = QPushButton(report_name)
            btn.setFixedHeight(50)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                    text-align: left;
                    padding: 10px 20px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QPushButton:pressed {
                    background-color: #21618c;
                }
            """)
            btn.clicked.connect(lambda checked, code=report_code: self.generate_financial_report(code))
            layout.addWidget(btn)
        
        layout.addStretch()
        reports_frame.setLayout(layout)
        
        return reports_frame
    
    def create_totals_frame(self):
        """إنشاء إطار الإجماليات"""
        totals_frame = QFrame()
        totals_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        totals_layout = QHBoxLayout()
        
        self.total_debit_label = QLabel("إجمالي المدين: 0.00")
        self.total_credit_label = QLabel("إجمالي الدائن: 0.00")
        self.balance_label = QLabel("الفرق: 0.00")
        
        for label in [self.total_debit_label, self.total_credit_label, self.balance_label]:
            label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        
        totals_layout.addWidget(self.total_debit_label)
        totals_layout.addWidget(self.total_credit_label)
        totals_layout.addStretch()
        totals_layout.addWidget(self.balance_label)
        
        totals_frame.setLayout(totals_layout)
        return totals_frame
    
    def style_report_table(self, table):
        """تنسيق جدول التقرير"""
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSortingEnabled(True)
    
    def load_accounts_to_combo(self, combo):
        """تحميل الحسابات إلى ComboBox"""
        try:
            query = "SELECT account_code, account_name_ar FROM chart_of_accounts WHERE is_active = 1 ORDER BY account_code"
            accounts = self.db_manager.execute_query(query)
            
            combo.addItem("اختر الحساب...", "")
            for account in accounts:
                display_text = f"{account['account_code']} - {account['account_name_ar']}"
                combo.addItem(display_text, account['account_code'])
                
        except Exception as e:
            print(f"خطأ في تحميل الحسابات: {e}")
    
    def generate_report(self, report_type):
        """إنشاء التقرير"""
        if report_type == "trial_balance":
            self.generate_trial_balance()
        elif report_type == "account_statement":
            self.generate_account_statement()
        elif report_type == "journal_report":
            self.generate_journal_report()
        else:
            QMessageBox.information(self, "تقرير", f"تقرير {report_type} قيد التطوير")
    
    def generate_trial_balance(self):
        """إنشاء ميزان المراجعة"""
        try:
            date_from = self.tb_date_from.date().toString("yyyy-MM-dd")
            date_to = self.tb_date_to.date().toString("yyyy-MM-dd")
            account_type = self.tb_account_type.currentText()
            show_zero = self.tb_show_zero.currentIndex() == 1
            
            # استعلام ميزان المراجعة
            query = """
                SELECT 
                    ca.account_code,
                    ca.account_name_ar,
                    ca.account_type,
                    COALESCE(SUM(CASE WHEN jed.debit_amount > 0 THEN jed.debit_amount ELSE 0 END), 0) as total_debit,
                    COALESCE(SUM(CASE WHEN jed.credit_amount > 0 THEN jed.credit_amount ELSE 0 END), 0) as total_credit
                FROM chart_of_accounts ca
                LEFT JOIN journal_entry_details jed ON ca.account_code = jed.account_code
                LEFT JOIN journal_entries je ON jed.journal_entry_id = je.id
                WHERE ca.is_active = 1 
                    AND (je.entry_date IS NULL OR (je.entry_date >= ? AND je.entry_date <= ?))
                    AND (je.is_posted = 1 OR je.is_posted IS NULL)
            """
            
            params = [date_from, date_to]
            
            # فلترة نوع الحسابات
            if account_type != "الكل":
                type_map = {
                    "أصول": "asset",
                    "خصوم": "liability", 
                    "حقوق ملكية": "equity",
                    "إيرادات": "revenue",
                    "مصروفات": "expense"
                }
                if account_type in type_map:
                    query += " AND ca.account_type = ?"
                    params.append(type_map[account_type])
            
            query += " GROUP BY ca.account_code, ca.account_name_ar, ca.account_type ORDER BY ca.account_code"
            
            results = self.db_manager.execute_query(query, params)
            
            # مسح الجدول
            self.trial_balance_table.setRowCount(0)
            
            total_debit = Decimal('0')
            total_credit = Decimal('0')
            
            # إضافة البيانات
            for i, row in enumerate(results):
                debit_amount = Decimal(str(row['total_debit']))
                credit_amount = Decimal(str(row['total_credit']))
                balance = debit_amount - credit_amount
                
                # تخطي الأرصدة الصفرية إذا كان مطلوباً
                if not show_zero and balance == 0:
                    continue
                
                self.trial_balance_table.insertRow(i)
                
                # رقم الحساب
                self.trial_balance_table.setItem(i, 0, QTableWidgetItem(str(row['account_code'])))
                
                # اسم الحساب
                self.trial_balance_table.setItem(i, 1, QTableWidgetItem(row['account_name_ar']))
                
                # الأرصدة (مبسطة - بدون رصيد سابق)
                self.trial_balance_table.setItem(i, 2, QTableWidgetItem("0.00"))  # رصيد سابق مدين
                self.trial_balance_table.setItem(i, 3, QTableWidgetItem("0.00"))  # رصيد سابق دائن
                
                # الحركة
                self.trial_balance_table.setItem(i, 4, QTableWidgetItem(f"{debit_amount:,.2f}"))
                self.trial_balance_table.setItem(i, 5, QTableWidgetItem(f"{credit_amount:,.2f}"))
                
                # الرصيد الختامي
                if balance > 0:
                    self.trial_balance_table.setItem(i, 6, QTableWidgetItem(f"{balance:,.2f}"))
                    self.trial_balance_table.setItem(i, 7, QTableWidgetItem("0.00"))
                else:
                    self.trial_balance_table.setItem(i, 6, QTableWidgetItem("0.00"))
                    self.trial_balance_table.setItem(i, 7, QTableWidgetItem(f"{abs(balance):,.2f}"))
                
                total_debit += debit_amount
                total_credit += credit_amount
            
            # تحديث الإجماليات
            self.total_debit_label.setText(f"إجمالي المدين: {total_debit:,.2f}")
            self.total_credit_label.setText(f"إجمالي الدائن: {total_credit:,.2f}")
            
            difference = abs(total_debit - total_credit)
            if difference == 0:
                self.balance_label.setText("متوازن ✓")
                self.balance_label.setStyleSheet("font-weight: bold; color: #27ae60; font-size: 14px;")
            else:
                self.balance_label.setText(f"الفرق: {difference:,.2f}")
                self.balance_label.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 14px;")
            
            QMessageBox.information(self, "نجح", f"تم إنشاء ميزان المراجعة بنجاح\nعدد الحسابات: {self.trial_balance_table.rowCount()}")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء ميزان المراجعة:\n{str(e)}")
    
    def generate_account_statement(self):
        """إنشاء كشف حساب"""
        account_code = self.as_account_code.currentData()
        if not account_code:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار رقم الحساب")
            return
        
        try:
            date_from = self.as_date_from.date().toString("yyyy-MM-dd")
            date_to = self.as_date_to.date().toString("yyyy-MM-dd")
            
            # استعلام كشف الحساب
            query = """
                SELECT 
                    je.entry_date,
                    je.id as entry_id,
                    jed.description,
                    jed.debit_amount,
                    jed.credit_amount
                FROM journal_entry_details jed
                JOIN journal_entries je ON jed.journal_entry_id = je.id
                WHERE jed.account_code = ? 
                    AND je.entry_date >= ? 
                    AND je.entry_date <= ?
                    AND je.is_posted = 1
                ORDER BY je.entry_date, je.id
            """
            
            results = self.db_manager.execute_query(query, (account_code, date_from, date_to))
            
            # مسح الجدول
            self.account_statement_table.setRowCount(0)
            
            running_balance = Decimal('0')
            
            # إضافة البيانات
            for i, row in enumerate(results):
                self.account_statement_table.insertRow(i)
                
                debit = Decimal(str(row['debit_amount'] or 0))
                credit = Decimal(str(row['credit_amount'] or 0))
                running_balance += debit - credit
                
                # التاريخ
                self.account_statement_table.setItem(i, 0, QTableWidgetItem(str(row['entry_date'])))
                
                # رقم القيد
                self.account_statement_table.setItem(i, 1, QTableWidgetItem(str(row['entry_id'])))
                
                # البيان
                self.account_statement_table.setItem(i, 2, QTableWidgetItem(row['description'] or ''))
                
                # مدين
                debit_text = f"{debit:,.2f}" if debit > 0 else ""
                self.account_statement_table.setItem(i, 3, QTableWidgetItem(debit_text))
                
                # دائن
                credit_text = f"{credit:,.2f}" if credit > 0 else ""
                self.account_statement_table.setItem(i, 4, QTableWidgetItem(credit_text))
                
                # الرصيد
                balance_text = f"{running_balance:,.2f}"
                balance_item = QTableWidgetItem(balance_text)
                if running_balance < 0:
                    balance_item.setBackground(Qt.red)
                    balance_item.setForeground(Qt.white)
                self.account_statement_table.setItem(i, 5, balance_item)
            
            QMessageBox.information(self, "نجح", f"تم إنشاء كشف الحساب بنجاح\nعدد الحركات: {self.account_statement_table.rowCount()}")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء كشف الحساب:\n{str(e)}")
    
    def generate_journal_report(self):
        """إنشاء تقرير القيود"""
        try:
            date_from = self.jr_date_from.date().toString("yyyy-MM-dd")
            date_to = self.jr_date_to.date().toString("yyyy-MM-dd")
            status_filter = self.jr_status.currentText()
            
            # استعلام تقرير القيود
            query = """
                SELECT 
                    je.id as entry_id,
                    je.entry_date,
                    je.description as entry_description,
                    jed.account_code,
                    ca.account_name_ar,
                    jed.debit_amount,
                    jed.credit_amount,
                    je.is_posted
                FROM journal_entries je
                JOIN journal_entry_details jed ON je.id = jed.journal_entry_id
                LEFT JOIN chart_of_accounts ca ON jed.account_code = ca.account_code
                WHERE je.entry_date >= ? AND je.entry_date <= ?
            """
            
            params = [date_from, date_to]
            
            # فلترة الحالة
            if status_filter == "مرحل":
                query += " AND je.is_posted = 1"
            elif status_filter == "غير مرحل":
                query += " AND je.is_posted = 0"
            
            query += " ORDER BY je.entry_date, je.id, jed.id"
            
            results = self.db_manager.execute_query(query, params)
            
            # مسح الجدول
            self.journal_report_table.setRowCount(0)
            
            # إضافة البيانات
            for i, row in enumerate(results):
                self.journal_report_table.insertRow(i)
                
                # رقم القيد
                self.journal_report_table.setItem(i, 0, QTableWidgetItem(str(row['entry_id'])))
                
                # التاريخ
                self.journal_report_table.setItem(i, 1, QTableWidgetItem(str(row['entry_date'])))
                
                # البيان
                self.journal_report_table.setItem(i, 2, QTableWidgetItem(row['entry_description'] or ''))
                
                # رقم الحساب
                self.journal_report_table.setItem(i, 3, QTableWidgetItem(str(row['account_code'])))
                
                # اسم الحساب
                self.journal_report_table.setItem(i, 4, QTableWidgetItem(row['account_name_ar'] or ''))
                
                # مدين
                debit = Decimal(str(row['debit_amount'] or 0))
                debit_text = f"{debit:,.2f}" if debit > 0 else ""
                self.journal_report_table.setItem(i, 5, QTableWidgetItem(debit_text))
                
                # دائن
                credit = Decimal(str(row['credit_amount'] or 0))
                credit_text = f"{credit:,.2f}" if credit > 0 else ""
                self.journal_report_table.setItem(i, 6, QTableWidgetItem(credit_text))
                
                # الحالة
                status = "مرحل" if row['is_posted'] else "غير مرحل"
                status_item = QTableWidgetItem(status)
                if row['is_posted']:
                    status_item.setBackground(Qt.green)
                else:
                    status_item.setBackground(Qt.yellow)
                self.journal_report_table.setItem(i, 7, status_item)
            
            QMessageBox.information(self, "نجح", f"تم إنشاء تقرير القيود بنجاح\nعدد السجلات: {self.journal_report_table.rowCount()}")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء تقرير القيود:\n{str(e)}")
    
    def generate_financial_report(self, report_code):
        """إنشاء التقارير المالية"""
        reports_map = {
            "balance_sheet": "الميزانية العمومية",
            "income_statement": "قائمة الدخل",
            "cash_flow": "قائمة التدفقات النقدية",
            "equity_changes": "قائمة التغيرات في حقوق الملكية",
            "financial_analysis": "تحليل الأداء المالي",
            "kpi_dashboard": "مؤشرات الأداء الرئيسية"
        }
        
        report_name = reports_map.get(report_code, "تقرير غير محدد")
        QMessageBox.information(self, "تقرير مالي", f"تقرير {report_name} قيد التطوير")
    
    def export_report(self, report_type):
        """تصدير التقرير"""
        QMessageBox.information(self, "تصدير", f"تصدير تقرير {report_type} قيد التطوير")
    
    def print_report(self, report_type):
        """طباعة التقرير"""
        QMessageBox.information(self, "طباعة", f"طباعة تقرير {report_type} قيد التطوير")
    
    def refresh_report(self, report_type):
        """تحديث التقرير"""
        self.generate_report(report_type)