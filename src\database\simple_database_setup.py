#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد قاعدة البيانات المبسطة
Simple Database Setup
"""

import sqlite3
import os
import hashlib
from datetime import datetime

def setup_simple_database(db_path="accounting_system.db"):
    """إعداد قاعدة بيانات مبسطة"""
    try:
        print(f"🚀 بدء إعداد قاعدة البيانات: {db_path}")
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("✅ تم الاتصال بقاعدة البيانات")
        
        # إنشاء جدول المستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                full_name VARCHAR(100) NOT NULL,
                role VARCHAR(20) DEFAULT 'user',
                status VARCHAR(20) DEFAULT 'active',
                phone VARCHAR(20),
                address TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ تم إنشاء جدول المستخدمين")
        
        # إنشاء جدول العملاء
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_code VARCHAR(20) UNIQUE NOT NULL,
                customer_name VARCHAR(100) NOT NULL,
                customer_type VARCHAR(20) DEFAULT 'individual',
                phone VARCHAR(20),
                mobile VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                city VARCHAR(50),
                country VARCHAR(50) DEFAULT 'اليمن',
                credit_limit DECIMAL(15,2) DEFAULT 0,
                current_balance DECIMAL(15,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT 'active',
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ تم إنشاء جدول العملاء")
        
        # إنشاء جدول الموردين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                supplier_code VARCHAR(20) UNIQUE NOT NULL,
                supplier_name VARCHAR(100) NOT NULL,
                company_name VARCHAR(100),
                phone VARCHAR(20),
                mobile VARCHAR(20),
                email VARCHAR(100),
                address TEXT,
                city VARCHAR(50),
                country VARCHAR(50) DEFAULT 'اليمن',
                status VARCHAR(20) DEFAULT 'active',
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ تم إنشاء جدول الموردين")
        
        # إنشاء جدول فئات المنتجات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS product_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category_name VARCHAR(100) NOT NULL,
                category_code VARCHAR(20) UNIQUE,
                parent_id INTEGER,
                description TEXT,
                status VARCHAR(20) DEFAULT 'active',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES product_categories(id)
            )
        """)
        print("✅ تم إنشاء جدول فئات المنتجات")
        
        # إنشاء جدول المنتجات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_code VARCHAR(50) UNIQUE NOT NULL,
                barcode VARCHAR(50) UNIQUE,
                product_name VARCHAR(100) NOT NULL,
                description TEXT,
                category_id INTEGER,
                brand VARCHAR(50),
                unit VARCHAR(20) DEFAULT 'قطعة',
                cost_price DECIMAL(15,2) DEFAULT 0,
                selling_price DECIMAL(15,2) DEFAULT 0,
                wholesale_price DECIMAL(15,2) DEFAULT 0,
                min_stock_level INTEGER DEFAULT 0,
                max_stock_level INTEGER DEFAULT 0,
                current_stock INTEGER DEFAULT 0,
                reorder_point INTEGER DEFAULT 0,
                status VARCHAR(20) DEFAULT 'active',
                is_service BOOLEAN DEFAULT FALSE,
                track_inventory BOOLEAN DEFAULT TRUE,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES product_categories(id)
            )
        """)
        print("✅ تم إنشاء جدول المنتجات")
        
        # إنشاء جدول فواتير المبيعات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sales_invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number VARCHAR(50) UNIQUE NOT NULL,
                customer_id INTEGER NOT NULL,
                invoice_date DATE NOT NULL,
                due_date DATE,
                subtotal DECIMAL(15,2) DEFAULT 0,
                discount_amount DECIMAL(15,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) DEFAULT 0,
                paid_amount DECIMAL(15,2) DEFAULT 0,
                remaining_amount DECIMAL(15,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT 'draft',
                payment_status VARCHAR(20) DEFAULT 'unpaid',
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id)
            )
        """)
        print("✅ تم إنشاء جدول فواتير المبيعات")
        
        # إنشاء جدول تفاصيل فواتير المبيعات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sales_invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity DECIMAL(10,3) NOT NULL,
                unit_price DECIMAL(15,2) NOT NULL,
                discount_amount DECIMAL(15,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) NOT NULL,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES sales_invoices(id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
        """)
        print("✅ تم إنشاء جدول تفاصيل فواتير المبيعات")
        
        # إنشاء جدول المدفوعات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                payment_number VARCHAR(50) UNIQUE NOT NULL,
                payment_type VARCHAR(20) NOT NULL,
                reference_type VARCHAR(20) NOT NULL,
                reference_id INTEGER NOT NULL,
                amount DECIMAL(15,2) NOT NULL,
                payment_method VARCHAR(20) DEFAULT 'cash',
                payment_date DATE NOT NULL,
                notes TEXT,
                status VARCHAR(20) DEFAULT 'completed',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ تم إنشاء جدول المدفوعات")

        # إنشاء جدول المخازن
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS warehouses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code VARCHAR(20) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                location VARCHAR(200),
                type VARCHAR(50) DEFAULT 'مخزن رئيسي',
                manager VARCHAR(100),
                is_active BOOLEAN DEFAULT TRUE,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ تم إنشاء جدول المخازن")

        # إنشاء جدول حركات المخزون
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS inventory_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                movement_number VARCHAR(50) UNIQUE NOT NULL,
                movement_type VARCHAR(20) NOT NULL,
                warehouse_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity DECIMAL(10,3) NOT NULL,
                unit_price DECIMAL(15,2),
                total_amount DECIMAL(15,2),
                reason VARCHAR(200),
                reference_type VARCHAR(20),
                reference_id INTEGER,
                movement_date DATE NOT NULL,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (warehouse_id) REFERENCES warehouses(id),
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
        """)
        print("✅ تم إنشاء جدول حركات المخزون")
        
        # إنشاء جدول الإعدادات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key VARCHAR(100) UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type VARCHAR(20) DEFAULT 'string',
                description TEXT,
                category VARCHAR(50),
                is_public BOOLEAN DEFAULT FALSE,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ تم إنشاء جدول الإعدادات")
        
        # إنشاء جدول سجل النشاطات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action VARCHAR(50) NOT NULL,
                table_name VARCHAR(50),
                record_id INTEGER,
                old_values TEXT,
                new_values TEXT,
                ip_address VARCHAR(45),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )
        """)
        print("✅ تم إنشاء جدول سجل النشاطات")
        
        # إنشاء الفهارس
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_customers_code ON customers(customer_code)",
            "CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(customer_name)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_code ON suppliers(supplier_code)",
            "CREATE INDEX IF NOT EXISTS idx_products_code ON products(product_code)",
            "CREATE INDEX IF NOT EXISTS idx_products_name ON products(product_name)",
            "CREATE INDEX IF NOT EXISTS idx_sales_invoices_number ON sales_invoices(invoice_number)",
            "CREATE INDEX IF NOT EXISTS idx_sales_invoices_customer ON sales_invoices(customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_sales_invoices_date ON sales_invoices(invoice_date)",
            "CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(payment_date)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_movements_product ON inventory_movements(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_activity_log_user ON activity_log(user_id)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print("✅ تم إنشاء الفهارس")
        
        # إدراج البيانات الافتراضية
        insert_default_data(cursor)
        
        # حفظ التغييرات
        conn.commit()
        conn.close()
        
        print("🎉 تم إعداد قاعدة البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def insert_default_data(cursor):
    """إدراج البيانات الافتراضية"""
    try:
        # فحص وجود المستخدم الافتراضي
        cursor.execute("SELECT id FROM users WHERE username = 'admin'")
        if not cursor.fetchone():
            # إنشاء المستخدم الافتراضي
            password_hash = hashlib.sha256("admin123".encode()).hexdigest()
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, full_name, role, status)
                VALUES (?, ?, ?, ?, ?, ?)
            """, ('admin', '<EMAIL>', password_hash, 'مدير النظام', 'admin', 'active'))
            print("✅ تم إنشاء المستخدم الافتراضي: admin")
        
        # إدراج الإعدادات الافتراضية
        settings = [
            ('company_name', 'شركة المحاسبة المتقدمة', 'string', 'اسم الشركة', 'company', True),
            ('company_address', 'صنعاء - اليمن', 'string', 'عنوان الشركة', 'company', True),
            ('company_phone', '+967-1-123456', 'string', 'هاتف الشركة', 'company', True),
            ('company_email', '<EMAIL>', 'string', 'بريد الشركة', 'company', True),
            ('currency', 'YER', 'string', 'العملة الافتراضية', 'general', True),
            ('currency_symbol', 'ر.ي', 'string', 'رمز العملة', 'general', True),
            ('tax_rate', '0', 'decimal', 'معدل الضريبة الافتراضي', 'tax', True),
            ('invoice_prefix', 'INV', 'string', 'بادئة رقم الفاتورة', 'invoicing', False),
            ('next_invoice_number', '1', 'integer', 'رقم الفاتورة التالي', 'invoicing', False)
        ]
        
        for setting in settings:
            cursor.execute("""
                INSERT OR IGNORE INTO system_settings 
                (setting_key, setting_value, setting_type, description, category, is_public)
                VALUES (?, ?, ?, ?, ?, ?)
            """, setting)
        
        print("✅ تم إدراج الإعدادات الافتراضية")
        
        # إدراج فئات المنتجات الافتراضية
        categories = [
            ('CAT001', 'إلكترونيات', 'الأجهزة الإلكترونية'),
            ('CAT002', 'ملابس', 'الملابس والأزياء'),
            ('CAT003', 'أغذية', 'المواد الغذائية'),
            ('CAT004', 'كتب', 'الكتب والمطبوعات'),
            ('CAT005', 'أدوات منزلية', 'الأدوات المنزلية'),
        ]
        
        for cat_code, cat_name, description in categories:
            cursor.execute("""
                INSERT OR IGNORE INTO product_categories 
                (category_code, category_name, description)
                VALUES (?, ?, ?)
            """, (cat_code, cat_name, description))
        
        print("✅ تم إدراج فئات المنتجات الافتراضية")
        
        # إدراج بيانات تجريبية
        # عملاء تجريبيون
        customers = [
            ('CUST001', 'أحمد محمد علي', 'individual', '*********', '<EMAIL>', 'صنعاء'),
            ('CUST002', 'شركة التقنية المتقدمة', 'company', '*********', '<EMAIL>', 'عدن'),
            ('CUST003', 'فاطمة حسن', 'individual', '*********', '<EMAIL>', 'تعز'),
        ]
        
        for cust_code, cust_name, cust_type, phone, email, city in customers:
            cursor.execute("""
                INSERT OR IGNORE INTO customers 
                (customer_code, customer_name, customer_type, phone, email, city)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (cust_code, cust_name, cust_type, phone, email, city))
        
        print("✅ تم إدراج العملاء التجريبيين")
        
        # موردون تجريبيون
        suppliers = [
            ('SUPP001', 'شركة الإمدادات الذهبية', 'شركة الإمدادات الذهبية', '*********', '<EMAIL>', 'صنعاء'),
            ('SUPP002', 'مؤسسة التجارة الحديثة', 'مؤسسة التجارة الحديثة', '*********', '<EMAIL>', 'الحديدة'),
        ]
        
        for supp_code, supp_name, company, phone, email, city in suppliers:
            cursor.execute("""
                INSERT OR IGNORE INTO suppliers 
                (supplier_code, supplier_name, company_name, phone, email, city)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (supp_code, supp_name, company, phone, email, city))
        
        print("✅ تم إدراج الموردين التجريبيين")
        
        # منتجات تجريبية
        products = [
            ('PROD001', '1234567890123', 'لابتوب ديل', 'لابتوب ديل انسبايرون 15', 1, 'Dell', 'قطعة', 800000, 1000000, 950000, 5, 50, 25),
            ('PROD002', '2345678901234', 'ماوس لاسلكي', 'ماوس لاسلكي لوجيتك', 1, 'Logitech', 'قطعة', 15000, 25000, 22000, 10, 100, 45),
            ('PROD003', '3456789012345', 'كيبورد ميكانيكي', 'كيبورد ميكانيكي للألعاب', 1, 'Razer', 'قطعة', 45000, 75000, 65000, 5, 30, 12),
            ('PROD004', '4567890123456', 'شاشة LED 24 بوصة', 'شاشة LED عالية الدقة', 1, 'Samsung', 'قطعة', 120000, 180000, 160000, 3, 20, 8),
            ('PROD005', '5678901234567', 'طابعة ليزر', 'طابعة ليزر أبيض وأسود', 1, 'HP', 'قطعة', 200000, 300000, 280000, 2, 15, 5),
        ]
        
        for prod_code, barcode, prod_name, description, cat_id, brand, unit, cost, selling, wholesale, min_stock, max_stock, current_stock in products:
            cursor.execute("""
                INSERT OR IGNORE INTO products 
                (product_code, barcode, product_name, description, category_id, brand, unit, 
                 cost_price, selling_price, wholesale_price, min_stock_level, max_stock_level, current_stock)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (prod_code, barcode, prod_name, description, cat_id, brand, unit, cost, selling, wholesale, min_stock, max_stock, current_stock))
        
        print("✅ تم إدراج المنتجات التجريبية")
        
    except Exception as e:
        print(f"❌ خطأ في إدراج البيانات الافتراضية: {e}")

if __name__ == "__main__":
    setup_simple_database()
