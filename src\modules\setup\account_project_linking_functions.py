#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف إضافية لشاشة ربط الحسابات بإدخال المشاريع
Additional Functions for Account-Project Linking
"""

from PyQt5.QtWidgets import (QMessageBox, QTableWidgetItem, QGroupBox, QGridLayout,
                             QLabel, QCheckBox, QComboBox, QPushButton, QHBoxLayout,
                             QVBoxLayout, QWidget, QTextEdit, QProgressBar)
from PyQt5.QtCore import Qt
from datetime import datetime

def create_reports_tab_function(self):
    """إنشاء تبويب التقارير"""
    tab = QWidget()
    layout = QVBoxLayout()
    
    # مجموعة تقارير الربط
    reports_group = QGroupBox("📊 تقارير الربط")
    reports_layout = QGridLayout()
    
    # أزرار التقارير
    linking_summary_btn = QPushButton("📋 ملخص الربط")
    linking_summary_btn.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    linking_summary_btn.clicked.connect(self.generate_linking_summary)
    
    project_accounts_btn = QPushButton("📁 حسابات المشاريع")
    project_accounts_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    project_accounts_btn.clicked.connect(self.generate_project_accounts)
    
    unlinked_accounts_btn = QPushButton("🚫 الحسابات غير المربوطة")
    unlinked_accounts_btn.setStyleSheet("""
        QPushButton {
            background-color: #e67e22;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #d35400;
        }
    """)
    unlinked_accounts_btn.clicked.connect(self.generate_unlinked_accounts)
    
    reports_layout.addWidget(linking_summary_btn, 0, 0)
    reports_layout.addWidget(project_accounts_btn, 0, 1)
    reports_layout.addWidget(unlinked_accounts_btn, 0, 2)
    
    reports_group.setLayout(reports_layout)
    
    # منطقة عرض التقارير
    reports_display_group = QGroupBox("📄 عرض التقارير")
    reports_display_layout = QVBoxLayout()
    
    self.reports_display = QTextEdit()
    self.reports_display.setPlaceholderText("ستظهر التقارير هنا...")
    self.reports_display.setStyleSheet("""
        QTextEdit {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    """)
    
    # أزرار التحكم في التقارير
    report_buttons = QHBoxLayout()
    
    export_report_btn = QPushButton("📤 تصدير التقرير")
    export_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #17a2b8;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #138496;
        }
    """)
    export_report_btn.clicked.connect(self.export_report)
    
    print_report_btn = QPushButton("🖨️ طباعة التقرير")
    print_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #6c757d;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5a6268;
        }
    """)
    print_report_btn.clicked.connect(self.print_report)
    
    clear_report_btn = QPushButton("🗑️ مسح التقرير")
    clear_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #dc3545;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #c82333;
        }
    """)
    clear_report_btn.clicked.connect(self.clear_report)
    
    report_buttons.addWidget(export_report_btn)
    report_buttons.addWidget(print_report_btn)
    report_buttons.addWidget(clear_report_btn)
    report_buttons.addStretch()
    
    reports_display_layout.addWidget(self.reports_display)
    reports_display_layout.addLayout(report_buttons)
    reports_display_group.setLayout(reports_display_layout)
    
    layout.addWidget(reports_group)
    layout.addWidget(reports_display_group)
    
    tab.setLayout(layout)
    return tab

def create_control_buttons_function(self):
    """إنشاء أزرار التحكم"""
    layout = QHBoxLayout()
    
    # زر الحفظ
    save_btn = QPushButton("💾 حفظ إعدادات الربط")
    save_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    save_btn.clicked.connect(self.save_linking_settings)
    
    # زر الإلغاء
    cancel_btn = QPushButton("❌ إلغاء")
    cancel_btn.setStyleSheet("""
        QPushButton {
            background-color: #e74c3c;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #c0392b;
        }
    """)
    cancel_btn.clicked.connect(self.reject)
    
    # زر التحقق
    validate_btn = QPushButton("✅ التحقق من الربط")
    validate_btn.setStyleSheet("""
        QPushButton {
            background-color: #f39c12;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #e67e22;
        }
    """)
    validate_btn.clicked.connect(self.validate_linking)
    
    # زر المساعدة
    help_btn = QPushButton("❓ المساعدة")
    help_btn.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    help_btn.clicked.connect(self.show_help)
    
    layout.addWidget(help_btn)
    layout.addWidget(validate_btn)
    layout.addStretch()
    layout.addWidget(save_btn)
    layout.addWidget(cancel_btn)
    
    return layout

def generate_linking_summary_function(self):
    """إنتاج ملخص الربط"""
    try:
        report = f"""
📋 ملخص ربط الحسابات بالمشاريع
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 إحصائيات عامة:
   📝 إجمالي الحسابات: 19 حساب
   🔗 الحسابات المربوطة: 12 حساب (63%)
   🚫 الحسابات غير المربوطة: 7 حسابات (37%)
   📁 إجمالي المشاريع: 5 مشاريع

🔗 أنواع الربط:
   ✅ ربط إجباري: 8 حسابات
   🔄 ربط اختياري: 4 حسابات
   ⚙️ ربط تلقائي: 0 حسابات
   📋 ربط شرطي: 0 حسابات

📁 توزيع الحسابات على المشاريع:
   🏗️ مشروع تطوير النظام المحاسبي: 5 حسابات
   🏢 مشروع تطوير المباني: 3 حسابات
   💻 مشروع تحديث الأنظمة: 2 حسابات
   📊 مشروع تحليل البيانات: 1 حساب
   🔧 مشروع الصيانة العامة: 1 حساب

⚠️ تحذيرات:
   • 7 حسابات تحتاج إلى ربط
   • مشروع واحد بدون حسابات مربوطة
   • 3 قواعد ربط غير مفعلة

💡 التوصيات:
   • ربط الحسابات المتبقية بالمشاريع المناسبة
   • مراجعة قواعد الربط وتفعيلها
   • إنشاء مشاريع إضافية حسب الحاجة
"""
        
        self.reports_display.setPlainText(report)
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إنتاج ملخص الربط:\n{str(e)}")

def generate_project_accounts_function(self):
    """إنتاج تقرير حسابات المشاريع"""
    try:
        report = f"""
📁 تقرير حسابات المشاريع
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

"""
        
        # بيانات نموذجية للمشاريع والحسابات المربوطة
        projects_data = {
            "مشروع تطوير النظام المحاسبي (PROJ-001)": [
                ("7001", "مصروفات الرواتب", "ربط إجباري"),
                ("8001", "مصروفات الإيجار", "ربط إجباري"),
                ("6001", "إيرادات المبيعات", "ربط إجباري"),
                ("1004", "المخزون", "ربط اختياري"),
                ("2001", "الأثاث والمعدات", "ربط اختياري")
            ],
            "مشروع تطوير المباني (PROJ-002)": [
                ("2002", "السيارات", "ربط إجباري"),
                ("3001", "حساب الموردين", "ربط إجباري"),
                ("1003", "حساب العملاء", "ربط اختياري")
            ],
            "مشروع تحديث الأنظمة (PROJ-003)": [
                ("1001", "النقدية في الصندوق", "ربط اختياري"),
                ("1002", "البنك الأهلي اليمني", "ربط اختياري")
            ]
        }
        
        for project_name, accounts in projects_data.items():
            report += f"\n🏗️ {project_name}:\n"
            report += "-" * 50 + "\n"
            
            for account_code, account_name, linking_type in accounts:
                report += f"   {account_code} - {account_name} ({linking_type})\n"
            
            report += f"   📊 إجمالي الحسابات: {len(accounts)}\n\n"
        
        report += f"""
{'='*60}
📊 ملخص التوزيع:
   🏗️ مشروع تطوير النظام المحاسبي: 5 حسابات
   🏢 مشروع تطوير المباني: 3 حسابات  
   💻 مشروع تحديث الأنظمة: 2 حسابات
   📊 مشروع تحليل البيانات: 1 حساب
   🔧 مشروع الصيانة العامة: 1 حساب
   
   📝 إجمالي الحسابات المربوطة: 12 حساب
"""
        
        self.reports_display.setPlainText(report)
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إنتاج تقرير حسابات المشاريع:\n{str(e)}")

def generate_unlinked_accounts_function(self):
    """إنتاج تقرير الحسابات غير المربوطة"""
    try:
        report = f"""
🚫 تقرير الحسابات غير المربوطة
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

⚠️ الحسابات التي تحتاج إلى ربط:

📊 الحسابات الرئيسية:
   1000 - الأصول المتداولة (حساب رئيسي)
   3000 - الخصوم المتداولة (حساب رئيسي)
   5000 - رأس المال (حساب رئيسي)

📋 الحسابات الفرعية:
   3002 - المرتبات المستحقة (ينصح بالربط)
   5001 - رأس المال المدفوع (ربط اختياري)
   7000 - المصروفات التشغيلية (حساب رئيسي)
   8000 - المصروفات الإدارية (حساب رئيسي)

📊 تحليل الحسابات غير المربوطة:
   🏛️ حسابات رئيسية: 4 حسابات
   📋 حسابات فرعية: 3 حسابات
   💰 حسابات مالية: 2 حسابات
   📈 حسابات إيرادات/مصروفات: 2 حسابات

💡 توصيات الربط:
   • ربط حسابات المصروفات بمشاريع محددة
   • ربط الحسابات المالية بالمشاريع الاستثمارية
   • إنشاء مشاريع إدارية للحسابات العامة
   • مراجعة ضرورة ربط الحسابات الرئيسية

⚠️ أولويات الربط:
   🔴 عالية: حسابات المصروفات والإيرادات
   🟡 متوسطة: الحسابات المالية
   🟢 منخفضة: الحسابات الرئيسية

📋 الخطوات المقترحة:
   1. إنشاء مشروع "العمليات الإدارية العامة"
   2. ربط حسابات المصروفات الإدارية
   3. مراجعة ضرورة ربط الحسابات الرئيسية
   4. تحديث قواعد الربط حسب الحاجة
"""
        
        self.reports_display.setPlainText(report)
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إنتاج تقرير الحسابات غير المربوطة:\n{str(e)}")

def show_help_function(self):
    """عرض المساعدة"""
    help_text = """
🔗 مساعدة ربط الحسابات بالمشاريع

📋 الوظائف المتاحة:

🔗 إعداد الربط:
• اختيار الحساب من الشجرة
• تحديد نوع الربط (إجباري/اختياري/تلقائي/شرطي)
• اختيار المشروع الافتراضي
• تحديد مركز التكلفة
• إعدادات الموافقة والقيود التلقائية

📁 إدارة المشاريع:
• إضافة مشاريع جديدة
• تعديل تفاصيل المشاريع
• تحديد حالة المشروع ونوعه
• ربط الحسابات بالمشاريع

📋 قواعد الربط:
• قواعد عامة للربط الإجباري/الاختياري
• قواعد مخصصة لحسابات محددة
• إعدادات التحقق والتنبيهات
• معالجة حالات عدم الربط

📊 التقارير:
• ملخص شامل لحالة الربط
• تقرير حسابات كل مشروع
• قائمة الحسابات غير المربوطة
• إحصائيات وتحليلات مفصلة

💡 نصائح:
• استخدم الربط الإجباري للحسابات الهامة
• أنشئ مشاريع منطقية تعكس أنشطة الشركة
• راجع قواعد الربط بانتظام
• استخدم التقارير لمتابعة حالة الربط
• اختبر الربط قبل التطبيق النهائي

🔧 أنواع الربط:
• إجباري: يجب ربط الحساب بمشروع
• اختياري: يمكن ربط الحساب أو تركه
• تلقائي: ربط تلقائي بمشروع افتراضي
• شرطي: ربط حسب شروط محددة

📈 فوائد الربط:
• تتبع تكاليف المشاريع بدقة
• تحليل ربحية كل مشروع
• إعداد تقارير مالية مفصلة
• تحسين التحكم في الميزانية
• سهولة المراجعة والتدقيق
"""
    
    QMessageBox.information(self, "المساعدة", help_text)
