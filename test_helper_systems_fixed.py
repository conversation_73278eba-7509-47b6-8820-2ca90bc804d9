#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الأنظمة المساعدة المحسنة
Fixed Helper Systems Test
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_helper_systems():
    """اختبار الأنظمة المساعدة"""
    try:
        print("🚀 تشغيل الأنظمة المساعدة المحسنة...")
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # محاولة استيراد الأنظمة المساعدة
        try:
            from modules.helper_systems.helper_systems_main import HelperSystemsMainDialog
            print("✅ تم استيراد الأنظمة المساعدة بنجاح")
        except ImportError as e:
            print(f"❌ خطأ في استيراد الأنظمة المساعدة: {e}")
            return False
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass
        
        try:
            dialog = HelperSystemsMainDialog(MockDBManager())
            print("✅ تم إنشاء واجهة الأنظمة المساعدة بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء الواجهة: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        dialog.show()
        print("🛠️ الأنظمة المساعدة المحسنة جاهزة!")
        
        print("\n📋 التحسينات المطبقة:")
        print("   ✓ تحسين حجم وتخطيط البطاقات")
        print("   ✓ تحسين تنسيق الأيقونات والعناوين")
        print("   ✓ تحسين تنسيق الأوصاف")
        print("   ✓ تحسين تصميم الأزرار مع تأثيرات التمرير")
        print("   ✓ تحسين منطقة التمرير والتخطيط العام")
        print("   ✓ إضافة مساحات مرنة للتخطيط")
        
        print("\n🎨 التحسينات التصميمية:")
        print("   • بطاقات بحجم مرن (350-450 بكسل)")
        print("   • أيقونات محسنة (40 بكسل)")
        print("   • عناوين محاذاة لليمين")
        print("   • أوصاف متعددة الأسطر")
        print("   • أزرار مع تأثيرات ثلاثية الأبعاد")
        print("   • منطقة تمرير محسنة")
        
        print("\n🛠️ الأنظمة المتاحة (12 نظام):")
        systems_list = [
            "🧮 الآلة الحاسبة المتقدمة (مطورة بالكامل)",
            "📅 التقويم والمواعيد (قيد التطوير)",
            "📝 محرر النصوص (قيد التطوير)",
            "🔍 البحث المتقدم (قيد التطوير)",
            "📊 مولد التقارير (قيد التطوير)",
            "🔧 أدوات النظام (قيد التطوير)",
            "💱 محول العملات (قيد التطوير)",
            "📈 مراقب الأداء (قيد التطوير)",
            "🗂️ مدير الملفات (قيد التطوير)",
            "🔐 مولد كلمات المرور (قيد التطوير)",
            "📋 مدير الحافظة (قيد التطوير)",
            "🌐 متصفح الويب (قيد التطوير)"
        ]
        
        for i, system in enumerate(systems_list, 1):
            print(f"   {i:2d}. {system}")
        
        print("\n📊 إحصائيات التطوير:")
        print("   🛠️ 12 نظام مساعد مختلف")
        print("   ✅ 1 نظام مطور بالكامل")
        print("   🚧 11 نظام قيد التطوير")
        print("   🎨 واجهة محسنة ومتجاوبة")
        print("   🔧 تصميم احترافي مع تأثيرات متقدمة")
        
        print("\n🎯 كيفية الاستخدام:")
        print("   1. انقر على أي بطاقة نظام")
        print("   2. استخدم زر 'تشغيل' لفتح النظام")
        print("   3. الآلة الحاسبة متاحة للاستخدام الفوري")
        print("   4. باقي الأنظمة ستظهر رسالة 'قيد التطوير'")
        
        print("\n🎉 الأنظمة المساعدة المحسنة جاهزة للاستخدام!")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_helper_systems()
