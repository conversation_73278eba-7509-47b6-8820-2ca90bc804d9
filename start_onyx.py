#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام Onyx ERP المحسن
Enhanced Onyx ERP Startup File
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPixmap

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """الدالة الرئيسية لتشغيل نظام Onyx ERP"""
    try:
        print("Welcome to Onyx ERP System")
        print("نظام تخطيط موارد المؤسسات المتكامل")
        print("=" * 60)
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("Onyx ERP")
        app.setApplicationVersion("1.0.0")
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تعيين الخط الافتراضي
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        
        print("Starting Onyx ERP System...")
        print("جاري تشغيل نظام Onyx ERP...")
        
        # محاولة استيراد واجهة تسجيل الدخول
        try:
            from src.ui.onyx_login_window import OnyxLoginWindow
            print("Successfully loaded login module")
            print("تم تحميل وحدة تسجيل الدخول بنجاح")
        except ImportError as e:
            print(f"Failed to import login window: {e}")
            QMessageBox.critical(None, "خطأ في الاستيراد", 
                               f"فشل في تحميل واجهة تسجيل الدخول:\n{e}")
            return 1
        
        # إنشاء نافذة تسجيل الدخول
        try:
            login_window = OnyxLoginWindow()
            print("Login window created successfully")
            print("تم إنشاء نافذة تسجيل الدخول بنجاح")
        except Exception as e:
            print(f"Failed to create login window: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(None, "خطأ في الإنشاء", 
                               f"فشل في إنشاء نافذة تسجيل الدخول:\n{e}")
            return 1
        
        # إظهار النافذة
        login_window.show()
        login_window.raise_()
        login_window.activateWindow()
        
        print("System started successfully!")
        print("تم تشغيل النظام بنجاح!")
        print()
        print("Default login credentials:")
        print("بيانات تسجيل الدخول الافتراضية:")
        print("   User / المستخدم: admin")
        print("   Password / كلمة المرور: admin123")
        print("   Fiscal Year / السنة المالية: 2025")
        print("   Language / اللغة: عربي")
        print("   Company / الشركة: صقالات ابو سيف للنقل")
        print("   Branch / الفرع: 1 - الفرع الرئيسي")
        print("=" * 60)
        
        # تشغيل التطبيق
        return app.exec_()
        
    except KeyboardInterrupt:
        print("\nSystem stopped by user")
        print("تم إيقاف النظام بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"\nSystem error: {e}")
        print(f"خطأ في النظام: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"Critical error: {e}")
        print(f"خطأ حرج: {e}")
        sys.exit(1)
