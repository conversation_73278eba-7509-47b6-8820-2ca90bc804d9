#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تكامل الوحدات مع الواجهة الرئيسية
Test modules integration with main window
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_modules_import():
    """اختبار استيراد جميع الوحدات"""
    print("🧪 اختبار استيراد الوحدات...")
    
    modules_to_test = [
        ("accounts_module", "AccountsManagementWidget"),
        ("journal_entries_module", "JournalEntriesWidget"),
        ("journal_entries_module", "JournalEntryDialog"),
        ("customers_suppliers_module", "CustomersSupplierModule"),
        ("reports_module", "ReportsWidget"),
        ("advanced_reports_module", "AdvancedReportsModule")
    ]
    
    failed_imports = []
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(f"src.modules.{module_name}", fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_name}.{class_name}: تم الاستيراد بنجاح")
        except ImportError as e:
            print(f"❌ {module_name}.{class_name}: فشل الاستيراد - {e}")
            failed_imports.append((module_name, class_name, str(e)))
        except AttributeError as e:
            print(f"❌ {module_name}.{class_name}: الكلاس غير موجود - {e}")
            failed_imports.append((module_name, class_name, str(e)))
        except Exception as e:
            print(f"❌ {module_name}.{class_name}: خطأ عام - {e}")
            failed_imports.append((module_name, class_name, str(e)))
    
    return len(failed_imports) == 0, failed_imports

def test_modules_creation():
    """اختبار إنشاء الوحدات"""
    print("\n🧪 اختبار إنشاء الوحدات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        modules_to_create = [
            ("src.modules.accounts_module", "AccountsManagementWidget"),
            ("src.modules.journal_entries_module", "JournalEntriesWidget"),
            ("src.modules.customers_suppliers_module", "CustomersSupplierModule"),
            ("src.modules.reports_module", "ReportsWidget"),
            ("src.modules.advanced_reports_module", "AdvancedReportsModule")
        ]
        
        created_widgets = []
        failed_creations = []
        
        for module_name, class_name in modules_to_create:
            try:
                module = __import__(module_name, fromlist=[class_name])
                widget_class = getattr(module, class_name)
                widget = widget_class(db_manager)
                created_widgets.append(widget)
                print(f"✅ {class_name}: تم إنشاؤه بنجاح")
            except Exception as e:
                print(f"❌ {class_name}: فشل الإنشاء - {e}")
                failed_creations.append((class_name, str(e)))
        
        # تنظيف
        for widget in created_widgets:
            widget.close()
        
        db_manager.disconnect()
        app.quit()
        
        return len(failed_creations) == 0, failed_creations
        
    except Exception as e:
        print(f"❌ خطأ عام في اختبار إنشاء الوحدات: {e}")
        return False, [("General", str(e))]

def test_main_window_integration():
    """اختبار تكامل الوحدات مع الواجهة الرئيسية"""
    print("\n🧪 اختبار تكامل الواجهة الرئيسية...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.ui.onyx_main_window import OnyxMainWindow
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # بيانات جلسة تجريبية
        session_data = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'admin',
            'company': 'صقالات أبو سيف للنقل',
            'branch': '1 - الفرع الرئيسي',
            'year': '2025',
            'language': 'ar',
            'accounting_unit': '1',
            'main_center': '1 - المركز الرئيسي'
        }
        
        # إنشاء النافذة الرئيسية
        main_window = OnyxMainWindow(session_data, db_manager)
        print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار الوحدات
        modules_methods = [
            ("show_accounts_module", "وحدة الحسابات"),
            ("show_journal_entries", "وحدة القيود اليومية"),
            ("show_reports_module", "وحدة التقارير"),
            ("show_customers_suppliers_module", "وحدة العملاء والموردين"),
            ("show_advanced_reports_module", "وحدة التقارير المتقدمة")
        ]
        
        failed_modules = []
        
        for method_name, module_desc in modules_methods:
            try:
                if hasattr(main_window, method_name):
                    method = getattr(main_window, method_name)
                    method()
                    print(f"✅ {module_desc}: تم تحميلها بنجاح")
                else:
                    print(f"❌ {module_desc}: الدالة {method_name} غير موجودة")
                    failed_modules.append((module_desc, f"Method {method_name} not found"))
            except Exception as e:
                print(f"❌ {module_desc}: فشل التحميل - {e}")
                failed_modules.append((module_desc, str(e)))
        
        # تنظيف
        main_window.close()
        db_manager.disconnect()
        app.quit()
        
        return len(failed_modules) == 0, failed_modules
        
    except Exception as e:
        print(f"❌ خطأ عام في اختبار تكامل الواجهة الرئيسية: {e}")
        import traceback
        traceback.print_exc()
        return False, [("General", str(e))]

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🔧 اختبار تكامل الوحدات مع الواجهة الرئيسية")
    print("   Testing modules integration with main window")
    print("=" * 60)
    
    # اختبار الاستيراد
    import_success, import_failures = test_modules_import()
    
    # اختبار الإنشاء
    creation_success, creation_failures = test_modules_creation()
    
    # اختبار التكامل
    integration_success, integration_failures = test_main_window_integration()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبارات:")
    print(f"   🔍 اختبار الاستيراد: {'✅ نجح' if import_success else '❌ فشل'}")
    print(f"   🏗️ اختبار الإنشاء: {'✅ نجح' if creation_success else '❌ فشل'}")
    print(f"   🔗 اختبار التكامل: {'✅ نجح' if integration_success else '❌ فشل'}")
    
    if import_success and creation_success and integration_success:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ الوحدات مربوطة بشكل صحيح مع الواجهة الرئيسية")
    else:
        print("\n⚠️ بعض الاختبارات فشلت:")
        
        if not import_success:
            print("\n❌ أخطاء الاستيراد:")
            for module, class_name, error in import_failures:
                print(f"   • {module}.{class_name}: {error}")
        
        if not creation_success:
            print("\n❌ أخطاء الإنشاء:")
            for class_name, error in creation_failures:
                print(f"   • {class_name}: {error}")
        
        if not integration_success:
            print("\n❌ أخطاء التكامل:")
            for module_desc, error in integration_failures:
                print(f"   • {module_desc}: {error}")
    
    print("=" * 60)
    
    return 0 if (import_success and creation_success and integration_success) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
