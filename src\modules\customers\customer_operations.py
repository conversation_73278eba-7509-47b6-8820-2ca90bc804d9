#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عمليات إدارة العملاء
Customer Management Operations
"""

from PyQt5.QtWidgets import QMessageBox, QTableWidgetItem, QInputDialog, QFileDialog
from PyQt5.QtCore import Qt, QDate
from datetime import datetime
import json
import csv

def save_customer_function(self):
    """حفظ بيانات العميل"""
    # التحقق من البيانات المطلوبة
    if not self.customer_name_input.text().strip():
        QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم العميل")
        return
    
    if not self.phone_input.text().strip():
        QMessageBox.warning(self, "تحذير", "يرجى إدخال رقم الهاتف")
        return
    
    # إنشاء بيانات العميل
    customer_data = {
        "id": self.customer_id_input.text() or f"C{len(self.customers_data) + 1:03d}",
        "name": self.customer_name_input.text().strip(),
        "phone": self.phone_input.text().strip(),
        "email": self.email_input.text().strip(),
        "birth_date": self.birth_date_input.date().toString("yyyy-MM-dd"),
        "gender": self.gender_input.currentText(),
        "governorate": self.governorate_input.currentText(),
        "city": self.city_input.text().strip(),
        "address": self.address_input.toPlainText().strip(),
        "phone2": self.phone2_input.text().strip(),
        "website": self.website_input.text().strip(),
        "category": self.category_input.currentText(),
        "credit_limit": self.credit_limit_input.value(),
        "discount_rate": self.discount_rate_input.value(),
        "payment_method": self.payment_method_input.currentText(),
        "notes": self.notes_input.toPlainText().strip(),
        "balance": 0.0,  # رصيد افتتاحي
        "registration_date": datetime.now().strftime("%Y-%m-%d")
    }
    
    # التحقق من وجود العميل
    existing_customer = None
    for i, customer in enumerate(self.customers_data):
        if customer["id"] == customer_data["id"]:
            existing_customer = i
            break
    
    if existing_customer is not None:
        # تحديث عميل موجود
        self.customers_data[existing_customer] = customer_data
        QMessageBox.information(self, "تم", "تم تحديث بيانات العميل بنجاح")
    else:
        # إضافة عميل جديد
        self.customers_data.append(customer_data)
        QMessageBox.information(self, "تم", "تم إضافة العميل الجديد بنجاح")
    
    # تحديث الجداول
    self.update_customers_table()
    self.update_categories_table()
    self.update_status_bar()
    
    # مسح النموذج
    self.clear_form()
    
    # العودة إلى تبويب قائمة العملاء
    self.tabs.setCurrentIndex(0)

def edit_customer_function(self):
    """تعديل العميل المحدد"""
    current_row = self.customers_table.currentRow()
    if current_row < 0:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للتعديل")
        return
    
    # الحصول على بيانات العميل
    customer_id = self.customers_table.item(current_row, 0).text()
    customer_data = None
    
    for customer in self.customers_data:
        if customer["id"] == customer_id:
            customer_data = customer
            break
    
    if not customer_data:
        QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات العميل")
        return
    
    # الانتقال إلى تبويب النموذج
    self.tabs.setCurrentIndex(1)
    
    # ملء النموذج ببيانات العميل
    self.customer_id_input.setText(customer_data["id"])
    self.customer_name_input.setText(customer_data["name"])
    self.phone_input.setText(customer_data["phone"])
    self.email_input.setText(customer_data["email"])
    
    if "birth_date" in customer_data:
        birth_date = QDate.fromString(customer_data["birth_date"], "yyyy-MM-dd")
        self.birth_date_input.setDate(birth_date)
    
    if "gender" in customer_data:
        gender_index = self.gender_input.findText(customer_data["gender"])
        if gender_index >= 0:
            self.gender_input.setCurrentIndex(gender_index)
    
    governorate_index = self.governorate_input.findText(customer_data["governorate"])
    if governorate_index >= 0:
        self.governorate_input.setCurrentIndex(governorate_index)
    
    self.city_input.setText(customer_data["city"])
    self.address_input.setPlainText(customer_data.get("address", ""))
    self.phone2_input.setText(customer_data.get("phone2", ""))
    self.website_input.setText(customer_data.get("website", ""))
    
    category_index = self.category_input.findText(customer_data["category"])
    if category_index >= 0:
        self.category_input.setCurrentIndex(category_index)
    
    self.credit_limit_input.setValue(customer_data["credit_limit"])
    self.discount_rate_input.setValue(customer_data["discount_rate"])
    
    if "payment_method" in customer_data:
        payment_index = self.payment_method_input.findText(customer_data["payment_method"])
        if payment_index >= 0:
            self.payment_method_input.setCurrentIndex(payment_index)
    
    self.notes_input.setPlainText(customer_data.get("notes", ""))

def edit_selected_customer_function(self, item):
    """تعديل العميل بالنقر المزدوج"""
    self.edit_customer()

def delete_customer_function(self):
    """حذف العميل المحدد"""
    current_row = self.customers_table.currentRow()
    if current_row < 0:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للحذف")
        return
    
    # الحصول على بيانات العميل
    customer_id = self.customers_table.item(current_row, 0).text()
    customer_name = self.customers_table.item(current_row, 1).text()
    
    # تأكيد الحذف
    reply = QMessageBox.question(self, "تأكيد الحذف", 
                                f"هل أنت متأكد من حذف العميل:\n{customer_name}؟",
                                QMessageBox.Yes | QMessageBox.No,
                                QMessageBox.No)
    
    if reply == QMessageBox.Yes:
        # حذف العميل من البيانات
        self.customers_data = [customer for customer in self.customers_data 
                              if customer["id"] != customer_id]
        
        # تحديث الجداول
        self.update_customers_table()
        self.update_categories_table()
        self.update_status_bar()
        
        QMessageBox.information(self, "تم", "تم حذف العميل بنجاح")

def search_customers_function(self):
    """البحث في العملاء"""
    search_text, ok = QInputDialog.getText(self, "البحث في العملاء", 
                                          "أدخل نص البحث (الاسم، الهاتف، أو البريد الإلكتروني):")
    
    if ok and search_text:
        self.search_input.setText(search_text)
        self.filter_customers()

def refresh_data_function(self):
    """تحديث البيانات"""
    self.update_customers_table()
    self.update_categories_table()
    self.update_status_bar()
    QMessageBox.information(self, "تم", "تم تحديث البيانات بنجاح")

def update_selection_info_function(self):
    """تحديث معلومات التحديد"""
    current_row = self.customers_table.currentRow()
    if current_row >= 0:
        customer_name = self.customers_table.item(current_row, 1).text()
        self.status_bar.showMessage(f"العميل المحدد: {customer_name}", 3000)

def generate_all_customers_report_function(self):
    """إنتاج تقرير جميع العملاء"""
    report = f"""
📋 تقرير جميع العملاء
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 إحصائيات عامة:
   👥 إجمالي العملاء: {len(self.customers_data)} عميل
   
📋 قائمة العملاء:
"""
    
    for i, customer in enumerate(self.customers_data, 1):
        report += f"""
{i}. {customer['name']} ({customer['id']})
   📞 الهاتف: {customer['phone']}
   📧 البريد: {customer['email']}
   📍 العنوان: {customer['governorate']} - {customer['city']}
   🏷️ التصنيف: {customer['category']}
   💰 الرصيد: {customer['balance']:,.0f} ريال
   📅 تاريخ التسجيل: {customer['registration_date']}
   {'─'*50}
"""
    
    self.reports_display.setPlainText(report)

def generate_customers_by_category_report_function(self):
    """إنتاج تقرير العملاء حسب التصنيف"""
    # تجميع العملاء حسب التصنيف
    categories = {}
    for customer in self.customers_data:
        category = customer["category"]
        if category not in categories:
            categories[category] = []
        categories[category].append(customer)
    
    report = f"""
🏷️ تقرير العملاء حسب التصنيف
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

"""
    
    for category, customers in categories.items():
        total_balance = sum(customer["balance"] for customer in customers)
        avg_credit = sum(customer["credit_limit"] for customer in customers) / len(customers)
        
        report += f"""
🏷️ تصنيف: {category}
   👥 عدد العملاء: {len(customers)}
   💰 إجمالي الأرصدة: {total_balance:,.0f} ريال
   💳 متوسط حد الائتمان: {avg_credit:,.0f} ريال

   قائمة العملاء:
"""
        
        for customer in customers:
            report += f"   • {customer['name']} - {customer['phone']} - {customer['balance']:,.0f} ريال\n"
        
        report += "\n" + "─"*50 + "\n"
    
    self.reports_display.setPlainText(report)

def generate_customers_statistics_function(self):
    """إنتاج إحصائيات العملاء"""
    total_customers = len(self.customers_data)
    total_balance = sum(customer["balance"] for customer in self.customers_data)
    total_credit = sum(customer["credit_limit"] for customer in self.customers_data)
    
    # إحصائيات التصنيفات
    categories_stats = {}
    for customer in self.customers_data:
        category = customer["category"]
        if category not in categories_stats:
            categories_stats[category] = 0
        categories_stats[category] += 1
    
    # إحصائيات المحافظات
    governorates_stats = {}
    for customer in self.customers_data:
        governorate = customer["governorate"]
        if governorate not in governorates_stats:
            governorates_stats[governorate] = 0
        governorates_stats[governorate] += 1
    
    report = f"""
📊 إحصائيات العملاء
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 الإحصائيات العامة:
   👥 إجمالي العملاء: {total_customers} عميل
   💰 إجمالي الأرصدة: {total_balance:,.0f} ريال
   💳 إجمالي حدود الائتمان: {total_credit:,.0f} ريال
   📈 متوسط الرصيد للعميل: {total_balance/total_customers:,.0f} ريال
   📊 متوسط حد الائتمان: {total_credit/total_customers:,.0f} ريال

🏷️ توزيع العملاء حسب التصنيف:
"""
    
    for category, count in categories_stats.items():
        percentage = (count / total_customers) * 100
        report += f"   • {category}: {count} عميل ({percentage:.1f}%)\n"
    
    report += f"""

🌍 توزيع العملاء حسب المحافظات:
"""
    
    for governorate, count in governorates_stats.items():
        percentage = (count / total_customers) * 100
        report += f"   • {governorate}: {count} عميل ({percentage:.1f}%)\n"
    
    # أفضل العملاء حسب الرصيد
    top_customers = sorted(self.customers_data, key=lambda x: x["balance"], reverse=True)[:5]
    
    report += f"""

🏆 أفضل 5 عملاء حسب الرصيد:
"""
    
    for i, customer in enumerate(top_customers, 1):
        report += f"   {i}. {customer['name']}: {customer['balance']:,.0f} ريال\n"
    
    self.reports_display.setPlainText(report)

def export_customers_function(self):
    """تصدير بيانات العملاء"""
    file_path, _ = QFileDialog.getSaveFileName(self, "تصدير العملاء", 
                                              "customers.json", 
                                              "JSON Files (*.json);;CSV Files (*.csv)")
    
    if file_path:
        try:
            if file_path.endswith('.json'):
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.customers_data, f, ensure_ascii=False, indent=2)
            elif file_path.endswith('.csv'):
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    if self.customers_data:
                        writer = csv.DictWriter(f, fieldnames=self.customers_data[0].keys())
                        writer.writeheader()
                        writer.writerows(self.customers_data)
            
            QMessageBox.information(self, "تم", f"تم تصدير البيانات إلى:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير البيانات:\n{str(e)}")

def import_customers_function(self):
    """استيراد بيانات العملاء"""
    file_path, _ = QFileDialog.getOpenFileName(self, "استيراد العملاء", 
                                              "", 
                                              "JSON Files (*.json);;CSV Files (*.csv)")
    
    if file_path:
        try:
            if file_path.endswith('.json'):
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_data = json.load(f)
            elif file_path.endswith('.csv'):
                imported_data = []
                with open(file_path, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        imported_data.append(row)
            
            # دمج البيانات
            self.customers_data.extend(imported_data)
            
            # تحديث الجداول
            self.update_customers_table()
            self.update_categories_table()
            self.update_status_bar()
            
            QMessageBox.information(self, "تم", f"تم استيراد {len(imported_data)} عميل بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في استيراد البيانات:\n{str(e)}")

def export_report_function(self):
    """تصدير التقرير"""
    if not self.reports_display.toPlainText():
        QMessageBox.warning(self, "تحذير", "لا يوجد تقرير لتصديره")
        return
    
    file_path, _ = QFileDialog.getSaveFileName(self, "تصدير التقرير", 
                                              "report.txt", 
                                              "Text Files (*.txt)")
    
    if file_path:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(self.reports_display.toPlainText())
            QMessageBox.information(self, "تم", f"تم تصدير التقرير إلى:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير التقرير:\n{str(e)}")

def print_report_function(self):
    """طباعة التقرير"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة الطباعة قيد التطوير")

def clear_report_function(self):
    """مسح التقرير"""
    self.reports_display.clear()

def show_reports_function(self):
    """عرض تبويب التقارير"""
    self.tabs.setCurrentIndex(3)

def show_about_function(self):
    """عرض معلومات البرنامج"""
    QMessageBox.about(self, "حول البرنامج", 
                     "نظام إدارة العملاء v1.0\n\n"
                     "نظام شامل لإدارة العملاء والزبائن\n"
                     "يتضمن إدارة البيانات والتصنيفات والتقارير\n\n"
                     "تطوير: فريق التطوير")

# وظائف إدارة التصنيفات
def add_category_function(self):
    """إضافة تصنيف جديد"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة التصنيفات قيد التطوير")

def edit_category_function(self):
    """تعديل تصنيف"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل التصنيفات قيد التطوير")

def delete_category_function(self):
    """حذف تصنيف"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة حذف التصنيفات قيد التطوير")
