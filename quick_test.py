#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لنظام Onyx ERP
Quick Test for Onyx ERP System
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_login_window():
    """اختبار نافذة تسجيل الدخول"""
    print("🧪 اختبار نافذة تسجيل الدخول...")
    
    try:
        from src.ui.onyx_login_window import OnyxLoginWindow
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء نافذة تسجيل الدخول
        login_window = OnyxLoginWindow()
        login_window.show()
        
        print("✅ تم إنشاء نافذة تسجيل الدخول بنجاح")
        
        # إغلاق النافذة تلقائياً بعد 3 ثوان
        def close_window():
            print("🔄 إغلاق النافذة...")
            login_window.close()
            app.quit()
        
        QTimer.singleShot(3000, close_window)
        
        # تشغيل التطبيق
        app.exec_()
        
        print("✅ تم إغلاق النافذة بنجاح بدون رسائل خطأ")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نافذة تسجيل الدخول: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🧪 اختبار سريع لنظام Onyx ERP")
    print("=" * 50)
    
    # اختبار نافذة تسجيل الدخول
    if test_login_window():
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ لا توجد رسائل خطأ عند تشغيل نافذة تسجيل الدخول")
        return 0
    else:
        print("\n❌ فشل في الاختبار")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        sys.exit(1)