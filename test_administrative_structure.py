#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أنواع الهيكل الإداري
Administrative Structure Types Test
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    print("🚀 تشغيل شاشة أنواع الهيكل الإداري...")
    
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    from modules.setup.administrative_structure_types import AdministrativeStructureTypesDialog
    
    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("✅ تم إنشاء التطبيق بنجاح")
    
    dialog = AdministrativeStructureTypesDialog(MockDBManager())
    print("✅ تم إنشاء شاشة أنواع الهيكل الإداري بنجاح")
    
    dialog.show()
    print("🏗️ شاشة أنواع الهيكل الإداري جاهزة!")
    
    print("\n📋 الميزات المتاحة:")
    print("   ✓ إدارة أنواع الهياكل الإدارية")
    print("   ✓ 4 تبويبات شاملة للإدارة")
    print("   ✓ 5 أنواع هياكل نموذجية")
    print("   ✓ 8 مناصب ووظائف نموذجية")
    print("   ✓ هيكل تنظيمي مرئي")
    print("   ✓ تقارير شاملة وإحصائيات")
    print("   ✓ تسلسل هرمي بـ 4 مستويات")
    print("   ✓ تلوين حسب المستوى الإداري")
    
    print("\n🎯 التبويبات المتاحة:")
    print("   🏗️ أنواع الهياكل - إدارة أنواع الهياكل الإدارية")
    print("   👔 المناصب والوظائف - إدارة المناصب والرواتب")
    print("   📊 الهيكل التنظيمي - عرض مرئي للهيكل")
    print("   📋 التقارير - تقارير شاملة وإحصائيات")
    
    print("\n🏗️ أنواع الهياكل النموذجية:")
    print("   📊 الهيكل الهرمي التقليدي (5 مستويات)")
    print("   🔄 الهيكل المصفوفي (4 مستويات)")
    print("   📏 الهيكل المسطح (3 مستويات)")
    print("   🎯 الهيكل الوظيفي (4 مستويات)")
    print("   🌐 الهيكل الشبكي (3 مستويات)")
    
    print("\n👔 المناصب النموذجية:")
    print("   🔴 المستوى 1: المدير العام (500,000 ريال)")
    print("   🟡 المستوى 2: المدير المالي، مدير الموارد البشرية، مدير العمليات")
    print("   🟢 المستوى 3: محاسب أول، أخصائي موارد بشرية، مشرف عمليات")
    print("   🔵 المستوى 4: موظف إداري")
    
    print("\n📊 الإحصائيات:")
    print("   🏗️ 5 أنواع هياكل إدارية")
    print("   👔 8 مناصب ووظائف")
    print("   📊 4 مستويات إدارية")
    print("   🏢 5 أقسام مختلفة")
    print("   💰 إجمالي الرواتب: 2,060,000 ريال")
    
    print("\n🎉 الشاشة جاهزة للاستخدام!")
    
    sys.exit(app.exec_())
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من وجود ملفات الوحدة في المسار الصحيح")
    
except Exception as e:
    print(f"❌ خطأ في تشغيل الشاشة: {e}")
    import traceback
    traceback.print_exc()
