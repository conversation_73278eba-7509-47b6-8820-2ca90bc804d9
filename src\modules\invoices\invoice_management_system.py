#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الفواتير المحسن
Enhanced Invoice Management System
"""

import sys
from datetime import datetime, date, timedelta
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QTableWidget, QTableWidgetItem, QPushButton, 
                             QLineEdit, QLabel, QComboBox, QDateEdit, QGroupBox,
                             QSplitter, QTabWidget, QFrame, QHeaderView,
                             QApplication, QMessageBox, QMenu, QAction,
                             QToolBar, QStatusBar, QProgressBar, QCheckBox,
                             QSpinBox, QDoubleSpinBox, QTextEdit, QDialog,
                             QFormLayout, QDialogButtonBox, QFileDialog)
from PyQt5.QtCore import Qt, QD<PERSON>, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QColor, QIcon, QPalette

from .enhanced_invoice_system import (EnhancedInvoiceDialog, InvoiceStatusManager, 
                                     InvoiceSearchEngine, InvoiceValidationEngine)

class InvoiceStatsWidget(QWidget):
    """ودجت إحصائيات الفواتير"""
    
    def __init__(self, db_manager=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_stats()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QHBoxLayout()
        
        # إجمالي الفواتير
        total_card = self.create_stat_card(
            "📊 إجمالي الفواتير", "0", "#007bff", "إجمالي عدد الفواتير"
        )
        layout.addWidget(total_card)
        
        # الفواتير المدفوعة
        paid_card = self.create_stat_card(
            "✅ مدفوعة", "0", "#28a745", "الفواتير المدفوعة بالكامل"
        )
        layout.addWidget(paid_card)
        
        # الفواتير المعلقة
        pending_card = self.create_stat_card(
            "⏳ معلقة", "0", "#ffc107", "الفواتير غير المدفوعة"
        )
        layout.addWidget(pending_card)
        
        # الفواتير المتأخرة
        overdue_card = self.create_stat_card(
            "⚠️ متأخرة", "0", "#dc3545", "الفواتير المتأخرة"
        )
        layout.addWidget(overdue_card)
        
        # إجمالي المبالغ
        amount_card = self.create_stat_card(
            "💰 إجمالي المبالغ", "0 ريال", "#6f42c1", "إجمالي قيمة الفواتير"
        )
        layout.addWidget(amount_card)
        
        self.setLayout(layout)
        
        # حفظ مراجع البطاقات للتحديث
        self.stat_cards = {
            'total': total_card,
            'paid': paid_card,
            'pending': pending_card,
            'overdue': overdue_card,
            'amount': amount_card
        }
    
    def create_stat_card(self, title, value, color, description):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-left: 4px solid {color};
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }}
            QFrame:hover {{
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }}
        """)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                font-weight: bold;
                color: {color};
                margin-bottom: 5px;
            }}
        """)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 5px;
            }
        """)
        
        # الوصف
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #6c757d;
            }
        """)
        desc_label.setWordWrap(True)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addWidget(desc_label)
        
        card.setLayout(layout)
        
        # حفظ مرجع للتسمية للتحديث لاحقاً
        card.value_label = value_label
        
        return card
    
    def load_stats(self):
        """تحميل الإحصائيات"""
        try:
            if self.db_manager:
                # هنا يتم تحميل الإحصائيات من قاعدة البيانات
                # مؤقتاً سنستخدم قيم وهمية
                stats = {
                    'total': 150,
                    'paid': 120,
                    'pending': 25,
                    'overdue': 5,
                    'amount': 2500000
                }
                
                self.update_stats(stats)
        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")
    
    def update_stats(self, stats):
        """تحديث الإحصائيات"""
        self.stat_cards['total'].value_label.setText(str(stats.get('total', 0)))
        self.stat_cards['paid'].value_label.setText(str(stats.get('paid', 0)))
        self.stat_cards['pending'].value_label.setText(str(stats.get('pending', 0)))
        self.stat_cards['overdue'].value_label.setText(str(stats.get('overdue', 0)))
        self.stat_cards['amount'].value_label.setText(f"{stats.get('amount', 0):,.0f} ريال")

class InvoiceSearchWidget(QWidget):
    """ودجت البحث في الفواتير"""
    
    search_requested = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # مجموعة البحث السريع
        quick_search_group = QGroupBox("🔍 البحث السريع")
        quick_search_layout = QHBoxLayout()
        
        # حقل البحث
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث برقم الفاتورة أو اسم العميل...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #007bff;
            }
        """)
        
        # زر البحث
        search_btn = QPushButton("🔍 بحث")
        search_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        
        # زر مسح البحث
        clear_btn = QPushButton("🗑️ مسح")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        
        quick_search_layout.addWidget(self.search_edit)
        quick_search_layout.addWidget(search_btn)
        quick_search_layout.addWidget(clear_btn)
        
        quick_search_group.setLayout(quick_search_layout)
        layout.addWidget(quick_search_group)
        
        # مجموعة البحث المتقدم
        advanced_search_group = QGroupBox("🔧 البحث المتقدم")
        advanced_search_layout = QHBoxLayout()
        
        # فلتر الحالة
        status_layout = QVBoxLayout()
        status_layout.addWidget(QLabel("الحالة:"))
        self.status_combo = QComboBox()
        self.status_combo.addItem("جميع الحالات", "")
        for status, info in InvoiceStatusManager.STATUSES.items():
            self.status_combo.addItem(f"{info['icon']} {info['name']}", status)
        status_layout.addWidget(self.status_combo)
        advanced_search_layout.addLayout(status_layout)
        
        # فلتر التاريخ من
        date_from_layout = QVBoxLayout()
        date_from_layout.addWidget(QLabel("من تاريخ:"))
        self.date_from_edit = QDateEdit()
        self.date_from_edit.setDate(QDate.currentDate().addDays(-30))
        self.date_from_edit.setCalendarPopup(True)
        date_from_layout.addWidget(self.date_from_edit)
        advanced_search_layout.addLayout(date_from_layout)
        
        # فلتر التاريخ إلى
        date_to_layout = QVBoxLayout()
        date_to_layout.addWidget(QLabel("إلى تاريخ:"))
        self.date_to_edit = QDateEdit()
        self.date_to_edit.setDate(QDate.currentDate())
        self.date_to_edit.setCalendarPopup(True)
        date_to_layout.addWidget(self.date_to_edit)
        advanced_search_layout.addLayout(date_to_layout)
        
        # فلتر المبلغ من
        amount_from_layout = QVBoxLayout()
        amount_from_layout.addWidget(QLabel("المبلغ من:"))
        self.amount_from_spin = QDoubleSpinBox()
        self.amount_from_spin.setRange(0, 999999999)
        self.amount_from_spin.setSuffix(" ريال")
        amount_from_layout.addWidget(self.amount_from_spin)
        advanced_search_layout.addLayout(amount_from_layout)
        
        # فلتر المبلغ إلى
        amount_to_layout = QVBoxLayout()
        amount_to_layout.addWidget(QLabel("المبلغ إلى:"))
        self.amount_to_spin = QDoubleSpinBox()
        self.amount_to_spin.setRange(0, 999999999)
        self.amount_to_spin.setValue(999999999)
        self.amount_to_spin.setSuffix(" ريال")
        amount_to_layout.addWidget(self.amount_to_spin)
        advanced_search_layout.addLayout(amount_to_layout)
        
        # زر البحث المتقدم
        advanced_search_btn = QPushButton("🔍 بحث متقدم")
        advanced_search_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        advanced_search_layout.addWidget(advanced_search_btn)
        
        advanced_search_group.setLayout(advanced_search_layout)
        layout.addWidget(advanced_search_group)
        
        self.setLayout(layout)
        
        # حفظ مراجع الأزرار
        self.search_btn = search_btn
        self.clear_btn = clear_btn
        self.advanced_search_btn = advanced_search_btn
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.search_btn.clicked.connect(self.perform_search)
        self.clear_btn.clicked.connect(self.clear_search)
        self.advanced_search_btn.clicked.connect(self.perform_advanced_search)
        self.search_edit.returnPressed.connect(self.perform_search)
    
    def perform_search(self):
        """تنفيذ البحث السريع"""
        search_criteria = {
            'search_text': self.search_edit.text().strip()
        }
        self.search_requested.emit(search_criteria)
    
    def perform_advanced_search(self):
        """تنفيذ البحث المتقدم"""
        search_criteria = {
            'search_text': self.search_edit.text().strip(),
            'status': self.status_combo.currentData(),
            'date_from': self.date_from_edit.date().toString('yyyy-MM-dd'),
            'date_to': self.date_to_edit.date().toString('yyyy-MM-dd'),
            'amount_from': self.amount_from_spin.value() if self.amount_from_spin.value() > 0 else None,
            'amount_to': self.amount_to_spin.value() if self.amount_to_spin.value() < 999999999 else None
        }
        self.search_requested.emit(search_criteria)
    
    def clear_search(self):
        """مسح البحث"""
        self.search_edit.clear()
        self.status_combo.setCurrentIndex(0)
        self.date_from_edit.setDate(QDate.currentDate().addDays(-30))
        self.date_to_edit.setDate(QDate.currentDate())
        self.amount_from_spin.setValue(0)
        self.amount_to_spin.setValue(999999999)
        
        # إرسال بحث فارغ لإعادة تحميل جميع الفواتير
        self.search_requested.emit({})

class InvoiceManagementMainWindow(QMainWindow):
    """النافذة الرئيسية لإدارة الفواتير"""
    
    def __init__(self, db_manager=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.invoices_data = []
        self.setup_ui()
        self.setup_connections()
        self.load_invoices()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("نظام إدارة الفواتير المحسن")
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء القوائم
        self.create_menus()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # الودجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("🧾 نظام إدارة الفواتير المحسن")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # ودجت الإحصائيات
        self.stats_widget = InvoiceStatsWidget(self.db_manager)
        main_layout.addWidget(self.stats_widget)
        
        # المحتوى الرئيسي
        content_splitter = QSplitter(Qt.Vertical)
        
        # ودجت البحث
        self.search_widget = InvoiceSearchWidget()
        content_splitter.addWidget(self.search_widget)
        
        # جدول الفواتير
        self.invoices_table = self.create_invoices_table()
        content_splitter.addWidget(self.invoices_table)
        
        content_splitter.setSizes([200, 600])
        main_layout.addWidget(content_splitter)
        
        central_widget.setLayout(main_layout)
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_menus(self):
        """إنشاء القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("📁 ملف")
        
        new_action = QAction("➕ فاتورة جديدة", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_invoice)
        file_menu.addAction(new_action)
        
        file_menu.addSeparator()
        
        import_action = QAction("📥 استيراد", self)
        import_action.triggered.connect(self.import_invoices)
        file_menu.addAction(import_action)
        
        export_action = QAction("📤 تصدير", self)
        export_action.triggered.connect(self.export_invoices)
        file_menu.addAction(export_action)
        
        # قائمة التحرير
        edit_menu = menubar.addMenu("✏️ تحرير")
        
        edit_action = QAction("✏️ تعديل", self)
        edit_action.triggered.connect(self.edit_invoice)
        edit_menu.addAction(edit_action)
        
        duplicate_action = QAction("📋 نسخ", self)
        duplicate_action.triggered.connect(self.duplicate_invoice)
        edit_menu.addAction(duplicate_action)
        
        delete_action = QAction("🗑️ حذف", self)
        delete_action.triggered.connect(self.delete_invoice)
        edit_menu.addAction(delete_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu("👁️ عرض")
        
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_invoices)
        view_menu.addAction(refresh_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu("📊 تقارير")
        
        sales_report_action = QAction("📈 تقرير المبيعات", self)
        sales_report_action.triggered.connect(self.generate_sales_report)
        reports_menu.addAction(sales_report_action)
        
        customer_report_action = QAction("👥 تقرير العملاء", self)
        customer_report_action.triggered.connect(self.generate_customer_report)
        reports_menu.addAction(customer_report_action)

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # زر فاتورة جديدة
        new_action = QAction("➕ جديدة", self)
        new_action.setToolTip("إنشاء فاتورة جديدة")
        new_action.triggered.connect(self.new_invoice)
        toolbar.addAction(new_action)

        toolbar.addSeparator()

        # زر تعديل
        edit_action = QAction("✏️ تعديل", self)
        edit_action.setToolTip("تعديل الفاتورة المحددة")
        edit_action.triggered.connect(self.edit_invoice)
        toolbar.addAction(edit_action)

        # زر حذف
        delete_action = QAction("🗑️ حذف", self)
        delete_action.setToolTip("حذف الفاتورة المحددة")
        delete_action.triggered.connect(self.delete_invoice)
        toolbar.addAction(delete_action)

        toolbar.addSeparator()

        # زر طباعة
        print_action = QAction("🖨️ طباعة", self)
        print_action.setToolTip("طباعة الفاتورة المحددة")
        print_action.triggered.connect(self.print_invoice)
        toolbar.addAction(print_action)

        # زر تصدير
        export_action = QAction("📤 تصدير", self)
        export_action.setToolTip("تصدير الفواتير")
        export_action.triggered.connect(self.export_invoices)
        toolbar.addAction(export_action)

        toolbar.addSeparator()

        # زر تحديث
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setToolTip("تحديث قائمة الفواتير")
        refresh_action.triggered.connect(self.refresh_invoices)
        toolbar.addAction(refresh_action)

    def create_invoices_table(self):
        """إنشاء جدول الفواتير"""
        table = QTableWidget()
        table.setColumnCount(9)
        table.setHorizontalHeaderLabels([
            "رقم الفاتورة", "العميل", "التاريخ", "تاريخ الاستحقاق",
            "المبلغ", "المدفوع", "المتبقي", "الحالة", "الإجراءات"
        ])

        # تنسيق الجدول
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # العميل

        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #343a40;
                color: white;
                padding: 12px;
                font-weight: bold;
                border: none;
            }
        """)

        # إعداد الأحداث
        table.doubleClicked.connect(self.edit_invoice)
        table.setContextMenuPolicy(Qt.CustomContextMenu)
        table.customContextMenuRequested.connect(self.show_context_menu)

        return table

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()

        # تسمية الحالة
        self.status_label = QLabel("جاهز")
        status_bar.addWidget(self.status_label)

        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_bar.addPermanentWidget(self.progress_bar)

        # عدد الفواتير
        self.count_label = QLabel("0 فاتورة")
        status_bar.addPermanentWidget(self.count_label)

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.search_widget.search_requested.connect(self.search_invoices)

    def load_invoices(self, search_criteria=None):
        """تحميل الفواتير"""
        try:
            self.status_label.setText("جاري تحميل الفواتير...")
            self.progress_bar.setVisible(True)

            if self.db_manager and search_criteria is not None:
                # تنفيذ البحث
                query, params = InvoiceSearchEngine.build_search_query(search_criteria)
                invoices = self.db_manager.execute_query(query, params)
            else:
                # تحميل جميع الفواتير (محاكاة)
                invoices = self.get_sample_invoices()

            self.populate_invoices_table(invoices)
            self.invoices_data = invoices

            # تحديث الإحصائيات
            self.stats_widget.load_stats()

            self.status_label.setText("تم تحميل الفواتير بنجاح")
            self.count_label.setText(f"{len(invoices)} فاتورة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الفواتير:\n{str(e)}")
            self.status_label.setText("خطأ في التحميل")
        finally:
            self.progress_bar.setVisible(False)

    def get_sample_invoices(self):
        """الحصول على فواتير تجريبية"""
        sample_invoices = []

        for i in range(1, 21):
            invoice = {
                'id': i,
                'invoice_number': f"INV-2024-{i:04d}",
                'customer_name': f"عميل رقم {i}",
                'invoice_date': (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d'),
                'due_date': (datetime.now() + timedelta(days=30-i)).strftime('%Y-%m-%d'),
                'total_amount': 1000 + (i * 100),
                'paid_amount': 0 if i % 3 == 0 else (1000 + (i * 100)) if i % 2 == 0 else (500 + (i * 50)),
                'status': 'paid' if i % 2 == 0 else ('overdue' if i % 5 == 0 else 'sent'),
                'payment_method': 'نقدي' if i % 2 == 0 else 'آجل',
                'currency': 'ريال يمني'
            }

            # حساب المتبقي
            invoice['remaining_amount'] = invoice['total_amount'] - invoice['paid_amount']

            sample_invoices.append(invoice)

        return sample_invoices

    def populate_invoices_table(self, invoices):
        """ملء جدول الفواتير"""
        self.invoices_table.setRowCount(len(invoices))

        for row, invoice in enumerate(invoices):
            # رقم الفاتورة
            self.invoices_table.setItem(row, 0, QTableWidgetItem(invoice.get('invoice_number', '')))

            # العميل
            self.invoices_table.setItem(row, 1, QTableWidgetItem(invoice.get('customer_name', '')))

            # التاريخ
            self.invoices_table.setItem(row, 2, QTableWidgetItem(invoice.get('invoice_date', '')))

            # تاريخ الاستحقاق
            self.invoices_table.setItem(row, 3, QTableWidgetItem(invoice.get('due_date', '')))

            # المبلغ
            amount_item = QTableWidgetItem(f"{invoice.get('total_amount', 0):,.2f}")
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.invoices_table.setItem(row, 4, amount_item)

            # المدفوع
            paid_item = QTableWidgetItem(f"{invoice.get('paid_amount', 0):,.2f}")
            paid_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.invoices_table.setItem(row, 5, paid_item)

            # المتبقي
            remaining_item = QTableWidgetItem(f"{invoice.get('remaining_amount', 0):,.2f}")
            remaining_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.invoices_table.setItem(row, 6, remaining_item)

            # الحالة
            status = invoice.get('status', 'draft')
            status_info = InvoiceStatusManager.get_status_info(status)
            status_item = QTableWidgetItem(f"{status_info['icon']} {status_info['name']}")
            status_item.setBackground(QColor(status_info['color']))
            status_item.setForeground(QColor('white'))
            self.invoices_table.setItem(row, 7, status_item)

            # الإجراءات
            actions_widget = self.create_actions_widget(invoice)
            self.invoices_table.setCellWidget(row, 8, actions_widget)

    def create_actions_widget(self, invoice):
        """إنشاء ودجت الإجراءات"""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 2, 5, 2)

        # زر العرض
        view_btn = QPushButton("👁️")
        view_btn.setToolTip("عرض الفاتورة")
        view_btn.setMaximumSize(30, 25)
        view_btn.clicked.connect(lambda: self.view_invoice(invoice))

        # زر التعديل
        edit_btn = QPushButton("✏️")
        edit_btn.setToolTip("تعديل الفاتورة")
        edit_btn.setMaximumSize(30, 25)
        edit_btn.clicked.connect(lambda: self.edit_invoice_by_data(invoice))

        # زر الطباعة
        print_btn = QPushButton("🖨️")
        print_btn.setToolTip("طباعة الفاتورة")
        print_btn.setMaximumSize(30, 25)
        print_btn.clicked.connect(lambda: self.print_invoice_by_data(invoice))

        layout.addWidget(view_btn)
        layout.addWidget(edit_btn)
        layout.addWidget(print_btn)
        layout.addStretch()

        widget.setLayout(layout)
        return widget

    def show_context_menu(self, position):
        """عرض القائمة السياقية"""
        if self.invoices_table.itemAt(position):
            menu = QMenu(self)

            view_action = menu.addAction("👁️ عرض")
            edit_action = menu.addAction("✏️ تعديل")
            duplicate_action = menu.addAction("📋 نسخ")
            menu.addSeparator()
            print_action = menu.addAction("🖨️ طباعة")
            export_action = menu.addAction("📤 تصدير")
            menu.addSeparator()
            delete_action = menu.addAction("🗑️ حذف")

            # ربط الإجراءات
            view_action.triggered.connect(self.view_invoice)
            edit_action.triggered.connect(self.edit_invoice)
            duplicate_action.triggered.connect(self.duplicate_invoice)
            print_action.triggered.connect(self.print_invoice)
            export_action.triggered.connect(self.export_selected_invoice)
            delete_action.triggered.connect(self.delete_invoice)

            menu.exec_(self.invoices_table.mapToGlobal(position))

    def search_invoices(self, search_criteria):
        """البحث في الفواتير"""
        self.load_invoices(search_criteria)

    def refresh_invoices(self):
        """تحديث الفواتير"""
        self.load_invoices()

    def new_invoice(self):
        """إنشاء فاتورة جديدة"""
        dialog = EnhancedInvoiceDialog(self.db_manager, parent=self)
        dialog.invoice_saved.connect(self.on_invoice_saved)
        dialog.exec_()

    def edit_invoice(self):
        """تعديل الفاتورة المحددة"""
        current_row = self.invoices_table.currentRow()
        if current_row >= 0 and current_row < len(self.invoices_data):
            invoice_data = self.invoices_data[current_row]
            self.edit_invoice_by_data(invoice_data)
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة للتعديل")

    def edit_invoice_by_data(self, invoice_data):
        """تعديل فاتورة بالبيانات"""
        dialog = EnhancedInvoiceDialog(self.db_manager, invoice_data, parent=self)
        dialog.invoice_saved.connect(self.on_invoice_saved)
        dialog.exec_()

    def view_invoice(self, invoice_data=None):
        """عرض الفاتورة"""
        if not invoice_data:
            current_row = self.invoices_table.currentRow()
            if current_row >= 0 and current_row < len(self.invoices_data):
                invoice_data = self.invoices_data[current_row]
            else:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة للعرض")
                return

        # هنا يتم عرض الفاتورة
        QMessageBox.information(self, "عرض الفاتورة",
            f"عرض الفاتورة رقم: {invoice_data.get('invoice_number', '')}")

    def duplicate_invoice(self):
        """نسخ الفاتورة"""
        current_row = self.invoices_table.currentRow()
        if current_row >= 0 and current_row < len(self.invoices_data):
            invoice_data = self.invoices_data[current_row].copy()
            # إزالة المعرف ورقم الفاتورة لإنشاء نسخة جديدة
            invoice_data.pop('id', None)
            invoice_data.pop('invoice_number', None)

            dialog = EnhancedInvoiceDialog(self.db_manager, invoice_data, parent=self)
            dialog.invoice_saved.connect(self.on_invoice_saved)
            dialog.exec_()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة للنسخ")

    def delete_invoice(self):
        """حذف الفاتورة"""
        current_row = self.invoices_table.currentRow()
        if current_row >= 0 and current_row < len(self.invoices_data):
            invoice_data = self.invoices_data[current_row]

            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف الفاتورة رقم {invoice_data.get('invoice_number', '')}؟\n"
                "لا يمكن التراجع عن هذا الإجراء.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # هنا يتم حذف الفاتورة من قاعدة البيانات
                QMessageBox.information(self, "تم الحذف", "تم حذف الفاتورة بنجاح")
                self.refresh_invoices()
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة للحذف")

    def print_invoice(self):
        """طباعة الفاتورة"""
        current_row = self.invoices_table.currentRow()
        if current_row >= 0 and current_row < len(self.invoices_data):
            invoice_data = self.invoices_data[current_row]
            self.print_invoice_by_data(invoice_data)
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة للطباعة")

    def print_invoice_by_data(self, invoice_data):
        """طباعة فاتورة بالبيانات"""
        QMessageBox.information(self, "طباعة",
            f"طباعة الفاتورة رقم: {invoice_data.get('invoice_number', '')}")

    def export_invoices(self):
        """تصدير الفواتير"""
        QMessageBox.information(self, "تصدير", "سيتم تطوير وظيفة التصدير")

    def export_selected_invoice(self):
        """تصدير الفاتورة المحددة"""
        current_row = self.invoices_table.currentRow()
        if current_row >= 0 and current_row < len(self.invoices_data):
            invoice_data = self.invoices_data[current_row]
            QMessageBox.information(self, "تصدير",
                f"تصدير الفاتورة رقم: {invoice_data.get('invoice_number', '')}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فاتورة للتصدير")

    def import_invoices(self):
        """استيراد الفواتير"""
        QMessageBox.information(self, "استيراد", "سيتم تطوير وظيفة الاستيراد")

    def generate_sales_report(self):
        """إنشاء تقرير المبيعات"""
        QMessageBox.information(self, "تقرير المبيعات", "سيتم تطوير تقرير المبيعات")

    def generate_customer_report(self):
        """إنشاء تقرير العملاء"""
        QMessageBox.information(self, "تقرير العملاء", "سيتم تطوير تقرير العملاء")

    def on_invoice_saved(self, invoice_data):
        """عند حفظ الفاتورة"""
        self.refresh_invoices()
        self.status_label.setText(f"تم حفظ الفاتورة رقم {invoice_data.get('invoice_number', '')}")


def main():
    """تشغيل النظام"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = InvoiceManagementMainWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
