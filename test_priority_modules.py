#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الوحدات ذات الأولوية القصوى
Priority Modules Test
"""

import sys
import os
from datetime import datetime

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_journal_entries():
    """اختبار وحدة القيود اليومية"""
    print("📝 اختبار وحدة القيود اليومية...")

    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt

        # إنشاء التطبيق إذا لم يكن موجوداً
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)

        from modules.journal_entries_module import JournalEntriesWidget
        from database.sqlite_manager import SQLiteManager

        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager("accounting_system.db")

        # اختبار إنشاء الويدجت
        journal_widget = JournalEntriesWidget(db_manager)
        print("   ✅ تم إنشاء ويدجت القيود اليومية بنجاح")
        
        # اختبار الوظائف الأساسية
        if hasattr(journal_widget, 'load_journal_entries'):
            print("   ✅ دالة تحميل القيود موجودة")
        
        if hasattr(journal_widget, 'add_journal_entry'):
            print("   ✅ دالة إضافة قيد موجودة")
        
        if hasattr(journal_widget, 'entries_table'):
            print("   ✅ جدول القيود موجود")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار القيود اليومية: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_customers_suppliers():
    """اختبار وحدة العملاء والموردين"""
    print("\n👥 اختبار وحدة العملاء والموردين...")

    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt

        # إنشاء التطبيق إذا لم يكن موجوداً
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)

        from modules.customers_suppliers_module import CustomersSupplierModule, CustomerSupplierDialog
        from database.sqlite_manager import SQLiteManager

        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إنشاء الويدجت
        customers_widget = CustomersSupplierModule(db_manager)
        print("   ✅ تم إنشاء ويدجت العملاء والموردين بنجاح")
        
        # اختبار الحوار
        dialog = CustomerSupplierDialog(db_manager)
        print("   ✅ تم إنشاء حوار العميل/المورد بنجاح")
        
        # اختبار الوظائف الأساسية
        if hasattr(customers_widget, 'load_customers_suppliers'):
            print("   ✅ دالة تحميل العملاء والموردين موجودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار العملاء والموردين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_accounts_management():
    """اختبار وحدة إدارة الحسابات"""
    print("\n📋 اختبار وحدة إدارة الحسابات...")

    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt

        # إنشاء التطبيق إذا لم يكن موجوداً
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)

        from modules.accounts_module import AccountsManagementWidget
        from database.sqlite_manager import SQLiteManager

        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إنشاء الويدجت
        accounts_widget = AccountsManagementWidget(db_manager)
        print("   ✅ تم إنشاء ويدجت إدارة الحسابات بنجاح")
        
        # اختبار الوظائف الأساسية
        if hasattr(accounts_widget, 'load_accounts'):
            print("   ✅ دالة تحميل الحسابات موجودة")
        
        if hasattr(accounts_widget, 'accounts_table'):
            print("   ✅ جدول الحسابات موجود")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار إدارة الحسابات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_system_integration():
    """اختبار تكامل الوحدات مع النظام الرئيسي"""
    print("\n🔗 اختبار تكامل الوحدات مع النظام الرئيسي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # اختبار النظام الرئيسي
        import main_system
        from main_system import MainSystemWindow
        from database.sqlite_manager import SQLiteManager
        
        # إنشاء النافذة الرئيسية (بدون معامل)
        main_window = MainSystemWindow()
        print("   ✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار الوظائف المفعلة
        if hasattr(main_window, 'open_journal_entries'):
            print("   ✅ دالة فتح القيود اليومية موجودة")
        
        if hasattr(main_window, 'open_customers'):
            print("   ✅ دالة فتح العملاء موجودة")
        
        if hasattr(main_window, 'open_chart_of_accounts'):
            print("   ✅ دالة فتح دليل الحسابات موجودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التكامل: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_structure():
    """اختبار هيكل قاعدة البيانات للوحدات الجديدة"""
    print("\n🗄️ اختبار هيكل قاعدة البيانات...")
    
    try:
        import sqlite3
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect("accounting_system.db")
        cursor = conn.cursor()
        
        # فحص الجداول المطلوبة
        required_tables = [
            'journal_entries',
            'journal_entry_details', 
            'customers',
            'suppliers',
            'chart_of_accounts'
        ]
        
        existing_tables = []
        for table in required_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                existing_tables.append(table)
                print(f"   ✅ جدول {table} موجود")
            else:
                print(f"   ⚠️ جدول {table} غير موجود")
        
        # إحصائيات
        print(f"\n📊 إحصائيات الجداول:")
        for table in existing_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   📋 {table}: {count} سجل")
        
        conn.close()
        return len(existing_tables) >= 3  # على الأقل 3 جداول مطلوبة
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_ui_integration():
    """اختبار تكامل واجهات المستخدم"""
    print("\n🖥️ اختبار تكامل واجهات المستخدم...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # اختبار إنشاء الوحدات
        from database.sqlite_manager import SQLiteManager
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار القيود اليومية
        from modules.journal_entries_module import JournalEntriesWidget
        journal_widget = JournalEntriesWidget(db_manager)
        print("   ✅ واجهة القيود اليومية تعمل")
        
        # اختبار العملاء والموردين
        from modules.customers_suppliers_module import CustomersSupplierModule
        customers_widget = CustomersSupplierModule(db_manager)
        print("   ✅ واجهة العملاء والموردين تعمل")
        
        # اختبار الحسابات
        from modules.accounts_module import AccountsManagementWidget
        accounts_widget = AccountsManagementWidget(db_manager)
        print("   ✅ واجهة إدارة الحسابات تعمل")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار واجهات المستخدم: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار الوحدات ذات الأولوية القصوى")
    print("=" * 70)
    
    tests = [
        ("القيود اليومية", test_journal_entries),
        ("العملاء والموردين", test_customers_suppliers),
        ("إدارة الحسابات", test_accounts_management),
        ("هيكل قاعدة البيانات", test_database_structure),
        ("تكامل واجهات المستخدم", test_ui_integration),
        ("تكامل النظام الرئيسي", test_main_system_integration),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 جميع الوحدات ذات الأولوية القصوى تعمل بنجاح!")
        print("✅ النظام جاهز للاستخدام مع الوحدات الأساسية")
        
        print("\n🎯 الوحدات المفعلة:")
        print("   📝 القيود اليومية - مفعلة ✅")
        print("   👥 العملاء والموردين - مفعلة ✅")
        print("   📋 دليل الحسابات - مفعل ✅")
        
        print("\n🔧 كيفية الاستخدام:")
        print("   1. شغل النظام: python src/main_system.py")
        print("   2. انقر على 'قيود اليومية العامة' في الشجرة")
        print("   3. انقر على 'إدارة العملاء' أو 'إدارة الموردين'")
        print("   4. انقر على 'الدليل المحاسبي' في شاشات التهيئة")
        
    else:
        print("\n⚠️ بعض الوحدات تحتاج إلى إصلاح")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
