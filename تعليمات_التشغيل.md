# تعليمات تشغيل نظام Onyx ERP
## Onyx ERP System Startup Instructions

---

## 🚀 طرق تشغيل النظام / System Startup Methods

### الطريقة الأولى: استخدام الملف المحسن (الموصى بها)
**Method 1: Using Enhanced File (Recommended)**

```bash
# تشغيل مباشر بـ Python
python start_onyx.py

# أو استخدام ملف الـ batch
start_onyx.bat
```

### الطريقة الثانية: استخدام الملف الأصلي
**Method 2: Using Original File**

```bash
# تشغيل مباشر بـ Python
python run_onyx.py

# أو استخدام ملف الـ batch الأصلي
run_onyx.bat
```

---

## 📋 بيانات تسجيل الدخول الافتراضية
**Default Login Credentials**

| الحقل / Field | القيمة / Value |
|---------------|----------------|
| المستخدم / User | admin |
| كلمة المرور / Password | admin123 |
| السنة المالية / Fiscal Year | 2025 |
| اللغة / Language | عربي |
| الشركة / Company | صقالات ابو سيف للنقل |
| الفرع / Branch | 1 - الفرع الرئيسي |

---

## 🔧 المتطلبات / Requirements

النظام يحتاج إلى المكتبات التالية:
**The system requires the following libraries:**

- PyQt5
- bcrypt
- python-dateutil
- mysql-connector-python

### تثبيت المتطلبات / Installing Requirements

```bash
pip install -r requirements.txt
```

أو تثبيت يدوي:
**Or manual installation:**

```bash
pip install PyQt5 bcrypt python-dateutil mysql-connector-python
```

---

## 🖥️ واجهة المستخدم / User Interface

عند تشغيل النظام بنجاح، ستظهر نافذة تسجيل الدخول مع:
**When the system starts successfully, a login window will appear with:**

- ✅ حقول تسجيل الدخول / Login fields
- ✅ خيارات الشركة والفرع / Company and branch options
- ✅ إعدادات السنة المالية / Fiscal year settings
- ✅ خيارات اللغة / Language options

---

## 🐛 حل المشاكل / Troubleshooting

### مشكلة الترميز / Encoding Issues
إذا ظهرت أخطاء في الترميز:
**If encoding errors appear:**

```bash
chcp 65001
set PYTHONIOENCODING=utf-8
python start_onyx.py
```

### مشكلة عدم ظهور النافذة / Window Not Appearing
تأكد من:
**Make sure:**

- ✅ تثبيت PyQt5 بشكل صحيح / PyQt5 is properly installed
- ✅ وجود بيئة سطح مكتب / Desktop environment is available
- ✅ عدم وجود برامج حماية تمنع التشغيل / No antivirus blocking execution

---

## 📞 الدعم / Support

في حالة وجود مشاكل، يرجى التحقق من:
**In case of issues, please check:**

1. ملفات السجل / Log files
2. رسائل الخطأ في وحدة التحكم / Console error messages
3. إعدادات النظام / System settings

---

## 📝 ملاحظات مهمة / Important Notes

- 🔒 النظام يستخدم قاعدة بيانات SQLite محلية
- 🔒 System uses local SQLite database

- 🌐 يدعم اللغتين العربية والإنجليزية
- 🌐 Supports both Arabic and English languages

- 💾 يتم حفظ البيانات تلقائياً
- 💾 Data is saved automatically

---

**تم إنشاء هذا الدليل في:** 2025-07-31
**This guide was created on:** 2025-07-31
