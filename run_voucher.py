#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع للسند المحدث
Quick Run for Updated Voucher
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont("Tahoma", 11)
    app.setFont(font)
    
    try:
        # قاعدة بيانات وهمية للاختبار
        class MockDBManager:
            def __init__(self):
                self.cursor = type('obj', (object,), {'lastrowid': 1})()
            
            def execute_query(self, query, params=None):
                if "MAX" in query:
                    return [(0,)]
                elif "chart_of_accounts" in query:
                    return [
                        ('111001', 'صندوق المحل الرئيسي', 'أصول متداولة', 1, 3, '111000'),
                        ('112001', 'البنك الأهلي اليمني', 'أصول متداولة', 1, 3, '112000'),
                        ('211001', 'حسابات دائنة', 'خصوم متداولة', 1, 3, '211000'),
                    ]
                return []
            
            def execute_update(self, query, params=None):
                return True
        
        # إنشاء قاعدة البيانات الوهمية
        db_manager = MockDBManager()
        
        # استيراد وإنشاء السند
        from modules.vouchers.voucher_interface import VoucherInterface
        
        # إنشاء السند
        voucher = VoucherInterface(db_manager, "receipt")
        voucher.show()
        
        print("🎉 تم تشغيل السند بنجاح!")
        print("✅ الميزات المحدثة:")
        print("   📏 أزرار أكبر وأوضح (50px ارتفاع)")
        print("   🎨 تصميم محسن مطابق للصورة")
        print("   📝 نصوص واضحة مع الأيقونات")
        print("   🔧 وظائف مربوطة بالكامل")
        print("   📊 جدول 11 عمود محسن")
        print("   👤 بيانات المستخدم مفصلة")
        print("   💰 حسابات تلقائية للإجماليات")
        
        # رسالة ترحيب
        QMessageBox.information(
            None, 
            "مرحباً بك في Onyx Pro ERP",
            "🎉 تم تشغيل السند المحدث بنجاح!\n\n"
            "✅ التحسينات الجديدة:\n"
            "• أزرار أكبر وأوضح\n"
            "• تصميم مطابق للصورة\n"
            "• وظائف محسنة\n"
            "• جدول تفاصيل متقدم\n\n"
            "🔧 جرب الأزرار الجديدة:\n"
            "• 💾 حفظ (Ctrl+S)\n"
            "• 📄 جديد (Ctrl+N)\n"
            "• 🖨️ طباعة (Ctrl+P)\n"
            "• ❓ مساعدة (F1)"
        )
        
        sys.exit(app.exec_())
        
    except ImportError as e:
        QMessageBox.critical(
            None,
            "خطأ في الاستيراد",
            f"فشل في استيراد وحدة السند:\n{str(e)}\n\n"
            "تأكد من وجود ملفات النظام في المجلد الصحيح"
        )
        sys.exit(1)
        
    except Exception as e:
        QMessageBox.critical(
            None,
            "خطأ",
            f"فشل في تشغيل السند:\n{str(e)}\n\n"
            "تفاصيل الخطأ:\n"
            f"النوع: {type(e).__name__}\n"
            f"الرسالة: {str(e)}"
        )
        sys.exit(1)


if __name__ == "__main__":
    main()
