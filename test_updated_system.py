#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام المحدث - البنية الجديدة
Test Updated System - New Structure
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_system_import():
    """اختبار استيراد النظام المحدث"""
    print("🧪 اختبار استيراد النظام المحدث...")
    
    try:
        from src.main_system import MainSystemWindow
        print("✅ تم استيراد النظام المحدث بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في استيراد النظام المحدث: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_menu_structure():
    """اختبار هيكل القائمة الجديد"""
    print("\n🧪 اختبار هيكل القائمة الجديد...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.main_system import MainSystemWindow
        
        # إنشاء تطبيق مؤقت
        app = QApplication([])
        
        # إنشاء النظام الرئيسي
        main_system = MainSystemWindow()
        
        print("✅ تم إنشاء النظام المحدث بنجاح")
        
        # فحص الأقسام الجديدة
        expected_sections = [
            "🔧 تهيئة النظام",
            "⚙️ إدارة النظام", 
            "💰 أنظمة الحسابات",
            "👥 إدارة العملاء والمبيعات",
            "🏪 إدارة الموردين والمشتريات",
            "📊 نظام إدارة المعلومات",
            "🛠️ الأنظمة المساعدة"
        ]
        
        print("\n📋 الأقسام المتوقعة:")
        for i, section in enumerate(expected_sections, 1):
            print(f"   {i}. {section}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار هيكل القائمة: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_subsections():
    """اختبار التبويبات الفرعية"""
    print("\n🧪 اختبار التبويبات الفرعية...")
    
    try:
        expected_subsections = {
            "تهيئة النظام": ["📑 التهيئة العامة", "📥 المدخلات"],
            "إدارة النظام": ["📥 إدارة المستخدمين", "⚙️ صلاحيات المستخدمين", "🔐 خصوصية المستخدمين", "📛 الإقفالات", "💾 النسخ الاحتياطية", "🗃️ إدارة قاعدة البيانات"],
            "أنظمة الحسابات": ["🛠 التهيئة", "📥 المدخلات", "⚙️ العمليات", "📊 التقارير"],
            "إدارة العملاء والمبيعات": ["🛠 التهيئة", "📥 المدخلات", "⚙️ العمليات", "📊 التقارير"],
            "إدارة الموردين والمشتريات": ["🛠 التهيئة", "📥 المدخلات", "⚙️ العمليات", "📊 التقارير"],
            "نظام إدارة المعلومات": ["📊 التقارير الإدارية", "📊 تقارير المخزون", "📊 تقارير المشتريات", "📊 تقارير المبيعات"]
        }
        
        total_subsections = sum(len(subs) for subs in expected_subsections.values())
        
        print(f"✅ إجمالي التبويبات الفرعية المتوقعة: {total_subsections}")
        
        for section, subsections in expected_subsections.items():
            print(f"\n📁 {section}:")
            for sub in subsections:
                print(f"   • {sub}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التبويبات الفرعية: {e}")
        return False

def run_updated_system():
    """تشغيل النظام المحدث"""
    print("\n🚀 تشغيل النظام المحدث...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.main_system import MainSystemWindow
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إنشاء النظام الرئيسي
        main_system = MainSystemWindow()
        main_system.show()
        
        print("✅ تم تشغيل النظام المحدث بنجاح!")
        print("\n🎯 البنية الجديدة:")
        print("   🔧 تهيئة النظام - التهيئة العامة والمدخلات")
        print("   ⚙️ إدارة النظام - المستخدمين والصلاحيات والنسخ الاحتياطية")
        print("   💰 أنظمة الحسابات - التهيئة والمدخلات والعمليات والتقارير")
        print("   👥 إدارة العملاء والمبيعات - شامل جميع عمليات المبيعات")
        print("   🏪 إدارة الموردين والمشتريات - شامل جميع عمليات المشتريات")
        print("   📊 نظام إدارة المعلومات - التقارير المتخصصة")
        print("   🛠️ الأنظمة المساعدة - الأدوات المساعدة")
        
        print("\n✨ الميزات الجديدة:")
        print("   📋 تنظيم أفضل للوحدات حسب الوظيفة")
        print("   🎯 تبويبات فرعية منطقية (التهيئة، المدخلات، العمليات، التقارير)")
        print("   🔍 سهولة العثور على الوظائف المطلوبة")
        print("   📊 فصل التقارير حسب النوع والتخصص")
        print("   🛡️ تنظيم أفضل لإدارة النظام والأمان")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام المحدث: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 70)
    print("🧪 اختبار النظام المحدث - البنية التنظيمية الجديدة")
    print("=" * 70)
    
    tests = [
        ("استيراد النظام", test_system_import),
        ("هيكل القائمة", test_menu_structure),
        ("التبويبات الفرعية", test_subsections),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 نتائج الاختبار: {passed_tests}/{total_tests} اختبار نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("\n📋 البنية الجديدة تم تطبيقها بنجاح:")
        print("   ✅ 6 أقسام رئيسية منظمة")
        print("   ✅ تبويبات فرعية منطقية")
        print("   ✅ تنظيم الوحدات حسب الوظيفة")
        print("   ✅ الحفاظ على الواجهة الأصلية")
        
        print("\n🚀 سيتم الآن تشغيل النظام المحدث...")
        print("=" * 70)
        
        # تشغيل النظام المحدث
        run_updated_system()
    else:
        print("⚠️ بعض الاختبارات فشلت - يرجى مراجعة الأخطاء أعلاه")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
