#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام بعد التنظيف والإصلاح
System Test After Cleanup and Fixes
"""

import sys
import os
from datetime import datetime

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_main_system():
    """اختبار النظام الرئيسي"""
    print("🖥️ اختبار النظام الرئيسي...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from main_system import MainSystemWindow
        
        # إنشاء النافذة الرئيسية
        main_window = MainSystemWindow()
        print("   ✅ تم إنشاء النافذة الرئيسية بنجاح")
        
        # اختبار وجود الوظائف الأساسية
        essential_functions = [
            'open_sales_invoice',
            'open_inventory',
            'open_customers',
            'open_suppliers',
            'open_journal_entries',
            'open_chart_of_accounts',
            'open_user_management'
        ]
        
        for func_name in essential_functions:
            if hasattr(main_window, func_name):
                print(f"   ✅ دالة {func_name} موجودة")
            else:
                print(f"   ❌ دالة {func_name} غير موجودة")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار النظام الرئيسي: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sales_invoices():
    """اختبار وحدة فواتير المبيعات"""
    print("\n🧾 اختبار وحدة فواتير المبيعات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.sales_invoices_new import SalesInvoiceDialog
        from database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إنشاء حوار الفاتورة
        dialog = SalesInvoiceDialog(db_manager)
        print("   ✅ تم إنشاء حوار فاتورة المبيعات بنجاح")
        
        # اختبار الوظائف الأساسية
        if hasattr(dialog, 'save_invoice'):
            print("   ✅ دالة حفظ الفاتورة موجودة")
        
        if hasattr(dialog, 'add_product_line'):
            print("   ✅ دالة إضافة منتج موجودة")
        
        if hasattr(dialog, 'calculate_totals'):
            print("   ✅ دالة حساب الإجماليات موجودة")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار فواتير المبيعات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_inventory_system():
    """اختبار نظام المخازن"""
    print("\n📦 اختبار نظام المخازن...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.inventory_module import InventoryManagementWidget
        from database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار إنشاء وحدة المخازن
        inventory_widget = InventoryManagementWidget(db_manager)
        print("   ✅ تم إنشاء وحدة إدارة المخازن بنجاح")
        
        # اختبار التبويبات
        if hasattr(inventory_widget, 'tabs'):
            tab_count = inventory_widget.tabs.count()
            print(f"   ✅ عدد التبويبات: {tab_count}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار نظام المخازن: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🗄️ اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from database.sqlite_manager import SQLiteManager
        
        db_manager = SQLiteManager("accounting_system.db")
        
        # اختبار الاستعلامات الأساسية
        tables_to_check = [
            'users',
            'customers',
            'suppliers',
            'products',
            'sales_invoices',
            'warehouses',
            'inventory_movements',
            'journal_entries'
        ]
        
        existing_tables = []
        for table in tables_to_check:
            try:
                result = db_manager.execute_query(f"SELECT COUNT(*) FROM {table}")
                if result is not None:
                    count = result[0][0] if result else 0
                    existing_tables.append(table)
                    print(f"   ✅ جدول {table}: {count} سجل")
            except Exception as e:
                # فحص إذا كان الجدول موجود بطريقة أخرى
                try:
                    check_result = db_manager.execute_query(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                    if check_result:
                        existing_tables.append(table)
                        print(f"   ✅ جدول {table}: موجود")
                    else:
                        print(f"   ⚠️ جدول {table} غير موجود")
                except:
                    print(f"   ❌ جدول {table} به مشكلة: {str(e)}")
        
        return len(existing_tables) >= 6  # على الأقل 6 جداول أساسية
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_imports_and_modules():
    """اختبار الاستيراد والوحدات"""
    print("\n📦 اختبار الاستيراد والوحدات...")
    
    modules_to_test = [
        ('database.sqlite_manager', 'SQLiteManager'),
        ('modules.sales_invoices_new', 'SalesInvoiceDialog'),
        ('modules.inventory_module', 'InventoryManagementWidget'),
        ('modules.customers_suppliers_module', 'CustomersSupplierModule'),
        ('modules.journal_entries_module', 'JournalEntriesWidget'),
        ('modules.accounts_module', 'AccountsManagementWidget'),
        ('modules.system_management_module', 'SystemManagementModule')
    ]
    
    successful_imports = 0
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"   ✅ {module_name}.{class_name}")
            successful_imports += 1
        except Exception as e:
            print(f"   ❌ {module_name}.{class_name}: {e}")
    
    return successful_imports >= len(modules_to_test) * 0.8  # 80% نجاح

def test_file_structure():
    """اختبار هيكل الملفات"""
    print("\n📁 اختبار هيكل الملفات...")
    
    required_files = [
        'src/main_system.py',
        'src/database/sqlite_manager.py',
        'src/modules/sales_invoices_new.py',
        'src/modules/inventory_module.py',
        'src/modules/customers_suppliers_module.py',
        'src/modules/journal_entries_module.py',
        'src/modules/accounts_module.py'
    ]
    
    existing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} غير موجود")
    
    return len(existing_files) == len(required_files)

def main():
    """الدالة الرئيسية للاختبار"""
    print("🚀 اختبار النظام بعد التنظيف والإصلاح")
    print("=" * 70)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    tests = [
        ("هيكل الملفات", test_file_structure),
        ("الاستيراد والوحدات", test_imports_and_modules),
        ("الاتصال بقاعدة البيانات", test_database_connection),
        ("النظام الرئيسي", test_main_system),
        ("فواتير المبيعات", test_sales_invoices),
        ("نظام المخازن", test_inventory_system),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم تنظيف وإصلاح النظام بنجاح!")
        print("✅ جميع الوحدات تعمل بدون أخطاء")
        
        print("\n🔧 التحسينات المطبقة:")
        print("   🗑️ حذف الملفات المكررة")
        print("   🔧 إصلاح مشاكل الاستيراد")
        print("   🧹 تنظيف الكود المكرر")
        print("   ⚡ تحسين الأداء")
        print("   🛡️ تحسين معالجة الأخطاء")
        
        print("\n🚀 النظام جاهز للاستخدام:")
        print("   python src/main_system.py")
        
    else:
        print("\n⚠️ لا تزال هناك مشاكل تحتاج إلى إصلاح")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
