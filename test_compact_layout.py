#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التخطيط المضغوط - تفاصيل السند تأخذ المساحة الأكبر
Compact Layout Test - Voucher Details Take Most Space
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QPushButton, QWidget, QLabel, QMessageBox, QTextEdit
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class CompactLayoutTestWindow(QMainWindow):
    """نافذة اختبار التخطيط المضغوط"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_mock_db()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار التخطيط المضغوط - تفاصيل السند تأخذ المساحة الأكبر")
        self.setGeometry(200, 200, 1000, 700)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QMainWindow {
                background-color: #fdf2f8;
                font-family: "Tahoma", "Arial", sans-serif;
            }
            QPushButton {
                background-color: #e91e63;
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                margin: 10px;
                min-width: 300px;
            }
            QPushButton:hover {
                background-color: #c2185b;
            }
            QPushButton:pressed {
                background-color: #ad1457;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 14px;
                color: #2c3e50;
                padding: 10px;
            }
            QTextEdit {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 12px;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("📊 اختبار التخطيط المضغوط - تفاصيل السند تأخذ المساحة الأكبر")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                color: #e91e63;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 10px;
                padding: 15px;
                margin: 15px;
                text-align: center;
            }
        """)
        layout.addWidget(title_label)
        
        # أزرار الاختبار
        buttons_layout = QVBoxLayout()
        
        # اختبار سند القبض مضغوط
        receipt_btn = QPushButton("📄 سند قبض - تخطيط مضغوط")
        receipt_btn.clicked.connect(self.test_receipt_compact)
        buttons_layout.addWidget(receipt_btn)
        
        # اختبار سند الصرف مضغوط
        payment_btn = QPushButton("📄 سند صرف - تخطيط مضغوط")
        payment_btn.clicked.connect(self.test_payment_compact)
        buttons_layout.addWidget(payment_btn)
        
        # اختبار مع بيانات كثيرة
        many_items_btn = QPushButton("📋 اختبار مع بنود كثيرة")
        many_items_btn.clicked.connect(self.test_many_items)
        buttons_layout.addWidget(many_items_btn)
        
        layout.addLayout(buttons_layout)
        
        # معلومات التحسينات
        info = QTextEdit()
        info.setReadOnly(True)
        info.setMaximumHeight(300)
        info.setHtml("""
        <h3 style="color: #e91e63;">📊 التحسينات المطبقة على التخطيط:</h3>
        
        <h4 style="color: #27ae60;">✅ تصغير بيانات السند:</h4>
        <ul>
            <li><strong>ارتفاع أقصى 120px:</strong> بدلاً من المساحة الكبيرة</li>
            <li><strong>صفين فقط:</strong> البيانات الأساسية والثانوية</li>
            <li><strong>البيان كـ QLineEdit:</strong> بدلاً من QTextEdit الكبير</li>
            <li><strong>إخفاء الحقول الأقل أهمية:</strong> العملة، سعر الصرف، المرجع، الحالة</li>
            <li><strong>مسافات مقللة:</strong> بين العناصر</li>
        </ul>
        
        <h4 style="color: #3498db;">💰 تصغير قسم الإجماليات:</h4>
        <ul>
            <li><strong>ارتفاع أقصى 80px:</strong> بدلاً من المساحة الكبيرة</li>
            <li><strong>تخطيط أفقي:</strong> بدلاً من الشبكة</li>
            <li><strong>عناصر مضغوطة:</strong> حجم أصغر للليبلات</li>
            <li><strong>فواصل بصرية:</strong> لتنظيم المعلومات</li>
        </ul>
        
        <h4 style="color: #9b59b6;">📱 تصغير شريط الحالة:</h4>
        <ul>
            <li><strong>ارتفاع أقصى 35px:</strong> مضغوط جداً</li>
            <li><strong>معلومات أساسية فقط:</strong> حالة السند والتاريخ</li>
            <li><strong>خط أصغر:</strong> 10-11px</li>
        </ul>
        
        <h4 style="color: #e74c3c;">📈 النتيجة - مساحة أكبر للجدول:</h4>
        <ul>
            <li>✅ <strong>بيانات السند:</strong> ~120px (بدلاً من ~200px)</li>
            <li>✅ <strong>الإجماليات:</strong> ~80px (بدلاً من ~150px)</li>
            <li>✅ <strong>شريط الحالة:</strong> ~35px (بدلاً من ~50px)</li>
            <li>✅ <strong>شريط الأدوات:</strong> ~50px (كما هو)</li>
            <li>🎯 <strong>المتبقي للجدول:</strong> ~515px من أصل 800px (64%)</li>
        </ul>
        
        <h4 style="color: #f39c12;">🎯 الفوائد المحققة:</h4>
        <ul>
            <li>📊 <strong>مساحة أكبر للجدول:</strong> عرض المزيد من البنود</li>
            <li>⚡ <strong>تركيز على المهم:</strong> تفاصيل السند</li>
            <li>👁️ <strong>رؤية أفضل:</strong> للبيانات الأساسية</li>
            <li>🖱️ <strong>تمرير أقل:</strong> في الجدول</li>
            <li>💻 <strong>استغلال أمثل:</strong> لمساحة الشاشة</li>
        </ul>
        """)
        layout.addWidget(info)
        
        central_widget.setLayout(layout)
    
    def setup_mock_db(self):
        """إعداد قاعدة بيانات وهمية للاختبار"""
        class MockDBManager:
            def __init__(self):
                self.cursor = type('obj', (object,), {'lastrowid': 1})()
            
            def execute_query(self, query, params=None):
                # محاكاة نتائج الاستعلام
                if "MAX" in query:
                    return [(0,)]
                elif "chart_of_accounts" in query:
                    # إرجاع بيانات الحسابات التجريبية
                    return [
                        ('1010001', 'صندوق المركز الرئيسي', 'أصول متداولة', 1, 3, '101000'),
                        ('1020001', 'البنك الأهلي التجاري', 'أصول متداولة', 1, 3, '102000'),
                        ('1020002', 'مصرف الراجحي', 'أصول متداولة', 1, 3, '102000'),
                        ('1030001', 'العملاء المحليين', 'أصول متداولة', 1, 3, '103000'),
                        ('1030002', 'العملاء الأجانب', 'أصول متداولة', 1, 3, '103000'),
                        ('2010001', 'الموردين المحليين', 'خصوم متداولة', 1, 3, '201000'),
                        ('2010002', 'الموردين الأجانب', 'خصوم متداولة', 1, 3, '201000'),
                        ('4010001', 'مبيعات محلية', 'إيرادات', 1, 3, '401000'),
                        ('4010002', 'مبيعات تصدير', 'إيرادات', 1, 3, '401000'),
                        ('5010001', 'مصروفات عمومية', 'مصروفات', 1, 3, '501000'),
                        ('5010002', 'مصروفات إدارية', 'مصروفات', 1, 3, '501000'),
                        ('5010003', 'مصروفات تسويق', 'مصروفات', 1, 3, '501000'),
                    ]
                return []
            
            def execute_update(self, query, params=None):
                return True
        
        self.db_manager = MockDBManager()
    
    def test_receipt_compact(self):
        """اختبار سند القبض مع التخطيط المضغوط"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            receipt_dialog = VoucherInterface(self.db_manager, "receipt", self)
            receipt_dialog.show()
            
            QMessageBox.information(
                self, 
                "سند القبض - تخطيط مضغوط",
                "✅ تم فتح سند القبض مع التخطيط المضغوط\n\n"
                "📊 التحسينات المطبقة:\n"
                "• بيانات السند مضغوطة (120px)\n"
                "• الإجماليات مضغوطة (80px)\n"
                "• شريط الحالة مضغوط (35px)\n"
                "• مساحة أكبر للجدول (~65%)\n\n"
                "🔍 لاحظ:\n"
                "• الجدول يأخذ معظم المساحة\n"
                "• البيانات الأساسية واضحة\n"
                "• سهولة إضافة بنود كثيرة\n"
                "• تمرير أقل في الجدول"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند القبض:\n{str(e)}")
    
    def test_payment_compact(self):
        """اختبار سند الصرف مع التخطيط المضغوط"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            payment_dialog = VoucherInterface(self.db_manager, "payment", self)
            payment_dialog.show()
            
            QMessageBox.information(
                self, 
                "سند الصرف - تخطيط مضغوط",
                "✅ تم فتح سند الصرف مع التخطيط المضغوط\n\n"
                "📊 التحسينات المطبقة:\n"
                "• بيانات السند مضغوطة (120px)\n"
                "• الإجماليات مضغوطة (80px)\n"
                "• شريط الحالة مضغوط (35px)\n"
                "• مساحة أكبر للجدول (~65%)\n\n"
                "🔍 لاحظ:\n"
                "• الجدول يأخذ معظم المساحة\n"
                "• البيانات الأساسية واضحة\n"
                "• سهولة إضافة بنود كثيرة\n"
                "• تمرير أقل في الجدول"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند الصرف:\n{str(e)}")
    
    def test_many_items(self):
        """اختبار مع بنود كثيرة"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            from PyQt5.QtWidgets import QTableWidgetItem
            from PyQt5.QtCore import Qt
            
            # إنشاء سند قبض
            receipt_dialog = VoucherInterface(self.db_manager, "receipt", self)
            
            # تعبئة بيانات أساسية
            receipt_dialog.beneficiary_edit.setText("شركة التجارة الكبرى")
            receipt_dialog.beneficiary_id_edit.setText("C003")
            receipt_dialog.description_edit.setText("سند قبض - تحصيل فواتير متعددة")
            
            # إضافة بنود كثيرة للجدول
            table = receipt_dialog.details_table
            accounts = [
                ("1010001", "صندوق المركز الرئيسي", "مبلغ نقدي", "5,000.00"),
                ("1020001", "البنك الأهلي التجاري", "تحويل بنكي", "10,000.00"),
                ("1020002", "مصرف الراجحي", "شيك", "7,500.00"),
                ("1030001", "العملاء المحليين", "تحصيل فاتورة 001", "3,200.00"),
                ("1030001", "العملاء المحليين", "تحصيل فاتورة 002", "4,800.00"),
                ("1030001", "العملاء المحليين", "تحصيل فاتورة 003", "2,100.00"),
                ("4010001", "مبيعات محلية", "خصم مسموح", "500.00"),
                ("4010002", "مبيعات تصدير", "عمولة تحصيل", "300.00"),
            ]
            
            for i, (code, name, desc, amount) in enumerate(accounts):
                if i > 0:  # الصف الأول موجود بالفعل
                    receipt_dialog.add_detail_row()
                
                table.setItem(i, 1, QTableWidgetItem(code))
                table.setItem(i, 2, QTableWidgetItem(name))
                table.setItem(i, 3, QTableWidgetItem(desc))
                amount_item = QTableWidgetItem(amount)
                amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                table.setItem(i, 4, amount_item)
                table.setItem(i, 5, QTableWidgetItem("المركز الرئيسي"))
            
            # حساب الإجماليات
            receipt_dialog.calculate_totals()
            
            receipt_dialog.show()
            
            QMessageBox.information(
                self, 
                "اختبار بنود كثيرة",
                "✅ تم تعبئة السند ببنود كثيرة\n\n"
                "📋 البيانات المعبأة:\n"
                "• المستفيد: شركة التجارة الكبرى\n"
                "• عدد البنود: 8 بنود\n"
                "• إجمالي المبلغ: 33,400.00 ريال\n\n"
                "📊 لاحظ الفوائد:\n"
                "• الجدول يعرض بنود كثيرة بوضوح\n"
                "• التمرير سهل ومريح\n"
                "• البيانات العلوية مضغوطة\n"
                "• الإجماليات واضحة ومضغوطة\n"
                "• استغلال أمثل لمساحة الشاشة"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء البيانات:\n{str(e)}")


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont("Tahoma", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = CompactLayoutTestWindow()
    window.show()
    
    print("📊 تم تشغيل اختبار التخطيط المضغوط!")
    print("✅ التحسينات المطبقة:")
    print("   📏 بيانات السند: 120px (مضغوطة)")
    print("   💰 الإجماليات: 80px (مضغوطة)")
    print("   📱 شريط الحالة: 35px (مضغوط)")
    print("   📊 الجدول: ~65% من المساحة")
    print("")
    print("🎯 الفوائد:")
    print("   • مساحة أكبر لتفاصيل السند")
    print("   • عرض بنود أكثر بدون تمرير")
    print("   • تركيز على المحتوى المهم")
    print("   • استغلال أمثل للشاشة")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
