#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف إضافية لشاشة إعداد الدليل المحاسبي
Additional Functions for Accounting Guide Setup
"""

from PyQt5.QtWidgets import (QMessageBox, QTableWidgetItem, QGroupBox, QGridLayout,
                             QLabel, QCheckBox, QComboBox, QPushButton, QHBoxLayout,
                             QVBoxLayout, QWidget, QTextEdit, QProgressBar, QListWidget,
                             QListWidgetItem, QTreeWidgetItem)
from PyQt5.QtCore import Qt
from datetime import datetime

def create_settings_tab_function(self):
    """إنشاء تبويب إعدادات الدليل"""
    tab = QWidget()
    layout = QVBoxLayout()
    
    # مجموعة إعدادات العرض
    display_group = QGroupBox("🖥️ إعدادات عرض الدليل")
    display_layout = QGridLayout()
    
    # عرض الأرقام
    self.show_account_numbers = QCheckBox("عرض أرقام الحسابات")
    self.show_account_numbers.setChecked(True)
    display_layout.addWidget(self.show_account_numbers, 0, 0, 1, 2)
    
    # عرض الأرصدة
    self.show_balances = QCheckBox("عرض أرصدة الحسابات")
    self.show_balances.setChecked(True)
    display_layout.addWidget(self.show_balances, 1, 0, 1, 2)
    
    # عرض الحسابات غير النشطة
    self.show_inactive_accounts = QCheckBox("عرض الحسابات غير النشطة")
    display_layout.addWidget(self.show_inactive_accounts, 2, 0, 1, 2)
    
    # ألوان الحسابات
    display_layout.addWidget(QLabel("لون الأصول:"), 3, 0)
    self.assets_color = QComboBox()
    self.assets_color.addItems(["أخضر", "أزرق", "أسود", "رمادي"])
    display_layout.addWidget(self.assets_color, 3, 1)
    
    display_layout.addWidget(QLabel("لون الخصوم:"), 4, 0)
    self.liabilities_color = QComboBox()
    self.liabilities_color.addItems(["أحمر", "برتقالي", "بني", "رمادي"])
    display_layout.addWidget(self.liabilities_color, 4, 1)
    
    display_group.setLayout(display_layout)
    
    # مجموعة إعدادات الأمان
    security_group = QGroupBox("🔒 إعدادات الأمان")
    security_layout = QGridLayout()
    
    # حماية الحسابات الرئيسية
    self.protect_main_accounts = QCheckBox("حماية الحسابات الرئيسية من الحذف")
    self.protect_main_accounts.setChecked(True)
    security_layout.addWidget(self.protect_main_accounts, 0, 0, 1, 2)
    
    # تأكيد الحذف
    self.confirm_deletion = QCheckBox("تأكيد حذف الحسابات")
    self.confirm_deletion.setChecked(True)
    security_layout.addWidget(self.confirm_deletion, 1, 0, 1, 2)
    
    # سجل التغييرات
    self.log_changes = QCheckBox("تسجيل جميع التغييرات")
    self.log_changes.setChecked(True)
    security_layout.addWidget(self.log_changes, 2, 0, 1, 2)
    
    security_group.setLayout(security_layout)
    
    # مجموعة النسخ الاحتياطي
    backup_group = QGroupBox("💾 إعدادات النسخ الاحتياطي")
    backup_layout = QGridLayout()
    
    # نسخ احتياطي تلقائي
    self.auto_backup_guide = QCheckBox("نسخ احتياطي تلقائي للدليل")
    self.auto_backup_guide.setChecked(True)
    backup_layout.addWidget(self.auto_backup_guide, 0, 0, 1, 2)
    
    # تكرار النسخ الاحتياطي
    backup_layout.addWidget(QLabel("تكرار النسخ:"), 1, 0)
    self.backup_frequency_guide = QComboBox()
    self.backup_frequency_guide.addItems(["يومي", "أسبوعي", "شهري", "عند كل تغيير"])
    self.backup_frequency_guide.setCurrentText("عند كل تغيير")
    backup_layout.addWidget(self.backup_frequency_guide, 1, 1)
    
    backup_group.setLayout(backup_layout)
    
    layout.addWidget(display_group)
    layout.addWidget(security_group)
    layout.addWidget(backup_group)
    layout.addStretch()
    
    tab.setLayout(layout)
    return tab

def create_reports_tab_function(self):
    """إنشاء تبويب التقارير والتحليل"""
    tab = QWidget()
    layout = QVBoxLayout()
    
    # مجموعة التقارير المالية
    financial_reports_group = QGroupBox("📊 التقارير المالية")
    financial_reports_layout = QGridLayout()
    
    # أزرار التقارير
    chart_of_accounts_btn = QPushButton("📋 دليل الحسابات الكامل")
    chart_of_accounts_btn.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    chart_of_accounts_btn.clicked.connect(self.generate_chart_of_accounts)
    
    trial_balance_btn = QPushButton("⚖️ ميزان المراجعة")
    trial_balance_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    trial_balance_btn.clicked.connect(self.generate_trial_balance)
    
    account_balances_btn = QPushButton("💰 أرصدة الحسابات")
    account_balances_btn.setStyleSheet("""
        QPushButton {
            background-color: #e67e22;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #d35400;
        }
    """)
    account_balances_btn.clicked.connect(self.generate_account_balances)
    
    financial_reports_layout.addWidget(chart_of_accounts_btn, 0, 0)
    financial_reports_layout.addWidget(trial_balance_btn, 0, 1)
    financial_reports_layout.addWidget(account_balances_btn, 0, 2)
    
    financial_reports_group.setLayout(financial_reports_layout)
    
    # مجموعة تقارير التحليل
    analysis_reports_group = QGroupBox("📈 تقارير التحليل")
    analysis_reports_layout = QGridLayout()
    
    account_activity_btn = QPushButton("📊 نشاط الحسابات")
    account_activity_btn.setStyleSheet("""
        QPushButton {
            background-color: #9b59b6;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #8e44ad;
        }
    """)
    account_activity_btn.clicked.connect(self.generate_account_activity)
    
    unused_accounts_btn = QPushButton("🚫 الحسابات غير المستخدمة")
    unused_accounts_btn.setStyleSheet("""
        QPushButton {
            background-color: #95a5a6;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #7f8c8d;
        }
    """)
    unused_accounts_btn.clicked.connect(self.generate_unused_accounts)
    
    guide_statistics_btn = QPushButton("📊 إحصائيات الدليل")
    guide_statistics_btn.setStyleSheet("""
        QPushButton {
            background-color: #f39c12;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #e67e22;
        }
    """)
    guide_statistics_btn.clicked.connect(self.generate_guide_statistics)
    
    analysis_reports_layout.addWidget(account_activity_btn, 0, 0)
    analysis_reports_layout.addWidget(unused_accounts_btn, 0, 1)
    analysis_reports_layout.addWidget(guide_statistics_btn, 0, 2)
    
    analysis_reports_group.setLayout(analysis_reports_layout)
    
    # منطقة عرض التقارير
    reports_display_group = QGroupBox("📄 عرض التقارير")
    reports_display_layout = QVBoxLayout()
    
    self.reports_display = QTextEdit()
    self.reports_display.setPlaceholderText("ستظهر التقارير هنا...")
    self.reports_display.setStyleSheet("""
        QTextEdit {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    """)
    
    # أزرار التحكم في التقارير
    report_buttons = QHBoxLayout()
    
    export_report_btn = QPushButton("📤 تصدير التقرير")
    export_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #17a2b8;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #138496;
        }
    """)
    export_report_btn.clicked.connect(self.export_report)
    
    print_report_btn = QPushButton("🖨️ طباعة التقرير")
    print_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #6c757d;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5a6268;
        }
    """)
    print_report_btn.clicked.connect(self.print_report)
    
    clear_report_btn = QPushButton("🗑️ مسح التقرير")
    clear_report_btn.setStyleSheet("""
        QPushButton {
            background-color: #dc3545;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #c82333;
        }
    """)
    clear_report_btn.clicked.connect(self.clear_report)
    
    report_buttons.addWidget(export_report_btn)
    report_buttons.addWidget(print_report_btn)
    report_buttons.addWidget(clear_report_btn)
    report_buttons.addStretch()
    
    reports_display_layout.addWidget(self.reports_display)
    reports_display_layout.addLayout(report_buttons)
    reports_display_group.setLayout(reports_display_layout)
    
    layout.addWidget(financial_reports_group)
    layout.addWidget(analysis_reports_group)
    layout.addWidget(reports_display_group)
    
    tab.setLayout(layout)
    return tab

def create_control_buttons_function(self):
    """إنشاء أزرار التحكم"""
    layout = QHBoxLayout()
    
    # زر الحفظ
    save_btn = QPushButton("💾 حفظ الدليل المحاسبي")
    save_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    save_btn.clicked.connect(self.save_accounting_guide)
    
    # زر الإلغاء
    cancel_btn = QPushButton("❌ إلغاء")
    cancel_btn.setStyleSheet("""
        QPushButton {
            background-color: #e74c3c;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #c0392b;
        }
    """)
    cancel_btn.clicked.connect(self.reject)
    
    # زر التحقق من الدليل
    validate_btn = QPushButton("✅ التحقق من الدليل")
    validate_btn.setStyleSheet("""
        QPushButton {
            background-color: #f39c12;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #e67e22;
        }
    """)
    validate_btn.clicked.connect(self.validate_accounting_guide)
    
    # زر المساعدة
    help_btn = QPushButton("❓ المساعدة")
    help_btn.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    help_btn.clicked.connect(self.show_help)
    
    layout.addWidget(help_btn)
    layout.addWidget(validate_btn)
    layout.addStretch()
    layout.addWidget(save_btn)
    layout.addWidget(cancel_btn)
    
    return layout
