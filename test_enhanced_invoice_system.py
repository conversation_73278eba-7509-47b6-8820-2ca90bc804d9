#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الفواتير المحسن
Test Enhanced Invoice System
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_invoice_system_import():
    """اختبار استيراد نظام الفواتير"""
    print("🧪 اختبار استيراد نظام الفواتير المحسن...")
    
    try:
        from src.modules.invoices.enhanced_invoice_system import (
            EnhancedInvoiceSystem, EnhancedInvoiceDialog,
            InvoiceCalculationEngine, InvoiceStatusManager,
            InvoiceSearchEngine, InvoiceValidationEngine
        )
        print("✅ تم استيراد جميع كلاسات نظام الفواتير بنجاح")
        return True
    except ImportError as e:
        print(f"❌ فشل في استيراد نظام الفواتير: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🧪 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        db_manager = SQLiteManager()
        print("✅ تم الاتصال بقاعدة البيانات بنجاح")
        
        # اختبار وجود جداول الفواتير
        tables_query = """
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name IN ('sales_invoices', 'sales_invoice_items')
        """
        tables = db_manager.execute_query(tables_query)
        
        if len(tables) >= 2:
            print("✅ جداول الفواتير موجودة في قاعدة البيانات")
            return True, db_manager
        else:
            print("⚠️ بعض جداول الفواتير مفقودة")
            return False, db_manager
            
    except Exception as e:
        print(f"❌ فشل في الاتصال بقاعدة البيانات: {e}")
        return False, None

def test_invoice_calculation_engine():
    """اختبار محرك حساب الفواتير"""
    print("\n🧪 اختبار محرك حساب الفواتير...")
    
    try:
        from src.modules.invoices.enhanced_invoice_system import InvoiceCalculationEngine
        
        # اختبار حساب إجمالي السطر
        line_calc = InvoiceCalculationEngine.calculate_line_total(
            quantity=10,
            unit_price=100,
            discount_percentage=10,
            tax_rate=15
        )
        
        expected_subtotal = 1000  # 10 * 100
        expected_discount = 100   # 10% من 1000
        expected_after_discount = 900  # 1000 - 100
        expected_tax = 135        # 15% من 900
        expected_total = 1035     # 900 + 135
        
        if (abs(line_calc['subtotal'] - expected_subtotal) < 0.01 and
            abs(line_calc['discount_amount'] - expected_discount) < 0.01 and
            abs(line_calc['after_discount'] - expected_after_discount) < 0.01 and
            abs(line_calc['tax_amount'] - expected_tax) < 0.01 and
            abs(line_calc['total'] - expected_total) < 0.01):
            print("✅ محرك حساب السطر يعمل بشكل صحيح")
            print(f"   📊 المجموع الفرعي: {line_calc['subtotal']:.2f}")
            print(f"   💰 الخصم: {line_calc['discount_amount']:.2f}")
            print(f"   🧾 الضريبة: {line_calc['tax_amount']:.2f}")
            print(f"   💵 الإجمالي: {line_calc['total']:.2f}")
            return True
        else:
            print("❌ خطأ في حسابات السطر")
            return False
            
    except Exception as e:
        print(f"❌ فشل في اختبار محرك الحساب: {e}")
        return False

def test_invoice_status_manager():
    """اختبار مدير حالات الفواتير"""
    print("\n🧪 اختبار مدير حالات الفواتير...")
    
    try:
        from src.modules.invoices.enhanced_invoice_system import InvoiceStatusManager
        from datetime import datetime, timedelta
        
        # اختبار الحالات المختلفة
        test_cases = [
            (1000, 1000, None, 'paid'),      # مدفوعة بالكامل
            (1000, 500, None, 'partial'),    # مدفوعة جزئياً
            (1000, 0, None, 'sent'),         # غير مدفوعة
        ]
        
        all_passed = True
        for total, paid, due_date, expected_status in test_cases:
            calculated_status = InvoiceStatusManager.calculate_status(total, paid, due_date)
            if calculated_status == expected_status:
                print(f"✅ حالة الفاتورة صحيحة: {expected_status}")
            else:
                print(f"❌ خطأ في حالة الفاتورة: متوقع {expected_status}, حُسب {calculated_status}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ فشل في اختبار مدير الحالات: {e}")
        return False

def test_invoice_number_generation():
    """اختبار توليد أرقام الفواتير"""
    print("\n🧪 اختبار توليد أرقام الفواتير...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.modules.invoices.enhanced_invoice_system import EnhancedInvoiceSystem
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء تطبيق مؤقت
        app = QApplication([])
        db_manager = SQLiteManager()
        
        # إنشاء نظام الفواتير
        invoice_system = EnhancedInvoiceSystem(db_manager)
        
        # توليد رقم فاتورة
        invoice_number = invoice_system.generate_invoice_number()
        
        if invoice_number and invoice_number.startswith('INV-'):
            print(f"✅ تم توليد رقم الفاتورة بنجاح: {invoice_number}")
            return True
        else:
            print(f"❌ خطأ في توليد رقم الفاتورة: {invoice_number}")
            return False
            
    except Exception as e:
        print(f"❌ فشل في اختبار توليد رقم الفاتورة: {e}")
        return False

def test_invoice_validation():
    """اختبار التحقق من صحة الفاتورة"""
    print("\n🧪 اختبار التحقق من صحة الفاتورة...")
    
    try:
        from src.modules.invoices.enhanced_invoice_system import InvoiceValidationEngine
        
        # فاتورة صحيحة
        valid_invoice = {
            'invoice_number': 'INV-2025-0001',
            'customer_id': 1,
            'customer_name': 'عميل تجريبي',
            'invoice_date': '2025-01-01',
            'due_date': '2025-01-31',
            'items': [
                {
                    'product_id': 1,
                    'product_name': 'منتج تجريبي',
                    'quantity': 10,
                    'unit_price': 100,
                    'total': 1000
                }
            ],
            'total_amount': 1000
        }
        
        validation = InvoiceValidationEngine.validate_invoice_data(valid_invoice)
        
        if validation['is_valid']:
            print("✅ التحقق من صحة الفاتورة يعمل بشكل صحيح")
            return True
        else:
            print(f"❌ خطأ في التحقق من الفاتورة: {validation['errors']}")
            return False
            
    except Exception as e:
        print(f"❌ فشل في اختبار التحقق من الفاتورة: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار شامل لنظام الفواتير المحسن")
    print("=" * 60)
    
    tests = [
        ("استيراد النظام", test_invoice_system_import),
        ("الاتصال بقاعدة البيانات", test_database_connection),
        ("محرك الحساب", test_invoice_calculation_engine),
        ("مدير الحالات", test_invoice_status_manager),
        ("توليد أرقام الفواتير", test_invoice_number_generation),
        ("التحقق من صحة الفاتورة", test_invoice_validation),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed_tests}/{total_tests} اختبار نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! نظام الفواتير المحسن جاهز للاستخدام")
        
        print("\n✨ الميزات الجديدة المضافة:")
        print("   💾 حفظ الفواتير في قاعدة البيانات")
        print("   🔄 تحديث الفواتير الموجودة")
        print("   📊 حساب دقيق للضرائب والخصومات")
        print("   🔢 توليد أرقام فواتير تلقائي")
        print("   ✅ التحقق من صحة البيانات")
        print("   📋 إدارة حالات الفواتير")
        
        print("\n🚀 للاستخدام:")
        print("   python src/main_system.py")
        print("   ثم اختر 'الفواتير' من القائمة")
        
    else:
        print("⚠️ بعض الاختبارات فشلت - يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
