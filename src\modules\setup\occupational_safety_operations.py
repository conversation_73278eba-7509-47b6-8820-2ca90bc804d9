#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
عمليات أنواع السلامة المهنية
Occupational Safety Types Operations
"""

from PyQt5.QtWidgets import QMessageBox, QTableWidgetItem, QInputDialog, QFileDialog
from PyQt5.QtCore import Qt
from datetime import datetime

# وظائف أنواع السلامة
def add_safety_type_function(self):
    """إضافة نوع سلامة جديد"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة أنواع السلامة قيد التطوير")

def edit_safety_type_function(self):
    """تعديل نوع سلامة"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل أنواع السلامة قيد التطوير")

def delete_safety_type_function(self):
    """حذف نوع سلامة"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة حذف أنواع السلامة قيد التطوير")

# وظائف إجراءات السلامة
def add_safety_measure_function(self):
    """إضافة إجراء سلامة جديد"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة إجراءات السلامة قيد التطوير")

def edit_safety_measure_function(self):
    """تعديل إجراء سلامة"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل إجراءات السلامة قيد التطوير")

def delete_safety_measure_function(self):
    """حذف إجراء سلامة"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة حذف إجراءات السلامة قيد التطوير")

# وظائف الحوادث
def add_incident_function(self):
    """تسجيل حادث جديد"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة تسجيل الحوادث قيد التطوير")

def edit_incident_function(self):
    """تعديل حادث"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل الحوادث قيد التطوير")

def analyze_risks_function(self):
    """تحليل المخاطر"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة تحليل المخاطر قيد التطوير")

# وظائف برامج التدريب
def add_training_program_function(self):
    """إضافة برنامج تدريب جديد"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة برامج التدريب قيد التطوير")

def edit_training_program_function(self):
    """تعديل برنامج تدريب"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل برامج التدريب قيد التطوير")

def schedule_training_function(self):
    """جدولة التدريب"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة جدولة التدريب قيد التطوير")

# وظائف التقارير
def generate_safety_types_report_function(self):
    """إنتاج تقرير أنواع السلامة"""
    report = f"""
🦺 تقرير أنواع السلامة المهنية
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 إحصائيات عامة:
   🦺 إجمالي أنواع السلامة: {len(self.safety_types_data)} نوع
   ✅ الأنواع النشطة: {len([s for s in self.safety_types_data if s['status'] == 'نشط'])} نوع

📋 توزيع أنواع السلامة حسب التصنيف:
"""
    
    # تجميع حسب التصنيف
    classifications = {}
    for safety_type in self.safety_types_data:
        classification = safety_type["classification"]
        if classification not in classifications:
            classifications[classification] = []
        classifications[classification].append(safety_type)
    
    for classification, types in classifications.items():
        report += f"   📋 {classification}: {len(types)} نوع\n"
    
    report += f"""

📋 قائمة أنواع السلامة:
"""
    
    for i, safety_type in enumerate(self.safety_types_data, 1):
        report += f"""
{i}. {safety_type['name']} ({safety_type['code']})
   📋 التصنيف: {safety_type['classification']}
   ⚠️ مستوى الخطورة: {safety_type['risk_level']}
   📝 الوصف: {safety_type['description']}
   📋 متطلبات الامتثال: {safety_type['compliance_requirements']}
   ✅ الحالة: {safety_type['status']}
   {'─'*50}
"""
    
    self.reports_display.setPlainText(report)

def generate_incidents_report_function(self):
    """إنتاج تقرير الحوادث"""
    report = f"""
⚠️ تقرير الحوادث والمخاطر
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 إحصائيات عامة:
   ⚠️ إجمالي الحوادث: {len(self.incidents_data)} حادث
   🔴 الحوادث المفتوحة: {len([i for i in self.incidents_data if i['status'] != 'مغلق'])} حادث
   ✅ الحوادث المغلقة: {len([i for i in self.incidents_data if i['status'] == 'مغلق'])} حادث

📋 توزيع الحوادث حسب مستوى الخطورة:
"""
    
    # تجميع حسب مستوى الخطورة
    risk_levels = {}
    for incident in self.incidents_data:
        risk_level = incident["risk_level"]
        if risk_level not in risk_levels:
            risk_levels[risk_level] = 0
        risk_levels[risk_level] += 1
    
    for risk_level, count in risk_levels.items():
        report += f"   ⚠️ {risk_level}: {count} حادث\n"
    
    report += f"""

📋 قائمة الحوادث:
"""
    
    for i, incident in enumerate(self.incidents_data, 1):
        report += f"""
{i}. حادث رقم {incident['number']}
   📅 التاريخ: {incident['date']}
   📋 النوع: {incident['type']}
   📍 الموقع: {incident['location']}
   ⚠️ مستوى الخطورة: {incident['risk_level']}
   🏥 الإصابات: {incident['injuries']}
   💰 الأضرار: {incident['damages']}
   📊 الحالة: {incident['status']}
   {'─'*50}
"""
    
    self.reports_display.setPlainText(report)

def generate_training_report_function(self):
    """إنتاج تقرير التدريب"""
    report = f"""
📚 تقرير برامج التدريب
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 إحصائيات عامة:
   📚 إجمالي البرامج: {len(self.training_programs_data)} برنامج
   👥 إجمالي المتدربين: {sum(p['trainees'] for p in self.training_programs_data)} متدرب
   💰 إجمالي التكلفة: {sum(p['cost'] for p in self.training_programs_data):,} ريال

📋 توزيع البرامج حسب الحالة:
"""
    
    # تجميع حسب الحالة
    statuses = {}
    for program in self.training_programs_data:
        status = program["status"]
        if status not in statuses:
            statuses[status] = 0
        statuses[status] += 1
    
    for status, count in statuses.items():
        report += f"   📊 {status}: {count} برنامج\n"
    
    report += f"""

📋 قائمة برامج التدريب:
"""
    
    for i, program in enumerate(self.training_programs_data, 1):
        report += f"""
{i}. {program['name']} ({program['code']})
   🦺 نوع السلامة: {program['safety_type']}
   ⏱️ المدة: {program['duration']}
   👥 عدد المتدربين: {program['trainees']} متدرب
   💰 التكلفة: {program['cost']:,} ريال
   👨‍🏫 المدرب: {program['trainer']}
   📊 الحالة: {program['status']}
   {'─'*50}
"""
    
    self.reports_display.setPlainText(report)

def generate_compliance_report_function(self):
    """إنتاج تقرير الامتثال"""
    report = f"""
📋 تقرير الامتثال للسلامة المهنية
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 نظرة عامة على الامتثال:
   🦺 أنواع السلامة المطبقة: {len(self.safety_types_data)} نوع
   🛡️ إجراءات السلامة المنفذة: {len(self.safety_measures_data)} إجراء
   📚 برامج التدريب المكتملة: {len([p for p in self.training_programs_data if p['status'] == 'مكتمل'])} برنامج

📋 متطلبات الامتثال:
"""
    
    # تجميع متطلبات الامتثال
    compliance_requirements = set()
    for safety_type in self.safety_types_data:
        compliance_requirements.add(safety_type["compliance_requirements"])
    
    for requirement in compliance_requirements:
        report += f"   📋 {requirement}\n"
    
    report += f"""

🛡️ حالة إجراءات السلامة:
"""
    
    # تجميع حسب حالة الإجراءات
    measure_statuses = {}
    for measure in self.safety_measures_data:
        status = measure["status"]
        if status not in measure_statuses:
            measure_statuses[status] = 0
        measure_statuses[status] += 1
    
    for status, count in measure_statuses.items():
        report += f"   📊 {status}: {count} إجراء\n"
    
    report += f"""

📊 تقييم المخاطر:
   🔴 مخاطر حرجة: {len([s for s in self.safety_types_data if s['risk_level'] == 'حرج'])} نوع
   🟡 مخاطر عالية: {len([s for s in self.safety_types_data if s['risk_level'] == 'عالي'])} نوع
   🟢 مخاطر متوسطة: {len([s for s in self.safety_types_data if s['risk_level'] == 'متوسط'])} نوع
   ⚪ مخاطر منخفضة: {len([s for s in self.safety_types_data if s['risk_level'] == 'منخفض'])} نوع

💡 التوصيات:
   • مراجعة دورية لإجراءات السلامة
   • تحديث برامج التدريب بانتظام
   • تعزيز الامتثال للمعايير الدولية
   • تطوير خطط الطوارئ والاستجابة السريعة
"""
    
    self.reports_display.setPlainText(report)

def export_report_function(self):
    """تصدير التقرير"""
    QMessageBox.information(self, "قيد التطوير", "تصدير التقارير قيد التطوير")

def print_report_function(self):
    """طباعة التقرير"""
    QMessageBox.information(self, "قيد التطوير", "طباعة التقارير قيد التطوير")

def clear_report_function(self):
    """مسح التقرير"""
    self.reports_display.clear()

def save_changes_function(self):
    """حفظ التغييرات"""
    QMessageBox.information(self, "تم", "تم حفظ التغييرات بنجاح")

def show_help_function(self):
    """عرض المساعدة"""
    help_text = """
🦺 مساعدة أنواع السلامة المهنية

📋 الوظائف المتاحة:

🦺 أنواع السلامة:
• إدارة أنواع السلامة المهنية المختلفة
• تصنيف المخاطر حسب النوع والخطورة
• تحديد متطلبات الامتثال لكل نوع
• 8 تصنيفات رئيسية للسلامة

🛡️ إجراءات السلامة:
• تخطيط وتنفيذ إجراءات السلامة
• تحديد الأولويات والتكاليف
• متابعة تنفيذ الإجراءات
• تقييم فعالية الإجراءات

⚠️ إدارة الحوادث:
• تسجيل ومتابعة الحوادث
• تحليل أسباب الحوادث
• تقييم المخاطر والأضرار
• وضع خطط الوقاية

📚 برامج التدريب:
• تصميم برامج التدريب المتخصصة
• جدولة وتنظيم الدورات التدريبية
• متابعة أداء المتدربين
• تقييم فعالية البرامج

📊 التقارير:
• تقرير أنواع السلامة المهنية
• تقرير الحوادث والمخاطر
• تقرير برامج التدريب
• تقرير الامتثال والمعايير

💡 نصائح:
• راجع إجراءات السلامة بانتظام
• حدث برامج التدريب حسب المعايير الجديدة
• سجل جميع الحوادث مهما كانت بسيطة
• اتبع المعايير الدولية للسلامة المهنية
• أشرك الموظفين في برامج السلامة

🔧 أنواع السلامة المتاحة:
• السلامة الفيزيائية (الحرائق، الميكانيكية)
• السلامة الكيميائية (المواد الخطرة)
• السلامة الكهربائية (الصعق، الحرائق)
• السلامة البيولوجية (العدوى، التلوث)
• السلامة الإشعاعية (الإشعاع المؤين)
• السلامة النفسية (الضغط، التوتر)
• السلامة البيئية (التلوث، النفايات)

📈 فوائد النظام:
• تقليل الحوادث والإصابات
• الامتثال للمعايير القانونية
• تحسين بيئة العمل
• زيادة الوعي بالسلامة
• توفير التكاليف طويلة المدى
"""
    
    QMessageBox.information(self, "المساعدة", help_text)
