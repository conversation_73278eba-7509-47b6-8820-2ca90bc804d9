#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لوظائف إدارة النظام
Quick Test for System Management Functions
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_modules_import():
    """اختبار استيراد الوحدات"""
    print("🧪 اختبار استيراد وحدات إدارة النظام...")
    
    modules_to_test = [
        ("الإغلاقات والتوثيقات", "modules.closures_audits_module"),
        ("التحكم بواجهات الدخول", "modules.login_interface_control_module"),
        ("تتبع النشاطات", "modules.activity_tracking_module"),
        ("مزامنة البيانات", "modules.data_synchronization_module"),
    ]
    
    working_modules = 0
    total_modules = len(modules_to_test)
    
    for module_name, module_path in modules_to_test:
        try:
            __import__(module_path)
            print(f"   ✅ {module_name}")
            working_modules += 1
        except Exception as e:
            print(f"   ❌ {module_name}: {e}")
    
    print(f"\n📊 النتيجة: {working_modules}/{total_modules} وحدات تعمل بنجاح")
    return working_modules == total_modules

def test_main_system_functions():
    """اختبار دوال النظام الرئيسي"""
    print("\n🔗 اختبار دوال النظام الرئيسي...")
    
    try:
        # قراءة ملف النظام الرئيسي
        with open('src/main_system.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_functions = [
            'open_closures_and_audits',
            'open_login_interface_control',
            'open_activity_tracking', 
            'open_data_synchronization'
        ]
        
        working_functions = 0
        for func_name in required_functions:
            if f"def {func_name}(" in content:
                print(f"   ✅ {func_name}")
                working_functions += 1
            else:
                print(f"   ❌ {func_name}")
        
        print(f"\n📊 النتيجة: {working_functions}/{len(required_functions)} دوال موجودة")
        return working_functions == len(required_functions)
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص النظام الرئيسي: {e}")
        return False

def check_file_existence():
    """فحص وجود الملفات"""
    print("\n📁 فحص وجود ملفات الوحدات...")
    
    files_to_check = [
        "src/modules/closures_audits_module.py",
        "src/modules/login_interface_control_module.py", 
        "src/modules/activity_tracking_module.py",
        "src/modules/data_synchronization_module.py"
    ]
    
    existing_files = 0
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"   ✅ {os.path.basename(file_path)}")
            existing_files += 1
        else:
            print(f"   ❌ {os.path.basename(file_path)}")
    
    print(f"\n📊 النتيجة: {existing_files}/{len(files_to_check)} ملفات موجودة")
    return existing_files == len(files_to_check)

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار سريع لوظائف إدارة النظام المكتملة")
    print("=" * 60)
    
    tests = [
        ("فحص وجود الملفات", check_file_existence),
        ("اختبار دوال النظام الرئيسي", test_main_system_functions),
        ("اختبار استيراد الوحدات", test_modules_import),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 {test_name}:")
        print("-" * 40)
        
        if test_function():
            print(f"✅ نجح: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل: {test_name}")
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم إكمال تطوير وظائف إدارة النظام بنجاح!")
        
        print("\n🛠️ الوظائف المكتملة:")
        print("   🔒 الإغلاقات والتوثيقات")
        print("   🔐 التحكم بواجهات الدخول") 
        print("   📋 تتبع النشاطات (سجل الأحداث)")
        print("   🔄 مزامنة البيانات بين الفروع")
        
        print("\n🚀 للوصول للوظائف:")
        print("   1. شغل النظام: python src/main_system.py")
        print("   2. انقر على 'إدارة النظام'")
        print("   3. اختر الوظيفة المطلوبة")
        
        print("\n✨ الميزات الجديدة:")
        print("   🔒 إدارة الإغلاقات المحاسبية")
        print("   🔐 أمان متقدم للجلسات")
        print("   📋 تتبع شامل للنشاطات")
        print("   🔄 مزامنة احترافية للفروع")
        
    else:
        print("\n⚠️ بعض الوحدات تحتاج مراجعة")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
