#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الملفات المتقدم
Advanced File Manager System
"""

import os
import shutil
import stat
from datetime import datetime
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTreeWidget, 
                             QTreeWidgetItem, QLabel, QPushButton, QLineEdit,
                             QMessageBox, QFileDialog, QMenu, QAction, QSplitter,
                             QTextEdit, QGroupBox, QProgressBar, QTableWidget,
                             QTableWidgetItem, QHeaderView, QComboBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon

class FileOperationThread(QThread):
    """خيط لعمليات الملفات"""
    progress_updated = pyqtSignal(int)
    operation_completed = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, operation, source, destination=None):
        super().__init__()
        self.operation = operation
        self.source = source
        self.destination = destination
    
    def run(self):
        try:
            if self.operation == "copy":
                self.copy_file()
            elif self.operation == "move":
                self.move_file()
            elif self.operation == "delete":
                self.delete_file()
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def copy_file(self):
        if os.path.isfile(self.source):
            shutil.copy2(self.source, self.destination)
        else:
            shutil.copytree(self.source, self.destination)
        self.operation_completed.emit("تم نسخ الملف بنجاح")
    
    def move_file(self):
        shutil.move(self.source, self.destination)
        self.operation_completed.emit("تم نقل الملف بنجاح")
    
    def delete_file(self):
        if os.path.isfile(self.source):
            os.remove(self.source)
        else:
            shutil.rmtree(self.source)
        self.operation_completed.emit("تم حذف الملف بنجاح")

class FileManagerDialog(QDialog):
    """مدير الملفات المتقدم"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_path = os.path.expanduser("~")
        self.setup_ui()
        self.load_directory()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🗂️ مدير الملفات المتقدم")
        self.setGeometry(100, 100, 1000, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        layout = QVBoxLayout()
        
        # شريط العنوان
        title_label = QLabel("🗂️ مدير الملفات المتقدم")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
                background-color: #3498db;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # شريط التنقل
        nav_layout = QHBoxLayout()
        
        self.back_btn = QPushButton("⬅️ رجوع")
        self.back_btn.clicked.connect(self.go_back)
        nav_layout.addWidget(self.back_btn)
        
        self.forward_btn = QPushButton("➡️ تقدم")
        self.forward_btn.clicked.connect(self.go_forward)
        nav_layout.addWidget(self.forward_btn)
        
        self.up_btn = QPushButton("⬆️ أعلى")
        self.up_btn.clicked.connect(self.go_up)
        nav_layout.addWidget(self.up_btn)
        
        self.home_btn = QPushButton("🏠 الرئيسية")
        self.home_btn.clicked.connect(self.go_home)
        nav_layout.addWidget(self.home_btn)
        
        nav_layout.addStretch()
        
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.refresh_btn.clicked.connect(self.refresh)
        nav_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(nav_layout)
        
        # شريط المسار
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("المسار:"))
        
        self.path_edit = QLineEdit()
        self.path_edit.setText(self.current_path)
        self.path_edit.returnPressed.connect(self.navigate_to_path)
        path_layout.addWidget(self.path_edit)
        
        self.browse_btn = QPushButton("📁 تصفح")
        self.browse_btn.clicked.connect(self.browse_folder)
        path_layout.addWidget(self.browse_btn)
        
        layout.addLayout(path_layout)
        
        # المحتوى الرئيسي
        splitter = QSplitter(Qt.Horizontal)
        
        # قائمة الملفات
        self.file_tree = QTreeWidget()
        self.file_tree.setHeaderLabels(["الاسم", "النوع", "الحجم", "تاريخ التعديل"])
        self.file_tree.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.file_tree.setContextMenuPolicy(Qt.CustomContextMenu)
        self.file_tree.customContextMenuRequested.connect(self.show_context_menu)
        splitter.addWidget(self.file_tree)
        
        # لوحة المعلومات
        info_widget = QGroupBox("معلومات الملف")
        info_layout = QVBoxLayout()
        
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(200)
        info_layout.addWidget(self.info_text)
        
        # أزرار العمليات
        operations_layout = QHBoxLayout()
        
        self.copy_btn = QPushButton("📋 نسخ")
        self.copy_btn.clicked.connect(self.copy_file)
        operations_layout.addWidget(self.copy_btn)
        
        self.cut_btn = QPushButton("✂️ قص")
        self.cut_btn.clicked.connect(self.cut_file)
        operations_layout.addWidget(self.cut_btn)
        
        self.paste_btn = QPushButton("📌 لصق")
        self.paste_btn.clicked.connect(self.paste_file)
        operations_layout.addWidget(self.paste_btn)
        
        self.delete_btn = QPushButton("🗑️ حذف")
        self.delete_btn.clicked.connect(self.delete_file)
        operations_layout.addWidget(self.delete_btn)
        
        info_layout.addLayout(operations_layout)
        
        # أزرار إضافية
        extra_layout = QHBoxLayout()
        
        self.new_folder_btn = QPushButton("📁 مجلد جديد")
        self.new_folder_btn.clicked.connect(self.create_new_folder)
        extra_layout.addWidget(self.new_folder_btn)
        
        self.rename_btn = QPushButton("✏️ إعادة تسمية")
        self.rename_btn.clicked.connect(self.rename_file)
        extra_layout.addWidget(self.rename_btn)
        
        self.properties_btn = QPushButton("ℹ️ خصائص")
        self.properties_btn.clicked.connect(self.show_properties)
        extra_layout.addWidget(self.properties_btn)
        
        info_layout.addLayout(extra_layout)
        
        info_widget.setLayout(info_layout)
        splitter.addWidget(info_widget)
        
        layout.addWidget(splitter)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("جاهز")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)
        
        layout.addLayout(status_layout)
        
        self.setLayout(layout)
        
        # متغيرات للعمليات
        self.clipboard_path = None
        self.clipboard_operation = None
        self.history = []
        self.history_index = -1
    
    def load_directory(self):
        """تحميل محتويات المجلد"""
        try:
            self.file_tree.clear()
            self.path_edit.setText(self.current_path)
            
            if not os.path.exists(self.current_path):
                QMessageBox.warning(self, "خطأ", "المسار غير موجود")
                return
            
            items = os.listdir(self.current_path)
            items.sort()
            
            for item in items:
                item_path = os.path.join(self.current_path, item)
                
                try:
                    stat_info = os.stat(item_path)
                    
                    tree_item = QTreeWidgetItem()
                    tree_item.setText(0, item)
                    tree_item.setData(0, Qt.UserRole, item_path)
                    
                    if os.path.isdir(item_path):
                        tree_item.setText(1, "مجلد")
                        tree_item.setText(2, "")
                    else:
                        tree_item.setText(1, "ملف")
                        size = stat_info.st_size
                        tree_item.setText(2, self.format_size(size))
                    
                    mod_time = datetime.fromtimestamp(stat_info.st_mtime)
                    tree_item.setText(3, mod_time.strftime("%Y-%m-%d %H:%M"))
                    
                    self.file_tree.addTopLevelItem(tree_item)
                    
                except (OSError, PermissionError):
                    continue
            
            # تحديث شريط الحالة
            file_count = self.file_tree.topLevelItemCount()
            self.status_label.setText(f"تم تحميل {file_count} عنصر")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المجلد:\n{str(e)}")
    
    def format_size(self, size):
        """تنسيق حجم الملف"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    
    def on_item_double_clicked(self, item, column):
        """معالجة النقر المزدوج على عنصر"""
        item_path = item.data(0, Qt.UserRole)
        
        if os.path.isdir(item_path):
            self.navigate_to(item_path)
        else:
            # فتح الملف
            try:
                os.startfile(item_path)
            except:
                QMessageBox.information(self, "معلومات", "لا يمكن فتح هذا النوع من الملفات")
    
    def navigate_to(self, path):
        """التنقل إلى مسار معين"""
        if os.path.exists(path) and os.path.isdir(path):
            # إضافة للتاريخ
            if self.history_index == -1 or self.history[self.history_index] != self.current_path:
                self.history = self.history[:self.history_index + 1]
                self.history.append(self.current_path)
                self.history_index += 1
            
            self.current_path = path
            self.load_directory()
    
    def navigate_to_path(self):
        """التنقل للمسار المكتوب"""
        path = self.path_edit.text()
        self.navigate_to(path)
    
    def go_back(self):
        """الرجوع للخلف"""
        if self.history_index > 0:
            self.history_index -= 1
            self.current_path = self.history[self.history_index]
            self.load_directory()
    
    def go_forward(self):
        """التقدم للأمام"""
        if self.history_index < len(self.history) - 1:
            self.history_index += 1
            self.current_path = self.history[self.history_index]
            self.load_directory()
    
    def go_up(self):
        """الانتقال للمجلد الأعلى"""
        parent_path = os.path.dirname(self.current_path)
        if parent_path != self.current_path:
            self.navigate_to(parent_path)
    
    def go_home(self):
        """الانتقال للمجلد الرئيسي"""
        home_path = os.path.expanduser("~")
        self.navigate_to(home_path)
    
    def refresh(self):
        """تحديث المجلد الحالي"""
        self.load_directory()
    
    def browse_folder(self):
        """تصفح مجلد"""
        folder = QFileDialog.getExistingDirectory(self, "اختر مجلد", self.current_path)
        if folder:
            self.navigate_to(folder)
    
    def show_context_menu(self, position):
        """عرض القائمة السياقية"""
        item = self.file_tree.itemAt(position)
        
        menu = QMenu(self)
        
        if item:
            menu.addAction("فتح", lambda: self.on_item_double_clicked(item, 0))
            menu.addSeparator()
            menu.addAction("نسخ", self.copy_file)
            menu.addAction("قص", self.cut_file)
            menu.addAction("حذف", self.delete_file)
            menu.addAction("إعادة تسمية", self.rename_file)
            menu.addSeparator()
            menu.addAction("خصائص", self.show_properties)
        
        menu.addAction("لصق", self.paste_file)
        menu.addAction("مجلد جديد", self.create_new_folder)
        menu.addSeparator()
        menu.addAction("تحديث", self.refresh)
        
        menu.exec_(self.file_tree.mapToGlobal(position))
    
    def copy_file(self):
        """نسخ الملف المحدد"""
        current_item = self.file_tree.currentItem()
        if current_item:
            self.clipboard_path = current_item.data(0, Qt.UserRole)
            self.clipboard_operation = "copy"
            self.status_label.setText("تم نسخ الملف للحافظة")
    
    def cut_file(self):
        """قص الملف المحدد"""
        current_item = self.file_tree.currentItem()
        if current_item:
            self.clipboard_path = current_item.data(0, Qt.UserRole)
            self.clipboard_operation = "cut"
            self.status_label.setText("تم قص الملف للحافظة")
    
    def paste_file(self):
        """لصق الملف من الحافظة"""
        if not self.clipboard_path or not self.clipboard_operation:
            QMessageBox.information(self, "معلومات", "لا يوجد ملف في الحافظة")
            return
        
        source = self.clipboard_path
        filename = os.path.basename(source)
        destination = os.path.join(self.current_path, filename)
        
        if os.path.exists(destination):
            reply = QMessageBox.question(self, "تأكيد", 
                                       f"الملف {filename} موجود بالفعل. هل تريد استبداله؟")
            if reply != QMessageBox.Yes:
                return
        
        try:
            if self.clipboard_operation == "copy":
                if os.path.isfile(source):
                    shutil.copy2(source, destination)
                else:
                    shutil.copytree(source, destination)
            elif self.clipboard_operation == "cut":
                shutil.move(source, destination)
                self.clipboard_path = None
                self.clipboard_operation = None
            
            self.refresh()
            self.status_label.setText("تم لصق الملف بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في لصق الملف:\n{str(e)}")
    
    def delete_file(self):
        """حذف الملف المحدد"""
        current_item = self.file_tree.currentItem()
        if not current_item:
            return
        
        file_path = current_item.data(0, Qt.UserRole)
        filename = os.path.basename(file_path)
        
        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   f"هل أنت متأكد من حذف '{filename}'؟")
        if reply == QMessageBox.Yes:
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                else:
                    shutil.rmtree(file_path)
                
                self.refresh()
                self.status_label.setText("تم حذف الملف بنجاح")
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف الملف:\n{str(e)}")
    
    def create_new_folder(self):
        """إنشاء مجلد جديد"""
        from PyQt5.QtWidgets import QInputDialog
        
        name, ok = QInputDialog.getText(self, "مجلد جديد", "اسم المجلد:")
        if ok and name:
            folder_path = os.path.join(self.current_path, name)
            try:
                os.makedirs(folder_path)
                self.refresh()
                self.status_label.setText("تم إنشاء المجلد بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في إنشاء المجلد:\n{str(e)}")
    
    def rename_file(self):
        """إعادة تسمية الملف"""
        current_item = self.file_tree.currentItem()
        if not current_item:
            return
        
        from PyQt5.QtWidgets import QInputDialog
        
        old_path = current_item.data(0, Qt.UserRole)
        old_name = os.path.basename(old_path)
        
        new_name, ok = QInputDialog.getText(self, "إعادة تسمية", "الاسم الجديد:", text=old_name)
        if ok and new_name and new_name != old_name:
            new_path = os.path.join(os.path.dirname(old_path), new_name)
            try:
                os.rename(old_path, new_path)
                self.refresh()
                self.status_label.setText("تم تغيير الاسم بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تغيير الاسم:\n{str(e)}")
    
    def show_properties(self):
        """عرض خصائص الملف"""
        current_item = self.file_tree.currentItem()
        if not current_item:
            return
        
        file_path = current_item.data(0, Qt.UserRole)
        
        try:
            stat_info = os.stat(file_path)
            
            info = f"""
📁 المسار: {file_path}
📏 الحجم: {self.format_size(stat_info.st_size)}
📅 تاريخ الإنشاء: {datetime.fromtimestamp(stat_info.st_ctime).strftime('%Y-%m-%d %H:%M:%S')}
📅 تاريخ التعديل: {datetime.fromtimestamp(stat_info.st_mtime).strftime('%Y-%m-%d %H:%M:%S')}
📅 آخر وصول: {datetime.fromtimestamp(stat_info.st_atime).strftime('%Y-%m-%d %H:%M:%S')}
🔒 الصلاحيات: {stat.filemode(stat_info.st_mode)}
            """
            
            self.info_text.setText(info.strip())
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في جلب خصائص الملف:\n{str(e)}")

def show_file_manager(parent=None):
    """عرض مدير الملفات"""
    dialog = FileManagerDialog(parent)
    dialog.exec_()
