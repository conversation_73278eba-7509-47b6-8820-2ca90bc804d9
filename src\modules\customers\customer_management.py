#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة العملاء
Customer Management System
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QMessageBox, 
                             QFrame, QSplitter, QTextEdit, QTabWidget, QWidget,
                             QTreeWidget, QTreeWidgetItem, QListWidget, QListWidgetItem,
                             QProgressBar, QSlider, QDateEdit, QRadioButton, QButtonGroup,
                             QApplication, QMainWindow, QMenuBar, QMenu, QAction, QToolBar,
                             QStatusBar, QFileDialog)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTimer
from PyQt5.QtGui import QFont, QColor, QIcon, QPixmap

class CustomerManagementWindow(QMainWindow):
    """نافذة إدارة العملاء الرئيسية"""
    
    def __init__(self, db_manager=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.customers_data = []
        self.setup_ui()
        self.load_sample_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("إدارة العملاء - نظام إدارة شامل للعملاء والزبائن")
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء القوائم
        self.create_menus()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء الواجهة الرئيسية
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("👥 نظام إدارة العملاء - إدارة شاملة للعملاء والزبائن")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات النظام
        self.tabs = QTabWidget()
        
        # تبويب قائمة العملاء
        customers_list_tab = self.create_customers_list_tab()
        self.tabs.addTab(customers_list_tab, "👥 قائمة العملاء")
        
        # تبويب إضافة/تعديل عميل
        customer_form_tab = self.create_customer_form_tab()
        self.tabs.addTab(customer_form_tab, "➕ إضافة/تعديل عميل")
        
        # تبويب تصنيفات العملاء
        customer_categories_tab = self.create_customer_categories_tab()
        self.tabs.addTab(customer_categories_tab, "🏷️ تصنيفات العملاء")

        # تبويب بيانات العملاء
        customer_data_tab = self.create_customer_data_tab()
        self.tabs.addTab(customer_data_tab, "📋 بيانات العملاء")

        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        self.tabs.addTab(reports_tab, "📊 التقارير")
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        
        central_widget.setLayout(main_layout)
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
    def create_menus(self):
        """إنشاء القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("📁 ملف")
        
        new_customer_action = QAction("➕ عميل جديد", self)
        new_customer_action.setShortcut("Ctrl+N")
        new_customer_action.triggered.connect(self.new_customer)
        file_menu.addAction(new_customer_action)
        
        save_action = QAction("💾 حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_customer)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        import_action = QAction("📥 استيراد عملاء", self)
        import_action.triggered.connect(self.import_customers)
        file_menu.addAction(import_action)
        
        export_action = QAction("📤 تصدير عملاء", self)
        export_action.triggered.connect(self.export_customers)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("❌ خروج", self)
        exit_action.setShortcut("Alt+F4")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة تحرير
        edit_menu = menubar.addMenu("✏️ تحرير")
        
        edit_customer_action = QAction("✏️ تعديل عميل", self)
        edit_customer_action.setShortcut("F2")
        edit_customer_action.triggered.connect(self.edit_customer)
        edit_menu.addAction(edit_customer_action)
        
        delete_customer_action = QAction("🗑️ حذف عميل", self)
        delete_customer_action.setShortcut("Delete")
        delete_customer_action.triggered.connect(self.delete_customer)
        edit_menu.addAction(delete_customer_action)
        
        edit_menu.addSeparator()
        
        search_action = QAction("🔍 بحث", self)
        search_action.setShortcut("Ctrl+F")
        search_action.triggered.connect(self.search_customers)
        edit_menu.addAction(search_action)
        
        # قائمة عرض
        view_menu = menubar.addMenu("👁️ عرض")
        
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_data)
        view_menu.addAction(refresh_action)
        
        # قائمة تقارير
        reports_menu = menubar.addMenu("📊 تقارير")
        
        customers_report_action = QAction("📋 تقرير العملاء", self)
        customers_report_action.triggered.connect(self.generate_customers_report)
        reports_menu.addAction(customers_report_action)
        
        # قائمة مساعدة
        help_menu = menubar.addMenu("❓ مساعدة")
        
        about_action = QAction("ℹ️ حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # زر عميل جديد
        new_customer_action = QAction("➕ عميل جديد", self)
        new_customer_action.triggered.connect(self.new_customer)
        toolbar.addAction(new_customer_action)
        
        # زر حفظ
        save_action = QAction("💾 حفظ", self)
        save_action.triggered.connect(self.save_customer)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # زر تعديل
        edit_action = QAction("✏️ تعديل", self)
        edit_action.triggered.connect(self.edit_customer)
        toolbar.addAction(edit_action)
        
        # زر حذف
        delete_action = QAction("🗑️ حذف", self)
        delete_action.triggered.connect(self.delete_customer)
        toolbar.addAction(delete_action)
        
        toolbar.addSeparator()
        
        # زر بحث
        search_action = QAction("🔍 بحث", self)
        search_action.triggered.connect(self.search_customers)
        toolbar.addAction(search_action)
        
        # زر تحديث
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # زر تقارير
        reports_action = QAction("📊 تقارير", self)
        reports_action.triggered.connect(self.show_reports)
        toolbar.addAction(reports_action)
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = self.statusBar()
        
        # عدد العملاء
        self.customers_count_label = QLabel("عدد العملاء: 0")
        self.status_bar.addWidget(self.customers_count_label)
        
        self.status_bar.addPermanentWidget(QLabel("نظام إدارة العملاء v1.0"))
        
    def create_customers_list_tab(self):
        """إنشاء تبويب قائمة العملاء"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # شريط البحث والتصفية
        search_layout = QHBoxLayout()
        
        search_layout.addWidget(QLabel("🔍 البحث:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم، الهاتف، أو البريد الإلكتروني...")
        self.search_input.textChanged.connect(self.filter_customers)
        search_layout.addWidget(self.search_input)
        
        search_layout.addWidget(QLabel("🏷️ التصنيف:"))
        self.category_filter = QComboBox()
        self.category_filter.addItems(["الكل", "عميل عادي", "عميل مميز", "عميل VIP", "عميل تجاري"])
        self.category_filter.currentTextChanged.connect(self.filter_customers)
        search_layout.addWidget(self.category_filter)
        
        clear_search_btn = QPushButton("🗑️ مسح البحث")
        clear_search_btn.clicked.connect(self.clear_search)
        search_layout.addWidget(clear_search_btn)
        
        layout.addLayout(search_layout)
        
        # جدول العملاء
        self.customers_table = QTableWidget()
        self.customers_table.setColumnCount(8)
        self.customers_table.setHorizontalHeaderLabels([
            "رقم العميل", "اسم العميل", "الهاتف", "البريد الإلكتروني", 
            "المدينة", "التصنيف", "الرصيد", "تاريخ التسجيل"
        ])
        
        # تنسيق الجدول
        self.customers_table.horizontalHeader().setStretchLastSection(True)
        self.customers_table.setAlternatingRowColors(True)
        self.customers_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.customers_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 12px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)
        
        # ربط الأحداث
        self.customers_table.itemDoubleClicked.connect(self.edit_selected_customer)
        self.customers_table.itemSelectionChanged.connect(self.update_selection_info)
        
        layout.addWidget(self.customers_table)
        
        # أزرار الإجراءات
        actions_layout = QHBoxLayout()
        
        add_customer_btn = QPushButton("➕ إضافة عميل جديد")
        add_customer_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_customer_btn.clicked.connect(self.new_customer)
        
        edit_customer_btn = QPushButton("✏️ تعديل العميل")
        edit_customer_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_customer_btn.clicked.connect(self.edit_customer)
        
        delete_customer_btn = QPushButton("🗑️ حذف العميل")
        delete_customer_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_customer_btn.clicked.connect(self.delete_customer)

        # زر سند القبض
        receipt_voucher_btn = QPushButton("📄 سند قبض")
        receipt_voucher_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 10px 20px;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        receipt_voucher_btn.clicked.connect(self.open_receipt_voucher)

        actions_layout.addWidget(add_customer_btn)
        actions_layout.addWidget(edit_customer_btn)
        actions_layout.addWidget(delete_customer_btn)
        actions_layout.addWidget(receipt_voucher_btn)
        actions_layout.addStretch()
        
        layout.addLayout(actions_layout)
        
        tab.setLayout(layout)
        return tab

    def create_customer_form_tab(self):
        """إنشاء تبويب نموذج العميل"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة المعلومات الأساسية
        basic_info_group = QGroupBox("📋 المعلومات الأساسية")
        basic_info_layout = QGridLayout()

        # رقم العميل
        basic_info_layout.addWidget(QLabel("رقم العميل:"), 0, 0)
        self.customer_id_input = QLineEdit()
        self.customer_id_input.setPlaceholderText("سيتم إنشاؤه تلقائياً")
        self.customer_id_input.setReadOnly(True)
        basic_info_layout.addWidget(self.customer_id_input, 0, 1)

        # اسم العميل
        basic_info_layout.addWidget(QLabel("* اسم العميل:"), 0, 2)
        self.customer_name_input = QLineEdit()
        self.customer_name_input.setPlaceholderText("مثال: أحمد محمد علي")
        basic_info_layout.addWidget(self.customer_name_input, 0, 3)

        # رقم الهاتف
        basic_info_layout.addWidget(QLabel("* رقم الهاتف:"), 1, 0)
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("مثال: 777123456")
        basic_info_layout.addWidget(self.phone_input, 1, 1)

        # البريد الإلكتروني
        basic_info_layout.addWidget(QLabel("البريد الإلكتروني:"), 1, 2)
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("مثال: <EMAIL>")
        basic_info_layout.addWidget(self.email_input, 1, 3)

        # تاريخ الميلاد
        basic_info_layout.addWidget(QLabel("تاريخ الميلاد:"), 2, 0)
        self.birth_date_input = QDateEdit()
        self.birth_date_input.setDate(QDate.currentDate().addYears(-25))
        self.birth_date_input.setCalendarPopup(True)
        basic_info_layout.addWidget(self.birth_date_input, 2, 1)

        # الجنس
        basic_info_layout.addWidget(QLabel("الجنس:"), 2, 2)
        self.gender_input = QComboBox()
        self.gender_input.addItems(["ذكر", "أنثى"])
        basic_info_layout.addWidget(self.gender_input, 2, 3)

        basic_info_group.setLayout(basic_info_layout)

        # مجموعة معلومات الاتصال والعنوان
        contact_info_group = QGroupBox("📍 معلومات الاتصال والعنوان")
        contact_info_layout = QGridLayout()

        # المحافظة
        contact_info_layout.addWidget(QLabel("المحافظة:"), 0, 0)
        self.governorate_input = QComboBox()
        self.governorate_input.addItems([
            "صنعاء", "عدن", "تعز", "الحديدة", "إب", "ذمار", "المحويت", "حجة", "صعدة", "عمران",
            "البيضاء", "مأرب", "الجوف", "شبوة", "أبين", "لحج", "الضالع", "ريمة", "المهرة", "حضرموت", "سقطرى"
        ])
        contact_info_layout.addWidget(self.governorate_input, 0, 1)

        # المدينة
        contact_info_layout.addWidget(QLabel("المدينة:"), 0, 2)
        self.city_input = QLineEdit()
        self.city_input.setPlaceholderText("مثال: الثورة")
        contact_info_layout.addWidget(self.city_input, 0, 3)

        # العنوان التفصيلي
        contact_info_layout.addWidget(QLabel("العنوان التفصيلي:"), 1, 0)
        self.address_input = QTextEdit()
        self.address_input.setMaximumHeight(60)
        self.address_input.setPlaceholderText("مثال: شارع الزبيري، بجانب البنك الأهلي...")
        contact_info_layout.addWidget(self.address_input, 1, 1, 1, 3)

        # هاتف إضافي
        contact_info_layout.addWidget(QLabel("هاتف إضافي:"), 2, 0)
        self.phone2_input = QLineEdit()
        self.phone2_input.setPlaceholderText("مثال: 733123456")
        contact_info_layout.addWidget(self.phone2_input, 2, 1)

        # الموقع الإلكتروني
        contact_info_layout.addWidget(QLabel("الموقع الإلكتروني:"), 2, 2)
        self.website_input = QLineEdit()
        self.website_input.setPlaceholderText("مثال: www.example.com")
        contact_info_layout.addWidget(self.website_input, 2, 3)

        contact_info_group.setLayout(contact_info_layout)

        # مجموعة المعلومات التجارية
        business_info_group = QGroupBox("💼 المعلومات التجارية")
        business_info_layout = QGridLayout()

        # تصنيف العميل
        business_info_layout.addWidget(QLabel("* تصنيف العميل:"), 0, 0)
        self.category_input = QComboBox()
        self.category_input.addItems(["عميل عادي", "عميل مميز", "عميل VIP", "عميل تجاري"])
        business_info_layout.addWidget(self.category_input, 0, 1)

        # حد الائتمان
        business_info_layout.addWidget(QLabel("حد الائتمان:"), 0, 2)
        self.credit_limit_input = QDoubleSpinBox()
        self.credit_limit_input.setRange(0, 999999999)
        self.credit_limit_input.setSuffix(" ريال")
        business_info_layout.addWidget(self.credit_limit_input, 0, 3)

        # نسبة الخصم
        business_info_layout.addWidget(QLabel("نسبة الخصم:"), 1, 0)
        self.discount_rate_input = QDoubleSpinBox()
        self.discount_rate_input.setRange(0, 100)
        self.discount_rate_input.setSuffix(" %")
        business_info_layout.addWidget(self.discount_rate_input, 1, 1)

        # طريقة الدفع المفضلة
        business_info_layout.addWidget(QLabel("طريقة الدفع المفضلة:"), 1, 2)
        self.payment_method_input = QComboBox()
        self.payment_method_input.addItems(["نقدي", "آجل", "بطاقة ائتمان", "تحويل بنكي", "شيك"])
        business_info_layout.addWidget(self.payment_method_input, 1, 3)

        # ملاحظات
        business_info_layout.addWidget(QLabel("ملاحظات:"), 2, 0)
        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        self.notes_input.setPlaceholderText("أي ملاحظات إضافية عن العميل...")
        business_info_layout.addWidget(self.notes_input, 2, 1, 1, 3)

        business_info_group.setLayout(business_info_layout)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("💾 حفظ العميل")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_customer)

        clear_btn = QPushButton("🗑️ مسح النموذج")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        clear_btn.clicked.connect(self.clear_form)

        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(clear_btn)

        layout.addWidget(basic_info_group)
        layout.addWidget(contact_info_group)
        layout.addWidget(business_info_group)
        layout.addLayout(buttons_layout)
        layout.addStretch()

        tab.setLayout(layout)
        return tab

    def create_customer_categories_tab(self):
        """إنشاء تبويب تصنيفات العملاء"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة إدارة التصنيفات
        categories_group = QGroupBox("🏷️ إدارة تصنيفات العملاء")
        categories_layout = QVBoxLayout()

        # جدول التصنيفات
        self.categories_table = QTableWidget()
        self.categories_table.setColumnCount(5)
        self.categories_table.setHorizontalHeaderLabels([
            "اسم التصنيف", "الوصف", "نسبة الخصم الافتراضية", "حد الائتمان الافتراضي", "عدد العملاء"
        ])

        # تنسيق الجدول
        self.categories_table.horizontalHeader().setStretchLastSection(True)
        self.categories_table.setAlternatingRowColors(True)
        self.categories_table.setSelectionBehavior(QTableWidget.SelectRows)

        categories_layout.addWidget(self.categories_table)

        # أزرار إدارة التصنيفات
        categories_buttons = QHBoxLayout()

        add_category_btn = QPushButton("➕ إضافة تصنيف")
        add_category_btn.clicked.connect(self.add_category)

        edit_category_btn = QPushButton("✏️ تعديل تصنيف")
        edit_category_btn.clicked.connect(self.edit_category)

        delete_category_btn = QPushButton("🗑️ حذف تصنيف")
        delete_category_btn.clicked.connect(self.delete_category)

        categories_buttons.addWidget(add_category_btn)
        categories_buttons.addWidget(edit_category_btn)
        categories_buttons.addWidget(delete_category_btn)
        categories_buttons.addStretch()

        categories_layout.addLayout(categories_buttons)
        categories_group.setLayout(categories_layout)

        layout.addWidget(categories_group)
        layout.addStretch()

        tab.setLayout(layout)
        return tab

    def create_customer_data_tab(self):
        """إنشاء تبويب بيانات العملاء"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة عرض بيانات العملاء
        data_group = QGroupBox("📋 عرض وإدارة بيانات العملاء")
        data_layout = QVBoxLayout()

        # شريط أدوات بيانات العملاء
        toolbar_layout = QHBoxLayout()

        # أزرار التحكم
        refresh_data_btn = QPushButton("🔄 تحديث البيانات")
        refresh_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_data_btn.clicked.connect(self.refresh_customer_data)

        export_data_btn = QPushButton("📤 تصدير البيانات")
        export_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        export_data_btn.clicked.connect(self.export_customer_data)

        import_data_btn = QPushButton("📥 استيراد البيانات")
        import_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        import_data_btn.clicked.connect(self.import_customer_data)

        backup_data_btn = QPushButton("💾 نسخ احتياطي")
        backup_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e8650e;
            }
        """)
        backup_data_btn.clicked.connect(self.backup_customer_data)

        toolbar_layout.addWidget(refresh_data_btn)
        toolbar_layout.addWidget(export_data_btn)
        toolbar_layout.addWidget(import_data_btn)
        toolbar_layout.addWidget(backup_data_btn)
        toolbar_layout.addStretch()

        # جدول بيانات العملاء المفصل
        self.customer_data_table = QTableWidget()
        self.customer_data_table.setColumnCount(12)
        self.customer_data_table.setHorizontalHeaderLabels([
            "رقم العميل", "الاسم الكامل", "الشركة", "الهاتف", "البريد الإلكتروني",
            "العنوان", "المدينة", "التصنيف", "الرصيد", "حد الائتمان",
            "تاريخ التسجيل", "آخر معاملة"
        ])

        # تنسيق الجدول
        self.customer_data_table.horizontalHeader().setStretchLastSection(True)
        self.customer_data_table.setAlternatingRowColors(True)
        self.customer_data_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.customer_data_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 11px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
                font-size: 12px;
            }
        """)

        # إحصائيات البيانات
        stats_layout = QHBoxLayout()

        self.total_customers_label = QLabel("👥 إجمالي العملاء: 0")
        self.total_customers_label.setStyleSheet("""
            QLabel {
                background-color: #e3f2fd;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                color: #1976d2;
            }
        """)

        self.active_customers_label = QLabel("✅ العملاء النشطون: 0")
        self.active_customers_label.setStyleSheet("""
            QLabel {
                background-color: #e8f5e8;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                color: #2e7d32;
            }
        """)

        self.total_balance_label = QLabel("💰 إجمالي الأرصدة: 0 ريال")
        self.total_balance_label.setStyleSheet("""
            QLabel {
                background-color: #fff3e0;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                color: #f57c00;
            }
        """)

        stats_layout.addWidget(self.total_customers_label)
        stats_layout.addWidget(self.active_customers_label)
        stats_layout.addWidget(self.total_balance_label)
        stats_layout.addStretch()

        data_layout.addLayout(toolbar_layout)
        data_layout.addWidget(self.customer_data_table)
        data_layout.addLayout(stats_layout)
        data_group.setLayout(data_layout)

        layout.addWidget(data_group)

        tab.setLayout(layout)
        return tab

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        from .customer_functions import create_reports_tab_function
        return create_reports_tab_function(self)

    # ربط الوظائف من الملفات المنفصلة
    def load_sample_data(self):
        """تحميل البيانات النموذجية"""
        from .customer_functions import load_sample_data_function
        load_sample_data_function(self)

    def update_customers_table(self):
        """تحديث جدول العملاء"""
        from .customer_functions import update_customers_table_function
        update_customers_table_function(self)

    def update_categories_table(self):
        """تحديث جدول التصنيفات"""
        from .customer_functions import update_categories_table_function
        update_categories_table_function(self)

    def update_status_bar(self):
        """تحديث شريط الحالة"""
        from .customer_functions import update_status_bar_function
        update_status_bar_function(self)

    def filter_customers(self):
        """تصفية العملاء"""
        from .customer_functions import filter_customers_function
        filter_customers_function(self)

    def clear_search(self):
        """مسح البحث"""
        from .customer_functions import clear_search_function
        clear_search_function(self)

    def new_customer(self):
        """إضافة عميل جديد"""
        from .customer_functions import new_customer_function
        new_customer_function(self)

    def clear_form(self):
        """مسح النموذج"""
        from .customer_functions import clear_form_function
        clear_form_function(self)

    def save_customer(self):
        """حفظ العميل"""
        from .customer_operations import save_customer_function
        save_customer_function(self)

    def edit_customer(self):
        """تعديل العميل"""
        from .customer_operations import edit_customer_function
        edit_customer_function(self)

    def edit_selected_customer(self, item):
        """تعديل العميل بالنقر المزدوج"""
        from .customer_operations import edit_selected_customer_function
        edit_selected_customer_function(self, item)

    def delete_customer(self):
        """حذف العميل"""
        from .customer_operations import delete_customer_function
        delete_customer_function(self)

    def search_customers(self):
        """البحث في العملاء"""
        from .customer_operations import search_customers_function
        search_customers_function(self)

    def refresh_data(self):
        """تحديث البيانات"""
        from .customer_operations import refresh_data_function
        refresh_data_function(self)

    def update_selection_info(self):
        """تحديث معلومات التحديد"""
        from .customer_operations import update_selection_info_function
        update_selection_info_function(self)

    def generate_all_customers_report(self):
        """تقرير جميع العملاء"""
        from .customer_operations import generate_all_customers_report_function
        generate_all_customers_report_function(self)

    def generate_customers_by_category_report(self):
        """تقرير العملاء حسب التصنيف"""
        from .customer_operations import generate_customers_by_category_report_function
        generate_customers_by_category_report_function(self)

    def generate_customers_statistics(self):
        """إحصائيات العملاء"""
        from .customer_operations import generate_customers_statistics_function
        generate_customers_statistics_function(self)

    def generate_customers_report(self):
        """إنتاج تقرير العملاء"""
        self.generate_all_customers_report()

    def export_customers(self):
        """تصدير العملاء"""
        from .customer_operations import export_customers_function
        export_customers_function(self)

    def import_customers(self):
        """استيراد العملاء"""
        from .customer_operations import import_customers_function
        import_customers_function(self)

    def export_report(self):
        """تصدير التقرير"""
        from .customer_operations import export_report_function
        export_report_function(self)

    def print_report(self):
        """طباعة التقرير"""
        from .customer_operations import print_report_function
        print_report_function(self)

    def clear_report(self):
        """مسح التقرير"""
        from .customer_operations import clear_report_function
        clear_report_function(self)

    def show_reports(self):
        """عرض التقارير"""
        from .customer_operations import show_reports_function
        show_reports_function(self)

    def show_about(self):
        """حول البرنامج"""
        from .customer_operations import show_about_function
        show_about_function(self)

    def add_category(self):
        """إضافة تصنيف"""
        from .customer_operations import add_category_function
        add_category_function(self)

    def edit_category(self):
        """تعديل تصنيف"""
        from .customer_operations import edit_category_function
        edit_category_function(self)

    def delete_category(self):
        """حذف تصنيف"""
        from .customer_operations import delete_category_function
        delete_category_function(self)

    # وظائف بيانات العملاء الجديدة
    def refresh_customer_data(self):
        """تحديث بيانات العملاء"""
        self.update_customer_data_table()
        self.update_customer_data_stats()
        QMessageBox.information(self, "تم", "تم تحديث بيانات العملاء بنجاح")

    def update_customer_data_table(self):
        """تحديث جدول بيانات العملاء المفصل"""
        self.customer_data_table.setRowCount(len(self.customers_data))

        for row, customer in enumerate(self.customers_data):
            self.customer_data_table.setItem(row, 0, QTableWidgetItem(customer.get("customer_id", "")))
            self.customer_data_table.setItem(row, 1, QTableWidgetItem(customer.get("name", "")))
            self.customer_data_table.setItem(row, 2, QTableWidgetItem(customer.get("company", "")))
            self.customer_data_table.setItem(row, 3, QTableWidgetItem(customer.get("phone", "")))
            self.customer_data_table.setItem(row, 4, QTableWidgetItem(customer.get("email", "")))
            self.customer_data_table.setItem(row, 5, QTableWidgetItem(customer.get("address", "")))
            self.customer_data_table.setItem(row, 6, QTableWidgetItem(customer.get("city", "")))
            self.customer_data_table.setItem(row, 7, QTableWidgetItem(customer.get("category", "")))
            self.customer_data_table.setItem(row, 8, QTableWidgetItem(f"{customer.get('balance', 0):,.2f}"))
            self.customer_data_table.setItem(row, 9, QTableWidgetItem(f"{customer.get('credit_limit', 0):,.2f}"))
            self.customer_data_table.setItem(row, 10, QTableWidgetItem(customer.get("registration_date", "")))
            self.customer_data_table.setItem(row, 11, QTableWidgetItem(customer.get("last_transaction", "")))

    def update_customer_data_stats(self):
        """تحديث إحصائيات بيانات العملاء"""
        total_customers = len(self.customers_data)
        active_customers = len([c for c in self.customers_data if c.get("status") == "نشط"])
        total_balance = sum(c.get("balance", 0) for c in self.customers_data)

        self.total_customers_label.setText(f"👥 إجمالي العملاء: {total_customers}")
        self.active_customers_label.setText(f"✅ العملاء النشطون: {active_customers}")
        self.total_balance_label.setText(f"💰 إجمالي الأرصدة: {total_balance:,.2f} ريال")

    def export_customer_data(self):
        """تصدير بيانات العملاء"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير بيانات العملاء",
                f"customer_data_{QDate.currentDate().toString('yyyy-MM-dd')}.csv",
                "CSV Files (*.csv);;Excel Files (*.xlsx);;All Files (*)"
            )

            if file_path:
                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = [
                        'رقم العميل', 'الاسم الكامل', 'الشركة', 'الهاتف', 'البريد الإلكتروني',
                        'العنوان', 'المدينة', 'التصنيف', 'الرصيد', 'حد الائتمان',
                        'تاريخ التسجيل', 'آخر معاملة'
                    ]
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()

                    for customer in self.customers_data:
                        writer.writerow({
                            'رقم العميل': customer.get("customer_id", ""),
                            'الاسم الكامل': customer.get("name", ""),
                            'الشركة': customer.get("company", ""),
                            'الهاتف': customer.get("phone", ""),
                            'البريد الإلكتروني': customer.get("email", ""),
                            'العنوان': customer.get("address", ""),
                            'المدينة': customer.get("city", ""),
                            'التصنيف': customer.get("category", ""),
                            'الرصيد': customer.get("balance", 0),
                            'حد الائتمان': customer.get("credit_limit", 0),
                            'تاريخ التسجيل': customer.get("registration_date", ""),
                            'آخر معاملة': customer.get("last_transaction", "")
                        })

                QMessageBox.information(self, "نجح", f"تم تصدير بيانات العملاء إلى:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير البيانات:\n{str(e)}")

    def import_customer_data(self):
        """استيراد بيانات العملاء"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة استيراد بيانات العملاء قيد التطوير")

    def backup_customer_data(self):
        """إنشاء نسخة احتياطية من بيانات العملاء"""
        try:
            import json
            from datetime import datetime

            backup_data = {
                "backup_date": datetime.now().isoformat(),
                "customers_count": len(self.customers_data),
                "customers": self.customers_data
            }

            file_path, _ = QFileDialog.getSaveFileName(
                self, "حفظ النسخة الاحتياطية",
                f"customers_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "JSON Files (*.json);;All Files (*)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "نجح", f"تم إنشاء النسخة الاحتياطية:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}")

    def open_receipt_voucher(self):
        """فتح سند قبض للعميل المحدد"""
        try:
            # التحقق من وجود عميل محدد
            current_row = self.customers_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى تحديد عميل أولاً")
                return

            # الحصول على بيانات العميل
            customer_id = self.customers_table.item(current_row, 0).text()
            customer_name = self.customers_table.item(current_row, 1).text()

            # فتح سند القبض
            try:
                from ..vouchers.receipt_voucher import ReceiptVoucherDialog
                dialog = ReceiptVoucherDialog(self.db_manager, self)

                # تعبئة بيانات العميل
                dialog.beneficiary_edit.setText(customer_name)
                dialog.beneficiary_id_edit.setText(customer_id)
                dialog.description_edit.setPlainText(f"سند قبض من العميل: {customer_name}")

                dialog.exec_()

            except ImportError:
                # في حالة عدم وجود الوحدة، عرض رسالة
                QMessageBox.information(
                    self,
                    "سند قبض",
                    f"سيتم فتح سند قبض للعميل:\n{customer_name}\nرقم العميل: {customer_id}\n\nالوحدة قيد التطوير."
                )

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند القبض:\n{str(e)}")


def main():
    """اختبار نظام إدارة العملاء"""
    import sys

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass

    window = CustomerManagementWindow(MockDBManager())
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
