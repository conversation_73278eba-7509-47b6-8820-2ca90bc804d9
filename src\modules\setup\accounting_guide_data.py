#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف البيانات والتقارير لشاشة إعداد الدليل المحاسبي
Data and Reports Functions for Accounting Guide Setup
"""

from PyQt5.QtWidgets import QMessageBox, QTableWidgetItem, QTreeWidgetItem
from PyQt5.QtCore import Qt
from datetime import datetime

def load_data_function(self):
    """تحميل البيانات المحفوظة"""
    try:
        # تحميل قوائم البيانات
        self.load_currencies()
        self.load_sample_accounts_data()
        
        # بناء شجرة الحسابات
        self.build_accounts_tree()
        
        # تحديث الإحصائيات
        self.update_tree_statistics()
        
    except Exception as e:
        print(f"خطأ في تحميل البيانات: {e}")

def load_currencies_function(self):
    """تحميل قائمة العملات"""
    currencies = [
        "الريال اليمني (YER)", "الدولار الأمريكي (USD)", "الريال السعودي (SAR)",
        "الدرهم الإماراتي (AED)", "اليورو (EUR)", "الجنيه الإسترليني (GBP)"
    ]
    
    self.sub_account_currency.clear()
    for currency in currencies:
        self.sub_account_currency.addItem(currency)
    
    # تعيين العملة الافتراضية
    self.sub_account_currency.setCurrentText("الريال اليمني (YER)")

def load_sample_accounts_data_function(self):
    """تحميل البيانات النموذجية للحسابات"""
    # الحسابات الرئيسية النموذجية
    main_accounts = [
        ("الأصول المتداولة", "Current Assets", "1000", "الأصول", "مدين", 1, True, True),
        ("الأصول الثابتة", "Fixed Assets", "2000", "الأصول", "مدين", 1, True, False),
        ("الخصوم المتداولة", "Current Liabilities", "3000", "الخصوم", "دائن", 1, True, True),
        ("الخصوم طويلة الأجل", "Long-term Liabilities", "4000", "الخصوم", "دائن", 1, True, False),
        ("رأس المال", "Capital", "5000", "حقوق الملكية", "دائن", 1, True, True),
        ("الإيرادات", "Revenues", "6000", "الإيرادات", "دائن", 1, True, True),
        ("المصروفات التشغيلية", "Operating Expenses", "7000", "المصروفات", "مدين", 1, True, True),
        ("المصروفات الإدارية", "Administrative Expenses", "8000", "المصروفات", "مدين", 1, True, True)
    ]
    
    self.main_accounts_table.setRowCount(len(main_accounts))
    for row, (name, name_en, number, type_name, nature, level, active, direct_entry) in enumerate(main_accounts):
        self.main_accounts_table.setItem(row, 0, QTableWidgetItem(name))
        self.main_accounts_table.setItem(row, 1, QTableWidgetItem(number))
        self.main_accounts_table.setItem(row, 2, QTableWidgetItem(type_name))
        self.main_accounts_table.setItem(row, 3, QTableWidgetItem(nature))
        self.main_accounts_table.setItem(row, 4, QTableWidgetItem(str(level)))
        self.main_accounts_table.setItem(row, 5, QTableWidgetItem("0"))  # عدد الحسابات الفرعية
        
        status = "نشط"
        if direct_entry:
            status += " (يقبل القيد)"
        elif not active:
            status = "غير نشط"
        self.main_accounts_table.setItem(row, 6, QTableWidgetItem(status))
    
    # تحديث قائمة الحسابات الرئيسية
    self.update_parent_accounts()
    
    # الحسابات الفرعية النموذجية
    sub_accounts = [
        ("النقدية في الصندوق", "1001", "الأصول المتداولة", "الريال اليمني (YER)", 50000.00, "2024-01-01", True, True),
        ("البنك الأهلي اليمني", "1002", "الأصول المتداولة", "الريال اليمني (YER)", 500000.00, "2024-01-01", True, True),
        ("حساب العملاء", "1003", "الأصول المتداولة", "الريال اليمني (YER)", 200000.00, "2024-01-01", True, True),
        ("المخزون", "1004", "الأصول المتداولة", "الريال اليمني (YER)", 300000.00, "2024-01-01", True, True),
        ("الأثاث والمعدات", "2001", "الأصول الثابتة", "الريال اليمني (YER)", 150000.00, "2024-01-01", True, True),
        ("السيارات", "2002", "الأصول الثابتة", "الريال اليمني (YER)", 200000.00, "2024-01-01", True, True),
        ("حساب الموردين", "3001", "الخصوم المتداولة", "الريال اليمني (YER)", -100000.00, "2024-01-01", True, True),
        ("المرتبات المستحقة", "3002", "الخصوم المتداولة", "الريال اليمني (YER)", -50000.00, "2024-01-01", True, True),
        ("رأس المال المدفوع", "5001", "رأس المال", "الريال اليمني (YER)", -1000000.00, "2024-01-01", True, True),
        ("إيرادات المبيعات", "6001", "الإيرادات", "الريال اليمني (YER)", -800000.00, "2024-01-01", True, True),
        ("مصروفات الرواتب", "7001", "المصروفات التشغيلية", "الريال اليمني (YER)", 120000.00, "2024-01-01", True, True),
        ("مصروفات الإيجار", "8001", "المصروفات الإدارية", "الريال اليمني (YER)", 60000.00, "2024-01-01", True, True)
    ]
    
    self.sub_accounts_table.setRowCount(len(sub_accounts))
    for row, (name, number, parent, currency, opening_balance, date, active, show_reports) in enumerate(sub_accounts):
        self.sub_accounts_table.setItem(row, 0, QTableWidgetItem(name))
        self.sub_accounts_table.setItem(row, 1, QTableWidgetItem(number))
        self.sub_accounts_table.setItem(row, 2, QTableWidgetItem(parent))
        self.sub_accounts_table.setItem(row, 3, QTableWidgetItem(currency))
        self.sub_accounts_table.setItem(row, 4, QTableWidgetItem(f"{opening_balance:,.2f}"))
        self.sub_accounts_table.setItem(row, 5, QTableWidgetItem(f"{opening_balance:,.2f}"))  # الرصيد الحالي (نفس الافتتاحي)
        self.sub_accounts_table.setItem(row, 6, QTableWidgetItem(date))
        
        status = "نشط"
        if show_reports:
            status += " (يظهر في التقارير)"
        elif not active:
            status = "غير نشط"
        self.sub_accounts_table.setItem(row, 7, QTableWidgetItem(status))
    
    # تحديث عدد الحسابات الفرعية
    self.update_sub_accounts_count()

def update_parent_accounts_function(self):
    """تحديث قائمة الحسابات الرئيسية"""
    self.sub_account_parent.clear()
    for row in range(self.main_accounts_table.rowCount()):
        account_name = self.main_accounts_table.item(row, 0).text()
        self.sub_account_parent.addItem(account_name)

def update_sub_accounts_count_function(self):
    """تحديث عدد الحسابات الفرعية لكل حساب رئيسي"""
    for main_row in range(self.main_accounts_table.rowCount()):
        main_account_name = self.main_accounts_table.item(main_row, 0).text()
        count = 0
        
        for sub_row in range(self.sub_accounts_table.rowCount()):
            parent_account = self.sub_accounts_table.item(sub_row, 2).text()
            if parent_account == main_account_name:
                count += 1
        
        self.main_accounts_table.setItem(main_row, 5, QTableWidgetItem(str(count)))

def build_accounts_tree_function(self):
    """بناء شجرة الحسابات"""
    self.accounts_tree.clear()
    
    # إنشاء عقد الحسابات الرئيسية
    for main_row in range(self.main_accounts_table.rowCount()):
        main_name = self.main_accounts_table.item(main_row, 0).text()
        main_number = self.main_accounts_table.item(main_row, 1).text()
        main_type = self.main_accounts_table.item(main_row, 2).text()
        main_status = self.main_accounts_table.item(main_row, 6).text()
        
        # حساب إجمالي رصيد الحساب الرئيسي
        total_balance = 0.0
        for sub_row in range(self.sub_accounts_table.rowCount()):
            parent_account = self.sub_accounts_table.item(sub_row, 2).text()
            if parent_account == main_name:
                balance_text = self.sub_accounts_table.item(sub_row, 5).text().replace(',', '')
                total_balance += float(balance_text)
        
        main_item = QTreeWidgetItem([main_name, main_number, main_type, f"{total_balance:,.2f}", main_status])
        main_item.setExpanded(True)
        
        # إضافة الحسابات الفرعية
        for sub_row in range(self.sub_accounts_table.rowCount()):
            parent_account = self.sub_accounts_table.item(sub_row, 2).text()
            if parent_account == main_name:
                sub_name = self.sub_accounts_table.item(sub_row, 0).text()
                sub_number = self.sub_accounts_table.item(sub_row, 1).text()
                sub_currency = self.sub_accounts_table.item(sub_row, 3).text()
                sub_balance = self.sub_accounts_table.item(sub_row, 5).text()
                sub_status = self.sub_accounts_table.item(sub_row, 7).text()
                
                sub_item = QTreeWidgetItem([sub_name, sub_number, sub_currency, sub_balance, sub_status])
                main_item.addChild(sub_item)
        
        self.accounts_tree.addTopLevelItem(main_item)

def update_tree_statistics_function(self):
    """تحديث إحصائيات الشجرة"""
    main_accounts = self.main_accounts_table.rowCount()
    sub_accounts = self.sub_accounts_table.rowCount()
    
    stats_text = f"📊 إحصائيات الدليل: {main_accounts} حساب رئيسي، {sub_accounts} حساب فرعي"
    self.tree_info.setText(stats_text)

def on_account_selected_function(self, item, column):
    """معالجة اختيار حساب من الشجرة"""
    account_name = item.text(0)
    account_number = item.text(1)
    print(f"تم اختيار الحساب: {account_name} ({account_number})")

def on_account_double_clicked_function(self, item, column):
    """معالجة النقر المزدوج على حساب في الشجرة"""
    account_name = item.text(0)
    account_number = item.text(1)
    account_type = item.text(2)
    account_balance = item.text(3)
    account_status = item.text(4)
    
    QMessageBox.information(self, "تفاصيل الحساب", 
                           f"📊 الحساب: {account_name}\n"
                           f"🔢 الرقم: {account_number}\n"
                           f"🏷️ النوع: {account_type}\n"
                           f"💰 الرصيد: {account_balance}\n"
                           f"📊 الحالة: {account_status}")

def refresh_accounts_tree_function(self):
    """تحديث شجرة الحسابات"""
    self.build_accounts_tree()
    self.update_tree_statistics()
    QMessageBox.information(self, "تم", "تم تحديث شجرة الحسابات بنجاح")

def print_accounting_guide_function(self):
    """طباعة الدليل المحاسبي"""
    QMessageBox.information(self, "قيد التطوير", 
                           "🖨️ ميزة طباعة الدليل المحاسبي قيد التطوير\n"
                           "سيتم إضافتها في الإصدارات القادمة")

def generate_chart_of_accounts_function(self):
    """إنتاج دليل الحسابات الكامل"""
    try:
        report = f"""
📋 دليل الحسابات الكامل
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

"""
        
        # تجميع الحسابات حسب النوع
        account_types = {}
        
        for main_row in range(self.main_accounts_table.rowCount()):
            main_name = self.main_accounts_table.item(main_row, 0).text()
            main_number = self.main_accounts_table.item(main_row, 1).text()
            main_type = self.main_accounts_table.item(main_row, 2).text()
            
            if main_type not in account_types:
                account_types[main_type] = []
            
            # إضافة الحساب الرئيسي
            account_types[main_type].append({
                'name': main_name,
                'number': main_number,
                'level': 'رئيسي',
                'sub_accounts': []
            })
            
            # إضافة الحسابات الفرعية
            for sub_row in range(self.sub_accounts_table.rowCount()):
                parent_account = self.sub_accounts_table.item(sub_row, 2).text()
                if parent_account == main_name:
                    sub_name = self.sub_accounts_table.item(sub_row, 0).text()
                    sub_number = self.sub_accounts_table.item(sub_row, 1).text()
                    account_types[main_type][-1]['sub_accounts'].append({
                        'name': sub_name,
                        'number': sub_number
                    })
        
        # إنتاج التقرير
        for type_name, accounts in account_types.items():
            report += f"\n🏷️ {type_name}:\n"
            report += "-" * 40 + "\n"
            
            for account in accounts:
                report += f"   {account['number']} - {account['name']} ({account['level']})\n"
                
                for sub_account in account['sub_accounts']:
                    report += f"      {sub_account['number']} - {sub_account['name']}\n"
            
            report += "\n"
        
        # إحصائيات
        total_main = self.main_accounts_table.rowCount()
        total_sub = self.sub_accounts_table.rowCount()
        
        report += f"""
{'='*60}
📊 إحصائيات الدليل:
   🏛️ إجمالي الحسابات الرئيسية: {total_main}
   📋 إجمالي الحسابات الفرعية: {total_sub}
   📊 إجمالي الحسابات: {total_main + total_sub}
"""
        
        self.reports_display.setPlainText(report)
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إنتاج دليل الحسابات:\n{str(e)}")

def generate_trial_balance_function(self):
    """إنتاج ميزان المراجعة"""
    try:
        report = f"""
⚖️ ميزان المراجعة
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{'رقم الحساب':<12} {'اسم الحساب':<25} {'مدين':<15} {'دائن':<15}
{'-'*70}
"""
        
        total_debit = 0.0
        total_credit = 0.0
        
        for sub_row in range(self.sub_accounts_table.rowCount()):
            account_name = self.sub_accounts_table.item(sub_row, 0).text()
            account_number = self.sub_accounts_table.item(sub_row, 1).text()
            balance_text = self.sub_accounts_table.item(sub_row, 5).text().replace(',', '')
            balance = float(balance_text)
            
            if balance >= 0:
                debit = balance
                credit = 0.0
                total_debit += debit
            else:
                debit = 0.0
                credit = abs(balance)
                total_credit += credit
            
            report += f"{account_number:<12} {account_name:<25} {debit:>12,.2f} {credit:>12,.2f}\n"
        
        report += f"""
{'-'*70}
{'الإجمالي':<37} {total_debit:>12,.2f} {total_credit:>12,.2f}
{'='*70}

📊 ملخص ميزان المراجعة:
   💰 إجمالي المدين: {total_debit:,.2f}
   💰 إجمالي الدائن: {total_credit:,.2f}
   ⚖️ الفرق: {abs(total_debit - total_credit):,.2f}
   ✅ الميزان: {'متوازن' if abs(total_debit - total_credit) < 0.01 else 'غير متوازن'}
"""
        
        self.reports_display.setPlainText(report)
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إنتاج ميزان المراجعة:\n{str(e)}")

def generate_account_balances_function(self):
    """إنتاج تقرير أرصدة الحسابات"""
    try:
        report = f"""
💰 تقرير أرصدة الحسابات
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

"""
        
        # تجميع الأرصدة حسب نوع الحساب
        account_balances = {}
        
        for sub_row in range(self.sub_accounts_table.rowCount()):
            account_name = self.sub_accounts_table.item(sub_row, 0).text()
            account_number = self.sub_accounts_table.item(sub_row, 1).text()
            parent_account = self.sub_accounts_table.item(sub_row, 2).text()
            balance_text = self.sub_accounts_table.item(sub_row, 5).text().replace(',', '')
            balance = float(balance_text)
            
            if parent_account not in account_balances:
                account_balances[parent_account] = []
            
            account_balances[parent_account].append({
                'name': account_name,
                'number': account_number,
                'balance': balance
            })
        
        # إنتاج التقرير
        for parent_name, accounts in account_balances.items():
            total_balance = sum(acc['balance'] for acc in accounts)
            report += f"\n🏛️ {parent_name} (الإجمالي: {total_balance:,.2f}):\n"
            report += "-" * 50 + "\n"
            
            for account in accounts:
                report += f"   {account['number']} - {account['name']}: {account['balance']:>15,.2f}\n"
            
            report += "\n"
        
        self.reports_display.setPlainText(report)
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إنتاج تقرير الأرصدة:\n{str(e)}")

def show_help_function(self):
    """عرض المساعدة"""
    help_text = """
📚 مساعدة إعداد الدليل المحاسبي

📋 الوظائف المتاحة:

🏛️ الحسابات الرئيسية:
• إنشاء حسابات رئيسية جديدة
• تحديد نوع الحساب وطبيعته
• تعيين مستوى الحساب في الهيكل
• تحديد إمكانية القيد المباشر

📋 الحسابات الفرعية:
• إنشاء حسابات فرعية تحت الرئيسية
• تحديد الرصيد الافتتاحي
• اختيار عملة الحساب
• تحديد ظهور الحساب في التقارير

🏷️ أنواع الحسابات:
• تفعيل أنواع الحسابات المطلوبة
• إعداد نظام ترقيم الحسابات
• استخدام قوالب جاهزة للدليل
• تخصيص إعدادات الترقيم

📊 شجرة الحسابات:
• عرض الهيكل الهرمي للحسابات
• توسيع وطي الفروع
• عرض الأرصدة والحالات
• النقر المزدوج لعرض التفاصيل

📊 التقارير والتحليل:
• دليل الحسابات الكامل
• ميزان المراجعة
• أرصدة الحسابات
• إحصائيات الدليل

💡 نصائح:
• استخدم نظام ترقيم منطقي ومتسق
• تأكد من توازن ميزان المراجعة
• راجع الدليل بانتظام وحدثه
• احتفظ بنسخ احتياطية من الدليل
• استخدم القوالب الجاهزة لتوفير الوقت
"""
    
    QMessageBox.information(self, "المساعدة", help_text)
