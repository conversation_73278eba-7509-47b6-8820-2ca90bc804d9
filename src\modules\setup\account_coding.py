#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة ترميز الحسابات
Account Coding Setup Screen
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QMessageBox, 
                             QFrame, QSplitter, QTextEdit, QTabWidget, QWidget,
                             QTreeWidget, QTreeWidgetItem, QListWidget, QListWidgetItem,
                             QProgressBar, QSlider, QDateEdit, QRadioButton, QButtonGroup)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QColor, QIcon, QRegExpValidator
from PyQt5.QtCore import QRegExp

class AccountCodingDialog(QDialog):
    """شاشة ترميز الحسابات"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("ترميز الحسابات - نظام ترقيم وتصنيف الحسابات المحاسبية")
        self.setGeometry(50, 50, 1500, 950)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان الشاشة
        title_label = QLabel("🔢 ترميز الحسابات - نظام الترقيم والتصنيف المحاسبي")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 22px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات الإعداد
        tabs = QTabWidget()
        
        # تبويب أنظمة الترميز
        coding_systems_tab = self.create_coding_systems_tab()
        tabs.addTab(coding_systems_tab, "🔢 أنظمة الترميز")
        
        # تبويب قواعد الترميز
        coding_rules_tab = self.create_coding_rules_tab()
        tabs.addTab(coding_rules_tab, "📋 قواعد الترميز")
        
        # تبويب تطبيق الترميز
        apply_coding_tab = self.create_apply_coding_tab()
        tabs.addTab(apply_coding_tab, "✅ تطبيق الترميز")
        
        # تبويب التحقق والمراجعة
        validation_tab = self.create_validation_tab()
        tabs.addTab(validation_tab, "🔍 التحقق والمراجعة")
        
        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        tabs.addTab(reports_tab, "📊 تقارير الترميز")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_coding_systems_tab(self):
        """إنشاء تبويب أنظمة الترميز"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة اختيار نظام الترميز
        system_group = QGroupBox("🎯 اختيار نظام الترميز")
        system_layout = QGridLayout()
        
        # مجموعة أزرار الاختيار
        self.coding_system_group = QButtonGroup()
        
        # النظام العشري
        self.decimal_system = QRadioButton("النظام العشري (10, 20, 30...)")
        self.decimal_system.setChecked(True)
        self.coding_system_group.addButton(self.decimal_system, 1)
        system_layout.addWidget(self.decimal_system, 0, 0, 1, 2)
        
        # النظام المئوي
        self.hundred_system = QRadioButton("النظام المئوي (100, 200, 300...)")
        self.coding_system_group.addButton(self.hundred_system, 2)
        system_layout.addWidget(self.hundred_system, 1, 0, 1, 2)
        
        # النظام الألفي
        self.thousand_system = QRadioButton("النظام الألفي (1000, 2000, 3000...)")
        self.coding_system_group.addButton(self.thousand_system, 3)
        system_layout.addWidget(self.thousand_system, 2, 0, 1, 2)
        
        # النظام الهجين
        self.hybrid_system = QRadioButton("النظام الهجين (1-100, 2-100, 3-100...)")
        self.coding_system_group.addButton(self.hybrid_system, 4)
        system_layout.addWidget(self.hybrid_system, 3, 0, 1, 2)
        
        # النظام المخصص
        self.custom_system = QRadioButton("نظام مخصص")
        self.coding_system_group.addButton(self.custom_system, 5)
        system_layout.addWidget(self.custom_system, 4, 0, 1, 2)
        
        system_group.setLayout(system_layout)
        
        # مجموعة إعدادات النظام المختار
        settings_group = QGroupBox("⚙️ إعدادات النظام المختار")
        settings_layout = QGridLayout()
        
        # طول الرمز
        settings_layout.addWidget(QLabel("طول الرمز:"), 0, 0)
        self.code_length = QSpinBox()
        self.code_length.setRange(3, 15)
        self.code_length.setValue(4)
        settings_layout.addWidget(self.code_length, 0, 1)
        
        # عدد المستويات
        settings_layout.addWidget(QLabel("عدد المستويات:"), 0, 2)
        self.levels_count = QSpinBox()
        self.levels_count.setRange(2, 8)
        self.levels_count.setValue(3)
        settings_layout.addWidget(self.levels_count, 0, 3)
        
        # فاصل المستويات
        settings_layout.addWidget(QLabel("فاصل المستويات:"), 1, 0)
        self.level_separator = QComboBox()
        self.level_separator.addItems(["بدون فاصل", "نقطة (.)", "شرطة (-)", "شرطة مائلة (/)", "مسافة ( )"])
        settings_layout.addWidget(self.level_separator, 1, 1)
        
        # بادئة الرمز
        settings_layout.addWidget(QLabel("بادئة الرمز:"), 1, 2)
        self.code_prefix = QLineEdit()
        self.code_prefix.setPlaceholderText("مثال: ACC")
        self.code_prefix.setMaxLength(5)
        settings_layout.addWidget(self.code_prefix, 1, 3)
        
        # لاحقة الرمز
        settings_layout.addWidget(QLabel("لاحقة الرمز:"), 2, 0)
        self.code_suffix = QLineEdit()
        self.code_suffix.setPlaceholderText("مثال: YER")
        self.code_suffix.setMaxLength(5)
        settings_layout.addWidget(self.code_suffix, 2, 1)
        
        # ترقيم تلقائي
        self.auto_numbering = QCheckBox("ترقيم تلقائي للحسابات الجديدة")
        self.auto_numbering.setChecked(True)
        settings_layout.addWidget(self.auto_numbering, 2, 2, 1, 2)
        
        settings_group.setLayout(settings_layout)
        
        # مجموعة معاينة النظام
        preview_group = QGroupBox("👁️ معاينة النظام")
        preview_layout = QVBoxLayout()
        
        # منطقة المعاينة
        self.system_preview = QTextEdit()
        self.system_preview.setMaximumHeight(150)
        self.system_preview.setPlaceholderText("ستظهر معاينة نظام الترميز هنا...")
        self.system_preview.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        """)
        
        # زر تحديث المعاينة
        update_preview_btn = QPushButton("🔄 تحديث المعاينة")
        update_preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        update_preview_btn.clicked.connect(self.update_system_preview)
        
        preview_layout.addWidget(self.system_preview)
        preview_layout.addWidget(update_preview_btn)
        preview_group.setLayout(preview_layout)
        
        layout.addWidget(system_group)
        layout.addWidget(settings_group)
        layout.addWidget(preview_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab
        
    def create_coding_rules_tab(self):
        """إنشاء تبويب قواعد الترميز"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة قواعد المستويات
        levels_group = QGroupBox("📊 قواعد ترميز المستويات")
        levels_layout = QGridLayout()
        
        # جدول قواعد المستويات
        self.levels_table = QTableWidget()
        self.levels_table.setColumnCount(6)
        self.levels_table.setHorizontalHeaderLabels([
            "المستوى", "الوصف", "نمط الترميز", "البداية", "النهاية", "المثال"
        ])
        
        # تنسيق الجدول
        self.levels_table.horizontalHeader().setStretchLastSection(True)
        self.levels_table.setAlternatingRowColors(True)
        self.levels_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.levels_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        levels_layout.addWidget(self.levels_table, 0, 0, 1, 4)
        
        # أزرار إدارة المستويات
        add_level_btn = QPushButton("➕ إضافة مستوى")
        add_level_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_level_btn.clicked.connect(self.add_coding_level)
        
        edit_level_btn = QPushButton("✏️ تعديل مستوى")
        edit_level_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_level_btn.clicked.connect(self.edit_coding_level)
        
        delete_level_btn = QPushButton("🗑️ حذف مستوى")
        delete_level_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_level_btn.clicked.connect(self.delete_coding_level)
        
        levels_layout.addWidget(add_level_btn, 1, 0)
        levels_layout.addWidget(edit_level_btn, 1, 1)
        levels_layout.addWidget(delete_level_btn, 1, 2)
        
        levels_group.setLayout(levels_layout)
        
        # مجموعة قواعد التحقق
        validation_group = QGroupBox("✅ قواعد التحقق من صحة الترميز")
        validation_layout = QGridLayout()
        
        # قواعد التحقق
        self.check_uniqueness = QCheckBox("التحقق من عدم تكرار الأرقام")
        self.check_uniqueness.setChecked(True)
        validation_layout.addWidget(self.check_uniqueness, 0, 0, 1, 2)
        
        self.check_sequence = QCheckBox("التحقق من التسلسل المنطقي")
        self.check_sequence.setChecked(True)
        validation_layout.addWidget(self.check_sequence, 1, 0, 1, 2)
        
        self.check_format = QCheckBox("التحقق من تطابق النمط")
        self.check_format.setChecked(True)
        validation_layout.addWidget(self.check_format, 2, 0, 1, 2)
        
        self.check_hierarchy = QCheckBox("التحقق من الهيكل الهرمي")
        self.check_hierarchy.setChecked(True)
        validation_layout.addWidget(self.check_hierarchy, 3, 0, 1, 2)
        
        # إعدادات التحقق
        validation_layout.addWidget(QLabel("مستوى التحقق:"), 0, 2)
        self.validation_level = QComboBox()
        self.validation_level.addItems(["أساسي", "متوسط", "صارم", "مخصص"])
        self.validation_level.setCurrentText("متوسط")
        validation_layout.addWidget(self.validation_level, 0, 3)
        
        validation_layout.addWidget(QLabel("إجراء عند الخطأ:"), 1, 2)
        self.error_action = QComboBox()
        self.error_action.addItems(["تحذير", "منع", "تصحيح تلقائي", "تجاهل"])
        self.error_action.setCurrentText("تحذير")
        validation_layout.addWidget(self.error_action, 1, 3)
        
        validation_group.setLayout(validation_layout)
        
        layout.addWidget(levels_group)
        layout.addWidget(validation_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab

    def create_apply_coding_tab(self):
        """إنشاء تبويب تطبيق الترميز"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة الحسابات المتاحة للترميز
        accounts_group = QGroupBox("📋 الحسابات المتاحة للترميز")
        accounts_layout = QVBoxLayout()

        # جدول الحسابات
        self.accounts_table = QTableWidget()
        self.accounts_table.setColumnCount(6)
        self.accounts_table.setHorizontalHeaderLabels([
            "اسم الحساب", "الرمز الحالي", "الرمز المقترح", "النوع", "المستوى", "الحالة"
        ])

        # تنسيق الجدول
        self.accounts_table.horizontalHeader().setStretchLastSection(True)
        self.accounts_table.setAlternatingRowColors(True)
        self.accounts_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.accounts_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)

        accounts_layout.addWidget(self.accounts_table)

        # أزرار تطبيق الترميز
        apply_buttons = QHBoxLayout()

        generate_codes_btn = QPushButton("🔢 إنتاج الرموز")
        generate_codes_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        generate_codes_btn.clicked.connect(self.generate_account_codes)

        apply_selected_btn = QPushButton("✅ تطبيق المحدد")
        apply_selected_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        apply_selected_btn.clicked.connect(self.apply_selected_codes)

        apply_all_btn = QPushButton("🔄 تطبيق الكل")
        apply_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        apply_all_btn.clicked.connect(self.apply_all_codes)

        reset_codes_btn = QPushButton("🔄 إعادة تعيين")
        reset_codes_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        reset_codes_btn.clicked.connect(self.reset_account_codes)

        apply_buttons.addWidget(generate_codes_btn)
        apply_buttons.addWidget(apply_selected_btn)
        apply_buttons.addWidget(apply_all_btn)
        apply_buttons.addWidget(reset_codes_btn)
        apply_buttons.addStretch()

        accounts_layout.addLayout(apply_buttons)
        accounts_group.setLayout(accounts_layout)

        # مجموعة إعدادات التطبيق
        application_group = QGroupBox("⚙️ إعدادات التطبيق")
        application_layout = QGridLayout()

        # نمط التطبيق
        application_layout.addWidget(QLabel("نمط التطبيق:"), 0, 0)
        self.application_mode = QComboBox()
        self.application_mode.addItems(["تدريجي", "فوري", "مجدول", "تفاعلي"])
        self.application_mode.setCurrentText("تدريجي")
        application_layout.addWidget(self.application_mode, 0, 1)

        # معالجة التعارضات
        application_layout.addWidget(QLabel("معالجة التعارضات:"), 0, 2)
        self.conflict_resolution = QComboBox()
        self.conflict_resolution.addItems(["سؤال المستخدم", "تخطي", "استبدال", "إضافة لاحقة"])
        self.conflict_resolution.setCurrentText("سؤال المستخدم")
        application_layout.addWidget(self.conflict_resolution, 0, 3)

        # نسخ احتياطي قبل التطبيق
        self.backup_before_apply = QCheckBox("إنشاء نسخة احتياطية قبل التطبيق")
        self.backup_before_apply.setChecked(True)
        application_layout.addWidget(self.backup_before_apply, 1, 0, 1, 2)

        # تسجيل العمليات
        self.log_operations = QCheckBox("تسجيل جميع عمليات الترميز")
        self.log_operations.setChecked(True)
        application_layout.addWidget(self.log_operations, 1, 2, 1, 2)

        application_group.setLayout(application_layout)

        # مجموعة تقدم العملية
        progress_group = QGroupBox("📊 تقدم عملية الترميز")
        progress_layout = QVBoxLayout()

        # شريط التقدم
        self.coding_progress = QProgressBar()
        self.coding_progress.setRange(0, 100)
        self.coding_progress.setValue(0)
        self.coding_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)

        # معلومات التقدم
        self.progress_info = QLabel("جاهز لبدء عملية الترميز...")
        self.progress_info.setStyleSheet("""
            QLabel {
                background-color: #e8f4fd;
                border: 1px solid #bee5eb;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
                color: #0c5460;
            }
        """)

        progress_layout.addWidget(self.coding_progress)
        progress_layout.addWidget(self.progress_info)
        progress_group.setLayout(progress_layout)

        layout.addWidget(accounts_group)
        layout.addWidget(application_group)
        layout.addWidget(progress_group)

        tab.setLayout(layout)
        return tab

    def create_validation_tab(self):
        """إنشاء تبويب التحقق والمراجعة"""
        from .account_coding_functions import create_validation_tab_function
        return create_validation_tab_function(self)

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        from .account_coding_functions import create_reports_tab_function
        return create_reports_tab_function(self)

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        from .account_coding_functions import create_control_buttons_function
        return create_control_buttons_function(self)

    def load_data(self):
        """تحميل البيانات المحفوظة"""
        from .account_coding_data import load_data_function
        load_data_function(self)

    def load_coding_levels(self):
        """تحميل قواعد مستويات الترميز"""
        from .account_coding_data import load_coding_levels_function
        load_coding_levels_function(self)

    def load_accounts_for_coding(self):
        """تحميل الحسابات المتاحة للترميز"""
        from .account_coding_data import load_accounts_for_coding_function
        load_accounts_for_coding_function(self)

    def update_system_preview(self):
        """تحديث معاينة نظام الترميز"""
        from .account_coding_data import update_system_preview_function
        update_system_preview_function(self)

    def get_system_name(self, system_id):
        """الحصول على اسم النظام"""
        from .account_coding_data import get_system_name_function
        return get_system_name_function(self, system_id)

    def generate_account_codes(self):
        """إنتاج رموز الحسابات"""
        from .account_coding_data import generate_account_codes_function
        generate_account_codes_function(self)

    def generate_code_for_account(self, account_name, account_type, level, system_id):
        """إنتاج رمز لحساب محدد"""
        from .account_coding_data import generate_code_for_account_function
        return generate_code_for_account_function(self, account_name, account_type, level, system_id)

    def check_duplicates(self):
        """فحص التكرارات في الرموز"""
        from .account_coding_data import check_duplicates_function
        check_duplicates_function(self)

    def check_gaps(self):
        """فحص الفجوات في التسلسل"""
        from .account_coding_data import check_gaps_function
        check_gaps_function(self)

    def show_help(self):
        """عرض المساعدة"""
        from .account_coding_data import show_help_function
        show_help_function(self)

    # وظائف إضافية (ستكون في ملف منفصل)
    def add_coding_level(self):
        """إضافة مستوى ترميز جديد"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة مستوى الترميز قيد التطوير")

    def edit_coding_level(self):
        """تعديل مستوى ترميز"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل مستوى الترميز قيد التطوير")

    def delete_coding_level(self):
        """حذف مستوى ترميز"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة حذف مستوى الترميز قيد التطوير")

    def apply_selected_codes(self):
        """تطبيق الرموز المحددة"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تطبيق الرموز المحددة قيد التطوير")

    def apply_all_codes(self):
        """تطبيق جميع الرموز"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة تطبيق جميع الرموز قيد التطوير")

    def reset_account_codes(self):
        """إعادة تعيين رموز الحسابات"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة إعادة تعيين الرموز قيد التطوير")

    def check_hierarchy_function(self):
        """فحص الهيكل الهرمي"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة فحص الهيكل قيد التطوير")

    def full_validation(self):
        """فحص شامل للنظام"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة الفحص الشامل قيد التطوير")

    def repair_selected(self):
        """إصلاح المحدد"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة الإصلاح المحدد قيد التطوير")

    def repair_all(self):
        """إصلاح الكل"""
        QMessageBox.information(self, "قيد التطوير", "وظيفة إصلاح الكل قيد التطوير")

    def generate_coding_summary(self):
        """إنتاج ملخص الترميز"""
        QMessageBox.information(self, "قيد التطوير", "تقرير ملخص الترميز قيد التطوير")

    def generate_codes_list(self):
        """إنتاج قائمة الرموز"""
        QMessageBox.information(self, "قيد التطوير", "تقرير قائمة الرموز قيد التطوير")

    def generate_validation_report(self):
        """إنتاج تقرير التحقق"""
        QMessageBox.information(self, "قيد التطوير", "تقرير التحقق قيد التطوير")

    def export_report(self):
        """تصدير التقرير"""
        QMessageBox.information(self, "قيد التطوير", "تصدير التقارير قيد التطوير")

    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "قيد التطوير", "طباعة التقارير قيد التطوير")

    def clear_report(self):
        """مسح التقرير"""
        if hasattr(self, 'reports_display'):
            self.reports_display.clear()
        if hasattr(self, 'validation_results'):
            self.validation_results.clear()

    def save_coding_system(self):
        """حفظ نظام الترميز"""
        QMessageBox.information(self, "قيد التطوير", "حفظ نظام الترميز قيد التطوير")

    def apply_coding_system(self):
        """تطبيق نظام الترميز"""
        QMessageBox.information(self, "قيد التطوير", "تطبيق نظام الترميز قيد التطوير")


def main():
    """اختبار الشاشة"""
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass

    dialog = AccountCodingDialog(MockDBManager())
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
