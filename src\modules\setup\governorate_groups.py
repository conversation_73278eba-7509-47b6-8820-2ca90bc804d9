#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة مجموعات المحافظات
Governorate Groups Setup Screen
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QMessageBox, 
                             QFrame, QSplitter, QTextEdit, QTabWidget, QWidget,
                             QTreeWidget, QTreeWidgetItem, QListWidget, QListWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QColor

class GovernorateGroupsDialog(QDialog):
    """شاشة مجموعات المحافظات"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("مجموعات المحافظات - تجميع المحافظات حسب المعايير المختلفة")
        self.setGeometry(50, 50, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان الشاشة
        title_label = QLabel("🗺️ مجموعات المحافظات")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات الإدارة
        tabs = QTabWidget()
        
        # تبويب المجموعات
        groups_tab = self.create_groups_tab()
        tabs.addTab(groups_tab, "📋 إدارة المجموعات")
        
        # تبويب تعيين المحافظات
        assignment_tab = self.create_assignment_tab()
        tabs.addTab(assignment_tab, "🎯 تعيين المحافظات")
        
        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        tabs.addTab(reports_tab, "📊 التقارير والإحصائيات")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_groups_tab(self):
        """إنشاء تبويب إدارة المجموعات"""
        tab = QWidget()
        layout = QHBoxLayout()
        
        # الجانب الأيسر - نموذج إضافة مجموعة
        left_panel = QFrame()
        left_panel.setFrameStyle(QFrame.StyledPanel)
        left_panel.setFixedWidth(400)
        left_layout = QVBoxLayout()
        
        # عنوان القسم
        section_title = QLabel("➕ إضافة مجموعة جديدة")
        section_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                padding: 10px;
                background-color: #d5dbdb;
                border-radius: 5px;
                margin-bottom: 15px;
            }
        """)
        section_title.setAlignment(Qt.AlignCenter)
        
        # نموذج المجموعة
        group_form = QGroupBox("بيانات المجموعة")
        form_layout = QGridLayout()
        
        # اسم المجموعة
        form_layout.addWidget(QLabel("اسم المجموعة:"), 0, 0)
        self.group_name = QLineEdit()
        self.group_name.setPlaceholderText("مثال: المحافظات الشمالية")
        form_layout.addWidget(self.group_name, 0, 1)
        
        # اسم المجموعة بالإنجليزية
        form_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 1, 0)
        self.group_name_en = QLineEdit()
        self.group_name_en.setPlaceholderText("Northern Governorates")
        form_layout.addWidget(self.group_name_en, 1, 1)
        
        # رمز المجموعة
        form_layout.addWidget(QLabel("رمز المجموعة:"), 2, 0)
        self.group_code = QLineEdit()
        self.group_code.setPlaceholderText("NORTH")
        self.group_code.setMaxLength(10)
        form_layout.addWidget(self.group_code, 2, 1)
        
        # نوع التجميع
        form_layout.addWidget(QLabel("نوع التجميع:"), 3, 0)
        self.group_type = QComboBox()
        self.group_type.addItems([
            "جغرافي", "إداري", "اقتصادي", "ديموغرافي", 
            "مناخي", "تنموي", "أمني", "مخصص"
        ])
        form_layout.addWidget(self.group_type, 3, 1)
        
        # لون المجموعة
        form_layout.addWidget(QLabel("لون المجموعة:"), 4, 0)
        self.group_color = QComboBox()
        self.group_color.addItems([
            "أزرق", "أخضر", "أحمر", "أصفر", "بنفسجي", 
            "برتقالي", "وردي", "بني", "رمادي", "أسود"
        ])
        form_layout.addWidget(self.group_color, 4, 1)
        
        # وصف المجموعة
        form_layout.addWidget(QLabel("الوصف:"), 5, 0)
        self.group_description = QTextEdit()
        self.group_description.setMaximumHeight(80)
        self.group_description.setPlaceholderText("وصف مختصر للمجموعة ومعايير التجميع...")
        form_layout.addWidget(self.group_description, 5, 1)
        
        # مجموعة نشطة
        self.group_active = QCheckBox("مجموعة نشطة")
        self.group_active.setChecked(True)
        form_layout.addWidget(self.group_active, 6, 0, 1, 2)
        
        # مجموعة افتراضية
        self.group_default = QCheckBox("مجموعة افتراضية")
        form_layout.addWidget(self.group_default, 7, 0, 1, 2)
        
        group_form.setLayout(form_layout)
        
        # أزرار إدارة المجموعات
        group_buttons = QHBoxLayout()
        
        add_group_btn = QPushButton("➕ إضافة")
        add_group_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_group_btn.clicked.connect(self.add_group)
        
        edit_group_btn = QPushButton("✏️ تعديل")
        edit_group_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_group_btn.clicked.connect(self.edit_group)
        
        delete_group_btn = QPushButton("🗑️ حذف")
        delete_group_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_group_btn.clicked.connect(self.delete_group)
        
        group_buttons.addWidget(add_group_btn)
        group_buttons.addWidget(edit_group_btn)
        group_buttons.addWidget(delete_group_btn)
        
        left_layout.addWidget(section_title)
        left_layout.addWidget(group_form)
        left_layout.addLayout(group_buttons)
        left_layout.addStretch()
        
        left_panel.setLayout(left_layout)
        
        # الجانب الأيمن - جدول المجموعات
        right_panel = QFrame()
        right_panel.setFrameStyle(QFrame.StyledPanel)
        right_layout = QVBoxLayout()
        
        # عنوان الجدول
        table_title = QLabel("📋 المجموعات المعرفة")
        table_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        table_title.setAlignment(Qt.AlignCenter)
        
        # جدول المجموعات
        self.groups_table = QTableWidget()
        self.groups_table.setColumnCount(7)
        self.groups_table.setHorizontalHeaderLabels([
            "اسم المجموعة", "الاسم بالإنجليزية", "الرمز", "النوع", "عدد المحافظات", "اللون", "الحالة"
        ])
        
        # تنسيق الجدول
        self.groups_table.horizontalHeader().setStretchLastSection(True)
        self.groups_table.setAlternatingRowColors(True)
        self.groups_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.groups_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        # ربط الأحداث
        self.groups_table.itemClicked.connect(self.on_group_selected)
        
        right_layout.addWidget(table_title)
        right_layout.addWidget(self.groups_table)
        
        right_panel.setLayout(right_layout)
        
        layout.addWidget(left_panel)
        layout.addWidget(right_panel)
        
        tab.setLayout(layout)
        return tab
        
    def create_assignment_tab(self):
        """إنشاء تبويب تعيين المحافظات"""
        tab = QWidget()
        layout = QHBoxLayout()
        
        # الجانب الأيسر - المحافظات المتاحة
        left_panel = QFrame()
        left_panel.setFrameStyle(QFrame.StyledPanel)
        left_layout = QVBoxLayout()
        
        # عنوان القسم
        available_title = QLabel("🏛️ المحافظات المتاحة")
        available_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                padding: 10px;
                background-color: #d5dbdb;
                border-radius: 5px;
                margin-bottom: 15px;
            }
        """)
        available_title.setAlignment(Qt.AlignCenter)
        
        # قائمة المحافظات المتاحة
        self.available_governorates = QListWidget()
        self.available_governorates.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                font-size: 13px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
                min-height: 25px;
            }
            QListWidget::item:hover {
                background-color: #e8f4fd;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
        """)
        
        left_layout.addWidget(available_title)
        left_layout.addWidget(self.available_governorates)
        
        left_panel.setLayout(left_layout)
        
        # الوسط - أزرار التحكم
        middle_panel = QFrame()
        middle_panel.setFixedWidth(120)
        middle_layout = QVBoxLayout()
        
        # اختيار المجموعة
        group_selection = QGroupBox("اختيار المجموعة")
        group_sel_layout = QVBoxLayout()
        
        self.target_group = QComboBox()
        group_sel_layout.addWidget(self.target_group)
        
        group_selection.setLayout(group_sel_layout)
        
        # أزرار النقل
        move_right_btn = QPushButton("➡️\nإضافة")
        move_right_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 15px 10px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        move_right_btn.clicked.connect(self.move_to_group)
        
        move_left_btn = QPushButton("⬅️\nإزالة")
        move_left_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 15px 10px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        move_left_btn.clicked.connect(self.remove_from_group)
        
        move_all_right_btn = QPushButton("⏩\nإضافة الكل")
        move_all_right_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 15px 10px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        move_all_right_btn.clicked.connect(self.move_all_to_group)
        
        move_all_left_btn = QPushButton("⏪\nإزالة الكل")
        move_all_left_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 15px 10px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        move_all_left_btn.clicked.connect(self.remove_all_from_group)
        
        middle_layout.addWidget(group_selection)
        middle_layout.addStretch()
        middle_layout.addWidget(move_right_btn)
        middle_layout.addWidget(move_left_btn)
        middle_layout.addWidget(move_all_right_btn)
        middle_layout.addWidget(move_all_left_btn)
        middle_layout.addStretch()
        
        middle_panel.setLayout(middle_layout)
        
        # الجانب الأيمن - محافظات المجموعة
        right_panel = QFrame()
        right_panel.setFrameStyle(QFrame.StyledPanel)
        right_layout = QVBoxLayout()
        
        # عنوان القسم
        assigned_title = QLabel("🎯 محافظات المجموعة")
        assigned_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                padding: 10px;
                background-color: #d5dbdb;
                border-radius: 5px;
                margin-bottom: 15px;
            }
        """)
        assigned_title.setAlignment(Qt.AlignCenter)
        
        # قائمة محافظات المجموعة
        self.assigned_governorates = QListWidget()
        self.assigned_governorates.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                font-size: 13px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
                min-height: 25px;
            }
            QListWidget::item:hover {
                background-color: #e8f4fd;
            }
            QListWidget::item:selected {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
            }
        """)
        
        right_layout.addWidget(assigned_title)
        right_layout.addWidget(self.assigned_governorates)
        
        right_panel.setLayout(right_layout)
        
        layout.addWidget(left_panel)
        layout.addWidget(middle_panel)
        layout.addWidget(right_panel)
        
        tab.setLayout(layout)
        return tab
        
    def create_reports_tab(self):
        """إنشاء تبويب التقارير والإحصائيات"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة تقارير المجموعات
        reports_group = QGroupBox("📊 تقارير المجموعات")
        reports_layout = QGridLayout()
        
        # تقرير توزيع المحافظات
        distribution_btn = QPushButton("📈 تقرير توزيع المحافظات")
        distribution_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        distribution_btn.clicked.connect(self.generate_distribution_report)
        
        # تقرير إحصائيات المجموعات
        statistics_btn = QPushButton("📊 إحصائيات المجموعات")
        statistics_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        statistics_btn.clicked.connect(self.generate_statistics_report)
        
        # تقرير المحافظات غير المجمعة
        ungrouped_btn = QPushButton("⚠️ المحافظات غير المجمعة")
        ungrouped_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        ungrouped_btn.clicked.connect(self.generate_ungrouped_report)
        
        # تصدير التقارير
        export_btn = QPushButton("📤 تصدير جميع التقارير")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #1abc9c;
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #16a085;
            }
        """)
        export_btn.clicked.connect(self.export_all_reports)
        
        reports_layout.addWidget(distribution_btn, 0, 0)
        reports_layout.addWidget(statistics_btn, 0, 1)
        reports_layout.addWidget(ungrouped_btn, 1, 0)
        reports_layout.addWidget(export_btn, 1, 1)
        
        reports_group.setLayout(reports_layout)
        
        # منطقة عرض التقارير
        self.reports_display = QTextEdit()
        self.reports_display.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                font-family: 'Courier New', monospace;
                font-size: 12px;
                padding: 15px;
            }
        """)
        self.reports_display.setPlaceholderText("ستظهر التقارير والإحصائيات هنا...")
        
        layout.addWidget(reports_group)
        layout.addWidget(QLabel("📋 عرض التقارير:"))
        layout.addWidget(self.reports_display)
        
        tab.setLayout(layout)
        return tab

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        # زر الحفظ
        save_btn = QPushButton("💾 حفظ جميع التغييرات")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_all_changes)

        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)

        # زر المساعدة
        help_btn = QPushButton("❓ المساعدة")
        help_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        help_btn.clicked.connect(self.show_help)

        layout.addWidget(help_btn)
        layout.addStretch()
        layout.addWidget(save_btn)
        layout.addWidget(cancel_btn)

        return layout

    def load_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            # تحميل المحافظات المتاحة
            self.load_available_governorates()

            # تحميل المجموعات الافتراضية
            self.load_default_groups()

            # تحديث قائمة المجموعات
            self.update_target_groups()

        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")

    def load_available_governorates(self):
        """تحميل قائمة المحافظات المتاحة"""
        governorates = [
            "محافظة صنعاء",
            "محافظة عدن",
            "محافظة تعز",
            "محافظة الحديدة",
            "محافظة إب",
            "محافظة ذمار",
            "محافظة حضرموت",
            "محافظة المهرة",
            "محافظة شبوة",
            "محافظة أبين",
            "محافظة لحج",
            "محافظة الضالع",
            "محافظة البيضاء",
            "محافظة مأرب",
            "محافظة الجوف",
            "محافظة صعدة",
            "محافظة حجة",
            "محافظة المحويت",
            "محافظة ريمة",
            "محافظة صنعاء (أمانة العاصمة)",
            "محافظة عمران",
            "محافظة جزيرة سقطرى"
        ]

        self.available_governorates.clear()
        for gov in governorates:
            item = QListWidgetItem(gov)
            self.available_governorates.addItem(item)

    def load_default_groups(self):
        """تحميل المجموعات الافتراضية"""
        default_groups = [
            ("المحافظات الشمالية", "Northern Governorates", "NORTH", "جغرافي", "أزرق",
             "المحافظات الواقعة في الجزء الشمالي من اليمن", True, False),
            ("المحافظات الجنوبية", "Southern Governorates", "SOUTH", "جغرافي", "أخضر",
             "المحافظات الواقعة في الجزء الجنوبي من اليمن", True, False),
            ("المحافظات الشرقية", "Eastern Governorates", "EAST", "جغرافي", "أصفر",
             "المحافظات الواقعة في الجزء الشرقي من اليمن", True, False),
            ("المحافظات الغربية", "Western Governorates", "WEST", "جغرافي", "أحمر",
             "المحافظات الواقعة في الجزء الغربي من اليمن", True, False),
            ("المحافظات الساحلية", "Coastal Governorates", "COAST", "جغرافي", "بنفسجي",
             "المحافظات التي تطل على البحر", True, False),
            ("المحافظات الداخلية", "Inland Governorates", "INLAND", "جغرافي", "برتقالي",
             "المحافظات الداخلية غير الساحلية", True, False),
            ("المحافظات الكبرى", "Major Governorates", "MAJOR", "ديموغرافي", "وردي",
             "المحافظات ذات الكثافة السكانية العالية", True, True),
            ("المحافظات النفطية", "Oil Governorates", "OIL", "اقتصادي", "بني",
             "المحافظات التي تحتوي على حقول نفطية", True, False)
        ]

        self.groups_table.setRowCount(len(default_groups))
        for row, (name, name_en, code, type_name, color, desc, active, default) in enumerate(default_groups):
            self.groups_table.setItem(row, 0, QTableWidgetItem(name))
            self.groups_table.setItem(row, 1, QTableWidgetItem(name_en))
            self.groups_table.setItem(row, 2, QTableWidgetItem(code))
            self.groups_table.setItem(row, 3, QTableWidgetItem(type_name))
            self.groups_table.setItem(row, 4, QTableWidgetItem("0"))  # عدد المحافظات
            self.groups_table.setItem(row, 5, QTableWidgetItem(color))

            status = "نشطة"
            if default:
                status += " (افتراضية)"
            elif not active:
                status = "غير نشطة"
            self.groups_table.setItem(row, 6, QTableWidgetItem(status))

        # تحميل التعيينات الافتراضية
        self.load_default_assignments()

    def load_default_assignments(self):
        """تحميل التعيينات الافتراضية للمحافظات"""
        # هذه البيانات ستكون محفوظة في قاعدة البيانات
        # هنا نضع تعيينات نموذجية
        default_assignments = {
            "المحافظات الشمالية": [
                "محافظة صنعاء", "محافظة صنعاء (أمانة العاصمة)", "محافظة عمران",
                "محافظة صعدة", "محافظة الجوف", "محافظة حجة", "محافظة المحويت", "محافظة ريمة"
            ],
            "المحافظات الجنوبية": [
                "محافظة عدن", "محافظة لحج", "محافظة أبين", "محافظة الضالع"
            ],
            "المحافظات الشرقية": [
                "محافظة حضرموت", "محافظة المهرة", "محافظة شبوة", "محافظة مأرب"
            ],
            "المحافظات الغربية": [
                "محافظة الحديدة", "محافظة تعز", "محافظة إب", "محافظة ذمار", "محافظة البيضاء"
            ],
            "المحافظات الساحلية": [
                "محافظة الحديدة", "محافظة عدن", "محافظة لحج", "محافظة أبين",
                "محافظة حضرموت", "محافظة المهرة", "محافظة جزيرة سقطرى"
            ],
            "المحافظات الكبرى": [
                "محافظة صنعاء", "محافظة صنعاء (أمانة العاصمة)", "محافظة عدن",
                "محافظة تعز", "محافظة الحديدة", "محافظة إب"
            ],
            "المحافظات النفطية": [
                "محافظة مأرب", "محافظة شبوة", "محافظة حضرموت"
            ]
        }

        # تحديث عدد المحافظات في الجدول
        for row in range(self.groups_table.rowCount()):
            group_name = self.groups_table.item(row, 0).text()
            if group_name in default_assignments:
                count = len(default_assignments[group_name])
                self.groups_table.setItem(row, 4, QTableWidgetItem(str(count)))

        # حفظ التعيينات (في التطبيق الحقيقي ستكون في قاعدة البيانات)
        self.group_assignments = default_assignments

    # استيراد الوظائف الإضافية
    def update_target_groups(self):
        """تحديث قائمة المجموعات المستهدفة"""
        from .governorate_groups_functions import update_target_groups_function
        update_target_groups_function(self)

    def on_group_selected(self, item):
        """معالجة اختيار مجموعة من الجدول"""
        from .governorate_groups_functions import on_group_selected_function
        on_group_selected_function(self, item)

    def update_assignment_lists(self):
        """تحديث قوائم التعيين"""
        from .governorate_groups_functions import update_assignment_lists_function
        update_assignment_lists_function(self)

    def add_group(self):
        """إضافة مجموعة جديدة"""
        from .governorate_groups_functions import add_group_function
        add_group_function(self)

    def edit_group(self):
        """تعديل مجموعة محددة"""
        from .governorate_groups_functions import edit_group_function
        edit_group_function(self)

    def delete_group(self):
        """حذف مجموعة محددة"""
        from .governorate_groups_functions import delete_group_function
        delete_group_function(self)

    def clear_group_form(self):
        """تنظيف نموذج المجموعة"""
        from .governorate_groups_functions import clear_group_form_function
        clear_group_form_function(self)

    def move_to_group(self):
        """نقل محافظة إلى المجموعة"""
        from .governorate_groups_functions import move_to_group_function
        move_to_group_function(self)

    def remove_from_group(self):
        """إزالة محافظة من المجموعة"""
        from .governorate_groups_functions import remove_from_group_function
        remove_from_group_function(self)

    def move_all_to_group(self):
        """نقل جميع المحافظات إلى المجموعة"""
        from .governorate_groups_functions import move_all_to_group_function
        move_all_to_group_function(self)

    def remove_all_from_group(self):
        """إزالة جميع المحافظات من المجموعة"""
        from .governorate_groups_functions import remove_all_from_group_function
        remove_all_from_group_function(self)

    def update_group_counts(self):
        """تحديث عدد المحافظات في كل مجموعة"""
        from .governorate_groups_functions import update_group_counts_function
        update_group_counts_function(self)

    def generate_distribution_report(self):
        """إنتاج تقرير توزيع المحافظات"""
        from .governorate_groups_functions import generate_distribution_report_function
        generate_distribution_report_function(self)

    def generate_statistics_report(self):
        """إنتاج تقرير إحصائيات المجموعات"""
        from .governorate_groups_functions import generate_statistics_report_function
        generate_statistics_report_function(self)

    def generate_ungrouped_report(self):
        """إنتاج تقرير المحافظات غير المجمعة"""
        from .governorate_groups_functions import generate_ungrouped_report_function
        generate_ungrouped_report_function(self)

    def export_all_reports(self):
        """تصدير جميع التقارير"""
        from .governorate_groups_functions import export_all_reports_function
        export_all_reports_function(self)

    def show_help(self):
        """عرض المساعدة"""
        from .governorate_groups_functions import show_help_function
        show_help_function(self)

    def save_all_changes(self):
        """حفظ جميع التغييرات"""
        from .governorate_groups_functions import save_all_changes_function
        save_all_changes_function(self)


def main():
    """اختبار الشاشة"""
    import sys
    from PyQt5.QtWidgets import QApplication

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass

    dialog = GovernorateGroupsDialog(MockDBManager())
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
