#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار السند بتصميم مطابق للصورة
Voucher Test with Image-Matching Design
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QGroupBox, QLabel, QLineEdit, QComboBox, QDateEdit, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QPushButton, QToolBar, QAction, 
                             QFrame, QWidget, QTextEdit, QSpinBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

class ImageStyleVoucherWindow(QMainWindow):
    """نافذة السند بتصميم مطابق للصورة"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("سند قبض - Onyx Pro ERP")
        self.setGeometry(50, 50, 1400, 900)
        self.showMaximized()
        
        # تطبيق تنسيق مطابق للصورة
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0e6f7;
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 11px;
            }
            QLabel {
                font-size: 11px;
                color: #2c3e50;
                background-color: transparent;
                padding: 2px;
            }
            QLineEdit, QComboBox, QDateEdit, QSpinBox {
                font-size: 11px;
                color: #2c3e50;
                padding: 2px;
                border: 1px solid #bdc3c7;
                background-color: white;
                min-height: 18px;
            }
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus {
                border: 1px solid #3498db;
                background-color: #ebf3fd;
            }
            QTableWidget {
                font-size: 11px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #bdc3c7;
                gridline-color: #ddd;
            }
            QHeaderView::section {
                background-color: #e8e8e8;
                color: #2c3e50;
                padding: 4px;
                border: 1px solid #bdc3c7;
                font-weight: bold;
                font-size: 11px;
            }
            QToolBar {
                background-color: #e8e8e8;
                border: 1px solid #bdc3c7;
                spacing: 3px;
                padding: 5px;
            }
            QToolBar QToolButton {
                background-color: #f8f9fa;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 8px 12px;
                margin: 2px;
                color: #2c3e50;
                font-size: 12px;
                font-weight: bold;
                min-width: 60px;
                min-height: 32px;
            }
            QToolBar QToolButton:hover {
                background-color: #e3f2fd;
                border: 2px solid #2196f3;
                color: #1976d2;
            }
            QToolBar QToolButton:pressed {
                background-color: #bbdefb;
                border: 2px solid #1565c0;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        layout.setSpacing(5)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # شريط الأدوات العلوي
        self.create_toolbar()
        layout.addWidget(self.toolbar)
        
        # قسم بيانات السند
        self.create_voucher_info()
        layout.addWidget(self.voucher_info_frame)
        
        # جدول التفاصيل
        self.create_details_table()
        layout.addWidget(self.details_table)
        
        # قسم الإجماليات
        self.create_totals_section()
        layout.addWidget(self.totals_frame)
        
        # قسم بيانات المستخدم
        self.create_user_info()
        layout.addWidget(self.user_info_frame)
        
        central_widget.setLayout(layout)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات مطابق للصورة"""
        self.toolbar = QToolBar()
        self.toolbar.setFixedHeight(50)  # ارتفاع أكبر

        # أزرار شريط الأدوات مع نصوص واضحة
        toolbar_buttons = [
            ("💾 حفظ", "حفظ السند (Ctrl+S)"),
            ("📄 جديد", "سند جديد (Ctrl+N)"),
            ("✏️ تعديل", "تعديل السند (F2)"),
            ("🗑️ حذف", "حذف السند (Delete)"),
            ("🖨️ طباعة", "طباعة السند (Ctrl+P)"),
            ("👁️ معاينة", "معاينة الطباعة"),
            ("📂 فتح", "فتح سند موجود (Ctrl+O)"),
            ("🔍 بحث", "البحث في السندات (Ctrl+F)"),
            ("⏮️ الأول", "السند الأول"),
            ("⏪ السابق", "السند السابق"),
            ("⏩ التالي", "السند التالي"),
            ("⏭️ الأخير", "السند الأخير"),
            ("📊 تقرير", "تقارير السندات"),
            ("⚙️ إعدادات", "إعدادات النظام"),
            ("❓ مساعدة", "المساعدة (F1)"),
            ("❌ إغلاق", "إغلاق النافذة")
        ]

        for text, tooltip in toolbar_buttons:
            action = QAction(text, self)
            action.setToolTip(tooltip)

            # ربط الأحداث للأزرار المهمة
            if "حفظ" in text:
                action.triggered.connect(self.save_voucher)
            elif "جديد" in text:
                action.triggered.connect(self.new_voucher)
            elif "طباعة" in text:
                action.triggered.connect(self.print_voucher)
            elif "إغلاق" in text:
                action.triggered.connect(self.close)

            self.toolbar.addAction(action)

            # إضافة فواصل بعد مجموعات معينة
            if text in ["🗑️ حذف", "📂 فتح", "⏭️ الأخير"]:
                self.toolbar.addSeparator()
    
    def create_voucher_info(self):
        """إنشاء قسم بيانات السند مطابق للصورة تماماً"""
        self.voucher_info_frame = QFrame()
        layout = QGridLayout()
        layout.setVerticalSpacing(8)
        layout.setHorizontalSpacing(15)

        # الصف الأول - الجانب الأيسر
        layout.addWidget(QLabel("رقم السند:"), 0, 0)
        self.voucher_number_edit = QLineEdit()
        self.voucher_number_edit.setMaximumWidth(80)
        self.voucher_number_edit.setText("9905")
        layout.addWidget(self.voucher_number_edit, 0, 1)

        # فاصل
        layout.addWidget(QLabel(""), 0, 2)

        # الجانب الأيمن - نوع السند
        layout.addWidget(QLabel("نوع السند:"), 0, 3)
        self.voucher_type_combo = QComboBox()
        self.voucher_type_combo.addItems(["عام", "مبيعات", "مشتريات", "مرتجعات"])
        self.voucher_type_combo.setMaximumWidth(100)
        layout.addWidget(self.voucher_type_combo, 0, 4)

        # نوع العملية
        layout.addWidget(QLabel("نوع العملية:"), 0, 5)
        self.operation_type_combo = QComboBox()
        self.operation_type_combo.addItems(["نقداً", "بنكي"])
        self.operation_type_combo.setMaximumWidth(80)
        layout.addWidget(self.operation_type_combo, 0, 6)

        # العملة
        layout.addWidget(QLabel("العملة:"), 0, 7)
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["R.Y", "USD", "SAR", "EUR"])
        self.currency_combo.setMaximumWidth(60)
        layout.addWidget(self.currency_combo, 0, 8)

        # عدد المرفقات
        layout.addWidget(QLabel("عدد المرفقات:"), 0, 9)
        self.attachments_edit = QSpinBox()
        self.attachments_edit.setMaximumWidth(60)
        layout.addWidget(self.attachments_edit, 0, 10)

        # الصف الثاني
        # التاريخ
        layout.addWidget(QLabel("التاريخ:"), 1, 0)
        self.voucher_date_edit = QDateEdit()
        self.voucher_date_edit.setDate(QDate.currentDate())
        self.voucher_date_edit.setDisplayFormat("dd/MM/yyyy")
        self.voucher_date_edit.setMaximumWidth(100)
        layout.addWidget(self.voucher_date_edit, 1, 1)

        # فاصل
        layout.addWidget(QLabel(""), 1, 2)

        # مركز رئيسي/فرعي
        layout.addWidget(QLabel("مركز رئيسي/فرعي:"), 1, 3)
        self.center_combo = QComboBox()
        self.center_combo.addItems(["المركز الرئيسي", "الفرع الأول", "الفرع الثاني"])
        self.center_combo.setMaximumWidth(120)
        layout.addWidget(self.center_combo, 1, 4, 1, 2)

        # رقم الفرع
        layout.addWidget(QLabel("رقم الفرع:"), 1, 6)
        self.branch_number_edit = QLineEdit()
        self.branch_number_edit.setMaximumWidth(60)
        layout.addWidget(self.branch_number_edit, 1, 7)

        # رقم الصندوق
        layout.addWidget(QLabel("رقم الصندوق:"), 1, 8)
        self.cashbox_code_edit = QLineEdit()
        self.cashbox_code_edit.setMaximumWidth(100)
        self.cashbox_code_edit.setPlaceholderText("صندوق المحل الرئيسي")
        layout.addWidget(self.cashbox_code_edit, 1, 9)

        # زر البحث
        self.search_cashbox_btn = QPushButton("🔍")
        self.search_cashbox_btn.setMaximumWidth(25)
        self.search_cashbox_btn.setToolTip("البحث عن الصندوق/البنك (F9)")
        layout.addWidget(self.search_cashbox_btn, 1, 10)

        # الصف الثالث
        # R.Y المبلغ المحلي
        layout.addWidget(QLabel("R.Y المبلغ المحلي:"), 2, 0)
        self.local_amount_edit = QLineEdit("0.00")
        self.local_amount_edit.setReadOnly(True)
        self.local_amount_edit.setStyleSheet("background-color: #f0f0f0; font-weight: bold;")
        self.local_amount_edit.setMaximumWidth(100)
        layout.addWidget(self.local_amount_edit, 2, 1)

        # فاصل
        layout.addWidget(QLabel(""), 2, 2)

        # اسم المستلم
        layout.addWidget(QLabel("اسم المستلم:"), 2, 3)
        self.recipient_name_edit = QLineEdit()
        self.recipient_name_edit.setMaximumWidth(150)
        layout.addWidget(self.recipient_name_edit, 2, 4, 1, 2)

        # رقم المرجع
        layout.addWidget(QLabel("رقم المرجع:"), 2, 6)
        self.reference_number_edit = QLineEdit()
        self.reference_number_edit.setMaximumWidth(80)
        layout.addWidget(self.reference_number_edit, 2, 7)

        # اسم المرجع
        layout.addWidget(QLabel("اسم المرجع:"), 2, 8)
        self.reference_name_edit = QLineEdit()
        self.reference_name_edit.setMaximumWidth(120)
        layout.addWidget(self.reference_name_edit, 2, 9, 1, 2)

        # الصف الرابع
        # رقم الموظف
        layout.addWidget(QLabel("رقم الموظف:"), 3, 6)
        self.employee_number_edit = QLineEdit()
        self.employee_number_edit.setMaximumWidth(80)
        layout.addWidget(self.employee_number_edit, 3, 7)

        # الرقم
        layout.addWidget(QLabel("الرقم:"), 3, 8)
        self.number_edit = QLineEdit()
        self.number_edit.setMaximumWidth(80)
        layout.addWidget(self.number_edit, 3, 9)

        # الصف الخامس - بيان الصندوق/البنك
        layout.addWidget(QLabel("بيان الصندوق/البنك:"), 4, 3)
        self.cashbox_description_edit = QLineEdit()
        self.cashbox_description_edit.setPlaceholderText("شرح تفصيلي لسبب السند أو تفاصيل الدفع/التحصيل...")
        layout.addWidget(self.cashbox_description_edit, 4, 4, 1, 6)

        # أزرار إضافية
        self.calculator_btn = QPushButton("🧮")
        self.calculator_btn.setMaximumWidth(30)
        self.calculator_btn.setToolTip("فتح الآلة الحاسبة")
        layout.addWidget(self.calculator_btn, 4, 10)

        self.voucher_info_frame.setLayout(layout)
        self.voucher_info_frame.setMaximumHeight(160)  # ارتفاع أكبر للصفوف الإضافية
    
    def create_details_table(self):
        """إنشاء جدول التفاصيل مطابق للصورة"""
        self.details_table = QTableWidget()
        self.details_table.setColumnCount(11)
        
        headers = [
            "رقم الحساب", "رقم المندوب", "رقم المرجع", "الحساب التحليلي", 
            "البيان", "نسبة", "سعر التحويل", "العملة", "البيان", 
            "الاسم", "رقم الحساب"
        ]
        self.details_table.setHorizontalHeaderLabels(headers)
        
        # تعيين عرض الأعمدة
        column_widths = [100, 80, 80, 100, 150, 60, 80, 60, 150, 150, 100]
        for i, width in enumerate(column_widths):
            self.details_table.setColumnWidth(i, width)
        
        # إضافة بعض الصفوف الفارغة
        for _ in range(15):
            self.details_table.insertRow(self.details_table.rowCount())
        
        # تطبيق ألوان متناوبة
        self.details_table.setAlternatingRowColors(True)
        self.details_table.verticalHeader().setDefaultSectionSize(25)
    
    def create_totals_section(self):
        """إنشاء قسم الإجماليات"""
        self.totals_frame = QFrame()
        layout = QHBoxLayout()
        
        # المجموع
        layout.addWidget(QLabel("المجموع:"))
        self.total_edit = QLineEdit("0.00")
        self.total_edit.setReadOnly(True)
        self.total_edit.setMaximumWidth(100)
        layout.addWidget(self.total_edit)
        
        # الفرق
        layout.addWidget(QLabel("الفرق:"))
        self.difference_edit = QLineEdit("0.00")
        self.difference_edit.setReadOnly(True)
        self.difference_edit.setMaximumWidth(100)
        layout.addWidget(self.difference_edit)
        
        layout.addStretch()
        
        self.totals_frame.setLayout(layout)
        self.totals_frame.setMaximumHeight(40)
    
    def create_user_info(self):
        """إنشاء قسم بيانات المستخدم"""
        self.user_info_frame = QFrame()
        layout = QGridLayout()
        layout.setVerticalSpacing(3)
        
        # الصف الأول
        layout.addWidget(QLabel("مرات الطباعة:"), 0, 0)
        layout.addWidget(QLabel("0"), 0, 1)
        
        layout.addWidget(QLabel("الجهاز المدخل:"), 0, 2)
        layout.addWidget(QLabel("GLST002"), 0, 3)
        
        layout.addWidget(QLabel("تاريخ الإدخال:"), 0, 4)
        layout.addWidget(QLabel("28/07/2025 11:56:05"), 0, 5)
        
        layout.addWidget(QLabel("مدخل السجل:"), 0, 6)
        layout.addWidget(QLabel("مدير النظام"), 0, 7)
        
        # الصف الثاني
        layout.addWidget(QLabel("مرات التعديل:"), 1, 0)
        layout.addWidget(QLabel("0"), 1, 1)
        
        layout.addWidget(QLabel("الجهاز المعدل:"), 1, 2)
        layout.addWidget(QLabel("-"), 1, 3)
        
        layout.addWidget(QLabel("تاريخ آخر تعديل:"), 1, 4)
        layout.addWidget(QLabel("-"), 1, 5)
        
        layout.addWidget(QLabel("معدل السجل:"), 1, 6)
        layout.addWidget(QLabel("-"), 1, 7)
        
        # معلومات النظام
        version_label = QLabel("V8.1.18-05-2025")
        version_label.setStyleSheet("color: #7f8c8d; font-size: 10px;")
        layout.addWidget(version_label, 2, 0)
        
        help_label = QLabel("للمساعدة اضغط على F9")
        help_label.setStyleSheet("color: #3498db; font-size: 10px;")
        layout.addWidget(help_label, 2, 7)
        
        self.user_info_frame.setLayout(layout)
        self.user_info_frame.setMaximumHeight(80)

    def save_voucher(self):
        """حفظ السند"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "حفظ السند", "✅ تم حفظ السند بنجاح!")

    def new_voucher(self):
        """سند جديد"""
        from PyQt5.QtWidgets import QMessageBox
        reply = QMessageBox.question(self, "سند جديد",
                                   "هل تريد إنشاء سند جديد؟\nسيتم مسح البيانات الحالية.")
        if reply == QMessageBox.Yes:
            # مسح البيانات
            self.voucher_number_edit.clear()
            self.beneficiary_edit.clear()
            self.reference_edit.clear()
            QMessageBox.information(self, "سند جديد", "✅ تم إنشاء سند جديد!")

    def print_voucher(self):
        """طباعة السند"""
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.information(self, "طباعة السند",
                              "🖨️ جاري إرسال السند للطباعة...\n\n"
                              "📄 سيتم طباعة:\n"
                              "• رأس السند\n"
                              "• تفاصيل الحسابات\n"
                              "• بيانات المستخدم\n"
                              "• الإجماليات")


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont("Tahoma", 11)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = ImageStyleVoucherWindow()
    window.show()
    
    print("📄 تم تشغيل السند بتصميم مطابق للصورة!")
    print("✅ الميزات:")
    print("   📊 شريط أدوات مطابق للصورة")
    print("   📋 بيانات السند مرتبة في شبكة")
    print("   📈 جدول تفاصيل بـ 11 عمود")
    print("   👤 بيانات المستخدم مفصلة")
    print("   🎨 ألوان وتنسيق مطابق")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
