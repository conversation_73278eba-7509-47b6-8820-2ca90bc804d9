# دليل نظام Onyx ERP
## Onyx ERP System Guide

---

## 🏢 نظرة عامة على النظام

**Onyx ERP** هو نظام تخطيط موارد المؤسسات المتطور الذي يوفر حلولاً شاملة لإدارة العمليات المحاسبية والمالية. النظام مصمم خصيصاً للشركات العربية مع دعم كامل للغة العربية والإنجليزية.

---

## 🚀 تشغيل النظام

### طرق التشغيل المتاحة:

#### 1. التشغيل المباشر (موصى به)
```bash
python run_onyx.py
```

#### 2. Windows Batch File
```bash
run_onyx.bat
```

#### 3. التشغيل من سطر الأوامر
```bash
cd c:/Users/<USER>/Desktop/HGR
python run_onyx.py
```

---

## 🖥️ واجهة تسجيل الدخول

### مكونات الواجهة:

#### 🎨 التصميم العام
- **رأس الصفحة**: شعار Onyx ERP مع خلفية متدرجة أنيقة
- **منطقة النموذج**: حقول تسجيل الدخول بتصميم حديث
- **منطقة الأزرار**: أزرار موافقة وخروج بألوان مميزة
- **تذييل الصفحة**: شعار "Enterprise Resource Planning Solutions"

#### 📋 حقول النموذج:

1. **السنة المالية (Year)**
   - القيم المتاحة: 2024, 2025, 2026
   - القيمة الافتراضية: 2025
   - الغرض: تحديد السنة المالية للعمل

2. **اللغة (Language)**
   - الخيارات: عربي، English
   - القيمة الافتراضية: عربي
   - الغرض: تحديد لغة واجهة النظام

3. **الوحدة المحاسبية (Accounting Unit)**
   - القيم المتاحة: 1, 2, 3
   - القيمة الافتراضية: 1
   - الغرض: تحديد الوحدة المحاسبية للعمل

4. **المستخدم (User)**
   - الخيارات: 1 - admin, 2 - محاسب, 3 - مستخدم
   - القيمة الافتراضية: 1 - admin
   - الغرض: اختيار المستخدم لتسجيل الدخول

5. **كلمة المرور (Password)**
   - نوع الحقل: مخفي (Password)
   - مطلوب: نعم
   - الغرض: التحقق من هوية المستخدم

6. **الشركة (Company)**
   - الخيارات:
     - صقالات ابو سيف للنقل
     - شركة المحاسبة المتقدمة
     - مؤسسة التجارة الحديثة
   - القيمة الافتراضية: صقالات ابو سيف للنقل

7. **رقم الفرع (Branch Number)**
   - الخيارات:
     - 1 - الفرع الرئيسي
     - 2 - فرع الشمال
     - 3 - فرع الجنوب
   - القيمة الافتراضية: 1 - الفرع الرئيسي

8. **المركز الرئيسي (Main Center)**
   - الخيارات:
     - 1 - المركز الرئيسي
     - 2 - مركز فرعي
   - القيمة الافتراضية: 1 - المركز الرئيسي

#### 🔘 الأزرار:

- **موافقة (OK)**: لتأكيد البيانات وتسجيل الدخول
  - لون أخضر (#28a745)
  - اختصار لوحة المفاتيح: Enter

- **خروج (Exit)**: للخروج من النظام
  - لون أحمر (#dc3545)
  - اختصار لوحة المفاتيح: Escape

---

## 🔐 بيانات تسجيل الدخول

### البيانات الافتراضية:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **الدور**: مدير النظام

### إعدادات الجلسة الافتراضية:
- **السنة المالية**: 2025
- **اللغة**: عربي
- **الشركة**: صقالات ابو سيف للنقل
- **الفرع**: 1 - الفرع الرئيسي
- **الوحدة المحاسبية**: 1
- **المركز الرئيسي**: 1 - المركز الرئيسي

---

## 🎯 خطوات تسجيل الدخول

### الطريقة السريعة:
1. 🚀 تشغيل النظام: `python run_onyx.py`
2. ⏳ انتظار تحميل شاشة البداية (2 ثانية)
3. 🔑 إدخال كلمة المرور: `admin123`
4. ✅ الضغط على "موافقة" أو Enter
5. 🎉 الدخول إلى النظام بنجاح

### الطريقة المخصصة:
1. 🚀 تشغيل النظام
2. 📅 اختيار السنة المالية المطلوبة
3. 🌐 اختيار اللغة (عربي/إنجليزي)
4. 🏪 اختيار الشركة والفرع
5. 👤 اختيار المستخدم
6. 🔑 إدخال كلمة المرور
7. ✅ الضغط على "موافقة"

---

## 🎨 المميزات التصميمية

### الألوان والتنسيق:
- **الألوان الأساسية**: أزرق داكن (#2c3e50) وأزرق فاتح (#34495e)
- **ألوان الأزرار**: أخضر للموافقة، أحمر للخروج
- **الخطوط**: Segoe UI للوضوح والأناقة
- **التخطيط**: متجاوب ومتوافق مع اتجاه النص العربي

### التأثيرات البصرية:
- **تدرجات لونية** في رأس وتذييل الصفحة
- **ظلال وحدود** للحقول والأزرار
- **تأثيرات التمرير** (Hover Effects)
- **انتقالات سلسة** بين الحالات

### دعم اللغة العربية:
- **اتجاه النص**: من اليمين لليسار (RTL)
- **الخطوط العربية**: دعم كامل للنصوص العربية
- **التخطيط المرن**: يتكيف مع طول النصوص العربية

---

## 🔧 الإعدادات المتقدمة

### تخصيص الشركات:
يمكن إضافة شركات جديدة عبر تعديل الكود في:
```python
self.company_combo.addItems([
    "صقالات ابو سيف للنقل",
    "شركة جديدة",  # إضافة شركة جديدة
    "مؤسسة أخرى"   # إضافة مؤسسة أخرى
])
```

### تخصيص الفروع:
```python
self.branch_combo.addItems([
    "1 - الفرع الرئيسي",
    "2 - فرع جديد",    # إضافة فرع جديد
    "3 - فرع آخر"      # إضافة فرع آخر
])
```

### تخصيص السنوات المالية:
```python
self.year_combo.addItems([
    "2023", "2024", "2025", "2026", "2027"  # إضافة سنوات جديدة
])
```

---

## 🛡️ الأمان والحماية

### تشفير كلمات المرور:
- استخدام مكتبة `bcrypt` للتشفير القوي
- حفظ آمن في قاعدة البيانات
- عدم إظهار كلمة المرور في الواجهة

### التحقق من الصلاحيات:
- نظام أدوار متعدد (admin, accountant, user)
- التحقق من صحة البيانات قبل الدخول
- حماية من محاولات الدخول غير المصرح بها

### حماية قاعدة البيانات:
- استخدام SQLite مع حماية من SQL Injection
- نسخ احتياطية تلقائية
- تشفير البيانات الحساسة

---

## 📊 رسائل النظام

### رسائل النجاح:
- **تسجيل دخول ناجح**: عرض تفاصيل الجلسة
- **تحميل البيانات**: تأكيد تحميل الإعدادات
- **حفظ الإعدادات**: تأكيد حفظ التغييرات

### رسائل التحذير:
- **كلمة مرور فارغة**: "يرجى إدخال كلمة المرور"
- **بيانات ناقصة**: "يرجى ملء جميع الحقول المطلوبة"
- **اتصال قاعدة البيانات**: تحذيرات الاتصال

### رسائل الخطأ:
- **كلمة مرور خاطئة**: "كلمة المرور غير صحيحة"
- **مستخدم غير موجود**: "المستخدم غير مسجل في النظام"
- **خطأ في النظام**: تفاصيل الأخطاء التقنية

---

## 🔄 استكشاف الأخطاء

### المشاكل الشائعة:

#### 1. خطأ في تشغيل النظام
```bash
# الحل
pip install PyQt5 bcrypt python-dateutil
python run_onyx.py
```

#### 2. مشكلة في قاعدة البيانات
```bash
# حذف قاعدة البيانات وإعادة إنشائها
del accounting_system.db
python run_onyx.py
```

#### 3. مشكلة في الخطوط العربية
- تأكد من تثبيت خطوط عربية على النظام
- استخدم Windows 10/11 للحصول على أفضل دعم

#### 4. مشكلة في الأداء
- تأكد من توفر ذاكرة كافية (512 MB على الأقل)
- أغلق التطبيقات غير الضرورية

---

## 📈 التطوير المستقبلي

### الوحدات المخططة:
- [ ] **إدارة القيود المحاسبية**: إدخال وتعديل القيود
- [ ] **إدارة الفواتير**: فواتير البيع والشراء
- [ ] **إدارة المخزون**: تتبع المنتجات والكميات
- [ ] **التقارير المالية**: تقارير شاملة ومخصصة
- [ ] **إدارة الرواتب**: كشوف الرواتب والموظفين

### التحسينات المقترحة:
- [ ] **دعم قواعد بيانات متعددة**: MySQL, PostgreSQL
- [ ] **واجهة ويب**: نسخة ويب للوصول عن بُعد
- [ ] **تطبيق الهاتف**: نسخة للهواتف الذكية
- [ ] **التكامل السحابي**: دعم التخزين السحابي

---

## 📞 الدعم والمساعدة

### الملفات المرجعية:
- `ONYX_ERP_GUIDE.md` - هذا الدليل
- `PROJECT_SUMMARY.md` - ملخص المشروع
- `QUICK_START.md` - دليل البدء السريع

### معلومات النظام:
```bash
python system_info.py  # عرض معلومات مفصلة
```

### التواصل:
- 📧 البريد الإلكتروني: <EMAIL>
- 🌐 الموقع الإلكتروني: www.onyx-erp.com
- 📱 الهاتف: +966-XX-XXX-XXXX

---

## 🏆 الخلاصة

نظام **Onyx ERP** يوفر واجهة تسجيل دخول احترافية ومتطورة تلبي احتياجات الشركات العربية. الواجهة مصممة بعناية لتوفر:

- ✅ **سهولة الاستخدام** مع تصميم بديهي
- ✅ **الأمان العالي** مع تشفير متقدم
- ✅ **دعم اللغة العربية** بشكل كامل
- ✅ **مرونة في الإعدادات** لتناسب مختلف الشركات
- ✅ **تصميم احترافي** يعكس جودة النظام

النظام جاهز للاستخدام ويمكن تطويره تدريجياً لإضافة المزيد من الوحدات والمميزات.

---

**تاريخ الإصدار**: 2025-07-22  
**إصدار النظام**: Onyx ERP v1.0.0  
**الحالة**: ✅ جاهز للاستخدام  
**التقييم**: ⭐⭐⭐⭐⭐ (ممتاز)