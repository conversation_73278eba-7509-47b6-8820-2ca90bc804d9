#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة القيود اليومية
Journal Entries Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel, 
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QDialogButtonBox, QHeaderView, QFrame, QGroupBox,
                             QGridLayout, QTextEdit, QDateEdit, QDoubleSpinBox,
                             QSplitter, QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QIcon
from decimal import Decimal

class JournalEntriesWidget(QWidget):
    """ويدجت القيود اليومية"""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.setup_ui()
        self.load_journal_entries()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()
        
        # العنوان
        title = QLabel("📝 القيود اليومية")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title)
        
        # شريط الأدوات
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # منطقة البحث
        search_frame = self.create_search_frame()
        layout.addWidget(search_frame)
        
        # المقسم الرئيسي
        splitter = QSplitter(Qt.Vertical)
        
        # جدول القيود
        self.entries_table = self.create_entries_table()
        splitter.addWidget(self.entries_table)
        
        # جدول تفاصيل القيد
        self.details_table = self.create_details_table()
        splitter.addWidget(self.details_table)
        
        splitter.setSizes([400, 200])
        layout.addWidget(splitter)
        
        # شريط الحالة
        status_frame = self.create_status_frame()
        layout.addWidget(status_frame)
        
        self.setLayout(layout)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
            }
        """)
        
        toolbar_layout = QHBoxLayout()
        
        # أزرار الأدوات
        self.add_btn = QPushButton("➕ قيد جديد")
        self.edit_btn = QPushButton("✏️ تعديل")
        self.delete_btn = QPushButton("🗑️ حذف")
        self.post_btn = QPushButton("✅ ترحيل")
        self.unpost_btn = QPushButton("❌ إلغاء ترحيل")
        self.validate_btn = QPushButton("⚖️ التحقق من التوازن")
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.print_btn = QPushButton("🖨️ طباعة")

        buttons = [self.add_btn, self.edit_btn, self.delete_btn,
                  self.post_btn, self.unpost_btn, self.validate_btn, self.refresh_btn, self.print_btn]
        
        for btn in buttons:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 10px 15px;
                    border-radius: 5px;
                    font-weight: bold;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QPushButton:pressed {
                    background-color: #21618c;
                }
            """)
            toolbar_layout.addWidget(btn)
        
        # ربط الإشارات
        self.add_btn.clicked.connect(self.add_journal_entry)
        self.edit_btn.clicked.connect(self.edit_journal_entry)
        self.delete_btn.clicked.connect(self.delete_journal_entry)
        self.post_btn.clicked.connect(self.post_journal_entry)
        self.unpost_btn.clicked.connect(self.unpost_journal_entry)
        self.validate_btn.clicked.connect(self.validate_journal_entries)
        self.refresh_btn.clicked.connect(self.load_journal_entries)
        self.print_btn.clicked.connect(self.print_journal_entry)
        
        toolbar_layout.addStretch()
        toolbar_frame.setLayout(toolbar_layout)
        
        return toolbar_frame
    
    def create_search_frame(self):
        """إنشاء منطقة البحث"""
        search_frame = QGroupBox("🔍 البحث والتصفية")
        search_frame.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 5px;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
            }
        """)
        
        search_layout = QGridLayout()
        
        # حقول البحث
        search_layout.addWidget(QLabel("رقم القيد:"), 0, 0)
        self.search_entry_id = QLineEdit()
        self.search_entry_id.setPlaceholderText("رقم القيد...")
        search_layout.addWidget(self.search_entry_id, 0, 1)
        
        search_layout.addWidget(QLabel("من تاريخ:"), 0, 2)
        self.search_date_from = QDateEdit()
        self.search_date_from.setDate(QDate.currentDate().addDays(-30))
        self.search_date_from.setCalendarPopup(True)
        search_layout.addWidget(self.search_date_from, 0, 3)
        
        search_layout.addWidget(QLabel("إلى تاريخ:"), 1, 0)
        self.search_date_to = QDateEdit()
        self.search_date_to.setDate(QDate.currentDate())
        self.search_date_to.setCalendarPopup(True)
        search_layout.addWidget(self.search_date_to, 1, 1)
        
        search_layout.addWidget(QLabel("الحالة:"), 1, 2)
        self.search_status = QComboBox()
        self.search_status.addItems(["الكل", "مرحل", "غير مرحل"])
        search_layout.addWidget(self.search_status, 1, 3)
        
        # أزرار البحث
        search_btn = QPushButton("🔍 بحث")
        search_btn.clicked.connect(self.search_entries)
        search_layout.addWidget(search_btn, 2, 0)
        
        clear_btn = QPushButton("🗑️ مسح")
        clear_btn.clicked.connect(self.clear_search)
        search_layout.addWidget(clear_btn, 2, 1)
        
        # تنسيق أزرار البحث
        for btn in [search_btn, clear_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #229954;
                }
            """)
        
        search_frame.setLayout(search_layout)
        return search_frame
    
    def create_entries_table(self):
        """إنشاء جدول القيود"""
        table = QTableWidget()
        
        # تعيين الأعمدة
        headers = ["رقم القيد", "التاريخ", "البيان", "المبلغ", "الحالة", "المستخدم", "تاريخ الإنشاء"]
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        
        # تنسيق الجدول
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # إعدادات الجدول
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSortingEnabled(True)
        
        # تعديل عرض الأعمدة
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # رقم القيد
        header.setSectionResizeMode(1, QHeaderView.Fixed)  # التاريخ
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # البيان
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # المبلغ
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # الحالة
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # المستخدم
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # تاريخ الإنشاء
        
        table.setColumnWidth(0, 100)  # رقم القيد
        table.setColumnWidth(1, 120)  # التاريخ
        table.setColumnWidth(3, 120)  # المبلغ
        table.setColumnWidth(4, 100)  # الحالة
        table.setColumnWidth(5, 120)  # المستخدم
        table.setColumnWidth(6, 150)  # تاريخ الإنشاء
        
        # ربط النقر
        table.itemSelectionChanged.connect(self.load_entry_details)
        table.doubleClicked.connect(self.edit_journal_entry)
        
        return table
    
    def create_details_table(self):
        """إنشاء جدول تفاصيل القيد"""
        table = QTableWidget()
        
        # تعيين الأعمدة
        headers = ["رقم الحساب", "اسم الحساب", "البيان", "مدين", "دائن"]
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        
        # تنسيق الجدول
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e74c3c;
                selection-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }
            QHeaderView::section {
                background-color: #e74c3c;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # إعدادات الجدول
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # تعديل عرض الأعمدة
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # رقم الحساب
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم الحساب
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # البيان
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # مدين
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # دائن
        
        table.setColumnWidth(0, 120)  # رقم الحساب
        table.setColumnWidth(3, 120)  # مدين
        table.setColumnWidth(4, 120)  # دائن
        
        return table
    
    def create_status_frame(self):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border-top: 1px solid #bdc3c7;
                padding: 10px;
            }
        """)
        
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("جاري تحميل البيانات...")
        self.status_label.setStyleSheet("color: #7f8c8d; font-weight: bold;")
        
        self.balance_label = QLabel("")
        self.balance_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.balance_label)
        
        status_frame.setLayout(status_layout)
        return status_frame
    
    def load_journal_entries(self):
        """تحميل القيود اليومية"""
        try:
            query = """
                SELECT je.id, je.entry_date, je.description, je.total_amount,
                       je.is_posted, je.created_by, je.created_at,
                       u.full_name as user_name
                FROM journal_entries je
                LEFT JOIN users u ON je.created_by = u.id
                ORDER BY je.id DESC
            """
            
            entries = self.db_manager.execute_query(query)
            
            # مسح الجدول
            self.entries_table.setRowCount(0)
            
            # إضافة البيانات
            for i, entry in enumerate(entries):
                self.entries_table.insertRow(i)
                
                # رقم القيد
                self.entries_table.setItem(i, 0, QTableWidgetItem(str(entry['id'])))
                
                # التاريخ
                self.entries_table.setItem(i, 1, QTableWidgetItem(str(entry['entry_date'])))
                
                # البيان
                self.entries_table.setItem(i, 2, QTableWidgetItem(entry['description'] or ''))
                
                # المبلغ
                amount = f"{entry['total_amount']:,.2f}" if entry['total_amount'] else "0.00"
                self.entries_table.setItem(i, 3, QTableWidgetItem(amount))
                
                # الحالة
                status = "مرحل" if entry['is_posted'] else "غير مرحل"
                status_item = QTableWidgetItem(status)
                if entry['is_posted']:
                    status_item.setBackground(Qt.green)
                else:
                    status_item.setBackground(Qt.yellow)
                self.entries_table.setItem(i, 4, status_item)
                
                # المستخدم
                self.entries_table.setItem(i, 5, QTableWidgetItem(entry['user_name'] or ''))
                
                # تاريخ الإنشاء
                self.entries_table.setItem(i, 6, QTableWidgetItem(str(entry['created_at'])))
            
            # تحديث شريط الحالة
            count = len(entries)
            self.status_label.setText(f"تم تحميل {count} قيد")
            
            # مسح تفاصيل القيد
            self.details_table.setRowCount(0)
            self.balance_label.setText("")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل القيود:\n{str(e)}")
            self.status_label.setText("خطأ في تحميل البيانات")
    
    def load_entry_details(self):
        """تحميل تفاصيل القيد المحدد"""
        current_row = self.entries_table.currentRow()
        if current_row < 0:
            return
        
        entry_id = self.entries_table.item(current_row, 0).text()
        
        try:
            query = """
                SELECT jed.account_code, ca.account_name_ar, jed.description,
                       jed.debit_amount, jed.credit_amount
                FROM journal_entry_details jed
                LEFT JOIN chart_of_accounts ca ON jed.account_code = ca.account_code
                WHERE jed.journal_entry_id = ?
                ORDER BY jed.id
            """
            
            details = self.db_manager.execute_query(query, (entry_id,))
            
            # مسح الجدول
            self.details_table.setRowCount(0)
            
            total_debit = Decimal('0')
            total_credit = Decimal('0')
            
            # إضافة البيانات
            for i, detail in enumerate(details):
                self.details_table.insertRow(i)
                
                # رقم الحساب
                self.details_table.setItem(i, 0, QTableWidgetItem(str(detail['account_code'])))
                
                # اسم الحساب
                self.details_table.setItem(i, 1, QTableWidgetItem(detail['account_name_ar'] or ''))
                
                # البيان
                self.details_table.setItem(i, 2, QTableWidgetItem(detail['description'] or ''))
                
                # مدين
                debit = Decimal(str(detail['debit_amount'] or 0))
                debit_text = f"{debit:,.2f}" if debit > 0 else ""
                self.details_table.setItem(i, 3, QTableWidgetItem(debit_text))
                total_debit += debit
                
                # دائن
                credit = Decimal(str(detail['credit_amount'] or 0))
                credit_text = f"{credit:,.2f}" if credit > 0 else ""
                self.details_table.setItem(i, 4, QTableWidgetItem(credit_text))
                total_credit += credit
            
            # عرض الأرصدة
            balance_text = f"إجمالي المدين: {total_debit:,.2f} | إجمالي الدائن: {total_credit:,.2f}"
            if total_debit != total_credit:
                balance_text += f" | الفرق: {abs(total_debit - total_credit):,.2f}"
                self.balance_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            else:
                balance_text += " | متوازن ✓"
                self.balance_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            
            self.balance_label.setText(balance_text)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل تفاصيل القيد:\n{str(e)}")
    
    def search_entries(self):
        """البحث في القيود"""
        # تطبيق الفلاتر على الجدول
        entry_id_filter = self.search_entry_id.text().strip()
        date_from = self.search_date_from.date().toString("yyyy-MM-dd")
        date_to = self.search_date_to.date().toString("yyyy-MM-dd")
        status_filter = self.search_status.currentText()
        
        # إخفاء جميع الصفوف أولاً
        for row in range(self.entries_table.rowCount()):
            self.entries_table.setRowHidden(row, True)
        
        # إظهار الصفوف المطابقة
        visible_count = 0
        for row in range(self.entries_table.rowCount()):
            show_row = True
            
            # فلترة رقم القيد
            if entry_id_filter:
                entry_id = self.entries_table.item(row, 0).text()
                if entry_id_filter not in entry_id:
                    show_row = False
            
            # فلترة التاريخ
            if show_row:
                entry_date = self.entries_table.item(row, 1).text()
                if not (date_from <= entry_date <= date_to):
                    show_row = False
            
            # فلترة الحالة
            if status_filter != "الكل" and show_row:
                entry_status = self.entries_table.item(row, 4).text()
                if status_filter != entry_status:
                    show_row = False
            
            if show_row:
                self.entries_table.setRowHidden(row, False)
                visible_count += 1
        
        # تحديث شريط الحالة
        self.status_label.setText(f"عرض {visible_count} من أصل {self.entries_table.rowCount()} قيد")
    
    def clear_search(self):
        """مسح البحث"""
        self.search_entry_id.clear()
        self.search_date_from.setDate(QDate.currentDate().addDays(-30))
        self.search_date_to.setDate(QDate.currentDate())
        self.search_status.setCurrentIndex(0)
        
        # إظهار جميع الصفوف
        for row in range(self.entries_table.rowCount()):
            self.entries_table.setRowHidden(row, False)
        
        self.status_label.setText(f"عرض جميع القيود ({self.entries_table.rowCount()})")
    
    def add_journal_entry(self):
        """إضافة قيد جديد"""
        dialog = JournalEntryDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_journal_entries()
    
    def edit_journal_entry(self):
        """تعديل قيد"""
        current_row = self.entries_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار قيد للتعديل")
            return
        
        entry_id = int(self.entries_table.item(current_row, 0).text())
        is_posted = self.entries_table.item(current_row, 4).text() == "مرحل"
        
        if is_posted:
            QMessageBox.warning(self, "تحذير", "لا يمكن تعديل قيد مرحل")
            return
        
        dialog = JournalEntryDialog(self.db_manager, entry_id=entry_id, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_journal_entries()
    
    def delete_journal_entry(self):
        """حذف قيد"""
        current_row = self.entries_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار قيد للحذف")
            return
        
        entry_id = self.entries_table.item(current_row, 0).text()
        is_posted = self.entries_table.item(current_row, 4).text() == "مرحل"
        
        if is_posted:
            QMessageBox.warning(self, "تحذير", "لا يمكن حذف قيد مرحل")
            return
        
        reply = QMessageBox.question(
            self, 'تأكيد الحذف',
            f'هل تريد حذف القيد رقم {entry_id}؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # حذف تفاصيل القيد
                self.db_manager.execute_update(
                    "DELETE FROM journal_entry_details WHERE journal_entry_id = ?", 
                    (entry_id,)
                )
                
                # حذف القيد
                self.db_manager.execute_update(
                    "DELETE FROM journal_entries WHERE id = ?", 
                    (entry_id,)
                )
                
                QMessageBox.information(self, "نجح", "تم حذف القيد بنجاح")
                self.load_journal_entries()
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف القيد:\n{str(e)}")
    
    def post_journal_entry(self):
        """ترحيل قيد"""
        current_row = self.entries_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار قيد للترحيل")
            return
        
        entry_id = self.entries_table.item(current_row, 0).text()
        is_posted = self.entries_table.item(current_row, 4).text() == "مرحل"
        
        if is_posted:
            QMessageBox.information(self, "معلومة", "القيد مرحل مسبقاً")
            return
        
        try:
            # التحقق من توازن القيد
            query = """
                SELECT SUM(debit_amount) as total_debit, SUM(credit_amount) as total_credit
                FROM journal_entry_details 
                WHERE journal_entry_id = ?
            """
            result = self.db_manager.execute_query(query, (entry_id,))
            
            if result:
                total_debit = Decimal(str(result[0]['total_debit'] or 0))
                total_credit = Decimal(str(result[0]['total_credit'] or 0))
                
                if total_debit != total_credit:
                    QMessageBox.warning(self, "تحذير", 
                                      f"القيد غير متوازن:\nالمدين: {total_debit:,.2f}\nالدائن: {total_credit:,.2f}")
                    return
            
            # ترحيل القيد
            self.db_manager.execute_update(
                "UPDATE journal_entries SET is_posted = 1 WHERE id = ?", 
                (entry_id,)
            )
            
            QMessageBox.information(self, "نجح", "تم ترحيل القيد بنجاح")
            self.load_journal_entries()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في ترحيل القيد:\n{str(e)}")
    
    def unpost_journal_entry(self):
        """إلغاء ترحيل قيد"""
        current_row = self.entries_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار قيد لإلغاء ترحيله")
            return
        
        entry_id = self.entries_table.item(current_row, 0).text()
        is_posted = self.entries_table.item(current_row, 4).text() == "مرحل"
        
        if not is_posted:
            QMessageBox.information(self, "معلومة", "القيد غير مرحل أصلاً")
            return
        
        reply = QMessageBox.question(
            self, 'تأكيد إلغاء الترحيل',
            f'هل تريد إلغاء ترحيل القيد رقم {entry_id}؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # إلغاء ترحيل القيد
                self.db_manager.execute_update(
                    "UPDATE journal_entries SET is_posted = 0 WHERE id = ?", 
                    (entry_id,)
                )
                
                QMessageBox.information(self, "نجح", "تم إلغاء ترحيل القيد بنجاح")
                self.load_journal_entries()
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في إلغاء ترحيل القيد:\n{str(e)}")
    
    def print_journal_entry(self):
        """طباعة قيد"""
        current_row = self.entries_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار قيد للطباعة")
            return

        try:
            # الحصول على معرف القيد
            entry_id = self.entries_table.item(current_row, 0).text()

            # إنشاء حوار الطباعة
            print_dialog = JournalEntryPrintDialog(self.db_manager, entry_id, self)
            print_dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في طباعة القيد:\n{str(e)}")

    def validate_journal_entries(self):
        """التحقق من صحة جميع القيود"""
        try:
            # استعلام للتحقق من توازن القيود
            query = """
                SELECT je.id, je.entry_date, je.description,
                       COALESCE(SUM(jed.debit_amount), 0) as total_debit,
                       COALESCE(SUM(jed.credit_amount), 0) as total_credit
                FROM journal_entries je
                LEFT JOIN journal_entry_details jed ON je.id = jed.journal_entry_id
                GROUP BY je.id, je.entry_date, je.description
                HAVING total_debit != total_credit
                ORDER BY je.entry_date DESC
            """

            unbalanced_entries = self.db_manager.execute_query(query)

            if not unbalanced_entries:
                QMessageBox.information(self, "التحقق من التوازن",
                                      "✅ جميع القيود متوازنة!\n\nلا توجد قيود غير متوازنة في النظام.")
            else:
                # إنشاء تقرير بالقيود غير المتوازنة
                report = "⚠️ القيود غير المتوازنة:\n\n"
                for entry in unbalanced_entries:
                    difference = abs(entry['total_debit'] - entry['total_credit'])
                    report += f"• القيد رقم {entry['id']} - {entry['entry_date']}\n"
                    report += f"  البيان: {entry['description']}\n"
                    report += f"  الفرق: {difference:,.2f}\n\n"

                QMessageBox.warning(self, "قيود غير متوازنة", report)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في التحقق من توازن القيود:\n{str(e)}")


class JournalEntryPrintDialog(QDialog):
    """حوار طباعة القيد اليومي"""

    def __init__(self, db_manager, entry_id, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.entry_id = entry_id
        self.setup_ui()
        self.load_entry_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("طباعة القيد اليومي")
        self.setMinimumSize(600, 500)

        layout = QVBoxLayout()

        # منطقة عرض القيد
        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        self.preview_text.setFont(QFont("Courier New", 10))
        layout.addWidget(self.preview_text)

        # أزرار الحوار
        button_box = QDialogButtonBox()
        print_btn = button_box.addButton("🖨️ طباعة", QDialogButtonBox.AcceptRole)
        close_btn = button_box.addButton("إغلاق", QDialogButtonBox.RejectRole)

        print_btn.clicked.connect(self.print_entry)
        close_btn.clicked.connect(self.reject)

        layout.addWidget(button_box)
        self.setLayout(layout)

    def load_entry_data(self):
        """تحميل بيانات القيد"""
        try:
            # تحميل معلومات القيد
            entry_query = """
                SELECT je.id, je.entry_date, je.description, je.total_amount,
                       je.is_posted, je.created_at, u.full_name as created_by_name
                FROM journal_entries je
                LEFT JOIN users u ON je.created_by = u.id
                WHERE je.id = ?
            """
            entry_result = self.db_manager.execute_query(entry_query, (self.entry_id,))

            if not entry_result:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على القيد")
                return

            entry = entry_result[0]

            # تحميل تفاصيل القيد
            details_query = """
                SELECT jed.account_code, coa.account_name_ar, jed.description,
                       jed.debit_amount, jed.credit_amount
                FROM journal_entry_details jed
                LEFT JOIN chart_of_accounts coa ON jed.account_code = coa.account_code
                WHERE jed.journal_entry_id = ?
                ORDER BY jed.id
            """
            details = self.db_manager.execute_query(details_query, (self.entry_id,))

            # إنشاء نص القيد للطباعة
            self.generate_print_text(entry, details)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات القيد:\n{str(e)}")

    def generate_print_text(self, entry, details):
        """إنشاء نص القيد للطباعة"""
        text = f"""
{'='*60}
                    نظام Onyx ERP
                   القيد اليومي رقم {entry['id']}
{'='*60}

التاريخ: {entry['entry_date']}
البيان: {entry['description']}
الحالة: {'مرحل' if entry['is_posted'] else 'غير مرحل'}
تاريخ الإنشاء: {entry['created_at']}
المستخدم: {entry.get('created_by_name', 'غير محدد')}

{'-'*60}
{'رقم الحساب':<15} {'اسم الحساب':<25} {'مدين':<12} {'دائن':<12}
{'-'*60}
"""

        total_debit = Decimal('0')
        total_credit = Decimal('0')

        for detail in details:
            debit = Decimal(str(detail['debit_amount'] or 0))
            credit = Decimal(str(detail['credit_amount'] or 0))

            total_debit += debit
            total_credit += credit

            account_name = detail['account_name_ar'] or 'غير محدد'
            if len(account_name) > 23:
                account_name = account_name[:20] + "..."

            text += f"{detail['account_code']:<15} {account_name:<25} "
            text += f"{debit:>10.2f}  {credit:>10.2f}\n"

            if detail['description']:
                text += f"{'':>15} {detail['description']}\n"

        text += f"""
{'-'*60}
{'الإجمالي':<40} {total_debit:>10.2f}  {total_credit:>10.2f}
{'='*60}

الفرق: {abs(total_debit - total_credit):,.2f}
الحالة: {'متوازن' if total_debit == total_credit else 'غير متوازن'}

{'-'*60}
تاريخ الطباعة: {QDate.currentDate().toString('yyyy-MM-dd')}
نظام Onyx ERP - إدارة موارد المؤسسات
{'='*60}
"""

        self.preview_text.setPlainText(text)

    def print_entry(self):
        """طباعة القيد"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QTextDocument

            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                document = QTextDocument()
                document.setPlainText(self.preview_text.toPlainText())
                document.print_(printer)

                QMessageBox.information(self, "طباعة", "تم إرسال القيد للطباعة بنجاح")
                self.accept()

        except ImportError:
            # في حالة عدم توفر مكتبة الطباعة
            QMessageBox.information(self, "طباعة",
                                  "مكتبة الطباعة غير متوفرة.\n\nيمكنك نسخ النص ولصقه في برنامج آخر للطباعة.")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في الطباعة:\n{str(e)}")


class JournalEntryDialog(QDialog):
    """حوار إضافة/تعديل قيد يومي"""
    
    def __init__(self, db_manager, entry_id=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.entry_id = entry_id
        self.is_edit_mode = entry_id is not None
        
        self.setup_ui()
        if self.is_edit_mode:
            self.load_entry_data()
        else:
            self.add_empty_row()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل قيد يومي" if self.is_edit_mode else "قيد يومي جديد"
        self.setWindowTitle(title)
        self.setMinimumSize(800, 600)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # معلومات القيد
        info_frame = self.create_entry_info_frame()
        layout.addWidget(info_frame)
        
        # جدول التفاصيل
        self.details_table = self.create_details_table()
        layout.addWidget(self.details_table)
        
        # أزرار التفاصيل
        details_buttons = self.create_details_buttons()
        layout.addWidget(details_buttons)
        
        # معلومات الأرصدة
        balance_frame = self.create_balance_frame()
        layout.addWidget(balance_frame)
        
        # الأزرار الرئيسية
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept_dialog)
        button_box.rejected.connect(self.reject)
        
        # تنسيق الأزرار
        button_box.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                font-weight: bold;
                border-radius: 5px;
                margin: 5px;
            }
            QPushButton[text="OK"] {
                background-color: #27ae60;
                color: white;
                border: none;
            }
            QPushButton[text="OK"]:hover {
                background-color: #229954;
            }
            QPushButton[text="Cancel"] {
                background-color: #e74c3c;
                color: white;
                border: none;
            }
            QPushButton[text="Cancel"]:hover {
                background-color: #c0392b;
            }
        """)
        
        layout.addWidget(button_box)
        self.setLayout(layout)
    
    def create_entry_info_frame(self):
        """إنشاء إطار معلومات القيد"""
        info_frame = QGroupBox("معلومات القيد")
        info_layout = QGridLayout()
        
        # التاريخ
        info_layout.addWidget(QLabel("التاريخ:"), 0, 0)
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        info_layout.addWidget(self.date_edit, 0, 1)
        
        # البيان
        info_layout.addWidget(QLabel("البيان:"), 0, 2)
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("بيان القيد...")
        info_layout.addWidget(self.description_edit, 0, 3)
        
        info_frame.setLayout(info_layout)
        return info_frame
    
    def create_details_table(self):
        """إنشاء جدول تفاصيل القيد"""
        table = QTableWidget()
        
        # تعيين الأعمدة
        headers = ["رقم الحساب", "اسم الحساب", "البيان", "مدين", "دائن", ""]
        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        
        # تنسيق الجدول
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # تعديل عرض الأعمدة
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # رقم الحساب
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # اسم الحساب
        header.setSectionResizeMode(2, QHeaderView.Stretch)  # البيان
        header.setSectionResizeMode(3, QHeaderView.Fixed)  # مدين
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # دائن
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # حذف
        
        table.setColumnWidth(0, 120)  # رقم الحساب
        table.setColumnWidth(3, 120)  # مدين
        table.setColumnWidth(4, 120)  # دائن
        table.setColumnWidth(5, 50)   # حذف
        
        return table
    
    def create_details_buttons(self):
        """إنشاء أزرار التفاصيل"""
        buttons_frame = QFrame()
        buttons_layout = QHBoxLayout()
        
        add_row_btn = QPushButton("➕ إضافة سطر")
        add_row_btn.clicked.connect(self.add_empty_row)
        
        remove_row_btn = QPushButton("➖ حذف سطر")
        remove_row_btn.clicked.connect(self.remove_selected_row)
        
        for btn in [add_row_btn, remove_row_btn]:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 5px;
                    font-weight: bold;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
        
        buttons_layout.addWidget(add_row_btn)
        buttons_layout.addWidget(remove_row_btn)
        buttons_layout.addStretch()
        
        buttons_frame.setLayout(buttons_layout)
        return buttons_frame
    
    def create_balance_frame(self):
        """إنشاء إطار الأرصدة"""
        balance_frame = QFrame()
        balance_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        balance_layout = QHBoxLayout()
        
        self.total_debit_label = QLabel("إجمالي المدين: 0.00")
        self.total_credit_label = QLabel("إجمالي الدائن: 0.00")
        self.balance_status_label = QLabel("الحالة: غير متوازن")
        
        for label in [self.total_debit_label, self.total_credit_label, self.balance_status_label]:
            label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        
        balance_layout.addWidget(self.total_debit_label)
        balance_layout.addWidget(self.total_credit_label)
        balance_layout.addStretch()
        balance_layout.addWidget(self.balance_status_label)
        
        balance_frame.setLayout(balance_layout)
        return balance_frame
    
    def add_empty_row(self):
        """إضافة سطر فارغ"""
        row = self.details_table.rowCount()
        self.details_table.insertRow(row)
        
        # رقم الحساب (ComboBox)
        account_combo = QComboBox()
        account_combo.setEditable(True)
        self.load_accounts_to_combo(account_combo)
        account_combo.currentTextChanged.connect(lambda: self.update_account_name(row))
        self.details_table.setCellWidget(row, 0, account_combo)
        
        # اسم الحساب (Label)
        account_name_label = QLabel("")
        self.details_table.setCellWidget(row, 1, account_name_label)
        
        # البيان
        description_edit = QLineEdit()
        description_edit.setPlaceholderText("بيان السطر...")
        self.details_table.setCellWidget(row, 2, description_edit)
        
        # مدين
        debit_edit = QDoubleSpinBox()
        debit_edit.setRange(0, *********.99)
        debit_edit.setDecimals(2)
        debit_edit.valueChanged.connect(self.calculate_totals)
        self.details_table.setCellWidget(row, 3, debit_edit)
        
        # دائن
        credit_edit = QDoubleSpinBox()
        credit_edit.setRange(0, *********.99)
        credit_edit.setDecimals(2)
        credit_edit.valueChanged.connect(self.calculate_totals)
        self.details_table.setCellWidget(row, 4, credit_edit)
        
        # زر حذف
        delete_btn = QPushButton("🗑️")
        delete_btn.setFixedSize(30, 30)
        delete_btn.clicked.connect(lambda: self.remove_row(row))
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 15px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.details_table.setCellWidget(row, 5, delete_btn)
    
    def load_accounts_to_combo(self, combo):
        """تحميل الحسابات إلى ComboBox"""
        try:
            query = "SELECT account_code, account_name_ar FROM chart_of_accounts WHERE is_active = 1 ORDER BY account_code"
            accounts = self.db_manager.execute_query(query)
            
            combo.addItem("", "")  # خيار فارغ
            for account in accounts:
                display_text = f"{account['account_code']} - {account['account_name_ar']}"
                combo.addItem(display_text, account['account_code'])
                
        except Exception as e:
            print(f"خطأ في تحميل الحسابات: {e}")
    
    def update_account_name(self, row):
        """تحديث اسم الحساب"""
        account_combo = self.details_table.cellWidget(row, 0)
        account_name_label = self.details_table.cellWidget(row, 1)
        
        if account_combo and account_name_label:
            account_code = account_combo.currentData()
            if account_code:
                try:
                    query = "SELECT account_name_ar FROM chart_of_accounts WHERE account_code = ?"
                    result = self.db_manager.execute_query(query, (account_code,))
                    if result:
                        account_name_label.setText(result[0]['account_name_ar'])
                    else:
                        account_name_label.setText("")
                except:
                    account_name_label.setText("")
            else:
                account_name_label.setText("")
    
    def remove_row(self, row):
        """حذف سطر محدد"""
        self.details_table.removeRow(row)
        self.calculate_totals()
    
    def remove_selected_row(self):
        """حذف السطر المحدد"""
        current_row = self.details_table.currentRow()
        if current_row >= 0:
            self.details_table.removeRow(current_row)
            self.calculate_totals()
    
    def calculate_totals(self):
        """حساب الإجماليات"""
        total_debit = Decimal('0')
        total_credit = Decimal('0')
        
        for row in range(self.details_table.rowCount()):
            debit_widget = self.details_table.cellWidget(row, 3)
            credit_widget = self.details_table.cellWidget(row, 4)
            
            if debit_widget:
                total_debit += Decimal(str(debit_widget.value()))
            
            if credit_widget:
                total_credit += Decimal(str(credit_widget.value()))
        
        # تحديث التسميات
        self.total_debit_label.setText(f"إجمالي المدين: {total_debit:,.2f}")
        self.total_credit_label.setText(f"إجمالي الدائن: {total_credit:,.2f}")
        
        # حالة التوازن
        if total_debit == total_credit and total_debit > 0:
            self.balance_status_label.setText("الحالة: متوازن ✓")
            self.balance_status_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        else:
            difference = abs(total_debit - total_credit)
            self.balance_status_label.setText(f"الحالة: غير متوازن (الفرق: {difference:,.2f})")
            self.balance_status_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
    
    def load_entry_data(self):
        """تحميل بيانات القيد للتعديل"""
        try:
            # تحميل معلومات القيد
            entry_query = "SELECT entry_date, description FROM journal_entries WHERE id = ?"
            entry_result = self.db_manager.execute_query(entry_query, (self.entry_id,))
            
            if entry_result:
                entry = entry_result[0]
                self.date_edit.setDate(QDate.fromString(str(entry['entry_date']), "yyyy-MM-dd"))
                self.description_edit.setText(entry['description'] or '')
            
            # تحميل تفاصيل القيد
            details_query = """
                SELECT account_code, description, debit_amount, credit_amount
                FROM journal_entry_details 
                WHERE journal_entry_id = ?
                ORDER BY id
            """
            details = self.db_manager.execute_query(details_query, (self.entry_id,))
            
            # مسح الجدول
            self.details_table.setRowCount(0)
            
            # إضافة التفاصيل
            for detail in details:
                self.add_empty_row()
                row = self.details_table.rowCount() - 1
                
                # تعيين رقم الحساب
                account_combo = self.details_table.cellWidget(row, 0)
                for i in range(account_combo.count()):
                    if account_combo.itemData(i) == detail['account_code']:
                        account_combo.setCurrentIndex(i)
                        break
                
                # تعيين البيان
                description_edit = self.details_table.cellWidget(row, 2)
                description_edit.setText(detail['description'] or '')
                
                # تعيين المبالغ
                debit_edit = self.details_table.cellWidget(row, 3)
                debit_edit.setValue(float(detail['debit_amount'] or 0))
                
                credit_edit = self.details_table.cellWidget(row, 4)
                credit_edit.setValue(float(detail['credit_amount'] or 0))
            
            # حساب الإجماليات
            self.calculate_totals()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات القيد:\n{str(e)}")
    
    def accept_dialog(self):
        """قبول الحوار وحفظ البيانات"""
        # التحقق من صحة البيانات
        if not self.description_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال بيان القيد")
            return
        
        if self.details_table.rowCount() == 0:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة تفاصيل القيد")
            return
        
        # التحقق من التوازن
        total_debit = Decimal('0')
        total_credit = Decimal('0')
        valid_rows = 0
        
        for row in range(self.details_table.rowCount()):
            account_combo = self.details_table.cellWidget(row, 0)
            debit_widget = self.details_table.cellWidget(row, 3)
            credit_widget = self.details_table.cellWidget(row, 4)
            
            if account_combo and account_combo.currentData():
                debit_amount = Decimal(str(debit_widget.value()))
                credit_amount = Decimal(str(credit_widget.value()))
                
                if debit_amount > 0 or credit_amount > 0:
                    total_debit += debit_amount
                    total_credit += credit_amount
                    valid_rows += 1
        
        if valid_rows == 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال تفاصيل صحيحة للقيد")
            return
        
        if total_debit != total_credit:
            reply = QMessageBox.question(
                self, 'تحذير',
                f'القيد غير متوازن:\nالمدين: {total_debit:,.2f}\nالدائن: {total_credit:,.2f}\n\nهل تريد المتابعة؟',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return
        
        try:
            entry_date = self.date_edit.date().toString("yyyy-MM-dd")
            description = self.description_edit.text().strip()
            
            if self.is_edit_mode:
                # تحديث القيد
                update_entry_query = """
                    UPDATE journal_entries 
                    SET entry_date = ?, description = ?, total_amount = ?
                    WHERE id = ?
                """
                self.db_manager.execute_update(update_entry_query, 
                                            (entry_date, description, float(total_debit), self.entry_id))
                
                # حذف التفاصيل القديمة
                self.db_manager.execute_update(
                    "DELETE FROM journal_entry_details WHERE journal_entry_id = ?", 
                    (self.entry_id,)
                )
                
                entry_id = self.entry_id
            else:
                # إضافة قيد جديد
                insert_entry_query = """
                    INSERT INTO journal_entries (entry_date, description, total_amount, created_by)
                    VALUES (?, ?, ?, ?)
                """
                self.db_manager.execute_update(insert_entry_query, 
                                                     (entry_date, description, float(total_debit), 1))
                entry_id = self.db_manager.get_last_insert_id()
            
            # إضافة التفاصيل
            insert_detail_query = """
                INSERT INTO journal_entry_details 
                (journal_entry_id, account_code, description, debit_amount, credit_amount)
                VALUES (?, ?, ?, ?, ?)
            """
            
            for row in range(self.details_table.rowCount()):
                account_combo = self.details_table.cellWidget(row, 0)
                description_edit = self.details_table.cellWidget(row, 2)
                debit_widget = self.details_table.cellWidget(row, 3)
                credit_widget = self.details_table.cellWidget(row, 4)
                
                account_code = account_combo.currentData()
                if account_code:
                    detail_description = description_edit.text().strip()
                    debit_amount = debit_widget.value()
                    credit_amount = credit_widget.value()
                    
                    if debit_amount > 0 or credit_amount > 0:
                        self.db_manager.execute_update(insert_detail_query, 
                                                    (entry_id, account_code, detail_description, 
                                                     debit_amount, credit_amount))
            
            action = "تحديث" if self.is_edit_mode else "إضافة"
            QMessageBox.information(self, "نجح", f"تم {action} القيد بنجاح")
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ القيد:\n{str(e)}")