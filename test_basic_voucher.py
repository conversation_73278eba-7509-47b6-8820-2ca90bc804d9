#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أساسي للسند
Basic Voucher Test
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QPushButton, QWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class BasicVoucherTestWindow(QMainWindow):
    """نافذة اختبار أساسية"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار أساسي للسند")
        self.setGeometry(300, 300, 500, 300)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🔧 اختبار أساسي للسند")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #e91e63;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 8px;
                padding: 10px;
                margin: 10px;
                text-align: center;
            }
        """)
        layout.addWidget(title_label)
        
        # زر الاختبار
        test_btn = QPushButton("🧪 اختبار السند")
        test_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        test_btn.clicked.connect(self.test_voucher)
        layout.addWidget(test_btn)
        
        # معلومات
        info_label = QLabel("""
📋 هذا اختبار أساسي للسند الجديد

✅ الميزات الجديدة:
• رأس السند مع جميع البيانات
• بيانات الصندوق/البنك
• جدول محسن بـ 11 عمود
• بيانات المستخدم والتتبع

🔍 انقر على زر الاختبار لفتح السند
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
                margin: 10px;
                font-size: 11px;
            }
        """)
        layout.addWidget(info_label)
        
        central_widget.setLayout(layout)
    
    def test_voucher(self):
        """اختبار السند"""
        try:
            # قاعدة بيانات وهمية
            class MockDB:
                def __init__(self):
                    self.cursor = type('obj', (object,), {'lastrowid': 1})()
                def execute_query(self, query, params=None):
                    if "MAX" in query:
                        return [(0,)]
                    return [('111001', 'صندوق المحل', 'أصول', 1, 3, '111000')]
                def execute_update(self, query, params=None):
                    return True
            
            db = MockDB()
            
            # استيراد السند
            from modules.vouchers.voucher_interface import VoucherInterface
            
            # إنشاء السند
            voucher = VoucherInterface(db, "receipt", self)
            voucher.show()
            
            print("✅ تم فتح السند بنجاح!")
            
        except Exception as e:
            print(f"❌ خطأ: {e}")
            import traceback
            traceback.print_exc()


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    font = QFont("Tahoma", 10)
    app.setFont(font)
    
    window = BasicVoucherTestWindow()
    window.show()
    
    print("🔧 تم تشغيل الاختبار الأساسي!")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
