#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام الرئيسي
Test Main System Interface
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    print("🧪 اختبار النظام الرئيسي...")
    
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    print("✅ تم استيراد PyQt5 بنجاح")
    
    from src.main_system import MainSystemWindow
    print("✅ تم استيراد النظام الرئيسي")
    
    # إنشاء التطبيق
    app = QApplication([])
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إنشاء النافذة الرئيسية
    window = MainSystemWindow()
    print("✅ تم إنشاء النافذة الرئيسية بنجاح")
    
    # عرض النافذة
    window.show()
    print("🎉 تم عرض النظام الرئيسي!")
    print("📋 الميزات المتاحة:")
    print("   ✓ قائمة جانبية شاملة في اليمين")
    print("   ✓ 9 أقسام رئيسية مع رموز توضيحية")
    print("   ✓ إمكانية التوسعة والانهيار")
    print("   ✓ أزرار سريعة في الصفحة الرئيسية")
    print("   ✓ شريط أدوات مع وظائف سريعة")
    print("   ✓ شريط قوائم كامل")
    print("   ✓ شريط حالة معلوماتي")
    print("   ✓ واجهة احترافية ومنظمة")
    print("")
    print("🎯 الأقسام المتاحة:")
    print("   🔧 إدارة النظام")
    print("   💰 أنظمة الحسابات") 
    print("   💵 أنظمة الصندوق")
    print("   📦 أنظمة المخازن")
    print("   🏭 أنظمة الموردين")
    print("   👥 أنظمة العملاء")
    print("   🛒 نظام نقاط البيع (POS)")
    print("   📊 نظام إدارة المعلومات")
    print("   🛠️ الأنظمة المساعدة")
    print("")
    print("🔧 كيفية الاستخدام:")
    print("   • انقر على الأقسام في القائمة الجانبية للتوسعة")
    print("   • انقر على العناصر الفرعية لفتحها")
    print("   • استخدم الأزرار السريعة في الصفحة الرئيسية")
    print("   • استخدم شريط الأدوات للوصول السريع")
    print("   • فاتورة المبيعات متاحة وجاهزة للاستخدام")
    
    # تشغيل التطبيق
    app.exec_()
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
