#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف إضافية لشاشة تعريف الفرع
Additional Functions for Branch Definition
"""

from PyQt5.QtWidgets import (QMessageBox, QTableWidgetItem, QGroupBox, QGridLayout,
                             QLabel, QCheckBox, QTimeEdit, QSpinBox, QComboBox,
                             QListWidget, QListWidgetItem, QPushButton, QHBoxLayout,
                             QVBoxLayout, QWidget, QTextEdit, QProgressBar)
from PyQt5.QtCore import Qt, QTime
from datetime import datetime

def create_working_hours_tab_function(self):
    """إنشاء تبويب أوقات العمل"""
    tab = QWidget()
    layout = QVBoxLayout()
    
    # مجموعة أوقات العمل الأساسية
    working_hours_group = QGroupBox("⏰ أوقات العمل الأساسية")
    working_layout = QGridLayout()
    
    # أيام العمل
    days = ["السبت", "الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة"]
    self.working_days = {}
    self.start_times = {}
    self.end_times = {}
    
    working_layout.addWidget(QLabel("اليوم"), 0, 0)
    working_layout.addWidget(QLabel("يوم عمل"), 0, 1)
    working_layout.addWidget(QLabel("وقت البداية"), 0, 2)
    working_layout.addWidget(QLabel("وقت النهاية"), 0, 3)
    
    for i, day in enumerate(days, 1):
        working_layout.addWidget(QLabel(day), i, 0)
        
        # يوم عمل
        day_checkbox = QCheckBox()
        if day not in ["الجمعة"]:  # الجمعة عطلة افتراضياً
            day_checkbox.setChecked(True)
        self.working_days[day] = day_checkbox
        working_layout.addWidget(day_checkbox, i, 1)
        
        # وقت البداية
        start_time = QTimeEdit()
        start_time.setTime(QTime(8, 0))  # 8:00 صباحاً
        self.start_times[day] = start_time
        working_layout.addWidget(start_time, i, 2)
        
        # وقت النهاية
        end_time = QTimeEdit()
        end_time.setTime(QTime(16, 0))  # 4:00 مساءً
        self.end_times[day] = end_time
        working_layout.addWidget(end_time, i, 3)
    
    working_hours_group.setLayout(working_layout)
    
    # مجموعة الاستراحات
    break_group = QGroupBox("☕ فترات الاستراحة")
    break_layout = QGridLayout()
    
    # استراحة الغداء
    break_layout.addWidget(QLabel("استراحة الغداء:"), 0, 0)
    self.lunch_break = QCheckBox("مفعلة")
    self.lunch_break.setChecked(True)
    break_layout.addWidget(self.lunch_break, 0, 1)
    
    # وقت بداية الغداء
    break_layout.addWidget(QLabel("من:"), 0, 2)
    self.lunch_start = QTimeEdit()
    self.lunch_start.setTime(QTime(12, 0))  # 12:00 ظهراً
    break_layout.addWidget(self.lunch_start, 0, 3)
    
    # وقت نهاية الغداء
    break_layout.addWidget(QLabel("إلى:"), 0, 4)
    self.lunch_end = QTimeEdit()
    self.lunch_end.setTime(QTime(13, 0))  # 1:00 ظهراً
    break_layout.addWidget(self.lunch_end, 0, 5)
    
    # استراحة الصلاة
    break_layout.addWidget(QLabel("استراحة الصلاة:"), 1, 0)
    self.prayer_break = QCheckBox("مفعلة")
    self.prayer_break.setChecked(True)
    break_layout.addWidget(self.prayer_break, 1, 1)
    
    # مدة استراحة الصلاة
    break_layout.addWidget(QLabel("المدة (دقيقة):"), 1, 2)
    self.prayer_duration = QSpinBox()
    self.prayer_duration.setRange(5, 60)
    self.prayer_duration.setValue(15)
    break_layout.addWidget(self.prayer_duration, 1, 3)
    
    break_group.setLayout(break_layout)
    
    # مجموعة العطل والإجازات
    holidays_group = QGroupBox("🎉 العطل والإجازات")
    holidays_layout = QGridLayout()
    
    # العطل الرسمية
    holidays_layout.addWidget(QLabel("العطل الرسمية:"), 0, 0)
    self.official_holidays = QCheckBox("يطبق العطل الرسمية")
    self.official_holidays.setChecked(True)
    holidays_layout.addWidget(self.official_holidays, 0, 1)
    
    # العطل الدينية
    holidays_layout.addWidget(QLabel("العطل الدينية:"), 0, 2)
    self.religious_holidays = QCheckBox("يطبق العطل الدينية")
    self.religious_holidays.setChecked(True)
    holidays_layout.addWidget(self.religious_holidays, 0, 3)
    
    # إجازة نهاية الأسبوع
    holidays_layout.addWidget(QLabel("إجازة نهاية الأسبوع:"), 1, 0)
    self.weekend_type = QComboBox()
    self.weekend_type.addItems(["الجمعة", "الجمعة والسبت", "السبت والأحد", "مخصص"])
    holidays_layout.addWidget(self.weekend_type, 1, 1)
    
    holidays_group.setLayout(holidays_layout)
    
    layout.addWidget(working_hours_group)
    layout.addWidget(break_group)
    layout.addWidget(holidays_group)
    layout.addStretch()
    
    tab.setLayout(layout)
    return tab

def create_services_tab_function(self):
    """إنشاء تبويب الخدمات والمرافق"""
    tab = QWidget()
    layout = QVBoxLayout()
    
    # مجموعة الخدمات المتاحة
    services_group = QGroupBox("🛎️ الخدمات المتاحة")
    services_layout = QGridLayout()
    
    # قائمة الخدمات
    services = [
        "خدمة العملاء", "المبيعات", "المشتريات", "المحاسبة",
        "الموارد البشرية", "تقنية المعلومات", "التسويق", "المخازن",
        "الصيانة", "الأمن", "التدريب", "الاستشارات"
    ]
    
    self.available_services = {}
    for i, service in enumerate(services):
        row = i // 3
        col = i % 3
        checkbox = QCheckBox(service)
        if service in ["خدمة العملاء", "المبيعات", "المحاسبة"]:
            checkbox.setChecked(True)
        self.available_services[service] = checkbox
        services_layout.addWidget(checkbox, row, col)
    
    services_group.setLayout(services_layout)
    
    # مجموعة المرافق والتجهيزات
    facilities_group = QGroupBox("🏗️ المرافق والتجهيزات")
    facilities_layout = QGridLayout()
    
    # قائمة المرافق
    facilities = [
        "موقف سيارات", "مصعد", "مولد كهرباء", "نظام إنذار",
        "كاميرات مراقبة", "نظام إطفاء", "تكييف مركزي", "إنترنت عالي السرعة",
        "قاعة اجتماعات", "كافتيريا", "مصلى", "عيادة طبية"
    ]
    
    self.available_facilities = {}
    for i, facility in enumerate(facilities):
        row = i // 3
        col = i % 3
        checkbox = QCheckBox(facility)
        if facility in ["موقف سيارات", "تكييف مركزي", "إنترنت عالي السرعة"]:
            checkbox.setChecked(True)
        self.available_facilities[facility] = checkbox
        facilities_layout.addWidget(checkbox, row, col)
    
    facilities_group.setLayout(facilities_layout)
    
    # مجموعة معلومات إضافية
    additional_group = QGroupBox("ℹ️ معلومات إضافية")
    additional_layout = QGridLayout()
    
    # عدد الطوابق
    additional_layout.addWidget(QLabel("عدد الطوابق:"), 0, 0)
    self.floors_count = QSpinBox()
    self.floors_count.setRange(1, 50)
    self.floors_count.setValue(2)
    additional_layout.addWidget(self.floors_count, 0, 1)
    
    # المساحة الإجمالية
    additional_layout.addWidget(QLabel("المساحة (متر مربع):"), 0, 2)
    self.total_area = QSpinBox()
    self.total_area.setRange(50, 10000)
    self.total_area.setValue(500)
    additional_layout.addWidget(self.total_area, 0, 3)
    
    # عدد المكاتب
    additional_layout.addWidget(QLabel("عدد المكاتب:"), 1, 0)
    self.offices_count = QSpinBox()
    self.offices_count.setRange(1, 100)
    self.offices_count.setValue(10)
    additional_layout.addWidget(self.offices_count, 1, 1)
    
    # عدد أماكن الانتظار
    additional_layout.addWidget(QLabel("أماكن انتظار العملاء:"), 1, 2)
    self.waiting_seats = QSpinBox()
    self.waiting_seats.setRange(5, 200)
    self.waiting_seats.setValue(20)
    additional_layout.addWidget(self.waiting_seats, 1, 3)
    
    additional_group.setLayout(additional_layout)
    
    layout.addWidget(services_group)
    layout.addWidget(facilities_group)
    layout.addWidget(additional_group)
    layout.addStretch()
    
    tab.setLayout(layout)
    return tab

def create_security_tab_function(self):
    """إنشاء تبويب الأمان والصلاحيات"""
    tab = QWidget()
    layout = QVBoxLayout()
    
    # مجموعة إعدادات الأمان
    security_group = QGroupBox("🔒 إعدادات الأمان")
    security_layout = QGridLayout()
    
    # مستوى الأمان
    security_layout.addWidget(QLabel("مستوى الأمان:"), 0, 0)
    self.security_level = QComboBox()
    self.security_level.addItems(["منخفض", "متوسط", "عالي", "عالي جداً"])
    self.security_level.setCurrentText("متوسط")
    security_layout.addWidget(self.security_level, 0, 1)
    
    # نظام المراقبة
    self.surveillance_system = QCheckBox("نظام مراقبة مفعل")
    self.surveillance_system.setChecked(True)
    security_layout.addWidget(self.surveillance_system, 0, 2, 1, 2)
    
    # نظام الإنذار
    self.alarm_system = QCheckBox("نظام إنذار مفعل")
    self.alarm_system.setChecked(True)
    security_layout.addWidget(self.alarm_system, 1, 0, 1, 2)
    
    # حراسة أمنية
    self.security_guard = QCheckBox("حراسة أمنية")
    security_layout.addWidget(self.security_guard, 1, 2, 1, 2)
    
    security_group.setLayout(security_layout)
    
    # مجموعة صلاحيات الوصول
    access_group = QGroupBox("🔑 صلاحيات الوصول")
    access_layout = QGridLayout()
    
    # قائمة الصلاحيات
    permissions = [
        "إدارة المبيعات", "إدارة المشتريات", "إدارة المخزون", "إدارة العملاء",
        "إدارة الموردين", "إدارة الموظفين", "إدارة التقارير", "إدارة النظام",
        "إدارة الحسابات", "إدارة الصندوق", "إدارة الفواتير", "إدارة الخصومات"
    ]
    
    self.branch_permissions = {}
    for i, permission in enumerate(permissions):
        row = i // 3
        col = i % 3
        checkbox = QCheckBox(permission)
        if permission in ["إدارة المبيعات", "إدارة العملاء", "إدارة الفواتير"]:
            checkbox.setChecked(True)
        self.branch_permissions[permission] = checkbox
        access_layout.addWidget(checkbox, row, col)
    
    access_group.setLayout(access_layout)
    
    # مجموعة إعدادات النسخ الاحتياطي
    backup_group = QGroupBox("💾 إعدادات النسخ الاحتياطي")
    backup_layout = QGridLayout()
    
    # نسخ احتياطي تلقائي
    self.auto_backup = QCheckBox("نسخ احتياطي تلقائي")
    self.auto_backup.setChecked(True)
    backup_layout.addWidget(self.auto_backup, 0, 0, 1, 2)
    
    # تكرار النسخ الاحتياطي
    backup_layout.addWidget(QLabel("تكرار النسخ:"), 0, 2)
    self.backup_frequency = QComboBox()
    self.backup_frequency.addItems(["يومي", "أسبوعي", "شهري"])
    self.backup_frequency.setCurrentText("يومي")
    backup_layout.addWidget(self.backup_frequency, 0, 3)
    
    # مكان حفظ النسخ
    backup_layout.addWidget(QLabel("مكان الحفظ:"), 1, 0)
    self.backup_location = QComboBox()
    self.backup_location.addItems(["محلي", "سحابي", "كلاهما"])
    self.backup_location.setCurrentText("كلاهما")
    backup_layout.addWidget(self.backup_location, 1, 1)
    
    backup_group.setLayout(backup_layout)
    
    layout.addWidget(security_group)
    layout.addWidget(access_group)
    layout.addWidget(backup_group)
    layout.addStretch()
    
    tab.setLayout(layout)
    return tab

def create_control_buttons_function(self):
    """إنشاء أزرار التحكم"""
    layout = QHBoxLayout()
    
    # زر الحفظ
    save_btn = QPushButton("💾 حفظ تعريف الفرع")
    save_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    save_btn.clicked.connect(self.save_branch_definition)
    
    # زر الإلغاء
    cancel_btn = QPushButton("❌ إلغاء")
    cancel_btn.setStyleSheet("""
        QPushButton {
            background-color: #e74c3c;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #c0392b;
        }
    """)
    cancel_btn.clicked.connect(self.reject)
    
    # زر المعاينة
    preview_btn = QPushButton("👁️ معاينة التعريف")
    preview_btn.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    preview_btn.clicked.connect(self.preview_branch_definition)
    
    # زر المساعدة
    help_btn = QPushButton("❓ المساعدة")
    help_btn.setStyleSheet("""
        QPushButton {
            background-color: #9b59b6;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #8e44ad;
        }
    """)
    help_btn.clicked.connect(self.show_help)
    
    layout.addWidget(help_btn)
    layout.addWidget(preview_btn)
    layout.addStretch()
    layout.addWidget(save_btn)
    layout.addWidget(cancel_btn)
    
    return layout
