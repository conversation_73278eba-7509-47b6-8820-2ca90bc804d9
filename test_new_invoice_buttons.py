#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الأزرار الجديدة للفواتير في وحدة العملاء
Test New Invoice Buttons in Customers Module
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_buttons():
    """اختبار الأزرار الجديدة"""
    try:
        from PyQt5.QtWidgets import QApplication
        from src.modules.customers_suppliers_module import CustomersSupplierModule
        from src.database.sqlite_manager import SQLiteManager

        print("🧪 اختبار الأزرار الجديدة للفواتير...")
        
        # إنشاء تطبيق مؤقت
        app = QApplication([])
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # إنشاء وحدة العملاء
        module = CustomersSupplierModule(db_manager)
        
        print("\n📋 فحص الأزرار الجديدة:")
        
        # فحص الأزرار الجديدة
        new_buttons = [
            ('invoice_sales_btn', 'فاتورة مبيعات (جديد)'),
            ('invoice_return_btn', 'مردود مبيعات (جديد)'),
        ]
        
        found_new_buttons = 0
        
        for button_attr, button_desc in new_buttons:
            if hasattr(module, button_attr):
                button = getattr(module, button_attr)
                print(f"✅ {button_desc}: {button.text()}")
                found_new_buttons += 1
            else:
                print(f"❌ {button_desc}: غير موجود")
        
        # فحص الدوال الجديدة
        print("\n🔧 فحص الدوال الجديدة:")
        new_functions = [
            ('create_sales_invoice', 'إنشاء فاتورة مبيعات'),
            ('create_sales_return', 'إنشاء مردود مبيعات')
        ]
        
        found_new_functions = 0
        
        for func_attr, func_desc in new_functions:
            if hasattr(module, func_attr):
                print(f"✅ {func_desc}: موجودة")
                found_new_functions += 1
            else:
                print(f"❌ {func_desc}: غير موجودة")
        
        print(f"\n📊 النتيجة:")
        print(f"   🔘 الأزرار الجديدة: {found_new_buttons}/2")
        print(f"   🔧 الدوال الجديدة: {found_new_functions}/2")
        
        if found_new_buttons == 2 and found_new_functions == 2:
            print("🎉 جميع الأزرار والدوال الجديدة موجودة!")
            return True
        else:
            print("⚠️ بعض الأزرار أو الدوال مفقودة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_customers_module():
    """تشغيل وحدة العملاء مباشرة"""
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow
        from src.modules.customers_suppliers_module import CustomersSupplierModule
        from src.database.sqlite_manager import SQLiteManager

        print("\n🚀 تشغيل وحدة العملاء مع الأزرار الجديدة...")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # إنشاء النافذة الرئيسية
        window = QMainWindow()
        window.setWindowTitle("وحدة العملاء والموردين - مع أزرار الفواتير الجديدة")
        window.setGeometry(100, 100, 1400, 900)
        
        # إنشاء وحدة العملاء
        customers_module = CustomersSupplierModule(db_manager)
        window.setCentralWidget(customers_module)
        
        # عرض النافذة
        window.show()
        
        print("✅ تم فتح وحدة العملاء والموردين")
        print("\n🎯 ابحث عن الأزرار الجديدة في شريط الأدوات:")
        print("   🧾 فاتورة مبيعات")
        print("   ↩️ مردود مبيعات")
        print("\n💡 لاختبار الأزرار:")
        print("   1. حدد عميل من القائمة")
        print("   2. اضغط على أحد الأزرار الجديدة")
        print("   3. ستظهر رسالة تأكيد أو نافذة الفاتورة")
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل وحدة العملاء: {e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🧪 اختبار الأزرار الجديدة للفواتير في وحدة العملاء")
    print("=" * 70)
    
    # اختبار وجود الأزرار والدوال
    success = test_new_buttons()
    
    if success:
        print("\n" + "=" * 70)
        print("🎉 جميع الاختبارات نجحت!")
        print("🚀 سيتم الآن فتح وحدة العملاء لتجربة الأزرار الجديدة...")
        print("=" * 70)
        
        # تشغيل وحدة العملاء
        run_customers_module()
    else:
        print("\n⚠️ فشلت بعض الاختبارات - يرجى مراجعة الأخطاء أعلاه")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
