#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام العملاء المحدث مع بيانات العملاء
Updated Customer System Test with Customer Data
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_updated_customer_system():
    """اختبار نظام العملاء المحدث"""
    try:
        print("🚀 تشغيل نظام العملاء المحدث...")
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # اختبار نظام العملاء
        try:
            from modules.customers.customer_management import CustomerManagementWindow
            print("✅ تم استيراد نظام إدارة العملاء")
        except ImportError as e:
            print(f"❌ خطأ في استيراد نظام العملاء: {e}")
            return False

        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass

        try:
            window = CustomerManagementWindow(MockDBManager())
            print("✅ تم إنشاء نظام إدارة العملاء بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء نظام العملاء: {e}")
            import traceback
            traceback.print_exc()
            return False

        window.show()
        print("👥 نظام إدارة العملاء المحدث جاهز!")
        
        print("\n📋 التحديثات الجديدة:")
        print("   ✅ إضافة تبويب 'بيانات العملاء' كرقم 5")
        print("   ✅ جدول بيانات مفصل مع 12 عمود")
        print("   ✅ إحصائيات شاملة للعملاء")
        print("   ✅ وظائف تصدير واستيراد البيانات")
        print("   ✅ نسخ احتياطية للبيانات")
        print("   ✅ تحديث البيانات النموذجية")
        
        print("\n📊 تبويب بيانات العملاء الجديد:")
        print("   📋 جدول مفصل مع 12 عمود:")
        print("      • رقم العميل")
        print("      • الاسم الكامل")
        print("      • الشركة")
        print("      • الهاتف")
        print("      • البريد الإلكتروني")
        print("      • العنوان")
        print("      • المدينة")
        print("      • التصنيف")
        print("      • الرصيد")
        print("      • حد الائتمان")
        print("      • تاريخ التسجيل")
        print("      • آخر معاملة")
        
        print("\n🛠️ الوظائف المتاحة:")
        print("   🔄 تحديث البيانات - تحديث فوري للجدول والإحصائيات")
        print("   📤 تصدير البيانات - تصدير إلى CSV مع جميع التفاصيل")
        print("   📥 استيراد البيانات - استيراد من ملفات خارجية (قيد التطوير)")
        print("   💾 نسخ احتياطي - حفظ البيانات بصيغة JSON")
        
        print("\n📊 الإحصائيات المعروضة:")
        print("   👥 إجمالي العملاء - العدد الكلي للعملاء")
        print("   ✅ العملاء النشطون - العملاء ذوو الحالة النشطة")
        print("   💰 إجمالي الأرصدة - مجموع أرصدة جميع العملاء")
        
        print("\n📋 البيانات النموذجية المحدثة (8 عملاء):")
        customers_data = [
            "C001 - أحمد محمد علي الشامي (شركة الشامي للتجارة)",
            "C002 - فاطمة سالم أحمد (مؤسسة سالم التجارية)",
            "C003 - محمد عبدالله الحوثي (مكتب الحوثي للخدمات)",
            "C004 - عائشة علي محمد (شركة علي للاستيراد)",
            "C005 - خالد أحمد الزبيري (مؤسسة الزبيري التجارية)",
            "C006 - نادية سعد الدين (مكتب سعد الدين للخدمات)",
            "C007 - عبدالرحمن محمد الأهدل (شركة الأهدل للاستثمار)",
            "C008 - سميرة عبدالله باشا (مؤسسة باشا التجارية)"
        ]
        
        for i, customer in enumerate(customers_data, 1):
            print(f"   {i}. {customer}")
        
        print("\n💰 إحصائيات الأرصدة:")
        balances = [15000, 25000, 5000, 30000, 18000, 8000, 45000, 35000]
        total_balance = sum(balances)
        print(f"   💰 إجمالي الأرصدة: {total_balance:,} ريال")
        print(f"   📊 متوسط الرصيد: {total_balance/len(balances):,.2f} ريال")
        print(f"   🔝 أعلى رصيد: {max(balances):,} ريال")
        print(f"   🔻 أقل رصيد: {min(balances):,} ريال")
        
        print("\n🏷️ توزيع العملاء حسب التصنيف:")
        categories = ["عميل مميز", "عميل VIP", "عميل عادي", "عميل تجاري"]
        category_counts = {"عميل مميز": 2, "عميل VIP": 2, "عميل عادي": 2, "عميل تجاري": 2}
        for category, count in category_counts.items():
            print(f"   🏷️ {category}: {count} عميل")
        
        print("\n🌍 التوزيع الجغرافي:")
        locations = ["صنعاء", "عدن", "صعدة", "تعز", "الحديدة", "إب", "ذمار", "حضرموت"]
        for i, location in enumerate(locations, 1):
            print(f"   🌍 {location}: عميل واحد")
        
        print("\n🎯 كيفية الاستخدام:")
        print("   1. انقر على تبويب 'بيانات العملاء' (التبويب رقم 5)")
        print("   2. استعرض الجدول المفصل لجميع بيانات العملاء")
        print("   3. استخدم أزرار التحكم لإدارة البيانات:")
        print("      • 🔄 تحديث البيانات لتحديث الجدول")
        print("      • 📤 تصدير البيانات لحفظ ملف CSV")
        print("      • 💾 نسخ احتياطي لحفظ ملف JSON")
        print("   4. راجع الإحصائيات في أسفل الشاشة")
        
        print("\n📁 ملفات التصدير:")
        print("   📄 CSV: customer_data_YYYY-MM-DD.csv")
        print("   💾 JSON: customers_backup_YYYYMMDD_HHMMSS.json")
        
        print("\n🎨 التحسينات التصميمية:")
        print("   • جدول مع 12 عمود مرتب ومنسق")
        print("   • أزرار ملونة حسب الوظيفة")
        print("   • إحصائيات ملونة ومرئية")
        print("   • تخطيط احترافي ومنظم")
        
        print("\n📈 الإحصائيات العامة:")
        print("   👥 8 عملاء نموذجيين")
        print("   🏢 8 شركات مختلفة")
        print("   🌍 8 محافظات يمنية")
        print("   🏷️ 4 تصنيفات للعملاء")
        print("   💰 181,000 ريال إجمالي الأرصدة")
        print("   📊 5 تبويبات شاملة")
        
        print("\n🎉 نظام العملاء المحدث جاهز للاستخدام!")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_updated_customer_system()
