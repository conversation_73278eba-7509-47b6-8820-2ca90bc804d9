#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة إدارة البيانات المتكاملة
Integrated Data Management GUI
"""

import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QLabel, QLineEdit, QPushButton, 
                             QGroupBox, QTextEdit, QTabWidget, QComboBox,
                             QApplication, QMessageBox, QListWidget,
                             QTableWidget, QTableWidgetItem, QHeaderView,
                             QFormLayout, QSpinBox, QDoubleSpinBox,
                             QCheckBox, QDateEdit, QSplitter, QFrame)
from PyQt5.QtCore import Qt, QTimer, QDate
from PyQt5.QtGui import QFont, QColor, QPalette

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from database.data_manager import DataManager, get_data_manager
except ImportError:
    print("تحذير: لم يتم العثور على مدير البيانات")
    DataManager = None

class DataStatsWidget(QWidget):
    """ودجت إحصائيات البيانات"""
    
    def __init__(self, data_manager=None, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_ui()
        self.load_stats()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QGridLayout()
        
        # بطاقة المستخدمين
        users_card = self.create_stat_card("👥 المستخدمين", "0", "#007bff")
        layout.addWidget(users_card, 0, 0)
        
        # بطاقة العملاء
        customers_card = self.create_stat_card("🏢 العملاء", "0", "#28a745")
        layout.addWidget(customers_card, 0, 1)
        
        # بطاقة المنتجات
        products_card = self.create_stat_card("📦 المنتجات", "0", "#ffc107")
        layout.addWidget(products_card, 0, 2)
        
        # بطاقة قيمة المخزون
        inventory_card = self.create_stat_card("💰 قيمة المخزون", "0 ر.ي", "#dc3545")
        layout.addWidget(inventory_card, 0, 3)
        
        self.setLayout(layout)
        
        # حفظ مراجع البطاقات
        self.stat_cards = {
            'users': users_card,
            'customers': customers_card,
            'products': products_card,
            'inventory': inventory_card
        }
    
    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-left: 4px solid {color};
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }}
        """)
        
        layout = QVBoxLayout()
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                font-weight: bold;
                color: {color};
            }}
        """)
        
        value_label = QLabel(value)
        value_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        card.setLayout(layout)
        
        # حفظ مرجع للقيمة
        card.value_label = value_label
        
        return card
    
    def load_stats(self):
        """تحميل الإحصائيات"""
        try:
            if self.data_manager:
                stats = self.data_manager.get_dashboard_stats()
                self.update_stats(stats)
            else:
                # بيانات وهمية
                demo_stats = {
                    'users_count': 5,
                    'customers_count': 25,
                    'products_count': 150,
                    'inventory_value': 2500000
                }
                self.update_stats(demo_stats)
        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")
    
    def update_stats(self, stats):
        """تحديث الإحصائيات"""
        self.stat_cards['users'].value_label.setText(str(stats.get('users_count', 0)))
        self.stat_cards['customers'].value_label.setText(str(stats.get('customers_count', 0)))
        self.stat_cards['products'].value_label.setText(str(stats.get('products_count', 0)))
        
        inventory_value = stats.get('inventory_value', 0)
        if inventory_value > 1000000:
            value_text = f"{inventory_value/1000000:.1f}م ر.ي"
        elif inventory_value > 1000:
            value_text = f"{inventory_value/1000:.1f}ألف ر.ي"
        else:
            value_text = f"{inventory_value:.0f} ر.ي"
        
        self.stat_cards['inventory'].value_label.setText(value_text)

class UsersManagementWidget(QWidget):
    """ودجت إدارة المستخدمين"""
    
    def __init__(self, data_manager=None, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_ui()
        self.load_users()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QHBoxLayout()
        
        # قائمة المستخدمين
        left_panel = QWidget()
        left_layout = QVBoxLayout()
        
        # شريط أدوات المستخدمين
        toolbar_layout = QHBoxLayout()
        
        add_user_btn = QPushButton("➕ إضافة مستخدم")
        add_user_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_user_btn.clicked.connect(self.add_user)
        
        edit_user_btn = QPushButton("✏️ تعديل")
        edit_user_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        edit_user_btn.clicked.connect(self.edit_user)
        
        delete_user_btn = QPushButton("🗑️ حذف")
        delete_user_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        delete_user_btn.clicked.connect(self.delete_user)
        
        toolbar_layout.addWidget(add_user_btn)
        toolbar_layout.addWidget(edit_user_btn)
        toolbar_layout.addWidget(delete_user_btn)
        toolbar_layout.addStretch()
        
        left_layout.addLayout(toolbar_layout)
        
        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(6)
        self.users_table.setHorizontalHeaderLabels([
            "المعرف", "اسم المستخدم", "البريد الإلكتروني", "الاسم الكامل", "الدور", "الحالة"
        ])
        
        header = self.users_table.horizontalHeader()
        header.setStretchLastSection(True)
        
        self.users_table.setAlternatingRowColors(True)
        self.users_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        left_layout.addWidget(self.users_table)
        left_panel.setLayout(left_layout)
        
        # نموذج المستخدم
        right_panel = QGroupBox("تفاصيل المستخدم")
        right_layout = QFormLayout()
        
        self.username_edit = QLineEdit()
        self.email_edit = QLineEdit()
        self.full_name_edit = QLineEdit()
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        
        self.role_combo = QComboBox()
        self.role_combo.addItems(["user", "manager", "admin"])
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["active", "inactive", "suspended"])
        
        right_layout.addRow("اسم المستخدم:", self.username_edit)
        right_layout.addRow("البريد الإلكتروني:", self.email_edit)
        right_layout.addRow("الاسم الكامل:", self.full_name_edit)
        right_layout.addRow("كلمة المرور:", self.password_edit)
        right_layout.addRow("الدور:", self.role_combo)
        right_layout.addRow("الحالة:", self.status_combo)
        
        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        save_btn.clicked.connect(self.save_user)
        
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_btn.clicked.connect(self.clear_form)
        
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addStretch()
        
        right_layout.addRow(buttons_layout)
        right_panel.setLayout(right_layout)
        
        # تخطيط رئيسي
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([600, 400])
        
        layout.addWidget(splitter)
        self.setLayout(layout)
        
        # ربط الأحداث
        self.users_table.itemSelectionChanged.connect(self.on_user_selected)
        
        # متغيرات التحكم
        self.current_user_id = None
        self.edit_mode = False
    
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            if self.data_manager:
                users = self.data_manager.get_users()
            else:
                # بيانات وهمية
                users = [
                    {'id': 1, 'username': 'admin', 'email': '<EMAIL>', 'full_name': 'مدير النظام', 'role': 'admin', 'status': 'active'},
                    {'id': 2, 'username': 'user1', 'email': '<EMAIL>', 'full_name': 'مستخدم أول', 'role': 'user', 'status': 'active'},
                ]
            
            self.populate_users_table(users)
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المستخدمين:\n{str(e)}")
    
    def populate_users_table(self, users):
        """ملء جدول المستخدمين"""
        self.users_table.setRowCount(len(users))
        
        for row, user in enumerate(users):
            self.users_table.setItem(row, 0, QTableWidgetItem(str(user['id'])))
            self.users_table.setItem(row, 1, QTableWidgetItem(user['username']))
            self.users_table.setItem(row, 2, QTableWidgetItem(user['email']))
            self.users_table.setItem(row, 3, QTableWidgetItem(user['full_name']))
            self.users_table.setItem(row, 4, QTableWidgetItem(user['role']))
            self.users_table.setItem(row, 5, QTableWidgetItem(user['status']))
            
            # حفظ بيانات المستخدم في العنصر
            self.users_table.item(row, 0).setData(Qt.UserRole, user)
    
    def on_user_selected(self):
        """عند اختيار مستخدم"""
        current_row = self.users_table.currentRow()
        if current_row >= 0:
            user_item = self.users_table.item(current_row, 0)
            user_data = user_item.data(Qt.UserRole)
            
            if user_data:
                self.load_user_to_form(user_data)
    
    def load_user_to_form(self, user_data):
        """تحميل بيانات المستخدم في النموذج"""
        self.current_user_id = user_data['id']
        self.edit_mode = True
        
        self.username_edit.setText(user_data['username'])
        self.email_edit.setText(user_data['email'])
        self.full_name_edit.setText(user_data['full_name'])
        self.password_edit.clear()  # لا نعرض كلمة المرور
        
        # تعيين الدور
        role_index = self.role_combo.findText(user_data['role'])
        if role_index >= 0:
            self.role_combo.setCurrentIndex(role_index)
        
        # تعيين الحالة
        status_index = self.status_combo.findText(user_data['status'])
        if status_index >= 0:
            self.status_combo.setCurrentIndex(status_index)
    
    def clear_form(self):
        """مسح النموذج"""
        self.current_user_id = None
        self.edit_mode = False
        
        self.username_edit.clear()
        self.email_edit.clear()
        self.full_name_edit.clear()
        self.password_edit.clear()
        self.role_combo.setCurrentIndex(0)
        self.status_combo.setCurrentIndex(0)
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        self.clear_form()
    
    def edit_user(self):
        """تعديل المستخدم المحدد"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم للتعديل")
            return
        
        # البيانات محملة بالفعل من on_user_selected
    
    def save_user(self):
        """حفظ بيانات المستخدم"""
        try:
            # التحقق من البيانات
            username = self.username_edit.text().strip()
            email = self.email_edit.text().strip()
            full_name = self.full_name_edit.text().strip()
            password = self.password_edit.text().strip()
            
            if not username or not email or not full_name:
                QMessageBox.warning(self, "تحذير", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            if not self.edit_mode and not password:
                QMessageBox.warning(self, "تحذير", "يرجى إدخال كلمة المرور للمستخدم الجديد")
                return
            
            user_data = {
                'username': username,
                'email': email,
                'full_name': full_name,
                'role': self.role_combo.currentText(),
                'status': self.status_combo.currentText()
            }
            
            if password:  # إذا تم إدخال كلمة مرور جديدة
                user_data['password'] = password
            
            if self.data_manager:
                if self.edit_mode:
                    # تحديث المستخدم
                    result = self.data_manager.update_user(self.current_user_id, user_data)
                    if result > 0:
                        QMessageBox.information(self, "نجح", "تم تحديث المستخدم بنجاح")
                    else:
                        QMessageBox.warning(self, "تحذير", "لم يتم تحديث أي بيانات")
                else:
                    # إضافة مستخدم جديد
                    user_id = self.data_manager.create_user(user_data)
                    if user_id:
                        QMessageBox.information(self, "نجح", f"تم إضافة المستخدم بنجاح\nالمعرف: {user_id}")
                    else:
                        QMessageBox.critical(self, "خطأ", "فشل في إضافة المستخدم")
            else:
                QMessageBox.information(self, "محاكاة", "تم حفظ المستخدم (محاكاة)")
            
            # تحديث القائمة ومسح النموذج
            self.load_users()
            self.clear_form()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المستخدم:\n{str(e)}")
    
    def delete_user(self):
        """حذف المستخدم المحدد"""
        current_row = self.users_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مستخدم للحذف")
            return
        
        user_item = self.users_table.item(current_row, 0)
        user_data = user_item.data(Qt.UserRole)
        
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المستخدم:\n{user_data['full_name']} ({user_data['username']})?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                if self.data_manager:
                    result = self.data_manager.delete_user(user_data['id'])
                    if result > 0:
                        QMessageBox.information(self, "نجح", "تم حذف المستخدم بنجاح")
                        self.load_users()
                        self.clear_form()
                    else:
                        QMessageBox.warning(self, "تحذير", "لم يتم حذف أي بيانات")
                else:
                    QMessageBox.information(self, "محاكاة", "تم حذف المستخدم (محاكاة)")
                    self.load_users()
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المستخدم:\n{str(e)}")

class CustomersManagementWidget(QWidget):
    """ودجت إدارة العملاء"""

    def __init__(self, data_manager=None, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_ui()
        self.load_customers()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QHBoxLayout()

        # قائمة العملاء
        left_panel = QWidget()
        left_layout = QVBoxLayout()

        # شريط أدوات العملاء
        toolbar_layout = QHBoxLayout()

        add_customer_btn = QPushButton("➕ إضافة عميل")
        add_customer_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_customer_btn.clicked.connect(self.add_customer)

        edit_customer_btn = QPushButton("✏️ تعديل")
        edit_customer_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        edit_customer_btn.clicked.connect(self.edit_customer)

        delete_customer_btn = QPushButton("🗑️ حذف")
        delete_customer_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        delete_customer_btn.clicked.connect(self.delete_customer)

        toolbar_layout.addWidget(add_customer_btn)
        toolbar_layout.addWidget(edit_customer_btn)
        toolbar_layout.addWidget(delete_customer_btn)
        toolbar_layout.addStretch()

        left_layout.addLayout(toolbar_layout)

        # جدول العملاء
        self.customers_table = QTableWidget()
        self.customers_table.setColumnCount(6)
        self.customers_table.setHorizontalHeaderLabels([
            "المعرف", "كود العميل", "اسم العميل", "الهاتف", "البريد الإلكتروني", "العنوان"
        ])

        header = self.customers_table.horizontalHeader()
        header.setStretchLastSection(True)

        self.customers_table.setAlternatingRowColors(True)
        self.customers_table.setSelectionBehavior(QTableWidget.SelectRows)

        left_layout.addWidget(self.customers_table)
        left_panel.setLayout(left_layout)

        # نموذج العميل
        right_panel = QGroupBox("تفاصيل العميل")
        right_layout = QFormLayout()

        self.customer_code_edit = QLineEdit()
        self.customer_name_edit = QLineEdit()
        self.phone_edit = QLineEdit()
        self.email_edit = QLineEdit()
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)

        right_layout.addRow("كود العميل:", self.customer_code_edit)
        right_layout.addRow("اسم العميل:", self.customer_name_edit)
        right_layout.addRow("الهاتف:", self.phone_edit)
        right_layout.addRow("البريد الإلكتروني:", self.email_edit)
        right_layout.addRow("العنوان:", self.address_edit)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        save_btn.clicked.connect(self.save_customer)

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_btn.clicked.connect(self.clear_form)

        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addStretch()

        right_layout.addRow(buttons_layout)
        right_panel.setLayout(right_layout)

        # تخطيط رئيسي
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([600, 400])

        layout.addWidget(splitter)
        self.setLayout(layout)

        # ربط الأحداث
        self.customers_table.itemSelectionChanged.connect(self.on_customer_selected)

        # متغيرات التحكم
        self.current_customer_id = None
        self.edit_mode = False

    def load_customers(self):
        """تحميل قائمة العملاء"""
        try:
            if self.data_manager:
                customers = self.data_manager.get_customers()
            else:
                # بيانات وهمية
                customers = [
                    {'id': 1, 'customer_code': 'CUST001', 'customer_name': 'أحمد محمد', 'phone': '777123456', 'email': '<EMAIL>', 'address': 'صنعاء'},
                    {'id': 2, 'customer_code': 'CUST002', 'customer_name': 'فاطمة علي', 'phone': '777654321', 'email': '<EMAIL>', 'address': 'عدن'},
                ]

            self.populate_customers_table(customers)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل العملاء:\n{str(e)}")

    def populate_customers_table(self, customers):
        """ملء جدول العملاء"""
        self.customers_table.setRowCount(len(customers))

        for row, customer in enumerate(customers):
            self.customers_table.setItem(row, 0, QTableWidgetItem(str(customer['id'])))
            self.customers_table.setItem(row, 1, QTableWidgetItem(customer['customer_code']))
            self.customers_table.setItem(row, 2, QTableWidgetItem(customer['customer_name']))
            self.customers_table.setItem(row, 3, QTableWidgetItem(customer.get('phone', '')))
            self.customers_table.setItem(row, 4, QTableWidgetItem(customer.get('email', '')))
            self.customers_table.setItem(row, 5, QTableWidgetItem(customer.get('address', '')))

            # حفظ بيانات العميل في العنصر
            self.customers_table.item(row, 0).setData(Qt.UserRole, customer)

    def on_customer_selected(self):
        """عند اختيار عميل"""
        current_row = self.customers_table.currentRow()
        if current_row >= 0:
            customer_item = self.customers_table.item(current_row, 0)
            customer_data = customer_item.data(Qt.UserRole)

            if customer_data:
                self.load_customer_to_form(customer_data)

    def load_customer_to_form(self, customer_data):
        """تحميل بيانات العميل في النموذج"""
        self.current_customer_id = customer_data['id']
        self.edit_mode = True

        self.customer_code_edit.setText(customer_data['customer_code'])
        self.customer_name_edit.setText(customer_data['customer_name'])
        self.phone_edit.setText(customer_data.get('phone', ''))
        self.email_edit.setText(customer_data.get('email', ''))
        self.address_edit.setPlainText(customer_data.get('address', ''))

    def clear_form(self):
        """مسح النموذج"""
        self.current_customer_id = None
        self.edit_mode = False

        self.customer_code_edit.clear()
        self.customer_name_edit.clear()
        self.phone_edit.clear()
        self.email_edit.clear()
        self.address_edit.clear()

    def add_customer(self):
        """إضافة عميل جديد"""
        self.clear_form()
        # توليد كود عميل تلقائي
        next_code = f"CUST{len(self.customers_table.rowCount()) + 1:03d}"
        self.customer_code_edit.setText(next_code)

    def edit_customer(self):
        """تعديل العميل المحدد"""
        current_row = self.customers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للتعديل")
            return

    def save_customer(self):
        """حفظ بيانات العميل"""
        try:
            # التحقق من البيانات
            customer_code = self.customer_code_edit.text().strip()
            customer_name = self.customer_name_edit.text().strip()

            if not customer_code or not customer_name:
                QMessageBox.warning(self, "تحذير", "يرجى ملء الحقول المطلوبة (كود العميل واسم العميل)")
                return

            customer_data = {
                'customer_code': customer_code,
                'customer_name': customer_name,
                'phone': self.phone_edit.text().strip(),
                'email': self.email_edit.text().strip(),
                'address': self.address_edit.toPlainText().strip()
            }

            if self.data_manager:
                if self.edit_mode:
                    # تحديث العميل
                    result = self.data_manager.update_customer(self.current_customer_id, customer_data)
                    if result > 0:
                        QMessageBox.information(self, "نجح", "تم تحديث العميل بنجاح")
                    else:
                        QMessageBox.warning(self, "تحذير", "لم يتم تحديث أي بيانات")
                else:
                    # إضافة عميل جديد
                    customer_id = self.data_manager.create_customer(customer_data)
                    if customer_id:
                        QMessageBox.information(self, "نجح", f"تم إضافة العميل بنجاح\nالمعرف: {customer_id}")
                    else:
                        QMessageBox.critical(self, "خطأ", "فشل في إضافة العميل")
            else:
                QMessageBox.information(self, "محاكاة", "تم حفظ العميل (محاكاة)")

            # تحديث القائمة ومسح النموذج
            self.load_customers()
            self.clear_form()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ العميل:\n{str(e)}")

    def delete_customer(self):
        """حذف العميل المحدد"""
        current_row = self.customers_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للحذف")
            return

        customer_item = self.customers_table.item(current_row, 0)
        customer_data = customer_item.data(Qt.UserRole)

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف العميل:\n{customer_data['customer_name']} ({customer_data['customer_code']})?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if self.data_manager:
                    result = self.data_manager.delete_customer(customer_data['id'])
                    if result > 0:
                        QMessageBox.information(self, "نجح", "تم حذف العميل بنجاح")
                        self.load_customers()
                        self.clear_form()
                    else:
                        QMessageBox.warning(self, "تحذير", "لم يتم حذف أي بيانات")
                else:
                    QMessageBox.information(self, "محاكاة", "تم حذف العميل (محاكاة)")
                    self.load_customers()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف العميل:\n{str(e)}")

class ProductsManagementWidget(QWidget):
    """ودجت إدارة المنتجات"""

    def __init__(self, data_manager=None, parent=None):
        super().__init__(parent)
        self.data_manager = data_manager
        self.setup_ui()
        self.load_products()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout()

        # شريط البحث
        search_layout = QHBoxLayout()

        search_label = QLabel("🔍 البحث:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث في المنتجات...")
        self.search_edit.textChanged.connect(self.search_products)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_edit)
        search_layout.addStretch()

        layout.addLayout(search_layout)

        # المحتوى الرئيسي
        main_layout = QHBoxLayout()

        # قائمة المنتجات
        left_panel = QWidget()
        left_layout = QVBoxLayout()

        # شريط أدوات المنتجات
        toolbar_layout = QHBoxLayout()

        add_product_btn = QPushButton("➕ إضافة منتج")
        add_product_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_product_btn.clicked.connect(self.add_product)

        edit_product_btn = QPushButton("✏️ تعديل")
        edit_product_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        edit_product_btn.clicked.connect(self.edit_product)

        delete_product_btn = QPushButton("🗑️ حذف")
        delete_product_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        delete_product_btn.clicked.connect(self.delete_product)

        toolbar_layout.addWidget(add_product_btn)
        toolbar_layout.addWidget(edit_product_btn)
        toolbar_layout.addWidget(delete_product_btn)
        toolbar_layout.addStretch()

        left_layout.addLayout(toolbar_layout)

        # جدول المنتجات
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(5)
        self.products_table.setHorizontalHeaderLabels([
            "المعرف", "كود المنتج", "اسم المنتج", "السعر", "المخزون"
        ])

        header = self.products_table.horizontalHeader()
        header.setStretchLastSection(True)

        self.products_table.setAlternatingRowColors(True)
        self.products_table.setSelectionBehavior(QTableWidget.SelectRows)

        left_layout.addWidget(self.products_table)
        left_panel.setLayout(left_layout)

        # نموذج المنتج
        right_panel = QGroupBox("تفاصيل المنتج")
        right_layout = QFormLayout()

        self.product_code_edit = QLineEdit()
        self.product_name_edit = QLineEdit()
        self.selling_price_edit = QDoubleSpinBox()
        self.selling_price_edit.setMaximum(999999999.99)
        self.selling_price_edit.setSuffix(" ر.ي")

        self.current_stock_edit = QSpinBox()
        self.current_stock_edit.setMaximum(999999)

        right_layout.addRow("كود المنتج:", self.product_code_edit)
        right_layout.addRow("اسم المنتج:", self.product_name_edit)
        right_layout.addRow("سعر البيع:", self.selling_price_edit)
        right_layout.addRow("المخزون الحالي:", self.current_stock_edit)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        save_btn.clicked.connect(self.save_product)

        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        cancel_btn.clicked.connect(self.clear_form)

        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addStretch()

        right_layout.addRow(buttons_layout)
        right_panel.setLayout(right_layout)

        # تخطيط رئيسي
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([600, 400])

        main_layout.addWidget(splitter)
        layout.addLayout(main_layout)

        self.setLayout(layout)

        # ربط الأحداث
        self.products_table.itemSelectionChanged.connect(self.on_product_selected)

        # متغيرات التحكم
        self.current_product_id = None
        self.edit_mode = False
        self.all_products = []  # لحفظ جميع المنتجات للبحث

    def load_products(self):
        """تحميل قائمة المنتجات"""
        try:
            if self.data_manager:
                products = self.data_manager.get_products()
            else:
                # بيانات وهمية
                products = [
                    {'id': 1, 'product_code': 'PROD001', 'product_name': 'لابتوب ديل', 'selling_price': 1000000, 'current_stock': 25},
                    {'id': 2, 'product_code': 'PROD002', 'product_name': 'ماوس لاسلكي', 'selling_price': 25000, 'current_stock': 45},
                    {'id': 3, 'product_code': 'PROD003', 'product_name': 'كيبورد ميكانيكي', 'selling_price': 75000, 'current_stock': 12},
                ]

            self.all_products = products
            self.populate_products_table(products)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المنتجات:\n{str(e)}")

    def populate_products_table(self, products):
        """ملء جدول المنتجات"""
        self.products_table.setRowCount(len(products))

        for row, product in enumerate(products):
            self.products_table.setItem(row, 0, QTableWidgetItem(str(product['id'])))
            self.products_table.setItem(row, 1, QTableWidgetItem(product['product_code']))
            self.products_table.setItem(row, 2, QTableWidgetItem(product['product_name']))
            self.products_table.setItem(row, 3, QTableWidgetItem(f"{product.get('selling_price', 0):,.0f} ر.ي"))
            self.products_table.setItem(row, 4, QTableWidgetItem(str(product.get('current_stock', 0))))

            # حفظ بيانات المنتج في العنصر
            self.products_table.item(row, 0).setData(Qt.UserRole, product)

    def search_products(self):
        """البحث في المنتجات"""
        search_term = self.search_edit.text().strip()

        if not search_term:
            # عرض جميع المنتجات
            self.populate_products_table(self.all_products)
        else:
            # تصفية المنتجات
            filtered_products = []
            for product in self.all_products:
                if (search_term.lower() in product['product_name'].lower() or
                    search_term.lower() in product['product_code'].lower()):
                    filtered_products.append(product)

            self.populate_products_table(filtered_products)

    def on_product_selected(self):
        """عند اختيار منتج"""
        current_row = self.products_table.currentRow()
        if current_row >= 0:
            product_item = self.products_table.item(current_row, 0)
            product_data = product_item.data(Qt.UserRole)

            if product_data:
                self.load_product_to_form(product_data)

    def load_product_to_form(self, product_data):
        """تحميل بيانات المنتج في النموذج"""
        self.current_product_id = product_data['id']
        self.edit_mode = True

        self.product_code_edit.setText(product_data['product_code'])
        self.product_name_edit.setText(product_data['product_name'])
        self.selling_price_edit.setValue(product_data.get('selling_price', 0))
        self.current_stock_edit.setValue(product_data.get('current_stock', 0))

    def clear_form(self):
        """مسح النموذج"""
        self.current_product_id = None
        self.edit_mode = False

        self.product_code_edit.clear()
        self.product_name_edit.clear()
        self.selling_price_edit.setValue(0)
        self.current_stock_edit.setValue(0)

    def add_product(self):
        """إضافة منتج جديد"""
        self.clear_form()
        # توليد كود منتج تلقائي
        next_code = f"PROD{len(self.all_products) + 1:03d}"
        self.product_code_edit.setText(next_code)

    def edit_product(self):
        """تعديل المنتج المحدد"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للتعديل")
            return

    def save_product(self):
        """حفظ بيانات المنتج"""
        try:
            # التحقق من البيانات
            product_code = self.product_code_edit.text().strip()
            product_name = self.product_name_edit.text().strip()

            if not product_code or not product_name:
                QMessageBox.warning(self, "تحذير", "يرجى ملء الحقول المطلوبة (كود المنتج واسم المنتج)")
                return

            product_data = {
                'product_code': product_code,
                'product_name': product_name,
                'selling_price': self.selling_price_edit.value(),
                'current_stock': self.current_stock_edit.value()
            }

            if self.data_manager:
                if self.edit_mode:
                    # تحديث المنتج
                    result = self.data_manager.update_product(self.current_product_id, product_data)
                    if result > 0:
                        QMessageBox.information(self, "نجح", "تم تحديث المنتج بنجاح")
                    else:
                        QMessageBox.warning(self, "تحذير", "لم يتم تحديث أي بيانات")
                else:
                    # إضافة منتج جديد
                    product_id = self.data_manager.create_product(product_data)
                    if product_id:
                        QMessageBox.information(self, "نجح", f"تم إضافة المنتج بنجاح\nالمعرف: {product_id}")
                    else:
                        QMessageBox.critical(self, "خطأ", "فشل في إضافة المنتج")
            else:
                QMessageBox.information(self, "محاكاة", "تم حفظ المنتج (محاكاة)")

            # تحديث القائمة ومسح النموذج
            self.load_products()
            self.clear_form()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المنتج:\n{str(e)}")

    def delete_product(self):
        """حذف المنتج المحدد"""
        current_row = self.products_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار منتج للحذف")
            return

        product_item = self.products_table.item(current_row, 0)
        product_data = product_item.data(Qt.UserRole)

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف المنتج:\n{product_data['product_name']} ({product_data['product_code']})?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                if self.data_manager:
                    result = self.data_manager.delete_product(product_data['id'])
                    if result > 0:
                        QMessageBox.information(self, "نجح", "تم حذف المنتج بنجاح")
                        self.load_products()
                        self.clear_form()
                    else:
                        QMessageBox.warning(self, "تحذير", "لم يتم حذف أي بيانات")
                else:
                    QMessageBox.information(self, "محاكاة", "تم حذف المنتج (محاكاة)")
                    self.load_products()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المنتج:\n{str(e)}")

class DataManagementMainWindow(QMainWindow):
    """النافذة الرئيسية لإدارة البيانات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.data_manager = None
        self.setup_ui()
        self.initialize_data_manager()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("📊 إدارة البيانات المتكاملة")
        self.setGeometry(100, 100, 1400, 900)
        self.setLayoutDirection(Qt.RightToLeft)

        # الودجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # عنوان النظام
        title_label = QLabel("📊 إدارة البيانات المتكاملة - نظام شامل لإدارة المستخدمين والعملاء والمنتجات")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # لوحة الإحصائيات
        self.stats_widget = DataStatsWidget(self.data_manager)
        main_layout.addWidget(self.stats_widget)

        # التبويبات الرئيسية
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                padding: 10px 15px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #007bff;
            }
        """)

        # تبويب إدارة المستخدمين
        self.users_widget = UsersManagementWidget(self.data_manager)
        self.tabs.addTab(self.users_widget, "👥 إدارة المستخدمين")

        # تبويب إدارة العملاء
        self.customers_widget = CustomersManagementWidget(self.data_manager)
        self.tabs.addTab(self.customers_widget, "🏢 إدارة العملاء")

        # تبويب إدارة المنتجات
        self.products_widget = ProductsManagementWidget(self.data_manager)
        self.tabs.addTab(self.products_widget, "📦 إدارة المنتجات")

        # تبويب التقارير
        reports_tab = self.create_reports_tab()
        self.tabs.addTab(reports_tab, "📈 التقارير")

        main_layout.addWidget(self.tabs)

        central_widget.setLayout(main_layout)

        # شريط الحالة
        self.create_status_bar()

        # مؤقت تحديث الإحصائيات
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.refresh_stats)
        self.stats_timer.start(30000)  # تحديث كل 30 ثانية

    def create_reports_tab(self):
        """إنشاء تبويب التقارير"""
        tab = QWidget()
        layout = QVBoxLayout()

        # عنوان التقارير
        title_label = QLabel("📈 التقارير والإحصائيات")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # تخطيط التقارير
        reports_layout = QGridLayout()

        # تقرير المنتجات منخفضة المخزون
        low_stock_group = QGroupBox("📉 المنتجات منخفضة المخزون")
        low_stock_layout = QVBoxLayout()

        self.low_stock_table = QTableWidget()
        self.low_stock_table.setColumnCount(3)
        self.low_stock_table.setHorizontalHeaderLabels(["اسم المنتج", "المخزون الحالي", "الحد الأدنى"])
        self.low_stock_table.setMaximumHeight(200)

        refresh_low_stock_btn = QPushButton("🔄 تحديث")
        refresh_low_stock_btn.clicked.connect(self.load_low_stock_report)

        low_stock_layout.addWidget(self.low_stock_table)
        low_stock_layout.addWidget(refresh_low_stock_btn)
        low_stock_group.setLayout(low_stock_layout)

        reports_layout.addWidget(low_stock_group, 0, 0)

        # تقرير إحصائيات عامة
        general_stats_group = QGroupBox("📊 إحصائيات عامة")
        general_stats_layout = QFormLayout()

        self.total_users_label = QLabel("0")
        self.total_customers_label = QLabel("0")
        self.total_products_label = QLabel("0")
        self.total_inventory_value_label = QLabel("0 ر.ي")

        general_stats_layout.addRow("إجمالي المستخدمين:", self.total_users_label)
        general_stats_layout.addRow("إجمالي العملاء:", self.total_customers_label)
        general_stats_layout.addRow("إجمالي المنتجات:", self.total_products_label)
        general_stats_layout.addRow("قيمة المخزون:", self.total_inventory_value_label)

        general_stats_group.setLayout(general_stats_layout)
        reports_layout.addWidget(general_stats_group, 0, 1)

        # معلومات قاعدة البيانات
        db_info_group = QGroupBox("🗄️ معلومات قاعدة البيانات")
        db_info_layout = QFormLayout()

        self.db_size_label = QLabel("غير متاح")
        self.db_tables_label = QLabel("غير متاح")
        self.db_version_label = QLabel("غير متاح")

        db_info_layout.addRow("حجم قاعدة البيانات:", self.db_size_label)
        db_info_layout.addRow("عدد الجداول:", self.db_tables_label)
        db_info_layout.addRow("إصدار SQLite:", self.db_version_label)

        # زر النسخ الاحتياطي
        backup_btn = QPushButton("💾 إنشاء نسخة احتياطية")
        backup_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        backup_btn.clicked.connect(self.create_backup)

        db_info_layout.addRow(backup_btn)
        db_info_group.setLayout(db_info_layout)
        reports_layout.addWidget(db_info_group, 1, 0)

        # أدوات إضافية
        tools_group = QGroupBox("🔧 أدوات إضافية")
        tools_layout = QVBoxLayout()

        export_data_btn = QPushButton("📤 تصدير البيانات")
        export_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        export_data_btn.clicked.connect(self.export_data)

        import_data_btn = QPushButton("📥 استيراد البيانات")
        import_data_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e8650e;
            }
        """)
        import_data_btn.clicked.connect(self.import_data)

        tools_layout.addWidget(export_data_btn)
        tools_layout.addWidget(import_data_btn)
        tools_group.setLayout(tools_layout)
        reports_layout.addWidget(tools_group, 1, 1)

        layout.addLayout(reports_layout)
        layout.addStretch()

        tab.setLayout(layout)
        return tab

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()

        # تسمية الحالة
        self.status_label = QLabel("جاهز")
        status_bar.addWidget(self.status_label)

        # مؤشر الاتصال
        self.connection_indicator = QLabel("🔴 غير متصل")
        status_bar.addPermanentWidget(self.connection_indicator)

        # الوقت الحالي
        self.time_label = QLabel()
        self.update_time()
        status_bar.addPermanentWidget(self.time_label)

        # مؤقت تحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)

    def initialize_data_manager(self):
        """تهيئة مدير البيانات"""
        try:
            if DataManager:
                self.data_manager = get_data_manager()
                if self.data_manager.connect():
                    self.connection_indicator.setText("🟢 متصل")
                    self.status_label.setText("تم الاتصال بقاعدة البيانات")

                    # تحديث مدير البيانات في الودجات
                    self.update_widgets_data_manager()

                    # تحميل البيانات
                    self.refresh_all_data()
                else:
                    self.connection_indicator.setText("🔴 فشل الاتصال")
                    self.status_label.setText("فشل في الاتصال بقاعدة البيانات")
            else:
                self.connection_indicator.setText("🟡 وضع المحاكاة")
                self.status_label.setText("تشغيل في وضع المحاكاة")

        except Exception as e:
            self.connection_indicator.setText("🔴 خطأ")
            self.status_label.setText(f"خطأ: {str(e)}")
            print(f"خطأ في تهيئة مدير البيانات: {e}")

    def update_widgets_data_manager(self):
        """تحديث مدير البيانات في الودجات"""
        self.stats_widget.data_manager = self.data_manager
        self.users_widget.data_manager = self.data_manager
        self.customers_widget.data_manager = self.data_manager
        self.products_widget.data_manager = self.data_manager

    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        try:
            # تحديث الإحصائيات
            self.refresh_stats()

            # تحديث قوائم البيانات
            self.users_widget.load_users()
            self.customers_widget.load_customers()
            self.products_widget.load_products()

            # تحديث التقارير
            self.load_low_stock_report()
            self.load_database_info()

            self.status_label.setText("تم تحديث جميع البيانات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث البيانات:\n{str(e)}")

    def refresh_stats(self):
        """تحديث الإحصائيات"""
        try:
            self.stats_widget.load_stats()

            if self.data_manager:
                stats = self.data_manager.get_dashboard_stats()

                self.total_users_label.setText(str(stats.get('users_count', 0)))
                self.total_customers_label.setText(str(stats.get('customers_count', 0)))
                self.total_products_label.setText(str(stats.get('products_count', 0)))

                inventory_value = stats.get('inventory_value', 0)
                if inventory_value > 1000000:
                    value_text = f"{inventory_value/1000000:.1f}م ر.ي"
                elif inventory_value > 1000:
                    value_text = f"{inventory_value/1000:.1f}ألف ر.ي"
                else:
                    value_text = f"{inventory_value:.0f} ر.ي"

                self.total_inventory_value_label.setText(value_text)

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def load_low_stock_report(self):
        """تحميل تقرير المنتجات منخفضة المخزون"""
        try:
            if self.data_manager:
                low_stock_products = self.data_manager.get_low_stock_products(10)
            else:
                # بيانات وهمية
                low_stock_products = [
                    {'product_name': 'كيبورد ميكانيكي', 'current_stock': 2, 'min_stock_level': 5},
                    {'product_name': 'ماوس لاسلكي', 'current_stock': 3, 'min_stock_level': 10},
                ]

            self.low_stock_table.setRowCount(len(low_stock_products))

            for row, product in enumerate(low_stock_products):
                self.low_stock_table.setItem(row, 0, QTableWidgetItem(product['product_name']))
                self.low_stock_table.setItem(row, 1, QTableWidgetItem(str(product['current_stock'])))
                self.low_stock_table.setItem(row, 2, QTableWidgetItem(str(product.get('min_stock_level', 0))))

        except Exception as e:
            print(f"خطأ في تحميل تقرير المخزون المنخفض: {e}")

    def load_database_info(self):
        """تحميل معلومات قاعدة البيانات"""
        try:
            if self.data_manager:
                db_info = self.data_manager.get_database_info()

                # حجم قاعدة البيانات
                size_bytes = db_info.get('size', 0)
                if size_bytes > 1024*1024:
                    size_text = f"{size_bytes/(1024*1024):.2f} MB"
                elif size_bytes > 1024:
                    size_text = f"{size_bytes/1024:.2f} KB"
                else:
                    size_text = f"{size_bytes} B"

                self.db_size_label.setText(size_text)
                self.db_tables_label.setText(str(db_info.get('tables_count', 0)))
                self.db_version_label.setText(db_info.get('sqlite_version', 'غير معروف'))
            else:
                self.db_size_label.setText("1.5 MB (محاكاة)")
                self.db_tables_label.setText("15 (محاكاة)")
                self.db_version_label.setText("SQLite 3.39.0 (محاكاة)")

        except Exception as e:
            print(f"خطأ في تحميل معلومات قاعدة البيانات: {e}")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            if self.data_manager:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = f"backup_data_{timestamp}.db"

                if self.data_manager.create_backup(backup_path):
                    QMessageBox.information(self, "نجح", f"تم إنشاء النسخة الاحتياطية:\n{backup_path}")
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في إنشاء النسخة الاحتياطية")
            else:
                QMessageBox.information(self, "محاكاة", "تم إنشاء النسخة الاحتياطية (محاكاة)")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}")

    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "قريباً", "ميزة تصدير البيانات ستكون متاحة قريباً")

    def import_data(self):
        """استيراد البيانات"""
        QMessageBox.information(self, "قريباً", "ميزة استيراد البيانات ستكون متاحة قريباً")

    def update_time(self):
        """تحديث الوقت"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        try:
            if self.data_manager:
                self.data_manager.disconnect()
        except:
            pass
        event.accept()


def main():
    """تشغيل إدارة البيانات"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = DataManagementMainWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
