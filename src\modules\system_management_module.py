#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة النظام
System Management Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QDoubleSpinBox, QCheckBox,
                             QDateEdit, QSplitter, QFrame, QDialogButtonBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor
try:
    import bcrypt
except ImportError:
    # إذا لم تكن مكتبة bcrypt متوفرة، استخدم hashlib
    import hashlib
    bcrypt = None

from datetime import datetime

try:
    from ..database.sqlite_manager import SQLiteManager
except ImportError:
    # في حالة التشغيل المباشر
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class UserManagementDialog(QDialog):
    """حوار إضافة/تعديل مستخدم"""
    
    def __init__(self, db_manager: SQLiteManager, user_data=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.user_data = user_data
        self.is_edit_mode = user_data is not None
        
        self.setup_ui()
        self.setup_connections()
        
        if self.is_edit_mode:
            self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل مستخدم" if self.is_edit_mode else "إضافة مستخدم جديد"
        self.setWindowTitle(title)
        self.setFixedSize(500, 600)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # النموذج
        form_layout = QFormLayout()
        
        # اسم المستخدم
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("اسم المستخدم")
        if self.is_edit_mode:
            self.username_edit.setEnabled(False)  # لا يمكن تعديل اسم المستخدم
        form_layout.addRow("اسم المستخدم:", self.username_edit)
        
        # الاسم الكامل
        self.full_name_edit = QLineEdit()
        self.full_name_edit.setPlaceholderText("الاسم الكامل")
        form_layout.addRow("الاسم الكامل:", self.full_name_edit)
        
        # البريد الإلكتروني
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("البريد الإلكتروني")
        form_layout.addRow("البريد الإلكتروني:", self.email_edit)
        
        # كلمة المرور
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("كلمة المرور")
        if self.is_edit_mode:
            self.password_edit.setPlaceholderText("اتركها فارغة للاحتفاظ بكلمة المرور الحالية")
        form_layout.addRow("كلمة المرور:", self.password_edit)
        
        # تأكيد كلمة المرور
        self.confirm_password_edit = QLineEdit()
        self.confirm_password_edit.setEchoMode(QLineEdit.Password)
        self.confirm_password_edit.setPlaceholderText("تأكيد كلمة المرور")
        form_layout.addRow("تأكيد كلمة المرور:", self.confirm_password_edit)
        
        # الدور
        self.role_combo = QComboBox()
        self.role_combo.addItem("مدير النظام", "admin")
        self.role_combo.addItem("محاسب", "accountant")
        self.role_combo.addItem("مستخدم عادي", "user")
        form_layout.addRow("الدور:", self.role_combo)
        
        # الحالة
        self.is_active_check = QCheckBox("نشط")
        self.is_active_check.setChecked(True)
        form_layout.addRow("الحالة:", self.is_active_check)
        
        main_layout.addLayout(form_layout)
        
        # معلومات إضافية
        info_group = QGroupBox("معلومات إضافية")
        info_layout = QFormLayout()
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setPlaceholderText("ملاحظات حول المستخدم")
        info_layout.addRow("ملاحظات:", self.notes_edit)
        
        info_group.setLayout(info_layout)
        main_layout.addWidget(info_group)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        main_layout.addWidget(button_box)
        
        self.setLayout(main_layout)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLineEdit, QTextEdit, QComboBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #2196F3;
            }
            QPushButton {
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-weight: bold;
                color: #333;
            
            
                padding: 5px;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_user)
        self.cancel_btn.clicked.connect(self.reject)
    
    def load_data(self):
        """تحميل البيانات في حالة التعديل"""
        if not self.user_data:
            return
        
        data = self.user_data
        
        self.username_edit.setText(data.get('username', ''))
        self.full_name_edit.setText(data.get('full_name', ''))
        self.email_edit.setText(data.get('email', ''))
        
        # تعيين الدور
        role = data.get('role', 'user')
        for i in range(self.role_combo.count()):
            if self.role_combo.itemData(i) == role:
                self.role_combo.setCurrentIndex(i)
                break
        
        self.is_active_check.setChecked(data.get('is_active', True))
        
        # ملاحظات (إذا كانت موجودة في قاعدة البيانات)
        self.notes_edit.setPlainText(data.get('notes', ''))
    
    def save_user(self):
        """حفظ بيانات المستخدم"""
        # التحقق من صحة البيانات
        if not self.validate_data():
            return
        
        try:
            # جمع البيانات
            data = self.collect_data()
            
            if self.is_edit_mode:
                self.update_user(data)
            else:
                self.add_user(data)
            
            QMessageBox.information(self, "نجح", "تم حفظ بيانات المستخدم بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ البيانات:\n{str(e)}")
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        if not self.username_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المستخدم")
            self.username_edit.setFocus()
            return False
        
        if not self.full_name_edit.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال الاسم الكامل")
            self.full_name_edit.setFocus()
            return False
        
        # التحقق من كلمة المرور في حالة المستخدم الجديد
        if not self.is_edit_mode:
            if not self.password_edit.text():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال كلمة المرور")
                self.password_edit.setFocus()
                return False
            
            if len(self.password_edit.text()) < 6:
                QMessageBox.warning(self, "تحذير", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                self.password_edit.setFocus()
                return False
        
        # التحقق من تطابق كلمات المرور
        if self.password_edit.text() and self.password_edit.text() != self.confirm_password_edit.text():
            QMessageBox.warning(self, "تحذير", "كلمات المرور غير متطابقة")
            self.confirm_password_edit.setFocus()
            return False
        
        # التحقق من عدم تكرار اسم المستخدم
        if not self.is_edit_mode:
            existing_query = "SELECT COUNT(*) as count FROM users WHERE username = ?"
            result = self.db_manager.execute_query(existing_query, (self.username_edit.text().strip(),))
            if result and result[0]['count'] > 0:
                QMessageBox.warning(self, "تحذير", "اسم المستخدم موجود بالفعل")
                self.username_edit.setFocus()
                return False
        
        return True
    
    def collect_data(self):
        """جمع البيانات من النموذج"""
        data = {
            'username': self.username_edit.text().strip(),
            'full_name': self.full_name_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'role': self.role_combo.currentData(),
            'is_active': self.is_active_check.isChecked(),
            'notes': self.notes_edit.toPlainText().strip()
        }
        
        # إضافة كلمة المرور إذا تم إدخالها
        if self.password_edit.text():
            if bcrypt:
                password_hash = bcrypt.hashpw(
                    self.password_edit.text().encode('utf-8'),
                    bcrypt.gensalt()
                ).decode('utf-8')
            else:
                # استخدام hashlib كبديل
                import hashlib
                password_hash = hashlib.sha256(
                    self.password_edit.text().encode('utf-8')
                ).hexdigest()
            data['password_hash'] = password_hash
        
        return data
    
    def add_user(self, data):
        """إضافة مستخدم جديد"""
        query = """
            INSERT INTO users (
                username, password_hash, full_name, email, role, is_active
            ) VALUES (?, ?, ?, ?, ?, ?)
        """
        
        params = (
            data['username'], data['password_hash'], data['full_name'],
            data['email'], data['role'], data['is_active']
        )
        
        self.db_manager.execute_update(query, params)
    
    def update_user(self, data):
        """تحديث بيانات المستخدم"""
        if 'password_hash' in data:
            # تحديث مع كلمة المرور
            query = """
                UPDATE users SET
                    password_hash = ?, full_name = ?, email = ?, role = ?, is_active = ?
                WHERE id = ?
            """
            params = (
                data['password_hash'], data['full_name'], data['email'],
                data['role'], data['is_active'], self.user_data['id']
            )
        else:
            # تحديث بدون كلمة المرور
            query = """
                UPDATE users SET
                    full_name = ?, email = ?, role = ?, is_active = ?
                WHERE id = ?
            """
            params = (
                data['full_name'], data['email'], data['role'],
                data['is_active'], self.user_data['id']
            )
        
        self.db_manager.execute_update(query, params)


class UserManagementWidget(QWidget):
    """وحدة إدارة المستخدمين"""

    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.setup_connections()
        self.load_users()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # العنوان
        title = QLabel("👥 إدارة المستخدمين")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        main_layout.addWidget(title)

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        self.add_btn = QPushButton("➕ إضافة مستخدم")
        self.edit_btn = QPushButton("✏️ تعديل")
        self.delete_btn = QPushButton("🗑️ حذف")
        self.activate_btn = QPushButton("✅ تفعيل")
        self.deactivate_btn = QPushButton("❌ إلغاء تفعيل")
        self.refresh_btn = QPushButton("🔄 تحديث")

        # تنسيق الأزرار
        buttons = [self.add_btn, self.edit_btn, self.delete_btn,
                  self.activate_btn, self.deactivate_btn, self.refresh_btn]

        for btn in buttons:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QPushButton:pressed {
                    background-color: #21618c;
                }
                QPushButton:disabled {
                    background-color: #bdc3c7;
                }
            """)

        # ألوان خاصة لبعض الأزرار
        self.add_btn.setStyleSheet(self.add_btn.styleSheet().replace("#3498db", "#27ae60"))
        self.delete_btn.setStyleSheet(self.delete_btn.styleSheet().replace("#3498db", "#e74c3c"))
        self.deactivate_btn.setStyleSheet(self.deactivate_btn.styleSheet().replace("#3498db", "#f39c12"))

        toolbar_layout.addWidget(self.add_btn)
        toolbar_layout.addWidget(self.edit_btn)
        toolbar_layout.addWidget(self.delete_btn)
        toolbar_layout.addWidget(self.activate_btn)
        toolbar_layout.addWidget(self.deactivate_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.refresh_btn)

        main_layout.addLayout(toolbar_layout)

        # منطقة البحث والفلترة
        search_layout = QHBoxLayout()

        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في المستخدمين...")
        search_layout.addWidget(self.search_edit)

        search_layout.addWidget(QLabel("الدور:"))
        self.role_filter = QComboBox()
        self.role_filter.addItems(["الكل", "مدير النظام", "محاسب", "مستخدم عادي"])
        search_layout.addWidget(self.role_filter)

        search_layout.addWidget(QLabel("الحالة:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "نشط", "غير نشط"])
        search_layout.addWidget(self.status_filter)

        main_layout.addLayout(search_layout)

        # الجدول
        self.setup_table()
        main_layout.addWidget(self.table)

        # شريط الحالة
        status_layout = QHBoxLayout()
        self.status_label = QLabel("جاهز")
        self.user_count_label = QLabel("عدد المستخدمين: 0")

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.user_count_label)

        main_layout.addLayout(status_layout)

        self.setLayout(main_layout)
        self.setStyleSheet(self.get_module_stylesheet())

    def setup_table(self):
        """إعداد الجدول"""
        columns = ["المعرف", "اسم المستخدم", "الاسم الكامل", "البريد الإلكتروني",
                  "الدور", "الحالة", "تاريخ الإنشاء", "آخر تحديث"]
        self.table = QTableWidget()
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)

        # تنسيق الجدول
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setSortingEnabled(True)

        # إخفاء عمود المعرف
        self.table.setColumnHidden(0, True)

        # تعديل عرض الأعمدة
        header = self.table.horizontalHeader()
        header.resizeSection(1, 120)  # اسم المستخدم
        header.resizeSection(2, 200)  # الاسم الكامل
        header.resizeSection(3, 200)  # البريد الإلكتروني
        header.resizeSection(4, 100)  # الدور
        header.resizeSection(5, 80)   # الحالة
        header.resizeSection(6, 150)  # تاريخ الإنشاء
        header.resizeSection(7, 150)  # آخر تحديث
        header.setStretchLastSection(True)

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.add_btn.clicked.connect(self.add_user)
        self.edit_btn.clicked.connect(self.edit_user)
        self.delete_btn.clicked.connect(self.delete_user)
        self.activate_btn.clicked.connect(self.activate_user)
        self.deactivate_btn.clicked.connect(self.deactivate_user)
        self.refresh_btn.clicked.connect(self.load_users)

        self.search_edit.textChanged.connect(self.filter_table)
        self.role_filter.currentTextChanged.connect(self.filter_table)
        self.status_filter.currentTextChanged.connect(self.filter_table)

        self.table.itemSelectionChanged.connect(self.on_selection_changed)
        self.table.itemDoubleClicked.connect(self.edit_user)

    def load_users(self):
        """تحميل المستخدمين"""
        try:
            self.status_label.setText("جاري تحميل البيانات...")

            query = """
                SELECT id, username, full_name, email, role, is_active,
                       created_at, updated_at
                FROM users
                ORDER BY created_at DESC
            """

            data = self.db_manager.execute_query(query)
            self.populate_table(data)
            self.status_label.setText("تم تحميل البيانات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل البيانات:\n{str(e)}")
            self.status_label.setText("خطأ في تحميل البيانات")

    def populate_table(self, data):
        """ملء الجدول بالبيانات"""
        self.table.setRowCount(len(data))

        for row, record in enumerate(data):
            # المعرف (مخفي)
            id_item = QTableWidgetItem(str(record.get('id', '')))
            self.table.setItem(row, 0, id_item)

            # اسم المستخدم
            self.table.setItem(row, 1, QTableWidgetItem(str(record.get('username', ''))))

            # الاسم الكامل
            self.table.setItem(row, 2, QTableWidgetItem(str(record.get('full_name', ''))))

            # البريد الإلكتروني
            self.table.setItem(row, 3, QTableWidgetItem(str(record.get('email', ''))))

            # الدور
            role = record.get('role', '')
            role_text = {'admin': 'مدير النظام', 'accountant': 'محاسب', 'user': 'مستخدم عادي'}.get(role, role)
            self.table.setItem(row, 4, QTableWidgetItem(role_text))

            # الحالة
            status_text = "نشط" if record.get('is_active', True) else "غير نشط"
            status_item = QTableWidgetItem(status_text)
            if record.get('is_active', True):
                status_item.setBackground(QColor("#d4edda"))
            else:
                status_item.setBackground(QColor("#f8d7da"))
            self.table.setItem(row, 5, status_item)

            # تاريخ الإنشاء
            created_at = record.get('created_at', '')
            if created_at:
                try:
                    # تحويل التاريخ إلى تنسيق مقروء
                    from datetime import datetime
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                    self.table.setItem(row, 6, QTableWidgetItem(formatted_date))
                except:
                    self.table.setItem(row, 6, QTableWidgetItem(created_at))
            else:
                self.table.setItem(row, 6, QTableWidgetItem(''))

            # آخر تحديث
            updated_at = record.get('updated_at', '')
            if updated_at:
                try:
                    dt = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                    self.table.setItem(row, 7, QTableWidgetItem(formatted_date))
                except:
                    self.table.setItem(row, 7, QTableWidgetItem(updated_at))
            else:
                self.table.setItem(row, 7, QTableWidgetItem(''))

        self.user_count_label.setText(f"عدد المستخدمين: {len(data)}")

    def filter_table(self):
        """فلترة الجدول"""
        search_text = self.search_edit.text().lower()
        role_filter = self.role_filter.currentText()
        status_filter = self.status_filter.currentText()

        for row in range(self.table.rowCount()):
            show_row = True

            # فلترة النص
            if search_text:
                row_text = ""
                for col in range(1, self.table.columnCount()):  # تجاهل عمود المعرف
                    item = self.table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                if search_text not in row_text:
                    show_row = False

            # فلترة الدور
            if role_filter != "الكل":
                role_item = self.table.item(row, 4)
                if role_item and role_item.text() != role_filter:
                    show_row = False

            # فلترة الحالة
            if status_filter != "الكل":
                status_item = self.table.item(row, 5)
                if status_item and status_item.text() != status_filter:
                    show_row = False

            self.table.setRowHidden(row, not show_row)

    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        selected_items = self.table.selectedItems()
        has_selection = len(selected_items) > 0

        self.edit_btn.setEnabled(has_selection)
        self.delete_btn.setEnabled(has_selection)
        self.activate_btn.setEnabled(has_selection)
        self.deactivate_btn.setEnabled(has_selection)

    def add_user(self):
        """إضافة مستخدم جديد"""
        dialog = UserManagementDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_users()

    def edit_user(self):
        """تعديل المستخدم المحدد"""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        try:
            # الحصول على معرف المستخدم
            user_id = self.table.item(current_row, 0).text()

            # استعلام البيانات الكاملة
            query = "SELECT * FROM users WHERE id = ?"
            result = self.db_manager.execute_query(query, (user_id,))

            if result:
                user_data = result[0]
                dialog = UserManagementDialog(self.db_manager, user_data, self)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_users()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات المستخدم:\n{str(e)}")

    def delete_user(self):
        """حذف المستخدم المحدد"""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        try:
            # الحصول على معرف المستخدم والاسم
            user_id = self.table.item(current_row, 0).text()
            username = self.table.item(current_row, 1).text()

            # منع حذف المستخدم الحالي
            # يمكن إضافة فحص هنا للمستخدم الحالي

            # تأكيد الحذف
            reply = QMessageBox.question(
                self, 'تأكيد الحذف',
                f'هل أنت متأكد من حذف المستخدم "{username}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # حذف من قاعدة البيانات
                delete_query = "DELETE FROM users WHERE id = ?"
                self.db_manager.execute_update(delete_query, (user_id,))

                QMessageBox.information(self, "نجح", "تم حذف المستخدم بنجاح")
                self.load_users()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حذف المستخدم:\n{str(e)}")

    def activate_user(self):
        """تفعيل المستخدم المحدد"""
        self.change_user_status(True)

    def deactivate_user(self):
        """إلغاء تفعيل المستخدم المحدد"""
        self.change_user_status(False)

    def change_user_status(self, is_active):
        """تغيير حالة المستخدم"""
        current_row = self.table.currentRow()
        if current_row < 0:
            return

        try:
            user_id = self.table.item(current_row, 0).text()
            username = self.table.item(current_row, 1).text()

            action = "تفعيل" if is_active else "إلغاء تفعيل"

            reply = QMessageBox.question(
                self, f'تأكيد {action}',
                f'هل أنت متأكد من {action} المستخدم "{username}"؟',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تحديث الحالة في قاعدة البيانات
                update_query = "UPDATE users SET is_active = ? WHERE id = ?"
                self.db_manager.execute_update(update_query, (is_active, user_id))

                QMessageBox.information(self, "نجح", f"تم {action} المستخدم بنجاح")
                self.load_users()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تغيير حالة المستخدم:\n{str(e)}")

    def get_module_stylesheet(self):
        """تنسيق الوحدة"""
        return """
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLineEdit, QComboBox {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #007bff;
                outline: none;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """


class SystemManagementModule(QWidget):
    """وحدة إدارة النظام الرئيسية"""

    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # العنوان الرئيسي
        title = QLabel("⚙️ إدارة النظام")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 28px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 10px;
                margin-bottom: 15px;
            
            }
        """)
        main_layout.addWidget(title)

        # إنشاء التبويبات
        self.tab_widget = QTabWidget()

        # تبويب إدارة المستخدمين
        self.users_widget = UserManagementWidget(self.db_manager)
        self.tab_widget.addTab(self.users_widget, "👥 إدارة المستخدمين")

        # تبويب إعدادات النظام
        from .system_settings_module import SystemSettingsWidget
        self.system_settings_widget = SystemSettingsWidget(self.db_manager)
        self.tab_widget.addTab(self.system_settings_widget, "⚙️ إعدادات النظام")

        # تبويب إعدادات الأمان
        from .security_settings_module import SecuritySettingsWidget
        self.security_settings_widget = SecuritySettingsWidget(self.db_manager)
        self.tab_widget.addTab(self.security_settings_widget, "🔒 إعدادات الأمان")

        # تبويب النسخ الاحتياطي
        from .backup_module import BackupWidget
        self.backup_widget = BackupWidget(self.db_manager)
        self.tab_widget.addTab(self.backup_widget, "💾 النسخ الاحتياطي")

        main_layout.addWidget(self.tab_widget)

        self.setLayout(main_layout)
        self.setStyleSheet(self.get_main_stylesheet())







    def get_main_stylesheet(self):
        """تنسيق الوحدة الرئيسية"""
        return """
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                font-size: 14px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 3px solid #3498db;
                color: #2c3e50;
            }
            QTabBar::tab:hover {
                background-color: #f0f0f0;
                color: #2980b9;
            }
        """
