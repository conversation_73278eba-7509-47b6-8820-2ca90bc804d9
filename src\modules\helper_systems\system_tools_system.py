#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام أدوات النظام
System Tools System
"""

import sys
import os
import psutil
import platform
from datetime import datetime
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QPushButton, QGroupBox, 
                             QTextEdit, QTabWidget, QWidget, QComboBox,
                             QApplication, QMessageBox, QListWidget,
                             QListWidgetItem, QCheckBox, QSpinBox,
                             QProgressBar, QTableWidget, QTableWidgetItem,
                             QHeaderView, QSplitter, QTreeWidget, QTreeWidgetItem,
                             QScrollArea, QFrame)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QIcon

class SystemMonitorThread(QThread):
    """خيط مراقبة النظام"""
    system_info_updated = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.is_running = True
        
    def run(self):
        """تشغيل مراقبة النظام"""
        while self.is_running:
            try:
                system_info = {
                    'cpu_percent': psutil.cpu_percent(interval=1),
                    'memory_percent': psutil.virtual_memory().percent,
                    'disk_percent': psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent,
                    'network_sent': psutil.net_io_counters().bytes_sent,
                    'network_recv': psutil.net_io_counters().bytes_recv,
                    'processes_count': len(psutil.pids()),
                    'boot_time': psutil.boot_time()
                }
                
                self.system_info_updated.emit(system_info)
                self.msleep(2000)  # تحديث كل ثانيتين
                
            except Exception as e:
                print(f"خطأ في مراقبة النظام: {e}")
                self.msleep(5000)
                
    def stop(self):
        """إيقاف المراقبة"""
        self.is_running = False

class SystemToolsDialog(QDialog):
    """حوار أدوات النظام"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.system_monitor = None
        self.setup_ui()
        self.start_system_monitoring()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔧 أدوات النظام")
        self.setGeometry(200, 200, 1000, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("🔧 أدوات النظام - مراقبة وإدارة النظام والأداء")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات النظام
        self.tabs = QTabWidget()
        
        # تبويب مراقبة النظام
        system_monitor_tab = self.create_system_monitor_tab()
        self.tabs.addTab(system_monitor_tab, "📊 مراقبة النظام")
        
        # تبويب معلومات النظام
        system_info_tab = self.create_system_info_tab()
        self.tabs.addTab(system_info_tab, "💻 معلومات النظام")
        
        # تبويب إدارة العمليات
        processes_tab = self.create_processes_tab()
        self.tabs.addTab(processes_tab, "⚙️ إدارة العمليات")
        
        # تبويب أدوات الصيانة
        maintenance_tab = self.create_maintenance_tab()
        self.tabs.addTab(maintenance_tab, "🛠️ أدوات الصيانة")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_system_monitor_tab(self):
        """إنشاء تبويب مراقبة النظام"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة مراقبة الأداء
        performance_group = QGroupBox("📈 مراقبة الأداء في الوقت الفعلي")
        performance_layout = QGridLayout()
        
        # مراقب المعالج
        performance_layout.addWidget(QLabel("🖥️ استخدام المعالج:"), 0, 0)
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #3498db;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        performance_layout.addWidget(self.cpu_progress, 0, 1)
        
        self.cpu_label = QLabel("0%")
        self.cpu_label.setStyleSheet("font-weight: bold; color: #3498db;")
        performance_layout.addWidget(self.cpu_label, 0, 2)
        
        # مراقب الذاكرة
        performance_layout.addWidget(QLabel("💾 استخدام الذاكرة:"), 1, 0)
        self.memory_progress = QProgressBar()
        self.memory_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #e74c3c;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #e74c3c;
                border-radius: 3px;
            }
        """)
        performance_layout.addWidget(self.memory_progress, 1, 1)
        
        self.memory_label = QLabel("0%")
        self.memory_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        performance_layout.addWidget(self.memory_label, 1, 2)
        
        # مراقب القرص الصلب
        performance_layout.addWidget(QLabel("💿 استخدام القرص:"), 2, 0)
        self.disk_progress = QProgressBar()
        self.disk_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #f39c12;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #f39c12;
                border-radius: 3px;
            }
        """)
        performance_layout.addWidget(self.disk_progress, 2, 1)
        
        self.disk_label = QLabel("0%")
        self.disk_label.setStyleSheet("font-weight: bold; color: #f39c12;")
        performance_layout.addWidget(self.disk_label, 2, 2)
        
        performance_group.setLayout(performance_layout)
        
        # مجموعة إحصائيات الشبكة
        network_group = QGroupBox("🌐 إحصائيات الشبكة")
        network_layout = QGridLayout()
        
        network_layout.addWidget(QLabel("📤 البيانات المرسلة:"), 0, 0)
        self.network_sent_label = QLabel("0 MB")
        self.network_sent_label.setStyleSheet("font-weight: bold; color: #27ae60;")
        network_layout.addWidget(self.network_sent_label, 0, 1)
        
        network_layout.addWidget(QLabel("📥 البيانات المستقبلة:"), 0, 2)
        self.network_recv_label = QLabel("0 MB")
        self.network_recv_label.setStyleSheet("font-weight: bold; color: #8e44ad;")
        network_layout.addWidget(self.network_recv_label, 0, 3)
        
        network_group.setLayout(network_layout)
        
        # مجموعة معلومات سريعة
        quick_info_group = QGroupBox("⚡ معلومات سريعة")
        quick_info_layout = QGridLayout()
        
        quick_info_layout.addWidget(QLabel("🔢 عدد العمليات:"), 0, 0)
        self.processes_count_label = QLabel("0")
        self.processes_count_label.setStyleSheet("font-weight: bold; color: #34495e;")
        quick_info_layout.addWidget(self.processes_count_label, 0, 1)
        
        quick_info_layout.addWidget(QLabel("⏰ وقت التشغيل:"), 0, 2)
        self.uptime_label = QLabel("0 ساعة")
        self.uptime_label.setStyleSheet("font-weight: bold; color: #16a085;")
        quick_info_layout.addWidget(self.uptime_label, 0, 3)
        
        quick_info_group.setLayout(quick_info_layout)
        
        layout.addWidget(performance_group)
        layout.addWidget(network_group)
        layout.addWidget(quick_info_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab
        
    def create_system_info_tab(self):
        """إنشاء تبويب معلومات النظام"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة معلومات النظام
        system_info_group = QGroupBox("💻 معلومات النظام")
        system_info_layout = QVBoxLayout()
        
        self.system_info_text = QTextEdit()
        self.system_info_text.setReadOnly(True)
        self.system_info_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Courier New';
                font-size: 12px;
            }
        """)
        
        # تحميل معلومات النظام
        self.load_system_info()
        
        system_info_layout.addWidget(self.system_info_text)
        system_info_group.setLayout(system_info_layout)
        
        # أزرار معلومات النظام
        info_buttons = QHBoxLayout()
        
        refresh_info_btn = QPushButton("🔄 تحديث المعلومات")
        refresh_info_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        refresh_info_btn.clicked.connect(self.load_system_info)
        
        copy_info_btn = QPushButton("📋 نسخ المعلومات")
        copy_info_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        copy_info_btn.clicked.connect(self.copy_system_info)
        
        info_buttons.addWidget(refresh_info_btn)
        info_buttons.addWidget(copy_info_btn)
        info_buttons.addStretch()
        
        layout.addWidget(system_info_group)
        layout.addLayout(info_buttons)
        
        tab.setLayout(layout)
        return tab

    def create_maintenance_tab(self):
        """إنشاء تبويب أدوات الصيانة"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة أدوات التنظيف
        cleanup_group = QGroupBox("🧹 أدوات التنظيف")
        cleanup_layout = QGridLayout()

        # تنظيف الملفات المؤقتة
        cleanup_temp_btn = QPushButton("🗑️ تنظيف الملفات المؤقتة")
        cleanup_temp_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        cleanup_temp_btn.clicked.connect(self.cleanup_temp_files)
        cleanup_layout.addWidget(cleanup_temp_btn, 0, 0)

        # تنظيف سلة المحذوفات
        cleanup_recycle_btn = QPushButton("🗂️ تنظيف سلة المحذوفات")
        cleanup_recycle_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        cleanup_recycle_btn.clicked.connect(self.cleanup_recycle_bin)
        cleanup_layout.addWidget(cleanup_recycle_btn, 0, 1)

        # تنظيف ذاكرة التخزين المؤقت
        cleanup_cache_btn = QPushButton("💾 تنظيف ذاكرة التخزين المؤقت")
        cleanup_cache_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        cleanup_cache_btn.clicked.connect(self.cleanup_cache)
        cleanup_layout.addWidget(cleanup_cache_btn, 1, 0)

        # تحسين الذاكرة
        optimize_memory_btn = QPushButton("⚡ تحسين الذاكرة")
        optimize_memory_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        optimize_memory_btn.clicked.connect(self.optimize_memory)
        cleanup_layout.addWidget(optimize_memory_btn, 1, 1)

        cleanup_group.setLayout(cleanup_layout)

        # مجموعة أدوات النظام
        system_tools_group = QGroupBox("🛠️ أدوات النظام")
        system_tools_layout = QGridLayout()

        # فحص القرص الصلب
        disk_check_btn = QPushButton("🔍 فحص القرص الصلب")
        disk_check_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        disk_check_btn.clicked.connect(self.check_disk)
        system_tools_layout.addWidget(disk_check_btn, 0, 0)

        # إلغاء تجزئة القرص
        defrag_btn = QPushButton("🔧 إلغاء تجزئة القرص")
        defrag_btn.setStyleSheet("""
            QPushButton {
                background-color: #fd7e14;
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #e8650e;
            }
        """)
        defrag_btn.clicked.connect(self.defragment_disk)
        system_tools_layout.addWidget(defrag_btn, 0, 1)

        # تحديث النظام
        system_update_btn = QPushButton("🔄 تحديث النظام")
        system_update_btn.setStyleSheet("""
            QPushButton {
                background-color: #20c997;
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #1ba085;
            }
        """)
        system_update_btn.clicked.connect(self.check_system_updates)
        system_tools_layout.addWidget(system_update_btn, 1, 0)

        # إعادة تشغيل النظام
        restart_btn = QPushButton("🔄 إعادة تشغيل النظام")
        restart_btn.setStyleSheet("""
            QPushButton {
                background-color: #e83e8c;
                color: white;
                padding: 10px 15px;
                border-radius: 5px;
                font-weight: bold;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #d91a72;
            }
        """)
        restart_btn.clicked.connect(self.restart_system)
        system_tools_layout.addWidget(restart_btn, 1, 1)

        system_tools_group.setLayout(system_tools_layout)

        # مجموعة معلومات الصيانة
        maintenance_info_group = QGroupBox("📊 معلومات الصيانة")
        maintenance_info_layout = QVBoxLayout()

        self.maintenance_log = QTextEdit()
        self.maintenance_log.setReadOnly(True)
        self.maintenance_log.setMaximumHeight(150)
        self.maintenance_log.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-size: 11px;
            }
        """)
        self.maintenance_log.setPlainText("سجل أعمال الصيانة:\n- جاهز لبدء أعمال الصيانة...")

        maintenance_info_layout.addWidget(self.maintenance_log)
        maintenance_info_group.setLayout(maintenance_info_layout)

        layout.addWidget(cleanup_group)
        layout.addWidget(system_tools_group)
        layout.addWidget(maintenance_info_group)

        tab.setLayout(layout)
        return tab

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        # زر المساعدة
        help_btn = QPushButton("❓ المساعدة")
        help_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        help_btn.clicked.connect(self.show_help)

        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        close_btn.clicked.connect(self.close_system_tools)

        layout.addWidget(help_btn)
        layout.addStretch()
        layout.addWidget(close_btn)

        return layout

    # الوظائف الأساسية
    def start_system_monitoring(self):
        """بدء مراقبة النظام"""
        self.system_monitor = SystemMonitorThread()
        self.system_monitor.system_info_updated.connect(self.update_system_info)
        self.system_monitor.start()

    def update_system_info(self, info):
        """تحديث معلومات النظام"""
        # تحديث أشرطة التقدم
        self.cpu_progress.setValue(int(info['cpu_percent']))
        self.cpu_label.setText(f"{info['cpu_percent']:.1f}%")

        self.memory_progress.setValue(int(info['memory_percent']))
        self.memory_label.setText(f"{info['memory_percent']:.1f}%")

        self.disk_progress.setValue(int(info['disk_percent']))
        self.disk_label.setText(f"{info['disk_percent']:.1f}%")

        # تحديث معلومات الشبكة
        sent_mb = info['network_sent'] / (1024 * 1024)
        recv_mb = info['network_recv'] / (1024 * 1024)
        self.network_sent_label.setText(f"{sent_mb:.1f} MB")
        self.network_recv_label.setText(f"{recv_mb:.1f} MB")

        # تحديث المعلومات السريعة
        self.processes_count_label.setText(str(info['processes_count']))

        # حساب وقت التشغيل
        uptime_seconds = datetime.now().timestamp() - info['boot_time']
        uptime_hours = uptime_seconds / 3600
        self.uptime_label.setText(f"{uptime_hours:.1f} ساعة")

    def load_system_info(self):
        """تحميل معلومات النظام"""
        try:
            system_info = f"""
🖥️ معلومات النظام التفصيلية
{'='*50}

💻 نظام التشغيل:
   النظام: {platform.system()}
   الإصدار: {platform.release()}
   المعمارية: {platform.architecture()[0]}
   اسم الجهاز: {platform.node()}

🔧 المعالج:
   المعالج: {platform.processor()}
   عدد النوى: {psutil.cpu_count(logical=False)} نواة فيزيائية
   عدد الخيوط: {psutil.cpu_count(logical=True)} خيط منطقي
   التردد: {psutil.cpu_freq().current:.0f} MHz

💾 الذاكرة:
   إجمالي الذاكرة: {psutil.virtual_memory().total / (1024**3):.2f} GB
   الذاكرة المستخدمة: {psutil.virtual_memory().used / (1024**3):.2f} GB
   الذاكرة المتاحة: {psutil.virtual_memory().available / (1024**3):.2f} GB
   نسبة الاستخدام: {psutil.virtual_memory().percent:.1f}%

💿 التخزين:
   إجمالي المساحة: {psutil.disk_usage('/').total / (1024**3) if os.name != 'nt' else psutil.disk_usage('C:').total / (1024**3):.2f} GB
   المساحة المستخدمة: {psutil.disk_usage('/').used / (1024**3) if os.name != 'nt' else psutil.disk_usage('C:').used / (1024**3):.2f} GB
   المساحة المتاحة: {psutil.disk_usage('/').free / (1024**3) if os.name != 'nt' else psutil.disk_usage('C:').free / (1024**3):.2f} GB

🌐 الشبكة:
   البيانات المرسلة: {psutil.net_io_counters().bytes_sent / (1024**2):.2f} MB
   البيانات المستقبلة: {psutil.net_io_counters().bytes_recv / (1024**2):.2f} MB

⚙️ العمليات:
   عدد العمليات الجارية: {len(psutil.pids())}

⏰ وقت التشغيل:
   تاريخ بدء التشغيل: {datetime.fromtimestamp(psutil.boot_time()).strftime('%Y-%m-%d %H:%M:%S')}
   مدة التشغيل: {(datetime.now().timestamp() - psutil.boot_time()) / 3600:.1f} ساعة

🐍 Python:
   إصدار Python: {platform.python_version()}
   المترجم: {platform.python_implementation()}
            """

            self.system_info_text.setPlainText(system_info.strip())

        except Exception as e:
            self.system_info_text.setPlainText(f"خطأ في تحميل معلومات النظام: {str(e)}")

    def copy_system_info(self):
        """نسخ معلومات النظام"""
        from PyQt5.QtWidgets import QApplication
        clipboard = QApplication.clipboard()
        clipboard.setText(self.system_info_text.toPlainText())
        QMessageBox.information(self, "تم", "تم نسخ معلومات النظام إلى الحافظة")

    def load_processes(self):
        """تحميل قائمة العمليات"""
        try:
            self.processes_table.setRowCount(0)

            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            # ترتيب العمليات حسب استخدام المعالج
            processes.sort(key=lambda x: x['cpu_percent'] or 0, reverse=True)

            # عرض أول 50 عملية
            for i, proc in enumerate(processes[:50]):
                self.processes_table.insertRow(i)

                self.processes_table.setItem(i, 0, QTableWidgetItem(proc['name'] or 'غير معروف'))
                self.processes_table.setItem(i, 1, QTableWidgetItem(str(proc['pid'])))
                self.processes_table.setItem(i, 2, QTableWidgetItem(f"{proc['cpu_percent'] or 0:.1f}%"))
                self.processes_table.setItem(i, 3, QTableWidgetItem(f"{proc['memory_percent'] or 0:.1f}%"))
                self.processes_table.setItem(i, 4, QTableWidgetItem(proc['status'] or 'غير معروف'))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل العمليات: {str(e)}")

    def end_process(self):
        """إنهاء عملية محددة"""
        current_row = self.processes_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عملية أولاً")
            return

        pid_item = self.processes_table.item(current_row, 1)
        if not pid_item:
            return

        try:
            pid = int(pid_item.text())
            process_name = self.processes_table.item(current_row, 0).text()

            reply = QMessageBox.question(
                self, "تأكيد إنهاء العملية",
                f"هل أنت متأكد من إنهاء العملية:\n{process_name} (PID: {pid})?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                proc = psutil.Process(pid)
                proc.terminate()
                QMessageBox.information(self, "تم", f"تم إنهاء العملية {process_name}")
                self.load_processes()  # تحديث القائمة

        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إنهاء العملية: {str(e)}")
        except ValueError:
            QMessageBox.critical(self, "خطأ", "رقم العملية غير صحيح")

    # وظائف الصيانة
    def cleanup_temp_files(self):
        """تنظيف الملفات المؤقتة"""
        self.add_maintenance_log("🗑️ بدء تنظيف الملفات المؤقتة...")
        QMessageBox.information(self, "قيد التطوير", "وظيفة تنظيف الملفات المؤقتة قيد التطوير")
        self.add_maintenance_log("✅ تم تنظيف الملفات المؤقتة")

    def cleanup_recycle_bin(self):
        """تنظيف سلة المحذوفات"""
        self.add_maintenance_log("🗂️ بدء تنظيف سلة المحذوفات...")
        QMessageBox.information(self, "قيد التطوير", "وظيفة تنظيف سلة المحذوفات قيد التطوير")
        self.add_maintenance_log("✅ تم تنظيف سلة المحذوفات")

    def cleanup_cache(self):
        """تنظيف ذاكرة التخزين المؤقت"""
        self.add_maintenance_log("💾 بدء تنظيف ذاكرة التخزين المؤقت...")
        QMessageBox.information(self, "قيد التطوير", "وظيفة تنظيف ذاكرة التخزين المؤقت قيد التطوير")
        self.add_maintenance_log("✅ تم تنظيف ذاكرة التخزين المؤقت")

    def optimize_memory(self):
        """تحسين الذاكرة"""
        self.add_maintenance_log("⚡ بدء تحسين الذاكرة...")
        QMessageBox.information(self, "قيد التطوير", "وظيفة تحسين الذاكرة قيد التطوير")
        self.add_maintenance_log("✅ تم تحسين الذاكرة")

    def check_disk(self):
        """فحص القرص الصلب"""
        self.add_maintenance_log("🔍 بدء فحص القرص الصلب...")
        QMessageBox.information(self, "قيد التطوير", "وظيفة فحص القرص الصلب قيد التطوير")
        self.add_maintenance_log("✅ تم فحص القرص الصلب")

    def defragment_disk(self):
        """إلغاء تجزئة القرص"""
        self.add_maintenance_log("🔧 بدء إلغاء تجزئة القرص...")
        QMessageBox.information(self, "قيد التطوير", "وظيفة إلغاء تجزئة القرص قيد التطوير")
        self.add_maintenance_log("✅ تم إلغاء تجزئة القرص")

    def check_system_updates(self):
        """تحديث النظام"""
        self.add_maintenance_log("🔄 فحص تحديثات النظام...")
        QMessageBox.information(self, "قيد التطوير", "وظيفة تحديث النظام قيد التطوير")
        self.add_maintenance_log("✅ تم فحص التحديثات")

    def restart_system(self):
        """إعادة تشغيل النظام"""
        reply = QMessageBox.question(
            self, "تأكيد إعادة التشغيل",
            "هل أنت متأكد من إعادة تشغيل النظام؟\nسيتم إغلاق جميع البرامج المفتوحة.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.add_maintenance_log("🔄 إعادة تشغيل النظام...")
            QMessageBox.information(self, "قيد التطوير", "وظيفة إعادة تشغيل النظام قيد التطوير")

    def add_maintenance_log(self, message):
        """إضافة رسالة لسجل الصيانة"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.maintenance_log.append(log_message)

    def close_system_tools(self):
        """إغلاق أدوات النظام"""
        if self.system_monitor:
            self.system_monitor.stop()
            self.system_monitor.wait()
        self.reject()

    def show_help(self):
        """عرض المساعدة"""
        help_text = """
🔧 مساعدة أدوات النظام

📋 الميزات المتاحة:

📊 مراقبة النظام:
• مراقبة استخدام المعالج في الوقت الفعلي
• مراقبة استخدام الذاكرة
• مراقبة استخدام القرص الصلب
• إحصائيات الشبكة (البيانات المرسلة والمستقبلة)
• عدد العمليات الجارية
• وقت تشغيل النظام

💻 معلومات النظام:
• معلومات نظام التشغيل والإصدار
• تفاصيل المعالج وعدد النوى
• معلومات الذاكرة والتخزين
• إحصائيات الشبكة التفصيلية
• معلومات Python والبيئة
• نسخ المعلومات للحافظة

⚙️ إدارة العمليات:
• عرض قائمة العمليات الجارية
• ترتيب العمليات حسب استخدام المعالج
• عرض PID واستخدام الذاكرة لكل عملية
• إنهاء العمليات المحددة (بحذر)
• تحديث قائمة العمليات

🛠️ أدوات الصيانة:
• تنظيف الملفات المؤقتة
• تنظيف سلة المحذوفات
• تنظيف ذاكرة التخزين المؤقت
• تحسين الذاكرة
• فحص القرص الصلب
• إلغاء تجزئة القرص
• فحص تحديثات النظام
• إعادة تشغيل النظام

⚠️ تحذيرات مهمة:
• كن حذراً عند إنهاء العمليات
• تأكد من حفظ عملك قبل إعادة التشغيل
• بعض وظائف الصيانة تحتاج صلاحيات إدارية
• راجع سجل الصيانة لمتابعة العمليات

💡 نصائح الاستخدام:
• راقب استخدام الموارد بانتظام
• نظف الملفات المؤقتة دورياً
• تحقق من العمليات المستهلكة للموارد
• احتفظ بنسخة احتياطية قبل الصيانة

🔄 التحديث التلقائي:
• تتحدث معلومات المراقبة كل ثانيتين
• يمكن تحديث المعلومات يدوياً
• العمليات تُرتب حسب استخدام المعالج
• السجل يحفظ جميع عمليات الصيانة
        """

        QMessageBox.information(self, "المساعدة", help_text)


def main():
    """اختبار نظام أدوات النظام"""
    import sys

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    dialog = SystemToolsDialog()
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
        
    def create_processes_tab(self):
        """إنشاء تبويب إدارة العمليات"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة العمليات
        processes_group = QGroupBox("⚙️ العمليات الجارية")
        processes_layout = QVBoxLayout()
        
        # جدول العمليات
        self.processes_table = QTableWidget()
        self.processes_table.setColumnCount(5)
        self.processes_table.setHorizontalHeaderLabels([
            "اسم العملية", "PID", "استخدام المعالج", "استخدام الذاكرة", "الحالة"
        ])
        
        # تنسيق الجدول
        self.processes_table.horizontalHeader().setStretchLastSection(True)
        self.processes_table.setAlternatingRowColors(True)
        self.processes_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.processes_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 11px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)
        
        # تحميل العمليات
        self.load_processes()
        
        # أزرار إدارة العمليات
        processes_buttons = QHBoxLayout()
        
        refresh_processes_btn = QPushButton("🔄 تحديث")
        refresh_processes_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        refresh_processes_btn.clicked.connect(self.load_processes)
        
        end_process_btn = QPushButton("❌ إنهاء العملية")
        end_process_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        end_process_btn.clicked.connect(self.end_process)
        
        processes_buttons.addWidget(refresh_processes_btn)
        processes_buttons.addWidget(end_process_btn)
        processes_buttons.addStretch()
        
        processes_layout.addWidget(self.processes_table)
        processes_layout.addLayout(processes_buttons)
        processes_group.setLayout(processes_layout)
        
        layout.addWidget(processes_group)
        
        tab.setLayout(layout)
        return tab
