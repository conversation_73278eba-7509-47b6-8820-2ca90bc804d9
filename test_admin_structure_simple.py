#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لأنواع الهيكل الإداري
Simple Test for Administrative Structure Types
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_admin_structure():
    """اختبار شاشة أنواع الهيكل الإداري"""
    try:
        print("🚀 تشغيل شاشة أنواع الهيكل الإداري...")
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # محاولة استيراد الشاشة
        try:
            from modules.setup.administrative_structure_types import AdministrativeStructureTypesDialog
            print("✅ تم استيراد الشاشة بنجاح")
        except ImportError as e:
            print(f"❌ خطأ في استيراد الشاشة: {e}")
            return False
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass
        
        try:
            dialog = AdministrativeStructureTypesDialog(MockDBManager())
            print("✅ تم إنشاء شاشة أنواع الهيكل الإداري بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء الشاشة: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        dialog.show()
        print("🏗️ شاشة أنواع الهيكل الإداري جاهزة!")
        
        print("\n📋 الميزات المتاحة:")
        print("   ✓ إدارة أنواع الهياكل الإدارية")
        print("   ✓ 4 تبويبات شاملة للإدارة")
        print("   ✓ 5 أنواع هياكل نموذجية")
        print("   ✓ 8 مناصب ووظائف نموذجية")
        print("   ✓ هيكل تنظيمي مرئي")
        print("   ✓ تقارير شاملة وإحصائيات")
        
        print("\n🎉 الشاشة جاهزة للاستخدام!")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_admin_structure()
