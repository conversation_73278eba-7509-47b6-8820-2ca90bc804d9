#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف إضافية لشاشة مجموعات المحافظات
Additional Functions for Governorate Groups
"""

from PyQt5.QtWidgets import QMessageBox, QTableWidgetItem, QListWidgetItem
from datetime import datetime

def update_target_groups_function(self):
    """تحديث قائمة المجموعات المستهدفة"""
    self.target_group.clear()
    for row in range(self.groups_table.rowCount()):
        group_name = self.groups_table.item(row, 0).text()
        self.target_group.addItem(group_name)

def on_group_selected_function(self, item):
    """معالجة اختيار مجموعة من الجدول"""
    if item.column() == 0:  # النقر على اسم المجموعة
        group_name = item.text()
        self.target_group.setCurrentText(group_name)
        self.update_assignment_lists()

def update_assignment_lists_function(self):
    """تحديث قوائم التعيين"""
    selected_group = self.target_group.currentText()
    
    # تنظيف القوائم
    self.available_governorates.clear()
    self.assigned_governorates.clear()
    
    # تحميل جميع المحافظات
    all_governorates = [
        "محافظة صنعاء", "محافظة عدن", "محافظة تعز", "محافظة الحديدة",
        "محافظة إب", "محافظة ذمار", "محافظة حضرموت", "محافظة المهرة",
        "محافظة شبوة", "محافظة أبين", "محافظة لحج", "محافظة الضالع",
        "محافظة البيضاء", "محافظة مأرب", "محافظة الجوف", "محافظة صعدة",
        "محافظة حجة", "محافظة المحويت", "محافظة ريمة", 
        "محافظة صنعاء (أمانة العاصمة)", "محافظة عمران", "محافظة جزيرة سقطرى"
    ]
    
    # الحصول على المحافظات المعينة للمجموعة المحددة
    assigned = self.group_assignments.get(selected_group, [])
    
    # تقسيم المحافظات
    for gov in all_governorates:
        if gov in assigned:
            item = QListWidgetItem(gov)
            self.assigned_governorates.addItem(item)
        else:
            item = QListWidgetItem(gov)
            self.available_governorates.addItem(item)

def add_group_function(self):
    """إضافة مجموعة جديدة"""
    try:
        if not self.group_name.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم المجموعة")
            return
            
        if not self.group_code.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز المجموعة")
            return
            
        # إضافة المجموعة للجدول
        row = self.groups_table.rowCount()
        self.groups_table.insertRow(row)
        
        self.groups_table.setItem(row, 0, QTableWidgetItem(self.group_name.text()))
        self.groups_table.setItem(row, 1, QTableWidgetItem(self.group_name_en.text()))
        self.groups_table.setItem(row, 2, QTableWidgetItem(self.group_code.text().upper()))
        self.groups_table.setItem(row, 3, QTableWidgetItem(self.group_type.currentText()))
        self.groups_table.setItem(row, 4, QTableWidgetItem("0"))
        self.groups_table.setItem(row, 5, QTableWidgetItem(self.group_color.currentText()))
        
        status = "نشطة"
        if self.group_default.isChecked():
            status += " (افتراضية)"
        elif not self.group_active.isChecked():
            status = "غير نشطة"
        self.groups_table.setItem(row, 6, QTableWidgetItem(status))
        
        # إضافة المجموعة لقاموس التعيينات
        self.group_assignments[self.group_name.text()] = []
        
        # تنظيف النموذج
        self.clear_group_form()
        
        # تحديث قائمة المجموعات
        self.update_target_groups()
        
        QMessageBox.information(self, "نجح", "تم إضافة المجموعة بنجاح")
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إضافة المجموعة:\n{str(e)}")

def edit_group_function(self):
    """تعديل مجموعة محددة"""
    current_row = self.groups_table.currentRow()
    if current_row < 0:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار مجموعة للتعديل")
        return
        
    # ملء النموذج ببيانات المجموعة المحددة
    self.group_name.setText(self.groups_table.item(current_row, 0).text())
    self.group_name_en.setText(self.groups_table.item(current_row, 1).text())
    self.group_code.setText(self.groups_table.item(current_row, 2).text())
    self.group_type.setCurrentText(self.groups_table.item(current_row, 3).text())
    self.group_color.setCurrentText(self.groups_table.item(current_row, 5).text())
    
    status = self.groups_table.item(current_row, 6).text()
    self.group_active.setChecked("نشطة" in status)
    self.group_default.setChecked("افتراضية" in status)

def delete_group_function(self):
    """حذف مجموعة محددة"""
    current_row = self.groups_table.currentRow()
    if current_row < 0:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار مجموعة للحذف")
        return
        
    group_name = self.groups_table.item(current_row, 0).text()
    gov_count = int(self.groups_table.item(current_row, 4).text())
    
    if gov_count > 0:
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   f"المجموعة '{group_name}' تحتوي على {gov_count} محافظة\n"
                                   f"هل تريد حذف المجموعة؟\n"
                                   f"سيتم إلغاء تعيين جميع المحافظات",
                                   QMessageBox.Yes | QMessageBox.No)
    else:
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   f"هل تريد حذف المجموعة '{group_name}'؟",
                                   QMessageBox.Yes | QMessageBox.No)
    
    if reply == QMessageBox.Yes:
        # حذف المجموعة من الجدول
        self.groups_table.removeRow(current_row)
        
        # حذف التعيينات
        if group_name in self.group_assignments:
            del self.group_assignments[group_name]
        
        # تحديث قائمة المجموعات
        self.update_target_groups()
        
        QMessageBox.information(self, "تم", "تم حذف المجموعة بنجاح")

def clear_group_form_function(self):
    """تنظيف نموذج المجموعة"""
    self.group_name.clear()
    self.group_name_en.clear()
    self.group_code.clear()
    self.group_description.clear()
    self.group_active.setChecked(True)
    self.group_default.setChecked(False)

def move_to_group_function(self):
    """نقل محافظة إلى المجموعة"""
    current_item = self.available_governorates.currentItem()
    if not current_item:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار محافظة من القائمة المتاحة")
        return
        
    selected_group = self.target_group.currentText()
    if not selected_group:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار مجموعة مستهدفة")
        return
        
    # نقل المحافظة
    gov_name = current_item.text()
    
    # إضافة للمجموعة
    if selected_group not in self.group_assignments:
        self.group_assignments[selected_group] = []
    self.group_assignments[selected_group].append(gov_name)
    
    # تحديث القوائم
    self.update_assignment_lists()
    
    # تحديث عدد المحافظات في الجدول
    self.update_group_counts()

def remove_from_group_function(self):
    """إزالة محافظة من المجموعة"""
    current_item = self.assigned_governorates.currentItem()
    if not current_item:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار محافظة من قائمة المجموعة")
        return
        
    selected_group = self.target_group.currentText()
    gov_name = current_item.text()
    
    # إزالة من المجموعة
    if selected_group in self.group_assignments:
        if gov_name in self.group_assignments[selected_group]:
            self.group_assignments[selected_group].remove(gov_name)
    
    # تحديث القوائم
    self.update_assignment_lists()
    
    # تحديث عدد المحافظات في الجدول
    self.update_group_counts()

def move_all_to_group_function(self):
    """نقل جميع المحافظات إلى المجموعة"""
    selected_group = self.target_group.currentText()
    if not selected_group:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار مجموعة مستهدفة")
        return
        
    # نقل جميع المحافظات المتاحة
    if selected_group not in self.group_assignments:
        self.group_assignments[selected_group] = []
        
    for i in range(self.available_governorates.count()):
        item = self.available_governorates.item(i)
        gov_name = item.text()
        if gov_name not in self.group_assignments[selected_group]:
            self.group_assignments[selected_group].append(gov_name)
    
    # تحديث القوائم
    self.update_assignment_lists()
    
    # تحديث عدد المحافظات في الجدول
    self.update_group_counts()

def remove_all_from_group_function(self):
    """إزالة جميع المحافظات من المجموعة"""
    selected_group = self.target_group.currentText()
    if not selected_group:
        QMessageBox.warning(self, "تحذير", "يرجى اختيار مجموعة")
        return
        
    reply = QMessageBox.question(self, "تأكيد الإزالة", 
                               f"هل تريد إزالة جميع المحافظات من المجموعة '{selected_group}'؟",
                               QMessageBox.Yes | QMessageBox.No)
    
    if reply == QMessageBox.Yes:
        # إزالة جميع المحافظات
        if selected_group in self.group_assignments:
            self.group_assignments[selected_group] = []
        
        # تحديث القوائم
        self.update_assignment_lists()
        
        # تحديث عدد المحافظات في الجدول
        self.update_group_counts()

def update_group_counts_function(self):
    """تحديث عدد المحافظات في كل مجموعة"""
    for row in range(self.groups_table.rowCount()):
        group_name = self.groups_table.item(row, 0).text()
        count = len(self.group_assignments.get(group_name, []))
        self.groups_table.setItem(row, 4, QTableWidgetItem(str(count)))

def generate_distribution_report_function(self):
    """إنتاج تقرير توزيع المحافظات"""
    try:
        report = f"""
📈 تقرير توزيع المحافظات على المجموعات
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

"""
        
        total_groups = self.groups_table.rowCount()
        total_governorates = 22  # إجمالي المحافظات اليمنية
        assigned_governorates = 0
        
        for row in range(self.groups_table.rowCount()):
            group_name = self.groups_table.item(row, 0).text()
            group_type = self.groups_table.item(row, 3).text()
            count = int(self.groups_table.item(row, 4).text())
            assigned_governorates += count
            
            percentage = (count / total_governorates) * 100 if total_governorates > 0 else 0
            
            report += f"""
🗂️ {group_name} ({group_type})
   📊 عدد المحافظات: {count}
   📈 النسبة المئوية: {percentage:.1f}%
   📋 المحافظات: {', '.join(self.group_assignments.get(group_name, []))}
"""
        
        unassigned = total_governorates - len(set([gov for govs in self.group_assignments.values() for gov in govs]))
        
        report += f"""
{'='*60}
📊 ملخص التوزيع:
   🗂️ إجمالي المجموعات: {total_groups}
   🏛️ إجمالي المحافظات: {total_governorates}
   ✅ المحافظات المعينة: {len(set([gov for govs in self.group_assignments.values() for gov in govs]))}
   ⚠️ المحافظات غير المعينة: {unassigned}
   📈 نسبة التغطية: {((total_governorates - unassigned) / total_governorates * 100):.1f}%
"""
        
        self.reports_display.setPlainText(report)
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إنتاج التقرير:\n{str(e)}")

def generate_statistics_report_function(self):
    """إنتاج تقرير إحصائيات المجموعات"""
    try:
        report = f"""
📊 تقرير إحصائيات المجموعات
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

"""
        
        # إحصائيات حسب النوع
        type_stats = {}
        for row in range(self.groups_table.rowCount()):
            group_type = self.groups_table.item(row, 3).text()
            count = int(self.groups_table.item(row, 4).text())
            
            if group_type not in type_stats:
                type_stats[group_type] = {'groups': 0, 'governorates': 0}
            type_stats[group_type]['groups'] += 1
            type_stats[group_type]['governorates'] += count
        
        report += "📈 إحصائيات حسب نوع التجميع:\n"
        for type_name, stats in type_stats.items():
            report += f"   🏷️ {type_name}: {stats['groups']} مجموعات، {stats['governorates']} محافظة\n"
        
        # أكبر وأصغر المجموعات
        max_count = 0
        min_count = float('inf')
        max_group = ""
        min_group = ""
        
        for row in range(self.groups_table.rowCount()):
            group_name = self.groups_table.item(row, 0).text()
            count = int(self.groups_table.item(row, 4).text())
            
            if count > max_count:
                max_count = count
                max_group = group_name
            if count < min_count and count > 0:
                min_count = count
                min_group = group_name
        
        report += f"""
📊 إحصائيات المجموعات:
   🥇 أكبر مجموعة: {max_group} ({max_count} محافظة)
   🥉 أصغر مجموعة: {min_group} ({min_count} محافظة)
   📊 متوسط المحافظات لكل مجموعة: {sum([int(self.groups_table.item(row, 4).text()) for row in range(self.groups_table.rowCount())]) / self.groups_table.rowCount():.1f}
"""
        
        # المحافظات المتداخلة
        all_assigned = []
        for govs in self.group_assignments.values():
            all_assigned.extend(govs)
        
        duplicates = set([gov for gov in all_assigned if all_assigned.count(gov) > 1])
        
        if duplicates:
            report += f"\n⚠️ محافظات معينة لأكثر من مجموعة:\n"
            for gov in duplicates:
                groups = [group for group, govs in self.group_assignments.items() if gov in govs]
                report += f"   🏛️ {gov}: {', '.join(groups)}\n"
        else:
            report += "\n✅ لا توجد محافظات متداخلة\n"
        
        self.reports_display.setPlainText(report)
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إنتاج التقرير:\n{str(e)}")

def generate_ungrouped_report_function(self):
    """إنتاج تقرير المحافظات غير المجمعة"""
    try:
        all_governorates = [
            "محافظة صنعاء", "محافظة عدن", "محافظة تعز", "محافظة الحديدة",
            "محافظة إب", "محافظة ذمار", "محافظة حضرموت", "محافظة المهرة",
            "محافظة شبوة", "محافظة أبين", "محافظة لحج", "محافظة الضالع",
            "محافظة البيضاء", "محافظة مأرب", "محافظة الجوف", "محافظة صعدة",
            "محافظة حجة", "محافظة المحويت", "محافظة ريمة", 
            "محافظة صنعاء (أمانة العاصمة)", "محافظة عمران", "محافظة جزيرة سقطرى"
        ]
        
        assigned_governorates = set()
        for govs in self.group_assignments.values():
            assigned_governorates.update(govs)
        
        ungrouped = [gov for gov in all_governorates if gov not in assigned_governorates]
        
        report = f"""
⚠️ تقرير المحافظات غير المجمعة
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 إحصائيات:
   🏛️ إجمالي المحافظات: {len(all_governorates)}
   ✅ المحافظات المجمعة: {len(assigned_governorates)}
   ⚠️ المحافظات غير المجمعة: {len(ungrouped)}
   📈 نسبة التغطية: {(len(assigned_governorates) / len(all_governorates) * 100):.1f}%

"""
        
        if ungrouped:
            report += "⚠️ المحافظات غير المجمعة:\n"
            for i, gov in enumerate(ungrouped, 1):
                report += f"   {i}. {gov}\n"
            
            report += "\n💡 توصيات:\n"
            report += "   • يُنصح بتجميع هذه المحافظات في مجموعات مناسبة\n"
            report += "   • يمكن إنشاء مجموعة جديدة أو إضافتها لمجموعة موجودة\n"
            report += "   • تأكد من أن جميع المحافظات مغطاة لضمان اكتمال التقارير\n"
        else:
            report += "✅ جميع المحافظات مجمعة بنجاح!\n"
            report += "🎉 لا توجد محافظات غير مجمعة\n"
        
        self.reports_display.setPlainText(report)
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إنتاج التقرير:\n{str(e)}")

def export_all_reports_function(self):
    """تصدير جميع التقارير"""
    QMessageBox.information(self, "قيد التطوير", 
                           "📤 ميزة تصدير التقارير قيد التطوير\n"
                           "سيتم إضافتها في الإصدارات القادمة\n\n"
                           "الميزات المخططة:\n"
                           "• تصدير بصيغة PDF\n"
                           "• تصدير بصيغة Excel\n"
                           "• تصدير بصيغة Word\n"
                           "• إرسال التقارير بالبريد الإلكتروني")

def show_help_function(self):
    """عرض المساعدة"""
    help_text = """
🗺️ مساعدة مجموعات المحافظات

📋 الوظائف المتاحة:

📋 تبويب إدارة المجموعات:
• إنشاء مجموعات جديدة للمحافظات
• تحديد نوع التجميع (جغرافي، إداري، اقتصادي...)
• تعيين ألوان للمجموعات للتمييز البصري
• تعديل وحذف المجموعات الموجودة

🎯 تبويب تعيين المحافظات:
• نقل المحافظات بين المجموعات
• عرض المحافظات المتاحة والمعينة
• إمكانية النقل الفردي أو الجماعي
• إزالة المحافظات من المجموعات

📊 تبويب التقارير والإحصائيات:
• تقرير توزيع المحافظات على المجموعات
• إحصائيات المجموعات حسب النوع
• تقرير المحافظات غير المجمعة
• تصدير التقارير (قيد التطوير)

💡 نصائح:
• استخدم أنواع التجميع المختلفة حسب الحاجة
• تأكد من تجميع جميع المحافظات
• راجع التقارير بانتظام للتأكد من صحة التوزيع
• يمكن للمحافظة أن تكون في أكثر من مجموعة
"""
    
    QMessageBox.information(self, "المساعدة", help_text)

def save_all_changes_function(self):
    """حفظ جميع التغييرات"""
    try:
        # جمع بيانات المجموعات
        groups_data = []
        for row in range(self.groups_table.rowCount()):
            group = {
                'name': self.groups_table.item(row, 0).text(),
                'name_en': self.groups_table.item(row, 1).text(),
                'code': self.groups_table.item(row, 2).text(),
                'type': self.groups_table.item(row, 3).text(),
                'governorate_count': int(self.groups_table.item(row, 4).text()),
                'color': self.groups_table.item(row, 5).text(),
                'status': self.groups_table.item(row, 6).text()
            }
            groups_data.append(group)
        
        # حفظ في قاعدة البيانات
        # هنا يتم حفظ البيانات في قاعدة البيانات
        
        total_assignments = sum(len(govs) for govs in self.group_assignments.values())
        unique_governorates = len(set([gov for govs in self.group_assignments.values() for gov in govs]))
        
        summary = f"""✅ تم حفظ مجموعات المحافظات بنجاح!

🗂️ المجموعات المحفوظة: {len(groups_data)}
🏛️ إجمالي التعيينات: {total_assignments}
✅ المحافظات المجمعة: {unique_governorates}

📊 ملخص المجموعات:"""
        
        for group in groups_data:
            if group['governorate_count'] > 0:
                summary += f"\n   • {group['name']}: {group['governorate_count']} محافظة"
        
        QMessageBox.information(self, "نجح الحفظ", summary)
        self.accept()
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في حفظ البيانات:\n{str(e)}")
