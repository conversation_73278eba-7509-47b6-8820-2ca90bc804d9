#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوحدة إعدادات الأمان
Comprehensive test for security settings module
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_security_settings_module():
    """اختبار وحدة إعدادات الأمان"""
    print("🧪 اختبار وحدة إعدادات الأمان...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.security_settings_module import SecuritySettingsWidget, PasswordPolicyDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار إنشاء وحدة إعدادات الأمان
        security_widget = SecuritySettingsWidget(db_manager)
        print("✅ تم إنشاء وحدة إعدادات الأمان")
        
        # اختبار تحميل حالة الأمان
        security_widget.load_security_status()
        print("✅ تم تحميل حالة الأمان")
        
        # اختبار حساب نتيجة الأمان
        security_widget.calculate_security_score()
        score = security_widget.security_score_bar.value()
        print(f"📊 نتيجة الأمان: {score}%")
        
        # اختبار بطاقات الحالة
        active_users = security_widget.active_users_card.value_label.text()
        failed_attempts = security_widget.failed_attempts_card.value_label.text()
        print(f"👥 المستخدمين النشطين: {active_users}")
        print(f"⚠️ المحاولات الفاشلة: {failed_attempts}")
        
        # تنظيف
        security_widget.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وحدة إعدادات الأمان: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_password_policy_dialog():
    """اختبار حوار سياسة كلمات المرور"""
    print("\n🧪 اختبار حوار سياسة كلمات المرور...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.security_settings_module import PasswordPolicyDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار حوار سياسة كلمات المرور
        dialog = PasswordPolicyDialog(db_manager)
        print("✅ تم إنشاء حوار سياسة كلمات المرور")
        
        # اختبار تحميل السياسة الحالية
        dialog.load_current_policy()
        print("✅ تم تحميل السياسة الحالية")
        
        # اختبار إعدادات السياسة
        min_length = dialog.min_length_spin.value()
        require_uppercase = dialog.require_uppercase.isChecked()
        require_numbers = dialog.require_numbers.isChecked()
        
        print(f"🔐 إعدادات السياسة:")
        print(f"   الحد الأدنى للطول: {min_length}")
        print(f"   يتطلب أحرف كبيرة: {'نعم' if require_uppercase else 'لا'}")
        print(f"   يتطلب أرقام: {'نعم' if require_numbers else 'لا'}")
        
        # اختبار قوة كلمة المرور
        dialog.test_password_edit.setText("TestPassword123!")
        dialog.test_password_strength()
        strength_score = dialog.strength_bar.value()
        print(f"💪 قوة كلمة المرور التجريبية: {strength_score}%")
        
        # تنظيف
        dialog.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حوار سياسة كلمات المرور: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_security_audit_dialog():
    """اختبار حوار مراجعة الأمان"""
    print("\n🧪 اختبار حوار مراجعة الأمان...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.security_settings_module import SecurityAuditDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار حوار مراجعة الأمان
        dialog = SecurityAuditDialog(db_manager)
        print("✅ تم إنشاء حوار مراجعة الأمان")
        
        # اختبار وجود التبويبات
        tab_count = dialog.tab_widget.count()
        print(f"✅ عدد تبويبات المراجعة: {tab_count}")
        
        # اختبار تحميل بيانات المراجعة
        dialog.load_audit_data()
        print("✅ تم تحميل بيانات المراجعة")
        
        # اختبار عدد المستخدمين في مراجعة المستخدمين
        users_count = dialog.users_table.rowCount()
        print(f"👥 عدد المستخدمين في المراجعة: {users_count}")
        
        # اختبار عدد كلمات المرور في المراجعة
        passwords_count = dialog.passwords_table.rowCount()
        print(f"🔐 عدد كلمات المرور في المراجعة: {passwords_count}")
        
        # اختبار عدد الجلسات في المراجعة
        sessions_count = dialog.sessions_table.rowCount()
        print(f"🔗 عدد الجلسات في المراجعة: {sessions_count}")
        
        # تنظيف
        dialog.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حوار مراجعة الأمان: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_security_database_operations():
    """اختبار عمليات قاعدة البيانات للأمان"""
    print("\n🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار استعلام المستخدمين النشطين
        active_users_query = "SELECT COUNT(*) as count FROM users WHERE is_active = 1"
        active_users_result = db_manager.execute_query(active_users_query)
        active_users_count = active_users_result[0]['count'] if active_users_result else 0
        print(f"✅ المستخدمين النشطين: {active_users_count}")
        
        # اختبار استعلام إعدادات الأمان
        security_settings_query = """
            SELECT setting_key, setting_value FROM system_settings 
            WHERE category = 'security'
            ORDER BY setting_key
        """
        security_settings = db_manager.execute_query(security_settings_query)
        print(f"🔒 إعدادات الأمان: {len(security_settings)}")
        
        for setting in security_settings:
            key = setting['setting_key']
            value = setting['setting_value']
            print(f"   {key}: {value}")
        
        # اختبار استعلام سياسة كلمات المرور
        password_policy_query = """
            SELECT setting_key, setting_value FROM system_settings 
            WHERE setting_key LIKE 'password_%' OR setting_key LIKE 'max_login_%'
        """
        password_policy = db_manager.execute_query(password_policy_query)
        print(f"🔐 إعدادات سياسة كلمات المرور: {len(password_policy)}")
        
        # اختبار استعلام المستخدمين مع تفاصيل الأمان
        users_security_query = """
            SELECT username, full_name, role, is_active, 
                   created_at, updated_at
            FROM users 
            ORDER BY username
        """
        users_security = db_manager.execute_query(users_security_query)
        print(f"👥 تفاصيل أمان المستخدمين: {len(users_security)}")
        
        # إحصائيات الأدوار
        roles_query = """
            SELECT role, COUNT(*) as count FROM users 
            GROUP BY role
            ORDER BY count DESC
        """
        roles_result = db_manager.execute_query(roles_query)
        print(f"📊 توزيع الأدوار:")
        for role_data in roles_result:
            role_name = {'admin': 'مدير النظام', 'accountant': 'محاسب', 'user': 'مستخدم عادي'}.get(
                role_data['role'], role_data['role']
            )
            print(f"   {role_name}: {role_data['count']}")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار شامل لوحدة إعدادات الأمان")
    print("   Comprehensive test for security settings module")
    print("=" * 60)
    
    # اختبار وحدة إعدادات الأمان
    module_test = test_security_settings_module()
    
    # اختبار حوار سياسة كلمات المرور
    password_policy_test = test_password_policy_dialog()
    
    # اختبار حوار مراجعة الأمان
    audit_test = test_security_audit_dialog()
    
    # اختبار عمليات قاعدة البيانات
    database_test = test_security_database_operations()
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار وحدة إعدادات الأمان:")
    print(f"   🏗️ اختبار الوحدة: {'✅ نجح' if module_test else '❌ فشل'}")
    print(f"   🔐 اختبار سياسة كلمات المرور: {'✅ نجح' if password_policy_test else '❌ فشل'}")
    print(f"   🔍 اختبار مراجعة الأمان: {'✅ نجح' if audit_test else '❌ فشل'}")
    print(f"   🗄️ اختبار قاعدة البيانات: {'✅ نجح' if database_test else '❌ فشل'}")
    
    if module_test and password_policy_test and audit_test and database_test:
        print("\n🎉 جميع اختبارات وحدة إعدادات الأمان نجحت!")
        print("✅ وحدة إعدادات الأمان جاهزة للاستخدام")
        
        print("\n📋 الوظائف المتاحة:")
        print("   🔐 إدارة سياسة كلمات المرور المتقدمة")
        print("   🔍 مراجعة أمان شاملة للنظام")
        print("   📊 لوحة حالة الأمان مع المؤشرات")
        print("   👥 مراجعة المستخدمين والصلاحيات")
        print("   🔗 مراقبة الجلسات النشطة")
        print("   💪 اختبار قوة كلمات المرور")
        print("   ⚠️ تتبع المحاولات الفاشلة")
        print("   📈 حساب نتيجة الأمان العامة")
        print("   💾 نسخ احتياطي لإعدادات الأمان")
    else:
        print("\n⚠️ بعض اختبارات وحدة إعدادات الأمان فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)
    
    return 0 if (module_test and password_policy_test and audit_test and database_test) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
