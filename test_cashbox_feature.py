#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة الصندوق/البنك في السندات
Cashbox/Bank Feature Test in Vouchers
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QPushButton, QWidget, QLabel, QMessageBox, QTextEdit
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class CashboxFeatureTestWindow(QMainWindow):
    """نافذة اختبار ميزة الصندوق/البنك"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_mock_db()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("اختبار ميزة الصندوق/البنك في السندات")
        self.setGeometry(200, 200, 1000, 700)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QMainWindow {
                background-color: #fdf2f8;
                font-family: "Tahoma", "Arial", sans-serif;
            }
            QPushButton {
                background-color: #e91e63;
                color: white;
                border: none;
                padding: 15px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                margin: 10px;
                min-width: 300px;
            }
            QPushButton:hover {
                background-color: #c2185b;
            }
            QPushButton:pressed {
                background-color: #ad1457;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 14px;
                color: #2c3e50;
                padding: 10px;
            }
            QTextEdit {
                font-family: "Tahoma", "Arial", sans-serif;
                font-size: 12px;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel("🏦 اختبار ميزة الصندوق/البنك في السندات")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 22px;
                color: #e91e63;
                background-color: white;
                border: 2px solid #e91e63;
                border-radius: 10px;
                padding: 20px;
                margin: 20px;
                text-align: center;
            }
        """)
        layout.addWidget(title_label)
        
        # أزرار الاختبار
        buttons_layout = QVBoxLayout()
        
        # اختبار سند قبض مع الصندوق
        receipt_btn = QPushButton("📄 سند قبض مع الصندوق/البنك")
        receipt_btn.clicked.connect(self.test_receipt_with_cashbox)
        buttons_layout.addWidget(receipt_btn)
        
        # اختبار سند صرف مع الصندوق
        payment_btn = QPushButton("📄 سند صرف مع الصندوق/البنك")
        payment_btn.clicked.connect(self.test_payment_with_cashbox)
        buttons_layout.addWidget(payment_btn)
        
        # اختبار البحث عن الصناديق
        search_btn = QPushButton("🔍 اختبار البحث عن الصناديق")
        search_btn.clicked.connect(self.test_cashbox_search)
        buttons_layout.addWidget(search_btn)
        
        layout.addLayout(buttons_layout)
        
        # معلومات الميزة الجديدة
        info = QTextEdit()
        info.setReadOnly(True)
        info.setMaximumHeight(350)
        info.setHtml("""
        <h3 style="color: #e91e63;">🏦 ميزة الصندوق/البنك الجديدة:</h3>
        
        <h4 style="color: #27ae60;">✅ الحقول المضافة:</h4>
        <ul>
            <li><strong>رقم الصندوق:</strong> حقل لإدخال رقم الصندوق أو البنك</li>
            <li><strong>اسم الصندوق:</strong> حقل للقراءة فقط يعرض اسم الصندوق</li>
            <li><strong>زر البحث:</strong> 🔍 للبحث عن الصناديق والبنوك</li>
        </ul>
        
        <h4 style="color: #3498db;">🔍 ميزات البحث:</h4>
        <ul>
            <li><strong>F9 في حقل رقم الصندوق:</strong> فتح نافذة البحث</li>
            <li><strong>تصفية تلقائية:</strong> عرض الصناديق والبنوك فقط</li>
            <li><strong>قائمة افتراضية:</strong> في حالة عدم وجود قاعدة بيانات</li>
            <li><strong>تعبئة تلقائية:</strong> للرقم والاسم عند الاختيار</li>
        </ul>
        
        <h4 style="color: #9b59b6;">🏦 الصناديق والبنوك المتاحة:</h4>
        <table border="1" style="border-collapse: collapse; width: 100%; font-size: 11px;">
            <tr style="background-color: #34495e; color: white;">
                <th>الرقم</th>
                <th>الاسم</th>
                <th>النوع</th>
            </tr>
            <tr><td>1010001</td><td>صندوق المركز الرئيسي</td><td>صندوق</td></tr>
            <tr><td>1010002</td><td>صندوق الفرع الأول</td><td>صندوق</td></tr>
            <tr><td>1010003</td><td>صندوق الفرع الثاني</td><td>صندوق</td></tr>
            <tr><td>1020001</td><td>البنك الأهلي التجاري</td><td>بنك</td></tr>
            <tr><td>1020002</td><td>مصرف الراجحي</td><td>بنك</td></tr>
            <tr><td>1020003</td><td>البنك السعودي للاستثمار</td><td>بنك</td></tr>
            <tr><td>1020004</td><td>بنك الرياض</td><td>بنك</td></tr>
            <tr><td>1020005</td><td>البنك السعودي الفرنسي</td><td>بنك</td></tr>
        </table>
        
        <h4 style="color: #e74c3c;">📊 التخطيط المحدث:</h4>
        <ul>
            <li><strong>الصف الأول:</strong> رقم السند | التاريخ | المستفيد</li>
            <li><strong>الصف الثاني:</strong> رقم الصندوق | 🔍 | اسم الصندوق</li>
            <li><strong>الصف الثالث:</strong> نوع السند | رقم المستفيد | البيان</li>
            <li><strong>ارتفاع القسم:</strong> 150px (بدلاً من 120px)</li>
        </ul>
        
        <h4 style="color: #f39c12;">🎯 الفوائد:</h4>
        <ul>
            <li>✅ <strong>تتبع دقيق:</strong> للصناديق والبنوك في كل سند</li>
            <li>✅ <strong>تقارير مفصلة:</strong> حسب الصندوق أو البنك</li>
            <li>✅ <strong>رقابة مالية:</strong> أفضل على الحركات</li>
            <li>✅ <strong>سهولة الاستخدام:</strong> مع البحث السريع</li>
            <li>✅ <strong>دقة البيانات:</strong> مع التعبئة التلقائية</li>
        </ul>
        
        <h4 style="color: #17a2b8;">⌨️ اختصارات لوحة المفاتيح:</h4>
        <ul>
            <li><strong>F9 في حقل رقم الصندوق:</strong> البحث عن الصناديق/البنوك</li>
            <li><strong>F9 في جدول التفاصيل:</strong> البحث عن الحسابات</li>
            <li><strong>Enter:</strong> اختيار الصندوق من قائمة البحث</li>
            <li><strong>Esc:</strong> إلغاء البحث</li>
        </ul>
        
        <h4 style="color: #6f42c1;">💾 قاعدة البيانات:</h4>
        <ul>
            <li><strong>حقول جديدة:</strong> cashbox_code, cashbox_name</li>
            <li><strong>تخزين آمن:</strong> للبيانات مع السند</li>
            <li><strong>استعلامات محسنة:</strong> للتقارير والبحث</li>
            <li><strong>تكامل كامل:</strong> مع النظام الحالي</li>
        </ul>
        """)
        layout.addWidget(info)
        
        central_widget.setLayout(layout)
    
    def setup_mock_db(self):
        """إعداد قاعدة بيانات وهمية للاختبار"""
        class MockDBManager:
            def __init__(self):
                self.cursor = type('obj', (object,), {'lastrowid': 1})()
            
            def execute_query(self, query, params=None):
                # محاكاة نتائج الاستعلام
                if "MAX" in query:
                    return [(0,)]
                elif "chart_of_accounts" in query:
                    # إرجاع بيانات الحسابات مع التركيز على الصناديق والبنوك
                    return [
                        ('1010001', 'صندوق المركز الرئيسي', 'أصول متداولة', 1, 3, '101000'),
                        ('1010002', 'صندوق الفرع الأول', 'أصول متداولة', 1, 3, '101000'),
                        ('1010003', 'صندوق الفرع الثاني', 'أصول متداولة', 1, 3, '101000'),
                        ('1020001', 'البنك الأهلي التجاري', 'أصول متداولة', 1, 3, '102000'),
                        ('1020002', 'مصرف الراجحي', 'أصول متداولة', 1, 3, '102000'),
                        ('1020003', 'البنك السعودي للاستثمار', 'أصول متداولة', 1, 3, '102000'),
                        ('1020004', 'بنك الرياض', 'أصول متداولة', 1, 3, '102000'),
                        ('1020005', 'البنك السعودي الفرنسي', 'أصول متداولة', 1, 3, '102000'),
                        ('1030001', 'العملاء المحليين', 'أصول متداولة', 1, 3, '103000'),
                        ('2010001', 'الموردين المحليين', 'خصوم متداولة', 1, 3, '201000'),
                        ('4010001', 'مبيعات محلية', 'إيرادات', 1, 3, '401000'),
                        ('5010001', 'مصروفات عمومية', 'مصروفات', 1, 3, '501000'),
                    ]
                return []
            
            def execute_update(self, query, params=None):
                return True
        
        self.db_manager = MockDBManager()
    
    def test_receipt_with_cashbox(self):
        """اختبار سند القبض مع الصندوق"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            receipt_dialog = VoucherInterface(self.db_manager, "receipt", self)
            
            # تعبئة بيانات تجريبية
            receipt_dialog.beneficiary_edit.setText("شركة التقنية المتقدمة")
            receipt_dialog.beneficiary_id_edit.setText("C004")
            receipt_dialog.cashbox_code_edit.setText("1010001")
            receipt_dialog.cashbox_name_edit.setText("صندوق المركز الرئيسي")
            receipt_dialog.description_edit.setText("سند قبض - تحصيل فاتورة مبيعات")
            
            receipt_dialog.show()
            
            QMessageBox.information(
                self, 
                "سند قبض مع الصندوق",
                "✅ تم فتح سند القبض مع ميزة الصندوق\n\n"
                "🏦 الميزات الجديدة:\n"
                "• حقل رقم الصندوق\n"
                "• حقل اسم الصندوق (للقراءة فقط)\n"
                "• زر البحث 🔍\n"
                "• اختصار F9 للبحث\n\n"
                "🔍 للاختبار:\n"
                "• انقر في حقل رقم الصندوق\n"
                "• اضغط F9 أو انقر زر البحث\n"
                "• اختر صندوق من القائمة\n"
                "• لاحظ التعبئة التلقائية للاسم"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند القبض:\n{str(e)}")
    
    def test_payment_with_cashbox(self):
        """اختبار سند الصرف مع الصندوق"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            payment_dialog = VoucherInterface(self.db_manager, "payment", self)
            
            # تعبئة بيانات تجريبية
            payment_dialog.beneficiary_edit.setText("شركة الخدمات اللوجستية")
            payment_dialog.beneficiary_id_edit.setText("S005")
            payment_dialog.cashbox_code_edit.setText("1020001")
            payment_dialog.cashbox_name_edit.setText("البنك الأهلي التجاري")
            payment_dialog.description_edit.setText("سند صرف - دفع مستحقات مورد")
            
            payment_dialog.show()
            
            QMessageBox.information(
                self, 
                "سند صرف مع الصندوق",
                "✅ تم فتح سند الصرف مع ميزة الصندوق\n\n"
                "🏦 الميزات الجديدة:\n"
                "• حقل رقم الصندوق/البنك\n"
                "• حقل اسم الصندوق/البنك\n"
                "• زر البحث المتخصص\n"
                "• تصفية للصناديق والبنوك فقط\n\n"
                "🔍 للاختبار:\n"
                "• جرب البحث عن بنوك مختلفة\n"
                "• لاحظ التصفية التلقائية\n"
                "• اختبر الاختصارات\n"
                "• تحقق من حفظ البيانات"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح سند الصرف:\n{str(e)}")
    
    def test_cashbox_search(self):
        """اختبار البحث عن الصناديق"""
        try:
            from modules.vouchers.voucher_interface import VoucherInterface
            dialog = VoucherInterface(self.db_manager, "receipt", self)
            
            # فتح البحث مباشرة
            dialog.search_cashbox()
            
            QMessageBox.information(
                self, 
                "اختبار البحث عن الصناديق",
                "🔍 تم فتح نافذة البحث عن الصناديق\n\n"
                "📋 الميزات:\n"
                "• تصفية تلقائية للصناديق والبنوك\n"
                "• بحث بالرقم أو الاسم\n"
                "• قائمة افتراضية شاملة\n"
                "• تعبئة تلقائية عند الاختيار\n\n"
                "💡 نصائح:\n"
                "• اكتب 'صندوق' للبحث عن الصناديق\n"
                "• اكتب 'بنك' للبحث عن البنوك\n"
                "• اكتب الرقم للبحث المباشر\n"
                "• انقر نقراً مزدوجاً للاختيار"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في اختبار البحث:\n{str(e)}")


def main():
    """دالة التشغيل الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تطبيق خط عربي
    font = QFont("Tahoma", 10)
    app.setFont(font)
    
    # إنشاء النافذة الرئيسية
    window = CashboxFeatureTestWindow()
    window.show()
    
    print("🏦 تم تشغيل اختبار ميزة الصندوق/البنك!")
    print("✅ الميزات الجديدة:")
    print("   🏦 حقل رقم الصندوق/البنك")
    print("   📝 حقل اسم الصندوق/البنك")
    print("   🔍 زر البحث المتخصص")
    print("   ⌨️ اختصار F9 للبحث")
    print("   🎯 تصفية تلقائية للصناديق والبنوك")
    print("   💾 حفظ البيانات مع السند")
    print("")
    print("🎯 الفوائد:")
    print("   • تتبع دقيق للحركات المالية")
    print("   • تقارير مفصلة حسب الصندوق")
    print("   • رقابة مالية محسنة")
    print("   • سهولة الاستخدام")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
