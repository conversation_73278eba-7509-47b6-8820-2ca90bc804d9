#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة المؤشرات العامة
General Indicators Setup Screen
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QDateEdit, QFrame,
                             QMessageBox, QTabWidget, QWidget, QTextEdit)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont

class GeneralIndicatorsDialog(QDialog):
    """شاشة المؤشرات العامة"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("المؤشرات العامة - تحديد خصائص النظام")
        self.setGeometry(200, 100, 800, 600)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان الشاشة
        title_label = QLabel("⚙️ المؤشرات العامة")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات المؤشرات
        tabs = QTabWidget()
        
        # تبويب المعلومات الأساسية
        basic_tab = self.create_basic_info_tab()
        tabs.addTab(basic_tab, "📋 المعلومات الأساسية")
        
        # تبويب الإعدادات المالية
        financial_tab = self.create_financial_settings_tab()
        tabs.addTab(financial_tab, "💰 الإعدادات المالية")
        
        # تبويب إعدادات النظام
        system_tab = self.create_system_settings_tab()
        tabs.addTab(system_tab, "🔧 إعدادات النظام")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_basic_info_tab(self):
        """إنشاء تبويب المعلومات الأساسية"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة بيانات الشركة
        company_group = QGroupBox("🏢 بيانات الشركة")
        company_layout = QGridLayout()
        
        # اسم الشركة
        company_layout.addWidget(QLabel("اسم الشركة:"), 0, 0)
        self.company_name = QLineEdit()
        self.company_name.setPlaceholderText("أدخل اسم الشركة")
        company_layout.addWidget(self.company_name, 0, 1)
        
        # نوع الشركة
        company_layout.addWidget(QLabel("نوع الشركة:"), 1, 0)
        self.company_type = QComboBox()
        self.company_type.addItems([
            "شركة تجارية", "شركة صناعية", "شركة خدمية", 
            "مؤسسة فردية", "شركة مساهمة", "شركة محدودة"
        ])
        company_layout.addWidget(self.company_type, 1, 1)
        
        # رقم السجل التجاري
        company_layout.addWidget(QLabel("رقم السجل التجاري:"), 2, 0)
        self.commercial_register = QLineEdit()
        company_layout.addWidget(self.commercial_register, 2, 1)
        
        # الرقم الضريبي
        company_layout.addWidget(QLabel("الرقم الضريبي:"), 3, 0)
        self.tax_number = QLineEdit()
        company_layout.addWidget(self.tax_number, 3, 1)
        
        company_group.setLayout(company_layout)
        
        # مجموعة السنة المالية
        fiscal_group = QGroupBox("📅 السنة المالية")
        fiscal_layout = QGridLayout()
        
        # بداية السنة المالية
        fiscal_layout.addWidget(QLabel("بداية السنة المالية:"), 0, 0)
        self.fiscal_year_start = QDateEdit()
        self.fiscal_year_start.setDate(QDate.currentDate().addDays(-QDate.currentDate().dayOfYear() + 1))
        self.fiscal_year_start.setCalendarPopup(True)
        fiscal_layout.addWidget(self.fiscal_year_start, 0, 1)
        
        # نهاية السنة المالية
        fiscal_layout.addWidget(QLabel("نهاية السنة المالية:"), 1, 0)
        self.fiscal_year_end = QDateEdit()
        self.fiscal_year_end.setDate(QDate.currentDate().addDays(365 - QDate.currentDate().dayOfYear()))
        self.fiscal_year_end.setCalendarPopup(True)
        fiscal_layout.addWidget(self.fiscal_year_end, 1, 1)
        
        fiscal_group.setLayout(fiscal_layout)
        
        layout.addWidget(company_group)
        layout.addWidget(fiscal_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab
        
    def create_financial_settings_tab(self):
        """إنشاء تبويب الإعدادات المالية"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة العملة الأساسية
        currency_group = QGroupBox("💱 العملة الأساسية")
        currency_layout = QGridLayout()
        
        # العملة الأساسية
        currency_layout.addWidget(QLabel("العملة الأساسية:"), 0, 0)
        self.base_currency = QComboBox()
        self.base_currency.addItems(["ريال يمني", "دولار أمريكي", "يورو", "ريال سعودي"])
        currency_layout.addWidget(self.base_currency, 0, 1)
        
        # رمز العملة
        currency_layout.addWidget(QLabel("رمز العملة:"), 1, 0)
        self.currency_symbol = QLineEdit()
        self.currency_symbol.setText("ر.ي")
        currency_layout.addWidget(self.currency_symbol, 1, 1)
        
        currency_group.setLayout(currency_layout)
        
        # مجموعة الكسور العشرية
        decimal_group = QGroupBox("🔢 الكسور العشرية")
        decimal_layout = QGridLayout()
        
        # عدد الكسور العشرية للمبالغ
        decimal_layout.addWidget(QLabel("عدد الكسور العشرية للمبالغ:"), 0, 0)
        self.decimal_places_amount = QSpinBox()
        self.decimal_places_amount.setRange(0, 6)
        self.decimal_places_amount.setValue(2)
        decimal_layout.addWidget(self.decimal_places_amount, 0, 1)
        
        # عدد الكسور العشرية للكميات
        decimal_layout.addWidget(QLabel("عدد الكسور العشرية للكميات:"), 1, 0)
        self.decimal_places_quantity = QSpinBox()
        self.decimal_places_quantity.setRange(0, 6)
        self.decimal_places_quantity.setValue(3)
        decimal_layout.addWidget(self.decimal_places_quantity, 1, 1)
        
        # عدد الكسور العشرية للأسعار
        decimal_layout.addWidget(QLabel("عدد الكسور العشرية للأسعار:"), 2, 0)
        self.decimal_places_price = QSpinBox()
        self.decimal_places_price.setRange(0, 6)
        self.decimal_places_price.setValue(2)
        decimal_layout.addWidget(self.decimal_places_price, 2, 1)
        
        decimal_group.setLayout(decimal_layout)
        
        # مجموعة الضرائب
        tax_group = QGroupBox("📊 إعدادات الضرائب")
        tax_layout = QGridLayout()
        
        # نسبة الضريبة الافتراضية
        tax_layout.addWidget(QLabel("نسبة الضريبة الافتراضية (%):"), 0, 0)
        self.default_tax_rate = QDoubleSpinBox()
        self.default_tax_rate.setRange(0.0, 100.0)
        self.default_tax_rate.setValue(0.0)
        self.default_tax_rate.setSuffix("%")
        tax_layout.addWidget(self.default_tax_rate, 0, 1)
        
        # تطبيق الضريبة تلقائياً
        self.auto_apply_tax = QCheckBox("تطبيق الضريبة تلقائياً على الفواتير")
        tax_layout.addWidget(self.auto_apply_tax, 1, 0, 1, 2)
        
        tax_group.setLayout(tax_layout)
        
        layout.addWidget(currency_group)
        layout.addWidget(decimal_group)
        layout.addWidget(tax_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab
        
    def create_system_settings_tab(self):
        """إنشاء تبويب إعدادات النظام"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إعدادات الترقيم
        numbering_group = QGroupBox("🔢 إعدادات الترقيم")
        numbering_layout = QGridLayout()
        
        # ترقيم الفواتير
        numbering_layout.addWidget(QLabel("نمط ترقيم الفواتير:"), 0, 0)
        self.invoice_numbering = QComboBox()
        self.invoice_numbering.addItems(["تلقائي", "يدوي", "حسب السنة", "حسب الشهر"])
        numbering_layout.addWidget(self.invoice_numbering, 0, 1)
        
        # بادئة رقم الفاتورة
        numbering_layout.addWidget(QLabel("بادئة رقم الفاتورة:"), 1, 0)
        self.invoice_prefix = QLineEdit()
        self.invoice_prefix.setText("INV-")
        numbering_layout.addWidget(self.invoice_prefix, 1, 1)
        
        numbering_group.setLayout(numbering_layout)
        
        # مجموعة إعدادات النسخ الاحتياطي
        backup_group = QGroupBox("💾 النسخ الاحتياطي")
        backup_layout = QGridLayout()
        
        # النسخ الاحتياطي التلقائي
        self.auto_backup = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        backup_layout.addWidget(self.auto_backup, 0, 0, 1, 2)
        
        # فترة النسخ الاحتياطي
        backup_layout.addWidget(QLabel("فترة النسخ الاحتياطي:"), 1, 0)
        self.backup_frequency = QComboBox()
        self.backup_frequency.addItems(["يومي", "أسبوعي", "شهري"])
        backup_layout.addWidget(self.backup_frequency, 1, 1)
        
        backup_group.setLayout(backup_layout)
        
        # مجموعة إعدادات أخرى
        other_group = QGroupBox("🔧 إعدادات أخرى")
        other_layout = QVBoxLayout()
        
        # السماح بالتعديل على الفواتير المحفوظة
        self.allow_edit_saved = QCheckBox("السماح بالتعديل على الفواتير المحفوظة")
        other_layout.addWidget(self.allow_edit_saved)
        
        # السماح بحذف الفواتير
        self.allow_delete_invoices = QCheckBox("السماح بحذف الفواتير")
        other_layout.addWidget(self.allow_delete_invoices)
        
        # طباعة تلقائية بعد الحفظ
        self.auto_print = QCheckBox("طباعة تلقائية بعد حفظ الفاتورة")
        other_layout.addWidget(self.auto_print)
        
        other_group.setLayout(other_layout)
        
        layout.addWidget(numbering_group)
        layout.addWidget(backup_group)
        layout.addWidget(other_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab
        
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر الحفظ
        save_btn = QPushButton("💾 حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_settings)
        
        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        # زر الافتراضي
        default_btn = QPushButton("🔄 القيم الافتراضية")
        default_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        default_btn.clicked.connect(self.load_defaults)
        
        layout.addStretch()
        layout.addWidget(default_btn)
        layout.addWidget(save_btn)
        layout.addWidget(cancel_btn)
        
        return layout
        
    def load_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            # هنا يتم تحميل البيانات من قاعدة البيانات
            # مؤقتاً سنستخدم قيم افتراضية
            pass
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            
    def load_defaults(self):
        """تحميل القيم الافتراضية"""
        # إعادة تعيين جميع الحقول للقيم الافتراضية
        self.company_name.setText("شركة تجارية")
        self.company_type.setCurrentText("شركة تجارية")
        self.commercial_register.setText("")
        self.tax_number.setText("")
        
        # إعادة تعيين التواريخ
        current_date = QDate.currentDate()
        self.fiscal_year_start.setDate(current_date.addDays(-current_date.dayOfYear() + 1))
        self.fiscal_year_end.setDate(current_date.addDays(365 - current_date.dayOfYear()))
        
        # إعادة تعيين الإعدادات المالية
        self.base_currency.setCurrentText("ريال يمني")
        self.currency_symbol.setText("ر.ي")
        self.decimal_places_amount.setValue(2)
        self.decimal_places_quantity.setValue(3)
        self.decimal_places_price.setValue(2)
        self.default_tax_rate.setValue(0.0)
        
        # إعادة تعيين إعدادات النظام
        self.invoice_numbering.setCurrentText("تلقائي")
        self.invoice_prefix.setText("INV-")
        self.backup_frequency.setCurrentText("أسبوعي")
        
        # إعادة تعيين الخيارات
        self.auto_apply_tax.setChecked(False)
        self.auto_backup.setChecked(True)
        self.allow_edit_saved.setChecked(True)
        self.allow_delete_invoices.setChecked(False)
        self.auto_print.setChecked(False)
        
        QMessageBox.information(self, "تم", "تم تحميل القيم الافتراضية بنجاح")
        
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # التحقق من صحة البيانات
            if not self.company_name.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الشركة")
                return
                
            # جمع البيانات
            settings_data = {
                'company_name': self.company_name.text(),
                'company_type': self.company_type.currentText(),
                'commercial_register': self.commercial_register.text(),
                'tax_number': self.tax_number.text(),
                'fiscal_year_start': self.fiscal_year_start.date().toString('yyyy-MM-dd'),
                'fiscal_year_end': self.fiscal_year_end.date().toString('yyyy-MM-dd'),
                'base_currency': self.base_currency.currentText(),
                'currency_symbol': self.currency_symbol.text(),
                'decimal_places_amount': self.decimal_places_amount.value(),
                'decimal_places_quantity': self.decimal_places_quantity.value(),
                'decimal_places_price': self.decimal_places_price.value(),
                'default_tax_rate': self.default_tax_rate.value(),
                'auto_apply_tax': self.auto_apply_tax.isChecked(),
                'invoice_numbering': self.invoice_numbering.currentText(),
                'invoice_prefix': self.invoice_prefix.text(),
                'auto_backup': self.auto_backup.isChecked(),
                'backup_frequency': self.backup_frequency.currentText(),
                'allow_edit_saved': self.allow_edit_saved.isChecked(),
                'allow_delete_invoices': self.allow_delete_invoices.isChecked(),
                'auto_print': self.auto_print.isChecked()
            }
            
            # حفظ في قاعدة البيانات
            # هنا يتم حفظ البيانات في قاعدة البيانات
            
            QMessageBox.information(self, "نجح الحفظ", 
                                  "✅ تم حفظ المؤشرات العامة بنجاح!\n\n"
                                  f"🏢 الشركة: {settings_data['company_name']}\n"
                                  f"💱 العملة: {settings_data['base_currency']}\n"
                                  f"📅 السنة المالية: {settings_data['fiscal_year_start']} إلى {settings_data['fiscal_year_end']}")
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات:\n{str(e)}")


def main():
    """اختبار الشاشة"""
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass
    
    dialog = GeneralIndicatorsDialog(MockDBManager())
    dialog.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
