#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة المدفوعات
Payments Management Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QDoubleSpinBox, QCheckBox,
                             QDateEdit, QSplitter, QFrame, QDialogButtonBox,
                             QScrollArea, QProgressBar)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor, QPixmap, QPainter
from datetime import datetime, timedelta

from ..database.sqlite_manager import SQLiteManager


class PaymentDialog(QDialog):
    """حوار تسجيل مدفوعات جديدة"""
    
    def __init__(self, db_manager: SQLiteManager, payment_data=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.payment_data = payment_data
        self.is_edit_mode = payment_data is not None
        
        self.setup_ui()
        self.setup_connections()
        self.load_invoices()
        
        if self.is_edit_mode:
            self.load_payment_data()
        else:
            self.generate_payment_number()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        title = "تعديل المدفوعات" if self.is_edit_mode else "تسجيل مدفوعات جديدة"
        self.setWindowTitle(title)
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel(f"💳 {title}")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout()
        
        # معلومات المدفوعات الأساسية
        payment_info_group = QGroupBox("📋 معلومات المدفوعات")
        payment_info_layout = QFormLayout()
        
        # رقم المدفوعات
        self.payment_number_edit = QLineEdit()
        self.payment_number_edit.setReadOnly(True)
        payment_info_layout.addRow("رقم المدفوعات:", self.payment_number_edit)
        
        # الفاتورة
        self.invoice_combo = QComboBox()
        self.invoice_combo.setMinimumWidth(300)
        payment_info_layout.addRow("الفاتورة *:", self.invoice_combo)
        
        # تاريخ المدفوعات
        self.payment_date_edit = QDateEdit()
        self.payment_date_edit.setDate(QDate.currentDate())
        self.payment_date_edit.setCalendarPopup(True)
        payment_info_layout.addRow("تاريخ المدفوعات:", self.payment_date_edit)
        
        # المبلغ
        self.amount_spin = QDoubleSpinBox()
        self.amount_spin.setRange(0.01, 9999999.99)
        self.amount_spin.setSuffix(" ريال")
        self.amount_spin.setDecimals(2)
        payment_info_layout.addRow("المبلغ *:", self.amount_spin)
        
        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["نقداً", "تحويل بنكي", "شيك", "بطاقة ائتمان", "أخرى"])
        payment_info_layout.addRow("طريقة الدفع:", self.payment_method_combo)
        
        payment_info_group.setLayout(payment_info_layout)
        scroll_layout.addWidget(payment_info_group)
        
        # معلومات الفاتورة
        invoice_info_group = QGroupBox("🧾 معلومات الفاتورة")
        invoice_info_layout = QFormLayout()
        
        # العميل
        self.customer_label = QLabel("غير محدد")
        self.customer_label.setStyleSheet("color: #2c3e50; font-weight: bold;")
        invoice_info_layout.addRow("العميل:", self.customer_label)
        
        # إجمالي الفاتورة
        self.invoice_total_label = QLabel("0.00 ريال")
        self.invoice_total_label.setStyleSheet("color: #3498db; font-weight: bold;")
        invoice_info_layout.addRow("إجمالي الفاتورة:", self.invoice_total_label)
        
        # المدفوع سابقاً
        self.paid_amount_label = QLabel("0.00 ريال")
        self.paid_amount_label.setStyleSheet("color: #27ae60; font-weight: bold;")
        invoice_info_layout.addRow("المدفوع سابقاً:", self.paid_amount_label)
        
        # المتبقي
        self.remaining_amount_label = QLabel("0.00 ريال")
        self.remaining_amount_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        invoice_info_layout.addRow("المتبقي:", self.remaining_amount_label)
        
        # المتبقي بعد هذه الدفعة
        self.new_remaining_label = QLabel("0.00 ريال")
        self.new_remaining_label.setStyleSheet("color: #f39c12; font-weight: bold; font-size: 14px;")
        invoice_info_layout.addRow("المتبقي بعد الدفع:", self.new_remaining_label)
        
        invoice_info_group.setLayout(invoice_info_layout)
        scroll_layout.addWidget(invoice_info_group)
        
        # تفاصيل إضافية
        details_group = QGroupBox("📝 تفاصيل إضافية")
        details_layout = QFormLayout()
        
        # رقم المرجع
        self.reference_number_edit = QLineEdit()
        self.reference_number_edit.setPlaceholderText("رقم الشيك، رقم التحويل، إلخ...")
        details_layout.addRow("رقم المرجع:", self.reference_number_edit)
        
        # اسم البنك
        self.bank_name_edit = QLineEdit()
        self.bank_name_edit.setPlaceholderText("اسم البنك (للتحويلات والشيكات)")
        details_layout.addRow("اسم البنك:", self.bank_name_edit)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        details_layout.addRow("ملاحظات:", self.notes_edit)
        
        details_group.setLayout(details_layout)
        scroll_layout.addWidget(details_group)
        
        scroll_widget.setLayout(scroll_layout)
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        main_layout.addWidget(button_box)
        
        self.setLayout(main_layout)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #27ae60;
            }
            QPushButton {
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                background-color: #27ae60;
                color: white;
                border: none;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #ddd;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_payment)
        self.cancel_btn.clicked.connect(self.reject)
        
        self.invoice_combo.currentTextChanged.connect(self.on_invoice_changed)
        self.amount_spin.valueChanged.connect(self.calculate_new_remaining)
        self.payment_method_combo.currentTextChanged.connect(self.on_payment_method_changed)
    
    def load_invoices(self):
        """تحميل الفواتير غير المدفوعة بالكامل"""
        try:
            query = """
                SELECT si.id, si.invoice_number, si.total_amount, si.paid_amount, 
                       si.remaining_amount, c.customer_name
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.id
                WHERE si.remaining_amount > 0 AND si.status != 'cancelled'
                ORDER BY si.invoice_date DESC
            """
            
            invoices = self.db_manager.execute_query(query)
            
            self.invoice_combo.clear()
            self.invoice_combo.addItem("اختر الفاتورة...", None)
            
            for invoice in invoices:
                display_text = f"{invoice['invoice_number']} - {invoice['customer_name']} - متبقي: {invoice['remaining_amount']:,.2f} ريال"
                self.invoice_combo.addItem(display_text, invoice)
                
        except Exception as e:
            print(f"خطأ في تحميل الفواتير: {e}")
    
    def generate_payment_number(self):
        """إنشاء رقم مدفوعات تلقائي"""
        try:
            # الحصول على آخر رقم مدفوعات
            query = "SELECT payment_number FROM payments ORDER BY id DESC LIMIT 1"
            result = self.db_manager.execute_query(query)
            
            if result:
                last_number = result[0]['payment_number']
                # استخراج الرقم من رقم المدفوعات
                if last_number.startswith('PAY'):
                    try:
                        last_num = int(last_number[3:])
                        new_number = last_num + 1
                        payment_number = f"PAY{new_number:05d}"
                    except:
                        payment_number = "PAY00001"
                else:
                    payment_number = "PAY00001"
            else:
                payment_number = "PAY00001"
            
            self.payment_number_edit.setText(payment_number)
            
        except Exception as e:
            print(f"خطأ في إنشاء رقم المدفوعات: {e}")
            self.payment_number_edit.setText("PAY00001")
    
    def on_invoice_changed(self):
        """معالجة تغيير الفاتورة"""
        invoice_data = self.invoice_combo.currentData()
        if invoice_data:
            # تحديث معلومات الفاتورة
            self.customer_label.setText(invoice_data['customer_name'])
            self.invoice_total_label.setText(f"{invoice_data['total_amount']:,.2f} ريال")
            self.paid_amount_label.setText(f"{invoice_data['paid_amount']:,.2f} ريال")
            self.remaining_amount_label.setText(f"{invoice_data['remaining_amount']:,.2f} ريال")
            
            # تعيين المبلغ الافتراضي للمتبقي
            self.amount_spin.setValue(float(invoice_data['remaining_amount']))
            
            self.calculate_new_remaining()
        else:
            # مسح المعلومات
            self.customer_label.setText("غير محدد")
            self.invoice_total_label.setText("0.00 ريال")
            self.paid_amount_label.setText("0.00 ريال")
            self.remaining_amount_label.setText("0.00 ريال")
            self.new_remaining_label.setText("0.00 ريال")
            self.amount_spin.setValue(0)
    
    def calculate_new_remaining(self):
        """حساب المتبقي بعد الدفعة الجديدة"""
        invoice_data = self.invoice_combo.currentData()
        if invoice_data:
            current_remaining = float(invoice_data['remaining_amount'])
            payment_amount = self.amount_spin.value()
            new_remaining = current_remaining - payment_amount
            
            self.new_remaining_label.setText(f"{new_remaining:,.2f} ريال")
            
            # تغيير اللون حسب المبلغ
            if new_remaining <= 0:
                self.new_remaining_label.setStyleSheet("color: #27ae60; font-weight: bold; font-size: 14px;")
            elif new_remaining < current_remaining:
                self.new_remaining_label.setStyleSheet("color: #f39c12; font-weight: bold; font-size: 14px;")
            else:
                self.new_remaining_label.setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 14px;")
    
    def on_payment_method_changed(self):
        """معالجة تغيير طريقة الدفع"""
        payment_method = self.payment_method_combo.currentText()
        
        # إظهار/إخفاء الحقول حسب طريقة الدفع
        if payment_method in ["تحويل بنكي", "شيك"]:
            self.reference_number_edit.setEnabled(True)
            self.bank_name_edit.setEnabled(True)
            
            if payment_method == "تحويل بنكي":
                self.reference_number_edit.setPlaceholderText("رقم التحويل...")
            else:
                self.reference_number_edit.setPlaceholderText("رقم الشيك...")
        else:
            if payment_method == "نقداً":
                self.reference_number_edit.setEnabled(False)
                self.bank_name_edit.setEnabled(False)
                self.reference_number_edit.clear()
                self.bank_name_edit.clear()
            else:
                self.reference_number_edit.setEnabled(True)
                self.bank_name_edit.setEnabled(False)
                self.bank_name_edit.clear()
                
                if payment_method == "بطاقة ائتمان":
                    self.reference_number_edit.setPlaceholderText("رقم العملية...")
                else:
                    self.reference_number_edit.setPlaceholderText("رقم المرجع...")
    
    def validate_payment(self):
        """التحقق من صحة بيانات المدفوعات"""
        if not self.invoice_combo.currentData():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار الفاتورة")
            return False
        
        if self.amount_spin.value() <= 0:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
            return False
        
        invoice_data = self.invoice_combo.currentData()
        if self.amount_spin.value() > float(invoice_data['remaining_amount']):
            reply = QMessageBox.question(
                self, 'تحذير',
                f'المبلغ المدخل ({self.amount_spin.value():,.2f} ريال) أكبر من المتبقي ({invoice_data["remaining_amount"]:,.2f} ريال).\n\nهل تريد المتابعة؟',
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return False
        
        return True
    
    def save_payment(self):
        """حفظ المدفوعات"""
        if not self.validate_payment():
            return
        
        try:
            # جمع بيانات المدفوعات
            payment_data = self.get_payment_data()
            
            if self.is_edit_mode:
                # تحديث المدفوعات
                self.update_payment(payment_data)
            else:
                # إنشاء مدفوعات جديدة
                self.create_payment(payment_data)
            
            QMessageBox.information(self, "نجح", "تم حفظ المدفوعات بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ المدفوعات:\n{str(e)}")
    
    def get_payment_data(self):
        """جمع بيانات المدفوعات"""
        invoice_data = self.invoice_combo.currentData()
        
        # تحويل طريقة الدفع
        payment_method_map = {
            "نقداً": "cash",
            "تحويل بنكي": "bank_transfer",
            "شيك": "check",
            "بطاقة ائتمان": "credit_card",
            "أخرى": "other"
        }
        
        return {
            'payment_number': self.payment_number_edit.text(),
            'invoice_id': invoice_data['id'],
            'payment_date': self.payment_date_edit.date().toString('yyyy-MM-dd'),
            'amount': self.amount_spin.value(),
            'payment_method': payment_method_map.get(self.payment_method_combo.currentText(), 'cash'),
            'reference_number': self.reference_number_edit.text().strip(),
            'bank_name': self.bank_name_edit.text().strip(),
            'notes': self.notes_edit.toPlainText().strip()
        }
    
    def create_payment(self, payment_data):
        """إنشاء مدفوعات جديدة"""
        # إدراج المدفوعات
        payment_query = """
            INSERT INTO payments (
                payment_number, invoice_id, payment_date, amount,
                payment_method, reference_number, bank_name, notes, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        payment_params = (
            payment_data['payment_number'], payment_data['invoice_id'],
            payment_data['payment_date'], payment_data['amount'],
            payment_data['payment_method'], payment_data['reference_number'],
            payment_data['bank_name'], payment_data['notes'], 1  # created_by
        )
        
        self.db_manager.execute_insert(payment_query, payment_params)
        
        # تحديث الفاتورة
        self.update_invoice_amounts(payment_data['invoice_id'], payment_data['amount'])
    
    def update_payment(self, payment_data):
        """تحديث مدفوعات موجودة"""
        # الحصول على المبلغ القديم
        old_amount_query = "SELECT amount, invoice_id FROM payments WHERE id = ?"
        old_result = self.db_manager.execute_query(old_amount_query, (self.payment_data['id'],))
        old_amount = float(old_result[0]['amount']) if old_result else 0
        old_invoice_id = old_result[0]['invoice_id'] if old_result else None
        
        # تحديث المدفوعات
        payment_query = """
            UPDATE payments SET
                invoice_id = ?, payment_date = ?, amount = ?,
                payment_method = ?, reference_number = ?, bank_name = ?, notes = ?
            WHERE id = ?
        """
        
        payment_params = (
            payment_data['invoice_id'], payment_data['payment_date'],
            payment_data['amount'], payment_data['payment_method'],
            payment_data['reference_number'], payment_data['bank_name'],
            payment_data['notes'], self.payment_data['id']
        )
        
        self.db_manager.execute_update(payment_query, payment_params)
        
        # تحديث الفاتورة القديمة (إذا تغيرت)
        if old_invoice_id and old_invoice_id != payment_data['invoice_id']:
            self.update_invoice_amounts(old_invoice_id, -old_amount)
        
        # تحديث الفاتورة الجديدة
        amount_diff = payment_data['amount'] - old_amount
        self.update_invoice_amounts(payment_data['invoice_id'], amount_diff)
    
    def update_invoice_amounts(self, invoice_id, amount_change):
        """تحديث مبالغ الفاتورة"""
        # الحصول على المبالغ الحالية
        invoice_query = "SELECT paid_amount, total_amount FROM sales_invoices WHERE id = ?"
        invoice_result = self.db_manager.execute_query(invoice_query, (invoice_id,))
        
        if invoice_result:
            current_paid = float(invoice_result[0]['paid_amount'])
            total_amount = float(invoice_result[0]['total_amount'])
            
            # حساب المبالغ الجديدة
            new_paid = current_paid + amount_change
            new_remaining = total_amount - new_paid
            
            # تحديد حالة الفاتورة
            if new_remaining <= 0:
                status = 'paid'
            elif new_paid > 0:
                status = 'partial'
            else:
                status = 'sent'
            
            # تحديث الفاتورة
            update_query = """
                UPDATE sales_invoices SET
                    paid_amount = ?, remaining_amount = ?, status = ?
                WHERE id = ?
            """
            
            self.db_manager.execute_update(update_query, (new_paid, new_remaining, status, invoice_id))
    
    def load_payment_data(self):
        """تحميل بيانات المدفوعات للتعديل"""
        if not self.payment_data:
            return
        
        data = self.payment_data
        
        # المعلومات الأساسية
        self.payment_number_edit.setText(data.get('payment_number', ''))
        
        # البحث عن الفاتورة في القائمة
        invoice_id = data.get('invoice_id')
        for i in range(self.invoice_combo.count()):
            invoice_data = self.invoice_combo.itemData(i)
            if invoice_data and invoice_data['id'] == invoice_id:
                self.invoice_combo.setCurrentIndex(i)
                break
        
        # التاريخ والمبلغ
        payment_date = QDate.fromString(data.get('payment_date', ''), 'yyyy-MM-dd')
        self.payment_date_edit.setDate(payment_date)
        self.amount_spin.setValue(float(data.get('amount', 0)))
        
        # طريقة الدفع
        payment_method_map = {
            "cash": "نقداً",
            "bank_transfer": "تحويل بنكي",
            "check": "شيك",
            "credit_card": "بطاقة ائتمان",
            "other": "أخرى"
        }
        
        payment_method_text = payment_method_map.get(data.get('payment_method', 'cash'), 'نقداً')
        self.payment_method_combo.setCurrentText(payment_method_text)
        
        # التفاصيل الإضافية
        self.reference_number_edit.setText(data.get('reference_number', ''))
        self.bank_name_edit.setText(data.get('bank_name', ''))
        self.notes_edit.setPlainText(data.get('notes', ''))


class PaymentsWidget(QWidget):
    """وحدة إدارة المدفوعات"""

    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.setup_connections()
        self.load_payments()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()

        # العنوان
        title = QLabel("💳 إدارة المدفوعات")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #27ae60, stop:1 #229954);
                color: white;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title)

        # شريط الأدوات
        toolbar_layout = QHBoxLayout()

        self.new_payment_btn = QPushButton("➕ تسجيل مدفوعات")
        self.edit_payment_btn = QPushButton("✏️ تعديل")
        self.delete_payment_btn = QPushButton("🗑️ حذف")
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.export_btn = QPushButton("📤 تصدير")
        self.outstanding_btn = QPushButton("📊 المبالغ المستحقة")

        # تنسيق الأزرار
        buttons = [self.new_payment_btn, self.edit_payment_btn, self.delete_payment_btn,
                  self.refresh_btn, self.export_btn, self.outstanding_btn]

        for btn in buttons:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 120px;
                }
                QPushButton:hover {
                    background-color: #229954;
                }
                QPushButton:pressed {
                    background-color: #1e8449;
                }
                QPushButton:disabled {
                    background-color: #bdc3c7;
                }
            """)

        # ألوان خاصة لبعض الأزرار
        self.delete_payment_btn.setStyleSheet(self.delete_payment_btn.styleSheet().replace("#27ae60", "#e74c3c"))
        self.export_btn.setStyleSheet(self.export_btn.styleSheet().replace("#27ae60", "#f39c12"))
        self.outstanding_btn.setStyleSheet(self.outstanding_btn.styleSheet().replace("#27ae60", "#3498db"))

        toolbar_layout.addWidget(self.new_payment_btn)
        toolbar_layout.addWidget(self.edit_payment_btn)
        toolbar_layout.addWidget(self.delete_payment_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addWidget(self.export_btn)
        toolbar_layout.addWidget(self.outstanding_btn)
        toolbar_layout.addStretch()

        main_layout.addLayout(toolbar_layout)

        # منطقة البحث والفلترة
        search_layout = QHBoxLayout()

        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في المدفوعات...")
        search_layout.addWidget(self.search_edit)

        search_layout.addWidget(QLabel("طريقة الدفع:"))
        self.payment_method_filter = QComboBox()
        self.payment_method_filter.addItems(["الكل", "نقداً", "تحويل بنكي", "شيك", "بطاقة ائتمان", "أخرى"])
        search_layout.addWidget(self.payment_method_filter)

        search_layout.addWidget(QLabel("من تاريخ:"))
        self.from_date_edit = QDateEdit()
        self.from_date_edit.setDate(QDate.currentDate().addDays(-30))
        self.from_date_edit.setCalendarPopup(True)
        search_layout.addWidget(self.from_date_edit)

        search_layout.addWidget(QLabel("إلى تاريخ:"))
        self.to_date_edit = QDateEdit()
        self.to_date_edit.setDate(QDate.currentDate())
        self.to_date_edit.setCalendarPopup(True)
        search_layout.addWidget(self.to_date_edit)

        main_layout.addLayout(search_layout)

        # جدول المدفوعات
        self.payments_table = QTableWidget()
        columns = ["رقم المدفوعات", "رقم الفاتورة", "العميل", "التاريخ", "المبلغ", "طريقة الدفع", "رقم المرجع", "البنك"]
        self.payments_table.setColumnCount(len(columns))
        self.payments_table.setHorizontalHeaderLabels(columns)

        # تنسيق الجدول
        self.payments_table.setAlternatingRowColors(True)
        self.payments_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.payments_table.setSelectionMode(QTableWidget.SingleSelection)
        self.payments_table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = self.payments_table.horizontalHeader()
        header.resizeSection(0, 120)  # رقم المدفوعات
        header.resizeSection(1, 120)  # رقم الفاتورة
        header.resizeSection(2, 200)  # العميل
        header.resizeSection(3, 100)  # التاريخ
        header.resizeSection(4, 120)  # المبلغ
        header.resizeSection(5, 120)  # طريقة الدفع
        header.resizeSection(6, 120)  # رقم المرجع
        header.setStretchLastSection(True)  # البنك

        # ربط النقر المزدوج
        self.payments_table.itemDoubleClicked.connect(self.edit_payment)

        main_layout.addWidget(self.payments_table)

        # شريط الحالة
        status_layout = QHBoxLayout()
        self.payments_count_label = QLabel("عدد المدفوعات: 0")
        self.total_payments_label = QLabel("إجمالي المدفوعات: 0 ريال")
        self.outstanding_amount_label = QLabel("المبالغ المستحقة: 0 ريال")

        status_layout.addWidget(self.payments_count_label)
        status_layout.addWidget(self.total_payments_label)
        status_layout.addStretch()
        status_layout.addWidget(self.outstanding_amount_label)

        main_layout.addLayout(status_layout)

        self.setLayout(main_layout)
        self.setStyleSheet(self.get_module_stylesheet())

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.new_payment_btn.clicked.connect(self.new_payment)
        self.edit_payment_btn.clicked.connect(self.edit_payment)
        self.delete_payment_btn.clicked.connect(self.delete_payment)
        self.refresh_btn.clicked.connect(self.load_payments)
        self.export_btn.clicked.connect(self.export_payments)
        self.outstanding_btn.clicked.connect(self.show_outstanding_amounts)

        self.search_edit.textChanged.connect(self.filter_payments)
        self.payment_method_filter.currentTextChanged.connect(self.filter_payments)
        self.from_date_edit.dateChanged.connect(self.filter_payments)
        self.to_date_edit.dateChanged.connect(self.filter_payments)

    def load_payments(self):
        """تحميل المدفوعات"""
        try:
            query = """
                SELECT p.*, si.invoice_number, c.customer_name
                FROM payments p
                LEFT JOIN sales_invoices si ON p.invoice_id = si.id
                LEFT JOIN customers c ON si.customer_id = c.id
                ORDER BY p.payment_date DESC, p.id DESC
            """

            payments = self.db_manager.execute_query(query)
            self.payments_table.setRowCount(len(payments))

            total_payments = 0

            # خريطة ترجمة طرق الدفع
            payment_method_map = {
                "cash": "نقداً",
                "bank_transfer": "تحويل بنكي",
                "check": "شيك",
                "credit_card": "بطاقة ائتمان",
                "other": "أخرى"
            }

            for row, payment in enumerate(payments):
                # رقم المدفوعات
                payment_number_item = QTableWidgetItem(payment.get('payment_number', ''))
                payment_number_item.setData(Qt.UserRole, payment.get('id'))
                self.payments_table.setItem(row, 0, payment_number_item)

                # رقم الفاتورة
                self.payments_table.setItem(row, 1, QTableWidgetItem(payment.get('invoice_number', '')))

                # العميل
                self.payments_table.setItem(row, 2, QTableWidgetItem(payment.get('customer_name', '')))

                # التاريخ
                payment_date = payment.get('payment_date', '')
                if payment_date:
                    date_obj = QDate.fromString(payment_date, 'yyyy-MM-dd')
                    formatted_date = date_obj.toString('dd/MM/yyyy')
                    self.payments_table.setItem(row, 3, QTableWidgetItem(formatted_date))

                # المبلغ
                amount = float(payment.get('amount', 0))
                total_payments += amount
                amount_item = QTableWidgetItem(f"{amount:,.2f}")
                amount_item.setBackground(QColor("#d4edda"))
                amount_item.setForeground(QColor("#155724"))
                self.payments_table.setItem(row, 4, amount_item)

                # طريقة الدفع
                payment_method = payment.get('payment_method', 'cash')
                method_text = payment_method_map.get(payment_method, payment_method)
                method_item = QTableWidgetItem(method_text)

                # تلوين طريقة الدفع
                method_colors = {
                    "نقداً": ("#28a745", "#ffffff"),
                    "تحويل بنكي": ("#007bff", "#ffffff"),
                    "شيك": ("#ffc107", "#212529"),
                    "بطاقة ائتمان": ("#6f42c1", "#ffffff"),
                    "أخرى": ("#6c757d", "#ffffff")
                }

                if method_text in method_colors:
                    bg_color, text_color = method_colors[method_text]
                    method_item.setBackground(QColor(bg_color))
                    method_item.setForeground(QColor(text_color))

                self.payments_table.setItem(row, 5, method_item)

                # رقم المرجع
                self.payments_table.setItem(row, 6, QTableWidgetItem(payment.get('reference_number', '')))

                # البنك
                self.payments_table.setItem(row, 7, QTableWidgetItem(payment.get('bank_name', '')))

            # حساب المبالغ المستحقة
            outstanding_query = "SELECT SUM(remaining_amount) as total FROM sales_invoices WHERE remaining_amount > 0"
            outstanding_result = self.db_manager.execute_query(outstanding_query)
            outstanding_amount = outstanding_result[0]['total'] if outstanding_result and outstanding_result[0]['total'] else 0

            # تحديث الإحصائيات
            self.payments_count_label.setText(f"عدد المدفوعات: {len(payments)}")
            self.total_payments_label.setText(f"إجمالي المدفوعات: {total_payments:,.2f} ريال")
            self.outstanding_amount_label.setText(f"المبالغ المستحقة: {outstanding_amount:,.2f} ريال")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المدفوعات:\n{str(e)}")

    def new_payment(self):
        """تسجيل مدفوعات جديدة"""
        dialog = PaymentDialog(self.db_manager, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_payments()

    def edit_payment(self):
        """تعديل المدفوعات المحددة"""
        current_row = self.payments_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مدفوعات للتعديل")
            return

        try:
            # الحصول على معرف المدفوعات
            payment_id = self.payments_table.item(current_row, 0).data(Qt.UserRole)

            # استعلام البيانات الكاملة
            query = "SELECT * FROM payments WHERE id = ?"
            result = self.db_manager.execute_query(query, (payment_id,))

            if result:
                payment_data = result[0]
                dialog = PaymentDialog(self.db_manager, payment_data, self)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_payments()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات المدفوعات:\n{str(e)}")

    def delete_payment(self):
        """حذف المدفوعات المحددة"""
        current_row = self.payments_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مدفوعات للحذف")
            return

        payment_number = self.payments_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self, 'تأكيد الحذف',
            f'هل أنت متأكد من حذف المدفوعات "{payment_number}"؟\n\nسيتم تحديث مبالغ الفاتورة المرتبطة.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                payment_id = self.payments_table.item(current_row, 0).data(Qt.UserRole)

                # الحصول على بيانات المدفوعات
                payment_query = "SELECT amount, invoice_id FROM payments WHERE id = ?"
                payment_result = self.db_manager.execute_query(payment_query, (payment_id,))

                if payment_result:
                    amount = float(payment_result[0]['amount'])
                    invoice_id = payment_result[0]['invoice_id']

                    # حذف المدفوعات
                    delete_query = "DELETE FROM payments WHERE id = ?"
                    self.db_manager.execute_update(delete_query, (payment_id,))

                    # تحديث الفاتورة (طرح المبلغ)
                    self.update_invoice_amounts(invoice_id, -amount)

                    QMessageBox.information(self, "نجح", "تم حذف المدفوعات بنجاح")
                    self.load_payments()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف المدفوعات:\n{str(e)}")

    def update_invoice_amounts(self, invoice_id, amount_change):
        """تحديث مبالغ الفاتورة"""
        try:
            # الحصول على المبالغ الحالية
            invoice_query = "SELECT paid_amount, total_amount FROM sales_invoices WHERE id = ?"
            invoice_result = self.db_manager.execute_query(invoice_query, (invoice_id,))

            if invoice_result:
                current_paid = float(invoice_result[0]['paid_amount'])
                total_amount = float(invoice_result[0]['total_amount'])

                # حساب المبالغ الجديدة
                new_paid = current_paid + amount_change
                new_remaining = total_amount - new_paid

                # تحديد حالة الفاتورة
                if new_remaining <= 0:
                    status = 'paid'
                elif new_paid > 0:
                    status = 'partial'
                else:
                    status = 'sent'

                # تحديث الفاتورة
                update_query = """
                    UPDATE sales_invoices SET
                        paid_amount = ?, remaining_amount = ?, status = ?
                    WHERE id = ?
                """

                self.db_manager.execute_update(update_query, (new_paid, new_remaining, status, invoice_id))

        except Exception as e:
            print(f"خطأ في تحديث مبالغ الفاتورة: {e}")

    def filter_payments(self):
        """فلترة المدفوعات"""
        search_text = self.search_edit.text().lower()
        payment_method_filter = self.payment_method_filter.currentText()
        from_date = self.from_date_edit.date()
        to_date = self.to_date_edit.date()

        for row in range(self.payments_table.rowCount()):
            show_row = True

            # فلترة النص
            if search_text:
                row_text = ""
                for col in range(self.payments_table.columnCount()):
                    item = self.payments_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                if search_text not in row_text:
                    show_row = False

            # فلترة طريقة الدفع
            if payment_method_filter != "الكل":
                method_item = self.payments_table.item(row, 5)
                if method_item and method_item.text() != payment_method_filter:
                    show_row = False

            # فلترة التاريخ
            date_item = self.payments_table.item(row, 3)
            if date_item:
                payment_date = QDate.fromString(date_item.text(), 'dd/MM/yyyy')
                if payment_date < from_date or payment_date > to_date:
                    show_row = False

            self.payments_table.setRowHidden(row, not show_row)

    def show_outstanding_amounts(self):
        """عرض المبالغ المستحقة"""
        try:
            query = """
                SELECT si.invoice_number, c.customer_name, si.invoice_date,
                       si.due_date, si.total_amount, si.paid_amount, si.remaining_amount,
                       CASE
                           WHEN si.due_date < date('now') THEN 'متأخرة'
                           ELSE 'مستحقة'
                       END as status
                FROM sales_invoices si
                LEFT JOIN customers c ON si.customer_id = c.id
                WHERE si.remaining_amount > 0 AND si.status != 'cancelled'
                ORDER BY si.due_date ASC
            """

            outstanding = self.db_manager.execute_query(query)

            if not outstanding:
                QMessageBox.information(self, "المبالغ المستحقة", "لا توجد مبالغ مستحقة حالياً")
                return

            # إنشاء حوار عرض المبالغ المستحقة
            dialog = OutstandingAmountsDialog(outstanding, self)
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل المبالغ المستحقة:\n{str(e)}")

    def export_payments(self):
        """تصدير المدفوعات"""
        QMessageBox.information(self, "تصدير المدفوعات", "وظيفة تصدير المدفوعات قيد التطوير")

    def get_module_stylesheet(self):
        """تنسيق الوحدة"""
        return """
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLineEdit, QComboBox, QDateEdit {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus, QDateEdit:focus {
                border-color: #27ae60;
                outline: none;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #27ae60;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """


class OutstandingAmountsDialog(QDialog):
    """حوار عرض المبالغ المستحقة"""

    def __init__(self, outstanding_data, parent=None):
        super().__init__(parent)
        self.outstanding_data = outstanding_data
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("المبالغ المستحقة")
        self.setFixedSize(800, 600)
        self.setModal(True)

        layout = QVBoxLayout()

        # العنوان
        title = QLabel("📊 المبالغ المستحقة من العملاء")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e74c3c, stop:1 #c0392b);
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # جدول المبالغ المستحقة
        self.outstanding_table = QTableWidget()
        columns = ["رقم الفاتورة", "العميل", "تاريخ الفاتورة", "تاريخ الاستحقاق", "الإجمالي", "المدفوع", "المتبقي", "الحالة"]
        self.outstanding_table.setColumnCount(len(columns))
        self.outstanding_table.setHorizontalHeaderLabels(columns)

        # تعبئة البيانات
        self.outstanding_table.setRowCount(len(self.outstanding_data))

        total_outstanding = 0
        overdue_count = 0

        for row, item in enumerate(self.outstanding_data):
            # رقم الفاتورة
            self.outstanding_table.setItem(row, 0, QTableWidgetItem(item.get('invoice_number', '')))

            # العميل
            self.outstanding_table.setItem(row, 1, QTableWidgetItem(item.get('customer_name', '')))

            # تاريخ الفاتورة
            invoice_date = item.get('invoice_date', '')
            if invoice_date:
                date_obj = QDate.fromString(invoice_date, 'yyyy-MM-dd')
                formatted_date = date_obj.toString('dd/MM/yyyy')
                self.outstanding_table.setItem(row, 2, QTableWidgetItem(formatted_date))

            # تاريخ الاستحقاق
            due_date = item.get('due_date', '')
            if due_date:
                due_date_obj = QDate.fromString(due_date, 'yyyy-MM-dd')
                formatted_due_date = due_date_obj.toString('dd/MM/yyyy')
                self.outstanding_table.setItem(row, 3, QTableWidgetItem(formatted_due_date))

            # الإجمالي
            total_amount = float(item.get('total_amount', 0))
            self.outstanding_table.setItem(row, 4, QTableWidgetItem(f"{total_amount:,.2f}"))

            # المدفوع
            paid_amount = float(item.get('paid_amount', 0))
            self.outstanding_table.setItem(row, 5, QTableWidgetItem(f"{paid_amount:,.2f}"))

            # المتبقي
            remaining_amount = float(item.get('remaining_amount', 0))
            total_outstanding += remaining_amount
            remaining_item = QTableWidgetItem(f"{remaining_amount:,.2f}")
            remaining_item.setBackground(QColor("#f8d7da"))
            remaining_item.setForeground(QColor("#721c24"))
            self.outstanding_table.setItem(row, 6, remaining_item)

            # الحالة
            status = item.get('status', 'مستحقة')
            status_item = QTableWidgetItem(status)

            if status == 'متأخرة':
                status_item.setBackground(QColor("#dc3545"))
                status_item.setForeground(QColor("#ffffff"))
                overdue_count += 1
            else:
                status_item.setBackground(QColor("#ffc107"))
                status_item.setForeground(QColor("#212529"))

            self.outstanding_table.setItem(row, 7, status_item)

        # تنسيق الجدول
        self.outstanding_table.setAlternatingRowColors(True)
        self.outstanding_table.setSortingEnabled(True)
        header = self.outstanding_table.horizontalHeader()
        header.setStretchLastSection(True)

        layout.addWidget(self.outstanding_table)

        # إحصائيات
        stats_layout = QHBoxLayout()

        total_label = QLabel(f"إجمالي المبالغ المستحقة: {total_outstanding:,.2f} ريال")
        total_label.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 14px;")

        overdue_label = QLabel(f"الفواتير المتأخرة: {overdue_count}")
        overdue_label.setStyleSheet("font-weight: bold; color: #dc3545; font-size: 14px;")

        stats_layout.addWidget(total_label)
        stats_layout.addStretch()
        stats_layout.addWidget(overdue_label)

        layout.addLayout(stats_layout)

        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.accept)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)

        layout.addWidget(close_btn)

        self.setLayout(layout)
