#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مساعد أنماط النص
Text Styles Helper
"""

def get_title_style(bg_color="#3498db"):
    """الحصول على نمط العنوان"""
    return f"""
        QLabel {{
            font-family: "Tahoma", "Arial", sans-serif;
            font-size: 18px;
            font-weight: bold;
            color: white;
            background-color: {bg_color};
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
        }}
    """

def get_button_style(bg_color="#27ae60", hover_color="#229954"):
    """الحصول على نمط الزر"""
    return f"""
        QPushButton {{
            font-family: "Tahoma", "Arial", sans-serif;
            background-color: {bg_color};
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            font-size: 14px;
            border: none;
        }}
        QPushButton:hover {{
            background-color: {hover_color};
        }}
        QPushButton:pressed {{
            background-color: {hover_color};
            padding: 11px 19px 9px 21px;
        }}
    """

def get_info_label_style():
    """الحصول على نمط تسمية المعلومات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", sans-serif;
            color: #2c3e50;
            font-size: 14px;
            font-weight: bold;
            padding: 8px 15px;
            border-radius: 5px;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
        }
    """

def get_status_style(status_type="success"):
    """الحصول على نمط الحالة"""
    colors = {
        "success": {"bg": "#d5f4e6", "border": "#27ae60", "color": "#27ae60"},
        "warning": {"bg": "#fff3cd", "border": "#f39c12", "color": "#f39c12"},
        "error": {"bg": "#ffebee", "border": "#e74c3c", "color": "#e74c3c"},
        "info": {"bg": "#ebf3fd", "border": "#3498db", "color": "#3498db"}
    }
    
    style_colors = colors.get(status_type, colors["info"])
    
    return f"""
        QLabel {{
            font-family: "Tahoma", "Arial", sans-serif;
            color: {style_colors["color"]};
            font-weight: bold;
            padding: 8px 15px;
            border: 2px solid {style_colors["border"]};
            border-radius: 5px;
            background-color: {style_colors["bg"]};
            font-size: 14px;
        }}
    """

def get_table_style():
    """الحصول على نمط الجدول"""
    return """
        QTableWidget {
            font-family: "Tahoma", "Arial", sans-serif;
            background-color: white;
            alternate-background-color: #f8f9fa;
            selection-background-color: #3498db;
            selection-color: white;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            font-size: 12px;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #ecf0f1;
        }
        
        QHeaderView::section {
            background-color: #34495e;
            color: white;
            padding: 10px;
            border: none;
            font-weight: bold;
            font-size: 13px;
        }
    """

def get_text_edit_style():
    """الحصول على نمط منطقة النص"""
    return """
        QTextEdit {
            font-family: "Courier New", "Consolas", monospace;
            background-color: white;
            color: #2c3e50;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            padding: 10px;
            font-size: 12px;
            line-height: 1.4;
        }
        
        QTextEdit:focus {
            border-color: #3498db;
        }
    """

def apply_global_font_fix():
    """تطبيق إصلاح الخط العام"""
    return """
        * {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
        }
        
        QWidget {
            color: #2c3e50;
            background-color: #ffffff;
        }
        
        QDialog {
            background-color: #f8f9fa;
        }
    """
