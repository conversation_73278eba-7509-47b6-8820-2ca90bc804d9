#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وحدة العملاء والموردين
Test Customers and Suppliers Module
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_customers_suppliers_module():
    """اختبار وحدة العملاء والموردين"""
    print("🧪 اختبار وحدة العملاء والموردين...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        from src.modules.customers_suppliers_module import CustomersSupplierModule
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء وحدة العملاء والموردين
        customers_suppliers_widget = CustomersSupplierModule(db_manager)
        customers_suppliers_widget.show()
        
        print("✅ تم إنشاء وحدة العملاء والموردين بنجاح")
        
        # إغلاق النافذة تلقائياً بعد 3 ثوان
        def close_widget():
            print("🔄 إغلاق الوحدة...")
            customers_suppliers_widget.close()
            app.quit()
        
        QTimer.singleShot(3000, close_widget)
        
        # تشغيل التطبيق
        app.exec_()
        
        # إغلاق قاعدة البيانات
        db_manager.disconnect()
        
        print("✅ تم اختبار وحدة العملاء والموردين بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وحدة العملاء والموردين: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_add_sample_customers_suppliers():
    """اختبار إضافة عملاء وموردين نموذجيين"""
    print("\n🧪 اختبار إضافة عملاء وموردين نموذجيين...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # بيانات عملاء وموردين نموذجيين
        sample_data = [
            {
                'code': 'C001',
                'name_ar': 'شركة الرياض للتجارة',
                'name_en': 'Riyadh Trading Company',
                'type': 'customer',
                'category': 'VIP',
                'phone': '0112345678',
                'email': '<EMAIL>',
                'city': 'الرياض',
                'credit_limit': 50000.00
            },
            {
                'code': 'S001',
                'name_ar': 'مؤسسة جدة للمواد',
                'name_en': 'Jeddah Materials Est.',
                'type': 'supplier',
                'category': 'جملة',
                'phone': '0123456789',
                'email': '<EMAIL>',
                'city': 'جدة',
                'credit_limit': 100000.00
            },
            {
                'code': 'CS001',
                'name_ar': 'شركة الدمام المتحدة',
                'name_en': 'Dammam United Company',
                'type': 'both',
                'category': 'عادي',
                'phone': '0134567890',
                'email': '<EMAIL>',
                'city': 'الدمام',
                'credit_limit': 75000.00
            }
        ]
        
        # إضافة البيانات النموذجية
        for data in sample_data:
            query = """
                INSERT INTO customers_suppliers (
                    code, name_ar, name_en, type, phone, email, credit_limit
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            
            params = (
                data['code'], data['name_ar'], data['name_en'], data['type'],
                data['phone'], data['email'], data['credit_limit']
            )
            
            db_manager.execute_update(query, params)
            print(f"✅ تم إضافة {data['name_ar']}")
        
        # التحقق من البيانات المضافة
        result = db_manager.execute_query("SELECT COUNT(*) as count FROM customers_suppliers")
        count = result[0]['count'] if result else 0
        
        print(f"✅ تم إضافة {count} عميل/مورد بنجاح")
        
        # إغلاق قاعدة البيانات
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات النموذجية: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار شامل لوحدة العملاء والموردين")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 2
    
    # اختبار إضافة البيانات النموذجية
    if test_add_sample_customers_suppliers():
        tests_passed += 1
    
    # اختبار الوحدة
    if test_customers_suppliers_module():
        tests_passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار:")
    print(f"   ✅ نجح: {tests_passed}/{total_tests}")
    print(f"   ❌ فشل: {total_tests - tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 جميع اختبارات وحدة العملاء والموردين نجحت!")
        return 0
    else:
        print("⚠️ بعض الاختبارات فشلت")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        sys.exit(1)