#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف نظام مولد التقارير
Reports Generator Functions
"""

from datetime import datetime, date
from PyQt5.QtWidgets import QMessageBox, QFileDialog
from PyQt5.QtCore import QDate

def load_report_templates_function(self):
    """تحميل قوالب التقارير"""
    # قوالب نموذجية
    self.report_templates = {
        "تقرير المبيعات الشهري": {
            "type": "تقرير مبيعات",
            "fields": ["التاريخ", "رقم الفاتورة", "اسم العميل", "الإجمالي", "طريقة الدفع"],
            "sources": ["فواتير البيع", "العملاء والزبائن"],
            "description": "تقرير شامل عن مبيعات الشهر مع تفاصيل العملاء والمبالغ"
        },
        "تقرير المخزون": {
            "type": "تقرير مخزون",
            "fields": ["اسم المنتج", "الكمية", "السعر", "القيمة الإجمالية"],
            "sources": ["المنتجات والخدمات", "المخزون والمستودعات"],
            "description": "تقرير حالة المخزون الحالية مع القيم والكميات"
        },
        "تقرير الأرباح والخسائر": {
            "type": "تقرير مالي",
            "fields": ["الإيرادات", "المصروفات", "صافي الربح", "النسبة المئوية"],
            "sources": ["الإيرادات", "المصروفات", "الحسابات المالية"],
            "description": "تقرير مالي شامل يوضح الأرباح والخسائر للفترة المحددة"
        },
        "تقرير العملاء": {
            "type": "تقرير عملاء",
            "fields": ["اسم العميل", "الهاتف", "المدينة", "إجمالي المشتريات", "آخر معاملة"],
            "sources": ["العملاء والزبائن", "فواتير البيع"],
            "description": "تقرير شامل عن العملاء ونشاطهم التجاري"
        }
    }
    
    # تحديث قائمة القوالب
    self.templates_list.clear()
    for template_name in self.report_templates.keys():
        self.templates_list.addItem(template_name)

def on_report_type_changed_function(self):
    """عند تغيير نوع التقرير"""
    report_type = self.report_type.currentText()
    
    # تحديث الحقول المقترحة حسب نوع التقرير
    suggested_fields = {
        "تقرير مالي": ["التاريخ", "الحساب", "المبلغ", "البيان", "الرصيد"],
        "تقرير مبيعات": ["التاريخ", "رقم الفاتورة", "العميل", "المنتج", "الكمية", "السعر", "الإجمالي"],
        "تقرير مخزون": ["المنتج", "الكمية المتاحة", "الكمية المحجوزة", "السعر", "القيمة"],
        "تقرير عملاء": ["اسم العميل", "الهاتف", "العنوان", "إجمالي المشتريات", "آخر معاملة"],
        "تقرير أرباح وخسائر": ["الإيرادات", "المصروفات", "الربح الإجمالي", "صافي الربح"],
        "تقرير ميزانية": ["الأصول", "الخصوم", "حقوق الملكية", "إجمالي الميزانية"],
        "تقرير تدفق نقدي": ["التدفقات الداخلة", "التدفقات الخارجة", "صافي التدفق"]
    }
    
    if report_type in suggested_fields:
        # مسح الحقول المحددة
        self.selected_fields.clear()
        
        # إضافة الحقول المقترحة
        for field in suggested_fields[report_type]:
            self.selected_fields.addItem(field)

def add_field_to_report_function(self):
    """إضافة حقل إلى التقرير"""
    current_item = self.available_fields.currentItem()
    if current_item:
        field_text = current_item.text()
        
        # التحقق من عدم وجود الحقل مسبقاً
        existing_items = [self.selected_fields.item(i).text() 
                         for i in range(self.selected_fields.count())]
        
        if field_text not in existing_items:
            self.selected_fields.addItem(field_text)

def remove_field_from_report_function(self):
    """إزالة حقل من التقرير"""
    current_item = self.selected_fields.currentItem()
    if current_item:
        row = self.selected_fields.row(current_item)
        self.selected_fields.takeItem(row)

def generate_report_function(self):
    """إنشاء التقرير"""
    # التحقق من البيانات المطلوبة
    if not self.report_name.text().strip():
        QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم التقرير")
        return
    
    if self.selected_fields.count() == 0:
        QMessageBox.warning(self, "تحذير", "يرجى تحديد حقول التقرير")
        return
    
    # جمع معايير التقرير
    report_criteria = {
        'name': self.report_name.text(),
        'type': self.report_type.currentText(),
        'date_from': self.date_from.date().toString('yyyy-MM-dd'),
        'date_to': self.date_to.date().toString('yyyy-MM-dd'),
        'fields': [self.selected_fields.item(i).text() 
                  for i in range(self.selected_fields.count())],
        'sources': [source for source, checkbox in self.data_source_checkboxes.items() 
                   if checkbox.isChecked()]
    }
    
    # إنشاء التقرير
    report_content = self.create_report_content(report_criteria)
    
    # حفظ بيانات التقرير
    self.current_report_data = {
        'criteria': report_criteria,
        'content': report_content,
        'generated_at': datetime.now().isoformat()
    }
    
    # تحديث المعاينة
    self.report_preview.setHtml(report_content)
    
    # الانتقال إلى تبويب المعاينة
    self.tabs.setCurrentIndex(2)
    
    QMessageBox.information(self, "نجح", "تم إنشاء التقرير بنجاح")

def create_report_content_function(self, criteria):
    """إنشاء محتوى التقرير"""
    # رأس التقرير
    company_name = getattr(self, 'company_name', None)
    company_text = company_name.text() if company_name else "شركة HGR للإدارة"
    
    report_title = getattr(self, 'report_title', None)
    title_text = report_title.text() if report_title and report_title.text() else criteria['name']
    
    html_content = f"""
    <html dir="rtl">
    <head>
        <meta charset="utf-8">
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
                direction: rtl;
                text-align: right;
            }}
            .header {{
                text-align: center;
                border-bottom: 2px solid #3498db;
                padding-bottom: 20px;
                margin-bottom: 30px;
            }}
            .company-name {{
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 10px;
            }}
            .report-title {{
                font-size: 20px;
                color: #3498db;
                margin-bottom: 10px;
            }}
            .report-info {{
                font-size: 14px;
                color: #7f8c8d;
            }}
            .content {{
                margin: 20px 0;
            }}
            .section {{
                margin-bottom: 30px;
            }}
            .section-title {{
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                border-bottom: 1px solid #bdc3c7;
                padding-bottom: 5px;
                margin-bottom: 15px;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin: 15px 0;
            }}
            th, td {{
                border: 1px solid #bdc3c7;
                padding: 8px;
                text-align: right;
            }}
            th {{
                background-color: #ecf0f1;
                font-weight: bold;
                color: #2c3e50;
            }}
            .summary {{
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                margin-top: 20px;
            }}
            .footer {{
                margin-top: 40px;
                text-align: center;
                font-size: 12px;
                color: #7f8c8d;
                border-top: 1px solid #bdc3c7;
                padding-top: 15px;
            }}
        </style>
    </head>
    <body>
        <div class="header">
            <div class="company-name">{company_text}</div>
            <div class="report-title">{title_text}</div>
            <div class="report-info">
                نوع التقرير: {criteria['type']}<br>
                الفترة: من {criteria['date_from']} إلى {criteria['date_to']}<br>
                تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}
            </div>
        </div>
        
        <div class="content">
    """
    
    # محتوى التقرير حسب النوع
    if criteria['type'] == "تقرير مبيعات":
        html_content += self.create_sales_report_content(criteria)
    elif criteria['type'] == "تقرير مخزون":
        html_content += self.create_inventory_report_content(criteria)
    elif criteria['type'] == "تقرير عملاء":
        html_content += self.create_customers_report_content(criteria)
    elif criteria['type'] == "تقرير مالي":
        html_content += self.create_financial_report_content(criteria)
    else:
        html_content += self.create_generic_report_content(criteria)
    
    # ذيل التقرير
    html_content += """
        </div>
        
        <div class="footer">
            تم إنشاء هذا التقرير بواسطة نظام HGR للإدارة<br>
            جميع الحقوق محفوظة © 2024
        </div>
    </body>
    </html>
    """
    
    return html_content

def create_sales_report_content_function(self, criteria):
    """إنشاء محتوى تقرير المبيعات"""
    content = """
    <div class="section">
        <div class="section-title">📊 تفاصيل المبيعات</div>
        <table>
            <thead>
                <tr>
                    <th>التاريخ</th>
                    <th>رقم الفاتورة</th>
                    <th>اسم العميل</th>
                    <th>المبلغ</th>
                    <th>طريقة الدفع</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>2024-01-15</td>
                    <td>INV-001</td>
                    <td>أحمد محمد الشامي</td>
                    <td>15,000 ريال</td>
                    <td>نقداً</td>
                </tr>
                <tr>
                    <td>2024-01-16</td>
                    <td>INV-002</td>
                    <td>فاطمة سالم أحمد</td>
                    <td>25,000 ريال</td>
                    <td>آجل</td>
                </tr>
                <tr>
                    <td>2024-01-17</td>
                    <td>INV-003</td>
                    <td>محمد عبدالله الحوثي</td>
                    <td>8,500 ريال</td>
                    <td>نقداً</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="summary">
        <div class="section-title">📈 ملخص المبيعات</div>
        <p><strong>إجمالي المبيعات:</strong> 48,500 ريال</p>
        <p><strong>عدد الفواتير:</strong> 3 فاتورة</p>
        <p><strong>متوسط قيمة الفاتورة:</strong> 16,167 ريال</p>
        <p><strong>المبيعات النقدية:</strong> 23,500 ريال (48.5%)</p>
        <p><strong>المبيعات الآجلة:</strong> 25,000 ريال (51.5%)</p>
    </div>
    """
    return content

def create_inventory_report_content_function(self, criteria):
    """إنشاء محتوى تقرير المخزون"""
    content = """
    <div class="section">
        <div class="section-title">📦 حالة المخزون</div>
        <table>
            <thead>
                <tr>
                    <th>اسم المنتج</th>
                    <th>الكمية المتاحة</th>
                    <th>السعر</th>
                    <th>القيمة الإجمالية</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>لابتوب ديل</td>
                    <td>15 جهاز</td>
                    <td>2,500 ريال</td>
                    <td>37,500 ريال</td>
                    <td>متوفر</td>
                </tr>
                <tr>
                    <td>طابعة HP</td>
                    <td>8 جهاز</td>
                    <td>1,200 ريال</td>
                    <td>9,600 ريال</td>
                    <td>متوفر</td>
                </tr>
                <tr>
                    <td>ماوس لاسلكي</td>
                    <td>2 جهاز</td>
                    <td>150 ريال</td>
                    <td>300 ريال</td>
                    <td>مخزون منخفض</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="summary">
        <div class="section-title">📊 ملخص المخزون</div>
        <p><strong>إجمالي قيمة المخزون:</strong> 47,400 ريال</p>
        <p><strong>عدد الأصناف:</strong> 3 أصناف</p>
        <p><strong>الأصناف المتوفرة:</strong> 2 صنف</p>
        <p><strong>الأصناف منخفضة المخزون:</strong> 1 صنف</p>
    </div>
    """
    return content

def create_customers_report_content_function(self, criteria):
    """إنشاء محتوى تقرير العملاء"""
    content = """
    <div class="section">
        <div class="section-title">👥 قائمة العملاء</div>
        <table>
            <thead>
                <tr>
                    <th>اسم العميل</th>
                    <th>الهاتف</th>
                    <th>المدينة</th>
                    <th>إجمالي المشتريات</th>
                    <th>آخر معاملة</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>أحمد محمد الشامي</td>
                    <td>777123456</td>
                    <td>صنعاء</td>
                    <td>15,000 ريال</td>
                    <td>2024-01-20</td>
                </tr>
                <tr>
                    <td>فاطمة سالم أحمد</td>
                    <td>733987654</td>
                    <td>عدن</td>
                    <td>25,000 ريال</td>
                    <td>2024-02-05</td>
                </tr>
                <tr>
                    <td>محمد عبدالله الحوثي</td>
                    <td>770555444</td>
                    <td>صعدة</td>
                    <td>5,000 ريال</td>
                    <td>2024-01-25</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="summary">
        <div class="section-title">📈 إحصائيات العملاء</div>
        <p><strong>إجمالي العملاء:</strong> 8 عميل</p>
        <p><strong>العملاء النشطون:</strong> 8 عميل</p>
        <p><strong>إجمالي المشتريات:</strong> 181,000 ريال</p>
        <p><strong>متوسط المشتريات للعميل:</strong> 22,625 ريال</p>
    </div>
    """
    return content

def create_financial_report_content_function(self, criteria):
    """إنشاء محتوى التقرير المالي"""
    content = """
    <div class="section">
        <div class="section-title">💰 الأرباح والخسائر</div>
        <table>
            <thead>
                <tr>
                    <th>البيان</th>
                    <th>المبلغ</th>
                    <th>النسبة</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>إجمالي الإيرادات</td>
                    <td>150,000 ريال</td>
                    <td>100%</td>
                </tr>
                <tr>
                    <td>تكلفة البضاعة المباعة</td>
                    <td>90,000 ريال</td>
                    <td>60%</td>
                </tr>
                <tr>
                    <td>إجمالي الربح</td>
                    <td>60,000 ريال</td>
                    <td>40%</td>
                </tr>
                <tr>
                    <td>المصروفات التشغيلية</td>
                    <td>25,000 ريال</td>
                    <td>16.7%</td>
                </tr>
                <tr>
                    <td>صافي الربح</td>
                    <td>35,000 ريال</td>
                    <td>23.3%</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="summary">
        <div class="section-title">📊 التحليل المالي</div>
        <p><strong>هامش الربح الإجمالي:</strong> 40%</p>
        <p><strong>هامش الربح الصافي:</strong> 23.3%</p>
        <p><strong>نسبة المصروفات:</strong> 16.7%</p>
        <p><strong>العائد على المبيعات:</strong> 23.3%</p>
    </div>
    """
    return content

def create_generic_report_content_function(self, criteria):
    """إنشاء محتوى تقرير عام"""
    content = f"""
    <div class="section">
        <div class="section-title">📋 تفاصيل التقرير</div>
        <p><strong>نوع التقرير:</strong> {criteria['type']}</p>
        <p><strong>الفترة:</strong> من {criteria['date_from']} إلى {criteria['date_to']}</p>
        <p><strong>مصادر البيانات:</strong> {', '.join(criteria['sources'])}</p>
        <p><strong>الحقول المحددة:</strong> {', '.join(criteria['fields'])}</p>
    </div>
    
    <div class="section">
        <div class="section-title">📊 بيانات نموذجية</div>
        <table>
            <thead>
                <tr>
    """
    
    # إضافة رؤوس الأعمدة
    for field in criteria['fields']:
        content += f"<th>{field}</th>"
    
    content += """
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>بيانات نموذجية 1</td>
                    <td>قيمة 1</td>
                    <td>معلومات 1</td>
                </tr>
                <tr>
                    <td>بيانات نموذجية 2</td>
                    <td>قيمة 2</td>
                    <td>معلومات 2</td>
                </tr>
            </tbody>
        </table>
    </div>
    
    <div class="summary">
        <div class="section-title">📈 ملخص</div>
        <p>هذا تقرير نموذجي تم إنشاؤه بناءً على المعايير المحددة.</p>
        <p>يمكن تخصيص المحتوى حسب نوع التقرير ومصادر البيانات.</p>
    </div>
    """
    
    return content
