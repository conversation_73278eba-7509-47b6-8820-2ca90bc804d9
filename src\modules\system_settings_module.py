#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إعدادات النظام
System Settings Module
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QLabel,
                             QComboBox, QMessageBox, QDialog, QFormLayout,
                             QTextEdit, QGroupBox, QGridLayout, QHeaderView,
                             QTabWidget, QSpinBox, QDoubleSpinBox, QCheckBox,
                             QDateEdit, QSplitter, QFrame, QDialogButtonBox,
                             QScrollArea)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor

try:
    from ..database.sqlite_manager import SQLiteManager
except ImportError:
    # في حالة التشغيل المباشر
    import sys
    import os
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    from database.sqlite_manager import SQLiteManager

def get_clear_label_style():
    """الحصول على نمط واضح للليبلات"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 100px;
            border: none;
        }
    """

def get_form_label_style():
    """الحصول على نمط ليبلات النماذج"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 12px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 8px;
            min-width: 150px;
            text-align: right;
        }
    """

def get_grid_label_style():
    """الحصول على نمط ليبلات الشبكة"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            font-weight: bold;
            color: #2c3e50;
            background-color: transparent;
            padding: 5px;
            min-width: 140px;
            max-width: 200px;
        }
    """

def get_value_label_style():
    """الحصول على نمط ليبلات القيم"""
    return """
        QLabel {
            font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
            font-size: 11px;
            color: #2c3e50;
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 3px;
            padding: 5px;
            min-width: 100px;
        }
    """



class SystemSettingsDialog(QDialog):
    """حوار تعديل إعداد النظام"""
    
    def __init__(self, db_manager: SQLiteManager, setting_data=None, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setting_data = setting_data
        
        self.setup_ui()
        self.setup_connections()
        
        if self.setting_data:
            self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تعديل إعداد النظام")
        self.setFixedSize(400, 300)
        self.setModal(True)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # النموذج
        form_layout = QFormLayout()
        
        # مفتاح الإعداد (للقراءة فقط)
        self.key_label = QLabel()
        self.key_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        form_layout.addRow("مفتاح الإعداد:", self.key_label)
        
        # الوصف (للقراءة فقط)
        self.description_label = QLabel()
        self.description_label.setWordWrap(True)
        form_layout.addRow("الوصف:", self.description_label)
        
        # القيمة (حسب النوع)
        self.value_widget = None
        self.value_layout = QVBoxLayout()
        form_layout.addRow("القيمة:", self.value_layout)
        
        main_layout.addLayout(form_layout)
        
        # أزرار الحوار
        button_box = QDialogButtonBox()
        self.save_btn = button_box.addButton("حفظ", QDialogButtonBox.AcceptRole)
        self.cancel_btn = button_box.addButton("إلغاء", QDialogButtonBox.RejectRole)
        
        main_layout.addWidget(button_box)
        
        self.setLayout(main_layout)
        
        # تطبيق التنسيق
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #2196F3;
            }
            QPushButton {
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-weight: bold;
                color: #333;
            
            
                padding: 5px;
            }
        """)
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.save_btn.clicked.connect(self.save_setting)
        self.cancel_btn.clicked.connect(self.reject)
    
    def load_data(self):
        """تحميل البيانات"""
        if not self.setting_data:
            return
        
        data = self.setting_data
        
        # عرض المفتاح والوصف
        self.key_label.setText(data.get('setting_key', ''))
        self.description_label.setText(data.get('description', ''))
        
        # إنشاء عنصر التحكم المناسب حسب نوع البيانات
        setting_type = data.get('setting_type', 'string')
        current_value = data.get('setting_value', '')
        
        # إزالة العنصر السابق إن وجد
        if self.value_widget:
            self.value_layout.removeWidget(self.value_widget)
            self.value_widget.deleteLater()
        
        if setting_type == 'boolean':
            self.value_widget = QCheckBox()
            self.value_widget.setChecked(current_value.lower() in ['1', 'true', 'yes'])
        elif setting_type == 'integer':
            self.value_widget = QSpinBox()
            self.value_widget.setRange(-999999, 999999)
            try:
                self.value_widget.setValue(int(current_value))
            except:
                self.value_widget.setValue(0)
        elif setting_type == 'float':
            self.value_widget = QDoubleSpinBox()
            self.value_widget.setRange(-999999.99, 999999.99)
            self.value_widget.setDecimals(2)
            try:
                self.value_widget.setValue(float(current_value))
            except:
                self.value_widget.setValue(0.0)
        elif data.get('setting_key') in ['default_currency', 'language', 'theme', 'backup_frequency']:
            # قوائم منسدلة للخيارات المحددة مسبقاً
            self.value_widget = QComboBox()
            if data.get('setting_key') == 'default_currency':
                self.value_widget.addItems(['SAR', 'USD', 'EUR', 'GBP'])
            elif data.get('setting_key') == 'language':
                self.value_widget.addItems(['ar', 'en'])
            elif data.get('setting_key') == 'theme':
                self.value_widget.addItems(['default', 'dark', 'light'])
            elif data.get('setting_key') == 'backup_frequency':
                self.value_widget.addItems(['daily', 'weekly', 'monthly'])
            
            # تعيين القيمة الحالية
            index = self.value_widget.findText(current_value)
            if index >= 0:
                self.value_widget.setCurrentIndex(index)
        else:
            # نص عادي
            if len(current_value) > 100:
                self.value_widget = QTextEdit()
                self.value_widget.setMaximumHeight(100)
                self.value_widget.setPlainText(current_value)
            else:
                self.value_widget = QLineEdit()
                self.value_widget.setText(current_value)
        
        self.value_layout.addWidget(self.value_widget)
    
    def save_setting(self):
        """حفظ الإعداد"""
        if not self.setting_data or not self.value_widget:
            return
        
        try:
            # الحصول على القيمة الجديدة
            new_value = self.get_widget_value()
            
            # تحديث قاعدة البيانات
            query = """
                UPDATE system_settings 
                SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """
            
            self.db_manager.execute_update(query, (new_value, self.setting_data['id']))
            
            QMessageBox.information(self, "نجح", "تم حفظ الإعداد بنجاح")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعداد:\n{str(e)}")
    
    def get_widget_value(self):
        """الحصول على قيمة عنصر التحكم"""
        if isinstance(self.value_widget, QCheckBox):
            return '1' if self.value_widget.isChecked() else '0'
        elif isinstance(self.value_widget, (QSpinBox, QDoubleSpinBox)):
            return str(self.value_widget.value())
        elif isinstance(self.value_widget, QComboBox):
            return self.value_widget.currentText()
        elif isinstance(self.value_widget, QTextEdit):
            return self.value_widget.toPlainText()
        elif isinstance(self.value_widget, QLineEdit):
            return self.value_widget.text()
        else:
            return ''


class SystemSettingsWidget(QWidget):
    """وحدة إعدادات النظام"""
    
    def __init__(self, db_manager: SQLiteManager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.setup_connections()
        self.load_settings()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # العنوان
        title = QLabel("⚙️ إعدادات النظام")
        title.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", sans-serif;
                
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            
            }
        """)
        main_layout.addWidget(title)
        
        # شريط الأدوات
        toolbar_layout = QHBoxLayout()
        
        self.edit_btn = QPushButton("✏️ تعديل")
        self.refresh_btn = QPushButton("🔄 تحديث")
        self.reset_btn = QPushButton("🔄 استعادة الافتراضي")
        self.export_btn = QPushButton("📤 تصدير")
        self.import_btn = QPushButton("📥 استيراد")
        
        # تنسيق الأزرار
        buttons = [self.edit_btn, self.refresh_btn, self.reset_btn, self.export_btn, self.import_btn]
        
        for btn in buttons:
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                    min-width: 100px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
                QPushButton:pressed {
                    background-color: #21618c;
                }
                QPushButton:disabled {
                    background-color: #bdc3c7;
                }
            """)
        
        # ألوان خاصة لبعض الأزرار
        self.reset_btn.setStyleSheet(self.reset_btn.styleSheet().replace("#3498db", "#e74c3c"))
        self.export_btn.setStyleSheet(self.export_btn.styleSheet().replace("#3498db", "#27ae60"))
        self.import_btn.setStyleSheet(self.import_btn.styleSheet().replace("#3498db", "#f39c12"))
        
        toolbar_layout.addWidget(self.edit_btn)
        toolbar_layout.addWidget(self.refresh_btn)
        toolbar_layout.addWidget(self.reset_btn)
        toolbar_layout.addWidget(self.export_btn)
        toolbar_layout.addWidget(self.import_btn)
        toolbar_layout.addStretch()
        
        main_layout.addLayout(toolbar_layout)
        
        # منطقة البحث والفلترة
        search_layout = QHBoxLayout()
        
        search_layout.addWidget(QLabel("البحث:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في الإعدادات...")
        search_layout.addWidget(self.search_edit)
        
        search_layout.addWidget(QLabel("الفئة:"))
        self.category_filter = QComboBox()
        self.category_filter.addItems(["الكل", "الشركة", "المحاسبة", "النظام", "الأمان", "الواجهة"])
        search_layout.addWidget(self.category_filter)
        
        main_layout.addLayout(search_layout)
        
        # إنشاء التبويبات للفئات
        self.tab_widget = QTabWidget()
        
        # تبويب إعدادات الشركة
        self.company_widget = self.create_category_widget('company')
        self.tab_widget.addTab(self.company_widget, "🏢 إعدادات الشركة")
        
        # تبويب إعدادات المحاسبة
        self.accounting_widget = self.create_category_widget('accounting')
        self.tab_widget.addTab(self.accounting_widget, "💰 إعدادات المحاسبة")
        
        # تبويب إعدادات النظام
        self.system_widget = self.create_category_widget('system')
        self.tab_widget.addTab(self.system_widget, "⚙️ إعدادات النظام")
        
        # تبويب إعدادات الأمان
        self.security_widget = self.create_category_widget('security')
        self.tab_widget.addTab(self.security_widget, "🔒 إعدادات الأمان")
        
        # تبويب إعدادات الواجهة
        self.ui_widget = self.create_category_widget('ui')
        self.tab_widget.addTab(self.ui_widget, "🎨 إعدادات الواجهة")
        
        main_layout.addWidget(self.tab_widget)
        
        # شريط الحالة
        status_layout = QHBoxLayout()
        self.status_label = QLabel("جاهز")
        self.settings_count_label = QLabel("عدد الإعدادات: 0")
        
        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.settings_count_label)
        
        main_layout.addLayout(status_layout)
        
        self.setLayout(main_layout)
        self.setStyleSheet(self.get_module_stylesheet())

    def create_category_widget(self, category):
        """إنشاء وحدة فرعية لفئة معينة"""
        widget = QWidget()
        layout = QVBoxLayout()

        # إنشاء جدول للفئة
        table = QTableWidget()
        columns = ["المفتاح", "الوصف", "القيمة", "النوع"]
        table.setColumnCount(len(columns))
        table.setHorizontalHeaderLabels(columns)

        # تنسيق الجدول
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSelectionMode(QTableWidget.SingleSelection)
        table.setSortingEnabled(True)

        # تعديل عرض الأعمدة
        header = table.horizontalHeader()
        header.resizeSection(0, 200)  # المفتاح
        header.resizeSection(1, 300)  # الوصف
        header.resizeSection(2, 200)  # القيمة
        header.resizeSection(3, 100)  # النوع
        header.setStretchLastSection(True)

        # ربط النقر المزدوج
        table.itemDoubleClicked.connect(self.edit_setting)

        layout.addWidget(table)
        widget.setLayout(layout)

        # حفظ مرجع للجدول
        setattr(self, f'{category}_table', table)

        return widget

    def setup_connections(self):
        """إعداد الاتصالات"""
        self.edit_btn.clicked.connect(self.edit_setting)
        self.refresh_btn.clicked.connect(self.load_settings)
        self.reset_btn.clicked.connect(self.reset_to_defaults)
        self.export_btn.clicked.connect(self.export_settings)
        self.import_btn.clicked.connect(self.import_settings)

        self.search_edit.textChanged.connect(self.filter_settings)
        self.category_filter.currentTextChanged.connect(self.filter_settings)

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            self.status_label.setText("جاري تحميل الإعدادات...")

            query = """
                SELECT id, setting_key, setting_value, setting_type, description, category, is_editable
                FROM system_settings
                ORDER BY category, setting_key
            """

            data = self.db_manager.execute_query(query)

            # تجميع البيانات حسب الفئة
            categories = {
                'company': [],
                'accounting': [],
                'system': [],
                'security': [],
                'ui': []
            }

            for setting in data:
                category = setting.get('category', 'system')
                if category in categories:
                    categories[category].append(setting)

            # ملء الجداول
            for category, settings in categories.items():
                self.populate_category_table(category, settings)

            self.settings_count_label.setText(f"عدد الإعدادات: {len(data)}")
            self.status_label.setText("تم تحميل الإعدادات بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل الإعدادات:\n{str(e)}")
            self.status_label.setText("خطأ في تحميل الإعدادات")

    def populate_category_table(self, category, settings):
        """ملء جدول فئة معينة"""
        table = getattr(self, f'{category}_table', None)
        if not table:
            return

        table.setRowCount(len(settings))

        for row, setting in enumerate(settings):
            # المفتاح
            key_item = QTableWidgetItem(setting.get('setting_key', ''))
            key_item.setData(Qt.UserRole, setting.get('id'))
            table.setItem(row, 0, key_item)

            # الوصف
            table.setItem(row, 1, QTableWidgetItem(setting.get('description', '')))

            # القيمة
            value = setting.get('setting_value', '')
            if setting.get('setting_type') == 'boolean':
                value = 'نعم' if value.lower() in ['1', 'true', 'yes'] else 'لا'
            table.setItem(row, 2, QTableWidgetItem(str(value)))

            # النوع
            type_mapping = {
                'string': 'نص',
                'integer': 'رقم صحيح',
                'float': 'رقم عشري',
                'boolean': 'منطقي'
            }
            type_text = type_mapping.get(setting.get('setting_type', 'string'), 'نص')
            table.setItem(row, 3, QTableWidgetItem(type_text))

            # تلوين الصفوف غير القابلة للتعديل
            if not setting.get('is_editable', True):
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    if item:
                        item.setBackground(QColor("#f8f9fa"))

    def filter_settings(self):
        """فلترة الإعدادات"""
        search_text = self.search_edit.text().lower()
        category_filter = self.category_filter.currentText()

        # تطبيق الفلترة على كل تبويب
        categories = ['company', 'accounting', 'system', 'security', 'ui']

        for category in categories:
            table = getattr(self, f'{category}_table', None)
            if not table:
                continue

            # إخفاء/إظهار التبويب حسب الفلتر
            tab_index = self.get_tab_index_by_category(category)
            if tab_index >= 0:
                if category_filter != "الكل":
                    category_mapping = {
                        "الشركة": "company",
                        "المحاسبة": "accounting",
                        "النظام": "system",
                        "الأمان": "security",
                        "الواجهة": "ui"
                    }
                    if category_mapping.get(category_filter) != category:
                        self.tab_widget.setTabVisible(tab_index, False)
                        continue
                    else:
                        self.tab_widget.setTabVisible(tab_index, True)
                else:
                    self.tab_widget.setTabVisible(tab_index, True)

            # فلترة الصفوف داخل الجدول
            for row in range(table.rowCount()):
                show_row = True

                if search_text:
                    row_text = ""
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        if item:
                            row_text += item.text().lower() + " "

                    if search_text not in row_text:
                        show_row = False

                table.setRowHidden(row, not show_row)

    def get_tab_index_by_category(self, category):
        """الحصول على فهرس التبويب حسب الفئة"""
        category_mapping = {
            'company': 0,
            'accounting': 1,
            'system': 2,
            'security': 3,
            'ui': 4
        }
        return category_mapping.get(category, -1)

    def edit_setting(self):
        """تعديل الإعداد المحدد"""
        # الحصول على الجدول النشط
        current_tab = self.tab_widget.currentIndex()
        categories = ['company', 'accounting', 'system', 'security', 'ui']

        if current_tab < 0 or current_tab >= len(categories):
            return

        category = categories[current_tab]
        table = getattr(self, f'{category}_table', None)

        if not table:
            return

        current_row = table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار إعداد للتعديل")
            return

        try:
            # الحصول على معرف الإعداد
            setting_id = table.item(current_row, 0).data(Qt.UserRole)

            # استعلام البيانات الكاملة
            query = "SELECT * FROM system_settings WHERE id = ?"
            result = self.db_manager.execute_query(query, (setting_id,))

            if result:
                setting_data = result[0]

                # التحقق من إمكانية التعديل
                if not setting_data.get('is_editable', True):
                    QMessageBox.warning(self, "تحذير", "هذا الإعداد غير قابل للتعديل")
                    return

                dialog = SystemSettingsDialog(self.db_manager, setting_data, self)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_settings()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل بيانات الإعداد:\n{str(e)}")

    def reset_to_defaults(self):
        """استعادة الإعدادات الافتراضية"""
        reply = QMessageBox.question(
            self, 'تأكيد الاستعادة',
            'هل أنت متأكد من استعادة جميع الإعدادات إلى القيم الافتراضية؟\n\nسيتم فقدان جميع التخصيصات الحالية.',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # حذف جميع الإعدادات الحالية
                self.db_manager.execute_update("DELETE FROM system_settings")

                # إعادة إنشاء الإعدادات الافتراضية
                # (سيتم تشغيلها تلقائياً عند الاتصال التالي بقاعدة البيانات)

                QMessageBox.information(self, "نجح", "تم استعادة الإعدادات الافتراضية بنجاح")
                self.load_settings()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في استعادة الإعدادات:\n{str(e)}")

    def export_settings(self):
        """تصدير الإعدادات"""
        QMessageBox.information(self, "تصدير الإعدادات", "وظيفة تصدير الإعدادات قيد التطوير")

    def import_settings(self):
        """استيراد الإعدادات"""
        QMessageBox.information(self, "استيراد الإعدادات", "وظيفة استيراد الإعدادات قيد التطوير")

    def get_module_stylesheet(self):
        """تنسيق الوحدة"""
        return """
            QWidget {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QLineEdit, QComboBox {
                padding: 6px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #007bff;
                outline: none;
            }
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
            QHeaderView::section {
                background-color: #e9ecef;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 10px 15px;
                margin-right: 2px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #3498db;
                color: #2c3e50;
            }
            QTabBar::tab:hover {
                background-color: #f0f0f0;
                color: #2980b9;
            }
        """
