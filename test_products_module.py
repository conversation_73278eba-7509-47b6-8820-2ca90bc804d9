#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوحدة إدارة المنتجات والخدمات
Comprehensive test for products module
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_products_module():
    """اختبار وحدة إدارة المنتجات"""
    print("🧪 اختبار وحدة إدارة المنتجات والخدمات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.products_module import ProductsWidget, ProductDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار إنشاء وحدة إدارة المنتجات
        products_widget = ProductsWidget(db_manager)
        print("✅ تم إنشاء وحدة إدارة المنتجات")
        
        # اختبار تحميل المنتجات
        products_widget.load_products()
        print("✅ تم تحميل المنتجات من قاعدة البيانات")
        
        # اختبار عدد المنتجات المحملة
        product_count = products_widget.products_table.rowCount()
        print(f"📊 عدد المنتجات المحملة: {product_count}")
        
        # اختبار البحث والفلترة
        products_widget.search_edit.setText("جهاز")
        products_widget.filter_products()
        print("✅ تم اختبار وظيفة البحث")
        
        # اختبار فلترة النوع
        products_widget.type_filter.setCurrentText("منتجات")
        products_widget.filter_products()
        print("✅ تم اختبار فلترة النوع")
        
        # اختبار فلترة الفئة
        products_widget.category_filter.setCurrentText("إلكترونيات")
        products_widget.filter_products()
        print("✅ تم اختبار فلترة الفئة")
        
        # اختبار مسح الفلاتر
        products_widget.search_edit.clear()
        products_widget.type_filter.setCurrentText("الكل")
        products_widget.category_filter.setCurrentText("الكل")
        products_widget.status_filter.setCurrentText("الكل")
        products_widget.filter_products()
        print("✅ تم اختبار مسح الفلاتر")
        
        # تنظيف
        products_widget.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وحدة إدارة المنتجات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_product_dialog():
    """اختبار حوار إدارة المنتجات"""
    print("\n🧪 اختبار حوار إدارة المنتجات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.modules.products_module import ProductDialog
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار حوار إضافة منتج جديد
        dialog = ProductDialog(db_manager)
        print("✅ تم إنشاء حوار إضافة منتج")
        
        # اختبار ملء البيانات
        dialog.product_name_edit.setText("منتج تجريبي")
        dialog.product_type_combo.setCurrentIndex(0)  # منتج
        dialog.category_combo.setCurrentText("إلكترونيات")
        dialog.unit_combo.setCurrentText("قطعة")
        dialog.cost_price_spin.setValue(100.0)
        dialog.selling_price_spin.setValue(150.0)
        dialog.tax_rate_spin.setValue(15.0)
        dialog.stock_quantity_spin.setValue(50)
        dialog.min_stock_spin.setValue(10)
        dialog.max_stock_spin.setValue(100)
        print("✅ تم ملء البيانات الأساسية")
        
        # اختبار التحقق من صحة البيانات
        if dialog.validate_data():
            print("✅ البيانات صحيحة")
        else:
            print("❌ البيانات غير صحيحة")
        
        # اختبار إنشاء كود المنتج
        product_code = dialog.generate_product_code()
        print(f"✅ تم إنشاء كود المنتج: {product_code}")
        
        # اختبار حساب هامش الربح
        dialog.calculate_profit_margin()
        profit_margin = dialog.profit_margin_label.text()
        print(f"✅ تم حساب هامش الربح: {profit_margin}")
        
        # اختبار تغيير نوع المنتج
        dialog.product_type_combo.setCurrentIndex(1)  # خدمة
        dialog.on_product_type_changed()
        print("✅ تم اختبار تغيير نوع المنتج إلى خدمة")
        
        # اختبار العودة للمنتج
        dialog.product_type_combo.setCurrentIndex(0)  # منتج
        dialog.on_product_type_changed()
        print("✅ تم اختبار العودة لنوع المنتج")
        
        # تنظيف
        dialog.close()
        db_manager.disconnect()
        app.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حوار إدارة المنتجات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_products_database_operations():
    """اختبار عمليات قاعدة البيانات للمنتجات"""
    print("\n🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار استعلام المنتجات
        query = """
            SELECT id, product_code, product_name, product_type, category, unit,
                   cost_price, selling_price, stock_quantity, min_stock_level, is_active
            FROM products 
            ORDER BY product_name
        """
        
        products = db_manager.execute_query(query)
        print(f"✅ تم استعلام {len(products)} منتج من قاعدة البيانات")
        
        # اختبار استعلام المنتجات النشطة
        active_query = """
            SELECT COUNT(*) as count FROM products WHERE is_active = 1
        """
        active_result = db_manager.execute_query(active_query)
        active_count = active_result[0]['count'] if active_result else 0
        print(f"✅ المنتجات النشطة: {active_count}")
        
        # اختبار استعلام أنواع المنتجات
        types_query = """
            SELECT product_type, COUNT(*) as count FROM products 
            GROUP BY product_type
            ORDER BY count DESC
        """
        types_result = db_manager.execute_query(types_query)
        print(f"📊 توزيع أنواع المنتجات:")
        for type_data in types_result:
            type_name = {'product': 'منتجات', 'service': 'خدمات'}.get(
                type_data['product_type'], type_data['product_type']
            )
            print(f"   {type_name}: {type_data['count']}")
        
        # اختبار حساب إجمالي قيمة المخزون
        value_query = """
            SELECT SUM(stock_quantity * cost_price) as total_value 
            FROM products 
            WHERE product_type = 'product' AND is_active = 1
        """
        value_result = db_manager.execute_query(value_query)
        total_value = value_result[0]['total_value'] if value_result else 0
        print(f"💰 إجمالي قيمة المخزون: {total_value:,.2f} ريال")
        
        # اختبار البحث في المنتجات
        search_query = """
            SELECT COUNT(*) as count FROM products 
            WHERE product_name LIKE ? OR product_code LIKE ? OR category LIKE ?
        """
        search_result = db_manager.execute_query(search_query, ('%جهاز%', '%PROD%', '%إلكترونيات%'))
        search_count = search_result[0]['count'] if search_result else 0
        print(f"🔍 نتائج البحث: {search_count}")
        
        # اختبار المنتجات حسب الفئات
        categories_query = """
            SELECT category, COUNT(*) as count FROM products 
            WHERE category IS NOT NULL AND category != ''
            GROUP BY category
            ORDER BY count DESC
            LIMIT 5
        """
        categories_result = db_manager.execute_query(categories_query)
        print(f"🏷️ أهم الفئات:")
        for category_data in categories_result:
            print(f"   {category_data['category']}: {category_data['count']}")
        
        # اختبار المخزون المنخفض
        low_stock_query = """
            SELECT COUNT(*) as count FROM products 
            WHERE product_type = 'product' AND stock_quantity <= min_stock_level AND min_stock_level > 0
        """
        low_stock_result = db_manager.execute_query(low_stock_query)
        low_stock_count = low_stock_result[0]['count'] if low_stock_result else 0
        print(f"⚠️ منتجات بمخزون منخفض: {low_stock_count}")
        
        # اختبار متوسط الأسعار
        avg_prices_query = """
            SELECT 
                AVG(cost_price) as avg_cost,
                AVG(selling_price) as avg_selling,
                AVG((selling_price - cost_price) / cost_price * 100) as avg_margin
            FROM products 
            WHERE cost_price > 0 AND selling_price > 0
        """
        avg_result = db_manager.execute_query(avg_prices_query)
        if avg_result:
            avg_data = avg_result[0]
            print(f"📈 متوسط سعر التكلفة: {avg_data['avg_cost']:,.2f} ريال")
            print(f"📈 متوسط سعر البيع: {avg_data['avg_selling']:,.2f} ريال")
            print(f"📈 متوسط هامش الربح: {avg_data['avg_margin']:,.2f}%")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار عمليات قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_structure():
    """اختبار هيكل قاعدة البيانات للمنتجات"""
    print("\n🧪 اختبار هيكل قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار وجود جدول products
        table_query = "SELECT name FROM sqlite_master WHERE type='table' AND name='products'"
        table_result = db_manager.execute_query(table_query)
        
        if table_result:
            print("✅ جدول products موجود")
        else:
            print("❌ جدول products مفقود")
            return False
        
        # اختبار أعمدة الجدول
        columns_query = "PRAGMA table_info(products)"
        columns = db_manager.execute_query(columns_query)
        column_names = [col['name'] for col in columns]
        
        required_columns = [
            'id', 'product_code', 'product_name', 'product_type', 'category', 'unit',
            'cost_price', 'selling_price', 'tax_rate', 'stock_quantity', 'min_stock_level',
            'max_stock_level', 'barcode', 'description', 'is_active', 'created_at', 'updated_at'
        ]
        
        print("\n📋 أعمدة جدول products:")
        for col in required_columns:
            if col in column_names:
                print(f"   ✅ {col}")
            else:
                print(f"   ❌ {col} مفقود")
        
        # عرض جميع الأعمدة الموجودة
        print(f"\n📊 إجمالي الأعمدة الموجودة: {len(column_names)}")
        print(f"الأعمدة: {', '.join(column_names)}")
        
        # تنظيف
        db_manager.disconnect()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار هيكل قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار شامل لوحدة إدارة المنتجات والخدمات")
    print("   Comprehensive test for products module")
    print("=" * 60)
    
    # اختبار هيكل قاعدة البيانات
    structure_test = test_database_structure()
    
    # اختبار وحدة إدارة المنتجات
    module_test = test_products_module()
    
    # اختبار حوار إدارة المنتجات
    dialog_test = test_product_dialog()
    
    # اختبار عمليات قاعدة البيانات
    database_test = test_products_database_operations()
    
    print("\n" + "=" * 60)
    print("📊 نتائج اختبار وحدة إدارة المنتجات:")
    print(f"   🗄️ هيكل قاعدة البيانات: {'✅ نجح' if structure_test else '❌ فشل'}")
    print(f"   🏗️ اختبار الوحدة: {'✅ نجح' if module_test else '❌ فشل'}")
    print(f"   💬 اختبار الحوار: {'✅ نجح' if dialog_test else '❌ فشل'}")
    print(f"   🗄️ اختبار قاعدة البيانات: {'✅ نجح' if database_test else '❌ فشل'}")
    
    if structure_test and module_test and dialog_test and database_test:
        print("\n🎉 جميع اختبارات وحدة إدارة المنتجات نجحت!")
        print("✅ وحدة إدارة المنتجات والخدمات جاهزة للاستخدام")
        
        print("\n📋 الوظائف المتاحة:")
        print("   📦 إدارة المنتجات والخدمات (إضافة، تعديل، حذف)")
        print("   🔍 البحث والفلترة المتقدمة")
        print("   📊 عرض إحصائيات المنتجات")
        print("   💰 إدارة الأسعار وهامش الربح")
        print("   🏷️ تصنيف المنتجات في فئات")
        print("   📦 إدارة المخزون والحدود")
        print("   🔢 نظام الباركود")
        print("   ⚠️ تنبيهات المخزون المنخفض")
        print("   📤 تصدير واستيراد المنتجات")
    else:
        print("\n⚠️ بعض اختبارات وحدة إدارة المنتجات فشلت")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)
    
    return 0 if (structure_test and module_test and dialog_test and database_test) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
