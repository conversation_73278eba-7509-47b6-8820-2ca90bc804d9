#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لوظائف إدارة النظام المكتملة
Complete System Management Functions Test
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_closures_audits_module():
    """اختبار وحدة الإغلاقات والتوثيقات"""
    print("🔒 اختبار وحدة الإغلاقات والتوثيقات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.closures_audits_module import ClosuresAuditsWidget, ClosureDialog, AuditReportDialog
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            def execute_query(self, query, params=None):
                return []
            def execute_update(self, query, params=None):
                return True
        
        db_manager = MockDBManager()
        
        # اختبار الوحدة الرئيسية
        widget = ClosuresAuditsWidget(db_manager)
        print("   ✅ تم إنشاء وحدة الإغلاقات والتوثيقات بنجاح")
        
        # اختبار حوار الإغلاق
        closure_dialog = ClosureDialog(db_manager)
        print("   ✅ تم إنشاء حوار الإغلاق بنجاح")
        
        # اختبار حوار تقرير التدقيق
        audit_dialog = AuditReportDialog(db_manager)
        print("   ✅ تم إنشاء حوار تقرير التدقيق بنجاح")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار وحدة الإغلاقات والتوثيقات: {e}")
        return False

def test_login_interface_control_module():
    """اختبار وحدة التحكم بواجهات الدخول"""
    print("\n🔐 اختبار وحدة التحكم بواجهات الدخول...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.login_interface_control_module import LoginInterfaceControlWidget, LoginSessionDialog
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            def execute_query(self, query, params=None):
                if "users" in query:
                    return [(1, "admin", "المدير"), (2, "user1", "مستخدم 1")]
                return []
            def execute_update(self, query, params=None):
                return True
        
        db_manager = MockDBManager()
        
        # اختبار الوحدة الرئيسية
        widget = LoginInterfaceControlWidget(db_manager)
        print("   ✅ تم إنشاء وحدة التحكم بواجهات الدخول بنجاح")
        
        # اختبار حوار إدارة الجلسة
        session_dialog = LoginSessionDialog(db_manager)
        print("   ✅ تم إنشاء حوار إدارة الجلسة بنجاح")
        
        # فحص التبويبات
        tab_count = widget.tabs.count()
        print(f"   ✅ عدد التبويبات: {tab_count}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار وحدة التحكم بواجهات الدخول: {e}")
        return False

def test_activity_tracking_module():
    """اختبار وحدة تتبع النشاطات"""
    print("\n📋 اختبار وحدة تتبع النشاطات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.activity_tracking_module import ActivityTrackingWidget, ActivityLogger, ActivityFilterDialog
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            def execute_query(self, query, params=None):
                if "activity_log" in query and "COUNT" in query:
                    return [(100,)]
                elif "activity_log" in query:
                    return [
                        ("2024-01-15 10:00:00", "admin", "تسجيل دخول", "المصادقة", 
                         "تسجيل دخول ناجح", "INFO", "192.168.1.100", True, "")
                    ]
                return []
            def execute_update(self, query, params=None):
                return True
        
        db_manager = MockDBManager()
        
        # اختبار مسجل النشاطات
        logger = ActivityLogger(db_manager)
        print("   ✅ تم إنشاء مسجل النشاطات بنجاح")
        
        # اختبار تسجيل نشاط
        logger.log_activity("تسجيل دخول", "المصادقة", "تسجيل دخول ناجح", username="admin")
        print("   ✅ تم تسجيل نشاط تجريبي بنجاح")
        
        # اختبار الوحدة الرئيسية
        widget = ActivityTrackingWidget(db_manager)
        print("   ✅ تم إنشاء وحدة تتبع النشاطات بنجاح")
        
        # اختبار حوار الفلترة
        filter_dialog = ActivityFilterDialog()
        print("   ✅ تم إنشاء حوار الفلترة بنجاح")
        
        # فحص التبويبات
        tab_count = widget.tabs.count()
        print(f"   ✅ عدد التبويبات: {tab_count}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار وحدة تتبع النشاطات: {e}")
        return False

def test_data_synchronization_module():
    """اختبار وحدة مزامنة البيانات"""
    print("\n🔄 اختبار وحدة مزامنة البيانات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        from modules.data_synchronization_module import DataSynchronizationWidget, BranchConfigDialog, SyncProgressDialog
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            def execute_query(self, query, params=None):
                if "branches" in query:
                    return [
                        (1, "الفرع الرئيسي", "MAIN", "الرياض", "أحمد محمد", 
                         "192.168.1.100", True, "2024-01-15 10:00:00", True)
                    ]
                return []
            def execute_update(self, query, params=None):
                return True
        
        db_manager = MockDBManager()
        
        # اختبار الوحدة الرئيسية
        widget = DataSynchronizationWidget(db_manager)
        print("   ✅ تم إنشاء وحدة مزامنة البيانات بنجاح")
        
        # اختبار حوار إعداد الفرع
        branch_dialog = BranchConfigDialog(db_manager)
        print("   ✅ تم إنشاء حوار إعداد الفرع بنجاح")
        
        # اختبار حوار تقدم المزامنة
        progress_dialog = SyncProgressDialog()
        print("   ✅ تم إنشاء حوار تقدم المزامنة بنجاح")
        
        # فحص التبويبات
        tab_count = widget.tabs.count()
        print(f"   ✅ عدد التبويبات: {tab_count}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار وحدة مزامنة البيانات: {e}")
        return False

def test_main_system_integration():
    """اختبار التكامل مع النظام الرئيسي"""
    print("\n🔗 اختبار التكامل مع النظام الرئيسي...")
    
    try:
        # فحص وجود الدوال في النظام الرئيسي
        import main_system
        
        # إنشاء نسخة من النظام الرئيسي
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
            app.setLayoutDirection(Qt.RightToLeft)
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            def execute_query(self, query, params=None):
                return []
            def execute_update(self, query, params=None):
                return True
        
        # لا نقوم بإنشاء النظام الرئيسي فعلياً لتجنب فتح النافذة
        # بدلاً من ذلك نفحص وجود الدوال المطلوبة
        
        required_functions = [
            'open_closures_and_audits',
            'open_login_interface_control', 
            'open_activity_tracking',
            'open_data_synchronization'
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if hasattr(main_system.AccountingMainWindow, func_name):
                print(f"   ✅ {func_name}")
            else:
                missing_functions.append(func_name)
                print(f"   ❌ {func_name}")
        
        if missing_functions:
            print(f"   ⚠️ الدوال المفقودة: {len(missing_functions)}")
            return False
        else:
            print(f"   🎉 جميع الدوال موجودة: {len(required_functions)}")
            return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التكامل: {e}")
        return False

def test_existing_system_management():
    """اختبار وحدات إدارة النظام الموجودة"""
    print("\n🧪 اختبار وحدات إدارة النظام الموجودة...")
    
    existing_modules = [
        ("إدارة المستخدمين", "modules.system_management_module", "SystemManagementModule"),
        ("النسخ الاحتياطي", "modules.backup_module", "BackupWidget"),
        ("إدارة قاعدة البيانات", "modules.database_management_module", "DatabaseManagementWidget"),
        ("الترقيم التلقائي", "modules.auto_numbering_module", "AutoNumberingWidget"),
        ("إعدادات النظام", "modules.system_settings_module", "SystemSettingsWidget")
    ]
    
    working_modules = 0
    total_modules = len(existing_modules)
    
    for module_name, module_path, class_name in existing_modules:
        try:
            module = __import__(module_path, fromlist=[class_name])
            module_class = getattr(module, class_name)
            print(f"   ✅ {module_name}")
            working_modules += 1
        except Exception as e:
            print(f"   ❌ {module_name}: {e}")
    
    print(f"   📊 الوحدات العاملة: {working_modules}/{total_modules}")
    return working_modules == total_modules

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار شامل لوظائف إدارة النظام المكتملة")
    print("=" * 70)
    
    tests = [
        ("وحدة الإغلاقات والتوثيقات", test_closures_audits_module),
        ("وحدة التحكم بواجهات الدخول", test_login_interface_control_module),
        ("وحدة تتبع النشاطات", test_activity_tracking_module),
        ("وحدة مزامنة البيانات", test_data_synchronization_module),
        ("التكامل مع النظام الرئيسي", test_main_system_integration),
        ("وحدات إدارة النظام الموجودة", test_existing_system_management),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 50)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    print("\n" + "=" * 70)
    print("📊 ملخص النتائج:")
    print(f"   ✅ الاختبارات الناجحة: {passed_tests}/{total_tests}")
    print(f"   📈 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 تم إكمال تطوير وظائف إدارة النظام بنجاح!")
        print("✅ جميع الوحدات تعمل بدون مشاكل")
        
        print("\n🛠️ وظائف إدارة النظام المكتملة:")
        print("   ✅ 👥 إدارة المستخدمين - مكتمل")
        print("   ✅ 🔐 صلاحيات المستخدمين - مكتمل")
        print("   ✅ 🔒 خصوصية المستخدم - مكتمل")
        print("   ✅ 🔒 الإغلاقات والتوثيقات - مكتمل حديثاً")
        print("   ✅ 💾 النسخ الاحتياطي - مكتمل")
        print("   ✅ 🗄️ إدارة قاعدة البيانات - مكتمل")
        print("   ✅ 🔐 التحكم بواجهات الدخول - مكتمل حديثاً")
        print("   ✅ 📋 تتبع النشاطات (سجل الأحداث) - مكتمل حديثاً")
        print("   ✅ 📄 الترقيم التلقائي للمستندات - مكتمل")
        print("   ✅ ⚙️ تهيئة إعدادات النظام التقنية - مكتمل")
        print("   ✅ 🔄 مزامنة البيانات بين الفروع - مكتمل حديثاً")
        
        print("\n🆕 الوظائف الجديدة المضافة:")
        print("   🔒 الإغلاقات والتوثيقات:")
        print("      - إغلاق الفترات المحاسبية")
        print("      - تقارير التدقيق والمراجعة")
        print("      - النسخ الاحتياطي قبل الإغلاق")
        print("      - التحقق من صحة البيانات")
        
        print("   🔐 التحكم بواجهات الدخول:")
        print("      - إدارة الجلسات النشطة")
        print("      - إعدادات الأمان المتقدمة")
        print("      - سجل محاولات الدخول")
        print("      - التحكم في أوقات الوصول")
        
        print("   📋 تتبع النشاطات:")
        print("      - سجل شامل لجميع النشاطات")
        print("      - فلترة وبحث متقدم")
        print("      - إحصائيات مفصلة")
        print("      - تنبيهات الأمان")
        
        print("   🔄 مزامنة البيانات:")
        print("      - إدارة الفروع المتعددة")
        print("      - مزامنة تلقائية ويدوية")
        print("      - حل تعارضات البيانات")
        print("      - مراقبة حالة المزامنة")
        
        print("\n🚀 للوصول لوظائف إدارة النظام:")
        print("   1. شغل النظام: python src/main_system.py")
        print("   2. انقر على 'إدارة النظام' في الشجرة الجانبية")
        print("   3. اختر الوظيفة المطلوبة من القائمة")
        
        print("\n🎯 الميزات الجديدة:")
        print("   🔒 إدارة شاملة للإغلاقات المحاسبية")
        print("   🔐 تحكم متقدم في أمان النظام")
        print("   📋 تتبع مفصل لجميع النشاطات")
        print("   🔄 مزامنة احترافية بين الفروع")
        print("   📊 تقارير وإحصائيات متقدمة")
        print("   🛡️ أمان عالي المستوى")
        
    else:
        print("\n⚠️ بعض الوحدات تحتاج إلى مراجعة")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
    
    return 0 if passed_tests == total_tests else 1

if __name__ == "__main__":
    sys.exit(main())
