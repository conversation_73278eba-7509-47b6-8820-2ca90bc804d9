#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
النظام الرئيسي - الواجهة الشاملة
Main System Interface
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QHBoxLayout, 
                             QVBoxLayout, QTreeWidget, QTreeWidgetItem, QStackedWidget,
                             QLabel, QPushButton, QFrame, QScrollArea, QSplitter,
                             QToolBar, QAction, QMenuBar, QStatusBar, QMessageBox)
from PyQt5.QtCore import Qt, QSize, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QPalette, QColor

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.database.sqlite_manager import SQLiteManager


class MainSystemWindow(QMainWindow):
    """النافذة الرئيسية للنظام"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = SQLiteManager()
        self.current_user = "ahmed_user"
        
        self.setup_ui()
        self.setup_menu_data()
        self.setup_connections()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("نظام إدارة الأعمال المتكامل")
        self.setGeometry(100, 100, 1400, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # تطبيق تنسيق عام
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
                font-family: 'Cairo', 'Tahoma', sans-serif;
            }
        """)
        
        # إنشاء شريط القوائم
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء الواجهة الرئيسية
        self.create_main_interface()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu('ملف')
        file_menu.addAction('جديد', self.new_file)
        file_menu.addAction('فتح', self.open_file)
        file_menu.addSeparator()
        file_menu.addAction('خروج', self.close)
        
        # قائمة عرض
        view_menu = menubar.addMenu('عرض')
        view_menu.addAction('ملء الشاشة', self.toggle_fullscreen)
        view_menu.addAction('إخفاء القائمة الجانبية', self.toggle_sidebar)
        
        # قائمة مساعدة
        help_menu = menubar.addMenu('مساعدة')
        help_menu.addAction('حول البرنامج', self.show_about)
        help_menu.addAction('دليل المستخدم', self.show_help)
        
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = QToolBar()
        toolbar.setMovable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # أزرار سريعة
        toolbar.addAction('🏠 الرئيسية', self.go_home)
        toolbar.addSeparator()
        toolbar.addAction('📄 فاتورة مبيعات', self.open_sales_invoice)
        toolbar.addAction('📦 إدارة المخزون', self.open_inventory)
        toolbar.addAction('👥 العملاء', self.open_customers)
        toolbar.addSeparator()
        toolbar.addAction('📊 التقارير', self.open_reports)
        toolbar.addAction('⚙️ الإعدادات', self.open_settings)
        
        self.addToolBar(toolbar)
        
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # إنشاء القائمة الجانبية
        self.sidebar = self.create_sidebar()
        
        # إنشاء منطقة المحتوى
        self.content_area = self.create_content_area()
        
        # إضافة العناصر للتخطيط (القائمة في اليمين، المحتوى في اليسار)
        main_layout.addWidget(self.sidebar, 1)       # القائمة الجانبية في اليمين
        main_layout.addWidget(self.content_area, 2)  # المحتوى في اليسار
        
        central_widget.setLayout(main_layout)
        
    def create_sidebar(self):
        """إنشاء شجرة النظام الجانبية"""
        sidebar_frame = QFrame()
        sidebar_frame.setFixedWidth(400)  # زيادة العرض لاستيعاب النصوص الطويلة
        sidebar_frame.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                border-radius: 10px;
                margin: 5px;
                border: 2px solid #34495e;
            }
        """)

        sidebar_layout = QVBoxLayout()
        sidebar_layout.setContentsMargins(10, 10, 10, 10)

        # عنوان شجرة النظام
        title_label = QLabel("🌳 شجرة النظام - يمن سوفت")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
                padding: 12px;
                background-color: #e74c3c;
                border-radius: 8px;
                margin-bottom: 10px;
                border: 2px solid #c0392b;
                text-align: center;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)

        # شجرة القوائم المحسنة
        self.menu_tree = QTreeWidget()
        self.menu_tree.setHeaderHidden(True)
        self.menu_tree.setRootIsDecorated(True)  # إظهار أيقونات التوسع
        self.menu_tree.setAnimated(True)  # تحريك التوسع والانهيار
        self.menu_tree.setIndentation(20)  # مسافة المسافة البادئة

        self.menu_tree.setStyleSheet("""
            QTreeWidget {
                background-color: #34495e;
                color: white;
                border: 2px solid #2c3e50;
                border-radius: 8px;
                font-size: 13px;
                outline: none;
                selection-background-color: #e74c3c;
            }
            QTreeWidget::item {
                padding: 6px 8px;
                border-bottom: 1px solid #2c3e50;
                min-height: 25px;
            }
            QTreeWidget::item:hover {
                background-color: #3498db;
                color: white;
            }
            QTreeWidget::item:selected {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
            }
            QTreeWidget::item:selected:hover {
                background-color: #c0392b;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed,
            QTreeWidget::branch:closed:has-children:has-siblings {
                border-image: none;
                image: none;
            }
            QTreeWidget::branch:open:has-children:!has-siblings,
            QTreeWidget::branch:open:has-children:has-siblings {
                border-image: none;
                image: none;
            }
            QTreeWidget::branch:has-children:!has-siblings:closed:hover,
            QTreeWidget::branch:closed:has-children:has-siblings:hover {
                background-color: #3498db;
            }
        """)

        # إضافة شريط تمرير مخصص
        scroll_area = QScrollArea()
        scroll_area.setWidget(self.menu_tree)
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #2c3e50;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #3498db;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #2980b9;
            }
        """)

        sidebar_layout.addWidget(title_label)
        sidebar_layout.addWidget(self.menu_tree)

        sidebar_frame.setLayout(sidebar_layout)
        return sidebar_frame
        
    def create_content_area(self):
        """إنشاء منطقة المحتوى (في الجهة اليسرى)"""
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 10px;
                margin: 5px;
                border: 2px solid #bdc3c7;
            }
        """)

        content_layout = QVBoxLayout()
        content_layout.setContentsMargins(20, 20, 20, 20)

        # عنوان منطقة المحتوى
        content_title = QLabel("📋 منطقة العمل الرئيسية")
        content_title.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-size: 18px;
                font-weight: bold;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 15px;
                text-align: center;
            }
        """)
        content_title.setAlignment(Qt.AlignCenter)

        # منطقة المحتوى المكدسة
        self.stacked_widget = QStackedWidget()

        # الصفحة الرئيسية
        home_page = self.create_home_page()
        self.stacked_widget.addWidget(home_page)

        content_layout.addWidget(content_title)
        content_layout.addWidget(self.stacked_widget)
        content_frame.setLayout(content_layout)

        return content_frame
        
    def create_home_page(self):
        """إنشاء الصفحة الرئيسية"""
        home_widget = QWidget()
        home_layout = QVBoxLayout()
        
        # ترحيب
        welcome_label = QLabel(f"🎉 مرحباً بك في نظام إدارة الأعمال المتكامل")
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
                text-align: center;
            }
        """)
        welcome_label.setAlignment(Qt.AlignCenter)
        
        # معلومات المستخدم
        user_info = QLabel(f"""
        👤 المستخدم الحالي: {self.current_user}
        📅 تاريخ اليوم: {self.get_current_date()}
        🕒 الوقت: {self.get_current_time()}
        """)
        user_info.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #34495e;
                padding: 15px;
                background-color: #d5dbdb;
                border-radius: 8px;
                margin-bottom: 20px;
                text-align: right;
            }
        """)
        
        # أزرار سريعة
        quick_buttons_layout = self.create_quick_buttons()
        
        home_layout.addWidget(welcome_label)
        home_layout.addWidget(user_info)
        home_layout.addLayout(quick_buttons_layout)
        home_layout.addStretch()
        
        home_widget.setLayout(home_layout)
        return home_widget

    def setup_menu_data(self):
        """إعداد بيانات شجرة النظام الشاملة"""
        menu_data = [
            {
                'title': '📁 التهيئة',
                'children': [
                    '📄 المؤشرات العامة',
                    '📄 إعداد قواعد النظام',
                    '📄 تهيئة العملات',
                    '📄 الأقاليم الدولية / الدول',
                    '📄 بيانات المحافظات / المناطق / المدن',
                    '📄 مجموعات المحافظات',
                    '📄 شجرة الفروع',
                    '📄 تعريف الفرع',
                    '📄 إعداد الدليل المحاسبي',
                    '📄 ترميز الحسابات',
                    '📄 ربط الحسابات بإدخال المشاريع',
                    '📄 أنواع الهيكل الإداري',
                    '📄 أنواع السلامة المهنية',
                    '📄 مجموعة الموظفين',
                    '📄 تعريف أصناف العملاء'
                ]
            },
            {
                'title': '📁 المدخلات',
                'children': [
                    '📄 الدليل المحاسبي العام للوحدات',
                    '📄 الإدارة المحاسبية الافتراضية',
                    '📄 الربط المحاسبي',
                    '📄 ربط الحسابات بالحسابات العامة والتدفقات النقدية',
                    '📄 ربط الحسابات بالأنشطة أو المراكز',
                    '📄 إعدادات ترحيل القيود الآلية',
                    '📄 ربط الحسابات مع الموردين والعملاء والأصناف'
                ]
            },
            {
                'title': '📁 إدارة النظام',
                'children': [
                    '📄 إدارة المستخدمين',
                    '📄 صلاحيات المستخدمين',
                    '📄 خصوصية المستخدم',
                    '📄 الإغلاقات والتوثيقات',
                    '📄 النسخ الاحتياطي',
                    '📄 إدارة قاعدة البيانات',
                    '📄 التحكم بواجهات الدخول',
                    '📄 تتبع النشاطات (سجل الأحداث)',
                    '📄 الترقيم التلقائي للمستندات',
                    '📄 تهيئة إعدادات النظام التقنية',
                    '📄 مزامنة البيانات بين الفروع'
                ]
            },
            {
                'title': '📁 أنظمة الحسابات',
                'children': [
                    '📄 قيود اليومية العامة',
                    '📄 قيود يومية آلية',
                    '📄 دفتر الأستاذ العام',
                    '📄 ميزان المراجعة',
                    '📄 ميزان المراجعة التفصيلي',
                    '📄 شجرة الحسابات (الدليل المحاسبي)',
                    '📄 بطاقة حساب',
                    '📄 المراكز المحاسبية / مراكز التكلفة',
                    '📄 التحليل المالي',
                    '📄 ترحيل القيود',
                    '📄 إقفال الحسابات',
                    '📄 تجهيز القوائم المالية',
                    '📄 التسويات المحاسبية',
                    '📄 أوامر الدفع / القبض العامة',
                    '📄 التحويلات البنكية',
                    '📄 ضبط إعدادات القيود'
                ]
            },

            {
                'title': '📁 أنظمة المخازن',
                'children': [
                    '📄 بطاقة صنف',
                    '📄 أنواع الأصناف',
                    '📄 المجموعات الصنفية',
                    '📄 الوحدات الصنفية',
                    '📄 تعريف المخازن',
                    '📄 إدخال مخزني',
                    '📄 إخراج مخزني',
                    '📄 تحويل مخزني بين المخازن',
                    '📄 تسويات مخزنية',
                    '📄 جرد المخزون',
                    '📄 تقرير أرصدة الأصناف',
                    '📄 تقرير حركات الأصناف',
                    '📄 تقرير التالف والمرتجع',
                    '📄 مستويات المخزون (الحد الأدنى/الأقصى)',
                    '📄 التكلفة والربحية',
                    '📄 إعدادات المخزون'
                ]
            },
            {
                'title': '📁 أنظمة الموردين',
                'children': [
                    '📄 بطاقة مورد (تعريف مورد جديد)',
                    '📄 تصنيفات الموردين',
                    '📄 مجموعات الموردين',
                    '📄 ربط الموردين بالحسابات المحاسبية',
                    '📄 رصيد افتتاحي للمورد',
                    '📄 حركة الموردين',
                    '📄 كشف حساب مورد',
                    '📄 تقرير أعمار الديون للموردين',
                    '📄 حدود الائتمان والتعاقدات',
                    '📄 متابعة المشتريات',
                    '📄 تقارير الموردين',
                    '📄 ملاحظات الموردين / مرفقات',
                    '📄 إعدادات إشعارات المدفوعات'
                ]
            },
            {
                'title': '📁 أنظمة الموردين',
                'children': [
                    '📄 بطاقة مورد (تعريف مورد جديد)',
                    '📄 تصنيفات الموردين',
                    '📄 مجموعات الموردين',
                    '📄 ربط الموردين بالحسابات المحاسبية',
                    '📄 رصيد افتتاحي للمورد',
                    '📄 حركة الموردين',
                    '📄 كشف حساب مورد',
                    '📄 تقرير أعمار الديون للموردين',
                    '📄 حدود الائتمان والتعاقدات',
                    '📄 متابعة المشتريات',
                    '📄 تقارير الموردين',
                    '📄 ملاحظات الموردين / مرفقات',
                    '📄 إعدادات إشعارات المدفوعات'
                ]
            },
            {
                'title': '📁 نظام العملاء',
                'children': [
                    '📄 بيانات عملاء (تعريف عميل جديد)',
                    '📄 تصنيفات العملاء / أنواع العملاء',
                    '📄 مجموعات العملاء',
                    '📄 ربط العملاء بالحسابات المحاسبية',
                    '📄 رصيد افتتاحي للعميل',
                    '📄 حركة العملاء',
                    '📄 كشف حساب عميل',
                    '📄 تقرير أعمار الديون',
                    '📄 حدود الائتمان والمتابعة',
                    '📄 تقارير العملاء',
                    '📄 ملاحظات العملاء / المرفقات',
                    '📄 متابعة التحصيل'
                ]
            },
            {
                'title': '📁 نظام نقاط البيع',
                'children': [
                    '📄 شاشة نقاط البيع الرئيسية',
                    '📄 إعدادات نقاط البيع',
                    '📄 إدارة الكاشيرات',
                    '📄 تقارير المبيعات اليومية',
                    '📄 إدارة الخصومات والعروض',
                    '📄 ربط أجهزة الباركود',
                    '📄 إعدادات الطابعات',
                    '📄 إدارة الورديات'
                ]
            },
            {
                'title': '📁 نظام إدارة المعلومات',
                'children': [
                    '📄 التقارير المالية الشاملة',
                    '📄 تقارير المبيعات والمشتريات',
                    '📄 تقارير المخزون والحركة',
                    '📄 تقارير العملاء والموردين',
                    '📄 الاستعلامات المتقدمة',
                    '📄 تحليل الأداء المالي',
                    '📄 مؤشرات الأداء الرئيسية',
                    '📄 التقارير المخصصة'
                ]
            },
            {
                'title': '🛠️ الأنظمة المساعدة',
                'children': [
                    '🧮 الآلة الحاسبة المتقدمة',
                    '📅 التقويم والمواعيد',
                    '📝 محرر النصوص',
                    '🔍 البحث المتقدم',
                    '📊 مولد التقارير',
                    '🔧 أدوات النظام',
                    '💱 محول العملات',
                    '📈 مراقب الأداء',
                    '🗂️ مدير الملفات',
                    '🔐 مولد كلمات المرور',
                    '📋 مدير الحافظة',
                    '🌐 متصفح الويب'
                ]
            }
        ]

        # إضافة العناصر للشجرة مع تنسيق محسن
        for section in menu_data:
            # إنشاء العنصر الرئيسي (المجلد)
            parent_item = QTreeWidgetItem([section['title']])
            parent_item.setExpanded(False)  # مطوي افتراضياً

            # تنسيق العنصر الرئيسي
            font = parent_item.font(0)
            font.setBold(True)
            font.setPointSize(14)
            parent_item.setFont(0, font)

            # إضافة العناصر الفرعية
            for i, child in enumerate(section['children'], 1):
                child_item = QTreeWidgetItem([f"  {i:02d}. {child}"])

                # تنسيق العنصر الفرعي
                child_font = child_item.font(0)
                child_font.setPointSize(12)
                child_item.setFont(0, child_font)

                # إضافة tooltip مع الوصف
                child_item.setToolTip(0, f"الشاشة رقم {i}: {child}")

                parent_item.addChild(child_item)

            self.menu_tree.addTopLevelItem(parent_item)

        # توسيع القسم الأول افتراضياً (التهيئة)
        if self.menu_tree.topLevelItemCount() > 0:
            self.menu_tree.topLevelItem(0).setExpanded(True)

    def setup_connections(self):
        """إعداد الاتصالات والأحداث"""
        self.menu_tree.itemClicked.connect(self.on_menu_item_clicked)
        self.menu_tree.itemDoubleClicked.connect(self.on_menu_item_double_clicked)

    def create_quick_buttons(self):
        """إنشاء الأزرار السريعة"""
        from PyQt5.QtWidgets import QGridLayout

        buttons_layout = QGridLayout()
        buttons_layout.setSpacing(15)

        # بيانات الأزرار السريعة
        quick_buttons = [
            ('🧾 فاتورة مبيعات', self.open_sales_invoice, '#e74c3c'),
            ('🛒 فاتورة شراء', self.open_purchase_invoice, '#3498db'),
            ('📦 إدارة المخزون', self.open_inventory, '#f39c12'),
            ('👥 العملاء', self.open_customers, '#2ecc71'),
            ('🏭 الموردين', self.open_suppliers, '#9b59b6'),
            ('📊 التقارير', self.open_reports, '#1abc9c'),
            ('💰 الصندوق', self.open_cashbox, '#e67e22'),
            ('⚙️ الإعدادات', self.open_settings, '#34495e')
        ]

        # إنشاء الأزرار
        for i, (text, func, color) in enumerate(quick_buttons):
            btn = QPushButton(text)
            btn.setFixedSize(200, 80)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 10px;
                    font-size: 14px;
                    font-weight: bold;
                    text-align: center;
                }}
                QPushButton:hover {{
                    background-color: {self.darken_color(color)};
                    transform: scale(1.05);
                }}
                QPushButton:pressed {{
                    background-color: {self.darken_color(color, 0.3)};
                }}
            """)
            btn.clicked.connect(func)

            row = i // 4
            col = i % 4
            buttons_layout.addWidget(btn, row, col)

        return buttons_layout

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = QStatusBar()
        status_bar.showMessage(f"مرحباً {self.current_user} | النظام جاهز للعمل")
        self.setStatusBar(status_bar)

    # وظائف مساعدة
    def get_current_date(self):
        """الحصول على التاريخ الحالي"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d")

    def get_current_time(self):
        """الحصول على الوقت الحالي"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")

    def darken_color(self, color, factor=0.2):
        """تغميق اللون"""
        # تحويل اللون السادس عشري إلى RGB وتغميقه
        color = color.lstrip('#')
        rgb = tuple(int(color[i:i+2], 16) for i in (0, 2, 4))
        darkened = tuple(int(c * (1 - factor)) for c in rgb)
        return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

    # معالجات الأحداث
    def on_menu_item_clicked(self, item, column):
        """معالجة النقر على عنصر في شجرة النظام"""
        item_text = item.text(0).strip()

        # تنظيف النص من الترقيم
        clean_text = item_text
        if '. ' in clean_text:
            clean_text = clean_text.split('. ', 1)[1] if '. ' in clean_text else clean_text

        print(f"تم النقر على: {clean_text}")

        # معالجة الشاشات المختلفة حسب النص
        if 'فواتير البيع' in clean_text:
            self.open_sales_invoice()
        elif 'بيانات عملاء' in clean_text:
            self.open_customer_data_interface()
        elif 'بطاقة صنف' in clean_text:
            self.open_item_card()
        elif 'تعريف المخازن' in clean_text:
            self.open_warehouse_definition()
        elif 'إدخال مخزني' in clean_text:
            self.open_inventory_in()
        elif 'إخراج مخزني' in clean_text:
            self.open_inventory_out()
        elif 'تحويل مخزني' in clean_text:
            self.open_inventory_transfer()
        elif 'تسويات مخزنية' in clean_text:
            self.open_inventory_adjustments()
        elif 'جرد المخزون' in clean_text:
            self.open_inventory_count()
        elif 'أرصدة الأصناف' in clean_text:
            self.open_inventory_balances()
        elif 'حركات الأصناف' in clean_text:
            self.open_inventory_movements()
        elif 'إدارة المستخدمين' in clean_text:
            self.open_user_management()
        elif 'صلاحيات المستخدمين' in clean_text:
            self.open_user_permissions()
        elif 'خصوصية المستخدم' in clean_text:
            self.open_user_privacy()
        elif 'الإغلاقات والتوثيقات' in clean_text:
            self.open_closures_and_audits()
        elif 'النسخ الاحتياطي' in clean_text:
            self.open_backup_management()
        elif 'إدارة قاعدة البيانات' in clean_text:
            self.open_database_management()
        elif 'التحكم بواجهات الدخول' in clean_text:
            self.open_login_interface_control()
        elif 'تتبع النشاطات' in clean_text or 'سجل الأحداث' in clean_text:
            self.open_activity_tracking()
        elif 'تهيئة إعدادات النظام التقنية' in clean_text:
            self.open_technical_system_settings()
        elif 'الترقيم التلقائي' in clean_text:
            self.open_auto_numbering()
        elif 'مزامنة البيانات' in clean_text:
            self.open_data_synchronization()
        elif 'قيود اليومية العامة' in clean_text:
            self.open_journal_entries()
        elif 'المؤشرات العامة' in clean_text:
            self.open_general_settings()
        elif 'إعداد قواعد النظام' in clean_text:
            self.open_system_rules()
        elif 'الدليل المحاسبي' in clean_text:
            self.open_chart_of_accounts()
        elif 'تهيئة العملات' in clean_text:
            self.open_currency_setup()
        elif 'الأقاليم الدولية' in clean_text or 'الدول' in clean_text:
            self.open_international_regions()
        elif 'المحافظات' in clean_text or 'المناطق' in clean_text or 'المدن' in clean_text:
            self.open_administrative_divisions()
        elif 'مجموعات المحافظات' in clean_text:
            self.open_governorate_groups()
        elif 'شجرة الفروع' in clean_text:
            self.open_branch_tree()
        elif 'تعريف الفرع' in clean_text or 'إعداد الفرع' in clean_text:
            self.open_branch_definition()
        elif 'الدليل المحاسبي' in clean_text or 'إعداد الدليل' in clean_text:
            self.open_accounting_guide()
        elif 'ترميز الحسابات' in clean_text or 'ترميز' in clean_text:
            self.open_account_coding()
        elif 'ربط الحسابات' in clean_text or 'المشاريع' in clean_text:
            self.open_account_project_linking()
        elif 'الهيكل الإداري' in clean_text or 'أنواع الهيكل' in clean_text:
            self.open_administrative_structure_types()
        elif 'السلامة المهنية' in clean_text or 'أنواع السلامة' in clean_text:
            self.open_occupational_safety_types()
        elif 'الأنظمة المساعدة' in clean_text or 'أنظمة مساعدة' in clean_text:
            self.open_helper_systems()
        elif 'شاشة نقاط البيع' in clean_text:
            self.open_pos_screen()
        elif 'التقارير المالية' in clean_text:
            self.open_financial_reports()
        elif 'النسخ الاحتياطي' in clean_text:
            self.open_backup_manager()
        elif clean_text.startswith('📁'):
            # هذا مجلد رئيسي - توسيع/طي
            item.setExpanded(not item.isExpanded())
        else:
            # شاشة غير مطبقة بعد
            self.show_coming_soon(clean_text)

    def on_menu_item_double_clicked(self, item, column):
        """معالجة النقر المزدوج على عنصر في القائمة"""
        self.on_menu_item_clicked(item, column)

    # وظائف القوائم
    def new_file(self):
        """ملف جديد"""
        QMessageBox.information(self, "ملف جديد", "إنشاء ملف جديد")

    def open_file(self):
        """فتح ملف"""
        QMessageBox.information(self, "فتح ملف", "فتح ملف موجود")

    def toggle_fullscreen(self):
        """تبديل ملء الشاشة"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()

    def toggle_sidebar(self):
        """إخفاء/إظهار القائمة الجانبية"""
        self.sidebar.setVisible(not self.sidebar.isVisible())

    def show_about(self):
        """حول البرنامج"""
        QMessageBox.about(self, "حول البرنامج",
                         "نظام إدارة الأعمال المتكامل\n"
                         "الإصدار 1.0\n"
                         "تطوير: فريق التطوير")

    def show_help(self):
        """دليل المستخدم"""
        QMessageBox.information(self, "المساعدة", "دليل المستخدم قيد التطوير")

    # وظائف الأزرار السريعة
    def go_home(self):
        """العودة للصفحة الرئيسية"""
        self.stacked_widget.setCurrentIndex(0)

    def open_sales_invoice(self):
        """فتح فاتورة المبيعات"""
        try:
            from src.modules.sales_invoices_new import SalesInvoiceDialog

            dialog = SalesInvoiceDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح فاتورة المبيعات:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_purchase_invoice(self):
        """فتح فاتورة الشراء"""
        self.show_coming_soon("فاتورة الشراء")

    def open_inventory(self):
        """فتح إدارة المخزون"""
        try:
            from src.modules.inventory_module import InventoryManagementWidget

            # إنشاء ويدجت إدارة المخازن
            inventory_widget = InventoryManagementWidget(self.db_manager)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(inventory_widget)
            self.stacked_widget.setCurrentWidget(inventory_widget)

            # تحديث شريط الحالة
            self.statusBar().showMessage("تم فتح وحدة إدارة المخازن والمخزون")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إدارة المخزون:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_warehouse_definition(self):
        """فتح تعريف المخازن"""
        try:
            from src.modules.inventory_module import InventoryManagementWidget

            # إنشاء ويدجت إدارة المخازن وفتح تبويب المخازن
            inventory_widget = InventoryManagementWidget(self.db_manager)
            inventory_widget.tabs.setCurrentIndex(0)  # تبويب المخازن

            self.stacked_widget.addWidget(inventory_widget)
            self.stacked_widget.setCurrentWidget(inventory_widget)

            self.statusBar().showMessage("تم فتح تعريف المخازن")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح تعريف المخازن:\n{str(e)}")

    def open_inventory_in(self):
        """فتح إدخال مخزني"""
        try:
            from src.modules.inventory_module import InventoryMovementDialog

            dialog = InventoryMovementDialog(self.db_manager, "in", parent=self)
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إدخال مخزني:\n{str(e)}")

    def open_inventory_out(self):
        """فتح إخراج مخزني"""
        try:
            from src.modules.inventory_module import InventoryMovementDialog

            dialog = InventoryMovementDialog(self.db_manager, "out", parent=self)
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إخراج مخزني:\n{str(e)}")

    def open_inventory_transfer(self):
        """فتح تحويل مخزني"""
        self.show_coming_soon("تحويل مخزني بين المخازن")

    def open_inventory_adjustments(self):
        """فتح تسويات مخزنية"""
        self.show_coming_soon("تسويات مخزنية")

    def open_inventory_count(self):
        """فتح جرد المخزون"""
        self.show_coming_soon("جرد المخزون")

    def open_inventory_balances(self):
        """فتح تقرير أرصدة الأصناف"""
        try:
            from src.modules.inventory_module import InventoryManagementWidget

            # إنشاء ويدجت إدارة المخازن وفتح تبويب الأرصدة
            inventory_widget = InventoryManagementWidget(self.db_manager)
            inventory_widget.tabs.setCurrentIndex(2)  # تبويب الأرصدة

            self.stacked_widget.addWidget(inventory_widget)
            self.stacked_widget.setCurrentWidget(inventory_widget)

            self.statusBar().showMessage("تم فتح تقرير أرصدة الأصناف")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح أرصدة الأصناف:\n{str(e)}")

    def open_inventory_movements(self):
        """فتح تقرير حركات الأصناف"""
        try:
            from src.modules.inventory_module import InventoryManagementWidget

            # إنشاء ويدجت إدارة المخازن وفتح تبويب الحركات
            inventory_widget = InventoryManagementWidget(self.db_manager)
            inventory_widget.tabs.setCurrentIndex(1)  # تبويب الحركات

            self.stacked_widget.addWidget(inventory_widget)
            self.stacked_widget.setCurrentWidget(inventory_widget)

            self.statusBar().showMessage("تم فتح تقرير حركات الأصناف")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح حركات الأصناف:\n{str(e)}")

    def open_auto_numbering(self):
        """فتح إدارة الترقيم التلقائي"""
        try:
            from src.modules.auto_numbering_module import AutoNumberingWidget

            # إنشاء ويدجت الترقيم التلقائي
            numbering_widget = AutoNumberingWidget(self.db_manager, parent=self)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(numbering_widget)
            self.stacked_widget.setCurrentWidget(numbering_widget)

            # تحديث شريط الحالة
            self.statusBar().showMessage("تم فتح وحدة الترقيم التلقائي")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الترقيم التلقائي:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_customers(self):
        """فتح إدارة العملاء"""
        try:
            from src.modules.customers_suppliers_module import CustomersSupplierModule

            # إنشاء ويدجت العملاء والموردين
            customers_widget = CustomersSupplierModule(self.db_manager)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(customers_widget)
            self.stacked_widget.setCurrentWidget(customers_widget)

            # تحديث شريط الحالة
            self.status_bar.showMessage("تم فتح وحدة إدارة العملاء والموردين")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إدارة العملاء:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_customer_data_interface(self):
        """فتح واجهة بيانات العملاء الجديدة"""
        try:
            from src.modules.customers.customer_data_interface import CustomerDataInterface

            # إنشاء واجهة بيانات العملاء
            customer_interface = CustomerDataInterface(self.db_manager)

            # إضافة الواجهة إلى المنطقة المركزية
            self.stacked_widget.addWidget(customer_interface)
            self.stacked_widget.setCurrentWidget(customer_interface)

            # تحديث شريط الحالة
            self.status_bar.showMessage("تم فتح واجهة بيانات العملاء - Onyx Pro ERP")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح واجهة بيانات العملاء:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_suppliers(self):
        """فتح إدارة الموردين"""
        try:
            from src.modules.customers_suppliers_module import CustomersSupplierModule

            # إنشاء ويدجت العملاء والموردين (نفس الوحدة)
            suppliers_widget = CustomersSupplierModule(self.db_manager)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(suppliers_widget)
            self.stacked_widget.setCurrentWidget(suppliers_widget)

            # تحديث شريط الحالة
            self.status_bar.showMessage("تم فتح وحدة إدارة العملاء والموردين")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إدارة الموردين:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_reports(self):
        """فتح التقارير"""
        self.show_coming_soon("التقارير")

    def open_cashbox(self):
        """فتح الصندوق"""
        self.show_coming_soon("إدارة الصندوق")

    def open_settings(self):
        """فتح الإعدادات"""
        try:
            from src.modules.system_settings_module import SystemSettingsDialog
            dialog = SystemSettingsDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الإعدادات:\n{str(e)}")
            # في حالة الفشل، عرض إعدادات بديلة
            self.open_alternative_settings()

    def open_user_settings(self):
        """فتح إعدادات المستخدمين"""
        self.show_coming_soon("إعدادات المستخدمين")

    def open_alternative_settings(self):
        """فتح إعدادات بديلة مبسطة"""
        try:
            from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                                         QWidget, QFormLayout, QLineEdit, QPushButton,
                                         QComboBox, QSpinBox, QCheckBox, QTextEdit,
                                         QGroupBox, QLabel)

            class SimpleSettingsDialog(QDialog):
                def __init__(self, db_manager, parent=None):
                    super().__init__(parent)
                    self.db_manager = db_manager
                    self.setup_ui()

                def setup_ui(self):
                    self.setWindowTitle("⚙️ إعدادات النظام")
                    self.setGeometry(200, 200, 800, 600)
                    self.setLayoutDirection(Qt.RightToLeft)

                    layout = QVBoxLayout()

                    # عنوان
                    title = QLabel("⚙️ إعدادات النظام العامة")
                    title.setStyleSheet("""
                        QLabel {
                            font-size: 18px;
                            font-weight: bold;
                            color: #2c3e50;
                            padding: 15px;
                            background-color: #ecf0f1;
                            border-radius: 8px;
                            margin-bottom: 15px;
                        }
                    """)
                    title.setAlignment(Qt.AlignCenter)
                    layout.addWidget(title)

                    # تبويبات الإعدادات
                    tabs = QTabWidget()

                    # تبويب إعدادات الشركة
                    company_tab = self.create_company_tab()
                    tabs.addTab(company_tab, "🏢 بيانات الشركة")

                    # تبويب إعدادات النظام
                    system_tab = self.create_system_tab()
                    tabs.addTab(system_tab, "⚙️ إعدادات النظام")

                    # تبويب إعدادات المحاسبة
                    accounting_tab = self.create_accounting_tab()
                    tabs.addTab(accounting_tab, "💰 إعدادات المحاسبة")

                    layout.addWidget(tabs)

                    # أزرار التحكم
                    buttons_layout = QHBoxLayout()

                    save_btn = QPushButton("💾 حفظ الإعدادات")
                    save_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #27ae60;
                            color: white;
                            padding: 10px 20px;
                            border-radius: 5px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #229954;
                        }
                    """)
                    save_btn.clicked.connect(self.save_settings)

                    cancel_btn = QPushButton("❌ إلغاء")
                    cancel_btn.setStyleSheet("""
                        QPushButton {
                            background-color: #e74c3c;
                            color: white;
                            padding: 10px 20px;
                            border-radius: 5px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #c0392b;
                        }
                    """)
                    cancel_btn.clicked.connect(self.reject)

                    buttons_layout.addStretch()
                    buttons_layout.addWidget(save_btn)
                    buttons_layout.addWidget(cancel_btn)

                    layout.addLayout(buttons_layout)
                    self.setLayout(layout)

                def create_company_tab(self):
                    tab = QWidget()
                    layout = QFormLayout()

                    self.company_name = QLineEdit("شركة المحاسبة المتقدمة")
                    self.company_address = QTextEdit("صنعاء - اليمن")
                    self.company_address.setMaximumHeight(80)
                    self.company_phone = QLineEdit("+967-1-123456")
                    self.company_email = QLineEdit("<EMAIL>")
                    self.tax_number = QLineEdit("*********")

                    layout.addRow("اسم الشركة:", self.company_name)
                    layout.addRow("عنوان الشركة:", self.company_address)
                    layout.addRow("هاتف الشركة:", self.company_phone)
                    layout.addRow("بريد الشركة:", self.company_email)
                    layout.addRow("الرقم الضريبي:", self.tax_number)

                    tab.setLayout(layout)
                    return tab

                def create_system_tab(self):
                    tab = QWidget()
                    layout = QFormLayout()

                    self.language = QComboBox()
                    self.language.addItems(["العربية", "English"])

                    self.theme = QComboBox()
                    self.theme.addItems(["فاتح", "داكن"])

                    self.font_size = QSpinBox()
                    self.font_size.setRange(8, 20)
                    self.font_size.setValue(10)

                    self.auto_backup = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
                    self.auto_backup.setChecked(True)

                    layout.addRow("لغة النظام:", self.language)
                    layout.addRow("مظهر النظام:", self.theme)
                    layout.addRow("حجم الخط:", self.font_size)
                    layout.addRow("", self.auto_backup)

                    tab.setLayout(layout)
                    return tab

                def create_accounting_tab(self):
                    tab = QWidget()
                    layout = QFormLayout()

                    self.currency = QComboBox()
                    self.currency.addItems(["ريال يمني (YER)", "ريال سعودي (SAR)", "دولار أمريكي (USD)"])

                    self.decimal_places = QSpinBox()
                    self.decimal_places.setRange(0, 4)
                    self.decimal_places.setValue(2)

                    self.tax_rate = QSpinBox()
                    self.tax_rate.setRange(0, 100)
                    self.tax_rate.setValue(0)
                    self.tax_rate.setSuffix("%")

                    self.invoice_prefix = QLineEdit("INV")

                    layout.addRow("العملة الافتراضية:", self.currency)
                    layout.addRow("عدد الخانات العشرية:", self.decimal_places)
                    layout.addRow("معدل الضريبة:", self.tax_rate)
                    layout.addRow("بادئة رقم الفاتورة:", self.invoice_prefix)

                    tab.setLayout(layout)
                    return tab

                def save_settings(self):
                    try:
                        # حفظ الإعدادات في قاعدة البيانات
                        settings = [
                            ('company_name', self.company_name.text()),
                            ('company_address', self.company_address.toPlainText()),
                            ('company_phone', self.company_phone.text()),
                            ('company_email', self.company_email.text()),
                            ('tax_number', self.tax_number.text()),
                            ('currency', self.currency.currentText()),
                            ('decimal_places', str(self.decimal_places.value())),
                            ('tax_rate', str(self.tax_rate.value())),
                            ('invoice_prefix', self.invoice_prefix.text())
                        ]

                        for key, value in settings:
                            query = """
                                INSERT OR REPLACE INTO system_settings
                                (setting_key, setting_value, setting_type, description, category, is_editable)
                                VALUES (?, ?, 'string', ?, 'general', 1)
                            """
                            self.db_manager.execute_query(query, (key, value, f"إعداد {key}"))

                        QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح!")
                        self.accept()

                    except Exception as e:
                        QMessageBox.critical(self, "خطأ", f"فشل في حفظ الإعدادات:\n{str(e)}")

            dialog = SimpleSettingsDialog(self.db_manager, parent=self)
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الإعدادات البديلة:\n{str(e)}")

    # وظائف الشاشات الجديدة
    def open_item_card(self):
        """فتح بطاقة صنف"""
        self.show_coming_soon("بطاقة صنف")

    def open_user_management(self):
        """فتح إدارة المستخدمين"""
        try:
            from src.modules.system_management_module import SystemManagementModule

            # إنشاء ويدجت إدارة النظام
            system_management_widget = SystemManagementModule(self.db_manager)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(system_management_widget)
            self.stacked_widget.setCurrentWidget(system_management_widget)

            # تحديث شريط الحالة
            self.statusBar().showMessage("تم فتح وحدة إدارة النظام")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إدارة النظام:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_user_permissions(self):
        """فتح صلاحيات المستخدمين"""
        try:
            from src.modules.security_settings_module import SecuritySettingsWidget

            # إنشاء ويدجت إعدادات الأمان
            security_widget = SecuritySettingsWidget(self.db_manager)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(security_widget)
            self.stacked_widget.setCurrentWidget(security_widget)

            # تحديث شريط الحالة
            self.statusBar().showMessage("تم فتح إعدادات الأمان والصلاحيات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح صلاحيات المستخدمين:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_user_privacy(self):
        """فتح خصوصية المستخدم"""
        try:
            from src.modules.user_privacy_module import UserPrivacyWidget

            # إنشاء ويدجت خصوصية المستخدم
            privacy_widget = UserPrivacyWidget(self.db_manager, self.current_user, parent=self)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(privacy_widget)
            self.stacked_widget.setCurrentWidget(privacy_widget)

            # تحديث شريط الحالة
            self.statusBar().showMessage("تم فتح وحدة خصوصية المستخدم")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح خصوصية المستخدم:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_closures_and_audits(self):
        """فتح الإغلاقات والتوثيقات"""
        try:
            from src.modules.closures_audits_module import show_closures_audits

            # إنشاء ويدجت الإغلاقات والتوثيقات
            closures_audits_widget = show_closures_audits(self.db_manager)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(closures_audits_widget)
            self.stacked_widget.setCurrentWidget(closures_audits_widget)

            # تحديث شريط الحالة
            self.statusBar().showMessage("تم فتح وحدة الإغلاقات والتوثيقات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الإغلاقات والتوثيقات:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_backup_management(self):
        """فتح إدارة النسخ الاحتياطي"""
        try:
            from src.modules.backup_module import BackupWidget

            # إنشاء ويدجت النسخ الاحتياطي
            backup_widget = BackupWidget(self.db_manager)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(backup_widget)
            self.stacked_widget.setCurrentWidget(backup_widget)

            # تحديث شريط الحالة
            self.statusBar().showMessage("تم فتح إدارة النسخ الاحتياطي")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إدارة النسخ الاحتياطي:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_database_management(self):
        """فتح إدارة قاعدة البيانات"""
        try:
            from src.database.database_admin_gui import DatabaseAdminMainWindow

            self.db_admin_window = DatabaseAdminMainWindow()
            self.db_admin_window.show()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إدارة قاعدة البيانات:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_login_interface_control(self):
        """فتح التحكم بواجهات الدخول"""
        try:
            from src.modules.login_interface_control_module import show_login_interface_control

            # إنشاء ويدجت التحكم بواجهات الدخول
            login_control_widget = show_login_interface_control(self.db_manager)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(login_control_widget)
            self.stacked_widget.setCurrentWidget(login_control_widget)

            # تحديث شريط الحالة
            self.statusBar().showMessage("تم فتح وحدة التحكم بواجهات الدخول")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح التحكم بواجهات الدخول:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_activity_tracking(self):
        """فتح تتبع النشاطات"""
        try:
            from src.modules.activity_tracking_module import show_activity_tracking

            # إنشاء ويدجت تتبع النشاطات
            activity_tracking_widget = show_activity_tracking(self.db_manager)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(activity_tracking_widget)
            self.stacked_widget.setCurrentWidget(activity_tracking_widget)

            # تحديث شريط الحالة
            self.statusBar().showMessage("تم فتح وحدة تتبع النشاطات")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح تتبع النشاطات:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_technical_system_settings(self):
        """فتح تهيئة إعدادات النظام التقنية"""
        try:
            from src.modules.system_settings_module import SystemSettingsWidget

            # إنشاء ويدجت إعدادات النظام
            settings_widget = SystemSettingsWidget(self.db_manager)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(settings_widget)
            self.stacked_widget.setCurrentWidget(settings_widget)

            # تحديث شريط الحالة
            self.statusBar().showMessage("تم فتح إعدادات النظام التقنية")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إعدادات النظام التقنية:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_data_synchronization(self):
        """فتح مزامنة البيانات"""
        try:
            from src.modules.data_synchronization_module import show_data_synchronization

            # إنشاء ويدجت مزامنة البيانات
            data_sync_widget = show_data_synchronization(self.db_manager)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(data_sync_widget)
            self.stacked_widget.setCurrentWidget(data_sync_widget)

            # تحديث شريط الحالة
            self.statusBar().showMessage("تم فتح وحدة مزامنة البيانات بين الفروع")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح مزامنة البيانات:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_journal_entries(self):
        """فتح قيود اليومية العامة"""
        try:
            from src.modules.journal_entries_module import JournalEntriesWidget

            # إنشاء ويدجت القيود اليومية
            journal_widget = JournalEntriesWidget(self.db_manager)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(journal_widget)
            self.stacked_widget.setCurrentWidget(journal_widget)

            # تحديث شريط الحالة
            self.statusBar().showMessage("تم فتح وحدة القيود اليومية العامة")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح القيود اليومية:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_general_settings(self):
        """فتح المؤشرات العامة"""
        try:
            from src.modules.setup.general_indicators import GeneralIndicatorsDialog
            dialog = GeneralIndicatorsDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح المؤشرات العامة:\n{str(e)}")

    def open_system_rules(self):
        """فتح إعداد قواعد النظام"""
        try:
            from src.modules.setup.system_rules import SystemRulesDialog
            dialog = SystemRulesDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح إعداد قواعد النظام:\n{str(e)}")

    def open_chart_of_accounts(self):
        """فتح الدليل المحاسبي"""
        try:
            from src.modules.accounts_module import AccountsManagementWidget

            # إنشاء ويدجت دليل الحسابات
            accounts_widget = AccountsManagementWidget(self.db_manager)

            # إضافة الويدجت إلى المنطقة المركزية
            self.stacked_widget.addWidget(accounts_widget)
            self.stacked_widget.setCurrentWidget(accounts_widget)

            # تحديث شريط الحالة
            self.status_bar.showMessage("تم فتح دليل الحسابات المحاسبي")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح دليل الحسابات:\n{str(e)}")
            import traceback
            traceback.print_exc()

    def open_currency_setup(self):
        """فتح تهيئة العملات"""
        try:
            from src.modules.setup.currency_setup import CurrencySetupDialog
            dialog = CurrencySetupDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح تهيئة العملات:\n{str(e)}")

    def open_international_regions(self):
        """فتح الأقاليم الدولية / الدول"""
        try:
            from src.modules.setup.international_regions import InternationalRegionsDialog
            dialog = InternationalRegionsDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الأقاليم الدولية:\n{str(e)}")

    def open_administrative_divisions(self):
        """فتح بيانات المحافظات / المناطق / المدن"""
        try:
            from src.modules.setup.administrative_divisions import AdministrativeDivisionsDialog
            dialog = AdministrativeDivisionsDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح التقسيمات الإدارية:\n{str(e)}")

    def open_governorate_groups(self):
        """فتح مجموعات المحافظات"""
        try:
            from src.modules.setup.governorate_groups import GovernorateGroupsDialog
            dialog = GovernorateGroupsDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح مجموعات المحافظات:\n{str(e)}")

    def open_branch_tree(self):
        """فتح شجرة الفروع"""
        try:
            from src.modules.setup.branch_tree import BranchTreeDialog
            dialog = BranchTreeDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح شجرة الفروع:\n{str(e)}")

    def open_branch_definition(self):
        """فتح تعريف الفرع"""
        try:
            from src.modules.setup.branch_definition import BranchDefinitionDialog
            dialog = BranchDefinitionDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح تعريف الفرع:\n{str(e)}")

    def open_accounting_guide(self):
        """فتح إعداد الدليل المحاسبي"""
        try:
            from src.modules.setup.accounting_guide import AccountingGuideDialog
            dialog = AccountingGuideDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الدليل المحاسبي:\n{str(e)}")

    def open_account_coding(self):
        """فتح ترميز الحسابات"""
        try:
            from src.modules.setup.account_coding import AccountCodingDialog
            dialog = AccountCodingDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح ترميز الحسابات:\n{str(e)}")

    def open_account_project_linking(self):
        """فتح ربط الحسابات بإدخال المشاريع"""
        try:
            from src.modules.setup.account_project_linking import AccountProjectLinkingDialog
            dialog = AccountProjectLinkingDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح ربط الحسابات بالمشاريع:\n{str(e)}")

    def open_administrative_structure_types(self):
        """فتح أنواع الهيكل الإداري"""
        try:
            from src.modules.setup.administrative_structure_types import AdministrativeStructureTypesDialog
            dialog = AdministrativeStructureTypesDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح أنواع الهيكل الإداري:\n{str(e)}")

    def open_occupational_safety_types(self):
        """فتح أنواع السلامة المهنية"""
        try:
            from src.modules.setup.occupational_safety_types import OccupationalSafetyTypesDialog
            dialog = OccupationalSafetyTypesDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح أنواع السلامة المهنية:\n{str(e)}")

    def open_helper_systems(self):
        """فتح الأنظمة المساعدة"""
        try:
            from src.modules.helper_systems.helper_systems_main import HelperSystemsMainDialog
            dialog = HelperSystemsMainDialog(self.db_manager, parent=self)
            dialog.exec_()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في فتح الأنظمة المساعدة:\n{str(e)}")

    def open_pos_screen(self):
        """فتح شاشة نقاط البيع"""
        self.show_coming_soon("شاشة نقاط البيع الرئيسية")

    def open_financial_reports(self):
        """فتح التقارير المالية"""
        self.show_coming_soon("التقارير المالية الشاملة")

    def open_backup_manager(self):
        """فتح مدير النسخ الاحتياطي"""
        self.show_coming_soon("النسخ الاحتياطي")

    def show_coming_soon(self, feature_name):
        """عرض رسالة قيد التطوير مع معلومات إضافية"""
        # تنظيف اسم الميزة من الترقيم
        clean_name = feature_name
        if '. ' in clean_name:
            clean_name = clean_name.split('. ', 1)[1] if '. ' in clean_name else clean_name

        # تحديد نوع الشاشة
        screen_type = "📄 شاشة إدخال بيانات"
        if "تقرير" in clean_name:
            screen_type = "📊 تقرير"
        elif "إعداد" in clean_name or "تهيئة" in clean_name:
            screen_type = "⚙️ شاشة إعدادات"
        elif "إدارة" in clean_name:
            screen_type = "🔧 شاشة إدارة"

        QMessageBox.information(self, "قيد التطوير",
                               f"🚧 {screen_type}\n\n"
                               f"📋 الشاشة: {clean_name}\n\n"
                               f"⏳ هذه الشاشة قيد التطوير وستكون متاحة في الإصدارات القادمة\n\n"
                               f"✅ الشاشات المتاحة حالياً:\n"
                               f"   • فواتير البيع (مكتملة)\n"
                               f"   • شجرة النظام (مكتملة)\n"
                               f"   • الواجهة الرئيسية (مكتملة)")

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        reply = QMessageBox.question(self, "إغلاق النظام",
                                   "هل تريد إغلاق النظام؟",
                                   QMessageBox.Yes | QMessageBox.No)

        if reply == QMessageBox.Yes:
            # تنظيف الموارد
            if hasattr(self, 'db_manager'):
                self.db_manager.disconnect()
            event.accept()
        else:
            event.ignore()


def main():
    """الوظيفة الرئيسية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    # تطبيق تنسيق عام
    app.setStyleSheet("""
        QApplication {
            font-family: 'Cairo', 'Tahoma', sans-serif;
            font-size: 12px;
        }
    """)

    # إنشاء النافذة الرئيسية
    window = MainSystemWindow()
    window.show()

    # تشغيل التطبيق
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
