#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
شاشة الأقاليم الدولية / الدول
International Regions / Countries Setup Screen
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QGroupBox, QCheckBox, QTableWidget, 
                             QTableWidgetItem, QHeaderView, QMessageBox, 
                             QFrame, QSplitter, QTextEdit, QTabWidget, QWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class InternationalRegionsDialog(QDialog):
    """شاشة الأقاليم الدولية / الدول"""
    
    def __init__(self, db_manager, parent=None):
        super().__init__(parent)
        self.db_manager = db_manager
        self.setup_ui()
        self.load_data()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("الأقاليم الدولية / الدول - إعداد التقسيم الجغرافي الدولي")
        self.setGeometry(100, 50, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان الشاشة
        title_label = QLabel("🌍 الأقاليم الدولية والدول")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # تبويبات الإدارة
        tabs = QTabWidget()
        
        # تبويب الأقاليم
        regions_tab = self.create_regions_tab()
        tabs.addTab(regions_tab, "🌍 الأقاليم الدولية")
        
        # تبويب الدول
        countries_tab = self.create_countries_tab()
        tabs.addTab(countries_tab, "🏳️ الدول")
        
        # تبويب الإعدادات
        settings_tab = self.create_settings_tab()
        tabs.addTab(settings_tab, "⚙️ إعدادات التقسيم")
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(tabs)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_regions_tab(self):
        """إنشاء تبويب الأقاليم الدولية"""
        tab = QWidget()
        layout = QHBoxLayout()
        
        # الجانب الأيسر - نموذج إضافة إقليم
        left_panel = QFrame()
        left_panel.setFrameStyle(QFrame.StyledPanel)
        left_panel.setFixedWidth(350)
        left_layout = QVBoxLayout()
        
        # عنوان القسم
        section_title = QLabel("➕ إضافة إقليم جديد")
        section_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                padding: 10px;
                background-color: #d5dbdb;
                border-radius: 5px;
                margin-bottom: 15px;
            }
        """)
        section_title.setAlignment(Qt.AlignCenter)
        
        # نموذج الإقليم
        region_form = QGroupBox("بيانات الإقليم")
        form_layout = QGridLayout()
        
        # اسم الإقليم
        form_layout.addWidget(QLabel("اسم الإقليم:"), 0, 0)
        self.region_name = QLineEdit()
        self.region_name.setPlaceholderText("مثال: الشرق الأوسط")
        form_layout.addWidget(self.region_name, 0, 1)
        
        # اسم الإقليم بالإنجليزية
        form_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 1, 0)
        self.region_name_en = QLineEdit()
        self.region_name_en.setPlaceholderText("Middle East")
        form_layout.addWidget(self.region_name_en, 1, 1)
        
        # رمز الإقليم
        form_layout.addWidget(QLabel("رمز الإقليم:"), 2, 0)
        self.region_code = QLineEdit()
        self.region_code.setPlaceholderText("ME")
        self.region_code.setMaxLength(5)
        form_layout.addWidget(self.region_code, 2, 1)
        
        # وصف الإقليم
        form_layout.addWidget(QLabel("الوصف:"), 3, 0)
        self.region_description = QTextEdit()
        self.region_description.setMaximumHeight(80)
        self.region_description.setPlaceholderText("وصف مختصر للإقليم...")
        form_layout.addWidget(self.region_description, 3, 1)
        
        # إقليم نشط
        self.region_active = QCheckBox("إقليم نشط")
        self.region_active.setChecked(True)
        form_layout.addWidget(self.region_active, 4, 0, 1, 2)
        
        region_form.setLayout(form_layout)
        
        # أزرار إدارة الأقاليم
        region_buttons = QHBoxLayout()
        
        add_region_btn = QPushButton("➕ إضافة")
        add_region_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_region_btn.clicked.connect(self.add_region)
        
        edit_region_btn = QPushButton("✏️ تعديل")
        edit_region_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_region_btn.clicked.connect(self.edit_region)
        
        delete_region_btn = QPushButton("🗑️ حذف")
        delete_region_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_region_btn.clicked.connect(self.delete_region)
        
        region_buttons.addWidget(add_region_btn)
        region_buttons.addWidget(edit_region_btn)
        region_buttons.addWidget(delete_region_btn)
        
        left_layout.addWidget(section_title)
        left_layout.addWidget(region_form)
        left_layout.addLayout(region_buttons)
        left_layout.addStretch()
        
        left_panel.setLayout(left_layout)
        
        # الجانب الأيمن - جدول الأقاليم
        right_panel = QFrame()
        right_panel.setFrameStyle(QFrame.StyledPanel)
        right_layout = QVBoxLayout()
        
        # عنوان الجدول
        table_title = QLabel("📋 الأقاليم المعرفة")
        table_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        table_title.setAlignment(Qt.AlignCenter)
        
        # جدول الأقاليم
        self.regions_table = QTableWidget()
        self.regions_table.setColumnCount(5)
        self.regions_table.setHorizontalHeaderLabels([
            "اسم الإقليم", "الاسم بالإنجليزية", "الرمز", "عدد الدول", "الحالة"
        ])
        
        # تنسيق الجدول
        self.regions_table.horizontalHeader().setStretchLastSection(True)
        self.regions_table.setAlternatingRowColors(True)
        self.regions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.regions_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        right_layout.addWidget(table_title)
        right_layout.addWidget(self.regions_table)
        
        right_panel.setLayout(right_layout)
        
        layout.addWidget(left_panel)
        layout.addWidget(right_panel)
        
        tab.setLayout(layout)
        return tab
        
    def create_countries_tab(self):
        """إنشاء تبويب الدول"""
        tab = QWidget()
        layout = QHBoxLayout()
        
        # الجانب الأيسر - نموذج إضافة دولة
        left_panel = QFrame()
        left_panel.setFrameStyle(QFrame.StyledPanel)
        left_panel.setFixedWidth(350)
        left_layout = QVBoxLayout()
        
        # عنوان القسم
        section_title = QLabel("🏳️ إضافة دولة جديدة")
        section_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #34495e;
                padding: 10px;
                background-color: #d5dbdb;
                border-radius: 5px;
                margin-bottom: 15px;
            }
        """)
        section_title.setAlignment(Qt.AlignCenter)
        
        # نموذج الدولة
        country_form = QGroupBox("بيانات الدولة")
        form_layout = QGridLayout()
        
        # اسم الدولة
        form_layout.addWidget(QLabel("اسم الدولة:"), 0, 0)
        self.country_name = QLineEdit()
        self.country_name.setPlaceholderText("مثال: الجمهورية اليمنية")
        form_layout.addWidget(self.country_name, 0, 1)
        
        # اسم الدولة بالإنجليزية
        form_layout.addWidget(QLabel("الاسم بالإنجليزية:"), 1, 0)
        self.country_name_en = QLineEdit()
        self.country_name_en.setPlaceholderText("Republic of Yemen")
        form_layout.addWidget(self.country_name_en, 1, 1)
        
        # رمز الدولة (ISO)
        form_layout.addWidget(QLabel("رمز الدولة (ISO):"), 2, 0)
        self.country_code = QLineEdit()
        self.country_code.setPlaceholderText("YE")
        self.country_code.setMaxLength(3)
        form_layout.addWidget(self.country_code, 2, 1)
        
        # رمز الهاتف الدولي
        form_layout.addWidget(QLabel("رمز الهاتف:"), 3, 0)
        self.phone_code = QLineEdit()
        self.phone_code.setPlaceholderText("+967")
        form_layout.addWidget(self.phone_code, 3, 1)
        
        # الإقليم التابع له
        form_layout.addWidget(QLabel("الإقليم:"), 4, 0)
        self.country_region = QComboBox()
        form_layout.addWidget(self.country_region, 4, 1)
        
        # العملة الرسمية
        form_layout.addWidget(QLabel("العملة الرسمية:"), 5, 0)
        self.country_currency = QComboBox()
        form_layout.addWidget(self.country_currency, 5, 1)
        
        # دولة نشطة
        self.country_active = QCheckBox("دولة نشطة")
        self.country_active.setChecked(True)
        form_layout.addWidget(self.country_active, 6, 0, 1, 2)
        
        country_form.setLayout(form_layout)
        
        # أزرار إدارة الدول
        country_buttons = QHBoxLayout()
        
        add_country_btn = QPushButton("🏳️ إضافة")
        add_country_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        add_country_btn.clicked.connect(self.add_country)
        
        edit_country_btn = QPushButton("✏️ تعديل")
        edit_country_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        edit_country_btn.clicked.connect(self.edit_country)
        
        delete_country_btn = QPushButton("🗑️ حذف")
        delete_country_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        delete_country_btn.clicked.connect(self.delete_country)
        
        country_buttons.addWidget(add_country_btn)
        country_buttons.addWidget(edit_country_btn)
        country_buttons.addWidget(delete_country_btn)
        
        left_layout.addWidget(section_title)
        left_layout.addWidget(country_form)
        left_layout.addLayout(country_buttons)
        left_layout.addStretch()
        
        left_panel.setLayout(left_layout)
        
        # الجانب الأيمن - جدول الدول
        right_panel = QFrame()
        right_panel.setFrameStyle(QFrame.StyledPanel)
        right_layout = QVBoxLayout()
        
        # عنوان الجدول
        table_title = QLabel("🏳️ الدول المعرفة")
        table_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        table_title.setAlignment(Qt.AlignCenter)
        
        # جدول الدول
        self.countries_table = QTableWidget()
        self.countries_table.setColumnCount(7)
        self.countries_table.setHorizontalHeaderLabels([
            "اسم الدولة", "الاسم بالإنجليزية", "الرمز", "رمز الهاتف", "الإقليم", "العملة", "الحالة"
        ])
        
        # تنسيق الجدول
        self.countries_table.horizontalHeader().setStretchLastSection(True)
        self.countries_table.setAlternatingRowColors(True)
        self.countries_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.countries_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        right_layout.addWidget(table_title)
        right_layout.addWidget(self.countries_table)
        
        right_panel.setLayout(right_layout)
        
        layout.addWidget(left_panel)
        layout.addWidget(right_panel)
        
        tab.setLayout(layout)
        return tab
        
    def create_settings_tab(self):
        """إنشاء تبويب إعدادات التقسيم"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # مجموعة إعدادات العرض
        display_group = QGroupBox("🖥️ إعدادات العرض")
        display_layout = QGridLayout()
        
        # ترتيب العرض
        display_layout.addWidget(QLabel("ترتيب عرض الأقاليم:"), 0, 0)
        self.region_sort_order = QComboBox()
        self.region_sort_order.addItems(["أبجدي", "حسب الرمز", "حسب عدد الدول", "مخصص"])
        display_layout.addWidget(self.region_sort_order, 0, 1)
        
        # ترتيب عرض الدول
        display_layout.addWidget(QLabel("ترتيب عرض الدول:"), 1, 0)
        self.country_sort_order = QComboBox()
        self.country_sort_order.addItems(["أبجدي", "حسب الرمز", "حسب الإقليم", "مخصص"])
        display_layout.addWidget(self.country_sort_order, 1, 1)
        
        # عرض الأقاليم غير النشطة
        self.show_inactive_regions = QCheckBox("عرض الأقاليم غير النشطة")
        display_layout.addWidget(self.show_inactive_regions, 2, 0, 1, 2)
        
        # عرض الدول غير النشطة
        self.show_inactive_countries = QCheckBox("عرض الدول غير النشطة")
        display_layout.addWidget(self.show_inactive_countries, 3, 0, 1, 2)
        
        display_group.setLayout(display_layout)
        
        # مجموعة إعدادات التصدير والاستيراد
        import_export_group = QGroupBox("📤 التصدير والاستيراد")
        import_export_layout = QVBoxLayout()
        
        # أزرار التصدير والاستيراد
        ie_buttons = QHBoxLayout()
        
        export_btn = QPushButton("📤 تصدير البيانات")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        export_btn.clicked.connect(self.export_data)
        
        import_btn = QPushButton("📥 استيراد البيانات")
        import_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        import_btn.clicked.connect(self.import_data)
        
        load_defaults_btn = QPushButton("🌍 تحميل البيانات الافتراضية")
        load_defaults_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        load_defaults_btn.clicked.connect(self.load_default_data)
        
        ie_buttons.addWidget(export_btn)
        ie_buttons.addWidget(import_btn)
        ie_buttons.addWidget(load_defaults_btn)
        ie_buttons.addStretch()
        
        import_export_layout.addLayout(ie_buttons)
        
        # معلومات إضافية
        info_label = QLabel("""
        💡 معلومات مفيدة:
        • يمكن تصدير البيانات بصيغة Excel أو CSV
        • الاستيراد يدعم نفس الصيغ
        • البيانات الافتراضية تشمل جميع دول العالم مقسمة حسب الأقاليم
        • يمكن تخصيص الترتيب والعرض حسب احتياجات المؤسسة
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #e8f4fd;
                border: 1px solid #bee5eb;
                border-radius: 5px;
                padding: 15px;
                font-size: 12px;
                color: #0c5460;
            }
        """)
        
        import_export_layout.addWidget(info_label)
        import_export_group.setLayout(import_export_layout)
        
        layout.addWidget(display_group)
        layout.addWidget(import_export_group)
        layout.addStretch()
        
        tab.setLayout(layout)
        return tab
        
    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()
        
        # زر الحفظ
        save_btn = QPushButton("💾 حفظ جميع التغييرات")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        save_btn.clicked.connect(self.save_all_changes)
        
        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        # زر المساعدة
        help_btn = QPushButton("❓ المساعدة")
        help_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        help_btn.clicked.connect(self.show_help)
        
        layout.addWidget(help_btn)
        layout.addStretch()
        layout.addWidget(save_btn)
        layout.addWidget(cancel_btn)
        
        return layout
        
    def load_data(self):
        """تحميل البيانات المحفوظة"""
        try:
            # تحميل الأقاليم الافتراضية
            default_regions = [
                ("الشرق الأوسط", "Middle East", "ME", "منطقة الشرق الأوسط وشمال أفريقيا", True),
                ("آسيا", "Asia", "AS", "القارة الآسيوية", True),
                ("أوروبا", "Europe", "EU", "القارة الأوروبية", True),
                ("أفريقيا", "Africa", "AF", "القارة الأفريقية", True),
                ("أمريكا الشمالية", "North America", "NA", "قارة أمريكا الشمالية", True),
                ("أمريكا الجنوبية", "South America", "SA", "قارة أمريكا الجنوبية", True),
                ("أوقيانوسيا", "Oceania", "OC", "قارة أوقيانوسيا", True)
            ]
            
            self.regions_table.setRowCount(len(default_regions))
            for row, (name, name_en, code, desc, active) in enumerate(default_regions):
                self.regions_table.setItem(row, 0, QTableWidgetItem(name))
                self.regions_table.setItem(row, 1, QTableWidgetItem(name_en))
                self.regions_table.setItem(row, 2, QTableWidgetItem(code))
                self.regions_table.setItem(row, 3, QTableWidgetItem("0"))  # عدد الدول
                status = "نشط" if active else "غير نشط"
                self.regions_table.setItem(row, 4, QTableWidgetItem(status))
            
            # تحديث قائمة الأقاليم في تبويب الدول
            self.update_region_combo()
            
            # تحميل الدول الافتراضية
            self.load_sample_countries()
            
            # تحديث قائمة العملات
            self.update_currency_combo()
            
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            
    def update_region_combo(self):
        """تحديث قائمة الأقاليم"""
        self.country_region.clear()
        for row in range(self.regions_table.rowCount()):
            region_name = self.regions_table.item(row, 0).text()
            self.country_region.addItem(region_name)
            
    def update_currency_combo(self):
        """تحديث قائمة العملات"""
        currencies = [
            "ريال يمني (YER)", "دولار أمريكي (USD)", "يورو (EUR)",
            "ريال سعودي (SAR)", "درهم إماراتي (AED)", "دينار كويتي (KWD)",
            "ريال قطري (QAR)", "دينار بحريني (BHD)", "ليرة لبنانية (LBP)",
            "جنيه مصري (EGP)", "دينار أردني (JOD)", "ليرة سورية (SYP)"
        ]
        
        self.country_currency.clear()
        for currency in currencies:
            self.country_currency.addItem(currency)
            
    def load_sample_countries(self):
        """تحميل دول نموذجية"""
        sample_countries = [
            ("الجمهورية اليمنية", "Republic of Yemen", "YE", "+967", "الشرق الأوسط", "ريال يمني (YER)", True),
            ("المملكة العربية السعودية", "Saudi Arabia", "SA", "+966", "الشرق الأوسط", "ريال سعودي (SAR)", True),
            ("دولة الإمارات العربية المتحدة", "UAE", "AE", "+971", "الشرق الأوسط", "درهم إماراتي (AED)", True),
            ("جمهورية مصر العربية", "Egypt", "EG", "+20", "الشرق الأوسط", "جنيه مصري (EGP)", True),
            ("المملكة الأردنية الهاشمية", "Jordan", "JO", "+962", "الشرق الأوسط", "دينار أردني (JOD)", True),
            ("الولايات المتحدة الأمريكية", "United States", "US", "+1", "أمريكا الشمالية", "دولار أمريكي (USD)", True),
            ("المملكة المتحدة", "United Kingdom", "GB", "+44", "أوروبا", "جنيه إسترليني (GBP)", True)
        ]
        
        self.countries_table.setRowCount(len(sample_countries))
        for row, (name, name_en, code, phone, region, currency, active) in enumerate(sample_countries):
            self.countries_table.setItem(row, 0, QTableWidgetItem(name))
            self.countries_table.setItem(row, 1, QTableWidgetItem(name_en))
            self.countries_table.setItem(row, 2, QTableWidgetItem(code))
            self.countries_table.setItem(row, 3, QTableWidgetItem(phone))
            self.countries_table.setItem(row, 4, QTableWidgetItem(region))
            self.countries_table.setItem(row, 5, QTableWidgetItem(currency))
            status = "نشطة" if active else "غير نشطة"
            self.countries_table.setItem(row, 6, QTableWidgetItem(status))
            
        # تحديث عدد الدول في جدول الأقاليم
        self.update_region_country_counts()
        
    def update_region_country_counts(self):
        """تحديث عدد الدول في كل إقليم"""
        for region_row in range(self.regions_table.rowCount()):
            region_name = self.regions_table.item(region_row, 0).text()
            count = 0
            
            for country_row in range(self.countries_table.rowCount()):
                country_region = self.countries_table.item(country_row, 4).text()
                if country_region == region_name:
                    count += 1
                    
            self.regions_table.setItem(region_row, 3, QTableWidgetItem(str(count)))
            
    def add_region(self):
        """إضافة إقليم جديد"""
        try:
            if not self.region_name.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الإقليم")
                return
                
            if not self.region_code.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز الإقليم")
                return
                
            # إضافة الإقليم للجدول
            row = self.regions_table.rowCount()
            self.regions_table.insertRow(row)
            
            self.regions_table.setItem(row, 0, QTableWidgetItem(self.region_name.text()))
            self.regions_table.setItem(row, 1, QTableWidgetItem(self.region_name_en.text()))
            self.regions_table.setItem(row, 2, QTableWidgetItem(self.region_code.text().upper()))
            self.regions_table.setItem(row, 3, QTableWidgetItem("0"))
            
            status = "نشط" if self.region_active.isChecked() else "غير نشط"
            self.regions_table.setItem(row, 4, QTableWidgetItem(status))
            
            # تنظيف النموذج
            self.clear_region_form()
            
            # تحديث قائمة الأقاليم
            self.update_region_combo()
            
            QMessageBox.information(self, "نجح", "تم إضافة الإقليم بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الإقليم:\n{str(e)}")
            
    def edit_region(self):
        """تعديل إقليم محدد"""
        current_row = self.regions_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار إقليم للتعديل")
            return
            
        # ملء النموذج ببيانات الإقليم المحدد
        self.region_name.setText(self.regions_table.item(current_row, 0).text())
        self.region_name_en.setText(self.regions_table.item(current_row, 1).text())
        self.region_code.setText(self.regions_table.item(current_row, 2).text())
        
        status = self.regions_table.item(current_row, 4).text()
        self.region_active.setChecked(status == "نشط")
        
    def delete_region(self):
        """حذف إقليم محدد"""
        current_row = self.regions_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار إقليم للحذف")
            return
            
        region_name = self.regions_table.item(current_row, 0).text()
        country_count = int(self.regions_table.item(current_row, 3).text())
        
        if country_count > 0:
            QMessageBox.warning(self, "تحذير", 
                               f"لا يمكن حذف الإقليم '{region_name}'\n"
                               f"يحتوي على {country_count} دولة\n"
                               f"يرجى حذف الدول أولاً أو نقلها لإقليم آخر")
            return
            
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   f"هل تريد حذف الإقليم '{region_name}'؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.regions_table.removeRow(current_row)
            self.update_region_combo()
            QMessageBox.information(self, "تم", "تم حذف الإقليم بنجاح")
            
    def clear_region_form(self):
        """تنظيف نموذج الإقليم"""
        self.region_name.clear()
        self.region_name_en.clear()
        self.region_code.clear()
        self.region_description.clear()
        self.region_active.setChecked(True)
        
    def add_country(self):
        """إضافة دولة جديدة"""
        try:
            if not self.country_name.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الدولة")
                return
                
            if not self.country_code.text().strip():
                QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز الدولة")
                return
                
            # إضافة الدولة للجدول
            row = self.countries_table.rowCount()
            self.countries_table.insertRow(row)
            
            self.countries_table.setItem(row, 0, QTableWidgetItem(self.country_name.text()))
            self.countries_table.setItem(row, 1, QTableWidgetItem(self.country_name_en.text()))
            self.countries_table.setItem(row, 2, QTableWidgetItem(self.country_code.text().upper()))
            self.countries_table.setItem(row, 3, QTableWidgetItem(self.phone_code.text()))
            self.countries_table.setItem(row, 4, QTableWidgetItem(self.country_region.currentText()))
            self.countries_table.setItem(row, 5, QTableWidgetItem(self.country_currency.currentText()))
            
            status = "نشطة" if self.country_active.isChecked() else "غير نشطة"
            self.countries_table.setItem(row, 6, QTableWidgetItem(status))
            
            # تنظيف النموذج
            self.clear_country_form()
            
            # تحديث عدد الدول في الأقاليم
            self.update_region_country_counts()
            
            QMessageBox.information(self, "نجح", "تم إضافة الدولة بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إضافة الدولة:\n{str(e)}")
            
    def edit_country(self):
        """تعديل دولة محددة"""
        current_row = self.countries_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار دولة للتعديل")
            return
            
        # ملء النموذج ببيانات الدولة المحددة
        self.country_name.setText(self.countries_table.item(current_row, 0).text())
        self.country_name_en.setText(self.countries_table.item(current_row, 1).text())
        self.country_code.setText(self.countries_table.item(current_row, 2).text())
        self.phone_code.setText(self.countries_table.item(current_row, 3).text())
        
        region = self.countries_table.item(current_row, 4).text()
        currency = self.countries_table.item(current_row, 5).text()
        
        self.country_region.setCurrentText(region)
        self.country_currency.setCurrentText(currency)
        
        status = self.countries_table.item(current_row, 6).text()
        self.country_active.setChecked(status == "نشطة")
        
    def delete_country(self):
        """حذف دولة محددة"""
        current_row = self.countries_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار دولة للحذف")
            return
            
        country_name = self.countries_table.item(current_row, 0).text()
        
        reply = QMessageBox.question(self, "تأكيد الحذف", 
                                   f"هل تريد حذف الدولة '{country_name}'؟",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.countries_table.removeRow(current_row)
            self.update_region_country_counts()
            QMessageBox.information(self, "تم", "تم حذف الدولة بنجاح")
            
    def clear_country_form(self):
        """تنظيف نموذج الدولة"""
        self.country_name.clear()
        self.country_name_en.clear()
        self.country_code.clear()
        self.phone_code.clear()
        self.country_active.setChecked(True)
        
    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(self, "قيد التطوير", 
                               "📤 ميزة تصدير البيانات قيد التطوير\n"
                               "سيتم إضافتها في الإصدارات القادمة")
        
    def import_data(self):
        """استيراد البيانات"""
        QMessageBox.information(self, "قيد التطوير", 
                               "📥 ميزة استيراد البيانات قيد التطوير\n"
                               "سيتم إضافتها في الإصدارات القادمة")
        
    def load_default_data(self):
        """تحميل البيانات الافتراضية"""
        reply = QMessageBox.question(self, "تأكيد التحميل", 
                                   "هل تريد تحميل البيانات الافتراضية؟\n"
                                   "سيتم استبدال البيانات الحالية",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.load_data()
            QMessageBox.information(self, "تم", "تم تحميل البيانات الافتراضية بنجاح")
            
    def show_help(self):
        """عرض المساعدة"""
        help_text = """
        🌍 مساعدة الأقاليم الدولية والدول

        📋 الوظائف المتاحة:
        
        🌍 تبويب الأقاليم:
        • إضافة أقاليم جغرافية جديدة
        • تعديل بيانات الأقاليم الموجودة
        • حذف الأقاليم (إذا لم تحتوي على دول)
        
        🏳️ تبويب الدول:
        • إضافة دول جديدة
        • ربط الدول بالأقاليم
        • تحديد العملة الرسمية لكل دولة
        • إدارة رموز الهاتف الدولية
        
        ⚙️ تبويب الإعدادات:
        • تخصيص ترتيب العرض
        • تصدير واستيراد البيانات
        • تحميل البيانات الافتراضية
        
        💡 نصائح:
        • استخدم رموز ISO المعيارية للدول
        • تأكد من صحة رموز الهاتف الدولية
        • يمكن إضافة أقاليم مخصصة حسب احتياجات المؤسسة
        """
        
        QMessageBox.information(self, "المساعدة", help_text)
        
    def save_all_changes(self):
        """حفظ جميع التغييرات"""
        try:
            # جمع بيانات الأقاليم
            regions_data = []
            for row in range(self.regions_table.rowCount()):
                region = {
                    'name': self.regions_table.item(row, 0).text(),
                    'name_en': self.regions_table.item(row, 1).text(),
                    'code': self.regions_table.item(row, 2).text(),
                    'country_count': int(self.regions_table.item(row, 3).text()),
                    'status': self.regions_table.item(row, 4).text()
                }
                regions_data.append(region)
            
            # جمع بيانات الدول
            countries_data = []
            for row in range(self.countries_table.rowCount()):
                country = {
                    'name': self.countries_table.item(row, 0).text(),
                    'name_en': self.countries_table.item(row, 1).text(),
                    'code': self.countries_table.item(row, 2).text(),
                    'phone_code': self.countries_table.item(row, 3).text(),
                    'region': self.countries_table.item(row, 4).text(),
                    'currency': self.countries_table.item(row, 5).text(),
                    'status': self.countries_table.item(row, 6).text()
                }
                countries_data.append(country)
            
            # حفظ في قاعدة البيانات
            # هنا يتم حفظ البيانات في قاعدة البيانات
            
            summary = f"""✅ تم حفظ الأقاليم والدول بنجاح!

🌍 الأقاليم المحفوظة: {len(regions_data)}
🏳️ الدول المحفوظة: {len(countries_data)}

📊 إحصائيات:"""
            
            for region in regions_data:
                if region['status'] == 'نشط':
                    summary += f"\n   • {region['name']}: {region['country_count']} دولة"
            
            QMessageBox.information(self, "نجح الحفظ", summary)
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ البيانات:\n{str(e)}")


def main():
    """اختبار الشاشة"""
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass
    
    dialog = InternationalRegionsDialog(MockDBManager())
    dialog.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
