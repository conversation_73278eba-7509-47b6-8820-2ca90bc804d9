#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الأنظمة المساعدة
Helper Systems Test
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

try:
    print("🚀 تشغيل الأنظمة المساعدة...")
    
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    from modules.helper_systems.helper_systems_main import HelperSystemsMainDialog
    
    # محاكاة مدير قاعدة البيانات
    class MockDBManager:
        pass
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    print("✅ تم إنشاء التطبيق بنجاح")
    
    dialog = HelperSystemsMainDialog(MockDBManager())
    print("✅ تم إنشاء الأنظمة المساعدة بنجاح")
    
    dialog.show()
    print("🛠️ الأنظمة المساعدة جاهزة!")
    
    print("\n📋 الأنظمة المتاحة:")
    print("   ✓ 🧮 الآلة الحاسبة المتقدمة - حاسبة علمية ومالية")
    print("   ✓ 📅 التقويم والمواعيد - تقويم هجري وميلادي")
    print("   ✓ 📝 محرر النصوص - محرر متقدم مع دعم العربية")
    print("   ✓ 🔍 البحث المتقدم - بحث شامل في البيانات")
    print("   ✓ 📊 مولد التقارير - تقارير مخصصة ومتقدمة")
    print("   ✓ 🔧 أدوات النظام - صيانة وتحسين النظام")
    print("   ✓ 💱 محول العملات - تحويل العملات المختلفة")
    print("   ✓ 📈 مراقب الأداء - مراقبة أداء النظام")
    print("   ✓ 🗂️ مدير الملفات - إدارة الملفات والمجلدات")
    print("   ✓ 🔐 مولد كلمات المرور - كلمات مرور قوية")
    print("   ✓ 📋 مدير الحافظة - إدارة متقدمة للحافظة")
    print("   ✓ 🌐 متصفح الويب - متصفح مدمج للمراجع")
    
    print("\n🎯 الأنظمة المطورة:")
    print("   🧮 الآلة الحاسبة المتقدمة:")
    print("      • حاسبة أساسية مع العمليات الأساسية")
    print("      • حاسبة علمية مع الدوال المثلثية")
    print("      • محول الوحدات (طول، وزن، مساحة، حجم، حرارة، سرعة)")
    print("      • تاريخ العمليات مع إمكانية الحفظ")
    print("      • واجهة عربية احترافية")
    
    print("\n📊 الإحصائيات:")
    print("   🛠️ 12 نظام مساعد مختلف")
    print("   🧮 1 نظام مطور بالكامل (الآلة الحاسبة)")
    print("   📋 11 نظام قيد التطوير")
    print("   🎨 واجهة عربية احترافية")
    print("   🔧 تصميم متقدم مع بطاقات تفاعلية")
    
    print("\n🎉 الأنظمة المساعدة جاهزة للاستخدام!")
    
    sys.exit(app.exec_())
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("💡 تأكد من وجود ملفات الوحدة في المسار الصحيح")
    
except Exception as e:
    print(f"❌ خطأ في تشغيل الأنظمة المساعدة: {e}")
    import traceback
    traceback.print_exc()
