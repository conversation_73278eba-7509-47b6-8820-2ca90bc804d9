"""
اختبار نافذة المتغيرات العامة
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from src.database.sqlite_manager import SQLiteManager
from src.modules.setup.general_variables import GeneralVariablesDialog

def test_general_variables():
    """اختبار نافذة المتغيرات العامة"""
    app = QApplication(sys.argv)
    
    # إنشاء مدير قاعدة البيانات
    db_manager = SQLiteManager()
    
    # إنشاء النافذة
    dialog = GeneralVariablesDialog(db_manager)
    dialog.show()
    
    print("تم فتح نافذة المتغيرات العامة بنجاح!")
    
    # تشغيل التطبيق
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_general_variables()
