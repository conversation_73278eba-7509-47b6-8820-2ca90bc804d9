#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف أنواع الهيكل الإداري
Administrative Structure Types Functions
"""

from PyQt5.QtWidgets import QMessageBox, QTableWidgetItem, QTreeWidgetItem, QInputDialog, QFileDialog
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor
from datetime import datetime

def create_control_buttons_function(self):
    """إنشاء أزرار التحكم"""
    from PyQt5.QtWidgets import QHBoxLayout, QPushButton
    
    layout = QHBoxLayout()
    
    # زر الحفظ
    save_btn = QPushButton("💾 حفظ التغييرات")
    save_btn.setStyleSheet("""
        QPushButton {
            background-color: #27ae60;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #229954;
        }
    """)
    save_btn.clicked.connect(self.save_changes)
    
    # زر الإلغاء
    cancel_btn = QPushButton("❌ إلغاء")
    cancel_btn.setStyleSheet("""
        QPushButton {
            background-color: #e74c3c;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #c0392b;
        }
    """)
    cancel_btn.clicked.connect(self.reject)
    
    # زر المساعدة
    help_btn = QPushButton("❓ المساعدة")
    help_btn.setStyleSheet("""
        QPushButton {
            background-color: #3498db;
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 14px;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
    """)
    help_btn.clicked.connect(self.show_help)
    
    layout.addWidget(help_btn)
    layout.addStretch()
    layout.addWidget(save_btn)
    layout.addWidget(cancel_btn)
    
    return layout

def load_sample_data_function(self):
    """تحميل البيانات النموذجية"""
    # بيانات أنواع الهياكل النموذجية
    self.structure_types_data = [
        {
            "code": "HIER001",
            "name": "الهيكل الهرمي التقليدي",
            "description": "هيكل تنظيمي هرمي تقليدي مع تسلسل واضح للسلطة من الأعلى إلى الأسفل",
            "levels_count": 5,
            "hierarchy_type": "هرمي",
            "status": "نشط"
        },
        {
            "code": "MATRIX001",
            "name": "الهيكل المصفوفي",
            "description": "هيكل مصفوفي يجمع بين التخصص الوظيفي والتنظيم حسب المشاريع",
            "levels_count": 4,
            "hierarchy_type": "مصفوفي",
            "status": "نشط"
        },
        {
            "code": "FLAT001",
            "name": "الهيكل المسطح",
            "description": "هيكل تنظيمي مسطح مع عدد قليل من المستويات الإدارية",
            "levels_count": 3,
            "hierarchy_type": "مسطح",
            "status": "نشط"
        },
        {
            "code": "FUNC001",
            "name": "الهيكل الوظيفي",
            "description": "تنظيم حسب الوظائف والتخصصات المختلفة",
            "levels_count": 4,
            "hierarchy_type": "وظيفي",
            "status": "نشط"
        },
        {
            "code": "NETWORK001",
            "name": "الهيكل الشبكي",
            "description": "هيكل شبكي مرن يسمح بالتعاون بين الأقسام المختلفة",
            "levels_count": 3,
            "hierarchy_type": "شبكي",
            "status": "نشط"
        }
    ]
    
    # بيانات المناصب النموذجية
    self.positions_data = [
        {
            "code": "CEO001",
            "name": "المدير العام",
            "level": 1,
            "department": "الإدارة العليا",
            "basic_salary": 500000,
            "responsibilities": "الإشراف العام على جميع عمليات الشركة",
            "status": "شغل"
        },
        {
            "code": "CFO001",
            "name": "المدير المالي",
            "level": 2,
            "department": "الشؤون المالية",
            "basic_salary": 350000,
            "responsibilities": "إدارة الشؤون المالية والمحاسبية",
            "status": "شغل"
        },
        {
            "code": "HRM001",
            "name": "مدير الموارد البشرية",
            "level": 2,
            "department": "الموارد البشرية",
            "basic_salary": 300000,
            "responsibilities": "إدارة شؤون الموظفين والتوظيف",
            "status": "شغل"
        },
        {
            "code": "OPM001",
            "name": "مدير العمليات",
            "level": 2,
            "department": "العمليات",
            "basic_salary": 320000,
            "responsibilities": "إدارة العمليات التشغيلية اليومية",
            "status": "شغل"
        },
        {
            "code": "ACC001",
            "name": "محاسب أول",
            "level": 3,
            "department": "الشؤون المالية",
            "basic_salary": 180000,
            "responsibilities": "إعداد القوائم المالية والتقارير المحاسبية",
            "status": "شغل"
        },
        {
            "code": "HRA001",
            "name": "أخصائي موارد بشرية",
            "level": 3,
            "department": "الموارد البشرية",
            "basic_salary": 150000,
            "responsibilities": "تنفيذ سياسات الموارد البشرية",
            "status": "شغل"
        },
        {
            "code": "SUP001",
            "name": "مشرف عمليات",
            "level": 3,
            "department": "العمليات",
            "basic_salary": 160000,
            "responsibilities": "الإشراف على العمليات التشغيلية",
            "status": "شغل"
        },
        {
            "code": "CLERK001",
            "name": "موظف إداري",
            "level": 4,
            "department": "الإدارة العامة",
            "basic_salary": 100000,
            "responsibilities": "تنفيذ المهام الإدارية العامة",
            "status": "شغل"
        }
    ]
    
    # تحديث الجداول
    self.update_structure_types_table()
    self.update_positions_table()
    self.build_org_chart()

def update_structure_types_table_function(self):
    """تحديث جدول أنواع الهياكل"""
    self.structure_types_table.setRowCount(len(self.structure_types_data))
    
    for row, structure_type in enumerate(self.structure_types_data):
        self.structure_types_table.setItem(row, 0, QTableWidgetItem(structure_type["code"]))
        self.structure_types_table.setItem(row, 1, QTableWidgetItem(structure_type["name"]))
        self.structure_types_table.setItem(row, 2, QTableWidgetItem(structure_type["description"]))
        self.structure_types_table.setItem(row, 3, QTableWidgetItem(str(structure_type["levels_count"])))
        self.structure_types_table.setItem(row, 4, QTableWidgetItem(structure_type["hierarchy_type"]))
        self.structure_types_table.setItem(row, 5, QTableWidgetItem(structure_type["status"]))

def update_positions_table_function(self):
    """تحديث جدول المناصب"""
    self.positions_table.setRowCount(len(self.positions_data))
    
    for row, position in enumerate(self.positions_data):
        self.positions_table.setItem(row, 0, QTableWidgetItem(position["code"]))
        self.positions_table.setItem(row, 1, QTableWidgetItem(position["name"]))
        self.positions_table.setItem(row, 2, QTableWidgetItem(str(position["level"])))
        self.positions_table.setItem(row, 3, QTableWidgetItem(position["department"]))
        self.positions_table.setItem(row, 4, QTableWidgetItem(f"{position['basic_salary']:,} ريال"))
        self.positions_table.setItem(row, 5, QTableWidgetItem(position["responsibilities"]))
        self.positions_table.setItem(row, 6, QTableWidgetItem(position["status"]))

def build_org_chart_function(self):
    """بناء الهيكل التنظيمي"""
    self.org_chart_tree.clear()
    
    # تجميع المناصب حسب المستوى
    levels = {}
    for position in self.positions_data:
        level = position["level"]
        if level not in levels:
            levels[level] = []
        levels[level].append(position)
    
    # إنشاء العقد حسب المستوى
    level_items = {}
    
    for level in sorted(levels.keys()):
        for position in levels[level]:
            item = QTreeWidgetItem([
                position["name"],
                "غير محدد",  # اسم الموظف
                position["department"],
                f"المستوى {position['level']}"
            ])
            
            # تلوين حسب المستوى
            if level == 1:
                color = QColor(255, 200, 200)  # أحمر فاتح
                item.setBackground(0, color)
                item.setBackground(1, color)
                item.setBackground(2, color)
                item.setBackground(3, color)
            elif level == 2:
                color = QColor(255, 255, 200)  # أصفر فاتح
                item.setBackground(0, color)
                item.setBackground(1, color)
                item.setBackground(2, color)
                item.setBackground(3, color)
            elif level == 3:
                color = QColor(200, 255, 200)  # أخضر فاتح
                item.setBackground(0, color)
                item.setBackground(1, color)
                item.setBackground(2, color)
                item.setBackground(3, color)
            else:
                color = QColor(200, 200, 255)  # أزرق فاتح
                item.setBackground(0, color)
                item.setBackground(1, color)
                item.setBackground(2, color)
                item.setBackground(3, color)
            
            if level == 1:
                # المستوى الأول (الإدارة العليا)
                self.org_chart_tree.addTopLevelItem(item)
                level_items[position["code"]] = item
            else:
                # المستويات الأخرى تحت الإدارة العليا
                if level == 2:
                    # البحث عن المدير العام
                    ceo_item = None
                    for code, tree_item in level_items.items():
                        if "CEO" in code:
                            ceo_item = tree_item
                            break
                    if ceo_item:
                        ceo_item.addChild(item)
                        level_items[position["code"]] = item
                elif level == 3:
                    # ربط بالمدراء في المستوى الثاني
                    parent_found = False
                    for code, tree_item in level_items.items():
                        if position["department"] in tree_item.text(2):
                            tree_item.addChild(item)
                            level_items[position["code"]] = item
                            parent_found = True
                            break
                    if not parent_found:
                        # إضافة تحت الجذر إذا لم يجد الوالد
                        self.org_chart_tree.addTopLevelItem(item)
                        level_items[position["code"]] = item
                else:
                    # المستويات الأخرى
                    self.org_chart_tree.addTopLevelItem(item)
                    level_items[position["code"]] = item
    
    # توسيع جميع العقد
    self.org_chart_tree.expandAll()

def add_structure_type_function(self):
    """إضافة نوع هيكل جديد"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة أنواع الهياكل قيد التطوير")

def edit_structure_type_function(self):
    """تعديل نوع هيكل"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل أنواع الهياكل قيد التطوير")

def delete_structure_type_function(self):
    """حذف نوع هيكل"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة حذف أنواع الهياكل قيد التطوير")

def add_position_function(self):
    """إضافة منصب جديد"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة إضافة المناصب قيد التطوير")

def edit_position_function(self):
    """تعديل منصب"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة تعديل المناصب قيد التطوير")

def delete_position_function(self):
    """حذف منصب"""
    QMessageBox.information(self, "قيد التطوير", "وظيفة حذف المناصب قيد التطوير")

def refresh_org_chart_function(self):
    """تحديث الهيكل التنظيمي"""
    self.build_org_chart()
    QMessageBox.information(self, "تم", "تم تحديث الهيكل التنظيمي بنجاح")

def export_org_chart_function(self):
    """تصدير الهيكل التنظيمي"""
    QMessageBox.information(self, "قيد التطوير", "تصدير الهيكل التنظيمي قيد التطوير")

def print_org_chart_function(self):
    """طباعة الهيكل التنظيمي"""
    QMessageBox.information(self, "قيد التطوير", "طباعة الهيكل التنظيمي قيد التطوير")

def generate_structure_report_function(self):
    """إنتاج تقرير أنواع الهياكل"""
    report = f"""
🏗️ تقرير أنواع الهياكل الإدارية
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 إحصائيات عامة:
   🏗️ إجمالي أنواع الهياكل: {len(self.structure_types_data)} نوع
   📈 الأنواع النشطة: {len([s for s in self.structure_types_data if s['status'] == 'نشط'])} نوع

📋 قائمة أنواع الهياكل:
"""
    
    for i, structure_type in enumerate(self.structure_types_data, 1):
        report += f"""
{i}. {structure_type['name']} ({structure_type['code']})
   📝 الوصف: {structure_type['description']}
   📊 عدد المستويات: {structure_type['levels_count']}
   🔗 نوع التسلسل: {structure_type['hierarchy_type']}
   ✅ الحالة: {structure_type['status']}
   {'─'*50}
"""
    
    self.reports_display.setPlainText(report)

def generate_positions_report_function(self):
    """إنتاج تقرير المناصب"""
    report = f"""
👔 تقرير المناصب والوظائف
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 إحصائيات عامة:
   👔 إجمالي المناصب: {len(self.positions_data)} منصب
   💰 إجمالي الرواتب: {sum(p['basic_salary'] for p in self.positions_data):,} ريال
   📈 متوسط الراتب: {sum(p['basic_salary'] for p in self.positions_data) // len(self.positions_data):,} ريال

📋 توزيع المناصب حسب المستوى:
"""
    
    # تجميع حسب المستوى
    levels_stats = {}
    for position in self.positions_data:
        level = position["level"]
        if level not in levels_stats:
            levels_stats[level] = {"count": 0, "total_salary": 0}
        levels_stats[level]["count"] += 1
        levels_stats[level]["total_salary"] += position["basic_salary"]
    
    for level in sorted(levels_stats.keys()):
        stats = levels_stats[level]
        avg_salary = stats["total_salary"] // stats["count"]
        report += f"   المستوى {level}: {stats['count']} منصب - متوسط الراتب: {avg_salary:,} ريال\n"
    
    report += f"""

📋 قائمة المناصب:
"""
    
    for i, position in enumerate(self.positions_data, 1):
        report += f"""
{i}. {position['name']} ({position['code']})
   🏢 القسم: {position['department']}
   📊 المستوى: {position['level']}
   💰 الراتب الأساسي: {position['basic_salary']:,} ريال
   📝 المسؤوليات: {position['responsibilities']}
   ✅ الحالة: {position['status']}
   {'─'*50}
"""
    
    self.reports_display.setPlainText(report)

def generate_hierarchy_report_function(self):
    """إنتاج تقرير التسلسل الهرمي"""
    report = f"""
📊 تقرير التسلسل الهرمي
{'='*60}
تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🏗️ الهيكل التنظيمي:

المستوى 1 - الإدارة العليا:
"""
    
    # تجميع حسب المستوى
    levels = {}
    for position in self.positions_data:
        level = position["level"]
        if level not in levels:
            levels[level] = []
        levels[level].append(position)
    
    for level in sorted(levels.keys()):
        if level > 1:
            report += f"\nالمستوى {level}:\n"
        
        for position in levels[level]:
            indent = "  " * (level - 1)
            report += f"{indent}├── {position['name']} ({position['department']})\n"
    
    report += f"""

📊 إحصائيات التسلسل:
   📈 عدد المستويات: {max(levels.keys())} مستوى
   👥 إجمالي المناصب: {len(self.positions_data)} منصب
   🏢 عدد الأقسام: {len(set(p['department'] for p in self.positions_data))} قسم

📋 الأقسام:
"""
    
    departments = set(p['department'] for p in self.positions_data)
    for dept in sorted(departments):
        dept_positions = [p for p in self.positions_data if p['department'] == dept]
        report += f"   🏢 {dept}: {len(dept_positions)} منصب\n"
    
    self.reports_display.setPlainText(report)

def export_report_function(self):
    """تصدير التقرير"""
    QMessageBox.information(self, "قيد التطوير", "تصدير التقارير قيد التطوير")

def print_report_function(self):
    """طباعة التقرير"""
    QMessageBox.information(self, "قيد التطوير", "طباعة التقارير قيد التطوير")

def clear_report_function(self):
    """مسح التقرير"""
    self.reports_display.clear()

def save_changes_function(self):
    """حفظ التغييرات"""
    QMessageBox.information(self, "تم", "تم حفظ التغييرات بنجاح")

def show_help_function(self):
    """عرض المساعدة"""
    help_text = """
🏗️ مساعدة أنواع الهيكل الإداري

📋 الوظائف المتاحة:

🏗️ أنواع الهياكل:
• إدارة أنواع الهياكل الإدارية المختلفة
• تحديد عدد المستويات لكل نوع
• تصنيف أنواع التسلسل (هرمي، مصفوفي، مسطح، وظيفي، شبكي)

👔 المناصب والوظائف:
• إدارة المناصب والوظائف في المؤسسة
• تحديد المستويات والأقسام
• إدارة الرواتب والمسؤوليات

📊 الهيكل التنظيمي:
• عرض مرئي للهيكل التنظيمي
• تسلسل هرمي واضح للمناصب
• تلوين حسب المستوى الإداري

📋 التقارير:
• تقرير أنواع الهياكل الإدارية
• تقرير المناصب والوظائف
• تقرير التسلسل الهرمي

💡 نصائح:
• استخدم الهيكل الهرمي للمؤسسات التقليدية
• الهيكل المصفوفي مناسب للمشاريع
• الهيكل المسطح يقلل البيروقراطية
• راجع التقارير بانتظام لتحليل الهيكل
"""
    
    QMessageBox.information(self, "المساعدة", help_text)
