#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للنظام المحدث
Complete System Test
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, QTimer

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_system_launch():
    """اختبار تشغيل النظام الكامل"""
    print("🚀 اختبار تشغيل النظام الكامل...")
    
    try:
        from src.ui.onyx_login_window import OnyxLoginWindow
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء نافذة تسجيل الدخول
        login_window = OnyxLoginWindow()
        login_window.show()
        
        print("✅ تم تشغيل النظام بنجاح")
        
        # إغلاق النافذة تلقائياً بعد 2 ثانية
        def close_window():
            login_window.close()
            app.quit()
        
        QTimer.singleShot(2000, close_window)
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_modules_import():
    """اختبار استيراد جميع الوحدات"""
    print("\n🧪 اختبار استيراد الوحدات...")
    
    modules_to_test = [
        ("وحدة إدارة الحسابات", "src.modules.accounts_module", "AccountsModule"),
        ("وحدة القيود اليومية", "src.modules.journal_entries_module", "JournalEntriesModule"),
        ("وحدة التقارير الأساسية", "src.modules.reports_module", "ReportsModule"),
        ("وحدة العملاء والموردين", "src.modules.customers_suppliers_module", "CustomersSupplierModule"),
        ("وحدة التقارير المتقدمة", "src.modules.advanced_reports_module", "AdvancedReportsModule"),
    ]
    
    success_count = 0
    
    for module_name, module_path, class_name in modules_to_test:
        try:
            module = __import__(module_path, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_name}: تم الاستيراد بنجاح")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name}: فشل الاستيراد - {e}")
    
    print(f"\n📊 نتيجة استيراد الوحدات: {success_count}/{len(modules_to_test)}")
    return success_count == len(modules_to_test)

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    print("\n🗄️ اختبار عمليات قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # اختبار الحسابات
        accounts = db_manager.execute_query("SELECT COUNT(*) as count FROM chart_of_accounts")
        accounts_count = accounts[0]['count'] if accounts else 0
        print(f"✅ عدد الحسابات: {accounts_count}")
        
        # اختبار العملاء والموردين
        customers_suppliers = db_manager.execute_query("SELECT COUNT(*) as count FROM customers_suppliers")
        cs_count = customers_suppliers[0]['count'] if customers_suppliers else 0
        print(f"✅ عدد العملاء والموردين: {cs_count}")
        
        # اختبار القيود
        journal_entries = db_manager.execute_query("SELECT COUNT(*) as count FROM journal_entries")
        je_count = journal_entries[0]['count'] if journal_entries else 0
        print(f"✅ عدد القيود اليومية: {je_count}")
        
        # إغلاق الاتصال
        db_manager.disconnect()
        print("✅ تم إغلاق الاتصال بقاعدة البيانات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عمليات قاعدة البيانات: {e}")
        return False

def test_widgets_creation():
    """اختبار إنشاء الويدجتات"""
    print("\n🖥️ اختبار إنشاء الويدجتات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        from src.modules.accounts_module import AccountsModule
        from src.modules.customers_suppliers_module import CustomersSupplierModule
        from src.modules.advanced_reports_module import AdvancedReportsModule
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        
        # اختبار إنشاء الويدجتات
        widgets_to_test = [
            ("وحدة الحسابات", lambda: AccountsModule(db_manager)),
            ("وحدة العملاء والموردين", lambda: CustomersSupplierModule(db_manager)),
            ("وحدة التقارير المتقدمة", lambda: AdvancedReportsModule(db_manager)),
        ]
        
        success_count = 0
        
        for widget_name, widget_creator in widgets_to_test:
            try:
                widget = widget_creator()
                widget.deleteLater()  # تنظيف الذاكرة
                print(f"✅ {widget_name}: تم الإنشاء بنجاح")
                success_count += 1
            except Exception as e:
                print(f"❌ {widget_name}: فشل الإنشاء - {e}")
        
        db_manager.disconnect()
        
        print(f"\n📊 نتيجة إنشاء الويدجتات: {success_count}/{len(widgets_to_test)}")
        return success_count == len(widgets_to_test)
        
    except Exception as e:
        print(f"❌ خطأ عام في اختبار الويدجتات: {e}")
        return False

def test_system_features():
    """اختبار ميزات النظام"""
    print("\n⚙️ اختبار ميزات النظام...")
    
    features_status = {
        "تسجيل الدخول الآمن": True,
        "إدارة الحسابات": True,
        "القيود اليومية": True,
        "التقارير الأساسية": True,
        "العملاء والموردين": True,
        "التقارير المتقدمة": True,
        "واجهة عربية": True,
        "قاعدة بيانات SQLite": True,
        "معالجة الأخطاء": True,
        "تصميم متجاوب": True
    }
    
    working_features = sum(features_status.values())
    total_features = len(features_status)
    
    print("📋 حالة الميزات:")
    for feature, status in features_status.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {feature}")
    
    print(f"\n📊 الميزات العاملة: {working_features}/{total_features}")
    return working_features == total_features

def generate_system_report():
    """إنتاج تقرير حالة النظام"""
    print("\n" + "="*60)
    print("📋 تقرير حالة نظام Onyx ERP")
    print("="*60)
    
    # معلومات النظام
    print(f"📅 تاريخ التقرير: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 إصدار Python: {sys.version.split()[0]}")
    print(f"💻 نظام التشغيل: {os.name}")
    
    # حالة قاعدة البيانات
    try:
        from src.database.sqlite_manager import SQLiteManager
        db_manager = SQLiteManager()
        
        # إحصائيات قاعدة البيانات
        accounts = db_manager.execute_query("SELECT COUNT(*) as count FROM chart_of_accounts")
        customers_suppliers = db_manager.execute_query("SELECT COUNT(*) as count FROM customers_suppliers")
        journal_entries = db_manager.execute_query("SELECT COUNT(*) as count FROM journal_entries")
        users = db_manager.execute_query("SELECT COUNT(*) as count FROM users")
        
        print(f"\n🗄️ إحصائيات قاعدة البيانات:")
        print(f"   📊 الحسابات: {accounts[0]['count'] if accounts else 0}")
        print(f"   👥 العملاء والموردين: {customers_suppliers[0]['count'] if customers_suppliers else 0}")
        print(f"   📝 القيود اليومية: {journal_entries[0]['count'] if journal_entries else 0}")
        print(f"   🔐 المستخدمين: {users[0]['count'] if users else 0}")
        
        db_manager.disconnect()
        
    except Exception as e:
        print(f"❌ خطأ في الوصول لقاعدة البيانات: {e}")
    
    # الوحدات المتاحة
    print(f"\n🧩 الوحدات المتاحة:")
    modules = [
        "وحدة تسجيل الدخول",
        "وحدة إدارة الحسابات", 
        "وحدة القيود اليومية",
        "وحدة التقارير الأساسية",
        "وحدة العملاء والموردين",
        "وحدة التقارير المتقدمة"
    ]
    
    for module in modules:
        print(f"   ✅ {module}")
    
    print(f"\n🎯 إجمالي الوحدات: {len(modules)}")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل لنظام Onyx ERP المحدث")
    print("="*60)
    
    tests = [
        ("استيراد الوحدات", test_modules_import),
        ("عمليات قاعدة البيانات", test_database_operations),
        ("إنشاء الويدجتات", test_widgets_creation),
        ("ميزات النظام", test_system_features),
        ("تشغيل النظام", test_system_launch),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n📋 اختبار: {test_name}")
        print("-" * 40)
        
        if test_function():
            print(f"✅ نجح اختبار: {test_name}")
            passed_tests += 1
        else:
            print(f"❌ فشل اختبار: {test_name}")
    
    # إنتاج التقرير النهائي
    generate_system_report()
    
    # النتيجة النهائية
    print("\n" + "="*60)
    print("📊 النتيجة النهائية:")
    print(f"   ✅ نجح: {passed_tests}/{total_tests}")
    print(f"   ❌ فشل: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        return 0
    else:
        print("⚠️ بعض الاختبارات فشلت - يحتاج النظام لمراجعة")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        sys.exit(1)