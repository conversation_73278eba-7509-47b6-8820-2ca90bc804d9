#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف البيانات لشاشة تعريف الفرع
Data Functions for Branch Definition
"""

from PyQt5.QtWidgets import QMessageBox
from datetime import datetime

def load_data_function(self):
    """تحميل البيانات المحفوظة"""
    try:
        # تحميل قوائم البيانات
        self.load_locations()
        self.load_currencies()
        self.load_managers()
        
        # تحميل البيانات النموذجية
        self.load_sample_branch_data()
        
    except Exception as e:
        print(f"خطأ في تحميل البيانات: {e}")

def load_locations_function(self):
    """تحميل قائمة المواقع الجغرافية"""
    # الدول
    countries = ["الجمهورية اليمنية", "المملكة العربية السعودية", "دولة الإمارات العربية المتحدة"]
    self.country.clear()
    for country in countries:
        self.country.addItem(country)
    self.country.setCurrentText("الجمهورية اليمنية")
    
    # المحافظات اليمنية
    governorates = [
        "محافظة صنعاء", "محافظة عدن", "محافظة تعز", "محافظة الحديدة",
        "محافظة إب", "محافظة ذمار", "محافظة حضرموت", "محافظة المهرة",
        "محافظة شبوة", "محافظة أبين", "محافظة لحج", "محافظة الضالع"
    ]
    
    self.governorate.clear()
    for gov in governorates:
        self.governorate.addItem(gov)
    
    # المدن الرئيسية
    cities = [
        "مدينة صنعاء", "مدينة عدن", "مدينة تعز", "مدينة الحديدة",
        "مدينة إب", "مدينة ذمار", "مدينة المكلا", "مدينة الغيضة",
        "مدينة عتق", "مدينة زنجبار", "مدينة الحوطة", "مدينة الضالع"
    ]
    
    self.city.clear()
    for city in cities:
        self.city.addItem(city)
    
    # المناطق/المديريات
    districts = [
        "مديرية الثورة", "مديرية الصافية", "مديرية شعوب", "مديرية معين",
        "مديرية كريتر", "مديرية المعلا", "مديرية صيرة", "مديرية البريقة",
        "مديرية صالة", "مديرية الحوك", "مديرية الميناء", "مديرية التحرير"
    ]
    
    self.district.clear()
    for district in districts:
        self.district.addItem(district)

def load_currencies_function(self):
    """تحميل قائمة العملات"""
    currencies = [
        "الريال اليمني (YER)", "الدولار الأمريكي (USD)", "الريال السعودي (SAR)",
        "الدرهم الإماراتي (AED)", "اليورو (EUR)", "الجنيه الإسترليني (GBP)"
    ]
    
    for combo in [self.base_currency, self.secondary_currency]:
        combo.clear()
        for currency in currencies:
            combo.addItem(currency)
    
    # تعيين العملات الافتراضية
    self.base_currency.setCurrentText("الريال اليمني (YER)")
    self.secondary_currency.setCurrentText("الدولار الأمريكي (USD)")

def load_managers_function(self):
    """تحميل قائمة المديرين والموظفين"""
    managers = [
        "أحمد محمد علي الشامي", "فاطمة أحمد سالم الحميري", "محمد عبدالله حسن الزبيري",
        "عائشة علي محمد الهاشمي", "عبدالرحمن سعيد أحمد العولقي", "زينب محمد عبدالله الأهدل",
        "سعيد أحمد علي الحضرمي", "مريم عبدالله سالم التهامي", "علي محمد حسن الصنعاني",
        "خديجة سالم أحمد العدني", "يوسف محمد علي التعزي", "آمنة أحمد سعيد الإبي"
    ]
    
    # تحديث جميع قوائم المديرين
    for combo in [self.branch_manager, self.deputy_manager]:
        combo.clear()
        for manager in managers:
            combo.addItem(manager)

def load_sample_branch_data_function(self):
    """تحميل البيانات النموذجية للفرع"""
    # بيانات فرع صنعاء الرئيسي كمثال
    self.branch_name.setText("فرع صنعاء الرئيسي")
    self.branch_name_en.setText("Sana'a Main Branch")
    self.branch_code.setText("SAN-001")
    self.branch_type.setCurrentText("فرع رئيسي")
    
    # الموقع
    self.governorate.setCurrentText("محافظة صنعاء")
    self.city.setCurrentText("مدينة صنعاء")
    self.district.setCurrentText("مديرية الثورة")
    self.detailed_address.setText("شارع الزبيري، مجمع الأعمال التجاري، الطابق الثالث")
    self.postal_code.setText("12345")
    self.po_box.setText("P.O. Box 1234")
    
    # الاتصال
    self.main_phone.setText("+967-1-123456")
    self.secondary_phone.setText("+967-1-654321")
    self.fax.setText("+967-1-789012")
    self.mobile.setText("+967-77-1234567")
    self.email.setText("<EMAIL>")
    self.website.setText("www.company.com/sanaa")
    
    # المديرين
    self.branch_manager.setCurrentText("أحمد محمد علي الشامي")
    self.deputy_manager.setCurrentText("فاطمة أحمد سالم الحميري")
    
    # الوصف
    self.branch_description.setPlainText(
        "الفرع الرئيسي للشركة في العاصمة صنعاء، يقدم جميع الخدمات المصرفية والتجارية "
        "للعملاء في المحافظة ويعتبر المقر الإداري الرئيسي للعمليات."
    )
    
    # الإعدادات المالية
    self.daily_sales_limit.setValue(500000)
    self.daily_purchase_limit.setValue(300000)
    self.cash_limit.setValue(100000)
    self.credit_limit.setValue(1000000)
    self.sales_tax_rate.setValue(15.0)
    self.tax_registration.setText("*********")
    
    # السعة والمرافق
    self.branch_capacity.setValue(75)
    self.floors_count.setValue(3)
    self.total_area.setValue(1200)
    self.offices_count.setValue(25)
    self.waiting_seats.setValue(50)

def locate_on_map_function(self):
    """تحديد الموقع على الخريطة"""
    QMessageBox.information(self, "قيد التطوير", 
                           "📍 ميزة تحديد الموقع على الخريطة قيد التطوير\n"
                           "سيتم إضافتها في الإصدارات القادمة\n\n"
                           "الميزات المخططة:\n"
                           "• خريطة تفاعلية\n"
                           "• تحديد الإحداثيات بالنقر\n"
                           "• عرض الموقع الحالي\n"
                           "• حفظ المواقع المفضلة")

def preview_branch_definition_function(self):
    """معاينة تعريف الفرع"""
    try:
        # جمع البيانات الأساسية
        basic_info = f"""
📋 معاينة تعريف الفرع
{'='*50}

🏢 المعلومات الأساسية:
   • اسم الفرع: {self.branch_name.text()}
   • الاسم بالإنجليزية: {self.branch_name_en.text()}
   • رمز الفرع: {self.branch_code.text()}
   • نوع الفرع: {self.branch_type.currentText()}
   • مدير الفرع: {self.branch_manager.currentText()}
   • نائب المدير: {self.deputy_manager.currentText()}

📍 الموقع والاتصال:
   • المحافظة: {self.governorate.currentText()}
   • المدينة: {self.city.currentText()}
   • المنطقة: {self.district.currentText()}
   • العنوان: {self.detailed_address.text()}
   • الهاتف الرئيسي: {self.main_phone.text()}
   • البريد الإلكتروني: {self.email.text()}

💰 الإعدادات المالية:
   • العملة الأساسية: {self.base_currency.currentText()}
   • حد المبيعات اليومي: {self.daily_sales_limit.value():,.0f} ريال
   • حد المشتريات اليومي: {self.daily_purchase_limit.value():,.0f} ريال
   • حد الصندوق: {self.cash_limit.value():,.0f} ريال
   • معدل ضريبة المبيعات: {self.sales_tax_rate.value()}%

🏗️ المرافق والتجهيزات:
   • عدد الطوابق: {self.floors_count.value()}
   • المساحة الإجمالية: {self.total_area.value()} متر مربع
   • عدد المكاتب: {self.offices_count.value()}
   • سعة الفرع: {self.branch_capacity.value()} موظف

⚙️ الحالة والإعدادات:
   • فرع نشط: {'نعم' if self.branch_active.isChecked() else 'لا'}
   • فرع رئيسي: {'نعم' if self.is_main_branch.isChecked() else 'لا'}
   • يقبل المعاملات المالية: {'نعم' if self.accepts_transactions.isChecked() else 'لا'}
   • يقبل عمليات البيع: {'نعم' if self.accepts_sales.isChecked() else 'لا'}
   • مستوى الأولوية: {self.priority_level.currentText()}
"""
        
        # عرض المعاينة
        preview_dialog = QMessageBox(self)
        preview_dialog.setWindowTitle("معاينة تعريف الفرع")
        preview_dialog.setText(basic_info)
        preview_dialog.setIcon(QMessageBox.Information)
        preview_dialog.exec_()
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في إنتاج المعاينة:\n{str(e)}")

def show_help_function(self):
    """عرض المساعدة"""
    help_text = """
🏢 مساعدة تعريف الفرع

📋 الوظائف المتاحة:

📝 المعلومات الأساسية:
• تعريف اسم الفرع ورمزه
• تحديد نوع الفرع ومديره
• إدخال وصف مختصر للفرع
• تحديد حالة الفرع (نشط/غير نشط)

📍 الموقع والاتصال:
• تحديد الموقع الجغرافي بالتفصيل
• إدخال معلومات الاتصال الكاملة
• تحديد الإحداثيات الجغرافية
• ربط الفرع بالخريطة

💰 الإعدادات المالية:
• تحديد العملات المستخدمة
• تعيين الحدود المالية اليومية
• إعداد معدلات الضرائب
• تحديد أرقام التسجيل الضريبي

⏰ أوقات العمل:
• تحديد أيام وساعات العمل
• إعداد فترات الاستراحة
• تحديد العطل والإجازات
• تخصيص جدول العمل

🛎️ الخدمات والمرافق:
• تحديد الخدمات المتاحة
• إدراج المرافق والتجهيزات
• تحديد المساحات والسعات
• وصف الإمكانيات المتاحة

🔒 الأمان والصلاحيات:
• تحديد مستوى الأمان
• إعداد صلاحيات الوصول
• تفعيل أنظمة المراقبة
• إعداد النسخ الاحتياطي

💡 نصائح:
• تأكد من صحة جميع البيانات قبل الحفظ
• استخدم رموز واضحة ومميزة للفروع
• حدد الحدود المالية بعناية
• راجع الصلاحيات والأمان بانتظام
• احتفظ بنسخ احتياطية من التعريفات
"""
    
    QMessageBox.information(self, "المساعدة", help_text)

def save_branch_definition_function(self):
    """حفظ تعريف الفرع"""
    try:
        # التحقق من صحة البيانات الأساسية
        if not self.branch_name.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال اسم الفرع")
            return
            
        if not self.branch_code.text().strip():
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رمز الفرع")
            return
            
        if not self.branch_manager.currentText().strip():
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مدير الفرع")
            return
        
        # جمع جميع البيانات
        branch_data = {
            # المعلومات الأساسية
            'name': self.branch_name.text(),
            'name_en': self.branch_name_en.text(),
            'code': self.branch_code.text(),
            'type': self.branch_type.currentText(),
            'establishment_date': self.establishment_date.date().toString(),
            'start_date': self.start_date.date().toString(),
            'manager': self.branch_manager.currentText(),
            'deputy_manager': self.deputy_manager.currentText(),
            'description': self.branch_description.toPlainText(),
            
            # الحالة والإعدادات
            'active': self.branch_active.isChecked(),
            'is_main': self.is_main_branch.isChecked(),
            'accepts_transactions': self.accepts_transactions.isChecked(),
            'accepts_sales': self.accepts_sales.isChecked(),
            'accepts_purchases': self.accepts_purchases.isChecked(),
            'accepts_transfers': self.accepts_transfers.isChecked(),
            'priority_level': self.priority_level.currentText(),
            'capacity': self.branch_capacity.value(),
            
            # الموقع والاتصال
            'country': self.country.currentText(),
            'governorate': self.governorate.currentText(),
            'city': self.city.currentText(),
            'district': self.district.currentText(),
            'address': self.detailed_address.text(),
            'postal_code': self.postal_code.text(),
            'po_box': self.po_box.text(),
            'main_phone': self.main_phone.text(),
            'secondary_phone': self.secondary_phone.text(),
            'fax': self.fax.text(),
            'mobile': self.mobile.text(),
            'email': self.email.text(),
            'website': self.website.text(),
            'longitude': self.longitude.value(),
            'latitude': self.latitude.value(),
            
            # الإعدادات المالية
            'base_currency': self.base_currency.currentText(),
            'secondary_currency': self.secondary_currency.currentText(),
            'exchange_rate': self.exchange_rate.value(),
            'auto_update_rates': self.auto_update_rates.isChecked(),
            'daily_sales_limit': self.daily_sales_limit.value(),
            'daily_purchase_limit': self.daily_purchase_limit.value(),
            'cash_limit': self.cash_limit.value(),
            'credit_limit': self.credit_limit.value(),
            'sales_tax_rate': self.sales_tax_rate.value(),
            'tax_registration': self.tax_registration.text(),
            'subject_to_tax': self.subject_to_tax.isChecked(),
            'applies_vat': self.applies_vat.isChecked(),
            
            # المرافق والتجهيزات
            'floors_count': self.floors_count.value(),
            'total_area': self.total_area.value(),
            'offices_count': self.offices_count.value(),
            'waiting_seats': self.waiting_seats.value(),
            
            # الأمان
            'security_level': self.security_level.currentText(),
            'surveillance_system': self.surveillance_system.isChecked(),
            'alarm_system': self.alarm_system.isChecked(),
            'security_guard': self.security_guard.isChecked(),
            'auto_backup': self.auto_backup.isChecked(),
            'backup_frequency': self.backup_frequency.currentText(),
            'backup_location': self.backup_location.currentText(),
        }
        
        # حفظ في قاعدة البيانات
        # هنا يتم حفظ البيانات في قاعدة البيانات
        
        summary = f"""✅ تم حفظ تعريف الفرع بنجاح!

🏢 الفرع: {branch_data['name']} ({branch_data['code']})
📍 الموقع: {branch_data['governorate']} - {branch_data['city']}
👤 المدير: {branch_data['manager']}
💰 حد المبيعات اليومي: {branch_data['daily_sales_limit']:,.0f} ريال
🏗️ المساحة: {branch_data['total_area']} متر مربع
👥 السعة: {branch_data['capacity']} موظف

📊 تم حفظ جميع الإعدادات والتفاصيل بنجاح"""
        
        QMessageBox.information(self, "نجح الحفظ", summary)
        self.accept()
        
    except Exception as e:
        QMessageBox.critical(self, "خطأ", f"فشل في حفظ تعريف الفرع:\n{str(e)}")
