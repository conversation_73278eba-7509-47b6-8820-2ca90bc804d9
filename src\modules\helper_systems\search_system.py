#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام البحث المتقدم
Advanced Search System
"""

import sys
import os
import re
from datetime import datetime, date
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                             QLabel, QLineEdit, QPushButton, QGroupBox, 
                             QTextEdit, QTabWidget, QWidget, QComboBox,
                             QApplication, QMessageBox, QListWidget,
                             QListWidgetItem, QCheckBox, QSpinBox,
                             QDateEdit, QProgressBar, QTableWidget,
                             QTableWidgetItem, QHeaderView, QSplitter,
                             QTreeWidget, QTreeWidgetItem, QRadioButton,
                             QButtonGroup, QSlider, QFrame)
from PyQt5.QtCore import Qt, QDate, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QIcon

class SearchWorker(QThread):
    """خيط البحث في الخلفية"""
    progress_updated = pyqtSignal(int)
    result_found = pyqtSignal(dict)
    search_completed = pyqtSignal(list)
    
    def __init__(self, search_params, data_sources):
        super().__init__()
        self.search_params = search_params
        self.data_sources = data_sources
        self.is_running = True
        
    def run(self):
        """تشغيل البحث"""
        results = []
        total_sources = len(self.data_sources)
        
        for i, source in enumerate(self.data_sources):
            if not self.is_running:
                break
                
            # محاكاة البحث في مصدر البيانات
            source_results = self.search_in_source(source)
            results.extend(source_results)
            
            # تحديث التقدم
            progress = int((i + 1) / total_sources * 100)
            self.progress_updated.emit(progress)
            
            # إرسال النتائج المؤقتة
            for result in source_results:
                self.result_found.emit(result)
        
        self.search_completed.emit(results)
    
    def search_in_source(self, source):
        """البحث في مصدر بيانات محدد"""
        results = []
        search_term = self.search_params.get('term', '').lower()
        
        # محاكاة بيانات للبحث
        sample_data = self.get_sample_data(source)
        
        for item in sample_data:
            if self.matches_criteria(item, search_term):
                results.append({
                    'source': source,
                    'title': item.get('title', ''),
                    'content': item.get('content', ''),
                    'type': item.get('type', ''),
                    'date': item.get('date', ''),
                    'relevance': self.calculate_relevance(item, search_term)
                })
        
        return results
    
    def get_sample_data(self, source):
        """الحصول على بيانات نموذجية للبحث"""
        if source == "العملاء":
            return [
                {'title': 'أحمد محمد الشامي', 'content': 'عميل مميز من صنعاء', 'type': 'عميل', 'date': '2024-01-15'},
                {'title': 'فاطمة سالم أحمد', 'content': 'عميل VIP من عدن', 'type': 'عميل', 'date': '2024-01-20'},
                {'title': 'محمد عبدالله الحوثي', 'content': 'عميل عادي من صعدة', 'type': 'عميل', 'date': '2024-01-25'},
            ]
        elif source == "المنتجات":
            return [
                {'title': 'لابتوب ديل', 'content': 'جهاز كمبيوتر محمول عالي الأداء', 'type': 'منتج', 'date': '2024-01-10'},
                {'title': 'طابعة HP', 'content': 'طابعة ليزر ملونة', 'type': 'منتج', 'date': '2024-01-12'},
                {'title': 'ماوس لاسلكي', 'content': 'ماوس بتقنية البلوتوث', 'type': 'منتج', 'date': '2024-01-14'},
            ]
        elif source == "الفواتير":
            return [
                {'title': 'فاتورة رقم 001', 'content': 'فاتورة بيع للعميل أحمد الشامي', 'type': 'فاتورة', 'date': '2024-01-16'},
                {'title': 'فاتورة رقم 002', 'content': 'فاتورة شراء من المورد الرئيسي', 'type': 'فاتورة', 'date': '2024-01-18'},
                {'title': 'فاتورة رقم 003', 'content': 'فاتورة خدمات استشارية', 'type': 'فاتورة', 'date': '2024-01-20'},
            ]
        elif source == "التقارير":
            return [
                {'title': 'تقرير المبيعات الشهري', 'content': 'تقرير شامل عن مبيعات يناير', 'type': 'تقرير', 'date': '2024-01-31'},
                {'title': 'تقرير المخزون', 'content': 'حالة المخزون الحالية', 'type': 'تقرير', 'date': '2024-01-30'},
                {'title': 'تقرير الأرباح', 'content': 'تحليل الأرباح والخسائر', 'type': 'تقرير', 'date': '2024-01-29'},
            ]
        else:
            return [
                {'title': f'عنصر من {source}', 'content': f'محتوى نموذجي من {source}', 'type': 'عام', 'date': '2024-01-15'}
            ]
    
    def matches_criteria(self, item, search_term):
        """فحص مطابقة العنصر لمعايير البحث"""
        if not search_term:
            return True
            
        title_match = search_term in item.get('title', '').lower()
        content_match = search_term in item.get('content', '').lower()
        
        return title_match or content_match
    
    def calculate_relevance(self, item, search_term):
        """حساب درجة الصلة"""
        relevance = 0
        
        if search_term in item.get('title', '').lower():
            relevance += 10
        if search_term in item.get('content', '').lower():
            relevance += 5
            
        return relevance
    
    def stop(self):
        """إيقاف البحث"""
        self.is_running = False

class AdvancedSearchDialog(QDialog):
    """حوار البحث المتقدم"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.search_worker = None
        self.search_results = []
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔍 البحث المتقدم")
        self.setGeometry(200, 200, 1000, 700)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("🔍 البحث المتقدم - بحث شامل في جميع بيانات النظام")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        
        # منطقة البحث
        search_area = self.create_search_area()
        
        # منطقة النتائج
        results_area = self.create_results_area()
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        
        # أزرار التحكم
        buttons_layout = self.create_control_buttons()
        
        main_layout.addWidget(title_label)
        main_layout.addWidget(search_area)
        main_layout.addWidget(self.progress_bar)
        main_layout.addWidget(results_area)
        main_layout.addLayout(buttons_layout)
        
        self.setLayout(main_layout)
        
    def create_search_area(self):
        """إنشاء منطقة البحث"""
        search_group = QGroupBox("🔍 معايير البحث")
        search_layout = QGridLayout()
        
        # مربع البحث الرئيسي
        search_layout.addWidget(QLabel("كلمة البحث:"), 0, 0)
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ادخل كلمة أو عبارة للبحث...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        search_layout.addWidget(self.search_input, 0, 1, 1, 2)
        
        # زر البحث السريع
        quick_search_btn = QPushButton("🔍 بحث سريع")
        quick_search_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        quick_search_btn.clicked.connect(self.quick_search)
        search_layout.addWidget(quick_search_btn, 0, 3)
        
        # مصادر البيانات
        search_layout.addWidget(QLabel("البحث في:"), 1, 0)
        
        self.data_sources = QGroupBox("مصادر البيانات")
        sources_layout = QHBoxLayout()
        
        self.source_checkboxes = {}
        sources = ["العملاء", "المنتجات", "الفواتير", "التقارير", "المخزون", "الحسابات"]
        
        for source in sources:
            checkbox = QCheckBox(source)
            checkbox.setChecked(True)
            self.source_checkboxes[source] = checkbox
            sources_layout.addWidget(checkbox)
        
        self.data_sources.setLayout(sources_layout)
        search_layout.addWidget(self.data_sources, 1, 1, 1, 3)
        
        # نوع البحث
        search_layout.addWidget(QLabel("نوع البحث:"), 2, 0)
        
        self.search_type = QComboBox()
        self.search_type.addItems([
            "بحث عام", "بحث دقيق", "بحث بالعبارة الكاملة", 
            "بحث بالتعبير النمطي", "بحث صوتي"
        ])
        search_layout.addWidget(self.search_type, 2, 1)
        
        # فلاتر التاريخ
        search_layout.addWidget(QLabel("من تاريخ:"), 2, 2)
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        search_layout.addWidget(self.date_from, 2, 3)
        
        search_layout.addWidget(QLabel("إلى تاريخ:"), 3, 0)
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        search_layout.addWidget(self.date_to, 3, 1)
        
        # زر البحث المتقدم
        advanced_search_btn = QPushButton("🔍 بحث متقدم")
        advanced_search_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        advanced_search_btn.clicked.connect(self.advanced_search)
        search_layout.addWidget(advanced_search_btn, 3, 2, 1, 2)
        
        search_group.setLayout(search_layout)
        return search_group

    def create_results_area(self):
        """إنشاء منطقة النتائج"""
        results_splitter = QSplitter(Qt.Horizontal)

        # قائمة النتائج
        results_group = QGroupBox("📋 نتائج البحث")
        results_layout = QVBoxLayout()

        # معلومات النتائج
        self.results_info = QLabel("جاهز للبحث...")
        self.results_info.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #ecf0f1;
                border-radius: 5px;
                font-weight: bold;
            }
        """)

        # جدول النتائج
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(6)
        self.results_table.setHorizontalHeaderLabels([
            "العنوان", "النوع", "المصدر", "التاريخ", "الصلة", "المحتوى"
        ])

        # تنسيق الجدول
        self.results_table.horizontalHeader().setStretchLastSection(True)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.results_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                font-size: 12px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)

        # ربط إشارة النقر
        self.results_table.itemClicked.connect(self.on_result_selected)

        results_layout.addWidget(self.results_info)
        results_layout.addWidget(self.results_table)
        results_group.setLayout(results_layout)

        # تفاصيل النتيجة المحددة
        details_group = QGroupBox("📄 تفاصيل النتيجة")
        details_layout = QVBoxLayout()

        self.result_details = QTextEdit()
        self.result_details.setReadOnly(True)
        self.result_details.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-size: 12px;
            }
        """)
        self.result_details.setPlainText("حدد نتيجة من القائمة لعرض التفاصيل...")

        details_layout.addWidget(self.result_details)
        details_group.setLayout(details_layout)

        # إضافة المجموعات للمقسم
        results_splitter.addWidget(results_group)
        results_splitter.addWidget(details_group)
        results_splitter.setSizes([600, 400])

        return results_splitter

    def create_control_buttons(self):
        """إنشاء أزرار التحكم"""
        layout = QHBoxLayout()

        # زر مسح النتائج
        clear_btn = QPushButton("🗑️ مسح النتائج")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        clear_btn.clicked.connect(self.clear_results)

        # زر تصدير النتائج
        export_btn = QPushButton("📤 تصدير النتائج")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        export_btn.clicked.connect(self.export_results)

        # زر المساعدة
        help_btn = QPushButton("❓ المساعدة")
        help_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        help_btn.clicked.connect(self.show_help)

        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        close_btn.clicked.connect(self.reject)

        layout.addWidget(clear_btn)
        layout.addWidget(export_btn)
        layout.addWidget(help_btn)
        layout.addStretch()
        layout.addWidget(close_btn)

        return layout

    # الوظائف الأساسية
    def quick_search(self):
        """البحث السريع"""
        search_term = self.search_input.text().strip()
        if not search_term:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كلمة للبحث")
            return

        self.perform_search(search_term, quick=True)

    def advanced_search(self):
        """البحث المتقدم"""
        search_term = self.search_input.text().strip()
        if not search_term:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كلمة للبحث")
            return

        self.perform_search(search_term, quick=False)

    def perform_search(self, search_term, quick=False):
        """تنفيذ البحث"""
        # إعداد معايير البحث
        search_params = {
            'term': search_term,
            'type': self.search_type.currentText(),
            'date_from': self.date_from.date().toString('yyyy-MM-dd'),
            'date_to': self.date_to.date().toString('yyyy-MM-dd'),
            'quick': quick
        }

        # تحديد مصادر البيانات المحددة
        selected_sources = []
        for source, checkbox in self.source_checkboxes.items():
            if checkbox.isChecked():
                selected_sources.append(source)

        if not selected_sources:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد مصدر واحد على الأقل للبحث")
            return

        # مسح النتائج السابقة
        self.clear_results()

        # إظهار شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # تحديث معلومات البحث
        self.results_info.setText(f"🔍 جاري البحث عن: '{search_term}' في {len(selected_sources)} مصدر...")

        # بدء البحث في خيط منفصل
        self.search_worker = SearchWorker(search_params, selected_sources)
        self.search_worker.progress_updated.connect(self.update_progress)
        self.search_worker.result_found.connect(self.add_result)
        self.search_worker.search_completed.connect(self.search_finished)
        self.search_worker.start()

    def update_progress(self, value):
        """تحديث شريط التقدم"""
        self.progress_bar.setValue(value)

    def add_result(self, result):
        """إضافة نتيجة للجدول"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(result['title']))
        self.results_table.setItem(row, 1, QTableWidgetItem(result['type']))
        self.results_table.setItem(row, 2, QTableWidgetItem(result['source']))
        self.results_table.setItem(row, 3, QTableWidgetItem(result['date']))
        self.results_table.setItem(row, 4, QTableWidgetItem(str(result['relevance'])))
        self.results_table.setItem(row, 5, QTableWidgetItem(result['content']))

        # حفظ النتيجة
        self.search_results.append(result)

    def search_finished(self, results):
        """انتهاء البحث"""
        self.progress_bar.setVisible(False)

        total_results = len(results)
        if total_results > 0:
            self.results_info.setText(f"✅ تم العثور على {total_results} نتيجة")

            # ترتيب النتائج حسب الصلة
            self.results_table.sortItems(4, Qt.DescendingOrder)
        else:
            self.results_info.setText("❌ لم يتم العثور على نتائج")

    def on_result_selected(self, item):
        """عند تحديد نتيجة"""
        row = item.row()
        if row < len(self.search_results):
            result = self.search_results[row]

            details_text = f"""
📄 تفاصيل النتيجة:

🏷️ العنوان: {result['title']}
📂 النوع: {result['type']}
🗂️ المصدر: {result['source']}
📅 التاريخ: {result['date']}
⭐ درجة الصلة: {result['relevance']}

📝 المحتوى:
{result['content']}

🔍 معلومات إضافية:
• تم العثور على هذه النتيجة في مصدر البيانات: {result['source']}
• درجة الصلة تعتمد على مطابقة كلمات البحث
• يمكنك النقر المزدوج لفتح العنصر (إذا كان متاحاً)
            """

            self.result_details.setPlainText(details_text.strip())

    def clear_results(self):
        """مسح النتائج"""
        self.results_table.setRowCount(0)
        self.search_results.clear()
        self.result_details.setPlainText("حدد نتيجة من القائمة لعرض التفاصيل...")
        self.results_info.setText("جاهز للبحث...")

    def export_results(self):
        """تصدير النتائج"""
        if not self.search_results:
            QMessageBox.information(self, "تنبيه", "لا توجد نتائج للتصدير")
            return

        try:
            from PyQt5.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self, "تصدير نتائج البحث",
                f"search_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv);;Text Files (*.txt);;All Files (*)"
            )

            if file_path:
                import csv
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['العنوان', 'النوع', 'المصدر', 'التاريخ', 'الصلة', 'المحتوى']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()

                    for result in self.search_results:
                        writer.writerow({
                            'العنوان': result['title'],
                            'النوع': result['type'],
                            'المصدر': result['source'],
                            'التاريخ': result['date'],
                            'الصلة': result['relevance'],
                            'المحتوى': result['content']
                        })

                QMessageBox.information(self, "نجح", f"تم تصدير النتائج إلى:\n{file_path}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تصدير النتائج:\n{str(e)}")

    def show_help(self):
        """عرض المساعدة"""
        help_text = """
🔍 مساعدة نظام البحث المتقدم

📋 الميزات المتاحة:

🔍 أنواع البحث:
• بحث عام: بحث في جميع الحقول
• بحث دقيق: مطابقة تامة للكلمات
• بحث بالعبارة الكاملة: البحث عن العبارة كما هي
• بحث بالتعبير النمطي: استخدام أنماط متقدمة
• بحث صوتي: البحث بالنطق المشابه

📂 مصادر البيانات:
• العملاء: بيانات العملاء والزبائن
• المنتجات: كتالوج المنتجات والخدمات
• الفواتير: فواتير البيع والشراء
• التقارير: جميع التقارير المالية
• المخزون: بيانات المخزون والمستودعات
• الحسابات: الحسابات المالية والمحاسبية

🎯 نصائح البحث:
• استخدم كلمات مفتاحية واضحة
• حدد مصادر البيانات المناسبة
• استخدم فلاتر التاريخ لتضييق النتائج
• راجع درجة الصلة لترتيب النتائج

⚡ البحث السريع:
• بحث فوري في جميع المصادر المحددة
• نتائج سريعة للاستعلامات البسيطة

🔍 البحث المتقدم:
• بحث شامل مع جميع المعايير
• نتائج مفصلة مع درجات الصلة
• إمكانية تصدير النتائج

📊 النتائج:
• ترتيب تلقائي حسب درجة الصلة
• تفاصيل شاملة لكل نتيجة
• إمكانية تصدير إلى CSV

💡 اختصارات لوحة المفاتيح:
• Enter: تنفيذ البحث السريع
• Ctrl+F: التركيز على مربع البحث
• Ctrl+E: تصدير النتائج
• Escape: إغلاق النافذة
        """

        QMessageBox.information(self, "المساعدة", help_text)


def main():
    """اختبار نظام البحث المتقدم"""
    import sys

    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    dialog = AdvancedSearchDialog()
    dialog.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
