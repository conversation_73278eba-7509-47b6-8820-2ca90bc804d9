#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف الآلة الحاسبة المتقدمة
Advanced Calculator Functions
"""

import math
from datetime import datetime
from PyQt5.QtWidgets import QMessageBox, QFileDialog

def button_clicked_function(self, text):
    """معالجة النقر على أزرار الحاسبة الأساسية"""
    current_display = self.display.text()
    
    if text.isdigit() or text == '.':
        # أرقام ونقطة عشرية
        if current_display == "0" and text != '.':
            self.display.setText(text)
        else:
            self.display.setText(current_display + text)
    elif text in ['÷', '×', '-', '+']:
        # عمليات حسابية
        self.current_input = current_display
        self.operation = text
        self.display.setText("0")
    elif text == '=':
        # حساب النتيجة
        try:
            if self.operation and self.current_input:
                second_operand = current_display
                result = self.calculate_result(self.current_input, second_operand, self.operation)
                
                # إضافة إلى التاريخ
                operation_text = f"{self.current_input} {self.operation} {second_operand} = {result}"
                self.add_to_history(operation_text)
                
                self.display.setText(str(result))
                self.current_input = ""
                self.operation = ""
        except Exception as e:
            self.display.setText("خطأ")
            QMessageBox.warning(self, "خطأ", f"خطأ في العملية الحسابية: {str(e)}")
    elif text == 'C':
        # مسح
        self.display.setText("0")
        self.current_input = ""
        self.operation = ""
    elif text == '±':
        # تغيير الإشارة
        if current_display != "0":
            if current_display.startswith('-'):
                self.display.setText(current_display[1:])
            else:
                self.display.setText('-' + current_display)
    elif text == '%':
        # نسبة مئوية
        try:
            value = float(current_display) / 100
            self.display.setText(str(value))
        except:
            self.display.setText("خطأ")

def scientific_button_clicked_function(self, text):
    """معالجة النقر على أزرار الحاسبة العلمية"""
    current_display = self.scientific_display.text()
    
    try:
        if text.isdigit() or text == '.':
            # أرقام ونقطة عشرية
            if current_display == "0" and text != '.':
                self.scientific_display.setText(text)
            else:
                self.scientific_display.setText(current_display + text)
        elif text == 'sin':
            value = math.sin(math.radians(float(current_display)))
            self.scientific_display.setText(str(value))
            self.add_to_history(f"sin({current_display}) = {value}")
        elif text == 'cos':
            value = math.cos(math.radians(float(current_display)))
            self.scientific_display.setText(str(value))
            self.add_to_history(f"cos({current_display}) = {value}")
        elif text == 'tan':
            value = math.tan(math.radians(float(current_display)))
            self.scientific_display.setText(str(value))
            self.add_to_history(f"tan({current_display}) = {value}")
        elif text == 'log':
            value = math.log10(float(current_display))
            self.scientific_display.setText(str(value))
            self.add_to_history(f"log({current_display}) = {value}")
        elif text == 'ln':
            value = math.log(float(current_display))
            self.scientific_display.setText(str(value))
            self.add_to_history(f"ln({current_display}) = {value}")
        elif text == 'x²':
            value = float(current_display) ** 2
            self.scientific_display.setText(str(value))
            self.add_to_history(f"({current_display})² = {value}")
        elif text == 'x³':
            value = float(current_display) ** 3
            self.scientific_display.setText(str(value))
            self.add_to_history(f"({current_display})³ = {value}")
        elif text == '√':
            value = math.sqrt(float(current_display))
            self.scientific_display.setText(str(value))
            self.add_to_history(f"√{current_display} = {value}")
        elif text == '∛':
            value = float(current_display) ** (1/3)
            self.scientific_display.setText(str(value))
            self.add_to_history(f"∛{current_display} = {value}")
        elif text == 'π':
            self.scientific_display.setText(str(math.pi))
        elif text == 'e':
            self.scientific_display.setText(str(math.e))
        elif text == 'C':
            self.scientific_display.setText("0")
        elif text == 'AC':
            self.scientific_display.setText("0")
            self.current_input = ""
            self.operation = ""
        elif text in ['÷', '×', '-', '+']:
            self.current_input = current_display
            self.operation = text
            self.scientific_display.setText("0")
        elif text == '=':
            if self.operation and self.current_input:
                second_operand = current_display
                result = self.calculate_result(self.current_input, second_operand, self.operation)
                
                operation_text = f"{self.current_input} {self.operation} {second_operand} = {result}"
                self.add_to_history(operation_text)
                
                self.scientific_display.setText(str(result))
                self.current_input = ""
                self.operation = ""
    except Exception as e:
        self.scientific_display.setText("خطأ")
        QMessageBox.warning(self, "خطأ", f"خطأ في العملية العلمية: {str(e)}")

def calculate_result_function(self, first, second, operation):
    """حساب نتيجة العملية"""
    first_num = float(first)
    second_num = float(second)
    
    if operation == '+':
        return first_num + second_num
    elif operation == '-':
        return first_num - second_num
    elif operation == '×':
        return first_num * second_num
    elif operation == '÷':
        if second_num == 0:
            raise ValueError("لا يمكن القسمة على صفر")
        return first_num / second_num
    else:
        raise ValueError("عملية غير معروفة")

def update_units_function(self):
    """تحديث قوائم الوحدات حسب النوع المختار"""
    conversion_type = self.conversion_type.currentText()
    
    units = {
        "الطول": ["متر", "كيلومتر", "سنتيمتر", "مليمتر", "بوصة", "قدم", "ياردة", "ميل"],
        "الوزن": ["كيلوجرام", "جرام", "طن", "رطل", "أونصة", "قيراط"],
        "المساحة": ["متر مربع", "كيلومتر مربع", "سنتيمتر مربع", "هكتار", "فدان", "قدم مربع"],
        "الحجم": ["لتر", "مليلتر", "متر مكعب", "جالون", "كوب", "ملعقة كبيرة"],
        "درجة الحرارة": ["مئوية", "فهرنهايت", "كلفن"],
        "السرعة": ["متر/ثانية", "كيلومتر/ساعة", "ميل/ساعة", "عقدة"]
    }
    
    unit_list = units.get(conversion_type, [])
    
    self.from_unit.clear()
    self.to_unit.clear()
    self.from_unit.addItems(unit_list)
    self.to_unit.addItems(unit_list)

def convert_units_function(self):
    """تحويل الوحدات"""
    try:
        value = float(self.input_value.text())
        from_unit = self.from_unit.currentText()
        to_unit = self.to_unit.currentText()
        conversion_type = self.conversion_type.currentText()
        
        # معاملات التحويل (إلى الوحدة الأساسية)
        conversion_factors = {
            "الطول": {
                "متر": 1,
                "كيلومتر": 1000,
                "سنتيمتر": 0.01,
                "مليمتر": 0.001,
                "بوصة": 0.0254,
                "قدم": 0.3048,
                "ياردة": 0.9144,
                "ميل": 1609.34
            },
            "الوزن": {
                "كيلوجرام": 1,
                "جرام": 0.001,
                "طن": 1000,
                "رطل": 0.453592,
                "أونصة": 0.0283495,
                "قيراط": 0.0002
            },
            "المساحة": {
                "متر مربع": 1,
                "كيلومتر مربع": 1000000,
                "سنتيمتر مربع": 0.0001,
                "هكتار": 10000,
                "فدان": 4046.86,
                "قدم مربع": 0.092903
            },
            "الحجم": {
                "لتر": 1,
                "مليلتر": 0.001,
                "متر مكعب": 1000,
                "جالون": 3.78541,
                "كوب": 0.236588,
                "ملعقة كبيرة": 0.0147868
            }
        }
        
        if conversion_type == "درجة الحرارة":
            result = self.convert_temperature(value, from_unit, to_unit)
        elif conversion_type == "السرعة":
            speed_factors = {
                "متر/ثانية": 1,
                "كيلومتر/ساعة": 0.277778,
                "ميل/ساعة": 0.44704,
                "عقدة": 0.514444
            }
            # تحويل إلى متر/ثانية ثم إلى الوحدة المطلوبة
            base_value = value * speed_factors[from_unit]
            result = base_value / speed_factors[to_unit]
        else:
            factors = conversion_factors.get(conversion_type, {})
            if from_unit in factors and to_unit in factors:
                # تحويل إلى الوحدة الأساسية ثم إلى الوحدة المطلوبة
                base_value = value * factors[from_unit]
                result = base_value / factors[to_unit]
            else:
                raise ValueError("وحدة غير مدعومة")
        
        self.conversion_result.setText(f"{result:.6f}")
        
        # إضافة إلى التاريخ
        conversion_text = f"{value} {from_unit} = {result:.6f} {to_unit}"
        self.add_to_history(conversion_text)
        
    except ValueError as e:
        QMessageBox.warning(self, "خطأ", f"خطأ في التحويل: {str(e)}")
    except Exception as e:
        QMessageBox.warning(self, "خطأ", f"خطأ غير متوقع: {str(e)}")

def convert_temperature_function(self, value, from_unit, to_unit):
    """تحويل درجات الحرارة"""
    # تحويل إلى مئوية أولاً
    if from_unit == "فهرنهايت":
        celsius = (value - 32) * 5/9
    elif from_unit == "كلفن":
        celsius = value - 273.15
    else:  # مئوية
        celsius = value
    
    # تحويل من مئوية إلى الوحدة المطلوبة
    if to_unit == "فهرنهايت":
        return celsius * 9/5 + 32
    elif to_unit == "كلفن":
        return celsius + 273.15
    else:  # مئوية
        return celsius

def add_to_history_function(self, operation):
    """إضافة عملية إلى التاريخ"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    history_entry = f"[{timestamp}] {operation}"
    self.history.append(history_entry)
    
    # تحديث عرض التاريخ
    if hasattr(self, 'history_display'):
        current_text = self.history_display.toPlainText()
        if current_text:
            self.history_display.setPlainText(current_text + "\n" + history_entry)
        else:
            self.history_display.setPlainText(history_entry)

def clear_history_function(self):
    """مسح التاريخ"""
    self.history.clear()
    if hasattr(self, 'history_display'):
        self.history_display.clear()

def save_history_function(self):
    """حفظ التاريخ في ملف"""
    if not self.history:
        QMessageBox.information(self, "تنبيه", "لا يوجد تاريخ للحفظ")
        return
    
    file_path, _ = QFileDialog.getSaveFileName(
        self, 
        "حفظ تاريخ الحاسبة", 
        f"calculator_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
        "Text Files (*.txt)"
    )
    
    if file_path:
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("تاريخ الآلة الحاسبة المتقدمة\n")
                f.write("=" * 50 + "\n\n")
                for entry in self.history:
                    f.write(entry + "\n")
            
            QMessageBox.information(self, "نجح", f"تم حفظ التاريخ في:\n{file_path}")
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في حفظ الملف:\n{str(e)}")

def show_help_function(self):
    """عرض المساعدة"""
    help_text = """
🧮 مساعدة الآلة الحاسبة المتقدمة

📋 الميزات المتاحة:

🧮 الحاسبة الأساسية:
• العمليات الحسابية الأساسية (+، -، ×، ÷)
• تغيير الإشارة (±)
• النسبة المئوية (%)
• مسح العملية (C)

🔬 الحاسبة العلمية:
• الدوال المثلثية (sin، cos، tan)
• اللوغاريتمات (log، ln)
• القوى (x²، x³، xʸ)
• الجذور (√، ∛)
• الثوابت (π، e)
• الأقواس للعمليات المعقدة

🔄 محول الوحدات:
• الطول: متر، كيلومتر، سنتيمتر، بوصة، قدم، ياردة، ميل
• الوزن: كيلوجرام، جرام، طن، رطل، أونصة
• المساحة: متر مربع، كيلومتر مربع، هكتار، فدان
• الحجم: لتر، مليلتر، متر مكعب، جالون
• درجة الحرارة: مئوية، فهرنهايت، كلفن
• السرعة: متر/ثانية، كيلومتر/ساعة، ميل/ساعة

📜 التاريخ:
• حفظ جميع العمليات مع الوقت
• مسح التاريخ
• حفظ التاريخ في ملف نصي

💡 نصائح الاستخدام:
• استخدم الأقواس في العمليات المعقدة
• تأكد من صحة القيم المدخلة
• احفظ التاريخ للمراجعة لاحقاً
• استخدم محول الوحدات للتحويلات السريعة

⚠️ ملاحظات:
• الدوال المثلثية تستخدم الدرجات
• تجنب القسمة على صفر
• النتائج قد تحتوي على أرقام عشرية طويلة
"""
    
    QMessageBox.information(self, "المساعدة", help_text)
