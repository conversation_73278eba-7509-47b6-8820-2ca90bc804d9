#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام أدوات النظام - نسخة تجريبية
System Tools Demo Version
"""

import sys
import os
import platform
import random
import time
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QGridLayout, QLabel, QLineEdit, QPushButton, 
                             QGroupBox, QTextEdit, QTabWidget, QComboBox,
                             QApplication, QMessageBox, QListWidget,
                             QListWidgetItem, QCheckBox, QSpinBox,
                             QProgressBar, QTableWidget, QTableWidgetItem,
                             QHeaderView, QSplitter, QTreeWidget, QTreeWidgetItem,
                             QScrollArea, QFrame, QFileDialog, QMenu, QAction,
                             QToolBar, QStatusBar, QDoubleSpinBox, QDateTimeEdit,
                             QCalendarWidget, QDial, QLCDNumber, QFormLayout)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QDateTime, QSize
from PyQt5.QtGui import QFont, QColor, QIcon, QPalette, QPixmap, QPainter

class DemoSystemMonitor(QThread):
    """مراقب النظام التجريبي"""
    
    data_updated = pyqtSignal(dict)
    alert_triggered = pyqtSignal(str, str, str)
    
    def __init__(self):
        super().__init__()
        self.is_running = True
        self.cpu_base = 25
        self.memory_base = 45
        self.disk_base = 60
        
    def run(self):
        """تشغيل المراقبة التجريبية"""
        while self.is_running:
            try:
                # توليد بيانات وهمية واقعية
                data = {
                    'cpu_percent': max(0, min(100, self.cpu_base + random.randint(-10, 20))),
                    'cpu_count': 8,
                    'cpu_freq': {'current': 2400 + random.randint(-200, 400)},
                    'memory': {
                        'total': 16 * 1024**3,  # 16 GB
                        'used': int(16 * 1024**3 * (self.memory_base + random.randint(-5, 15)) / 100),
                        'available': int(16 * 1024**3 * (55 - self.memory_base + random.randint(-15, 5)) / 100),
                        'percent': max(0, min(100, self.memory_base + random.randint(-5, 15))),
                        'free': int(16 * 1024**3 * 0.1)
                    },
                    'swap': {
                        'total': 4 * 1024**3,
                        'used': int(4 * 1024**3 * 0.1),
                        'free': int(4 * 1024**3 * 0.9),
                        'percent': 10
                    },
                    'disk': {
                        'total': 500 * 1024**3,  # 500 GB
                        'used': int(500 * 1024**3 * (self.disk_base + random.randint(-5, 10)) / 100),
                        'free': int(500 * 1024**3 * (40 - self.disk_base + random.randint(-10, 5)) / 100),
                        'percent': max(0, min(100, self.disk_base + random.randint(-5, 10)))
                    },
                    'network': {
                        'bytes_sent': random.randint(1000000, 10000000),
                        'bytes_recv': random.randint(5000000, 50000000),
                        'packets_sent': random.randint(1000, 10000),
                        'packets_recv': random.randint(5000, 50000),
                        'speed_sent': random.randint(100, 1000) * 1024,  # KB/s
                        'speed_recv': random.randint(500, 5000) * 1024   # KB/s
                    },
                    'processes_count': random.randint(150, 250),
                    'boot_time': time.time() - random.randint(3600, 86400),
                    'uptime': random.randint(3600, 86400),
                    'temperature': random.randint(35, 65),
                    'battery': {
                        'percent': random.randint(20, 100),
                        'plugged': random.choice([True, False]),
                        'time_left': random.randint(3600, 14400)
                    } if random.choice([True, False]) else None,
                    'timestamp': time.time()
                }
                
                # فحص التنبيهات
                if data['cpu_percent'] > 80:
                    self.alert_triggered.emit('cpu', f"استخدام المعالج مرتفع: {data['cpu_percent']:.1f}%", 'warning')
                
                if data['memory']['percent'] > 85:
                    self.alert_triggered.emit('memory', f"استخدام الذاكرة مرتفع: {data['memory']['percent']:.1f}%", 'warning')
                
                if data['disk']['percent'] > 90:
                    self.alert_triggered.emit('disk', f"مساحة القرص منخفضة: {data['disk']['percent']:.1f}%", 'critical')
                
                self.data_updated.emit(data)
                self.msleep(2000)
                
            except Exception as e:
                print(f"خطأ في المراقبة: {e}")
                self.msleep(5000)
    
    def stop(self):
        """إيقاف المراقبة"""
        self.is_running = False

class SystemToolsDemoMainWindow(QMainWindow):
    """النافذة الرئيسية لأدوات النظام التجريبية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.system_monitor = None
        self.current_system_data = {}
        self.alerts_log = []
        self.setup_ui()
        self.start_monitoring()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("🔧 أدوات النظام المحسنة - النسخة التجريبية")
        self.setGeometry(100, 100, 1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # الودجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("🔧 أدوات النظام المحسنة - مراقبة وإدارة وتحسين شامل")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: white;
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)
        
        # لوحة المعلومات السريعة
        self.create_dashboard(main_layout)
        
        # التبويبات الرئيسية
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                padding: 8px 12px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #007bff;
            }
        """)
        
        # تبويب المراقبة المباشرة
        monitoring_tab = self.create_monitoring_tab()
        self.tabs.addTab(monitoring_tab, "📊 المراقبة المباشرة")
        
        # تبويب إدارة العمليات
        processes_tab = self.create_processes_tab()
        self.tabs.addTab(processes_tab, "⚙️ إدارة العمليات")
        
        # تبويب تنظيف النظام
        cleanup_tab = self.create_cleanup_tab()
        self.tabs.addTab(cleanup_tab, "🧹 تنظيف النظام")
        
        # تبويب تحسين الأداء
        optimization_tab = self.create_optimization_tab()
        self.tabs.addTab(optimization_tab, "⚡ تحسين الأداء")
        
        # تبويب معلومات النظام
        system_info_tab = self.create_system_info_tab()
        self.tabs.addTab(system_info_tab, "💻 معلومات النظام")
        
        # تبويب التنبيهات
        alerts_tab = self.create_alerts_tab()
        self.tabs.addTab(alerts_tab, "🚨 التنبيهات")
        
        main_layout.addWidget(self.tabs)
        
        central_widget.setLayout(main_layout)
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_dashboard(self, main_layout):
        """إنشاء لوحة المعلومات السريعة"""
        dashboard_frame = QFrame()
        dashboard_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 10px;
                padding: 10px;
                margin-bottom: 15px;
            }
        """)
        
        dashboard_layout = QHBoxLayout()
        
        # مؤشر المعالج
        self.cpu_gauge = self.create_gauge("🖥️ المعالج", "#007bff")
        dashboard_layout.addWidget(self.cpu_gauge)
        
        # مؤشر الذاكرة
        self.memory_gauge = self.create_gauge("💾 الذاكرة", "#28a745")
        dashboard_layout.addWidget(self.memory_gauge)
        
        # مؤشر القرص
        self.disk_gauge = self.create_gauge("💿 القرص", "#ffc107")
        dashboard_layout.addWidget(self.disk_gauge)
        
        # مؤشر الشبكة
        self.network_gauge = self.create_gauge("🌐 الشبكة", "#17a2b8")
        dashboard_layout.addWidget(self.network_gauge)
        
        # مؤشر درجة الحرارة
        self.temp_gauge = self.create_gauge("🌡️ الحرارة", "#dc3545")
        dashboard_layout.addWidget(self.temp_gauge)
        
        dashboard_frame.setLayout(dashboard_layout)
        main_layout.addWidget(dashboard_frame)
    
    def create_gauge(self, title, color):
        """إنشاء مؤشر دائري"""
        widget = QWidget()
        layout = QVBoxLayout()
        
        # العنوان
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-weight: bold;
                color: {color};
                font-size: 11px;
                margin-bottom: 5px;
            }}
        """)
        
        # المؤشر الدائري
        dial = QDial()
        dial.setRange(0, 100)
        dial.setValue(0)
        dial.setNotchesVisible(True)
        dial.setMaximumSize(70, 70)
        dial.setStyleSheet(f"""
            QDial {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 35px;
            }}
        """)
        
        # شاشة LCD للقيمة
        lcd = QLCDNumber(3)
        lcd.setMaximumHeight(25)
        lcd.setStyleSheet(f"""
            QLCDNumber {{
                background-color: {color};
                color: white;
                border-radius: 3px;
            }}
        """)
        
        layout.addWidget(title_label)
        layout.addWidget(dial, 0, Qt.AlignCenter)
        layout.addWidget(lcd)
        
        widget.setLayout(layout)
        
        # حفظ مراجع للتحديث
        widget.dial = dial
        widget.lcd = lcd
        
        return widget
    
    def create_monitoring_tab(self):
        """إنشاء تبويب المراقبة المباشرة"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # جدول الإحصائيات
        stats_group = QGroupBox("📊 إحصائيات النظام المباشرة")
        stats_layout = QVBoxLayout()
        
        self.stats_table = QTableWidget()
        self.stats_table.setColumnCount(2)
        self.stats_table.setHorizontalHeaderLabels(["المؤشر", "القيمة"])
        self.stats_table.horizontalHeader().setStretchLastSection(True)
        self.stats_table.setAlternatingRowColors(True)
        self.stats_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                font-size: 12px;
            }
            QHeaderView::section {
                background-color: #343a40;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)
        
        stats_layout.addWidget(self.stats_table)
        stats_group.setLayout(stats_layout)
        layout.addWidget(stats_group)
        
        tab.setLayout(layout)
        return tab
    
    def create_processes_tab(self):
        """إنشاء تبويب إدارة العمليات"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # شريط أدوات العمليات
        processes_toolbar = QHBoxLayout()
        
        refresh_processes_btn = QPushButton("🔄 تحديث العمليات")
        refresh_processes_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        refresh_processes_btn.clicked.connect(self.refresh_processes)
        
        processes_toolbar.addWidget(refresh_processes_btn)
        processes_toolbar.addStretch()
        
        layout.addLayout(processes_toolbar)
        
        # جدول العمليات التجريبي
        self.processes_table = QTableWidget()
        self.processes_table.setColumnCount(6)
        self.processes_table.setHorizontalHeaderLabels([
            "PID", "اسم العملية", "المعالج%", "الذاكرة%", "الذاكرة MB", "الحالة"
        ])
        
        # تنسيق الجدول
        header = self.processes_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        
        self.processes_table.setAlternatingRowColors(True)
        self.processes_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.processes_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-size: 11px;
            }
            QHeaderView::section {
                background-color: #343a40;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(self.processes_table)
        
        # ملء بيانات تجريبية
        self.refresh_processes()
        
        tab.setLayout(layout)
        return tab

    def create_cleanup_tab(self):
        """إنشاء تبويب تنظيف النظام"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة تحليل النظام
        analysis_group = QGroupBox("🔍 تحليل النظام")
        analysis_layout = QGridLayout()

        temp_files_label = QLabel("الملفات المؤقتة:")
        self.temp_files_info = QLabel("1,234 ملف - 567.8 MB")
        self.temp_files_info.setStyleSheet("color: #007bff; font-weight: bold;")

        recycle_label = QLabel("سلة المحذوفات:")
        self.recycle_info = QLabel("89.2 MB")
        self.recycle_info.setStyleSheet("color: #007bff; font-weight: bold;")

        analyze_btn = QPushButton("🔍 تحليل النظام")
        analyze_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        analyze_btn.clicked.connect(self.analyze_system)

        analysis_layout.addWidget(temp_files_label, 0, 0)
        analysis_layout.addWidget(self.temp_files_info, 0, 1)
        analysis_layout.addWidget(recycle_label, 1, 0)
        analysis_layout.addWidget(self.recycle_info, 1, 1)
        analysis_layout.addWidget(analyze_btn, 2, 0, 1, 2)

        analysis_group.setLayout(analysis_layout)
        layout.addWidget(analysis_group)

        # مجموعة عمليات التنظيف
        cleanup_group = QGroupBox("🧹 عمليات التنظيف")
        cleanup_layout = QGridLayout()

        clean_temp_btn = QPushButton("🗑️ تنظيف الملفات المؤقتة")
        clean_temp_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        clean_temp_btn.clicked.connect(self.clean_temp_files)

        empty_recycle_btn = QPushButton("🗑️ تفريغ سلة المحذوفات")
        empty_recycle_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        empty_recycle_btn.clicked.connect(self.empty_recycle_bin)

        full_cleanup_btn = QPushButton("🧹 تنظيف شامل")
        full_cleanup_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 15px 30px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        full_cleanup_btn.clicked.connect(self.full_cleanup)

        cleanup_layout.addWidget(clean_temp_btn, 0, 0)
        cleanup_layout.addWidget(empty_recycle_btn, 0, 1)
        cleanup_layout.addWidget(full_cleanup_btn, 1, 0, 1, 2)

        cleanup_group.setLayout(cleanup_layout)
        layout.addWidget(cleanup_group)

        # سجل التنظيف
        log_group = QGroupBox("📋 سجل التنظيف")
        log_layout = QVBoxLayout()

        self.cleanup_log = QTextEdit()
        self.cleanup_log.setMaximumHeight(120)
        self.cleanup_log.setReadOnly(True)
        self.cleanup_log.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        self.cleanup_log.append("جاهز لبدء عمليات التنظيف...")

        log_layout.addWidget(self.cleanup_log)
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)

        tab.setLayout(layout)
        return tab

    def create_optimization_tab(self):
        """إنشاء تبويب تحسين الأداء"""
        tab = QWidget()
        layout = QVBoxLayout()

        # مجموعة تحسين الذاكرة
        memory_group = QGroupBox("💾 تحسين الذاكرة")
        memory_layout = QGridLayout()

        optimize_memory_btn = QPushButton("⚡ تحسين الذاكرة")
        optimize_memory_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        optimize_memory_btn.clicked.connect(self.optimize_memory)

        memory_layout.addWidget(optimize_memory_btn)
        memory_group.setLayout(memory_layout)
        layout.addWidget(memory_group)

        # مجموعة تحسين النظام
        system_group = QGroupBox("🔧 تحسين النظام")
        system_layout = QGridLayout()

        defrag_registry_btn = QPushButton("📝 إلغاء تجزئة السجل")
        defrag_registry_btn.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        defrag_registry_btn.clicked.connect(self.defrag_registry)

        update_system_btn = QPushButton("🔄 فحص التحديثات")
        update_system_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        update_system_btn.clicked.connect(self.update_system)

        system_layout.addWidget(defrag_registry_btn, 0, 0)
        system_layout.addWidget(update_system_btn, 0, 1)

        system_group.setLayout(system_layout)
        layout.addWidget(system_group)

        # سجل التحسين
        optimization_log_group = QGroupBox("📋 سجل التحسين")
        optimization_log_layout = QVBoxLayout()

        self.optimization_log = QTextEdit()
        self.optimization_log.setMaximumHeight(150)
        self.optimization_log.setReadOnly(True)
        self.optimization_log.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        self.optimization_log.append("جاهز لبدء عمليات التحسين...")

        optimization_log_layout.addWidget(self.optimization_log)
        optimization_log_group.setLayout(optimization_log_layout)
        layout.addWidget(optimization_log_group)

        tab.setLayout(layout)
        return tab

    def create_system_info_tab(self):
        """إنشاء تبويب معلومات النظام"""
        tab = QWidget()
        layout = QVBoxLayout()

        # معلومات النظام
        self.system_info_tree = QTreeWidget()
        self.system_info_tree.setHeaderLabels(["المكون", "المعلومات"])
        self.system_info_tree.setAlternatingRowColors(True)
        self.system_info_tree.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                alternate-background-color: #f8f9fa;
                font-size: 12px;
            }
            QTreeWidget::item {
                padding: 5px;
            }
            QHeaderView::section {
                background-color: #343a40;
                color: white;
                padding: 8px;
                font-weight: bold;
            }
        """)

        # ملء معلومات النظام
        self.populate_system_info()

        layout.addWidget(self.system_info_tree)

        # زر تحديث معلومات النظام
        refresh_info_btn = QPushButton("🔄 تحديث معلومات النظام")
        refresh_info_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        refresh_info_btn.clicked.connect(self.populate_system_info)
        layout.addWidget(refresh_info_btn)

        tab.setLayout(layout)
        return tab

    def create_alerts_tab(self):
        """إنشاء تبويب التنبيهات"""
        tab = QWidget()
        layout = QVBoxLayout()

        # سجل التنبيهات
        alerts_log_group = QGroupBox("🚨 سجل التنبيهات")
        alerts_log_layout = QVBoxLayout()

        self.alerts_list = QListWidget()
        self.alerts_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QListWidget::item:selected {
                background-color: #007bff;
                color: white;
            }
        """)

        # أزرار إدارة السجل
        log_buttons_layout = QHBoxLayout()

        clear_log_btn = QPushButton("🗑️ مسح السجل")
        clear_log_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        clear_log_btn.clicked.connect(self.clear_alerts_log)

        log_buttons_layout.addWidget(clear_log_btn)
        log_buttons_layout.addStretch()

        alerts_log_layout.addWidget(self.alerts_list)
        alerts_log_layout.addLayout(log_buttons_layout)

        alerts_log_group.setLayout(alerts_log_layout)
        layout.addWidget(alerts_log_group)

        tab.setLayout(layout)
        return tab

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()

        # تسمية الحالة
        self.status_label = QLabel("جاهز")
        status_bar.addWidget(self.status_label)

        # مؤشر المراقبة
        self.monitoring_indicator = QLabel("🟢 المراقبة نشطة")
        status_bar.addPermanentWidget(self.monitoring_indicator)

        # الوقت الحالي
        self.time_label = QLabel()
        self.update_time()
        status_bar.addPermanentWidget(self.time_label)

        # مؤقت تحديث الوقت
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)

    def start_monitoring(self):
        """بدء مراقبة النظام"""
        self.system_monitor = DemoSystemMonitor()
        self.system_monitor.data_updated.connect(self.update_system_data)
        self.system_monitor.alert_triggered.connect(self.handle_alert)
        self.system_monitor.start()

        self.status_label.setText("تم بدء مراقبة النظام التجريبية")

    def update_system_data(self, data):
        """تحديث بيانات النظام"""
        self.current_system_data = data

        # تحديث المؤشرات الدائرية
        self.cpu_gauge.dial.setValue(int(data['cpu_percent']))
        self.cpu_gauge.lcd.display(f"{data['cpu_percent']:.1f}")

        self.memory_gauge.dial.setValue(int(data['memory']['percent']))
        self.memory_gauge.lcd.display(f"{data['memory']['percent']:.1f}")

        self.disk_gauge.dial.setValue(int(data['disk']['percent']))
        self.disk_gauge.lcd.display(f"{data['disk']['percent']:.1f}")

        # مؤشر الشبكة
        network_percent = min(100, (data['network']['speed_sent'] / (10 * 1024 * 1024)) * 100)
        self.network_gauge.dial.setValue(int(network_percent))
        self.network_gauge.lcd.display(f"{network_percent:.1f}")

        # مؤشر درجة الحرارة
        temp_percent = min(100, (data['temperature'] / 100) * 100)
        self.temp_gauge.dial.setValue(int(temp_percent))
        self.temp_gauge.lcd.display(f"{data['temperature']:.1f}")

        # تحديث جدول الإحصائيات
        self.update_stats_table(data)

    def update_stats_table(self, data):
        """تحديث جدول الإحصائيات"""
        stats = [
            ("استخدام المعالج", f"{data['cpu_percent']:.1f}%"),
            ("عدد أنوية المعالج", str(data['cpu_count'])),
            ("تردد المعالج", f"{data['cpu_freq']['current']:.0f} MHz"),
            ("إجمالي الذاكرة", f"{data['memory']['total'] / (1024**3):.1f} GB"),
            ("الذاكرة المستخدمة", f"{data['memory']['used'] / (1024**3):.1f} GB"),
            ("الذاكرة المتاحة", f"{data['memory']['available'] / (1024**3):.1f} GB"),
            ("نسبة استخدام الذاكرة", f"{data['memory']['percent']:.1f}%"),
            ("إجمالي مساحة القرص", f"{data['disk']['total'] / (1024**3):.1f} GB"),
            ("المساحة المستخدمة", f"{data['disk']['used'] / (1024**3):.1f} GB"),
            ("المساحة المتاحة", f"{data['disk']['free'] / (1024**3):.1f} GB"),
            ("نسبة استخدام القرص", f"{data['disk']['percent']:.1f}%"),
            ("البيانات المرسلة", f"{data['network']['bytes_sent'] / (1024**2):.1f} MB"),
            ("البيانات المستقبلة", f"{data['network']['bytes_recv'] / (1024**2):.1f} MB"),
            ("سرعة الإرسال", f"{data['network']['speed_sent'] / 1024:.1f} KB/s"),
            ("سرعة الاستقبال", f"{data['network']['speed_recv'] / 1024:.1f} KB/s"),
            ("عدد العمليات", str(data['processes_count'])),
            ("وقت تشغيل النظام", f"{data['uptime'] / 3600:.1f} ساعة"),
            ("درجة الحرارة", f"{data['temperature']:.1f}°C"),
        ]

        if data['battery']:
            stats.extend([
                ("مستوى البطارية", f"{data['battery']['percent']:.1f}%"),
                ("حالة الشحن", "متصل" if data['battery']['plugged'] else "غير متصل"),
                ("الوقت المتبقي", f"{data['battery']['time_left'] / 3600:.1f} ساعة"),
            ])

        self.stats_table.setRowCount(len(stats))
        for i, (key, value) in enumerate(stats):
            self.stats_table.setItem(i, 0, QTableWidgetItem(key))
            self.stats_table.setItem(i, 1, QTableWidgetItem(value))

    def handle_alert(self, alert_type, message, level):
        """معالجة التنبيهات"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        alert_text = f"[{timestamp}] {message}"

        # إضافة للسجل
        self.alerts_log.append({
            'timestamp': timestamp,
            'type': alert_type,
            'message': message,
            'level': level
        })

        # إضافة لقائمة التنبيهات
        item = QListWidgetItem(alert_text)
        if level == 'critical':
            item.setBackground(QColor("#dc3545"))
            item.setForeground(QColor("white"))
        elif level == 'warning':
            item.setBackground(QColor("#ffc107"))
            item.setForeground(QColor("#212529"))

        self.alerts_list.insertItem(0, item)

        # الحد الأقصى للعناصر في القائمة
        if self.alerts_list.count() > 50:
            self.alerts_list.takeItem(self.alerts_list.count() - 1)

        # تحديث شريط الحالة
        self.status_label.setText(f"تنبيه: {message}")

    def refresh_processes(self):
        """تحديث قائمة العمليات التجريبية"""
        try:
            # بيانات عمليات تجريبية
            demo_processes = [
                {"pid": 1234, "name": "chrome.exe", "cpu": 15.2, "memory": 8.5, "memory_mb": 512.3, "status": "running"},
                {"pid": 5678, "name": "python.exe", "cpu": 12.1, "memory": 4.2, "memory_mb": 256.7, "status": "running"},
                {"pid": 9012, "name": "explorer.exe", "cpu": 3.5, "memory": 2.1, "memory_mb": 128.4, "status": "running"},
                {"pid": 3456, "name": "notepad.exe", "cpu": 0.1, "memory": 0.5, "memory_mb": 32.1, "status": "running"},
                {"pid": 7890, "name": "winlogon.exe", "cpu": 0.0, "memory": 0.3, "memory_mb": 18.9, "status": "running"},
                {"pid": 2468, "name": "svchost.exe", "cpu": 2.3, "memory": 1.8, "memory_mb": 115.2, "status": "running"},
                {"pid": 1357, "name": "firefox.exe", "cpu": 8.7, "memory": 6.3, "memory_mb": 403.6, "status": "running"},
                {"pid": 8024, "name": "discord.exe", "cpu": 4.2, "memory": 3.1, "memory_mb": 198.7, "status": "running"},
            ]

            # إضافة تباين عشوائي
            for proc in demo_processes:
                proc["cpu"] += random.uniform(-2, 5)
                proc["memory"] += random.uniform(-1, 2)
                proc["memory_mb"] += random.uniform(-50, 100)
                proc["cpu"] = max(0, proc["cpu"])
                proc["memory"] = max(0, proc["memory"])
                proc["memory_mb"] = max(0, proc["memory_mb"])

            self.processes_table.setRowCount(len(demo_processes))

            for i, proc in enumerate(demo_processes):
                self.processes_table.setItem(i, 0, QTableWidgetItem(str(proc['pid'])))
                self.processes_table.setItem(i, 1, QTableWidgetItem(proc['name']))
                self.processes_table.setItem(i, 2, QTableWidgetItem(f"{proc['cpu']:.1f}%"))
                self.processes_table.setItem(i, 3, QTableWidgetItem(f"{proc['memory']:.1f}%"))
                self.processes_table.setItem(i, 4, QTableWidgetItem(f"{proc['memory_mb']:.1f}"))
                self.processes_table.setItem(i, 5, QTableWidgetItem(proc['status']))

            self.status_label.setText(f"تم تحديث {len(demo_processes)} عملية")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث العمليات:\n{str(e)}")

    def populate_system_info(self):
        """ملء معلومات النظام"""
        try:
            self.system_info_tree.clear()

            # معلومات النظام الأساسية
            system_item = QTreeWidgetItem(["💻 النظام", ""])
            system_item.addChild(QTreeWidgetItem(["نظام التشغيل", platform.system()]))
            system_item.addChild(QTreeWidgetItem(["إصدار النظام", platform.release()]))
            system_item.addChild(QTreeWidgetItem(["معمارية النظام", platform.architecture()[0]]))
            system_item.addChild(QTreeWidgetItem(["اسم الجهاز", platform.node()]))
            system_item.addChild(QTreeWidgetItem(["المعالج", platform.processor()]))
            self.system_info_tree.addTopLevelItem(system_item)

            # معلومات المعالج التجريبية
            cpu_item = QTreeWidgetItem(["🖥️ المعالج", ""])
            cpu_item.addChild(QTreeWidgetItem(["عدد الأنوية", "8"]))
            cpu_item.addChild(QTreeWidgetItem(["عدد الخيوط", "16"]))
            cpu_item.addChild(QTreeWidgetItem(["التردد الحالي", "2400 MHz"]))
            cpu_item.addChild(QTreeWidgetItem(["الحد الأدنى للتردد", "800 MHz"]))
            cpu_item.addChild(QTreeWidgetItem(["الحد الأقصى للتردد", "3600 MHz"]))
            self.system_info_tree.addTopLevelItem(cpu_item)

            # معلومات الذاكرة التجريبية
            memory_item = QTreeWidgetItem(["💾 الذاكرة", ""])
            memory_item.addChild(QTreeWidgetItem(["الإجمالي", "16.0 GB"]))
            memory_item.addChild(QTreeWidgetItem(["المتاح", "8.5 GB"]))
            memory_item.addChild(QTreeWidgetItem(["المستخدم", "7.5 GB"]))
            memory_item.addChild(QTreeWidgetItem(["النسبة المئوية", "46.9%"]))
            self.system_info_tree.addTopLevelItem(memory_item)

            # معلومات الأقراص التجريبية
            disks_item = QTreeWidgetItem(["💿 الأقراص", ""])

            disk_c = QTreeWidgetItem(["C:", ""])
            disk_c.addChild(QTreeWidgetItem(["نقطة التحميل", "C:\\"]))
            disk_c.addChild(QTreeWidgetItem(["نظام الملفات", "NTFS"]))
            disk_c.addChild(QTreeWidgetItem(["الحجم الإجمالي", "500.0 GB"]))
            disk_c.addChild(QTreeWidgetItem(["المستخدم", "300.0 GB"]))
            disk_c.addChild(QTreeWidgetItem(["المتاح", "200.0 GB"]))
            disk_c.addChild(QTreeWidgetItem(["النسبة المئوية", "60.0%"]))
            disks_item.addChild(disk_c)

            self.system_info_tree.addTopLevelItem(disks_item)

            # معلومات الشبكة التجريبية
            network_item = QTreeWidgetItem(["🌐 الشبكة", ""])
            network_item.addChild(QTreeWidgetItem(["البيانات المرسلة", "1,234.5 MB"]))
            network_item.addChild(QTreeWidgetItem(["البيانات المستقبلة", "5,678.9 MB"]))
            network_item.addChild(QTreeWidgetItem(["الحزم المرسلة", "123,456"]))
            network_item.addChild(QTreeWidgetItem(["الحزم المستقبلة", "567,890"]))

            # واجهات الشبكة التجريبية
            ethernet_item = QTreeWidgetItem(["Ethernet", ""])
            ethernet_item.addChild(QTreeWidgetItem(["IPv4", "*************"]))
            ethernet_item.addChild(QTreeWidgetItem(["IPv6", "fe80::1234:5678:9abc:def0"]))
            network_item.addChild(ethernet_item)

            wifi_item = QTreeWidgetItem(["Wi-Fi", ""])
            wifi_item.addChild(QTreeWidgetItem(["IPv4", "*************"]))
            network_item.addChild(wifi_item)

            self.system_info_tree.addTopLevelItem(network_item)

            # توسيع جميع العناصر
            self.system_info_tree.expandAll()

            self.status_label.setText("تم تحديث معلومات النظام")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحديث معلومات النظام:\n{str(e)}")

    def analyze_system(self):
        """تحليل النظام التجريبي"""
        # محاكاة تحليل النظام
        temp_count = random.randint(800, 1500)
        temp_size = random.randint(200, 800)
        recycle_size = random.randint(50, 200)

        self.temp_files_info.setText(f"{temp_count:,} ملف - {temp_size:.1f} MB")
        self.recycle_info.setText(f"{recycle_size:.1f} MB")

        self.status_label.setText("تم تحليل النظام")

    def clean_temp_files(self):
        """تنظيف الملفات المؤقتة التجريبي"""
        reply = QMessageBox.question(
            self, "تأكيد التنظيف",
            "هل أنت متأكد من حذف جميع الملفات المؤقتة؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # محاكاة التنظيف
            deleted_files = random.randint(500, 1200)
            freed_space = random.randint(100, 500)

            log_message = f"[{datetime.now().strftime('%H:%M:%S')}] تم حذف {deleted_files} ملف وتحرير {freed_space:.1f} MB"
            self.cleanup_log.append(log_message)

            QMessageBox.information(self, "تم التنظيف",
                f"تم حذف {deleted_files} ملف\nتم تحرير {freed_space:.1f} MB")

            self.analyze_system()

    def empty_recycle_bin(self):
        """تفريغ سلة المحذوفات التجريبي"""
        reply = QMessageBox.question(
            self, "تأكيد التفريغ",
            "هل أنت متأكد من تفريغ سلة المحذوفات؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            log_message = f"[{datetime.now().strftime('%H:%M:%S')}] تم تفريغ سلة المحذوفات"
            self.cleanup_log.append(log_message)

            QMessageBox.information(self, "تم التفريغ", "تم تفريغ سلة المحذوفات بنجاح")
            self.analyze_system()

    def full_cleanup(self):
        """تنظيف شامل تجريبي"""
        reply = QMessageBox.question(
            self, "تأكيد التنظيف الشامل",
            "هل أنت متأكد من التنظيف الشامل؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            log_message = f"[{datetime.now().strftime('%H:%M:%S')}] بدء التنظيف الشامل"
            self.cleanup_log.append(log_message)

            # محاكاة التنظيف الشامل
            deleted_files = random.randint(800, 1500)
            freed_space = random.randint(200, 700)

            self.cleanup_log.append(f"الملفات المؤقتة: {deleted_files} ملف - {freed_space:.1f} MB")
            self.cleanup_log.append("سلة المحذوفات: تم التفريغ")

            QMessageBox.information(self, "تم التنظيف الشامل",
                f"تم التنظيف الشامل بنجاح:\n- حذف {deleted_files} ملف\n- تحرير {freed_space:.1f} MB")

            self.analyze_system()

    def optimize_memory(self):
        """تحسين الذاكرة التجريبي"""
        log_message = f"[{datetime.now().strftime('%H:%M:%S')}] تم تحسين الذاكرة"
        self.optimization_log.append(log_message)

        QMessageBox.information(self, "تم التحسين", "تم تحسين الذاكرة بنجاح")

    def defrag_registry(self):
        """إلغاء تجزئة السجل التجريبي"""
        log_message = f"[{datetime.now().strftime('%H:%M:%S')}] تم إلغاء تجزئة السجل"
        self.optimization_log.append(log_message)

        QMessageBox.information(self, "تم التحسين", "تم إلغاء تجزئة السجل بنجاح")

    def update_system(self):
        """فحص التحديثات التجريبي"""
        log_message = f"[{datetime.now().strftime('%H:%M:%S')}] تم فحص التحديثات"
        self.optimization_log.append(log_message)

        QMessageBox.information(self, "فحص التحديثات", "تم فحص التحديثات - النظام محدث")

    def clear_alerts_log(self):
        """مسح سجل التنبيهات"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح جميع التنبيهات؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.alerts_list.clear()
            self.alerts_log.clear()
            self.status_label.setText("تم مسح سجل التنبيهات")

    def update_time(self):
        """تحديث الوقت"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)

    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        if self.system_monitor:
            self.system_monitor.stop()
            self.system_monitor.wait()
        event.accept()


def main():
    """تشغيل أدوات النظام التجريبية"""
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)

    window = SystemToolsDemoMainWindow()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
