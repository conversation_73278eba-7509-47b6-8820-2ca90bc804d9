#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة أنواع الهيكل الإداري
Debug Administrative Structure Types
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("🔍 بدء تشخيص مشكلة أنواع الهيكل الإداري...")

# اختبار الاستيراد الأساسي
try:
    print("1. اختبار استيراد PyQt5...")
    from PyQt5.QtWidgets import QApplication, QDialog
    from PyQt5.QtCore import Qt
    print("✅ تم استيراد PyQt5 بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد PyQt5: {e}")
    sys.exit(1)

# اختبار استيراد الملف الرئيسي
try:
    print("2. اختبار استيراد الملف الرئيسي...")
    from modules.setup.administrative_structure_types import AdministrativeStructureTypesDialog
    print("✅ تم استيراد الملف الرئيسي بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد الملف الرئيسي: {e}")
    print("🔍 محاولة تحديد المشكلة...")
    
    # اختبار وجود الملف
    file_path = os.path.join("src", "modules", "setup", "administrative_structure_types.py")
    if os.path.exists(file_path):
        print(f"✅ الملف موجود: {file_path}")
    else:
        print(f"❌ الملف غير موجود: {file_path}")
    
    # اختبار استيراد ملف الوظائف
    try:
        from modules.setup.administrative_structure_functions import load_sample_data_function
        print("✅ تم استيراد ملف الوظائف بنجاح")
    except ImportError as e2:
        print(f"❌ خطأ في استيراد ملف الوظائف: {e2}")
    
    sys.exit(1)

# اختبار إنشاء التطبيق
try:
    print("3. اختبار إنشاء التطبيق...")
    app = QApplication([])
    app.setLayoutDirection(Qt.RightToLeft)
    print("✅ تم إنشاء التطبيق بنجاح")
except Exception as e:
    print(f"❌ خطأ في إنشاء التطبيق: {e}")
    sys.exit(1)

# اختبار إنشاء الشاشة
try:
    print("4. اختبار إنشاء الشاشة...")
    
    class MockDBManager:
        pass
    
    dialog = AdministrativeStructureTypesDialog(MockDBManager())
    print("✅ تم إنشاء الشاشة بنجاح")
except Exception as e:
    print(f"❌ خطأ في إنشاء الشاشة: {e}")
    import traceback
    print("📋 تفاصيل الخطأ:")
    traceback.print_exc()
    sys.exit(1)

# اختبار عرض الشاشة
try:
    print("5. اختبار عرض الشاشة...")
    dialog.show()
    print("✅ تم عرض الشاشة بنجاح")
except Exception as e:
    print(f"❌ خطأ في عرض الشاشة: {e}")
    import traceback
    print("📋 تفاصيل الخطأ:")
    traceback.print_exc()
    sys.exit(1)

print("\n🎉 جميع الاختبارات نجحت! الشاشة تعمل بشكل صحيح.")
print("🏗️ شاشة أنواع الهيكل الإداري جاهزة للاستخدام!")

# تشغيل الشاشة
sys.exit(app.exec_())
