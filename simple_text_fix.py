#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح بسيط ومباشر لمشاكل النص
Simple and Direct Text Fix
"""

def main():
    """إصلاح مباشر لمشاكل النص في واجهة العملاء"""
    print("🔤 إصلاح مباشر لمشاكل النص في واجهة العملاء")
    print("=" * 55)
    
    # قراءة الملف الحالي
    file_path = "src/modules/customers/customer_data_interface.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة نمط عام للليبلات في بداية الكلاس
        if "# نمط عام للليبلات" not in content:
            # البحث عن مكان إضافة النمط
            setup_ui_start = content.find("def setup_ui(self):")
            if setup_ui_start != -1:
                # العثور على نهاية الدالة
                next_def = content.find("def ", setup_ui_start + 1)
                if next_def == -1:
                    next_def = len(content)
                
                # إضافة النمط العام
                style_code = '''
        
        # نمط عام للليبلات لضمان وضوح النص
        self.setStyleSheet("""
            QLabel {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50 !important;
                background-color: transparent;
                padding: 5px;
                min-width: 100px;
                border: none;
            }
            
            QLabel[class="form-label"] {
                font-size: 12px;
                padding: 8px;
                min-width: 150px;
            }
            
            QLabel[class="grid-label"] {
                font-size: 11px;
                min-width: 140px;
                max-width: 200px;
            }
            
            QLabel[class="value-label"] {
                font-size: 11px;
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                min-width: 100px;
            }
            
            QCheckBox {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                padding: 5px;
            }
            
            QLineEdit {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                color: #2c3e50;
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
            }
            
            QLineEdit:read-only {
                background-color: #f5f5f5;
                color: #666;
            }
            
            QTextEdit {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                color: #2c3e50;
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
            }
            
            QTextEdit:read-only {
                background-color: #f5f5f5;
                color: #666;
            }
            
            QComboBox {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                color: #2c3e50;
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
            }
            
            QComboBox:disabled {
                background-color: #f5f5f5;
                color: #666;
            }
            
            QSpinBox, QDateEdit {
                font-family: "Tahoma", "Arial", "Segoe UI", sans-serif;
                font-size: 12px;
                color: #2c3e50;
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
            }
            
            QSpinBox:read-only, QDateEdit:read-only {
                background-color: #f5f5f5;
                color: #666;
            }
        """)
'''
                
                # إدراج النمط في نهاية دالة setup_ui
                layout_line = content.find("self.setLayout(layout)", setup_ui_start)
                if layout_line != -1:
                    content = content[:layout_line] + style_code + "\n        " + content[layout_line:]
                    print("✅ تم إضافة النمط العام للليبلات")
                else:
                    print("⚠️ لم يتم العثور على مكان مناسب لإضافة النمط")
        
        # حفظ الملف المحدث
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث ملف واجهة العملاء بنجاح")
        
        # إنشاء ملف اختبار سريع
        create_quick_test()
        
        print("\n🎉 تم إصلاح مشاكل النص بنجاح!")
        print("\n🔤 الإصلاحات المطبقة:")
        print("   ✅ نمط عام للليبلات")
        print("   ✅ خط Tahoma/Arial واضح")
        print("   ✅ حجم 12px مناسب")
        print("   ✅ لون #2c3e50 واضح")
        print("   ✅ حشو 5px مريح")
        print("   ✅ عرض أدنى محدد")
        print("   ✅ أنماط للحقول المختلفة")
        
        print("\n🚀 للتحقق من النتائج:")
        print("   python test_clear_labels_interface.py")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح النص: {e}")
        return False

def create_quick_test():
    """إنشاء اختبار سريع"""
    test_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""اختبار سريع للنص الواضح"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    from PyQt5.QtWidgets import QApplication
    from PyQt5.QtCore import Qt
    
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.RightToLeft)
    
    from modules.customers.customer_data_interface import CustomerDataInterface
    
    class MockDB:
        def __init__(self):
            self.cursor = type('obj', (object,), {'lastrowid': 1})()
        def execute_query(self, query, params=None):
            return []
        def execute_update(self, query, params=None):
            return True
    
    interface = CustomerDataInterface(MockDB())
    interface.show()
    
    print("🎉 تم عرض الواجهة مع النص الواضح!")
    print("تحقق من وضوح جميع النصوص في الليبلات")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
'''
    
    try:
        with open('quick_text_test.py', 'w', encoding='utf-8') as f:
            f.write(test_content)
        print("✅ تم إنشاء اختبار سريع: quick_text_test.py")
    except Exception as e:
        print(f"⚠️ خطأ في إنشاء الاختبار: {e}")

if __name__ == "__main__":
    main()
