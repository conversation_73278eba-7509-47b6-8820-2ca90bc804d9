#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة العملاء
Customer Management System Test
"""

import sys
import os

# إضافة مسار src إلى Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_customer_management():
    """اختبار نظام إدارة العملاء"""
    try:
        print("🚀 تشغيل نظام إدارة العملاء...")
        
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from modules.customers.customer_management import CustomerManagementWindow
        
        # محاكاة مدير قاعدة البيانات
        class MockDBManager:
            pass
        
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        print("✅ تم إنشاء التطبيق بنجاح")
        
        window = CustomerManagementWindow(MockDBManager())
        print("✅ تم إنشاء نظام إدارة العملاء بنجاح")
        
        window.show()
        print("👥 نظام إدارة العملاء جاهز!")
        
        print("\n📋 الميزات المتاحة:")
        print("   ✓ إدارة شاملة للعملاء والزبائن")
        print("   ✓ 4 تبويبات متكاملة للإدارة")
        print("   ✓ قائمة العملاء مع البحث والتصفية")
        print("   ✓ نموذج إضافة/تعديل العملاء")
        print("   ✓ إدارة تصنيفات العملاء")
        print("   ✓ تقارير شاملة وإحصائيات")
        print("   ✓ 8 عملاء نموذجيين يمنيين")
        print("   ✓ 4 تصنيفات للعملاء")
        print("   ✓ بحث وتصفية متقدمة")
        print("   ✓ تصدير واستيراد البيانات")
        
        print("\n🎯 التبويبات المتاحة:")
        print("   👥 قائمة العملاء - عرض وإدارة جميع العملاء")
        print("   ➕ إضافة/تعديل عميل - نموذج شامل لبيانات العميل")
        print("   🏷️ تصنيفات العملاء - إدارة أنواع العملاء")
        print("   📊 التقارير - تقارير شاملة وإحصائيات")
        
        print("\n🌟 المميزات الخاصة:")
        print("   📊 جدول تفاعلي مع تنسيق احترافي")
        print("   🔍 بحث فوري في الاسم والهاتف والبريد")
        print("   🏷️ تصفية حسب تصنيف العميل")
        print("   📋 نموذج شامل مع جميع البيانات المطلوبة")
        print("   📊 تقارير متقدمة (جميع العملاء، حسب التصنيف، إحصائيات)")
        print("   💾 تصدير واستيراد JSON/CSV")
        print("   🎨 واجهة عربية احترافية")
        
        print("\n📊 البيانات النموذجية:")
        print("   👤 أحمد محمد علي الشامي - صنعاء (عميل مميز)")
        print("   👤 فاطمة سالم أحمد - عدن (عميل VIP)")
        print("   👤 محمد عبدالله الحوثي - صعدة (عميل عادي)")
        print("   👤 عائشة علي محمد - تعز (عميل تجاري)")
        print("   👤 خالد أحمد الزبيري - الحديدة (عميل مميز)")
        print("   👤 نادية سعد الدين - إب (عميل عادي)")
        print("   👤 عبدالرحمن محمد الأهدل - ذمار (عميل VIP)")
        print("   👤 سميرة عبدالله باشا - حضرموت (عميل تجاري)")
        
        print("\n🎮 كيفية الاستخدام:")
        print("   1. استخدم تبويب 'قائمة العملاء' لعرض وإدارة العملاء")
        print("   2. استخدم البحث والتصفية للعثور على عملاء محددين")
        print("   3. انقر نقراً مزدوجاً على عميل لتعديله")
        print("   4. استخدم تبويب 'إضافة/تعديل عميل' لإدخال بيانات جديدة")
        print("   5. راجع التقارير في تبويب 'التقارير'")
        print("   6. استخدم القوائم وشريط الأدوات للوصول السريع")
        
        print("\n⌨️ اختصارات لوحة المفاتيح:")
        print("   Ctrl+N - عميل جديد")
        print("   Ctrl+S - حفظ")
        print("   F2 - تعديل العميل المحدد")
        print("   Delete - حذف العميل المحدد")
        print("   Ctrl+F - البحث")
        print("   F5 - تحديث البيانات")
        print("   Alt+F4 - خروج")
        
        print("\n🎉 النظام جاهز للاستخدام!")
        
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 تأكد من وجود ملفات الوحدة في المسار الصحيح")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        return False

if __name__ == "__main__":
    test_customer_management()
