#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لنظام Onyx ERP
Complete Test for Onyx ERP System
"""

import sys
import os

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد المكتبات"""
    print("🔍 اختبار استيراد المكتبات...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        print("✅ PyQt5: تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ PyQt5: فشل الاستيراد - {e}")
        return False
    
    try:
        from src.ui.onyx_login_window import OnyxLoginWindow
        print("✅ OnyxLoginWindow: تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ OnyxLoginWindow: فشل الاستيراد - {e}")
        return False
    
    try:
        from src.ui.onyx_main_window import OnyxMainWindow
        print("✅ OnyxMainWindow: تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ OnyxMainWindow: فشل الاستيراد - {e}")
        return False
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        print("✅ SQLiteManager: تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ SQLiteManager: فشل الاستيراد - {e}")
        return False
    
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("\n🗄️ اختبار قاعدة البيانات...")
    
    try:
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء مدير قاعدة البيانات
        db_manager = SQLiteManager()
        print("✅ تم إنشاء مدير قاعدة البيانات")
        
        # اختبار الاتصال
        if db_manager.connection:
            print("✅ الاتصال بقاعدة البيانات نشط")
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        # اختبار المستخدم الافتراضي
        user = db_manager.authenticate_user("admin", "admin123")
        if user:
            print(f"✅ تم العثور على المستخدم الافتراضي: {user['full_name']}")
        else:
            print("❌ فشل في العثور على المستخدم الافتراضي")
            return False
        
        # اختبار دليل الحسابات
        accounts = db_manager.execute_query("SELECT COUNT(*) as count FROM chart_of_accounts")
        if accounts and accounts[0]['count'] > 0:
            print(f"✅ دليل الحسابات يحتوي على {accounts[0]['count']} حساب")
        else:
            print("❌ دليل الحسابات فارغ")
            return False
        
        # إغلاق الاتصال
        db_manager.disconnect()
        print("✅ تم إغلاق الاتصال بقاعدة البيانات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_ui_creation():
    """اختبار إنشاء واجهات المستخدم"""
    print("\n🖥️ اختبار إنشاء واجهات المستخدم...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from src.ui.onyx_login_window import OnyxLoginWindow
        from src.ui.onyx_main_window import OnyxMainWindow
        from src.database.sqlite_manager import SQLiteManager
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setLayoutDirection(Qt.RightToLeft)
        
        # اختبار نافذة تسجيل الدخول
        login_window = OnyxLoginWindow()
        print("✅ تم إنشاء نافذة تسجيل الدخول")
        
        # اختبار الواجهة الرئيسية
        db_manager = SQLiteManager()
        session_data = {
            'id': 1,
            'username': 'admin',
            'full_name': 'مدير النظام',
            'role': 'admin',
            'company': 'صقالات أبو سيف للنقل',
            'branch': '1 - الفرع الرئيسي',
            'year': '2025',
            'language': 'ar',
            'accounting_unit': '1',
            'main_center': '1 - المركز الرئيسي'
        }
        
        main_window = OnyxMainWindow(session_data, db_manager)
        print("✅ تم إنشاء الواجهة الرئيسية")
        
        # تنظيف الذاكرة
        login_window.close()
        main_window.close()
        db_manager.disconnect()
        app.quit()
        
        print("✅ تم إغلاق جميع النوافذ بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهات المستخدم: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_full_system():
    """تشغيل النظام الكامل"""
    print("\n🚀 تشغيل النظام الكامل...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMessageBox
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        from src.ui.onyx_login_window import OnyxLoginWindow
        
        print("جاري تشغيل نظام Onyx ERP...")
        
        # إنشاء التطبيق
        app = QApplication([])
        app.setApplicationName("Onyx ERP")
        app.setApplicationVersion("1.0.0")
        
        # تعيين الخط والاتجاه
        font = QFont("Segoe UI", 10)
        app.setFont(font)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # إنشاء نافذة تسجيل الدخول
        login_window = OnyxLoginWindow()
        login_window.show()
        
        print("✅ تم تشغيل النظام بنجاح!")
        print("\n📋 بيانات تسجيل الدخول:")
        print("   👤 المستخدم: admin")
        print("   🔑 كلمة المرور: admin123")
        
        # تشغيل التطبيق
        return app.exec_()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار شامل لنظام Onyx ERP")
    print("   Complete Test for Onyx ERP System")
    print("=" * 60)
    
    # اختبار الاستيراد
    if not test_imports():
        print("\n❌ فشل اختبار الاستيراد")
        return 1
    
    # اختبار قاعدة البيانات
    if not test_database():
        print("\n❌ فشل اختبار قاعدة البيانات")
        return 1
    
    # اختبار واجهات المستخدم
    if not test_ui_creation():
        print("\n❌ فشل اختبار واجهات المستخدم")
        return 1
    
    print("\n" + "=" * 60)
    print("✅ جميع الاختبارات نجحت!")
    print("🎉 النظام جاهز للتشغيل")
    print("=" * 60)
    
    # سؤال المستخدم عن تشغيل النظام
    response = input("\nهل تريد تشغيل النظام الآن؟ (y/n): ")
    if response.lower() in ['y', 'yes', 'نعم', 'ن']:
        return run_full_system()
    else:
        print("👋 شكراً لك!")
        return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️ تم إيقاف الاختبار بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)